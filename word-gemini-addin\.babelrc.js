module.exports = {
    presets: [
        [
            '@babel/preset-env',
            {
                targets: {
                    browsers: [
                        '> 1%',
                        'last 2 versions',
                        'not dead',
                        'not ie 11'
                    ]
                },
                modules: false, // 让 webpack 处理模块
                useBuiltIns: 'usage',
                corejs: 3,
                debug: false
            }
        ]
    ],
    plugins: [
        '@babel/plugin-proposal-class-properties',
        '@babel/plugin-proposal-optional-chaining',
        '@babel/plugin-proposal-nullish-coalescing-operator',
        '@babel/plugin-syntax-dynamic-import'
    ],
    env: {
        test: {
            presets: [
                [
                    '@babel/preset-env',
                    {
                        targets: {
                            node: 'current'
                        },
                        modules: 'commonjs' // Jest 需要 CommonJS 模块
                    }
                ]
            ]
        },
        development: {
            plugins: [
                // 开发环境特定插件
            ]
        },
        production: {
            plugins: [
                // 生产环境优化插件
                '@babel/plugin-transform-remove-console',
                [
                    'transform-remove-debugger',
                    {
                        // 移除 debugger 语句
                    }
                ]
            ]
        }
    }
};
