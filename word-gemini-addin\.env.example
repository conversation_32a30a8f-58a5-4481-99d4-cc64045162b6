# Word Gemini 插件环境配置示例
# 复制此文件为 .env 并填入实际值

# 开发环境设置
NODE_ENV=development

# API 配置
QWEN_API_URL=https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
QWEN_API_KEY=your_qwen_api_key_here

# 开发服务器配置
DEV_SERVER_PORT=3001
DEV_SERVER_HOST=localhost

# SSL 证书路径（用于 HTTPS 开发服务器）
SSL_CERT_PATH=./localhost+1.pem
SSL_KEY_PATH=./localhost+1-key.pem

# 调试设置
DEBUG_MODE=true
VERBOSE_LOGGING=false

# 性能监控
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=0.1

# 缓存设置
CACHE_ENABLED=true
CACHE_TTL=300000
CACHE_MAX_SIZE=100

# 错误报告
ERROR_REPORTING_ENABLED=true
ERROR_REPORTING_ENDPOINT=

# 功能开关
ENABLE_QUICK_ACTIONS=true
ENABLE_HISTORY_SERVICE=true
ENABLE_THEME_SWITCHING=true
ENABLE_OFFLINE_MODE=false

# 开发工具
ENABLE_HOT_RELOAD=true
ENABLE_SOURCE_MAPS=true
ENABLE_BUNDLE_ANALYZER=false

# 测试配置
TEST_TIMEOUT=30000
TEST_COVERAGE_THRESHOLD=80

# 构建配置
BUILD_TARGET=es2018
MINIFY_CODE=false
GENERATE_SOURCEMAPS=true

# Office 插件配置
OFFICE_ADDIN_ID=your-addin-id-here
OFFICE_ADDIN_VERSION=2.0.0
OFFICE_ADDIN_PROVIDER_NAME=Your Name

# 安全设置
ENABLE_CSP=true
ALLOWED_ORIGINS=https://localhost:3001,https://office.live.com

# 国际化
DEFAULT_LOCALE=zh-CN
SUPPORTED_LOCALES=zh-CN,en-US

# 分析和监控
ANALYTICS_ENABLED=false
ANALYTICS_TRACKING_ID=

# 备份和恢复
AUTO_BACKUP_ENABLED=true
BACKUP_INTERVAL=3600000
MAX_BACKUP_FILES=10
