module.exports = {
    env: {
        browser: true,
        es2021: true,
        node: true
    },
    extends: [
        'eslint:recommended'
    ],
    parserOptions: {
        ecmaVersion: 2021,
        sourceType: 'module'
    },
    globals: {
        Office: 'readonly',
        Word: 'readonly',
        marked: 'readonly'
    },
    rules: {
        // 代码质量规则
        'no-unused-vars': ['error', { 
            argsIgnorePattern: '^_',
            varsIgnorePattern: '^_' 
        }],
        'no-console': 'warn',
        'no-debugger': 'error',
        'no-alert': 'error',
        
        // 代码风格规则
        'indent': ['error', 4],
        'quotes': ['error', 'single'],
        'semi': ['error', 'always'],
        'comma-dangle': ['error', 'never'],
        'no-trailing-spaces': 'error',
        'eol-last': 'error',
        
        // ES6+ 规则
        'prefer-const': 'error',
        'no-var': 'error',
        'arrow-spacing': 'error',
        'template-curly-spacing': 'error',
        
        // 函数规则
        'func-style': ['error', 'declaration', { 
            allowArrowFunctions: true 
        }],
        'no-unused-expressions': 'error',
        
        // 对象和数组规则
        'object-curly-spacing': ['error', 'always'],
        'array-bracket-spacing': ['error', 'never'],
        
        // 注释规则
        'spaced-comment': ['error', 'always'],
        
        // 其他规则
        'no-multiple-empty-lines': ['error', { 
            max: 2, 
            maxEOF: 1 
        }],
        'space-before-function-paren': ['error', {
            anonymous: 'always',
            named: 'never',
            asyncArrow: 'always'
        }]
    }
};
