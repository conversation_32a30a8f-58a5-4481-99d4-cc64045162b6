module.exports = {
    // 基本格式化选项
    semi: true,                    // 语句末尾添加分号
    trailingComma: 'none',        // 尾随逗号：不添加
    singleQuote: true,            // 使用单引号
    doubleQuote: false,           // 不使用双引号
    quoteProps: 'as-needed',      // 对象属性引号：按需添加
    
    // 缩进和空格
    tabWidth: 4,                  // 缩进宽度：4个空格
    useTabs: false,               // 使用空格而不是制表符
    
    // 行宽和换行
    printWidth: 100,              // 行宽：100字符
    endOfLine: 'lf',              // 行尾：LF
    
    // 括号和空格
    bracketSpacing: true,         // 对象字面量括号内空格
    bracketSameLine: false,       // JSX 括号不与标签同行
    arrowParens: 'avoid',         // 箭头函数参数括号：避免单参数括号
    
    // HTML 和 JSX
    htmlWhitespaceSensitivity: 'css',  // HTML 空格敏感性
    vueIndentScriptAndStyle: false,    // Vue 文件中不缩进 script 和 style
    
    // 嵌入式语言格式化
    embeddedLanguageFormatting: 'auto',
    
    // 文件特定配置
    overrides: [
        {
            files: '*.json',
            options: {
                tabWidth: 2,
                printWidth: 80
            }
        },
        {
            files: '*.md',
            options: {
                tabWidth: 2,
                printWidth: 80,
                proseWrap: 'always'
            }
        },
        {
            files: '*.css',
            options: {
                tabWidth: 2,
                singleQuote: false
            }
        },
        {
            files: '*.html',
            options: {
                tabWidth: 2,
                printWidth: 120
            }
        },
        {
            files: '*.yml',
            options: {
                tabWidth: 2,
                singleQuote: false
            }
        },
        {
            files: '*.yaml',
            options: {
                tabWidth: 2,
                singleQuote: false
            }
        }
    ]
};
