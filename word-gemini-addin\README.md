# Word Gemini 插件 - 重构优化版

一个集成通义千问 AI 的 Microsoft Word 插件，支持智能对话和自动代码生成。

## 🚀 新版本特性

### ✨ 重构亮点
- **模块化架构**: 将单一文件拆分为清晰的模块结构
- **增强错误处理**: 完善的错误捕获和用户反馈机制
- **改进的 UI/UX**: 更流畅的交互体验和视觉效果
- **性能优化**: 优化 API 调用和内存使用
- **开发工具**: 集成 ESLint、Babel 等现代开发工具

### 🎯 核心功能
- **双模式操作**: 普通对话 + JavaScript 直接生成
- **智能代码生成**: 基于自然语言生成 Word API 代码
- **安全执行**: 代码沙箱和安全验证机制
- **历史记录**: 自动保存和恢复聊天历史
- **响应式设计**: 适配不同屏幕尺寸

## 📁 项目结构

```
word-gemini-addin/
├── src/                          # 源代码目录
│   ├── config/                   # 配置文件
│   │   └── constants.js          # 应用常量和配置
│   ├── services/                 # 服务层
│   │   ├── apiService.js         # API 调用服务
│   │   ├── storageService.js     # 本地存储服务
│   │   └── wordService.js        # Word API 操作服务
│   ├── ui/                       # 用户界面
│   │   └── chatManager.js        # 聊天界面管理器
│   ├── app.js                    # 主应用类
│   └── main.js                   # 应用入口
├── assets/                       # 静态资源
├── dist/                         # 构建输出目录
├── manifest.xml                  # Office 插件清单
├── taskpane.html                 # 主界面 HTML
├── taskpane.css                  # 样式文件
├── webpack.config.js             # Webpack 配置
├── package.json                  # 项目配置
└── README.md                     # 项目说明
```

## 🛠️ 开发环境设置

### 前置要求
- Node.js 16+ 
- npm 或 yarn
- Microsoft Word (用于测试)

### 安装依赖
```bash
cd word-gemini-addin
npm install
```

### 开发命令
```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 代码检查
npm run lint

# 自动修复代码风格
npm run lint:fix

# 验证清单文件
npm run validate

# 侧载插件到 Word
npm run sideload
```

## 🔧 配置说明

### API 配置
在插件界面中输入您的通义千问 API Key：
1. 打开插件
2. 在 API 配置区域输入 API Key
3. 点击保存

### 环境变量
可以通过环境变量配置：
- `NODE_ENV`: 运行环境 (development/production)
- `API_TIMEOUT`: API 超时时间 (默认 30000ms)

## 📖 使用指南

### 普通对话模式
1. 选择"普通对话"模式
2. 输入您的问题或需求
3. AI 将提供详细回答
4. 可以点击"插入到文档"将回答插入到 Word

### JavaScript 直接生成模式
1. 选择"直接生成JavaScript"模式
2. 描述您想要的 Word 操作
3. AI 将生成相应的 JavaScript 代码
4. 代码会自动执行，或您可以手动执行

### 示例操作
- "将所有标题设为蓝色"
- "在文档末尾插入一个3x4的表格"
- "将选中的文本设为粗体并居中"
- "查找并替换所有的'旧文本'为'新文本'"

## 🔒 安全特性

- **代码沙箱**: JavaScript 代码在受控环境中执行
- **API 验证**: 严格的 API 调用验证
- **输入清理**: 自动清理潜在危险的代码
- **错误隔离**: 完善的错误处理机制

## 🐛 故障排除

### 常见问题

**插件无法加载**
- 检查 Office 版本是否支持
- 确认开发证书已安装
- 查看浏览器控制台错误信息

**API 调用失败**
- 验证 API Key 是否正确
- 检查网络连接
- 确认 API 配额是否充足

**代码执行失败**
- 检查生成的代码语法
- 确认 Word API 版本兼容性
- 查看详细错误信息

### 调试工具
开发模式下可使用以下调试命令：
```javascript
// 在浏览器控制台中
window.debugApp()        // 查看应用状态
window.showDebugInfo()   // 显示调试信息
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- Microsoft Office JavaScript API
- 通义千问 API
- Webpack 和现代前端工具链
- 所有贡献者和用户

---

**版本**: 2.0.0  
**更新时间**: 2025-01-14  
**作者**: Your Name
