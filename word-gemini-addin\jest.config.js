module.exports = {
    // 测试环境
    testEnvironment: 'jsdom',
    
    // 根目录
    rootDir: '.',
    
    // 测试文件匹配模式
    testMatch: [
        '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
        '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
        '<rootDir>/tests/**/*.{js,jsx,ts,tsx}'
    ],
    
    // 忽略的文件和目录
    testPathIgnorePatterns: [
        '<rootDir>/node_modules/',
        '<rootDir>/dist/',
        '<rootDir>/build/'
    ],
    
    // 模块文件扩展名
    moduleFileExtensions: [
        'js',
        'jsx',
        'ts',
        'tsx',
        'json'
    ],
    
    // 模块路径映射
    moduleNameMapping: {
        '^@/(.*)$': '<rootDir>/src/$1',
        '^@config/(.*)$': '<rootDir>/src/config/$1',
        '^@services/(.*)$': '<rootDir>/src/services/$1',
        '^@ui/(.*)$': '<rootDir>/src/ui/$1'
    },
    
    // 设置文件
    setupFilesAfterEnv: [
        '<rootDir>/tests/setup.js'
    ],
    
    // 转换配置
    transform: {
        '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest',
        '^.+\\.css$': 'jest-transform-css'
    },
    
    // 不转换的模块
    transformIgnorePatterns: [
        'node_modules/(?!(office-js)/)'
    ],
    
    // 覆盖率配置
    collectCoverage: true,
    collectCoverageFrom: [
        'src/**/*.{js,jsx,ts,tsx}',
        '!src/**/*.d.ts',
        '!src/main.js',
        '!src/**/__tests__/**',
        '!src/**/*.test.{js,jsx,ts,tsx}',
        '!src/**/*.spec.{js,jsx,ts,tsx}'
    ],
    
    // 覆盖率报告
    coverageReporters: [
        'text',
        'lcov',
        'html',
        'json-summary'
    ],
    
    // 覆盖率输出目录
    coverageDirectory: 'coverage',
    
    // 覆盖率阈值
    coverageThreshold: {
        global: {
            branches: 70,
            functions: 70,
            lines: 70,
            statements: 70
        }
    },
    
    // 全局变量
    globals: {
        'Office': 'readonly',
        'Word': 'readonly',
        'marked': 'readonly'
    },
    
    // 模拟配置
    moduleNameMapping: {
        '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
    },
    
    // 测试超时
    testTimeout: 30000,
    
    // 详细输出
    verbose: true,
    
    // 清除模拟
    clearMocks: true,
    
    // 恢复模拟
    restoreMocks: true,
    
    // 错误时停止
    bail: false,
    
    // 最大工作进程数
    maxWorkers: '50%',
    
    // 缓存
    cache: true,
    cacheDirectory: '<rootDir>/.jest-cache',
    
    // 通知配置
    notify: false,
    notifyMode: 'failure-change',
    
    // 监视模式配置
    watchman: true,
    watchPathIgnorePatterns: [
        '<rootDir>/node_modules/',
        '<rootDir>/dist/',
        '<rootDir>/coverage/'
    ],
    
    // 报告器
    reporters: [
        'default',
        ['jest-junit', {
            outputDirectory: 'test-results',
            outputName: 'junit.xml'
        }]
    ],
    
    // 自定义解析器
    resolver: undefined,
    
    // 快照序列化器
    snapshotSerializers: [],
    
    // 测试结果处理器
    testResultsProcessor: undefined,
    
    // 运行器
    runner: 'jest-runner',
    
    // 项目配置（用于多项目设置）
    projects: undefined,
    
    // 依赖提取器
    dependencyExtractor: undefined,
    
    // 自定义环境选项
    testEnvironmentOptions: {
        url: 'https://localhost:3001'
    }
};
