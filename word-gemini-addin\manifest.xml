<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp
  xmlns="http://schemas.microsoft.com/office/appforoffice/1.1"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0"
  xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides"
  xsi:type="TaskPaneApp">

  <Id>12345678-1234-1234-1234-123456789012</Id>
  <Version>*******</Version>
  <ProviderName>Your Company</ProviderName>
  <DefaultLocale>zh-CN</DefaultLocale>
  <DisplayName DefaultValue="AI格式转换" />
  <Description DefaultValue="基于AI的智能格式转换助手" />
  <IconUrl DefaultValue="https://localhost:3001/assets/icon-32.png" />
  <HighResolutionIconUrl DefaultValue="https://localhost:3001/assets/icon-64.png" />
  <SupportUrl DefaultValue="https://localhost:3001" />
  <AppDomains>
    <AppDomain>https://localhost:3001</AppDomain>
  </AppDomains>
  
  <Hosts>
    <Host Name="Document" />
  </Hosts>
  
  <Requirements>
    <Sets>
      <Set Name="WordApi" MinVersion="1.1" />
    </Sets>
  </Requirements>
  
  <DefaultSettings>
    <SourceLocation DefaultValue="https://localhost:3001/taskpane.html"/>
  </DefaultSettings>
  
  <Permissions>ReadWriteDocument</Permissions>
  
  <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="VersionOverridesV1_0">
    <Hosts>
      <Host xsi:type="Document">
        <DesktopFormFactor>
          <GetStarted>
            <Title resid="GetStarted.Title"/>
            <Description resid="GetStarted.Description"/>
            <LearnMoreUrl resid="GetStarted.LearnMoreUrl"/>
          </GetStarted>
          <FunctionFile resid="Commands.Url" />
          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <OfficeTab id="TabHome">
              <Group id="CommandsGroup">
                <Label resid="CommandsGroup.Label" />
                <Icon>
                  <bt:Image size="16" resid="Icon.16x16" />
                  <bt:Image size="32" resid="Icon.32x32" />
                  <bt:Image size="80" resid="Icon.80x80" />
                </Icon>
                <Control xsi:type="Button" id="TaskpaneButton">
                  <Label resid="TaskpaneButton.Label" />
                  <Supertip>
                    <Title resid="TaskpaneButton.Label" />
                    <Description resid="TaskpaneButton.Tooltip" />
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Icon.16x16" />
                    <bt:Image size="32" resid="Icon.32x32" />
                    <bt:Image size="80" resid="Icon.80x80" />
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>ButtonId1</TaskpaneId>
                    <SourceLocation resid="Taskpane.Url" />
                  </Action>
                </Control>
              </Group>
            </OfficeTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>
    </Hosts>
    <Resources>
      <bt:Images>
        <bt:Image id="Icon.16x16" DefaultValue="https://localhost:3001/assets/icon-16.png" />
        <bt:Image id="Icon.32x32" DefaultValue="https://localhost:3001/assets/icon-32.png" />
        <bt:Image id="Icon.80x80" DefaultValue="https://localhost:3001/assets/icon-80.png" />
      </bt:Images>
      <bt:Urls>
        <bt:Url id="GetStarted.LearnMoreUrl" DefaultValue="https://localhost:3001" />
        <bt:Url id="Commands.Url" DefaultValue="https://localhost:3001/commands.html" />
        <bt:Url id="Taskpane.Url" DefaultValue="https://localhost:3001/taskpane.html" />
      </bt:Urls>
      <bt:ShortStrings>
        <bt:String id="GetStarted.Title" DefaultValue="开始使用AI格式转换" />
        <bt:String id="CommandsGroup.Label" DefaultValue="AI格式转换" />
        <bt:String id="TaskpaneButton.Label" DefaultValue="打开格式转换" />
      </bt:ShortStrings>
      <bt:LongStrings>
        <bt:String id="GetStarted.Description" DefaultValue="您的加载项已成功加载。开始使用AI格式转换。" />
        <bt:String id="TaskpaneButton.Tooltip" DefaultValue="点击打开AI格式转换界面" />
      </bt:LongStrings>
    </Resources>
  </VersionOverrides>
</OfficeApp>