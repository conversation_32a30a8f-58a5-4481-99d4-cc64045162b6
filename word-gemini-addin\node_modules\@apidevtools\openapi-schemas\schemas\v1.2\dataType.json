{"id": "https://raw.githubusercontent.com/OAI/OpenAPI-Specification/master/schemas/v1.2/dataType.json#", "$schema": "http://json-schema.org/draft-04/schema#", "description": "Data type as described by the specification (version 1.2)", "type": "object", "oneOf": [{"$ref": "#/definitions/refType"}, {"$ref": "#/definitions/voidType"}, {"$ref": "#/definitions/primitiveType"}, {"$ref": "#/definitions/modelType"}, {"$ref": "#/definitions/arrayType"}], "definitions": {"refType": {"required": ["$ref"], "properties": {"$ref": {"type": "string"}}, "additionalProperties": false}, "voidType": {"enum": [{"type": "void"}]}, "modelType": {"required": ["type"], "properties": {"type": {"type": "string", "not": {"enum": ["boolean", "integer", "number", "string", "array"]}}}, "additionalProperties": false}, "primitiveType": {"required": ["type"], "properties": {"type": {"enum": ["boolean", "integer", "number", "string"]}, "format": {"type": "string"}, "defaultValue": {"not": {"type": ["array", "object", "null"]}}, "enum": {"type": "array", "items": {"type": "string"}, "minItems": 1, "uniqueItems": true}, "minimum": {"type": "string"}, "maximum": {"type": "string"}}, "additionalProperties": false, "dependencies": {"format": {"oneOf": [{"properties": {"type": {"enum": ["integer"]}, "format": {"enum": ["int32", "int64"]}}}, {"properties": {"type": {"enum": ["number"]}, "format": {"enum": ["float", "double"]}}}, {"properties": {"type": {"enum": ["string"]}, "format": {"enum": ["byte", "date", "date-time"]}}}]}, "enum": {"properties": {"type": {"enum": ["string"]}}}, "minimum": {"properties": {"type": {"enum": ["integer", "number"]}}}, "maximum": {"properties": {"type": {"enum": ["integer", "number"]}}}}}, "arrayType": {"required": ["type", "items"], "properties": {"type": {"enum": ["array"]}, "items": {"type": "array", "items": {"$ref": "#/definitions/itemsObject"}}, "uniqueItems": {"type": "boolean"}}, "additionalProperties": false}, "itemsObject": {"oneOf": [{"$ref": "#/definitions/refType"}, {"allOf": [{"$ref": "#/definitions/primitiveType"}, {"properties": {"type": {}, "format": {}}, "additionalProperties": false}]}]}}}