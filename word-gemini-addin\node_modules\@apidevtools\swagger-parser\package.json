{"name": "@apidevtools/swagger-parser", "version": "10.1.1", "description": "Swagger 2.0 and OpenAPI 3.0 parser and validator for Node and browsers", "keywords": ["swagger", "openapi", "open-api", "json", "yaml", "parse", "parser", "validate", "validator", "validation", "spec", "specification", "schema", "reference", "dereference"], "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "homepage": "https://apitools.dev/swagger-parser/", "repository": {"type": "git", "url": "https://github.com/APIDevTools/swagger-parser.git"}, "license": "MIT", "main": "lib/index.js", "typings": "lib/index.d.ts", "files": ["lib"], "scripts": {"clean": "shx rm -rf .nyc_output coverage", "lint": "eslint lib test online/src/js", "lint:fix": "eslint --fix lib test online/src/js", "build": "npm run build:website && npm run build:sass", "build:website": "node build-website.mjs", "build:sass": "sass online/src/scss/style.scss online/css/style.min.css", "test": "npm run test:node && npm run test:typescript && npm run lint", "test:node": "mocha", "test:typescript": "tsc --noEmit --strict --lib esnext,dom test/specs/typescript-definition.spec.ts", "coverage": "npm run coverage:node", "coverage:node": "cross-env QUICK_TEST=true nyc mocha", "upgrade": "npm-check -u && npm audit fix", "bump": "bump --tag --push --all", "--release--cannot upgrade or everything breaks": "npm run upgrade && npm run clean && npm run build && npm test && npm run bump", "release": "npm run clean && npm run build && npm test && npm run bump"}, "devDependencies": {"@jsdevtools/host-environment": "^2.1.2", "@jsdevtools/version-bump-prompt": "^6.1.0", "@types/node": "^22.7.5", "chai": "^4", "cross-env": "^7.0.3", "esbuild": "^0.24.0", "esbuild-plugin-polyfill-node": "^0.3.0", "eslint": "^8", "eslint-plugin-jsdoc": "^50.3.1", "js-yaml": "^4.1.0", "mocha": "^10.7.3", "node-fetch": "^2", "npm-check": "^6.0.1", "nyc": "^17.1.0", "openapi-types": "^12.1.3", "sass": "^1.79.4", "shx": "^0.3.4", "sinon": "^19.0.2", "typescript": "^5.6.3"}, "dependencies": {"@apidevtools/json-schema-ref-parser": "11.7.2", "@apidevtools/openapi-schemas": "^2.1.0", "@apidevtools/swagger-methods": "^3.0.2", "@jsdevtools/ono": "^7.1.3", "ajv": "^8.17.1", "ajv-draft-04": "^1.0.0", "call-me-maybe": "^1.0.2"}, "peerDependencies": {"openapi-types": ">=7"}}