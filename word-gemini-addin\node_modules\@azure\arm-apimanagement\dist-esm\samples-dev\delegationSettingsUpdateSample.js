/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Update Delegation settings.
 *
 * @summary Update Delegation settings.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementPortalSettingsUpdateDelegation.json
 */
function apiManagementPortalSettingsUpdateDelegation() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const ifMatch = "*";
        const parameters = {
            subscriptions: { enabled: true },
            url: "http://contoso.com/delegation",
            userRegistration: { enabled: true },
            validationKey: "<validationKey>"
        };
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.delegationSettings.update(resourceGroupName, serviceName, ifMatch, parameters);
        console.log(result);
    });
}
apiManagementPortalSettingsUpdateDelegation().catch(console.error);
//# sourceMappingURL=delegationSettingsUpdateSample.js.map