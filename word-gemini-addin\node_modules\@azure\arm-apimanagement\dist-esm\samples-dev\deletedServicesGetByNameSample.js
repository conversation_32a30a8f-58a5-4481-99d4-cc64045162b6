/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Get soft-deleted Api Management Service by name.
 *
 * @summary Get soft-deleted Api Management Service by name.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementGetDeletedServiceByName.json
 */
function apiManagementGetDeletedServiceByName() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const serviceName = "apimService3";
        const location = "westus";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.deletedServices.getByName(serviceName, location);
        console.log(result);
    });
}
apiManagementGetDeletedServiceByName().catch(console.error);
//# sourceMappingURL=deletedServicesGetByNameSample.js.map