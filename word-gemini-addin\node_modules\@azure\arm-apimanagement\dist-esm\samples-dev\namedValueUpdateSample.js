/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Updates the specific named value.
 *
 * @summary Updates the specific named value.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementUpdateNamedValue.json
 */
function apiManagementUpdateNamedValue() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const namedValueId = "testprop2";
        const ifMatch = "*";
        const parameters = {
            displayName: "prop3name",
            secret: false,
            tags: ["foo", "bar2"],
            value: "propValue"
        };
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.namedValue.beginUpdateAndWait(resourceGroupName, serviceName, namedValueId, ifMatch, parameters);
        console.log(result);
    });
}
apiManagementUpdateNamedValue().catch(console.error);
//# sourceMappingURL=namedValueUpdateSample.js.map