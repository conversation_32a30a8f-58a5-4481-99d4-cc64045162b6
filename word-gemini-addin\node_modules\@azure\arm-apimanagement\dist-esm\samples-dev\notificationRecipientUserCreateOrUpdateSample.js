/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Adds the API Management User to the list of Recipients for the Notification.
 *
 * @summary Adds the API Management User to the list of Recipients for the Notification.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementCreateNotificationRecipientUser.json
 */
function apiManagementCreateNotificationRecipientUser() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const notificationName = "RequestPublisherNotificationMessage";
        const userId = "576823d0a40f7e74ec07d642";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.notificationRecipientUser.createOrUpdate(resourceGroupName, serviceName, notificationName, userId);
        console.log(result);
    });
}
apiManagementCreateNotificationRecipientUser().catch(console.error);
//# sourceMappingURL=notificationRecipientUserCreateOrUpdateSample.js.map