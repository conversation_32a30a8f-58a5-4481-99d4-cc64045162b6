/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Removes the API Management user from the list of Notification.
 *
 * @summary Removes the API Management user from the list of Notification.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementDeleteNotificationRecipientUser.json
 */
function apiManagementDeleteNotificationRecipientUser() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const notificationName = "RequestPublisherNotificationMessage";
        const userId = "576823d0a40f7e74ec07d642";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.notificationRecipientUser.delete(resourceGroupName, serviceName, notificationName, userId);
        console.log(result);
    });
}
apiManagementDeleteNotificationRecipientUser().catch(console.error);
//# sourceMappingURL=notificationRecipientUserDeleteSample.js.map