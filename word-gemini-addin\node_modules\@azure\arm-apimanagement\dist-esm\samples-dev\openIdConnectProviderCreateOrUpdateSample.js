/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Creates or updates the OpenID Connect Provider.
 *
 * @summary Creates or updates the OpenID Connect Provider.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementCreateOpenIdConnectProvider.json
 */
function apiManagementCreateOpenIdConnectProvider() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const opid = "templateOpenIdConnect3";
        const parameters = {
            clientId: "oidprovidertemplate3",
            clientSecret: "x",
            displayName: "templateoidprovider3",
            metadataEndpoint: "https://oidprovider-template3.net"
        };
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.openIdConnectProvider.createOrUpdate(resourceGroupName, serviceName, opid, parameters);
        console.log(result);
    });
}
apiManagementCreateOpenIdConnectProvider().catch(console.error);
//# sourceMappingURL=openIdConnectProviderCreateOrUpdateSample.js.map