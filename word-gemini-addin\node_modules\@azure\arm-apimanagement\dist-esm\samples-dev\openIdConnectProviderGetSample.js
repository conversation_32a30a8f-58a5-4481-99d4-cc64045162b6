/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Gets specific OpenID Connect Provider without secrets.
 *
 * @summary Gets specific OpenID Connect Provider without secrets.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementGetOpenIdConnectProvider.json
 */
function apiManagementGetOpenIdConnectProvider() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const opid = "templateOpenIdConnect2";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.openIdConnectProvider.get(resourceGroupName, serviceName, opid);
        console.log(result);
    });
}
apiManagementGetOpenIdConnectProvider().catch(console.error);
//# sourceMappingURL=openIdConnectProviderGetSample.js.map