{"version": 3, "file": "openIdConnectProviderUpdateSample.js", "sourceRoot": "", "sources": ["../../samples-dev/openIdConnectProviderUpdateSample.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;;AAEH,uCAAuC;AACvC,kCAAkC;AAClC,OAAO,EAEL,mBAAmB,EACpB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,sBAAsB,EAAE,MAAM,iBAAiB,CAAC;AAEzD;;;;;GAKG;AACH,SAAe,wCAAwC;;QACrD,MAAM,cAAc,GAAG,OAAO,CAAC;QAC/B,MAAM,iBAAiB,GAAG,KAAK,CAAC;QAChC,MAAM,WAAW,GAAG,cAAc,CAAC;QACnC,MAAM,IAAI,GAAG,wBAAwB,CAAC;QACtC,MAAM,OAAO,GAAG,GAAG,CAAC;QACpB,MAAM,UAAU,GAAwC;YACtD,YAAY,EAAE,eAAe;SAC9B,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAChD,MAAM,MAAM,GAAG,IAAI,mBAAmB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,qBAAqB,CAAC,MAAM,CACtD,iBAAiB,EACjB,WAAW,EACX,IAAI,EACJ,OAAO,EACP,UAAU,CACX,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;CAAA;AAED,wCAAwC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}