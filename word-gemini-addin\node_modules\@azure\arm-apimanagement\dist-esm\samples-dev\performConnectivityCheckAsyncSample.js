/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Performs a connectivity check between the API Management service and a given destination, and returns metrics for the connection, as well as errors encountered while trying to establish it.
 *
 * @summary Performs a connectivity check between the API Management service and a given destination, and returns metrics for the connection, as well as errors encountered while trying to establish it.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementPerformConnectivityCheckHttpConnect.json
 */
function httpConnectivityCheck() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const connectivityCheckRequestParams = {
            destination: { address: "https://microsoft.com", port: 3306 },
            protocolConfiguration: {
                httpConfiguration: {
                    method: "GET",
                    validStatusCodes: [200, 204]
                }
            },
            source: { region: "northeurope" },
            protocol: "HTTPS"
        };
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.beginPerformConnectivityCheckAsyncAndWait(resourceGroupName, serviceName, connectivityCheckRequestParams);
        console.log(result);
    });
}
httpConnectivityCheck().catch(console.error);
/**
 * This sample demonstrates how to Performs a connectivity check between the API Management service and a given destination, and returns metrics for the connection, as well as errors encountered while trying to establish it.
 *
 * @summary Performs a connectivity check between the API Management service and a given destination, and returns metrics for the connection, as well as errors encountered while trying to establish it.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementPerformConnectivityCheck.json
 */
function tcpConnectivityCheck() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const connectivityCheckRequestParams = {
            destination: { address: "*******", port: 53 },
            preferredIPVersion: "IPv4",
            source: { region: "northeurope" }
        };
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.beginPerformConnectivityCheckAsyncAndWait(resourceGroupName, serviceName, connectivityCheckRequestParams);
        console.log(result);
    });
}
tcpConnectivityCheck().catch(console.error);
//# sourceMappingURL=performConnectivityCheckAsyncSample.js.map