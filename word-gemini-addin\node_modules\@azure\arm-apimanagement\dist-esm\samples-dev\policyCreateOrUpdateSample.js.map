{"version": 3, "file": "policyCreateOrUpdateSample.js", "sourceRoot": "", "sources": ["../../samples-dev/policyCreateOrUpdateSample.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;;AAEH,uCAAuC;AACvC,kCAAkC;AAClC,OAAO,EAAkB,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAC/E,OAAO,EAAE,sBAAsB,EAAE,MAAM,iBAAiB,CAAC;AAEzD;;;;;GAKG;AACH,SAAe,yBAAyB;;QACtC,MAAM,cAAc,GAAG,OAAO,CAAC;QAC/B,MAAM,iBAAiB,GAAG,KAAK,CAAC;QAChC,MAAM,WAAW,GAAG,cAAc,CAAC;QACnC,MAAM,QAAQ,GAAG,QAAQ,CAAC;QAC1B,MAAM,UAAU,GAAmB;YACjC,MAAM,EAAE,KAAK;YACb,KAAK,EACH,gGAAgG;SACnG,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAChD,MAAM,MAAM,GAAG,IAAI,mBAAmB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAC/C,iBAAiB,EACjB,WAAW,EACX,QAAQ,EACR,UAAU,CACX,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;CAAA;AAED,yBAAyB,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}