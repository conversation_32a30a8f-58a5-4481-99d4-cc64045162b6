/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Deletes the policy configuration at the Product.
 *
 * @summary Deletes the policy configuration at the Product.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementDeleteProductPolicy.json
 */
function apiManagementDeleteProductPolicy() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const productId = "testproduct";
        const policyId = "policy";
        const ifMatch = "*";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.productPolicy.delete(resourceGroupName, serviceName, productId, policyId, ifMatch);
        console.log(result);
    });
}
apiManagementDeleteProductPolicy().catch(console.error);
//# sourceMappingURL=productPolicyDeleteSample.js.map