{"version": 3, "file": "quotaByCounterKeysUpdateSample.js", "sourceRoot": "", "sources": ["../../samples-dev/quotaByCounterKeysUpdateSample.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;;AAEH,uCAAuC;AACvC,kCAAkC;AAClC,OAAO,EAEL,mBAAmB,EACpB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,sBAAsB,EAAE,MAAM,iBAAiB,CAAC;AAEzD;;;;;GAKG;AACH,SAAe,kCAAkC;;QAC/C,MAAM,cAAc,GAAG,OAAO,CAAC;QAC/B,MAAM,iBAAiB,GAAG,KAAK,CAAC;QAChC,MAAM,WAAW,GAAG,cAAc,CAAC;QACnC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,MAAM,UAAU,GAAoC;YAClD,UAAU,EAAE,CAAC;YACb,aAAa,EAAE,YAAY;SAC5B,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAChD,MAAM,MAAM,GAAG,IAAI,mBAAmB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,MAAM,CACnD,iBAAiB,EACjB,WAAW,EACX,eAAe,EACf,UAAU,CACX,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;CAAA;AAED,kCAAkC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}