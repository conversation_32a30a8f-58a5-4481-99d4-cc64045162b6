/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Gets the value of the quota counter associated with the counter-key in the policy for the specific period in service instance.
 *
 * @summary Gets the value of the quota counter associated with the counter-key in the policy for the specific period in service instance.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementGetQuotaCounterKeysByQuotaPeriod.json
 */
function apiManagementGetQuotaCounterKeysByQuotaPeriod() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const quotaCounterKey = "ba";
        const quotaPeriodKey = "0_P3Y6M4DT12H30M5S";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.quotaByPeriodKeys.get(resourceGroupName, serviceName, quotaCounterKey, quotaPeriodKey);
        console.log(result);
    });
}
apiManagementGetQuotaCounterKeysByQuotaPeriod().catch(console.error);
//# sourceMappingURL=quotaByPeriodKeysGetSample.js.map