{"version": 3, "file": "quotaByPeriodKeysUpdateSample.js", "sourceRoot": "", "sources": ["../../samples-dev/quotaByPeriodKeysUpdateSample.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;;AAEH,uCAAuC;AACvC,kCAAkC;AAClC,OAAO,EAEL,mBAAmB,EACpB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,sBAAsB,EAAE,MAAM,iBAAiB,CAAC;AAEzD;;;;;GAKG;AACH,SAAe,+CAA+C;;QAC5D,MAAM,cAAc,GAAG,OAAO,CAAC;QAC/B,MAAM,iBAAiB,GAAG,KAAK,CAAC;QAChC,MAAM,WAAW,GAAG,cAAc,CAAC;QACnC,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,MAAM,cAAc,GAAG,oBAAoB,CAAC;QAC5C,MAAM,UAAU,GAAoC;YAClD,UAAU,EAAE,CAAC;YACb,aAAa,EAAE,CAAC;SACjB,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAChD,MAAM,MAAM,GAAG,IAAI,mBAAmB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAClD,iBAAiB,EACjB,WAAW,EACX,eAAe,EACf,cAAc,EACd,UAAU,CACX,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;CAAA;AAED,+CAA+C,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}