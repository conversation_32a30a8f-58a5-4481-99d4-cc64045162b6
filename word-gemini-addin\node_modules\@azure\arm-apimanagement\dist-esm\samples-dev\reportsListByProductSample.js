/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __asyncValues, __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Lists report records by Product.
 *
 * @summary Lists report records by Product.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementGetReportsByProduct.json
 */
function apiManagementGetReportsByProduct() {
    var e_1, _a;
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const filter = "timestamp ge datetime'2017-06-01T00:00:00' and timestamp le datetime'2017-06-04T00:00:00'";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const resArray = new Array();
        try {
            for (var _b = __asyncValues(client.reports.listByProduct(resourceGroupName, serviceName, filter)), _c; _c = yield _b.next(), !_c.done;) {
                let item = _c.value;
                resArray.push(item);
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) yield _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        console.log(resArray);
    });
}
apiManagementGetReportsByProduct().catch(console.error);
//# sourceMappingURL=reportsListByProductSample.js.map