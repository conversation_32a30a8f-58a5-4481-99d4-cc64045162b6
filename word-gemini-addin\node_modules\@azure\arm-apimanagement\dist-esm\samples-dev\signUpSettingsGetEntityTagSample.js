/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Gets the entity state (Etag) version of the SignUpSettings.
 *
 * @summary Gets the entity state (Etag) version of the SignUpSettings.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementHeadSignUpSettings.json
 */
function apiManagementHeadSignUpSettings() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.signUpSettings.getEntityTag(resourceGroupName, serviceName);
        console.log(result);
    });
}
apiManagementHeadSignUpSettings().catch(console.error);
//# sourceMappingURL=signUpSettingsGetEntityTagSample.js.map