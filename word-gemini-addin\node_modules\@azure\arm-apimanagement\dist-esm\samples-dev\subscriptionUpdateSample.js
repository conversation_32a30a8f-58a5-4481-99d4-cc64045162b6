/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Updates the details of a subscription specified by its identifier.
 *
 * @summary Updates the details of a subscription specified by its identifier.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementUpdateSubscription.json
 */
function apiManagementUpdateSubscription() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const sid = "testsub";
        const ifMatch = "*";
        const parameters = { displayName: "testsub" };
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.subscription.update(resourceGroupName, serviceName, sid, ifMatch, parameters);
        console.log(result);
    });
}
apiManagementUpdateSubscription().catch(console.error);
//# sourceMappingURL=subscriptionUpdateSample.js.map