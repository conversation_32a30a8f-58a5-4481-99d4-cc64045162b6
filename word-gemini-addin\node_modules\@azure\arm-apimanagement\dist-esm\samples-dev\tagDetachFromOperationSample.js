/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Detach the tag from the Operation.
 *
 * @summary Detach the tag from the Operation.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementDeleteApiOperationTag.json
 */
function apiManagementDeleteApiOperationTag() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const apiId = "59d5b28d1f7fab116c282650";
        const operationId = "59d5b28d1f7fab116c282651";
        const tagId = "59d5b28e1f7fab116402044e";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.tag.detachFromOperation(resourceGroupName, serviceName, apiId, operationId, tagId);
        console.log(result);
    });
}
apiManagementDeleteApiOperationTag().catch(console.error);
//# sourceMappingURL=tagDetachFromOperationSample.js.map