/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Detach the tag from the Product.
 *
 * @summary Detach the tag from the Product.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementDeleteProductTag.json
 */
function apiManagementDeleteProductTag() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const productId = "59d5b28d1f7fab116c282650";
        const tagId = "59d5b28e1f7fab116402044e";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.tag.detachFromProduct(resourceGroupName, serviceName, productId, tagId);
        console.log(result);
    });
}
apiManagementDeleteProductTag().catch(console.error);
//# sourceMappingURL=tagDetachFromProductSample.js.map