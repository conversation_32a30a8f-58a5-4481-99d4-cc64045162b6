{"version": 3, "file": "tenantAccessUpdateSample.js", "sourceRoot": "", "sources": ["../../samples-dev/tenantAccessUpdateSample.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;;AAEH,uCAAuC;AACvC,kCAAkC;AAClC,OAAO,EAEL,mBAAmB,EACpB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,sBAAsB,EAAE,MAAM,iBAAiB,CAAC;AAEzD;;;;;GAKG;AACH,SAAe,+BAA+B;;QAC5C,MAAM,cAAc,GAAG,OAAO,CAAC;QAC/B,MAAM,iBAAiB,GAAG,KAAK,CAAC;QAChC,MAAM,WAAW,GAAG,cAAc,CAAC;QACnC,MAAM,UAAU,GAAG,QAAQ,CAAC;QAC5B,MAAM,OAAO,GAAG,GAAG,CAAC;QACpB,MAAM,UAAU,GAAsC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QACxE,MAAM,UAAU,GAAG,IAAI,sBAAsB,EAAE,CAAC;QAChD,MAAM,MAAM,GAAG,IAAI,mBAAmB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAC7C,iBAAiB,EACjB,WAAW,EACX,UAAU,EACV,OAAO,EACP,UAAU,CACX,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtB,CAAC;CAAA;AAED,+BAA+B,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}