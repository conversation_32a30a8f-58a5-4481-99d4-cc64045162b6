/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Gets the status of the most recent synchronization between the configuration database and the Git repository.
 *
 * @summary Gets the status of the most recent synchronization between the configuration database and the Git repository.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementTenantAccessSyncState.json
 */
function apiManagementTenantAccessSyncState() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const configurationName = "configuration";
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.tenantConfiguration.getSyncState(resourceGroupName, serviceName, configurationName);
        console.log(result);
    });
}
apiManagementTenantAccessSyncState().catch(console.error);
//# sourceMappingURL=tenantConfigurationGetSyncStateSample.js.map