/*
 * Copyright (c) Microsoft Corporation.
 * Licensed under the MIT License.
 *
 * Code generated by Microsoft (R) AutoRest Code Generator.
 * Changes may cause incorrect behavior and will be lost if the code is regenerated.
 */
import { __awaiter } from "tslib";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import { ApiManagementClient } from "@azure/arm-apimanagement";
import { DefaultAzureCredential } from "@azure/identity";
/**
 * This sample demonstrates how to Gets the Shared Access Authorization Token for the User.
 *
 * @summary Gets the Shared Access Authorization Token for the User.
 * x-ms-original-file: specification/apimanagement/resource-manager/Microsoft.ApiManagement/stable/2021-08-01/examples/ApiManagementUserToken.json
 */
function apiManagementUserToken() {
    return __awaiter(this, void 0, void 0, function* () {
        const subscriptionId = "subid";
        const resourceGroupName = "rg1";
        const serviceName = "apimService1";
        const userId = "userId1718";
        const parameters = {
            expiry: new Date("2019-04-21T00:44:24.2845269Z"),
            keyType: "primary"
        };
        const credential = new DefaultAzureCredential();
        const client = new ApiManagementClient(credential, subscriptionId);
        const result = yield client.user.getSharedAccessToken(resourceGroupName, serviceName, userId, parameters);
        console.log(result);
    });
}
apiManagementUserToken().catch(console.error);
//# sourceMappingURL=userGetSharedAccessTokenSample.js.map