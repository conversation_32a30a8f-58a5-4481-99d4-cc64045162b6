import { ResourceManagementClient, DeploymentOperation } from "@azure/arm-resources";
import { AzureAccountProvider, FxError, Result, SolutionContext, TelemetryReporter, v2, v3 } from "@microsoft/teamsfx-api";
export declare type DeployContext = {
    ctx: SolutionContext;
    finished: boolean;
    client: ResourceManagementClient;
    resourceGroupName: string;
    deploymentStartTime: number;
    deploymentName: string;
};
declare type OperationStatus = {
    resourceName: string;
    resourceGroupName: string;
    subscriptionId: string;
    resourceType?: string;
    status: string;
};
export declare function getRequiredOperation(operation: DeploymentOperation, deployCtx: DeployContext): OperationStatus | undefined;
export declare function pollDeploymentStatus(deployCtx: DeployContext): Promise<void>;
export declare function doDeployArmTemplatesV3(ctx: v2.Context, inputs: v2.InputsWithProjectPath, envInfo: v3.EnvInfoV3, azureAccountProvider: AzureAccountProvider): Promise<Result<undefined, FxError>>;
export declare function handleArmDeploymentError(error: any, deployCtx: DeployContext): Promise<Result<undefined, FxError>>;
export declare function deployArmTemplatesV3(ctx: v2.Context, inputs: v2.InputsWithProjectPath, envInfo: v3.EnvInfoV3, azureAccountProvider: AzureAccountProvider): Promise<Result<undefined, FxError>>;
export declare function copyParameterJson(projectPath: string, appName: string, targetEnvName: string, sourceEnvName: string): Promise<void>;
export declare function updateAzureParameters(projectPath: string, appName: string, envName: string, hasSwitchedM365Tenant: boolean, hasSwitchedSubscription: boolean, hasBotServiceCreatedBefore: boolean): Promise<Result<undefined, FxError>>;
export declare function getParameterJsonV3(ctx: v2.Context, projectPath: string, envInfo: v3.EnvInfoV3): Promise<any>;
export declare function generateResourceBaseName(appName: string, envName: string): string;
export declare function wrapGetDeploymentError(deployCtx: DeployContext, resourceGroupName: string, deploymentName: string): Promise<Result<any, FxError>>;
export declare function formattedDeploymentError(deploymentError: any): any;
declare class Arm {
    deployArmTemplates(ctx: v2.Context, inputs: v2.InputsWithProjectPath, envInfo: v3.EnvInfoV3, azureAccountProvider: AzureAccountProvider): Promise<Result<undefined, FxError>>;
}
declare const arm: Arm;
export default arm;
export declare function sendErrorTelemetryThenReturnError(eventName: string, error: FxError, reporter?: TelemetryReporter, properties?: {
    [p: string]: string;
}, measurements?: {
    [p: string]: number;
}, errorProps?: string[]): FxError;
//# sourceMappingURL=arm.d.ts.map