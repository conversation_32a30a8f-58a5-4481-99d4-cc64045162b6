{"version": 3, "file": "arm.d.ts", "sourceRoot": "", "sources": ["../../src/component/arm.ts"], "names": [], "mappings": "AAGA,OAAO,EACL,wBAAwB,EACxB,mBAAmB,EAGpB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EACL,oBAAoB,EAKpB,OAAO,EAIP,MAAM,EACN,eAAe,EAEf,iBAAiB,EAEjB,EAAE,EACF,EAAE,EACH,MAAM,wBAAwB,CAAC;AA6ChC,oBAAY,aAAa,GAAG;IAC1B,GAAG,EAAE,eAAe,CAAC;IACrB,QAAQ,EAAE,OAAO,CAAC;IAClB,MAAM,EAAE,wBAAwB,CAAC;IACjC,iBAAiB,EAAE,MAAM,CAAC;IAC1B,mBAAmB,EAAE,MAAM,CAAC;IAC5B,cAAc,EAAE,MAAM,CAAC;CACxB,CAAC;AAEF,aAAK,eAAe,GAAG;IACrB,YAAY,EAAE,MAAM,CAAC;IACrB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,cAAc,EAAE,MAAM,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AASF,wBAAgB,oBAAoB,CAClC,SAAS,EAAE,mBAAmB,EAC9B,SAAS,EAAE,aAAa,GACvB,eAAe,GAAG,SAAS,CA4B7B;AAED,wBAAsB,oBAAoB,CAAC,SAAS,EAAE,aAAa,iBAgGlE;AAED,wBAAsB,sBAAsB,CAC1C,GAAG,EAAE,EAAE,CAAC,OAAO,EACf,MAAM,EAAE,EAAE,CAAC,qBAAqB,EAChC,OAAO,EAAE,EAAE,CAAC,SAAS,EACrB,oBAAoB,EAAE,oBAAoB,GACzC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAwFrC;AAcD,wBAAsB,wBAAwB,CAC5C,KAAK,EAAE,GAAG,EACV,SAAS,EAAE,aAAa,GACvB,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAiErC;AAyCD,wBAAsB,oBAAoB,CACxC,GAAG,EAAE,EAAE,CAAC,OAAO,EACf,MAAM,EAAE,EAAE,CAAC,qBAAqB,EAChC,OAAO,EAAE,EAAE,CAAC,SAAS,EACrB,oBAAoB,EAAE,oBAAoB,GACzC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAyDrC;AAED,wBAAsB,iBAAiB,CACrC,WAAW,EAAE,MAAM,EACnB,OAAO,EAAE,MAAM,EACf,aAAa,EAAE,MAAM,EACrB,aAAa,EAAE,MAAM,iBA4BtB;AAED,wBAAsB,qBAAqB,CACzC,WAAW,EAAE,MAAM,EACnB,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,MAAM,EACf,qBAAqB,EAAE,OAAO,EAC9B,uBAAuB,EAAE,OAAO,EAChC,0BAA0B,EAAE,OAAO,GAClC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAyCrC;AAED,wBAAsB,kBAAkB,CACtC,GAAG,EAAE,EAAE,CAAC,OAAO,EACf,WAAW,EAAE,MAAM,EACnB,OAAO,EAAE,EAAE,CAAC,SAAS,gBAyBtB;AA4GD,wBAAgB,wBAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,MAAM,CAUjF;AAED,wBAAsB,sBAAsB,CAC1C,SAAS,EAAE,aAAa,EACxB,iBAAiB,EAAE,MAAM,EACzB,cAAc,EAAE,MAAM,GACrB,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAyB/B;AA0FD,wBAAgB,wBAAwB,CAAC,eAAe,EAAE,GAAG,GAAG,GAAG,CAoBlE;AAED,cAAM,GAAG;IACD,kBAAkB,CACtB,GAAG,EAAE,EAAE,CAAC,OAAO,EACf,MAAM,EAAE,EAAE,CAAC,qBAAqB,EAChC,OAAO,EAAE,EAAE,CAAC,SAAS,EACrB,oBAAoB,EAAE,oBAAoB,GACzC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;CAGvC;AAED,QAAA,MAAM,GAAG,KAAY,CAAC;AACtB,eAAe,GAAG,CAAC;AAEnB,wBAAgB,iCAAiC,CAC/C,SAAS,EAAE,MAAM,EACjB,KAAK,EAAE,OAAO,EACd,QAAQ,CAAC,EAAE,iBAAiB,EAC5B,UAAU,CAAC,EAAE;IAAE,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;CAAE,EACpC,YAAY,CAAC,EAAE;IAAE,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAAA;CAAE,EACtC,UAAU,CAAC,EAAE,MAAM,EAAE,GACpB,OAAO,CAqBT"}