"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendErrorTelemetryThenReturnError = exports.formattedDeploymentError = exports.wrapGetDeploymentError = exports.generateResourceBaseName = exports.getParameterJsonV3 = exports.updateAzureParameters = exports.copyParameterJson = exports.deployArmTemplatesV3 = exports.handleArmDeploymentError = exports.doDeployArmTemplatesV3 = exports.pollDeploymentStatus = exports.getRequiredOperation = void 0;
const tslib_1 = require("tslib");
const arm_resources_1 = require("@azure/arm-resources");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const fs = tslib_1.__importStar(require("fs-extra"));
const os_1 = tslib_1.__importDefault(require("os"));
const path_1 = tslib_1.__importDefault(require("path"));
const constants_1 = require("./constants");
const constants_2 = require("../common/constants");
const cpUtils_1 = require("../common/cpUtils");
const tools_1 = require("../common/tools");
const bicepChecker_1 = require("./utils/depsChecker/bicepChecker");
const progressHelper_1 = require("./utils/progressHelper");
const localizeUtils_1 = require("../common/localizeUtils");
const migrate_1 = require("../component/migrate");
const utils_1 = require("../common/utils");
const bicepOrchestrationFileName = "main.bicep";
const configsFolder = `.${teamsfx_api_1.ConfigFolderName}/configs`;
const parameterFileNameTemplate = `azure.parameters.${teamsfx_api_1.EnvNamePlaceholder}.json`;
const pollWaitSeconds = 10;
const maxRetryTimes = 4;
// constant string
const resourceBaseName = "resourceBaseName";
const parameterName = "parameters";
const solutionName = "solution";
const ErrorCodes = {
    InvalidTemplate: constants_1.SolutionError.FailedToValidateArmTemplates,
    InvalidTemplateDeployment: constants_1.SolutionError.FailedToValidateArmTemplates,
    ResourceGroupNotFound: constants_1.SolutionError.ResourceGroupNotFound,
};
class DeploymentErrorMessage {
    constructor(value) {
        this.value = value;
    }
}
function getRequiredOperation(operation, deployCtx) {
    var _a, _b, _c, _d, _e, _f, _g;
    if (((_b = (_a = operation.properties) === null || _a === void 0 ? void 0 : _a.targetResource) === null || _b === void 0 ? void 0 : _b.resourceName) &&
        ((_d = (_c = operation.properties) === null || _c === void 0 ? void 0 : _c.targetResource) === null || _d === void 0 ? void 0 : _d.id) &&
        operation.properties.provisioningState &&
        ((_e = operation.properties) === null || _e === void 0 ? void 0 : _e.timestamp) &&
        operation.properties.timestamp.getTime() > deployCtx.deploymentStartTime) {
        try {
            const resourceGroupName = tools_1.getResourceGroupNameFromResourceId(operation.properties.targetResource.id);
            const subscriptionId = tools_1.getSubscriptionIdFromResourceId(operation.properties.targetResource.id);
            return {
                resourceName: (_g = (_f = operation.properties) === null || _f === void 0 ? void 0 : _f.targetResource) === null || _g === void 0 ? void 0 : _g.resourceName,
                resourceGroupName: resourceGroupName,
                subscriptionId: subscriptionId,
                resourceType: operation.properties.targetResource.resourceType,
                status: operation.properties.provisioningState,
            };
        }
        catch (error) {
            return undefined;
        }
    }
    else {
        return undefined;
    }
}
exports.getRequiredOperation = getRequiredOperation;
async function pollDeploymentStatus(deployCtx) {
    var e_1, _a;
    var _b, _c, _d;
    let tryCount = 0;
    let previousStatus = {};
    let polledOperations = [];
    (_b = deployCtx.ctx.logProvider) === null || _b === void 0 ? void 0 : _b.info(localizeUtils_1.getLocalizedString("core.deployArmTemplates.PollDeploymentStatusNotice", constants_2.PluginDisplayName.Solution));
    while (!deployCtx.finished) {
        await tools_1.waitSeconds(pollWaitSeconds);
        try {
            const operations = [];
            try {
                for (var _e = (e_1 = void 0, tslib_1.__asyncValues(deployCtx.client.deploymentOperations
                    .list(deployCtx.resourceGroupName, deployCtx.deploymentName)
                    .byPage({ maxPageSize: 100 }))), _f; _f = await _e.next(), !_f.done;) {
                    const page = _f.value;
                    for (const deploymentOperation of page) {
                        operations.push(deploymentOperation);
                    }
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (_f && !_f.done && (_a = _e.return)) await _a.call(_e);
                }
                finally { if (e_1) throw e_1.error; }
            }
            if (deployCtx.finished) {
                return;
            }
            const currentStatus = {};
            await Promise.all(operations.map(async (o) => {
                var e_2, _a;
                var _b;
                const operation = getRequiredOperation(o, deployCtx);
                if (operation) {
                    currentStatus[operation.resourceName] = operation.status;
                    if (!polledOperations.includes(operation.resourceName)) {
                        polledOperations.push(operation.resourceName);
                        // get sub operations when resource type is deployments.
                        if (operation.resourceType === constants_2.ConstantString.DeploymentResourceType) {
                            let client = deployCtx.client;
                            if (operation.subscriptionId !== deployCtx.client.subscriptionId) {
                                const azureToken = await ((_b = deployCtx.ctx.azureAccountProvider) === null || _b === void 0 ? void 0 : _b.getIdentityCredentialAsync());
                                client = new arm_resources_1.ResourceManagementClient(azureToken, operation.subscriptionId);
                            }
                            const subOperations = [];
                            try {
                                for (var _c = tslib_1.__asyncValues(client.deploymentOperations
                                    .list(operation.resourceGroupName, operation.resourceName)
                                    .byPage({ maxPageSize: 100 })), _d; _d = await _c.next(), !_d.done;) {
                                    const page = _d.value;
                                    for (const subOperation of page) {
                                        subOperations.push(subOperation);
                                    }
                                }
                            }
                            catch (e_2_1) { e_2 = { error: e_2_1 }; }
                            finally {
                                try {
                                    if (_d && !_d.done && (_a = _c.return)) await _a.call(_c);
                                }
                                finally { if (e_2) throw e_2.error; }
                            }
                            subOperations.forEach((sub) => {
                                const subOperation = getRequiredOperation(sub, deployCtx);
                                if (subOperation) {
                                    currentStatus[subOperation.resourceName] = subOperation.status;
                                }
                            });
                        }
                    }
                }
            }));
            for (const key in currentStatus) {
                if (currentStatus[key] !== previousStatus[key]) {
                    (_c = deployCtx.ctx.logProvider) === null || _c === void 0 ? void 0 : _c.info(`[${constants_2.PluginDisplayName.Solution}] ${key} -> ${currentStatus[key]}`);
                }
            }
            previousStatus = currentStatus;
            polledOperations = [];
        }
        catch (error) {
            tryCount++;
            if (tryCount < maxRetryTimes) {
                (_d = deployCtx.ctx.logProvider) === null || _d === void 0 ? void 0 : _d.warning(localizeUtils_1.getLocalizedString("core.deployArmTemplates.RetryGetDeploymentStatus", deployCtx.deploymentName, tryCount));
            }
            else if (tryCount === maxRetryTimes) {
                const pollError = new teamsfx_api_1.SystemError({
                    error,
                    source: constants_1.SolutionSource,
                    name: constants_1.SolutionError.FailedToPollArmDeploymentStatus,
                });
                sendErrorTelemetryThenReturnError(constants_1.SolutionTelemetryEvent.ArmDeployment, pollError, deployCtx.ctx.telemetryReporter);
            }
        }
    }
}
exports.pollDeploymentStatus = pollDeploymentStatus;
async function doDeployArmTemplatesV3(ctx, inputs, envInfo, azureAccountProvider) {
    var _a;
    const progressHandler = await progressHelper_1.ProgressHelper.startDeployArmTemplatesProgressHandler(ctx.userInteraction);
    await (progressHandler === null || progressHandler === void 0 ? void 0 : progressHandler.next(localizeUtils_1.getLocalizedString("core.deployArmTemplates.Progress.ExecuteDeployment")));
    // update parameters
    const parameterJson = await getParameterJsonV3(ctx, inputs.projectPath, envInfo);
    const envState = envInfo.state;
    const resourceGroupName = envState.solution.resourceGroupName;
    if (!resourceGroupName) {
        return teamsfx_api_1.err(new teamsfx_api_1.SystemError(constants_1.SolutionSource, "NoResourceGroupFound", localizeUtils_1.getDefaultString("core.deployArmTemplates.FailedToReadResourceGroup"), localizeUtils_1.getLocalizedString("core.deployArmTemplates.FailedToReadResourceGroup")));
    }
    const bicepCommand = await bicepChecker_1.ensureBicep(ctx, inputs);
    // Compile bicep file to json
    const templateDir = path_1.default.join(await utils_1.getProjectTemplatesFolderPath(inputs.projectPath), "azure");
    const bicepOrchestrationFilePath = path_1.default.join(templateDir, bicepOrchestrationFileName);
    const armTemplateJson = await compileBicepToJson(bicepCommand, bicepOrchestrationFilePath, ctx.logProvider);
    (_a = ctx.logProvider) === null || _a === void 0 ? void 0 : _a.info(localizeUtils_1.getLocalizedString("core.deployArmTemplates.CompileBicepSuccessNotice", constants_2.PluginDisplayName.Solution));
    // deploy arm templates to azure
    const client = await getResourceManagementClientForArmDeployment(azureAccountProvider, envState.solution.subscriptionId);
    const deploymentName = `${constants_2.PluginDisplayName.Solution}_deployment`.replace(" ", "_").toLowerCase();
    const deploymentParameters = {
        properties: {
            parameters: parameterJson.parameters,
            template: armTemplateJson,
            mode: "Incremental",
        },
    };
    const deployCtx = {
        ctx: ctx,
        finished: false,
        deploymentStartTime: Date.now(),
        client: client,
        resourceGroupName: resourceGroupName,
        deploymentName: deploymentName,
    };
    try {
        const result = client.deployments
            .beginCreateOrUpdateAndWait(resourceGroupName, deploymentName, deploymentParameters)
            .then((result) => {
            var _a, _b;
            (_a = ctx.logProvider) === null || _a === void 0 ? void 0 : _a.info(localizeUtils_1.getLocalizedString("core.deployArmTemplates.SuccessNotice", constants_2.PluginDisplayName.Solution, resourceGroupName, deploymentName));
            syncArmOutput(envInfo, (_b = result.properties) === null || _b === void 0 ? void 0 : _b.outputs);
            return result;
        })
            .finally(() => {
            deployCtx.finished = true;
        });
        await pollDeploymentStatus(deployCtx);
        await result;
        return teamsfx_api_1.ok(undefined);
    }
    catch (error) {
        return handleArmDeploymentError(error, deployCtx);
    }
}
exports.doDeployArmTemplatesV3 = doDeployArmTemplatesV3;
function fetchInnerError(error) {
    if (!error.details) {
        return error;
    }
    if (error.details.error) {
        return fetchInnerError(error.details.error);
    }
    else if (error.details instanceof Array && error.details[0]) {
        return fetchInnerError(error.details[0]);
    }
    return error;
}
async function handleArmDeploymentError(error, deployCtx) {
    var _a, _b;
    // return the error if the template is invalid
    if (Object.keys(ErrorCodes).includes(error.code)) {
        if (error.code === "InvalidTemplateDeployment") {
            error = fetchInnerError(error);
        }
        return teamsfx_api_1.err(new teamsfx_api_1.UserError({
            error,
            source: constants_1.SolutionSource,
            name: (_a = ErrorCodes[error.code]) !== null && _a !== void 0 ? _a : constants_1.SolutionError.FailedToValidateArmTemplates,
        }));
    }
    // try to get deployment error
    const result = await wrapGetDeploymentError(deployCtx, deployCtx.resourceGroupName, deployCtx.deploymentName);
    if (result.isOk()) {
        const deploymentError = result.value;
        // return thrown error if deploymentError is empty
        if (!deploymentError) {
            return teamsfx_api_1.err(new teamsfx_api_1.UserError({
                error,
                source: constants_1.SolutionSource,
                name: constants_1.SolutionError.FailedToDeployArmTemplatesToAzure,
            }));
        }
        const deploymentErrorObj = formattedDeploymentError(deploymentError);
        const deploymentErrorMessage = JSON.stringify(deploymentErrorObj, undefined, 2);
        let errorMessage = localizeUtils_1.getLocalizedString("core.deployArmTemplates.FailNotice", constants_2.PluginDisplayName.Solution, deployCtx.resourceGroupName, deployCtx.deploymentName);
        errorMessage += localizeUtils_1.getLocalizedString("core.deployArmTemplates.DeploymentErrorWithHelplink", error.message, deploymentErrorMessage, constants_2.HelpLinks.ArmHelpLink);
        const notificationMessage = getNotificationMessage(deploymentError, deployCtx.deploymentName);
        const returnError = new teamsfx_api_1.UserError({
            message: errorMessage,
            source: constants_1.SolutionSource,
            name: constants_1.SolutionError.FailedToDeployArmTemplatesToAzure,
            helpLink: constants_2.HelpLinks.ArmHelpLink,
            displayMessage: notificationMessage,
        });
        returnError.innerError = new DeploymentErrorMessage(JSON.stringify(deploymentErrorObj));
        return teamsfx_api_1.err(returnError);
    }
    else {
        (_b = deployCtx.ctx.logProvider) === null || _b === void 0 ? void 0 : _b.info(`origin error message is : \n${JSON.stringify(error, undefined, 2)}`);
        return result;
    }
}
exports.handleArmDeploymentError = handleArmDeploymentError;
function syncArmOutput(envInfo, armOutput) {
    if (armOutput instanceof Object) {
        const armOutputKeys = Object.keys(armOutput);
        for (const armOutputKey of armOutputKeys) {
            const moduleOutput = armOutput[armOutputKey].value;
            if (moduleOutput instanceof Object) {
                const moduleOutputKeys = Object.keys(moduleOutput);
                for (const moduleOutputKey of moduleOutputKeys) {
                    const pluginOutput = moduleOutput[moduleOutputKey].value;
                    if (pluginOutput instanceof Object) {
                        let pluginId = pluginOutput[constants_1.TEAMS_FX_RESOURCE_ID_KEY];
                        if (pluginId) {
                            pluginId = migrate_1.pluginName2ComponentName(pluginId);
                            const pluginOutputKeys = Object.keys(pluginOutput);
                            for (const pluginOutputKey of pluginOutputKeys) {
                                if (pluginOutputKey != constants_1.TEAMS_FX_RESOURCE_ID_KEY) {
                                    if (envInfo.state instanceof Map) {
                                        let configMap = envInfo.state.get(pluginId);
                                        if (!configMap) {
                                            configMap = new Map();
                                            envInfo.state.set(pluginId, configMap);
                                        }
                                        configMap.set(pluginOutputKey, pluginOutput[pluginOutputKey]);
                                    }
                                    else {
                                        if (!envInfo.state[pluginId])
                                            envInfo.state[pluginId] = {};
                                        envInfo.state[pluginId][pluginOutputKey] = pluginOutput[pluginOutputKey];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
async function deployArmTemplatesV3(ctx, inputs, envInfo, azureAccountProvider) {
    var _a, _b, _c;
    (_a = ctx.logProvider) === null || _a === void 0 ? void 0 : _a.info(localizeUtils_1.getLocalizedString("core.deployArmTemplates.StartNotice", constants_2.PluginDisplayName.Solution));
    let result;
    (_b = ctx.telemetryReporter) === null || _b === void 0 ? void 0 : _b.sendTelemetryEvent(constants_1.SolutionTelemetryEvent.ArmDeploymentStart, {
        [constants_1.SolutionTelemetryProperty.Component]: constants_1.SolutionTelemetryComponentName,
    });
    try {
        result = await doDeployArmTemplatesV3(ctx, inputs, envInfo, azureAccountProvider);
        if (result.isOk()) {
            (_c = ctx.telemetryReporter) === null || _c === void 0 ? void 0 : _c.sendTelemetryEvent(constants_1.SolutionTelemetryEvent.ArmDeployment, {
                [constants_1.SolutionTelemetryProperty.Component]: constants_1.SolutionTelemetryComponentName,
                [constants_1.SolutionTelemetryProperty.Success]: constants_1.SolutionTelemetrySuccess.Yes,
            });
        }
        else {
            const errorProperties = {};
            // If the innerError is a DeploymentErrorMessage value, we will set it in telemetry.
            if (result.error.innerError && result.error.innerError instanceof DeploymentErrorMessage) {
                errorProperties[constants_1.SolutionTelemetryProperty.ArmDeploymentError] =
                    result.error.innerError.value;
            }
            sendErrorTelemetryThenReturnError(constants_1.SolutionTelemetryEvent.ArmDeployment, result.error, ctx.telemetryReporter, errorProperties);
        }
    }
    catch (error) {
        if (error instanceof teamsfx_api_1.UserError || error instanceof teamsfx_api_1.SystemError) {
            result = teamsfx_api_1.err(error);
        }
        else if (error instanceof Error) {
            result = teamsfx_api_1.err(new teamsfx_api_1.SystemError({
                error,
                source: constants_1.SolutionSource,
                name: constants_1.SolutionError.FailedToDeployArmTemplatesToAzure,
            }));
        }
        else {
            result = teamsfx_api_1.err(new teamsfx_api_1.SystemError({
                error,
                source: constants_1.SolutionSource,
                name: constants_1.SolutionError.FailedToDeployArmTemplatesToAzure,
            }));
        }
        sendErrorTelemetryThenReturnError(constants_1.SolutionTelemetryEvent.ArmDeployment, result.error, ctx.telemetryReporter);
    }
    await progressHelper_1.ProgressHelper.endDeployArmTemplatesProgress(result.isOk());
    return result;
}
exports.deployArmTemplatesV3 = deployArmTemplatesV3;
async function copyParameterJson(projectPath, appName, targetEnvName, sourceEnvName) {
    var _a, _b, _c;
    if (!targetEnvName || !sourceEnvName) {
        return;
    }
    const parameterFolderPath = path_1.default.join(projectPath, configsFolder);
    const targetParameterFileName = parameterFileNameTemplate.replace(teamsfx_api_1.EnvNamePlaceholder, targetEnvName);
    const sourceParameterFileName = parameterFileNameTemplate.replace(teamsfx_api_1.EnvNamePlaceholder, sourceEnvName);
    const targetParameterFilePath = path_1.default.join(parameterFolderPath, targetParameterFileName);
    const sourceParameterFilePath = path_1.default.join(parameterFolderPath, sourceParameterFileName);
    const targetParameterContent = await fs.readJson(sourceParameterFilePath);
    if ((_c = (_b = (_a = targetParameterContent[parameterName]) === null || _a === void 0 ? void 0 : _a.provisionParameters) === null || _b === void 0 ? void 0 : _b.value) === null || _c === void 0 ? void 0 : _c.resourceBaseName) {
        targetParameterContent[parameterName].provisionParameters.value.resourceBaseName =
            generateResourceBaseName(appName, targetEnvName);
    }
    await fs.ensureDir(parameterFolderPath);
    await fs.writeFile(targetParameterFilePath, JSON.stringify(targetParameterContent, undefined, 2).replace(/\r?\n/g, os_1.default.EOL));
}
exports.copyParameterJson = copyParameterJson;
async function updateAzureParameters(projectPath, appName, envName, hasSwitchedM365Tenant, hasSwitchedSubscription, hasBotServiceCreatedBefore) {
    if (!envName ||
        !appName ||
        !projectPath ||
        (!hasSwitchedM365Tenant && !hasSwitchedSubscription) ||
        (hasSwitchedM365Tenant && !hasBotServiceCreatedBefore)) {
        return teamsfx_api_1.ok(undefined);
    }
    const parameterFolderPath = path_1.default.join(projectPath, configsFolder);
    const targetParameterFileName = parameterFileNameTemplate.replace(teamsfx_api_1.EnvNamePlaceholder, envName);
    const targetParameterFilePath = path_1.default.join(parameterFolderPath, targetParameterFileName);
    try {
        const targetParameterContent = await fs.readJson(targetParameterFilePath);
        if (hasSwitchedSubscription) {
            targetParameterContent[parameterName].provisionParameters.value.resourceBaseName =
                generateResourceBaseName(appName, envName);
        }
        else if (hasSwitchedM365Tenant && hasBotServiceCreatedBefore) {
            targetParameterContent[parameterName].provisionParameters.value.botServiceName =
                generateResourceBaseName(appName, envName);
        }
        await fs.ensureDir(parameterFolderPath);
        await fs.writeFile(targetParameterFilePath, JSON.stringify(targetParameterContent, undefined, 2).replace(/\r?\n/g, os_1.default.EOL));
        return teamsfx_api_1.ok(undefined);
    }
    catch (exception) {
        const error = new teamsfx_api_1.UserError(constants_1.SolutionSource, constants_1.SolutionError.FailedToUpdateAzureParameters, localizeUtils_1.getDefaultString("core.handleConfigFile.FailedToUpdateAzureParameters", envName), localizeUtils_1.getLocalizedString("core.handleConfigFile.FailedToUpdateAzureParameters", envName));
        return teamsfx_api_1.err(error);
    }
}
exports.updateAzureParameters = updateAzureParameters;
async function getParameterJsonV3(ctx, projectPath, envInfo) {
    var _a;
    if (!(envInfo === null || envInfo === void 0 ? void 0 : envInfo.envName)) {
        throw new Error(localizeUtils_1.getLocalizedString("core.deployArmTemplates.FailedToGetEnvironmentName"));
    }
    const parameterFileName = parameterFileNameTemplate.replace(teamsfx_api_1.EnvNamePlaceholder, envInfo.envName);
    const parameterFolderPath = path_1.default.join(projectPath, configsFolder);
    const parameterFilePath = path_1.default.join(parameterFolderPath, parameterFileName);
    try {
        await fs.stat(parameterFilePath);
    }
    catch (err) {
        const error = new teamsfx_api_1.UserError(constants_1.SolutionSource, "ParameterFileNotExist", localizeUtils_1.getDefaultString("core.deployArmTemplates.ParameterNotExist", parameterFilePath), localizeUtils_1.getLocalizedString("core.deployArmTemplates.ParameterNotExist", parameterFilePath));
        (_a = ctx.logProvider) === null || _a === void 0 ? void 0 : _a.error(error.message);
        throw error;
    }
    const parameterJson = await getExpandedParameterV3(ctx, envInfo, parameterFilePath); // only expand secrets in memory
    return parameterJson;
}
exports.getParameterJsonV3 = getParameterJsonV3;
async function getExpandedParameterV3(ctx, envInfo, filePath) {
    var _a;
    try {
        const parameterTemplate = await fs.readFile(filePath, constants_2.ConstantString.UTF8Encoding);
        const parameterJsonString = expandParameterPlaceholdersV3(ctx, envInfo, parameterTemplate);
        return JSON.parse(parameterJsonString);
    }
    catch (err) {
        (_a = ctx.logProvider) === null || _a === void 0 ? void 0 : _a.error(localizeUtils_1.getLocalizedString("core.deployArmTemplates.FailedToExpandParameter", filePath));
        throw err;
    }
}
async function getResourceManagementClientForArmDeployment(azureAccountProvider, subscriptionId) {
    const azureToken = await azureAccountProvider.getIdentityCredentialAsync();
    if (!azureToken) {
        throw new teamsfx_api_1.SystemError(constants_2.PluginDisplayName.Solution, constants_1.SolutionError.FailedToGetAzureCredential, localizeUtils_1.getDefaultString("core.deployArmTemplates.InvalidAzureCredential"), localizeUtils_1.getLocalizedString("core.deployArmTemplates.InvalidAzureCredential"));
    }
    if (!subscriptionId) {
        throw new teamsfx_api_1.SystemError(constants_2.PluginDisplayName.Solution, constants_1.SolutionError.NoSubscriptionSelected, localizeUtils_1.getDefaultString("core.deployArmTemplates.FailedToGetSubsId"), localizeUtils_1.getLocalizedString("core.deployArmTemplates.FailedToGetSubsId"));
    }
    return new arm_resources_1.ResourceManagementClient(azureToken, subscriptionId);
}
async function compileBicepToJson(bicepCommand, bicepOrchestrationFilePath, logger) {
    try {
        const result = await cpUtils_1.executeCommand(bicepCommand, ["build", bicepOrchestrationFilePath, "--stdout"], logger, { shell: false });
        return JSON.parse(result);
    }
    catch (err) {
        throw new Error(localizeUtils_1.getLocalizedString("core.deployArmTemplates.CompileBicepFailed", err.message));
    }
}
function expandParameterPlaceholdersV3(ctx, envInfo, parameterContent) {
    const projectSettingsV3 = ctx.projectSetting;
    const componentNames = projectSettingsV3.components.map((c) => c.name);
    const stateVariables = {};
    const availableVariables = { state: stateVariables };
    const envState = envInfo.state;
    // Add plugin contexts to available variables
    for (const componentName of componentNames) {
        const resourceState = envState[componentName] || {};
        // const pluginContext = getPluginContext(ctx, plugin.name);
        const pluginVariables = {};
        for (const key of Object.keys(resourceState)) {
            if (typeof resourceState[key] === "string") {
                // Currently we only config with string type
                pluginVariables[key] = resourceState[key];
            }
        }
        stateVariables[componentName] = pluginVariables;
    }
    // Add solution config to available variables
    const solutionConfig = envState.solution;
    if (solutionConfig) {
        const solutionVariables = {};
        for (const key of Object.keys(solutionConfig)) {
            if (typeof solutionConfig[key] === "string") {
                // Currently we only config with string type
                solutionVariables[key] = solutionConfig[key];
            }
        }
        stateVariables[solutionName] = solutionVariables;
    }
    // Add environment variable to available variables
    const processVariables = Object.keys(process.env).reduce((obj, key) => {
        obj[key] = process.env[key];
        return obj;
    }, {});
    availableVariables["$env"] = processVariables;
    parameterContent = migrate_1.convertManifestTemplateToV3(parameterContent);
    return tools_1.compileHandlebarsTemplateString(parameterContent, availableVariables);
}
function generateResourceBaseName(appName, envName) {
    const maxAppNameLength = 10;
    const maxEnvNameLength = 4;
    const normalizedAppName = appName.replace(/[^a-zA-Z0-9]/g, "").toLowerCase();
    const normalizedEnvName = envName.replace(/[^a-zA-Z0-9]/g, "").toLowerCase();
    return (normalizedAppName.substr(0, maxAppNameLength) +
        normalizedEnvName.substr(0, maxEnvNameLength) +
        tools_1.getUuid().substr(0, 6));
}
exports.generateResourceBaseName = generateResourceBaseName;
async function wrapGetDeploymentError(deployCtx, resourceGroupName, deploymentName) {
    var _a;
    try {
        const deploymentError = await getDeploymentError(deployCtx, resourceGroupName, deploymentName);
        return teamsfx_api_1.ok(deploymentError);
    }
    catch (error) {
        (_a = deployCtx.ctx.logProvider) === null || _a === void 0 ? void 0 : _a.error(localizeUtils_1.getLocalizedString("core.deployArmTemplates.FailedToGetDeploymentError", error.message));
        return teamsfx_api_1.err(new teamsfx_api_1.UserError(constants_1.SolutionSource, "GetDeploymentErrorFailed", localizeUtils_1.getDefaultString("core.deployArmTemplates.FailedToGetDeploymentErrorNotification", deployCtx.deploymentName, deployCtx.resourceGroupName), localizeUtils_1.getLocalizedString("core.deployArmTemplates.FailedToGetDeploymentErrorNotification", deployCtx.deploymentName, deployCtx.resourceGroupName)));
    }
}
exports.wrapGetDeploymentError = wrapGetDeploymentError;
async function getDeploymentError(deployCtx, resourceGroupName, deploymentName) {
    var e_3, _a;
    var _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
    let deployment;
    try {
        deployment = await deployCtx.client.deployments.get(resourceGroupName, deploymentName);
    }
    catch (error) {
        if (deploymentName !== deployCtx.deploymentName &&
            error.code === constants_2.ConstantString.DeploymentNotFound) {
            return undefined;
        }
        throw error;
    }
    // The root deployment error name is deployCtx.deploymentName.
    // If we find the root error has a timestamp less than startTime, it is an old error to be ignored.
    // Other erros will be ignored as well.
    if (deploymentName === deployCtx.deploymentName &&
        ((_b = deployment.properties) === null || _b === void 0 ? void 0 : _b.timestamp) &&
        deployment.properties.timestamp.getTime() < deployCtx.deploymentStartTime) {
        return undefined;
    }
    if (!((_c = deployment.properties) === null || _c === void 0 ? void 0 : _c.error)) {
        return undefined;
    }
    const deploymentError = {
        error: (_d = deployment.properties) === null || _d === void 0 ? void 0 : _d.error,
    };
    const operations = [];
    try {
        for (var _o = tslib_1.__asyncValues(deployCtx.client.deploymentOperations
            .list(resourceGroupName, deploymentName)
            .byPage({ maxPageSize: 100 })), _p; _p = await _o.next(), !_p.done;) {
            const page = _p.value;
            for (const deploymentOperation of page) {
                operations.push(deploymentOperation);
            }
        }
    }
    catch (e_3_1) { e_3 = { error: e_3_1 }; }
    finally {
        try {
            if (_p && !_p.done && (_a = _o.return)) await _a.call(_o);
        }
        finally { if (e_3) throw e_3.error; }
    }
    for (const operation of operations) {
        if ((_f = (_e = operation.properties) === null || _e === void 0 ? void 0 : _e.statusMessage) === null || _f === void 0 ? void 0 : _f.error) {
            if (!deploymentError.subErrors) {
                deploymentError.subErrors = {};
            }
            const name = (_h = (_g = operation.properties.targetResource) === null || _g === void 0 ? void 0 : _g.resourceName) !== null && _h !== void 0 ? _h : operation.id;
            deploymentError.subErrors[name] = {
                error: operation.properties.statusMessage.error,
            };
            if (((_j = operation.properties.targetResource) === null || _j === void 0 ? void 0 : _j.resourceType) ===
                constants_2.ConstantString.DeploymentResourceType &&
                ((_k = operation.properties.targetResource) === null || _k === void 0 ? void 0 : _k.resourceName) &&
                ((_l = operation.properties.targetResource) === null || _l === void 0 ? void 0 : _l.id)) {
                const resourceGroupName = tools_1.getResourceGroupNameFromResourceId(operation.properties.targetResource.id);
                const subError = await getDeploymentError(deployCtx, resourceGroupName, (_m = operation.properties.targetResource) === null || _m === void 0 ? void 0 : _m.resourceName);
                if (subError) {
                    deploymentError.subErrors[name].inner = subError;
                }
            }
        }
    }
    return deploymentError;
}
function getNotificationMessage(deploymentError, deploymentName) {
    let failedDeployments = [];
    if (deploymentError.subErrors) {
        failedDeployments = Object.keys(deploymentError.subErrors);
    }
    else {
        failedDeployments.push(deploymentName);
    }
    const format = failedDeployments.map((deployment) => deployment + " module");
    return localizeUtils_1.getLocalizedString("core.deployArmTemplates.DeploymentFailedNotification", format.join(", "));
}
function formattedDeploymentError(deploymentError) {
    var _a, _b, _c;
    if (deploymentError.subErrors) {
        const result = {};
        for (const key in deploymentError.subErrors) {
            const subError = deploymentError.subErrors[key];
            if (subError.inner) {
                result[key] = formattedDeploymentError(subError.inner);
            }
            else {
                const needFilter = ((_b = (_a = subError.error) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.includes("Template output evaluation skipped")) &&
                    ((_c = subError.error) === null || _c === void 0 ? void 0 : _c.code) === "DeploymentOperationFailed";
                if (!needFilter) {
                    result[key] = subError.error;
                }
            }
        }
        return result;
    }
    else {
        return deploymentError.error;
    }
}
exports.formattedDeploymentError = formattedDeploymentError;
class Arm {
    async deployArmTemplates(ctx, inputs, envInfo, azureAccountProvider) {
        return deployArmTemplatesV3(ctx, inputs, envInfo, azureAccountProvider);
    }
}
const arm = new Arm();
exports.default = arm;
function sendErrorTelemetryThenReturnError(eventName, error, reporter, properties, measurements, errorProps) {
    if (!properties) {
        properties = {};
    }
    if (constants_1.SolutionTelemetryProperty.Component in properties === false) {
        properties[constants_1.SolutionTelemetryProperty.Component] = constants_1.SolutionTelemetryComponentName;
    }
    properties[constants_1.SolutionTelemetryProperty.Success] = "no";
    if (error instanceof teamsfx_api_1.UserError) {
        properties["error-type"] = "user";
    }
    else {
        properties["error-type"] = "system";
    }
    properties["error-code"] = `${error.source}.${error.name}`;
    properties["error-message"] = error.message;
    reporter === null || reporter === void 0 ? void 0 : reporter.sendTelemetryErrorEvent(eventName, properties, measurements, errorProps);
    return error;
}
exports.sendErrorTelemetryThenReturnError = sendErrorTelemetryThenReturnError;
//# sourceMappingURL=arm.js.map