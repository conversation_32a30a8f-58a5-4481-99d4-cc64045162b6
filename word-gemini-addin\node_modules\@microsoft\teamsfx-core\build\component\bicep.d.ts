import { FxError, Result, InputsWithProjectPath, ResourceContextV3 } from "@microsoft/teamsfx-api";
import "reflect-metadata";
export declare class BicepComponent {
    readonly name = "bicep";
    init(projectPath: string): Promise<Result<undefined, FxError>>;
    deploy(context: ResourceContextV3, inputs: InputsWithProjectPath): Promise<Result<undefined, FxError>>;
}
//# sourceMappingURL=bicep.d.ts.map