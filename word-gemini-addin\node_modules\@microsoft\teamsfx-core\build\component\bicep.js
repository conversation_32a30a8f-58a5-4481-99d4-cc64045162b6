"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.BicepComponent = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const path = tslib_1.__importStar(require("path"));
require("reflect-metadata");
const typedi_1 = require("typedi");
const utils_1 = require("../common/utils");
const folder_1 = require("../folder");
const arm_1 = tslib_1.__importDefault(require("./arm"));
let BicepComponent = class BicepComponent {
    constructor() {
        this.name = "bicep";
    }
    async init(projectPath) {
        const sourceFolder = path.join(folder_1.getTemplatesFolder(), "bicep");
        const targetFolder = path.join(await utils_1.getProjectTemplatesFolderPath(projectPath), "azure");
        if ((await fs_extra_1.default.pathExists(path.join(targetFolder, "main.bicep"))) &&
            (await fs_extra_1.default.pathExists(path.join(targetFolder, "provision.bicep"))) &&
            (await fs_extra_1.default.pathExists(path.join(targetFolder, "config.bicep"))))
            return teamsfx_api_1.ok(undefined);
        await fs_extra_1.default.ensureDir(targetFolder);
        await fs_extra_1.default.ensureDir(path.join(targetFolder, "provision"));
        await fs_extra_1.default.ensureDir(path.join(targetFolder, "teamsFx"));
        if (!(await fs_extra_1.default.pathExists(path.join(targetFolder, "main.bicep")))) {
            await fs_extra_1.default.copyFile(path.join(sourceFolder, "main.bicep"), path.join(targetFolder, "main.bicep"));
        }
        if (!(await fs_extra_1.default.pathExists(path.join(targetFolder, "provision.bicep")))) {
            await fs_extra_1.default.copyFile(path.join(sourceFolder, "provision.bicep"), path.join(targetFolder, "provision.bicep"));
        }
        if (!(await fs_extra_1.default.pathExists(path.join(targetFolder, "config.bicep")))) {
            await fs_extra_1.default.copyFile(path.join(sourceFolder, "config.bicep"), path.join(targetFolder, "config.bicep"));
        }
        return teamsfx_api_1.ok(undefined);
    }
    async deploy(context, inputs) {
        const ctx = context;
        return await arm_1.default.deployArmTemplates(ctx, inputs, ctx.envInfo, ctx.tokenProvider.azureAccountProvider);
    }
};
BicepComponent = tslib_1.__decorate([
    typedi_1.Service("bicep")
], BicepComponent);
exports.BicepComponent = BicepComponent;
//# sourceMappingURL=bicep.js.map