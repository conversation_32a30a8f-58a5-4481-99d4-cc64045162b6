import { MultiSelectQuestion, OptionItem, UserError } from "@microsoft/teamsfx-api";
export declare const ComponentNames: {
    TeamsTab: string;
    TeamsBot: string;
    TeamsApi: string;
    AppManifest: string;
    AadApp: string;
    AzureWebApp: string;
    AzureStorage: string;
    BotService: string;
    SPFxTab: string;
    SPFx: string;
    Identity: string;
    APIMFeature: string;
    APIM: string;
    KeyVault: string;
    AzureSQL: string;
    TabCode: string;
    BotCode: string;
    SPFxTabCode: string;
    ApiCode: string;
    Function: string;
    SimpleAuth: string;
    SSO: string;
    ApiConnector: string;
    CICD: string;
};
export declare const AzureResources: string[];
export declare enum Scenarios {
    Tab = "Tab",
    Bot = "Bot",
    Api = "Api"
}
export declare const componentToScenario: Map<string, Scenarios>;
export declare const scenarioToComponent: Map<Scenarios, string>;
export declare enum ProgrammingLanguage {
    JS = "javascript",
    TS = "typescript",
    CSharp = "csharp"
}
export declare enum Runtime {
    nodejs = "node",
    dotnet = "dotnet"
}
export declare const languageToRuntime: Map<ProgrammingLanguage, Runtime>;
export declare const ActionNames: {
    provision: string;
    configure: string;
    generateBicep: string;
};
export declare const ActionTypeFunction = "function";
export declare const ActionTypeCall = "call";
export declare const ActionTypeGroup = "group";
export declare const ActionTypeShell = "shell";
export declare const BicepConstants: {
    writeFile: string;
};
export declare const TelemetryConstants: {
    eventPrefix: string;
    properties: {
        component: string;
        appId: string;
        tenantId: string;
        success: string;
        errorCode: string;
        errorType: string;
        errorMessage: string;
        timeCost: string;
    };
    values: {
        yes: string;
        no: string;
        userError: string;
        systemError: string;
    };
};
export declare const ErrorConstants: {
    unhandledError: string;
    unhandledErrorMessage: string;
};
export declare const AzureSqlOutputs: {
    sqlResourceId: {
        key: string;
        bicepVariable: string;
    };
    sqlEndpoint: {
        key: string;
        bicepVariable: string;
    };
    databaseName: {
        key: string;
        bicepVariable: string;
    };
};
export declare const IdentityOutputs: {
    identityResourceId: {
        key: string;
        bicepVariable: string;
    };
    identityName: {
        key: string;
        bicepVariable: string;
    };
    identityClientId: {
        key: string;
        bicepVariable: string;
    };
    identityPrincipalId: {
        key: string;
        bicepVariable: string;
    };
};
export declare const KeyVaultOutputs: {
    keyVaultResourceId: {
        key: string;
        bicepVariable: string;
    };
    m365ClientSecretReference: {
        key: string;
        bicepVariable: string;
    };
    botClientSecretReference: {
        key: string;
        bicepVariable: string;
    };
};
export declare const APIMOutputs: {
    serviceResourceId: {
        key: string;
        bicepVariable: string;
    };
    productResourceId: {
        key: string;
        bicepVariable: string;
    };
    authServerResourceId: {
        key: string;
    };
    apimClientAADObjectId: {
        key: string;
    };
    apimClientAADClientId: {
        key: string;
    };
    apimClientAADClientSecret: {
        key: string;
    };
};
export declare const WebAppOutputs: {
    resourceId: {
        key: string;
        bicepVariable: string;
    };
    endpoint: {
        key: string;
        bicepVariable: string;
    };
    endpointAsParam: {
        key: string;
        bicepVariable: string;
    };
};
export declare const FunctionOutputs: {
    resourceId: {
        key: string;
        bicepVariable: string;
    };
    endpoint: {
        key: string;
        bicepVariable: string;
    };
    endpointAsParam: {
        key: string;
        bicepVariable: string;
    };
};
export declare const StorageOutputs: {
    endpoint: {
        key: string;
        bicepVariable: string;
    };
    storageResourceId: {
        key: string;
        bicepVariable: string;
    };
    domain: {
        key: string;
        bicepVariable: string;
    };
    indexPath: {
        key: string;
        bicepVariable: string;
    };
};
export declare const BotServiceOutputs: {
    botId: {
        key: string;
    };
    botPassword: {
        key: string;
    };
};
export declare const AadAppOutputs: {
    applicationIdUris: {
        key: string;
    };
    clientId: {
        key: string;
    };
    clientSecret: {
        key: string;
    };
    objectId: {
        key: string;
    };
    oauth2PermissionScopeId: {
        key: string;
    };
    frontendEndpoint: {
        key: string;
    };
    botId: {
        key: string;
    };
    botEndpoint: {
        key: string;
    };
    domain: {
        key: string;
    };
    endpoint: {
        key: string;
    };
    oauthAuthority: {
        key: string;
    };
    oauthHost: {
        key: string;
    };
    tenantId: {
        key: string;
    };
};
export declare const FunctionAppSetting: {
    keys: {
        allowedAppIds: string;
    };
    allowedAppIdSep: string;
};
export declare const PathConstants: {
    botWorkingDir: string;
    apiWorkingDir: string;
    tabWorkingDir: string;
    dotnetWorkingDir: string;
    npmPackageFolder: string;
    nodePackageFile: string;
    functionExtensionsFolder: string;
    functionExtensionsFile: string;
    deploymentInfoFolder: string;
    deploymentInfoFile: string;
    nodeArtifactFolder: string;
    dotnetArtifactFolder: string;
    reactTabIndexPath: string;
    blazorTabIndexPath: string;
};
export declare const RegularExpr: {
    validFunctionNamePattern: RegExp;
};
/**
 * Void is used to construct Result<Void, FxError>.
 * e.g. return ok(Void);
 * It exists because ok(void) does not compile.
 */
export declare const Void: {};
/**
 * The key of global config visible to all resource plugins.
 */
export declare const GLOBAL_CONFIG = "solution";
/**
 * Used to track whether provision succeeded
 * Set to true when provison succeeds, to false when a new resource is added.
 */
export declare const SOLUTION_PROVISION_SUCCEEDED = "provisionSucceeded";
/**
 * Config key whose value is either javascript, typescript or csharp.
 */
export declare const PROGRAMMING_LANGUAGE = "programmingLanguage";
/**
 * Config key whose value is the default function name for adding a new function.
 */
export declare const DEFAULT_FUNC_NAME = "defaultFunctionName";
/**
 * Config key whose value is output of ARM templates deployment.
 */
export declare const ARM_TEMPLATE_OUTPUT = "armTemplateOutput";
export declare const TEAMS_FX_RESOURCE_ID_KEY = "teamsFxPluginId";
/**
 * Config key whose value is the resource group name of project.
 */
export declare const RESOURCE_GROUP_NAME = "resourceGroupName";
/**
 * Config key whose value is the resource group location of project.
 */
export declare const LOCATION = "location";
/**
 * Config key whose value is the subscription ID of project.
 */
export declare const SUBSCRIPTION_ID = "subscriptionId";
/**
 * Config key whose value is the subscription name of project.
 */
export declare const SUBSCRIPTION_NAME = "subscriptionName";
export declare const DEFAULT_PERMISSION_REQUEST: {
    resource: string;
    delegated: string[];
    application: never[];
}[];
export declare enum PluginNames {
    SQL = "fx-resource-azure-sql",
    MSID = "fx-resource-identity",
    FE = "fx-resource-frontend-hosting",
    SPFX = "fx-resource-spfx",
    BOT = "fx-resource-bot",
    AAD = "fx-resource-aad-app-for-teams",
    FUNC = "fx-resource-function",
    SA = "fx-resource-simple-auth",
    LDEBUG = "fx-resource-local-debug",
    APIM = "fx-resource-apim",
    APPST = "fx-resource-appstudio",
    SOLUTION = "solution"
}
export declare const BuiltInFeaturePluginNames: {
    appStudio: string;
    aad: string;
    bot: string;
    function: string;
    frontend: string;
    spfx: string;
    simpleAuth: string;
    identity: string;
    apim: string;
    keyVault: string;
    sql: string;
};
export declare enum SolutionError {
    InvalidSelectedPluginNames = "InvalidSelectedPluginNames",
    PluginNotFound = "PluginNotFound",
    AADPluginNotEnabled = "AADPluginNotEnabled",
    MissingPermissionsJson = "MissingPermissionsJson",
    DialogIsNotPresent = "DialogIsNotPresent",
    NoResourcePluginSelected = "NoResourcePluginSelected",
    NoAppStudioToken = "NoAppStudioToken",
    NoTeamsAppTenantId = "NoTeamsAppTenantId",
    NoUserName = "NoUserName",
    FailedToCreateResourceGroup = "FailedToCreateResourceGroup",
    FailedToListResourceGroup = "FailedToListResourceGrouop",
    FailedToListResourceGroupLocation = "FailedToListResourceGroupLocation",
    FailedToGetResourceGroupInfoInputs = "FailedToGetResourceGroupInfoInputs",
    ResourceGroupNotFound = "ResourceGroupNotFound",
    SubscriptionNotFound = "SubscriptionNotFound",
    NotLoginToAzure = "NotLoginToAzure",
    AzureAccountExtensionNotInitialized = "AzureAccountExtensionNotInitialized",
    LocalTabEndpointMissing = "LocalTabEndpointMissing",
    LocalTabDomainMissing = "LocalTabDomainMissing",
    LocalClientIDMissing = "LocalDebugClientIDMissing",
    LocalApplicationIdUrisMissing = "LocalApplicationIdUrisMissing",
    LocalClientSecretMissing = "LocalClientSecretMissing",
    CannotUpdatePermissionForSPFx = "CannotUpdatePermissionForSPFx",
    CannotAddResourceForSPFx = "CannotAddResourceForSPFx",
    FailedToParseAzureTenantId = "FailedToParseAzureTenantId",
    CannotRunProvisionInSPFxProject = "CannotRunProvisionInSPFxProject",
    CannotRunThisTaskInSPFxProject = "CannotRunThisTaskInSPFxProject",
    FrontendEndpointAndDomainNotFound = "FrontendEndpointAndDomainNotFound",
    RemoteClientIdNotFound = "RemoteClientIdNotFound",
    AddResourceNotSupport = "AddResourceNotSupport",
    AddCapabilityNotSupport = "AddCapabilityNotSupport",
    FailedToAddCapability = "FailedToAddCapability",
    NoResourceToDeploy = "NoResourceToDeploy",
    ProvisionInProgress = "ProvisionInProgress",
    DeploymentInProgress = "DeploymentInProgress",
    PublishInProgress = "PublishInProgress",
    UnknownSolutionRunningState = "UnknownSolutionRunningState",
    CannotDeployBeforeProvision = "CannotDeployBeforeProvision",
    CannotPublishBeforeProvision = "CannotPublishBeforeProvision",
    CannotLocalDebugInDifferentTenant = "CannotLocalDebugInDifferentTenant",
    NoSubscriptionFound = "NoSubscriptionFound",
    NoSubscriptionSelected = "NoSubscriptionSelected",
    FailedToGetParamForRegisterTeamsAppAndAad = "FailedToGetParamForRegisterTeamsAppAndAad",
    BotInternalError = "BotInternalError",
    InternelError = "InternelError",
    RegisterTeamsAppAndAadError = "RegisterTeamsAppAndAadError",
    GetLocalDebugConfigError = "GetLocalDebugConfigError",
    GetRemoteConfigError = "GetRemoteConfigError",
    UnsupportedPlatform = "UnsupportedPlatform",
    InvalidInput = "InvalidInput",
    FailedToCompileBicepFiles = "FailedToCompileBicepFiles",
    FailedToGetAzureCredential = "FailedToGetAzureCredential",
    FailedToGenerateArmTemplates = "FailedToGenerateArmTemplates",
    FailedToUpdateArmParameters = "FailedToUpdateArmTemplates",
    FailedToDeployArmTemplatesToAzure = "FailedToDeployArmTemplatesToAzure",
    FailedToPollArmDeploymentStatus = "FailedToPollArmDeploymentStatus",
    FailedToValidateArmTemplates = "FailedToValidateArmTemplates",
    FailedToRetrieveUserInfo = "FailedToRetrieveUserInfo",
    FeatureNotSupported = "FeatureNotSupported",
    CannotFindUserInCurrentTenant = "CannotFindUserInCurrentTenant",
    FailedToGrantPermission = "FailedToGrantPermission",
    FailedToCheckPermission = "FailedToCheckPermission",
    FailedToListCollaborator = "FailedToListCollaborator",
    EmailCannotBeEmptyOrSame = "EmailCannotBeEmptyOrSame",
    FailedToExecuteTasks = "FailedToExecuteTasks",
    FailedToGetEnvName = "FailedToGetEnvName",
    TeamsAppTenantIdNotRight = "TeamsAppTenantIdNotRight",
    AddSsoNotSupported = "AddSsoNotSupported",
    NeedEnableFeatureFlag = "NeedEnableFeatureFlag",
    SsoEnabled = "SsoEnabled",
    InvalidSsoProject = "InvalidSsoProject",
    InvalidProjectPath = "InvalidProjectPath",
    FailedToCreateAuthFiles = "FailedToCreateAuthFiles",
    FailedToUpdateAzureParameters = "FailedToUpdateAzureParameters",
    FailedToBackupFiles = "FailedToBackupFiles",
    MissingSubscriptionIdInConfig = "MissingSubscriptionIdInConfig",
    FailedToResetAppSettingsDevelopment = "FailedToResetAppSettingsDevelopment",
    FailedToLoadDotEnvFile = "FailedToLoadDotEnvFile",
    FailedToGetTeamsAppId = "FailedToGetTeamsAppId"
}
export declare const LOCAL_DEBUG_TAB_ENDPOINT = "localTabEndpoint";
export declare const LOCAL_DEBUG_TAB_DOMAIN = "localTabDomain";
export declare const LOCAL_DEBUG_BOT_DOMAIN = "localBotDomain";
export declare const BOT_DOMAIN = "validDomain";
export declare const BOT_SECTION = "bots";
export declare const COMPOSE_EXTENSIONS_SECTION = "composeExtensions";
export declare const LOCAL_WEB_APPLICATION_INFO_SOURCE = "local_applicationIdUris";
export declare const WEB_APPLICATION_INFO_SOURCE = "applicationIdUris";
export declare const LOCAL_DEBUG_AAD_ID = "local_clientId";
export declare const REMOTE_AAD_ID = "clientId";
export declare const LOCAL_APPLICATION_ID_URIS = "local_applicationIdUris";
export declare const REMOTE_APPLICATION_ID_URIS = "applicationIdUris";
export declare const LOCAL_CLIENT_SECRET = "local_clientSecret";
export declare const REMOTE_CLIENT_SECRET = "clientSecret";
export declare const REMOTE_TEAMS_APP_TENANT_ID = "teamsAppTenantId";
export declare const LOCAL_TENANT_ID = "local_tenantId";
export declare const LOCAL_DEBUG_TEAMS_APP_ID = "localDebugTeamsAppId";
export declare const REMOTE_TEAMS_APP_ID = "remoteTeamsAppId";
export declare const TEAMS_APP_ID = "teamsAppId";
export declare const AzureRoleAssignmentsHelpLink = "https://aka.ms/teamsfx-azure-role-assignments-help-link";
export declare const SharePointManageSiteAdminHelpLink = "https://aka.ms/teamsfx-sharepoint-manage-site-admin-help-link";
export declare const ViewAadAppHelpLink = "https://aka.ms/teamsfx-view-aad-app";
export declare const DoProvisionFirstError: UserError;
export declare const CancelError: UserError;
export declare enum SolutionTelemetryEvent {
    CreateStart = "create-start",
    Create = "create",
    AddResourceStart = "add-resource-start",
    AddResource = "add-resource",
    AddCapabilityStart = "add-capability-start",
    AddCapability = "add-capability",
    GrantPermissionStart = "grant-permission-start",
    GrantPermission = "grant-permission",
    CheckPermissionStart = "check-permission-start",
    CheckPermission = "check-permission",
    ListCollaboratorStart = "list-collaborator-start",
    ListCollaborator = "list-collaborator",
    GenerateArmTemplateStart = "generate-armtemplate-start",
    GenerateArmTemplate = "generate-armtemplate",
    ArmDeploymentStart = "deploy-armtemplate-start",
    ArmDeployment = "deploy-armtemplate",
    AddSsoStart = "add-sso-start",
    AddSso = "add-sso",
    AddSsoReadme = "add-sso-readme",
    DeployStart = "deploy-start",
    Deploy = "deploy",
    ProvisionStart = "provision-start",
    Provision = "provision"
}
export declare enum SolutionTelemetryProperty {
    Component = "component",
    Resources = "resources",
    Capabilities = "capabilities",
    Success = "success",
    CollaboratorCount = "collaborator-count",
    AadOwnerCount = "aad-owner-count",
    AadPermission = "aad-permission",
    ArmDeploymentError = "arm-deployment-error",
    TeamsAppPermission = "teams-app-permission",
    ProgrammingLanguage = "programming-language",
    Env = "env",
    IncludeAadManifest = "include-aad-manifest",
    ErrorCode = "error-code",
    ErrorMessage = "error-message",
    HostType = "host-type",
    SubscriptionId = "subscription-id",
    AddTabSso = "tab-sso",
    AddBotSso = "bot-sso",
    M365TenantId = "m365-tenant-id",
    PreviousSubsriptionId = "previous-subscription-id",
    PreviousM365TenantId = "previous-m365-tenant-id",
    ConfirmRes = "confirm-res"
}
export declare enum SolutionTelemetrySuccess {
    Yes = "yes",
    No = "no"
}
export declare const SolutionTelemetryComponentName = "solution";
export declare const SolutionSource = "Solution";
export declare const CoordinatorSource = "coordinator";
export declare class UnauthorizedToCheckResourceGroupError extends UserError {
    constructor(resourceGroupName: string, subscriptionId: string, subscriptionName: string);
}
export declare class FailedToCheckResourceGroupExistenceError extends UserError {
    constructor(error: unknown, resourceGroupName: string, subscriptionId: string, subscriptionName: string);
}
export declare enum Language {
    JavaScript = "javascript",
    TypeScript = "typescript",
    CSharp = "csharp"
}
export declare class AddSsoParameters {
    static readonly filePath: string;
    static readonly Bot = "bot";
    static readonly Tab = "tab";
    static readonly V3 = "V3";
    static readonly V3AuthFolder = "TeamsFx-Auth";
    static readonly Readme = "README.md";
    static readonly ReadmeCSharp = "README.txt";
    static readonly LearnMore: () => string;
    static readonly LearnMoreUrl = "https://aka.ms/teamsfx-add-sso-readme";
    static readonly AddSso = "addSso";
    static readonly AppSettings = "appsettings.json";
    static readonly AppSettingsDev = "appsettings.Development.json";
    static readonly AppSettingsToAdd: {
        Authentication: {
            ClientId: string;
            ClientSecret: string;
            OAuthAuthority: string;
        };
    };
    static readonly AppSettingsToAddForBot: {
        Authentication: {
            ClientId: string;
            ClientSecret: string;
            OAuthAuthority: string;
            ApplicationIdUri: string;
            Bot: {
                InitiateLoginEndpoint: string;
            };
        };
    };
}
export declare class UserTaskFunctionName {
    static readonly ConnectExistingApi = "connectExistingApi";
}
export interface ProvisionSubscriptionCheckResult {
    hasSwitchedSubscription: boolean;
}
export declare type FillInAzureConfigsResult = ProvisionSubscriptionCheckResult;
export declare function TabOptionItem(): OptionItem;
export declare function TabNewUIOptionItem(): OptionItem;
export declare function DashboardOptionItem(): OptionItem;
export declare function BotOptionItem(): OptionItem;
export declare function BotNewUIOptionItem(): OptionItem;
export declare function NotificationOptionItem(): OptionItem;
export declare function CommandAndResponseOptionItem(): OptionItem;
export declare function WorkflowOptionItem(): OptionItem;
export declare function ExistingTabOptionItem(): OptionItem;
export declare function MessageExtensionItem(): OptionItem;
export declare function MessageExtensionNewUIItem(): OptionItem;
export declare function TabSPFxItem(): OptionItem;
export declare function TabSPFxNewUIItem(): OptionItem;
export declare function TabSsoItem(): OptionItem;
export declare function BotSsoItem(): OptionItem;
export declare function TabNonSsoItem(): OptionItem;
export declare function TabNonSsoAndDefaultBotItem(): OptionItem;
export declare function DefaultBotAndMessageExtensionItem(): OptionItem;
export declare function M365SsoLaunchPageOptionItem(): OptionItem;
export declare function M365SearchAppOptionItem(): OptionItem;
export declare enum AzureSolutionQuestionNames {
    Capabilities = "capabilities",
    TabScopes = "tab-scopes",
    HostType = "host-type",
    AzureResources = "azure-resources",
    PluginSelectionDeploy = "deploy-plugin",
    AddResources = "add-azure-resources",
    AppName = "app-name",
    AskSub = "subscription",
    ProgrammingLanguage = "programming-language",
    Solution = "solution",
    Scenarios = "scenarios",
    Features = "features"
}
export declare function HostTypeOptionAzure(): OptionItem;
export declare function HostTypeOptionSPFx(): OptionItem;
export declare const AzureResourceSQL: OptionItem;
export declare const AzureResourceSQLNewUI: OptionItem;
export declare const AzureResourceFunction: OptionItem;
export declare const AzureResourceFunctionNewUI: OptionItem;
export declare const AzureResourceApim: OptionItem;
export declare const AzureResourceApimNewUI: OptionItem;
export declare const AzureResourceKeyVault: OptionItem;
export declare const AzureResourceKeyVaultNewUI: OptionItem;
export declare const SingleSignOnOptionItem: OptionItem;
export declare const ApiConnectionOptionItem: OptionItem;
export declare const CicdOptionItem: OptionItem;
export declare enum BotScenario {
    NotificationBot = "notificationBot",
    CommandAndResponseBot = "commandAndResponseBot",
    WorkflowBot = "workflowBot"
}
export declare const BotNotificationTriggers: {
    readonly Timer: "timer";
    readonly Http: "http";
};
export declare type BotNotificationTrigger = typeof BotNotificationTriggers[keyof typeof BotNotificationTriggers];
export declare const AzureResourcesQuestion: MultiSelectQuestion;
export declare const BotFeatureIds: () => string[];
export declare const TabFeatureIds: () => string[];
export declare const AadConstants: {
    DefaultTemplateFileName: string;
};
//# sourceMappingURL=constants.d.ts.map