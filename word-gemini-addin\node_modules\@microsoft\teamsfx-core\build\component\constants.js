"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LOCAL_WEB_APPLICATION_INFO_SOURCE = exports.COMPOSE_EXTENSIONS_SECTION = exports.BOT_SECTION = exports.BOT_DOMAIN = exports.LOCAL_DEBUG_BOT_DOMAIN = exports.LOCAL_DEBUG_TAB_DOMAIN = exports.LOCAL_DEBUG_TAB_ENDPOINT = exports.SolutionError = exports.BuiltInFeaturePluginNames = exports.PluginNames = exports.DEFAULT_PERMISSION_REQUEST = exports.SUBSCRIPTION_NAME = exports.SUBSCRIPTION_ID = exports.LOCATION = exports.RESOURCE_GROUP_NAME = exports.TEAMS_FX_RESOURCE_ID_KEY = exports.ARM_TEMPLATE_OUTPUT = exports.DEFAULT_FUNC_NAME = exports.PROGRAMMING_LANGUAGE = exports.SOLUTION_PROVISION_SUCCEEDED = exports.GLOBAL_CONFIG = exports.Void = exports.RegularExpr = exports.PathConstants = exports.FunctionAppSetting = exports.AadAppOutputs = exports.BotServiceOutputs = exports.StorageOutputs = exports.FunctionOutputs = exports.WebAppOutputs = exports.APIMOutputs = exports.KeyVaultOutputs = exports.IdentityOutputs = exports.AzureSqlOutputs = exports.ErrorConstants = exports.TelemetryConstants = exports.BicepConstants = exports.ActionTypeShell = exports.ActionTypeGroup = exports.ActionTypeCall = exports.ActionTypeFunction = exports.ActionNames = exports.languageToRuntime = exports.Runtime = exports.ProgrammingLanguage = exports.scenarioToComponent = exports.componentToScenario = exports.Scenarios = exports.AzureResources = exports.ComponentNames = void 0;
exports.HostTypeOptionAzure = exports.AzureSolutionQuestionNames = exports.M365SearchAppOptionItem = exports.M365SsoLaunchPageOptionItem = exports.DefaultBotAndMessageExtensionItem = exports.TabNonSsoAndDefaultBotItem = exports.TabNonSsoItem = exports.BotSsoItem = exports.TabSsoItem = exports.TabSPFxNewUIItem = exports.TabSPFxItem = exports.MessageExtensionNewUIItem = exports.MessageExtensionItem = exports.ExistingTabOptionItem = exports.WorkflowOptionItem = exports.CommandAndResponseOptionItem = exports.NotificationOptionItem = exports.BotNewUIOptionItem = exports.BotOptionItem = exports.DashboardOptionItem = exports.TabNewUIOptionItem = exports.TabOptionItem = exports.UserTaskFunctionName = exports.AddSsoParameters = exports.Language = exports.FailedToCheckResourceGroupExistenceError = exports.UnauthorizedToCheckResourceGroupError = exports.CoordinatorSource = exports.SolutionSource = exports.SolutionTelemetryComponentName = exports.SolutionTelemetrySuccess = exports.SolutionTelemetryProperty = exports.SolutionTelemetryEvent = exports.CancelError = exports.DoProvisionFirstError = exports.ViewAadAppHelpLink = exports.SharePointManageSiteAdminHelpLink = exports.AzureRoleAssignmentsHelpLink = exports.TEAMS_APP_ID = exports.REMOTE_TEAMS_APP_ID = exports.LOCAL_DEBUG_TEAMS_APP_ID = exports.LOCAL_TENANT_ID = exports.REMOTE_TEAMS_APP_TENANT_ID = exports.REMOTE_CLIENT_SECRET = exports.LOCAL_CLIENT_SECRET = exports.REMOTE_APPLICATION_ID_URIS = exports.LOCAL_APPLICATION_ID_URIS = exports.REMOTE_AAD_ID = exports.LOCAL_DEBUG_AAD_ID = exports.WEB_APPLICATION_INFO_SOURCE = void 0;
exports.AadConstants = exports.TabFeatureIds = exports.BotFeatureIds = exports.AzureResourcesQuestion = exports.BotNotificationTriggers = exports.BotScenario = exports.CicdOptionItem = exports.ApiConnectionOptionItem = exports.SingleSignOnOptionItem = exports.AzureResourceKeyVaultNewUI = exports.AzureResourceKeyVault = exports.AzureResourceApimNewUI = exports.AzureResourceApim = exports.AzureResourceFunctionNewUI = exports.AzureResourceFunction = exports.AzureResourceSQLNewUI = exports.AzureResourceSQL = exports.HostTypeOptionSPFx = void 0;
const tslib_1 = require("tslib");
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const ms_rest_js_1 = require("@azure/ms-rest-js");
const path_1 = tslib_1.__importDefault(require("path"));
const localizeUtils_1 = require("../common/localizeUtils");
exports.ComponentNames = {
    TeamsTab: "teams-tab",
    TeamsBot: "teams-bot",
    TeamsApi: "teams-api",
    AppManifest: "app-manifest",
    AadApp: "aad-app",
    AzureWebApp: "azure-web-app",
    AzureStorage: "azure-storage",
    BotService: "bot-service",
    SPFxTab: "spfx-tab",
    SPFx: "spfx",
    Identity: "identity",
    APIMFeature: "apim-feature",
    APIM: "apim",
    KeyVault: "key-vault",
    AzureSQL: "azure-sql",
    TabCode: "tab-code",
    BotCode: "bot-code",
    SPFxTabCode: "spfx-tab-code",
    ApiCode: "api-code",
    Function: "azure-function",
    SimpleAuth: "simple-auth",
    SSO: "sso",
    ApiConnector: "api-connector",
    CICD: "cicd",
};
exports.AzureResources = [
    exports.ComponentNames.APIM,
    exports.ComponentNames.AzureWebApp,
    exports.ComponentNames.Function,
    exports.ComponentNames.Identity,
    exports.ComponentNames.KeyVault,
    exports.ComponentNames.AzureSQL,
    exports.ComponentNames.AzureStorage,
];
var Scenarios;
(function (Scenarios) {
    Scenarios["Tab"] = "Tab";
    Scenarios["Bot"] = "Bot";
    Scenarios["Api"] = "Api";
})(Scenarios = exports.Scenarios || (exports.Scenarios = {}));
exports.componentToScenario = new Map([
    [exports.ComponentNames.TeamsApi, Scenarios.Api],
    [exports.ComponentNames.TeamsBot, Scenarios.Bot],
    [exports.ComponentNames.TeamsTab, Scenarios.Tab],
]);
exports.scenarioToComponent = new Map([
    [Scenarios.Api, exports.ComponentNames.TeamsApi],
    [Scenarios.Bot, exports.ComponentNames.TeamsBot],
    [Scenarios.Tab, exports.ComponentNames.TeamsTab],
]);
var ProgrammingLanguage;
(function (ProgrammingLanguage) {
    ProgrammingLanguage["JS"] = "javascript";
    ProgrammingLanguage["TS"] = "typescript";
    ProgrammingLanguage["CSharp"] = "csharp";
})(ProgrammingLanguage = exports.ProgrammingLanguage || (exports.ProgrammingLanguage = {}));
var Runtime;
(function (Runtime) {
    Runtime["nodejs"] = "node";
    Runtime["dotnet"] = "dotnet";
})(Runtime = exports.Runtime || (exports.Runtime = {}));
exports.languageToRuntime = new Map([
    [ProgrammingLanguage.JS, Runtime.nodejs],
    [ProgrammingLanguage.TS, Runtime.nodejs],
    [ProgrammingLanguage.CSharp, Runtime.dotnet],
]);
exports.ActionNames = {
    provision: "provision",
    configure: "configure",
    generateBicep: "generateBicep",
};
exports.ActionTypeFunction = "function";
exports.ActionTypeCall = "call";
exports.ActionTypeGroup = "group";
exports.ActionTypeShell = "shell";
exports.BicepConstants = {
    writeFile: "1",
};
exports.TelemetryConstants = {
    eventPrefix: "-start",
    properties: {
        component: "component",
        appId: "appid",
        tenantId: "tenant-id",
        success: "success",
        errorCode: "error-code",
        errorType: "error-type",
        errorMessage: "error-message",
        timeCost: "time-cost",
    },
    values: {
        yes: "yes",
        no: "no",
        userError: "user",
        systemError: "system",
    },
};
exports.ErrorConstants = {
    unhandledError: "UnhandledError",
    unhandledErrorMessage: "Unhandled Error",
};
exports.AzureSqlOutputs = {
    sqlResourceId: {
        key: "sqlResourceId",
        bicepVariable: "provisionOutputs.azureSqlOutput.value.sqlResourceId",
    },
    sqlEndpoint: {
        key: "sqlEndpoint",
        bicepVariable: "provisionOutputs.azureSqlOutput.value.sqlEndpoint",
    },
    databaseName: {
        key: "databaseName",
        bicepVariable: "provisionOutputs.azureSqlOutput.value.databaseName",
    },
};
exports.IdentityOutputs = {
    identityResourceId: {
        key: "identityResourceId",
        bicepVariable: "userAssignedIdentityProvision.outputs.identityResourceId",
    },
    identityName: {
        key: "identityName",
        bicepVariable: "provisionOutputs.identityOutput.value.identityName",
    },
    identityClientId: {
        key: "identityClientId",
        bicepVariable: "provisionOutputs.identityOutput.value.identityClientId",
    },
    identityPrincipalId: {
        key: "identityPrincipalId",
        bicepVariable: "userAssignedIdentityProvision.outputs.identityPrincipalId",
    },
};
exports.KeyVaultOutputs = {
    keyVaultResourceId: {
        key: "keyVaultResourceId",
        bicepVariable: "provisionOutputs.keyVaultOutput.value.keyVaultResourceId",
    },
    m365ClientSecretReference: {
        key: "m365ClientSecretReference",
        bicepVariable: "provisionOutputs.keyVaultOutput.value.m365ClientSecretReference",
    },
    botClientSecretReference: {
        key: "botClientSecretReference",
        bicepVariable: "provisionOutputs.keyVaultOutput.value.botClientSecretReference",
    },
};
exports.APIMOutputs = {
    serviceResourceId: {
        key: "serviceResourceId",
        bicepVariable: "provisionOutputs.apimOutput.value.serviceResourceId",
    },
    productResourceId: {
        key: "productResourceId",
        bicepVariable: "provisionOutputs.apimOutput.value.productResourceId",
    },
    authServerResourceId: {
        key: "authServerResourceId",
    },
    apimClientAADObjectId: {
        key: "apimClientAADObjectId",
    },
    apimClientAADClientId: {
        key: "apimClientAADClientId",
    },
    apimClientAADClientSecret: {
        key: "apimClientAADClientSecret",
    },
};
exports.WebAppOutputs = {
    resourceId: {
        key: "resourceId",
        bicepVariable: "provisionOutputs.azureWebApp{{scenario}}Output.value.resourceId",
    },
    endpoint: {
        key: "siteEndpoint",
        bicepVariable: "provisionOutputs.azureWebApp{{scenario}}Output.value.siteEndpoint",
    },
    endpointAsParam: {
        key: "siteEndpointAsParam",
        bicepVariable: "azureWebApp{{scenario}}Provision.outputs.siteEndpoint",
    },
};
exports.FunctionOutputs = {
    resourceId: {
        key: "functionAppResourceId",
        bicepVariable: "provisionOutputs.azureFunction{{scenario}}Output.value.functionAppResourceId",
    },
    endpoint: {
        key: "functionEndpoint",
        bicepVariable: "provisionOutputs.azureFunction{{scenario}}Output.value.functionEndpoint",
    },
    endpointAsParam: {
        key: "functionEndpointAsParam",
        bicepVariable: "azureFunction{{scenario}}Provision.outputs.functionEndpoint",
    },
};
exports.StorageOutputs = {
    endpoint: {
        key: "endpoint",
        bicepVariable: "provisionOutputs.azureStorage{{scenario}}Output.value.endpoint",
    },
    storageResourceId: {
        key: "storageResourceId",
        bicepVariable: "provisionOutputs.azureStorage{{scenario}}Output.value.storageResourceId",
    },
    domain: {
        key: "domain",
        bicepVariable: "provisionOutputs.azureStorage{{scenario}}Output.value.domain",
    },
    indexPath: {
        key: "indexPath",
        bicepVariable: "provisionOutputs.azureStorage{{scenario}}Output.value.indexPath",
    },
};
exports.BotServiceOutputs = {
    botId: {
        key: "botId",
    },
    botPassword: {
        key: "botPassword",
    },
};
exports.AadAppOutputs = {
    applicationIdUris: {
        key: "applicationIdUris",
    },
    clientId: {
        key: "clientId",
    },
    clientSecret: {
        key: "clientSecret",
    },
    objectId: {
        key: "objectId",
    },
    oauth2PermissionScopeId: {
        key: "oauth2PermissionScopeId",
    },
    frontendEndpoint: {
        key: "frontendEndpoint",
    },
    botId: {
        key: "botId",
    },
    botEndpoint: {
        key: "botEndpoint",
    },
    domain: {
        key: "domain",
    },
    endpoint: {
        key: "endpoint",
    },
    oauthAuthority: {
        key: "oauthAuthority",
    },
    oauthHost: {
        key: "oauthHost",
    },
    tenantId: {
        key: "tenantId",
    },
};
exports.FunctionAppSetting = {
    keys: {
        allowedAppIds: "ALLOWED_APP_IDS",
    },
    allowedAppIdSep: ";",
};
exports.PathConstants = {
    botWorkingDir: "bot",
    apiWorkingDir: "api",
    tabWorkingDir: "tabs",
    dotnetWorkingDir: ".",
    npmPackageFolder: "node_modules",
    nodePackageFile: "package.json",
    functionExtensionsFolder: "bin",
    functionExtensionsFile: "extensions.csproj",
    deploymentInfoFolder: ".deployment",
    deploymentInfoFile: "deployment.json",
    nodeArtifactFolder: "build",
    dotnetArtifactFolder: "publish",
    reactTabIndexPath: "/index.html#",
    blazorTabIndexPath: "/",
};
exports.RegularExpr = {
    validFunctionNamePattern: /^[a-zA-Z][\w-]{0,126}$/,
};
/**
 * Void is used to construct Result<Void, FxError>.
 * e.g. return ok(Void);
 * It exists because ok(void) does not compile.
 */
exports.Void = {};
/**
 * The key of global config visible to all resource plugins.
 */
exports.GLOBAL_CONFIG = "solution";
// export const SELECTED_PLUGINS = "selectedPlugins";
/**
 * Used to track whether provision succeeded
 * Set to true when provison succeeds, to false when a new resource is added.
 */
exports.SOLUTION_PROVISION_SUCCEEDED = "provisionSucceeded";
/**
 * Config key whose value is either javascript, typescript or csharp.
 */
exports.PROGRAMMING_LANGUAGE = "programmingLanguage";
/**
 * Config key whose value is the default function name for adding a new function.
 */
exports.DEFAULT_FUNC_NAME = "defaultFunctionName";
/**
 * Config key whose value is output of ARM templates deployment.
 */
exports.ARM_TEMPLATE_OUTPUT = "armTemplateOutput";
exports.TEAMS_FX_RESOURCE_ID_KEY = "teamsFxPluginId";
/**
 * Config key whose value is the resource group name of project.
 */
exports.RESOURCE_GROUP_NAME = "resourceGroupName";
/**
 * Config key whose value is the resource group location of project.
 */
exports.LOCATION = "location";
/**
 * Config key whose value is the subscription ID of project.
 */
exports.SUBSCRIPTION_ID = "subscriptionId";
/**
 * Config key whose value is the subscription name of project.
 */
exports.SUBSCRIPTION_NAME = "subscriptionName";
exports.DEFAULT_PERMISSION_REQUEST = [
    {
        resource: "Microsoft Graph",
        delegated: ["User.Read"],
        application: [],
    },
];
var PluginNames;
(function (PluginNames) {
    PluginNames["SQL"] = "fx-resource-azure-sql";
    PluginNames["MSID"] = "fx-resource-identity";
    PluginNames["FE"] = "fx-resource-frontend-hosting";
    PluginNames["SPFX"] = "fx-resource-spfx";
    PluginNames["BOT"] = "fx-resource-bot";
    PluginNames["AAD"] = "fx-resource-aad-app-for-teams";
    PluginNames["FUNC"] = "fx-resource-function";
    PluginNames["SA"] = "fx-resource-simple-auth";
    PluginNames["LDEBUG"] = "fx-resource-local-debug";
    PluginNames["APIM"] = "fx-resource-apim";
    PluginNames["APPST"] = "fx-resource-appstudio";
    PluginNames["SOLUTION"] = "solution";
})(PluginNames = exports.PluginNames || (exports.PluginNames = {}));
exports.BuiltInFeaturePluginNames = {
    appStudio: "fx-resource-appstudio",
    aad: "fx-resource-aad-app-for-teams",
    bot: "fx-resource-bot",
    function: "fx-resource-function",
    frontend: "fx-resource-frontend-hosting",
    spfx: "fx-resource-spfx",
    simpleAuth: "fx-resource-simple-auth",
    identity: "fx-resource-identity",
    apim: "fx-resource-apim",
    keyVault: "fx-resource-key-vault",
    sql: "fx-resource-azure-sql",
};
var SolutionError;
(function (SolutionError) {
    SolutionError["InvalidSelectedPluginNames"] = "InvalidSelectedPluginNames";
    SolutionError["PluginNotFound"] = "PluginNotFound";
    SolutionError["AADPluginNotEnabled"] = "AADPluginNotEnabled";
    SolutionError["MissingPermissionsJson"] = "MissingPermissionsJson";
    SolutionError["DialogIsNotPresent"] = "DialogIsNotPresent";
    SolutionError["NoResourcePluginSelected"] = "NoResourcePluginSelected";
    SolutionError["NoAppStudioToken"] = "NoAppStudioToken";
    SolutionError["NoTeamsAppTenantId"] = "NoTeamsAppTenantId";
    SolutionError["NoUserName"] = "NoUserName";
    SolutionError["FailedToCreateResourceGroup"] = "FailedToCreateResourceGroup";
    SolutionError["FailedToListResourceGroup"] = "FailedToListResourceGrouop";
    SolutionError["FailedToListResourceGroupLocation"] = "FailedToListResourceGroupLocation";
    SolutionError["FailedToGetResourceGroupInfoInputs"] = "FailedToGetResourceGroupInfoInputs";
    SolutionError["ResourceGroupNotFound"] = "ResourceGroupNotFound";
    SolutionError["SubscriptionNotFound"] = "SubscriptionNotFound";
    SolutionError["NotLoginToAzure"] = "NotLoginToAzure";
    SolutionError["AzureAccountExtensionNotInitialized"] = "AzureAccountExtensionNotInitialized";
    SolutionError["LocalTabEndpointMissing"] = "LocalTabEndpointMissing";
    SolutionError["LocalTabDomainMissing"] = "LocalTabDomainMissing";
    SolutionError["LocalClientIDMissing"] = "LocalDebugClientIDMissing";
    SolutionError["LocalApplicationIdUrisMissing"] = "LocalApplicationIdUrisMissing";
    SolutionError["LocalClientSecretMissing"] = "LocalClientSecretMissing";
    SolutionError["CannotUpdatePermissionForSPFx"] = "CannotUpdatePermissionForSPFx";
    SolutionError["CannotAddResourceForSPFx"] = "CannotAddResourceForSPFx";
    SolutionError["FailedToParseAzureTenantId"] = "FailedToParseAzureTenantId";
    SolutionError["CannotRunProvisionInSPFxProject"] = "CannotRunProvisionInSPFxProject";
    SolutionError["CannotRunThisTaskInSPFxProject"] = "CannotRunThisTaskInSPFxProject";
    SolutionError["FrontendEndpointAndDomainNotFound"] = "FrontendEndpointAndDomainNotFound";
    SolutionError["RemoteClientIdNotFound"] = "RemoteClientIdNotFound";
    SolutionError["AddResourceNotSupport"] = "AddResourceNotSupport";
    SolutionError["AddCapabilityNotSupport"] = "AddCapabilityNotSupport";
    SolutionError["FailedToAddCapability"] = "FailedToAddCapability";
    SolutionError["NoResourceToDeploy"] = "NoResourceToDeploy";
    SolutionError["ProvisionInProgress"] = "ProvisionInProgress";
    SolutionError["DeploymentInProgress"] = "DeploymentInProgress";
    SolutionError["PublishInProgress"] = "PublishInProgress";
    SolutionError["UnknownSolutionRunningState"] = "UnknownSolutionRunningState";
    SolutionError["CannotDeployBeforeProvision"] = "CannotDeployBeforeProvision";
    SolutionError["CannotPublishBeforeProvision"] = "CannotPublishBeforeProvision";
    SolutionError["CannotLocalDebugInDifferentTenant"] = "CannotLocalDebugInDifferentTenant";
    SolutionError["NoSubscriptionFound"] = "NoSubscriptionFound";
    SolutionError["NoSubscriptionSelected"] = "NoSubscriptionSelected";
    SolutionError["FailedToGetParamForRegisterTeamsAppAndAad"] = "FailedToGetParamForRegisterTeamsAppAndAad";
    SolutionError["BotInternalError"] = "BotInternalError";
    SolutionError["InternelError"] = "InternelError";
    SolutionError["RegisterTeamsAppAndAadError"] = "RegisterTeamsAppAndAadError";
    SolutionError["GetLocalDebugConfigError"] = "GetLocalDebugConfigError";
    SolutionError["GetRemoteConfigError"] = "GetRemoteConfigError";
    SolutionError["UnsupportedPlatform"] = "UnsupportedPlatform";
    SolutionError["InvalidInput"] = "InvalidInput";
    SolutionError["FailedToCompileBicepFiles"] = "FailedToCompileBicepFiles";
    SolutionError["FailedToGetAzureCredential"] = "FailedToGetAzureCredential";
    SolutionError["FailedToGenerateArmTemplates"] = "FailedToGenerateArmTemplates";
    SolutionError["FailedToUpdateArmParameters"] = "FailedToUpdateArmTemplates";
    SolutionError["FailedToDeployArmTemplatesToAzure"] = "FailedToDeployArmTemplatesToAzure";
    SolutionError["FailedToPollArmDeploymentStatus"] = "FailedToPollArmDeploymentStatus";
    SolutionError["FailedToValidateArmTemplates"] = "FailedToValidateArmTemplates";
    SolutionError["FailedToRetrieveUserInfo"] = "FailedToRetrieveUserInfo";
    SolutionError["FeatureNotSupported"] = "FeatureNotSupported";
    SolutionError["CannotFindUserInCurrentTenant"] = "CannotFindUserInCurrentTenant";
    SolutionError["FailedToGrantPermission"] = "FailedToGrantPermission";
    SolutionError["FailedToCheckPermission"] = "FailedToCheckPermission";
    SolutionError["FailedToListCollaborator"] = "FailedToListCollaborator";
    SolutionError["EmailCannotBeEmptyOrSame"] = "EmailCannotBeEmptyOrSame";
    SolutionError["FailedToExecuteTasks"] = "FailedToExecuteTasks";
    SolutionError["FailedToGetEnvName"] = "FailedToGetEnvName";
    SolutionError["TeamsAppTenantIdNotRight"] = "TeamsAppTenantIdNotRight";
    SolutionError["AddSsoNotSupported"] = "AddSsoNotSupported";
    SolutionError["NeedEnableFeatureFlag"] = "NeedEnableFeatureFlag";
    SolutionError["SsoEnabled"] = "SsoEnabled";
    SolutionError["InvalidSsoProject"] = "InvalidSsoProject";
    SolutionError["InvalidProjectPath"] = "InvalidProjectPath";
    SolutionError["FailedToCreateAuthFiles"] = "FailedToCreateAuthFiles";
    SolutionError["FailedToUpdateAzureParameters"] = "FailedToUpdateAzureParameters";
    SolutionError["FailedToBackupFiles"] = "FailedToBackupFiles";
    SolutionError["MissingSubscriptionIdInConfig"] = "MissingSubscriptionIdInConfig";
    SolutionError["FailedToResetAppSettingsDevelopment"] = "FailedToResetAppSettingsDevelopment";
    SolutionError["FailedToLoadDotEnvFile"] = "FailedToLoadDotEnvFile";
    SolutionError["FailedToGetTeamsAppId"] = "FailedToGetTeamsAppId";
})(SolutionError = exports.SolutionError || (exports.SolutionError = {}));
exports.LOCAL_DEBUG_TAB_ENDPOINT = "localTabEndpoint";
exports.LOCAL_DEBUG_TAB_DOMAIN = "localTabDomain";
exports.LOCAL_DEBUG_BOT_DOMAIN = "localBotDomain";
exports.BOT_DOMAIN = "validDomain";
exports.BOT_SECTION = "bots";
exports.COMPOSE_EXTENSIONS_SECTION = "composeExtensions";
exports.LOCAL_WEB_APPLICATION_INFO_SOURCE = "local_applicationIdUris";
exports.WEB_APPLICATION_INFO_SOURCE = "applicationIdUris";
exports.LOCAL_DEBUG_AAD_ID = "local_clientId";
exports.REMOTE_AAD_ID = "clientId";
exports.LOCAL_APPLICATION_ID_URIS = "local_applicationIdUris";
exports.REMOTE_APPLICATION_ID_URIS = "applicationIdUris";
exports.LOCAL_CLIENT_SECRET = "local_clientSecret";
exports.REMOTE_CLIENT_SECRET = "clientSecret";
exports.REMOTE_TEAMS_APP_TENANT_ID = "teamsAppTenantId";
exports.LOCAL_TENANT_ID = "local_tenantId";
// Teams App Id for local debug
exports.LOCAL_DEBUG_TEAMS_APP_ID = "localDebugTeamsAppId";
// Teams App Id for remote
exports.REMOTE_TEAMS_APP_ID = "remoteTeamsAppId";
exports.TEAMS_APP_ID = "teamsAppId";
exports.AzureRoleAssignmentsHelpLink = "https://aka.ms/teamsfx-azure-role-assignments-help-link";
exports.SharePointManageSiteAdminHelpLink = "https://aka.ms/teamsfx-sharepoint-manage-site-admin-help-link";
exports.ViewAadAppHelpLink = "https://aka.ms/teamsfx-view-aad-app";
exports.DoProvisionFirstError = new teamsfx_api_1.UserError("DoProvisionFirst", "DoProvisionFirst", "Solution");
exports.CancelError = new teamsfx_api_1.UserError("Solution", "UserCancel", "UserCancel");
// This is the max length specified in
// https://developer.microsoft.com/en-us/json-schemas/teams/v1.7/MicrosoftTeams.schema.json
var SolutionTelemetryEvent;
(function (SolutionTelemetryEvent) {
    SolutionTelemetryEvent["CreateStart"] = "create-start";
    SolutionTelemetryEvent["Create"] = "create";
    SolutionTelemetryEvent["AddResourceStart"] = "add-resource-start";
    SolutionTelemetryEvent["AddResource"] = "add-resource";
    SolutionTelemetryEvent["AddCapabilityStart"] = "add-capability-start";
    SolutionTelemetryEvent["AddCapability"] = "add-capability";
    SolutionTelemetryEvent["GrantPermissionStart"] = "grant-permission-start";
    SolutionTelemetryEvent["GrantPermission"] = "grant-permission";
    SolutionTelemetryEvent["CheckPermissionStart"] = "check-permission-start";
    SolutionTelemetryEvent["CheckPermission"] = "check-permission";
    SolutionTelemetryEvent["ListCollaboratorStart"] = "list-collaborator-start";
    SolutionTelemetryEvent["ListCollaborator"] = "list-collaborator";
    SolutionTelemetryEvent["GenerateArmTemplateStart"] = "generate-armtemplate-start";
    SolutionTelemetryEvent["GenerateArmTemplate"] = "generate-armtemplate";
    SolutionTelemetryEvent["ArmDeploymentStart"] = "deploy-armtemplate-start";
    SolutionTelemetryEvent["ArmDeployment"] = "deploy-armtemplate";
    SolutionTelemetryEvent["AddSsoStart"] = "add-sso-start";
    SolutionTelemetryEvent["AddSso"] = "add-sso";
    SolutionTelemetryEvent["AddSsoReadme"] = "add-sso-readme";
    SolutionTelemetryEvent["DeployStart"] = "deploy-start";
    SolutionTelemetryEvent["Deploy"] = "deploy";
    SolutionTelemetryEvent["ProvisionStart"] = "provision-start";
    SolutionTelemetryEvent["Provision"] = "provision";
})(SolutionTelemetryEvent = exports.SolutionTelemetryEvent || (exports.SolutionTelemetryEvent = {}));
var SolutionTelemetryProperty;
(function (SolutionTelemetryProperty) {
    SolutionTelemetryProperty["Component"] = "component";
    SolutionTelemetryProperty["Resources"] = "resources";
    SolutionTelemetryProperty["Capabilities"] = "capabilities";
    SolutionTelemetryProperty["Success"] = "success";
    SolutionTelemetryProperty["CollaboratorCount"] = "collaborator-count";
    SolutionTelemetryProperty["AadOwnerCount"] = "aad-owner-count";
    SolutionTelemetryProperty["AadPermission"] = "aad-permission";
    SolutionTelemetryProperty["ArmDeploymentError"] = "arm-deployment-error";
    SolutionTelemetryProperty["TeamsAppPermission"] = "teams-app-permission";
    SolutionTelemetryProperty["ProgrammingLanguage"] = "programming-language";
    SolutionTelemetryProperty["Env"] = "env";
    SolutionTelemetryProperty["IncludeAadManifest"] = "include-aad-manifest";
    SolutionTelemetryProperty["ErrorCode"] = "error-code";
    SolutionTelemetryProperty["ErrorMessage"] = "error-message";
    SolutionTelemetryProperty["HostType"] = "host-type";
    SolutionTelemetryProperty["SubscriptionId"] = "subscription-id";
    SolutionTelemetryProperty["AddTabSso"] = "tab-sso";
    SolutionTelemetryProperty["AddBotSso"] = "bot-sso";
    SolutionTelemetryProperty["M365TenantId"] = "m365-tenant-id";
    SolutionTelemetryProperty["PreviousSubsriptionId"] = "previous-subscription-id";
    SolutionTelemetryProperty["PreviousM365TenantId"] = "previous-m365-tenant-id";
    SolutionTelemetryProperty["ConfirmRes"] = "confirm-res";
})(SolutionTelemetryProperty = exports.SolutionTelemetryProperty || (exports.SolutionTelemetryProperty = {}));
var SolutionTelemetrySuccess;
(function (SolutionTelemetrySuccess) {
    SolutionTelemetrySuccess["Yes"] = "yes";
    SolutionTelemetrySuccess["No"] = "no";
})(SolutionTelemetrySuccess = exports.SolutionTelemetrySuccess || (exports.SolutionTelemetrySuccess = {}));
exports.SolutionTelemetryComponentName = "solution";
exports.SolutionSource = "Solution";
exports.CoordinatorSource = "coordinator";
class UnauthorizedToCheckResourceGroupError extends teamsfx_api_1.UserError {
    constructor(resourceGroupName, subscriptionId, subscriptionName) {
        const subscriptionInfoString = subscriptionId + (subscriptionName.length > 0 ? `(${subscriptionName})` : "");
        super(exports.SolutionSource, new.target.name, localizeUtils_1.getLocalizedString("error.rgUnauthorizedError", resourceGroupName, subscriptionInfoString));
    }
}
exports.UnauthorizedToCheckResourceGroupError = UnauthorizedToCheckResourceGroupError;
class FailedToCheckResourceGroupExistenceError extends teamsfx_api_1.UserError {
    constructor(error, resourceGroupName, subscriptionId, subscriptionName) {
        const subscriptionInfoString = subscriptionId + (subscriptionName.length > 0 ? `(${subscriptionName})` : "");
        const baseErrorMessage = localizeUtils_1.getLocalizedString("error.rgCheckBaseError", resourceGroupName, subscriptionInfoString);
        if (error instanceof ms_rest_js_1.RestError) {
            // Avoid sensitive information like request headers in the error message.
            const rawErrorString = JSON.stringify({
                code: error.code,
                statusCode: error.statusCode,
                body: error.body,
                name: error.name,
                message: error.message,
            });
            super(exports.SolutionSource, new.target.name, `${baseErrorMessage}, error: '${rawErrorString}'`);
        }
        else if (error instanceof Error) {
            // Reuse the original error object to prevent losing the stack info
            error.message = `${baseErrorMessage}, error: '${error.message}'`;
            super({ error, source: exports.SolutionSource });
        }
        else {
            super(exports.SolutionSource, new.target.name, `${baseErrorMessage}, error: '${JSON.stringify(error)}'`);
        }
    }
}
exports.FailedToCheckResourceGroupExistenceError = FailedToCheckResourceGroupExistenceError;
var Language;
(function (Language) {
    Language["JavaScript"] = "javascript";
    Language["TypeScript"] = "typescript";
    Language["CSharp"] = "csharp";
})(Language = exports.Language || (exports.Language = {}));
class AddSsoParameters {
}
exports.AddSsoParameters = AddSsoParameters;
AddSsoParameters.filePath = path_1.default.join("plugins", "resource", "aad", "auth");
AddSsoParameters.Bot = "bot";
AddSsoParameters.Tab = "tab";
AddSsoParameters.V3 = "V3";
AddSsoParameters.V3AuthFolder = "TeamsFx-Auth";
AddSsoParameters.Readme = "README.md";
AddSsoParameters.ReadmeCSharp = "README.txt";
AddSsoParameters.LearnMore = () => localizeUtils_1.getLocalizedString("core.provision.learnMore");
AddSsoParameters.LearnMoreUrl = "https://aka.ms/teamsfx-add-sso-readme";
AddSsoParameters.AddSso = "addSso";
AddSsoParameters.AppSettings = "appsettings.json";
AddSsoParameters.AppSettingsDev = "appsettings.Development.json";
AddSsoParameters.AppSettingsToAdd = {
    Authentication: {
        ClientId: "$clientId$",
        ClientSecret: "$client-secret$",
        OAuthAuthority: "$oauthAuthority$",
    },
};
AddSsoParameters.AppSettingsToAddForBot = {
    Authentication: {
        ClientId: "$clientId$",
        ClientSecret: "$client-secret$",
        OAuthAuthority: "$oauthAuthority$",
        ApplicationIdUri: "$applicationIdUri$",
        Bot: {
            InitiateLoginEndpoint: "$initiateLoginEndpoint$",
        },
    },
};
class UserTaskFunctionName {
}
exports.UserTaskFunctionName = UserTaskFunctionName;
UserTaskFunctionName.ConnectExistingApi = "connectExistingApi";
function TabOptionItem() {
    return {
        id: "Tab",
        label: localizeUtils_1.getLocalizedString("core.TabOption.label"),
        cliName: "tab",
        description: localizeUtils_1.getLocalizedString("core.TabOption.description"),
        detail: localizeUtils_1.getLocalizedString("core.TabOption.detail"),
    };
}
exports.TabOptionItem = TabOptionItem;
function TabNewUIOptionItem() {
    return {
        id: "Tab",
        label: `$(browser) ${localizeUtils_1.getLocalizedString("core.TabOption.labelNew")}`,
        cliName: "tab",
        detail: localizeUtils_1.getLocalizedString("core.TabOption.detailNew"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.scenario"),
        data: "https://aka.ms/teamsfx-tab-with-sso",
        buttons: [
            {
                iconPath: "file-symlink-file",
                tooltip: localizeUtils_1.getLocalizedString("core.option.github"),
                command: "fx-extension.openTutorial",
            },
        ],
    };
}
exports.TabNewUIOptionItem = TabNewUIOptionItem;
function DashboardOptionItem() {
    return {
        id: "dashboard-tab",
        label: `$(browser) ${localizeUtils_1.getLocalizedString("core.DashboardOption.label")}`,
        description: localizeUtils_1.getLocalizedString("core.Option.preview"),
        cliName: "dashboard-tab",
        detail: localizeUtils_1.getLocalizedString("core.DashboardOption.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.scenario"),
        data: "https://aka.ms/teamsfx-dashboard-app",
        buttons: [
            {
                iconPath: "file-symlink-file",
                tooltip: localizeUtils_1.getLocalizedString("core.option.github"),
                command: "fx-extension.openTutorial",
            },
        ],
    };
}
exports.DashboardOptionItem = DashboardOptionItem;
function BotOptionItem() {
    return {
        id: "Bot",
        label: "Bot",
        cliName: "bot",
        description: localizeUtils_1.getLocalizedString("core.BotOption.description"),
        detail: localizeUtils_1.getLocalizedString("core.BotOption.detail"),
    };
}
exports.BotOptionItem = BotOptionItem;
function BotNewUIOptionItem() {
    return {
        id: "Bot",
        label: `$(hubot) ${localizeUtils_1.getLocalizedString("core.BotNewUIOption.label")}`,
        cliName: "bot",
        detail: localizeUtils_1.getLocalizedString("core.BotNewUIOption.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.basic"),
    };
}
exports.BotNewUIOptionItem = BotNewUIOptionItem;
function NotificationOptionItem() {
    return {
        // For default option, id and cliName must be the same
        id: "Notification",
        label: `$(hubot) ${localizeUtils_1.getLocalizedString("core.NotificationOption.label")}`,
        description: localizeUtils_1.getLocalizedString("core.Option.recommend"),
        cliName: "notification",
        detail: localizeUtils_1.getLocalizedString("core.NotificationOption.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.scenario"),
        data: "https://aka.ms/teamsfx-send-notification",
        buttons: [
            {
                iconPath: "file-symlink-file",
                tooltip: localizeUtils_1.getLocalizedString("core.option.github"),
                command: "fx-extension.openTutorial",
            },
        ],
    };
}
exports.NotificationOptionItem = NotificationOptionItem;
function CommandAndResponseOptionItem() {
    return {
        // id must match cli `yargsHelp`
        id: "command-bot",
        label: `$(hubot) ${localizeUtils_1.getLocalizedString("core.CommandAndResponseOption.label")}`,
        description: localizeUtils_1.getLocalizedString("core.Option.recommend"),
        cliName: "command-bot",
        detail: localizeUtils_1.getLocalizedString("core.CommandAndResponseOption.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.scenario"),
        data: "https://aka.ms/teamsfx-create-command",
        buttons: [
            {
                iconPath: "file-symlink-file",
                tooltip: localizeUtils_1.getLocalizedString("core.option.github"),
                command: "fx-extension.openTutorial",
            },
        ],
    };
}
exports.CommandAndResponseOptionItem = CommandAndResponseOptionItem;
function WorkflowOptionItem() {
    return {
        // id must match cli `yargsHelp`
        id: "workflow-bot",
        label: `$(hubot) ${localizeUtils_1.getLocalizedString("core.WorkflowOption.label")}`,
        description: localizeUtils_1.getLocalizedString("core.Option.recommend"),
        cliName: "workflow-bot",
        detail: localizeUtils_1.getLocalizedString("core.WorkflowOption.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.scenario"),
        data: "https://aka.ms/teamsfx-create-workflow",
        buttons: [
            {
                iconPath: "file-symlink-file",
                tooltip: localizeUtils_1.getLocalizedString("core.option.github"),
                command: "fx-extension.openTutorial",
            },
        ],
    };
}
exports.WorkflowOptionItem = WorkflowOptionItem;
function ExistingTabOptionItem() {
    return {
        id: "ExistingTab",
        label: `$(browser) ${localizeUtils_1.getLocalizedString("core.ExistingTabOption.label")}`,
        cliName: "existing-tab",
        detail: localizeUtils_1.getLocalizedString("core.ExistingTabOption.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.scenario"),
        data: "https://aka.ms/teamsfx-embed-existing-web",
        buttons: [
            {
                iconPath: "tasklist",
                tooltip: localizeUtils_1.getLocalizedString("core.option.tutorial"),
                command: "fx-extension.openTutorial",
            },
        ],
    };
}
exports.ExistingTabOptionItem = ExistingTabOptionItem;
function MessageExtensionItem() {
    return {
        id: "MessagingExtension",
        label: localizeUtils_1.getLocalizedString("core.MessageExtensionOption.label"),
        cliName: "message-extension",
        description: localizeUtils_1.getLocalizedString("core.MessageExtensionOption.description"),
        detail: localizeUtils_1.getLocalizedString("core.MessageExtensionOption.detail"),
    };
}
exports.MessageExtensionItem = MessageExtensionItem;
function MessageExtensionNewUIItem() {
    return {
        id: "MessagingExtension",
        label: `$(comment-discussion) ${localizeUtils_1.getLocalizedString("core.MessageExtensionOption.labelNew")}`,
        cliName: "message-extension",
        detail: localizeUtils_1.getLocalizedString("core.MessageExtensionOption.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.basic"),
    };
}
exports.MessageExtensionNewUIItem = MessageExtensionNewUIItem;
function TabSPFxItem() {
    return {
        id: "TabSPFx",
        label: localizeUtils_1.getLocalizedString("core.TabSPFxOption.label"),
        cliName: "tab-spfx",
        description: localizeUtils_1.getLocalizedString("core.TabSPFxOption.description"),
        detail: localizeUtils_1.getLocalizedString("core.TabSPFxOption.detail"),
    };
}
exports.TabSPFxItem = TabSPFxItem;
function TabSPFxNewUIItem() {
    return {
        id: "TabSPFx",
        label: `$(browser) ${localizeUtils_1.getLocalizedString("core.TabSPFxOption.labelNew")}`,
        cliName: "tab-spfx",
        detail: localizeUtils_1.getLocalizedString("core.TabSPFxOption.detailNew"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.scenario"),
    };
}
exports.TabSPFxNewUIItem = TabSPFxNewUIItem;
function TabSsoItem() {
    return {
        id: "TabSSO",
        label: "TabSSO",
        cliName: "tab-sso",
        description: localizeUtils_1.getLocalizedString("core.TabSso.description"),
        detail: localizeUtils_1.getLocalizedString("core.TabSso.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.scenario"),
    };
}
exports.TabSsoItem = TabSsoItem;
function BotSsoItem() {
    return {
        id: "BotSSO",
        label: "BotSSO",
        cliName: "bot-sso",
        description: localizeUtils_1.getLocalizedString("core.BotSso.description"),
        detail: localizeUtils_1.getLocalizedString("core.BotSso.detail"),
    };
}
exports.BotSsoItem = BotSsoItem;
function TabNonSsoItem() {
    return {
        id: "TabNonSso",
        label: `$(browser) ${localizeUtils_1.getLocalizedString("core.TabNonSso.label")}`,
        cliName: "tab-non-sso",
        detail: localizeUtils_1.getLocalizedString("core.TabNonSso.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.basic"),
    };
}
exports.TabNonSsoItem = TabNonSsoItem;
function TabNonSsoAndDefaultBotItem() {
    return {
        id: "TabNonSsoAndBot",
        label: "", // No need to set display name as this option won't be shown in UI
    };
}
exports.TabNonSsoAndDefaultBotItem = TabNonSsoAndDefaultBotItem;
function DefaultBotAndMessageExtensionItem() {
    return {
        id: "BotAndMessageExtension",
        label: "", // No need to set display name as this option won't be shown in UI
    };
}
exports.DefaultBotAndMessageExtensionItem = DefaultBotAndMessageExtensionItem;
function M365SsoLaunchPageOptionItem() {
    return {
        id: "M365SsoLaunchPage",
        label: `$(browser) ${localizeUtils_1.getLocalizedString("core.M365SsoLaunchPageOptionItem.label")}`,
        cliName: "sso-launch-page",
        detail: localizeUtils_1.getLocalizedString("core.M365SsoLaunchPageOptionItem.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.m365"),
    };
}
exports.M365SsoLaunchPageOptionItem = M365SsoLaunchPageOptionItem;
function M365SearchAppOptionItem() {
    return {
        id: "M365SearchApp",
        label: `$(comment-discussion) ${localizeUtils_1.getLocalizedString("core.M365SearchAppOptionItem.label")}`,
        cliName: "search-app",
        detail: localizeUtils_1.getLocalizedString("core.M365SearchAppOptionItem.detail"),
        groupName: localizeUtils_1.getLocalizedString("core.options.separator.m365"),
    };
}
exports.M365SearchAppOptionItem = M365SearchAppOptionItem;
var AzureSolutionQuestionNames;
(function (AzureSolutionQuestionNames) {
    AzureSolutionQuestionNames["Capabilities"] = "capabilities";
    AzureSolutionQuestionNames["TabScopes"] = "tab-scopes";
    AzureSolutionQuestionNames["HostType"] = "host-type";
    AzureSolutionQuestionNames["AzureResources"] = "azure-resources";
    AzureSolutionQuestionNames["PluginSelectionDeploy"] = "deploy-plugin";
    AzureSolutionQuestionNames["AddResources"] = "add-azure-resources";
    AzureSolutionQuestionNames["AppName"] = "app-name";
    AzureSolutionQuestionNames["AskSub"] = "subscription";
    AzureSolutionQuestionNames["ProgrammingLanguage"] = "programming-language";
    AzureSolutionQuestionNames["Solution"] = "solution";
    AzureSolutionQuestionNames["Scenarios"] = "scenarios";
    AzureSolutionQuestionNames["Features"] = "features";
})(AzureSolutionQuestionNames = exports.AzureSolutionQuestionNames || (exports.AzureSolutionQuestionNames = {}));
function HostTypeOptionAzure() {
    return {
        id: "Azure",
        label: localizeUtils_1.getLocalizedString("core.HostTypeOptionAzure.label"),
        cliName: "azure",
    };
}
exports.HostTypeOptionAzure = HostTypeOptionAzure;
function HostTypeOptionSPFx() {
    return {
        id: "SPFx",
        label: localizeUtils_1.getLocalizedString("core.HostTypeOptionSPFx.label"),
        cliName: "spfx",
    };
}
exports.HostTypeOptionSPFx = HostTypeOptionSPFx;
exports.AzureResourceSQL = {
    id: "sql",
    label: localizeUtils_1.getLocalizedString("core.AzureResourceSQL.label"),
    description: localizeUtils_1.getLocalizedString("core.AzureResourceSQL.description"),
};
exports.AzureResourceSQLNewUI = {
    id: "sql",
    label: `$(azure) ${localizeUtils_1.getLocalizedString("core.AzureResourceSQLNewUI.label")}`,
    detail: localizeUtils_1.getLocalizedString("core.AzureResourceSQLNewUI.detail"),
    groupName: localizeUtils_1.getLocalizedString("core.options.separator.resource"),
};
exports.AzureResourceFunction = {
    id: "function",
    label: localizeUtils_1.getLocalizedString("core.AzureResourceFunction.label"),
};
exports.AzureResourceFunctionNewUI = {
    id: "function",
    label: `$(azure) ${localizeUtils_1.getLocalizedString("core.AzureResourceFunctionNewUI.label")}`,
    detail: localizeUtils_1.getLocalizedString("core.AzureResourceFunctionNewUI.detail"),
    groupName: localizeUtils_1.getLocalizedString("core.options.separator.resource"),
};
exports.AzureResourceApim = {
    id: "apim",
    label: localizeUtils_1.getLocalizedString("core.AzureResourceApim.label"),
    description: localizeUtils_1.getLocalizedString("core.AzureResourceApim.description"),
};
exports.AzureResourceApimNewUI = {
    id: "apim",
    label: `$(azure) ${localizeUtils_1.getLocalizedString("core.AzureResourceApimNewUI.label")}`,
    detail: localizeUtils_1.getLocalizedString("core.AzureResourceApimNewUI.detail"),
    groupName: localizeUtils_1.getLocalizedString("core.options.separator.resource"),
};
exports.AzureResourceKeyVault = {
    id: "keyvault",
    label: localizeUtils_1.getLocalizedString("core.AzureResourceKeyVault.label"),
    description: localizeUtils_1.getLocalizedString("core.AzureResourceKeyVault.description"),
};
exports.AzureResourceKeyVaultNewUI = {
    id: "keyvault",
    label: `$(azure) ${localizeUtils_1.getLocalizedString("core.AzureResourceKeyVaultNewUI.label")}`,
    detail: localizeUtils_1.getLocalizedString("core.AzureResourceKeyVaultNewUI.detail"),
    groupName: localizeUtils_1.getLocalizedString("core.options.separator.resource"),
};
exports.SingleSignOnOptionItem = {
    id: "sso",
    label: `$(unlock) ${localizeUtils_1.getLocalizedString("core.SingleSignOnOption.label")}`,
    detail: localizeUtils_1.getLocalizedString("core.SingleSignOnOption.detail"),
    groupName: localizeUtils_1.getLocalizedString("core.options.separator.additional"),
    data: "https://aka.ms/teamsfx-add-sso",
    buttons: [
        {
            iconPath: "tasklist",
            tooltip: localizeUtils_1.getLocalizedString("core.option.tutorial"),
            command: "fx-extension.openTutorial",
        },
    ],
};
exports.ApiConnectionOptionItem = {
    id: "api-connection",
    label: `$(arrow-swap) ${localizeUtils_1.getLocalizedString("core.ApiConnectionOption.label")}`,
    detail: localizeUtils_1.getLocalizedString("core.ApiConnectionOption.detail"),
    groupName: localizeUtils_1.getLocalizedString("core.options.separator.additional"),
    data: "https://aka.ms/teamsfx-connect-api",
    buttons: [
        {
            iconPath: "tasklist",
            tooltip: localizeUtils_1.getLocalizedString("core.option.tutorial"),
            command: "fx-extension.openTutorial",
        },
    ],
};
exports.CicdOptionItem = {
    id: "cicd",
    label: `$(sync) ${localizeUtils_1.getLocalizedString("core.cicdWorkflowOption.label")}`,
    detail: localizeUtils_1.getLocalizedString("core.cicdWorkflowOption.detail"),
    groupName: localizeUtils_1.getLocalizedString("core.options.separator.additional"),
    data: "https://aka.ms/teamsfx-add-cicd",
    buttons: [
        {
            iconPath: "tasklist",
            tooltip: localizeUtils_1.getLocalizedString("core.option.tutorial"),
            command: "fx-extension.openTutorial",
        },
    ],
};
var BotScenario;
(function (BotScenario) {
    BotScenario["NotificationBot"] = "notificationBot";
    BotScenario["CommandAndResponseBot"] = "commandAndResponseBot";
    BotScenario["WorkflowBot"] = "workflowBot";
})(BotScenario = exports.BotScenario || (exports.BotScenario = {}));
exports.BotNotificationTriggers = {
    Timer: "timer",
    Http: "http",
};
exports.AzureResourcesQuestion = {
    name: AzureSolutionQuestionNames.AzureResources,
    title: localizeUtils_1.getLocalizedString("core.question.AzureResourcesQuestion.title"),
    type: "multiSelect",
    staticOptions: [exports.AzureResourceSQL, exports.AzureResourceFunction],
    default: [],
    onDidChangeSelection: async function (currentSelectedIds, previousSelectedIds) {
        if (currentSelectedIds.has(exports.AzureResourceSQL.id)) {
            currentSelectedIds.add(exports.AzureResourceFunction.id);
        }
        return currentSelectedIds;
    },
    placeholder: localizeUtils_1.getLocalizedString("core.question.AzureResourcesQuestion.placeholder"),
};
const BotFeatureIds = () => [
    BotOptionItem().id,
    NotificationOptionItem().id,
    CommandAndResponseOptionItem().id,
    WorkflowOptionItem().id,
    MessageExtensionItem().id,
    M365SearchAppOptionItem().id,
];
exports.BotFeatureIds = BotFeatureIds;
const TabFeatureIds = () => [
    TabOptionItem().id,
    TabNonSsoItem().id,
    M365SsoLaunchPageOptionItem().id,
    DashboardOptionItem().id,
];
exports.TabFeatureIds = TabFeatureIds;
exports.AadConstants = {
    DefaultTemplateFileName: "aad.manifest.json",
};
//# sourceMappingURL=constants.js.map