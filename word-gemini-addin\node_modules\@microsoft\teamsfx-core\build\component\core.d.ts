import { Action<PERSON>ontext, <PERSON><PERSON>V<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, InputsWithProjectPath, ResourceContextV3, Result } from "@microsoft/teamsfx-api";
import "reflect-metadata";
import "./bicep";
import "./code/api/apiCode";
import "./code/botCode";
import "./code/spfxTabCode";
import "./code/tab/tabCode";
import "./connection/apimConfig";
import "./connection/azureFunctionConfig";
import "./connection/azureWebAppConfig";
import "./feature/api/api";
import "./feature/apiconnector/apiConnector";
import "./feature/apim";
import "./feature/bot/bot";
import "./feature/cicd/cicd";
import "./feature/keyVault";
import "./feature/spfx";
import "./feature/sql";
import "./feature/sso";
import "./feature/tab";
import "./resource/apim/apim";
import "./resource/azureAppService/azureFunction";
import "./resource/azureAppService/azureWebApp";
import "./resource/azureSql";
import "./resource/azureStorage/azureStorage";
import "./resource/botService/botService";
import "./resource/keyVault";
import "./resource/spfx";
import "./resource/aadApp/aadApp";
import "./resource/simpleAuth";
export declare class TeamsfxCore {
    name: string;
    /**
     * create project
     */
    create(context: ContextV3, inputs: InputsWithProjectPath, actionContext?: ActionContext): Promise<Result<string, FxError>>;
    /**
     * add feature
     */
    addFeature(context: ContextV3, inputs: InputsWithProjectPath, actionContext?: ActionContext): Promise<Result<any, FxError>>;
    init(context: ContextV3, inputs: InputsWithProjectPath, isInitExistingApp?: boolean): Promise<Result<undefined, FxError>>;
    provision(ctx: ResourceContextV3, inputs: InputsWithProjectPath, actionContext?: ActionContext): Promise<Result<undefined, FxError>>;
    /**
     * About AAD deploy:
     * 1. For VS platform, there is no "AAD" option in the deploy plugins selection question.
     *    "Deploy" command does not include "AAD" resource.
     * 2. For VS Code platform, there is no "AAD" option in the deploy plugins selection question.
     *    "Deploy" command does not include "AAD" resource. But there is another command "Deploy aad manifest" in aad manifest's context menu that will trigger the "deploy" lifecycle command in fxcore (with inputs["include-aad-manifest"] === "yes") will trigger "AAD" only deployment.
     * 3. For CLI platform, there is "AAD" option in the deploy plugins selection question.
     *    "Deploy" command includes "AAD" resource if the following conditions meet:
     *      1). inputs["include-aad-manifest"] === "yes" AND
     *      2). deploy options includes "AAD".
     *    In such a case "AAD" will be included in the deployment resources and will be deployed together with other resources.
     */
    deploy(context: ResourceContextV3, inputs: InputsWithProjectPath, actionContext?: ActionContext): Promise<Result<undefined, FxError>>;
}
export declare function preCheck(projectPath: string): Promise<Result<undefined, FxError>>;
export interface BotTroubleShootMessage {
    troubleShootLink: string;
    textForLogging: string;
    textForMsgBox: string;
    textForActionButton: string;
}
export declare function getBotTroubleShootMessage(isBot: boolean): BotTroubleShootMessage;
//# sourceMappingURL=core.d.ts.map