"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBotTroubleShootMessage = exports.preCheck = exports.TeamsfxCore = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const path_1 = tslib_1.__importDefault(require("path"));
require("reflect-metadata");
const typedi_1 = require("typedi");
const question_1 = require("../core/question");
const projectSettingsHelper_1 = require("./../common/projectSettingsHelper");
require("./bicep");
require("./code/api/apiCode");
require("./code/botCode");
require("./code/spfxTabCode");
require("./code/tab/tabCode");
require("./connection/apimConfig");
require("./connection/azureFunctionConfig");
require("./connection/azureWebAppConfig");
const debug_1 = require("./debug");
const envManager_1 = require("./envManager");
require("./feature/api/api");
require("./feature/apiconnector/apiConnector");
require("./feature/apim");
require("./feature/bot/bot");
require("./feature/cicd/cicd");
require("./feature/keyVault");
require("./feature/spfx");
require("./feature/sql");
require("./feature/sso");
require("./feature/tab");
require("./resource/apim/apim");
require("./resource/azureAppService/azureFunction");
require("./resource/azureAppService/azureWebApp");
require("./resource/azureSql");
require("./resource/azureStorage/azureStorage");
require("./resource/botService/botService");
require("./resource/keyVault");
require("./resource/spfx");
require("./resource/aadApp/aadApp");
require("./resource/simpleAuth");
const jsonschema = tslib_1.__importStar(require("jsonschema"));
const lodash_1 = require("lodash");
const constants_1 = require("../common/constants");
const globalState_1 = require("../common/globalState");
const localizeUtils_1 = require("../common/localizeUtils");
const projectSettingsHelperV3_1 = require("../common/projectSettingsHelperV3");
const tools_1 = require("../common/tools");
const downloadSample_1 = require("../core/downloadSample");
const error_1 = require("../core/error");
const globalVars_1 = require("../core/globalVars");
const arm_1 = tslib_1.__importDefault(require("./arm"));
const constants_2 = require("./constants");
const constants_3 = require("./constants");
const migrate_1 = require("./migrate");
const question_2 = require("./question");
const lib_1 = require("@feathersjs/hooks/lib");
const actionExecutionMW_1 = require("./middleware/actionExecutionMW");
const telemetry_1 = require("../common/telemetry");
const workflow_1 = require("./workflow");
const environment_1 = require("../core/environment");
const deployUtils_1 = require("./deployUtils");
const provisionUtils_1 = require("./provisionUtils");
const folder_1 = require("../folder");
const FxCore_1 = require("../core/FxCore");
const constants_4 = require("./constants");
const questionModel_1 = require("../core/middleware/questionModel");
const constants_5 = require("./resource/aadApp/constants");
const executor_1 = require("./utils/executor");
const projectSettingsLoader_1 = require("../core/middleware/projectSettingsLoader");
let TeamsfxCore = class TeamsfxCore {
    constructor() {
        this.name = "fx";
    }
    /**
     * create project
     */
    async create(context, inputs, actionContext) {
        const folder = inputs["folder"];
        if (!folder) {
            return teamsfx_api_1.err(error_1.InvalidInputError("folder is undefined"));
        }
        inputs.folder = folder;
        const scratch = inputs[question_1.CoreQuestionNames.CreateFromScratch];
        let projectPath;
        const automaticNpmInstall = "automaticNpmInstall";
        if (scratch === question_1.ScratchOptionNo().id) {
            // create from sample
            const downloadRes = await downloadSample_1.downloadSample(inputs, undefined, context);
            if (downloadRes.isErr()) {
                return teamsfx_api_1.err(downloadRes.error);
            }
            projectPath = downloadRes.value;
        }
        else {
            // create from new
            const appName = inputs[question_1.CoreQuestionNames.AppName];
            if (undefined === appName)
                return teamsfx_api_1.err(error_1.InvalidInputError(`App Name is empty`, inputs));
            const validateResult = jsonschema.validate(appName, {
                pattern: question_1.ProjectNamePattern,
            });
            if (validateResult.errors && validateResult.errors.length > 0) {
                return teamsfx_api_1.err(error_1.InvalidInputError(`${validateResult.errors[0].message}`, inputs));
            }
            projectPath = path_1.default.join(folder, appName);
            inputs.projectPath = projectPath;
            // set isVS global var when creating project
            globalVars_1.globalVars.isVS = inputs[question_1.CoreQuestionNames.ProgrammingLanguage] === "csharp";
            const features = inputs.capabilities;
            const isInitExistingApp = features === constants_2.ExistingTabOptionItem().id;
            if (isInitExistingApp) {
                const folderExist = await fs_extra_1.default.pathExists(projectPath);
                if (folderExist) {
                    return teamsfx_api_1.err(new error_1.ProjectFolderExistError(projectPath));
                }
            }
            else {
                const isValid = projectSettingsHelper_1.isValidProject(projectPath);
                if (isValid) {
                    return teamsfx_api_1.err(new error_1.OperationNotPermittedError("initialize a project in existing teamsfx project"));
                }
            }
            const initRes = await this.init(context, inputs, isInitExistingApp);
            if (initRes.isErr())
                return teamsfx_api_1.err(initRes.error);
            delete inputs.folder;
            if (isInitExistingApp) {
                lodash_1.merge(actionContext === null || actionContext === void 0 ? void 0 : actionContext.telemetryProps, {
                    [telemetry_1.TelemetryProperty.Capabilities]: features,
                });
                const appManifest = typedi_1.Container.get(constants_3.ComponentNames.AppManifest);
                const addCapRes = await appManifest.addCapability(inputs, [
                    { name: "staticTab", existingApp: true },
                ]);
                if (addCapRes.isErr())
                    return teamsfx_api_1.err(addCapRes.error);
                const sourceReadmePath = path_1.default.join(folder_1.getTemplatesFolder(), "core", teamsfx_api_1.DefaultReadme);
                if (await fs_extra_1.default.pathExists(sourceReadmePath)) {
                    const targetReadmePath = path_1.default.join(inputs.projectPath, teamsfx_api_1.DefaultReadme);
                    await fs_extra_1.default.copy(sourceReadmePath, targetReadmePath);
                }
            }
            else {
                if (features === constants_2.M365SsoLaunchPageOptionItem().id ||
                    features === constants_2.M365SearchAppOptionItem().id) {
                    context.projectSetting.isM365 = true;
                    inputs.isM365 = true;
                }
                if (constants_2.BotFeatureIds().includes(features)) {
                    inputs[constants_2.AzureSolutionQuestionNames.Features] = features;
                    const component = typedi_1.Container.get(constants_3.ComponentNames.TeamsBot);
                    const res = await component.add(context, inputs);
                    if (res.isErr())
                        return teamsfx_api_1.err(res.error);
                }
                if (constants_2.TabFeatureIds().includes(features)) {
                    inputs[constants_2.AzureSolutionQuestionNames.Features] = features;
                    const component = typedi_1.Container.get(constants_3.ComponentNames.TeamsTab);
                    const res = await component.add(context, inputs);
                    if (res.isErr())
                        return teamsfx_api_1.err(res.error);
                }
                if (features === constants_2.TabSPFxItem().id) {
                    inputs[constants_2.AzureSolutionQuestionNames.Features] = features;
                    const component = typedi_1.Container.get("spfx-tab");
                    const res = await component.add(context, inputs);
                    if (res.isErr())
                        return teamsfx_api_1.err(res.error);
                }
            }
            lodash_1.merge(actionContext === null || actionContext === void 0 ? void 0 : actionContext.telemetryProps, {
                [telemetry_1.TelemetryProperty.Feature]: features,
            });
        }
        if (inputs.platform === teamsfx_api_1.Platform.VSCode) {
            await globalState_1.globalStateUpdate(automaticNpmInstall, true);
        }
        context.projectPath = projectPath;
        return teamsfx_api_1.ok(projectPath);
    }
    /**
     * add feature
     */
    async addFeature(context, inputs, actionContext) {
        var _a, _b, _c, _d, _e, _f;
        const features = inputs[constants_2.AzureSolutionQuestionNames.Features];
        let component;
        if (constants_2.BotFeatureIds().includes(features)) {
            component = typedi_1.Container.get(constants_3.ComponentNames.TeamsBot);
        }
        else if (constants_2.TabFeatureIds().includes(features)) {
            component = typedi_1.Container.get(constants_3.ComponentNames.TeamsTab);
        }
        else if (features === constants_2.AzureResourceSQLNewUI.id) {
            component = typedi_1.Container.get("sql");
        }
        else if (features === constants_2.AzureResourceFunctionNewUI.id) {
            component = typedi_1.Container.get(constants_3.ComponentNames.TeamsApi);
        }
        else if (features === constants_2.AzureResourceApim.id) {
            component = typedi_1.Container.get(constants_3.ComponentNames.APIMFeature);
        }
        else if (features === constants_2.AzureResourceKeyVaultNewUI.id) {
            component = typedi_1.Container.get("key-vault-feature");
        }
        else if (features === constants_2.CicdOptionItem.id) {
            component = typedi_1.Container.get("cicd");
        }
        else if (features === constants_2.ApiConnectionOptionItem.id) {
            component = typedi_1.Container.get("api-connector");
        }
        else if (features === constants_2.SingleSignOnOptionItem.id) {
            component = typedi_1.Container.get("sso");
        }
        else if (features === constants_2.TabSPFxNewUIItem().id) {
            component = typedi_1.Container.get(constants_3.ComponentNames.SPFxTab);
        }
        if (component) {
            const res = await component.add(context, inputs);
            lodash_1.merge(actionContext === null || actionContext === void 0 ? void 0 : actionContext.telemetryProps, {
                [telemetry_1.TelemetryProperty.Feature]: features,
            });
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
            if (features !== constants_2.ApiConnectionOptionItem.id && features !== constants_2.CicdOptionItem.id) {
                if (((_c = (_b = (_a = context.envInfo) === null || _a === void 0 ? void 0 : _a.state) === null || _b === void 0 ? void 0 : _b.solution) === null || _c === void 0 ? void 0 : _c.provisionSucceeded) === true ||
                    ((_f = (_e = (_d = context.envInfo) === null || _d === void 0 ? void 0 : _d.state) === null || _e === void 0 ? void 0 : _e.solution) === null || _f === void 0 ? void 0 : _f.provisionSucceeded) === "true") {
                    context.envInfo.state.solution.provisionSucceeded = false;
                }
                await environment_1.environmentManager.resetProvisionState(inputs, context);
            }
            return teamsfx_api_1.ok(res.value);
        }
        return teamsfx_api_1.ok(undefined);
    }
    async init(context, inputs, isInitExistingApp = false) {
        const projectSettings = projectSettingsHelper_1.newProjectSettings();
        projectSettings.appName = inputs["app-name"];
        projectSettings.components = [];
        if (inputs.projectId) {
            projectSettings.projectId = inputs.projectId;
        }
        context.projectSetting = projectSettings;
        await fs_extra_1.default.ensureDir(inputs.projectPath);
        await fs_extra_1.default.ensureDir(path_1.default.join(inputs.projectPath, `.${teamsfx_api_1.ConfigFolderName}`));
        await fs_extra_1.default.ensureDir(path_1.default.join(inputs.projectPath, `.${teamsfx_api_1.ConfigFolderName}`, "configs"));
        const basicFolderRes = await FxCore_1.ensureBasicFolderStructure(inputs);
        if (basicFolderRes.isErr()) {
            return teamsfx_api_1.err(basicFolderRes.error);
        }
        if (isInitExistingApp) {
            // const folderExist = await fs.pathExists(inputs.projectPath);
            // if (folderExist) {
            //   return err(new ProjectFolderExistError(inputs.projectPath));
            // }
            // const isValid = isValidProject(inputs.projectPath);
            // if (isValid) {
            //   return err(
            //     new OperationNotPermittedError("initialize a project in existing teamsfx project")
            //   );
            // }
            // pre-check before initialize
            const preCheckResult = await preCheck(inputs.projectPath);
            if (preCheckResult.isErr()) {
                return teamsfx_api_1.err(preCheckResult.error);
            }
        }
        {
            const appManifest = typedi_1.Container.get(constants_3.ComponentNames.AppManifest);
            const res = await appManifest.init(context, inputs, isInitExistingApp);
            if (res.isErr())
                return res;
        }
        {
            const endpoint = inputs[question_1.CoreQuestionNames.ExistingTabEndpoint];
            const createEnvResult = await envManager_1.createEnvWithName(environment_1.environmentManager.getDefaultEnvName(), projectSettings.appName, inputs, isInitExistingApp ? endpoint : undefined);
            if (createEnvResult.isErr()) {
                return teamsfx_api_1.err(createEnvResult.error);
            }
            const createLocalEnvResult = await envManager_1.createEnvWithName(environment_1.environmentManager.getLocalEnvName(), projectSettings.appName, inputs, isInitExistingApp ? endpoint : undefined);
            if (createLocalEnvResult.isErr()) {
                return teamsfx_api_1.err(createLocalEnvResult.error);
            }
        }
        return teamsfx_api_1.ok(undefined);
    }
    async provision(ctx, inputs, actionContext) {
        ctx.envInfo.state.solution = ctx.envInfo.state.solution || {};
        ctx.envInfo.state.solution.provisionSucceeded = false;
        // 1. pre provision
        {
            const res = await provisionUtils_1.provisionUtils.preProvision(ctx, inputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
        }
        // 2. create a teams app
        const appManifest = typedi_1.Container.get(constants_3.ComponentNames.AppManifest);
        {
            const res = await appManifest.provision(ctx, inputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
        }
        // 3. call resources provision api
        const componentsToProvision = ctx.projectSetting.components.filter((r) => r.provision);
        {
            const thunks = [];
            for (const componentConfig of componentsToProvision) {
                const componentInstance = typedi_1.Container.get(componentConfig.name);
                if (componentInstance.provision) {
                    thunks.push({
                        pluginName: `${componentConfig.name}`,
                        taskName: "provision",
                        thunk: () => {
                            ctx.envInfo.state[componentConfig.name] =
                                ctx.envInfo.state[componentConfig.name] || {};
                            return componentInstance.provision(ctx, inputs);
                        },
                    });
                }
            }
            const provisionResult = await executor_1.executeConcurrently(thunks, ctx.logProvider);
            if (provisionResult.kind !== "success") {
                return teamsfx_api_1.err(provisionResult.error);
            }
            ctx.logProvider.info(localizeUtils_1.getLocalizedString("core.provision.ProvisionFinishNotice", constants_1.PluginDisplayName.Solution));
        }
        // 4
        if (ctx.envInfo.envName === "local") {
            //4.1 setup local env
            const localEnvSetupResult = await debug_1.setupLocalEnvironment(ctx, inputs);
            if (localEnvSetupResult.isErr()) {
                return teamsfx_api_1.err(localEnvSetupResult.error);
            }
        }
        else if (projectSettingsHelperV3_1.hasAzureResourceV3(ctx.projectSetting, true)) {
            //4.2 deploy arm templates for remote
            ctx.logProvider.info(localizeUtils_1.getLocalizedString("core.deployArmTemplates.StartNotice", constants_1.PluginDisplayName.Solution));
            const armRes = await arm_1.default.deployArmTemplates(ctx, inputs, ctx.envInfo, ctx.tokenProvider.azureAccountProvider);
            if (armRes.isErr()) {
                return teamsfx_api_1.err(armRes.error);
            }
        }
        // 5.0 "aad-app.setApplicationInContext"
        const aadApp = typedi_1.Container.get(constants_3.ComponentNames.AadApp);
        if (projectSettingsHelperV3_1.hasAAD(ctx.projectSetting)) {
            const res = await aadApp.setApplicationInContext(ctx, inputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
        }
        // 5. call resources configure api
        {
            const thunks = [];
            for (const componentConfig of componentsToProvision) {
                const componentInstance = typedi_1.Container.get(componentConfig.name);
                if (componentInstance.configure) {
                    thunks.push({
                        pluginName: `${componentConfig.name}`,
                        taskName: "configure",
                        thunk: () => {
                            ctx.envInfo.state[componentConfig.name] =
                                ctx.envInfo.state[componentConfig.name] || {};
                            return componentInstance.configure(ctx, inputs);
                        },
                    });
                }
            }
            const configResult = await executor_1.executeConcurrently(thunks, ctx.logProvider);
            if (configResult.kind !== "success") {
                return teamsfx_api_1.err(configResult.error);
            }
            ctx.logProvider.info(localizeUtils_1.getLocalizedString("core.provision.configurationFinishNotice", constants_1.PluginDisplayName.Solution));
        }
        // 6.
        if (ctx.envInfo.envName === "local") {
            const localConfigResult = await debug_1.configLocalEnvironment(ctx, inputs);
            if (localConfigResult.isErr()) {
                return teamsfx_api_1.err(localConfigResult.error);
            }
        }
        // 7. update teams app
        {
            const res = await appManifest.configure(ctx, inputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
        }
        // 8. show message and set state
        if (ctx.envInfo.envName !== "local") {
            const url = tools_1.getResourceGroupInPortal(ctx.envInfo.state.solution.subscriptionId, ctx.envInfo.state.solution.tenantId, ctx.envInfo.state.solution.resourceGroupName);
            const msg = localizeUtils_1.getLocalizedString("core.provision.successNotice", ctx.projectSetting.appName);
            if (url) {
                const title = localizeUtils_1.getLocalizedString("core.provision.viewResources");
                ctx.userInteraction.showMessage("info", msg, false, title).then((result) => {
                    const userSelected = result.isOk() ? result.value : undefined;
                    if (userSelected === title) {
                        ctx.userInteraction.openUrl(url);
                    }
                });
            }
            else {
                ctx.userInteraction.showMessage("info", msg, false);
            }
            ctx.logProvider.info(msg);
        }
        lodash_1.merge(actionContext === null || actionContext === void 0 ? void 0 : actionContext.telemetryProps, {
            [telemetry_1.TelemetryProperty.Components]: JSON.stringify(componentsToProvision.map((component) => component.name)),
        });
        ctx.envInfo.state.solution.provisionSucceeded = true;
        return teamsfx_api_1.ok(undefined);
    }
    /**
     * About AAD deploy:
     * 1. For VS platform, there is no "AAD" option in the deploy plugins selection question.
     *    "Deploy" command does not include "AAD" resource.
     * 2. For VS Code platform, there is no "AAD" option in the deploy plugins selection question.
     *    "Deploy" command does not include "AAD" resource. But there is another command "Deploy aad manifest" in aad manifest's context menu that will trigger the "deploy" lifecycle command in fxcore (with inputs["include-aad-manifest"] === "yes") will trigger "AAD" only deployment.
     * 3. For CLI platform, there is "AAD" option in the deploy plugins selection question.
     *    "Deploy" command includes "AAD" resource if the following conditions meet:
     *      1). inputs["include-aad-manifest"] === "yes" AND
     *      2). deploy options includes "AAD".
     *    In such a case "AAD" will be included in the deployment resources and will be deployed together with other resources.
     */
    async deploy(context, inputs, actionContext) {
        var _a;
        lodash_1.merge(actionContext === null || actionContext === void 0 ? void 0 : actionContext.telemetryProps, {
            [constants_4.SolutionTelemetryProperty.IncludeAadManifest]: (_a = inputs[constants_5.Constants.INCLUDE_AAD_MANIFEST]) !== null && _a !== void 0 ? _a : "no",
        });
        // deploy AAD only from VS Code
        const isDeployAADManifestFromVSCode = inputs[constants_5.Constants.INCLUDE_AAD_MANIFEST] === "yes" && inputs.platform === teamsfx_api_1.Platform.VSCode;
        if (isDeployAADManifestFromVSCode) {
            return deployUtils_1.deployUtils.deployAadFromVscode(context, inputs);
        }
        context.logProvider.info(`inputs(${constants_2.AzureSolutionQuestionNames.PluginSelectionDeploy}) = ${inputs[constants_2.AzureSolutionQuestionNames.PluginSelectionDeploy]}`);
        const projectSettings = context.projectSetting;
        const inputPlugins = inputs[constants_2.AzureSolutionQuestionNames.PluginSelectionDeploy] || [];
        let inputComponentNames = inputPlugins.map(migrate_1.pluginName2ComponentName);
        if (inputComponentNames.includes(constants_3.ComponentNames.AadApp)) {
            if (inputs[constants_5.Constants.INCLUDE_AAD_MANIFEST] != "yes" || inputs.platform !== teamsfx_api_1.Platform.CLI) {
                inputComponentNames = inputComponentNames.filter((c) => c !== constants_3.ComponentNames.AadApp);
            }
        }
        const thunks = [];
        let hasAzureResource = false;
        // 1. collect resources to deploy
        const isVS = projectSettingsHelper_1.isVSProject(projectSettings);
        for (const component of projectSettings.components) {
            if (component.deploy && (isVS || inputComponentNames.includes(component.name))) {
                const deployComponentName = component.hosting || component.name;
                const featureComponent = typedi_1.Container.get(component.name);
                const deployComponent = typedi_1.Container.get(deployComponentName);
                thunks.push({
                    pluginName: `${component.name}`,
                    taskName: `${featureComponent.build ? "build & " : ""}deploy`,
                    thunk: async () => {
                        const clonedInputs = lodash_1.cloneDeep(inputs);
                        clonedInputs.folder = component.folder;
                        clonedInputs.artifactFolder = component.artifactFolder || clonedInputs.folder;
                        clonedInputs.componentId = component.name;
                        if (featureComponent.build) {
                            const buildRes = await featureComponent.build(context, clonedInputs);
                            if (buildRes.isErr())
                                return teamsfx_api_1.err(buildRes.error);
                        }
                        // build process may change the artifact folder, so we need reassign the value
                        clonedInputs.artifactFolder = component.artifactFolder;
                        return await deployComponent.deploy(context, clonedInputs);
                    },
                });
                if (constants_3.AzureResources.includes(deployComponentName)) {
                    hasAzureResource = true;
                }
            }
        }
        if (!(inputs["include-app-manifest"] && inputs["include-app-manifest"] === "no") &&
            inputComponentNames.includes(constants_3.ComponentNames.AppManifest)) {
            const appManifest = typedi_1.Container.get(constants_3.ComponentNames.AppManifest);
            thunks.push({
                pluginName: constants_3.ComponentNames.AppManifest,
                taskName: "deploy",
                thunk: async () => {
                    return await appManifest.configure(context, inputs);
                },
            });
        }
        if (thunks.length === 0) {
            return teamsfx_api_1.err(new teamsfx_api_1.UserError("fx", "NoResourcePluginSelected", localizeUtils_1.getDefaultString("core.NoPluginSelected"), localizeUtils_1.getLocalizedString("core.NoPluginSelected")));
        }
        context.logProvider.info(localizeUtils_1.getLocalizedString("core.deploy.selectedPluginsToDeployNotice", constants_1.PluginDisplayName.Solution, JSON.stringify(thunks.map((p) => p.pluginName))));
        // 2. check azure account
        if (hasAzureResource) {
            const subscriptionResult = await deployUtils_1.deployUtils.checkDeployAzureSubscription(context, context.envInfo, context.tokenProvider.azureAccountProvider);
            if (subscriptionResult.isErr()) {
                return teamsfx_api_1.err(subscriptionResult.error);
            }
            const consent = await deployUtils_1.deployUtils.askForDeployConsent(context, context.tokenProvider.azureAccountProvider, context.envInfo);
            if (consent.isErr()) {
                return teamsfx_api_1.err(consent.error);
            }
        }
        // // 3. build
        // {
        //   const res = await this.build(context, inputs);
        //   if (res.isErr()) return err(res.error);
        // }
        // 4. start deploy
        context.logProvider.info(localizeUtils_1.getLocalizedString("core.deploy.startNotice", constants_1.PluginDisplayName.Solution));
        const result = await executor_1.executeConcurrently(thunks, context.logProvider);
        if (result.kind === "success") {
            if (hasAzureResource) {
                const botTroubleShootMsg = getBotTroubleShootMessage(projectSettingsHelperV3_1.hasBot(context.projectSetting));
                const msg = localizeUtils_1.getLocalizedString("core.deploy.successNotice", context.projectSetting.appName) +
                    botTroubleShootMsg.textForLogging;
                context.logProvider.info(msg);
                if (botTroubleShootMsg.textForLogging) {
                    // Show a `Learn more` action button for bot trouble shooting.
                    context.userInteraction
                        .showMessage("info", `${localizeUtils_1.getLocalizedString("core.deploy.successNotice", context.projectSetting.appName)} ${botTroubleShootMsg.textForMsgBox}`, false, botTroubleShootMsg.textForActionButton)
                        .then((result) => {
                        const userSelected = result.isOk() ? result.value : undefined;
                        if (userSelected === botTroubleShootMsg.textForActionButton) {
                            context.userInteraction.openUrl(botTroubleShootMsg.troubleShootLink);
                        }
                    });
                }
                else {
                    context.userInteraction.showMessage("info", msg, false);
                }
            }
            lodash_1.merge(actionContext === null || actionContext === void 0 ? void 0 : actionContext.telemetryProps, {
                [telemetry_1.TelemetryProperty.Components]: JSON.stringify(thunks.map((p) => p.pluginName)),
                [telemetry_1.TelemetryProperty.Hosting]: JSON.stringify(thunks.map((p) => { var _a; return (_a = workflow_1.getComponent(projectSettings, p.pluginName)) === null || _a === void 0 ? void 0 : _a.hosting; })),
            });
            return teamsfx_api_1.ok(undefined);
        }
        else {
            const msg = localizeUtils_1.getLocalizedString("core.deploy.failNotice", context.projectSetting.appName);
            context.logProvider.info(msg);
            return teamsfx_api_1.err(result.error);
        }
    }
};
tslib_1.__decorate([
    lib_1.hooks([
        actionExecutionMW_1.ActionExecutionMW({
            question: (context, inputs) => {
                return questionModel_1.getQuestionsForCreateProjectV2(inputs);
            },
            enableTelemetry: true,
            telemetryEventName: telemetry_1.TelemetryEvent.CreateProject,
            telemetryComponentName: "core",
        }),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TeamsfxCore.prototype, "create", null);
tslib_1.__decorate([
    lib_1.hooks([
        actionExecutionMW_1.ActionExecutionMW({
            question: (context, inputs) => {
                return question_2.getQuestionsForAddFeatureV3(context, inputs);
            },
            enableTelemetry: true,
            telemetryEventName: telemetry_1.TelemetryEvent.AddFeature,
            telemetryComponentName: "core",
        }),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TeamsfxCore.prototype, "addFeature", null);
tslib_1.__decorate([
    lib_1.hooks([
        actionExecutionMW_1.ActionExecutionMW({
            question: async (context, inputs) => {
                return await question_2.getQuestionsForProvisionV3(inputs);
            },
            enableTelemetry: true,
            telemetryEventName: telemetry_1.TelemetryEvent.Provision,
            telemetryComponentName: "core",
        }),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TeamsfxCore.prototype, "provision", null);
tslib_1.__decorate([
    lib_1.hooks([
        actionExecutionMW_1.ActionExecutionMW({
            question: async (context, inputs) => {
                return await question_2.getQuestionsForDeployV3(context, inputs, context.envInfo);
            },
            enableTelemetry: true,
            telemetryEventName: telemetry_1.TelemetryEvent.Deploy,
            telemetryComponentName: "core",
        }),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], TeamsfxCore.prototype, "deploy", null);
TeamsfxCore = tslib_1.__decorate([
    typedi_1.Service("fx")
], TeamsfxCore);
exports.TeamsfxCore = TeamsfxCore;
// pre-check before initialize
async function preCheck(projectPath) {
    const existFiles = new Array();
    // 0. check if projectSettings.json exists
    const settingsFile = projectSettingsLoader_1.getProjectSettingsPath(projectPath);
    if (await fs_extra_1.default.pathExists(settingsFile)) {
        existFiles.push(settingsFile);
    }
    const appManifest = typedi_1.Container.get(constants_3.ComponentNames.AppManifest);
    // 1. check if manifest templates exist
    const manifestPreCheckResult = await appManifest.preCheck(projectPath);
    existFiles.push(...manifestPreCheckResult);
    // 2. check if env config file exists
    const defaultEnvPath = environment_1.environmentManager.getEnvConfigPath(environment_1.environmentManager.getDefaultEnvName(), projectPath);
    if (await fs_extra_1.default.pathExists(defaultEnvPath)) {
        existFiles.push(defaultEnvPath);
    }
    const localEnvPath = environment_1.environmentManager.getEnvConfigPath(environment_1.environmentManager.getLocalEnvName(), projectPath);
    if (await fs_extra_1.default.pathExists(localEnvPath)) {
        existFiles.push(localEnvPath);
    }
    // 3. check if README.md exists
    const readmePath = path_1.default.join(projectPath, teamsfx_api_1.AutoGeneratedReadme);
    if (await fs_extra_1.default.pathExists(readmePath)) {
        existFiles.push(readmePath);
    }
    if (existFiles.length > 0) {
        return teamsfx_api_1.err(new error_1.InitializedFileAlreadyExistError(existFiles.join(", ")));
    }
    return teamsfx_api_1.ok(undefined);
}
exports.preCheck = preCheck;
function getBotTroubleShootMessage(isBot) {
    const botTroubleShootLink = "https://aka.ms/teamsfx-bot-help#how-can-i-troubleshoot-issues-when-teams-bot-isnt-responding-on-azure";
    const botTroubleShootDesc = localizeUtils_1.getLocalizedString("core.deploy.botTroubleShoot");
    const botTroubleShootLearnMore = localizeUtils_1.getLocalizedString("core.deploy.botTroubleShoot.learnMore");
    const botTroubleShootMsg = `${botTroubleShootDesc} ${botTroubleShootLearnMore}: ${botTroubleShootLink}.`;
    return {
        troubleShootLink: botTroubleShootLink,
        textForLogging: isBot ? botTroubleShootMsg : "",
        textForMsgBox: botTroubleShootDesc,
        textForActionButton: botTroubleShootLearnMore,
    };
}
exports.getBotTroubleShootMessage = getBotTroubleShootMessage;
//# sourceMappingURL=core.js.map