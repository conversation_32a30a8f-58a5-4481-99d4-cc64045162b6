import { AzureAccountProvider, FxError, InputsWithProjectPath, ResourceContextV3, Result, v2, v3, Void } from "@microsoft/teamsfx-api";
import { DriverContext } from "./driver/interface/commonArgs";
export declare class DeployUtils {
    /**
     * make sure subscription is correct before deployment
     *
     */
    checkDeployAzureSubscription(ctx: v2.Context, envInfo: v3.EnvInfoV3, azureAccountProvider: AzureAccountProvider): Promise<Result<Void, FxError>>;
    deployAadFromVscode(context: ResourceContextV3, inputs: InputsWithProjectPath): Promise<Result<undefined, FxError>>;
    askForDeployConsent(ctx: v2.Context, azureAccountProvider: AzureAccountProvider, envInfo: v3.EnvInfoV3): Promise<Result<Void, FxError>>;
    askForDeployConsentV3(ctx: DriverContext): Promise<Result<Void, FxError>>;
}
export declare const deployUtils: DeployUtils;
//# sourceMappingURL=deployUtils.d.ts.map