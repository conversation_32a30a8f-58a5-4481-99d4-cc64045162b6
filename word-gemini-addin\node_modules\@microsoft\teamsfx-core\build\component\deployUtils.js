"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.deployUtils = exports.DeployUtils = void 0;
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const lodash_1 = require("lodash");
const typedi_1 = require("typedi");
const constants_1 = require("../common/constants");
const localizeUtils_1 = require("../common/localizeUtils");
const constants_2 = require("./constants");
const constants_3 = require("./constants");
const utils_1 = require("./utils");
const executor_1 = require("./utils/executor");
class DeployUtils {
    /**
     * make sure subscription is correct before deployment
     *
     */
    async checkDeployAzureSubscription(ctx, envInfo, azureAccountProvider) {
        var _a;
        const subscriptionIdInConfig = ((_a = envInfo.config.azure) === null || _a === void 0 ? void 0 : _a.subscriptionId) || envInfo.state.solution.subscriptionId;
        const subscriptionInAccount = await azureAccountProvider.getSelectedSubscription(true);
        if (!subscriptionIdInConfig) {
            if (subscriptionInAccount) {
                envInfo.state.solution.subscriptionId = subscriptionInAccount.subscriptionId;
                envInfo.state.solution.subscriptionName = subscriptionInAccount.subscriptionName;
                envInfo.state.solution.tenantId = subscriptionInAccount.tenantId;
                ctx.logProvider.info(`[${constants_1.PluginDisplayName.Solution}] checkAzureSubscription pass!`);
                return teamsfx_api_1.ok(teamsfx_api_1.Void);
            }
            else {
                return teamsfx_api_1.err(new teamsfx_api_1.UserError(constants_2.SolutionSource, constants_2.SolutionError.SubscriptionNotFound, "Failed to select subscription"));
            }
        }
        // make sure the user is logged in
        await azureAccountProvider.getIdentityCredentialAsync(true);
        // verify valid subscription (permission)
        const subscriptions = await azureAccountProvider.listSubscriptions();
        const targetSubInfo = subscriptions.find((item) => item.subscriptionId === subscriptionIdInConfig);
        if (!targetSubInfo) {
            return teamsfx_api_1.err(new teamsfx_api_1.UserError(constants_2.SolutionSource, constants_2.SolutionError.SubscriptionNotFound, `The subscription '${subscriptionIdInConfig}'(${envInfo.state.solution.subscriptionName}) for '${envInfo.envName}' environment is not found in the current account, please use the right Azure account or check the '${teamsfx_api_1.EnvConfigFileNameTemplate.replace(teamsfx_api_1.EnvNamePlaceholder, envInfo.envName)}' file.`));
        }
        envInfo.state.solution.subscriptionId = targetSubInfo.subscriptionId;
        envInfo.state.solution.subscriptionName = targetSubInfo.subscriptionName;
        envInfo.state.solution.tenantId = targetSubInfo.tenantId;
        ctx.logProvider.info(`[${constants_1.PluginDisplayName.Solution}] checkAzureSubscription pass!`);
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    async deployAadFromVscode(context, inputs) {
        const thunks = [];
        // 1. collect resources to deploy
        const deployComponent = typedi_1.Container.get(constants_3.ComponentNames.AadApp);
        thunks.push({
            pluginName: `${deployComponent.name}`,
            taskName: `deploy`,
            thunk: async () => {
                const clonedInputs = lodash_1.cloneDeep(inputs);
                clonedInputs.componentId = deployComponent.name;
                return await deployComponent.deploy(context, clonedInputs);
            },
        });
        if (thunks.length === 0) {
            return teamsfx_api_1.err(new teamsfx_api_1.UserError("fx", "NoResourcePluginSelected", localizeUtils_1.getDefaultString("core.NoPluginSelected"), localizeUtils_1.getLocalizedString("core.NoPluginSelected")));
        }
        context.logProvider.info(localizeUtils_1.getLocalizedString("core.deploy.selectedPluginsToDeployNotice", constants_1.PluginDisplayName.Solution, JSON.stringify(thunks.map((p) => p.pluginName))));
        // 2. start deploy
        context.logProvider.info(localizeUtils_1.getLocalizedString("core.deploy.startNotice", constants_1.PluginDisplayName.Solution));
        const result = await executor_1.executeConcurrently(thunks, context.logProvider);
        if (result.kind === "success") {
            const msg = localizeUtils_1.getLocalizedString("core.deploy.aadManifestSuccessNotice");
            context.logProvider.info(msg);
            context.userInteraction
                .showMessage("info", msg, false, localizeUtils_1.getLocalizedString("core.deploy.aadManifestLearnMore"))
                .then((result) => {
                var _a;
                const userSelected = result.isOk() ? result.value : undefined;
                if (userSelected === localizeUtils_1.getLocalizedString("core.deploy.aadManifestLearnMore")) {
                    (_a = context.userInteraction) === null || _a === void 0 ? void 0 : _a.openUrl(constants_2.ViewAadAppHelpLink);
                }
            });
            return teamsfx_api_1.ok(undefined);
        }
        else {
            const msg = localizeUtils_1.getLocalizedString("core.deploy.failNotice", context.projectSetting.appName);
            context.logProvider.info(msg);
            return teamsfx_api_1.err(utils_1.sendErrorTelemetryThenReturnError(constants_2.SolutionTelemetryEvent.Deploy, result.error, context.telemetryReporter));
        }
    }
    async askForDeployConsent(ctx, azureAccountProvider, envInfo) {
        var _a, _b;
        const azureTokenJson = await azureAccountProvider.getJsonObject();
        // Only Azure project requires this confirm dialog
        const username = azureTokenJson.unique_name || "";
        const subscriptionId = ((_a = envInfo.state.solution) === null || _a === void 0 ? void 0 : _a.subscriptionId) || "";
        const subscriptionName = ((_b = envInfo.state.solution) === null || _b === void 0 ? void 0 : _b.subscriptionName) || "";
        const msg = localizeUtils_1.getLocalizedString("core.deploy.confirmEnvNotice", envInfo.envName, username, subscriptionName ? subscriptionName : subscriptionId);
        const deployOption = localizeUtils_1.getLocalizedString("core.option.deploy");
        const result = await ctx.userInteraction.showMessage("warn", msg, true, deployOption);
        const choice = (result === null || result === void 0 ? void 0 : result.isOk()) ? result.value : undefined;
        if (choice === deployOption) {
            return teamsfx_api_1.ok(teamsfx_api_1.Void);
        }
        return teamsfx_api_1.err(new teamsfx_api_1.UserError(constants_2.SolutionSource, "UserCancel", "UserCancel"));
    }
    async askForDeployConsentV3(ctx) {
        var _a;
        const msg = localizeUtils_1.getLocalizedString("core.deploy.confirmEnvNoticeV3", process.env.TEAMSFX_ENV);
        const deployOption = localizeUtils_1.getLocalizedString("core.option.deploy");
        const result = await ((_a = ctx.ui) === null || _a === void 0 ? void 0 : _a.showMessage("warn", msg, true, deployOption));
        const choice = (result === null || result === void 0 ? void 0 : result.isOk()) ? result.value : undefined;
        if (choice === deployOption) {
            return teamsfx_api_1.ok(teamsfx_api_1.Void);
        }
        return teamsfx_api_1.err(new teamsfx_api_1.UserError(constants_2.SolutionSource, "UserCancel", "UserCancel"));
    }
}
exports.DeployUtils = DeployUtils;
exports.deployUtils = new DeployUtils();
//# sourceMappingURL=deployUtils.js.map