/**
 * <AUTHOR> <<EMAIL>>
 */
import { AppDefinition } from "./resource/appManifest/interfaces/appDefinition";
import { Result, FxError, ContextV3, Inputs } from "@microsoft/teamsfx-api";
export declare const answerToRepaceBotId = "bot";
export declare const answerToReplaceMessageExtensionBotId = "messageExtension";
export declare class DeveloperPortalScaffoldUtils {
    updateFilesForTdp(ctx: ContextV3, appDefinition: AppDefinition, inputs: Inputs): Promise<Result<undefined, FxError>>;
}
export declare enum TabUrlType {
    WebsiteUrl = "WebsiteUrl",
    ContentUrl = "ContentUrl"
}
export declare function getTemplateId(teamsApp: AppDefinition): string | undefined;
export declare function updateScope(scopes: string[]): string[];
export declare function isFromDevPortalInVSC(inputs: Inputs): boolean;
export declare const developerPortalScaffoldUtils: DeveloperPortalScaffoldUtils;
//# sourceMappingURL=developerPortalScaffoldUtils.d.ts.map