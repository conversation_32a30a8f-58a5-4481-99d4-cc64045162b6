import { CoreCallbackEvent, CoreCallbackFunc, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FxError, Inputs, InputsWithProjectPath, ProjectConfig, ProjectConfigV3, QTreeNode, Result, Settings, Stage, Tools, v2, v3, Void } from "@microsoft/teamsfx-api";
import { CoreHookContext, PreProvisionResForVS, VersionCheckRes } from "./types";
import { FeatureId } from "../component/question";
import "../component/driver/index";
import { DriverContext } from "../component/driver/interface/commonArgs";
import { ILifecycle } from "../component/configManager/interface";
import { DotenvParseOutput } from "dotenv";
import { FxCoreV3Implement } from "./FxCoreImplementV3";
export declare class FxCore implements v3.ICore {
    tools: Tools;
    isFromSample?: boolean;
    settingsVersion?: string;
    v3Implement: FxCoreV3Implement;
    constructor(tools: Tools);
    /**
     * @todo this's a really primitive implement. Maybe could use Subscription Model to
     * refactor later.
     */
    on(event: CoreCallbackEvent, callback: CoreCallbackFunc): void;
    createExistingTabApp(inputs: Inputs, folder: string, ctx?: CoreHookContext): Promise<Result<string, FxError>>;
    createProject(inputs: Inputs): Promise<Result<string, FxError>>;
    /**
     * "teamsfx init infra" CLI command
     */
    initInfra(inputs: Inputs): Promise<Result<undefined, FxError>>;
    /**
     * "teamsfx init debug" CLI command
     */
    initDebug(inputs: Inputs): Promise<Result<undefined, FxError>>;
    createProjectOld(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<string, FxError>>;
    provisionResources(inputs: Inputs): Promise<Result<Void, FxError>>;
    provisionResourcesOld(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    /**
     * Only used to provision Teams app with user provided app package
     * @param inputs
     * @returns teamsAppId on provision success
     */
    provisionTeamsAppForCLI(inputs: Inputs): Promise<Result<string, FxError>>;
    deployArtifacts(inputs: Inputs): Promise<Result<Void, FxError>>;
    deployArtifactsOld(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    localDebug(inputs: Inputs): Promise<Result<Void, FxError>>;
    deployAadManifest(inputs: Inputs): Promise<Result<Void, FxError>>;
    publishApplication(inputs: Inputs): Promise<Result<Void, FxError>>;
    publishApplicationOld(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    addFeature(inputs: v2.InputsWithProjectPath, ctx?: CoreHookContext): Promise<Result<any, FxError>>;
    executeUserTask(func: Func, inputs: Inputs, ctx?: CoreHookContext): Promise<Result<any, FxError>>;
    executeUserTaskOld(func: Func, inputs: Inputs, ctx?: CoreHookContext): Promise<Result<any, FxError>>;
    deployTeamsManifest(inputs: Inputs): Promise<Result<Void, FxError>>;
    /**
     * Warning: this API only works for CLI_HELP, it has no business with interactive run for CLI!
     */
    getQuestions(stage: Stage, inputs: Inputs): Promise<Result<QTreeNode | undefined, FxError>>;
    getQuestionsForAddFeature(featureId: FeatureId, inputs: Inputs): Promise<Result<QTreeNode | undefined, FxError>>;
    getQuestionsForUserTask(func: FunctionRouter, inputs: Inputs): Promise<Result<QTreeNode | undefined, FxError>>;
    getSettings(inputs: InputsWithProjectPath): Promise<Result<Settings, FxError>>;
    getDotEnv(inputs: InputsWithProjectPath): Promise<Result<DotenvParseOutput | undefined, FxError>>;
    getProjectConfig(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<ProjectConfig | undefined, FxError>>;
    getProjectConfigV3(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<ProjectConfigV3 | undefined, FxError>>;
    grantPermission(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<any, FxError>>;
    checkPermission(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<any, FxError>>;
    listCollaborator(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<any, FxError>>;
    getSelectedEnv(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<string | undefined, FxError>>;
    encrypt(plaintext: string, inputs: Inputs, ctx?: CoreHookContext): Promise<Result<string, FxError>>;
    decrypt(ciphertext: string, inputs: Inputs, ctx?: CoreHookContext): Promise<Result<string, FxError>>;
    buildArtifacts(inputs: Inputs): Promise<Result<Void, FxError>>;
    createEnv(inputs: Inputs): Promise<Result<Void, FxError>>;
    createEnvOld(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    phantomMigrationV3(inputs: Inputs): Promise<Result<Void, FxError>>;
    projectVersionCheck(inputs: Inputs): Promise<Result<VersionCheckRes, FxError>>;
    createEnvCopy(targetEnvName: string, sourceEnvName: string, inputs: Inputs, core: FxCore): Promise<Result<Void, FxError>>;
    activateEnv(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    apply(inputs: Inputs, templatePath: string, lifecycleName: string): Promise<Result<Void, FxError>>;
    runLifecycle(lifecycle: ILifecycle, driverContext: DriverContext, env: string): Promise<Result<Void, FxError>>;
    _init(inputs: Inputs, ctx?: CoreHookContext, isInitExistingApp?: boolean): Promise<Result<string, FxError>>;
    init(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<string, FxError>>;
    preProvisionForVS(inputs: Inputs): Promise<Result<PreProvisionResForVS, FxError>>;
    publishInDeveloperPortal(inputs: Inputs): Promise<Result<Void, FxError>>;
}
export declare function ensureBasicFolderStructure(inputs: Inputs, createPackageJson?: boolean): Promise<Result<null, FxError>>;
//# sourceMappingURL=FxCore.d.ts.map