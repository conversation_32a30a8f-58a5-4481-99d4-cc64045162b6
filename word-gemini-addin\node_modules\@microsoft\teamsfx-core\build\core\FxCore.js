"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.ensureBasicFolderStructure = exports.FxCore = void 0;
const tslib_1 = require("tslib");
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const jsonschema = tslib_1.__importStar(require("jsonschema"));
const path = tslib_1.__importStar(require("path"));
const typedi_1 = require("typedi");
const uuid = tslib_1.__importStar(require("uuid"));
const hooks_1 = require("@feathersjs/hooks");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const localizeUtils_1 = require("../common/localizeUtils");
const localSettingsProvider_1 = require("../common/localSettingsProvider");
const projectSettingsHelper_1 = require("../common/projectSettingsHelper");
const telemetry_1 = require("../common/telemetry");
const tools_1 = require("../common/tools");
const folder_1 = require("../folder");
const constants_1 = require("../component/constants");
const callback_1 = require("./callback");
const collaborator_1 = require("./collaborator");
const crypto_1 = require("./crypto");
const environment_1 = require("./environment");
const error_1 = require("./error");
const globalVars_1 = require("./globalVars");
const aadManifestMigration_1 = require("./middleware/aadManifestMigration");
const concurrentLocker_1 = require("./middleware/concurrentLocker");
const consolidateLocalRemote_1 = require("./middleware/consolidateLocalRemote");
const contextInjector_1 = require("./middleware/contextInjector");
const envInfoLoaderV3_1 = require("./middleware/envInfoLoaderV3");
const envInfoWriterV3_1 = require("./middleware/envInfoWriterV3");
const errorHandler_1 = require("./middleware/errorHandler");
const projectMigrator_1 = require("./middleware/projectMigrator");
const projectSettingsLoader_1 = require("./middleware/projectSettingsLoader");
const projectSettingsWriter_1 = require("./middleware/projectSettingsWriter");
const questionModel_1 = require("./middleware/questionModel");
const question_1 = require("./question");
const telemetry_2 = require("./telemetry");
const utils_1 = require("../component/utils");
const core_1 = require("../component/core");
const question_2 = require("../component/question");
const projectVersionChecker_1 = require("./middleware/projectVersionChecker");
const cicd_1 = require("../component/feature/cicd/cicd");
const appManifest_1 = require("../component/resource/appManifest/appManifest");
const ApiConnectorImpl_1 = require("../component/feature/apiconnector/ApiConnectorImpl");
const envManager_1 = require("../component/envManager");
const utils_2 = require("../common/utils");
const ManifestUtils_1 = require("../component/resource/appManifest/utils/ManifestUtils");
const arm_1 = require("../component/arm");
const local_1 = require("../common/local");
require("../component/driver/index");
const envUtil_1 = require("../component/utils/envUtil");
const parser_1 = require("../component/configManager/parser");
const videoFilterAppBlocker_1 = require("./middleware/videoFilterAppBlocker");
const FxCoreImplementV3_1 = require("./FxCoreImplementV3");
class FxCore {
    constructor(tools) {
        this.tools = tools;
        globalVars_1.setTools(tools);
        telemetry_1.TelemetryReporterInstance.telemetryReporter = tools.telemetryReporter;
        this.v3Implement = new FxCoreImplementV3_1.FxCoreV3Implement(tools);
    }
    /**
     * @todo this's a really primitive implement. Maybe could use Subscription Model to
     * refactor later.
     */
    on(event, callback) {
        return callback_1.CallbackRegistry.set(event, callback);
    }
    async createExistingTabApp(inputs, folder, ctx) {
        var _a, _b;
        (_a = globalVars_1.TOOLS.telemetryReporter) === null || _a === void 0 ? void 0 : _a.sendTelemetryEvent(telemetry_2.CoreTelemetryEvent.CreateStart, {
            [telemetry_2.CoreTelemetryProperty.Component]: telemetry_2.CoreTelemetryComponentName,
            [telemetry_2.CoreTelemetryProperty.Capabilities]: constants_1.ExistingTabOptionItem().id,
        });
        const appName = inputs[question_1.CoreQuestionNames.AppName];
        inputs.folder = path.join(folder, appName);
        const result = await this._init(inputs, ctx, true);
        if (result.isErr()) {
            return teamsfx_api_1.err(telemetry_2.sendErrorTelemetryThenReturnError(telemetry_2.CoreTelemetryEvent.Create, result.error, globalVars_1.TOOLS.telemetryReporter));
        }
        globalVars_1.TOOLS.ui.showMessage("info", localizeUtils_1.getLocalizedString("core.create.successNotice"), false);
        (_b = globalVars_1.TOOLS.telemetryReporter) === null || _b === void 0 ? void 0 : _b.sendTelemetryEvent(telemetry_2.CoreTelemetryEvent.Create, {
            [telemetry_2.CoreTelemetryProperty.Component]: telemetry_2.CoreTelemetryComponentName,
            [telemetry_2.CoreTelemetryProperty.Success]: telemetry_2.CoreTelemetrySuccess.Yes,
            [telemetry_2.CoreTelemetryProperty.Capabilities]: constants_1.ExistingTabOptionItem().id,
        });
        return result;
    }
    async createProject(inputs) {
        if (tools_1.isV3Enabled())
            return this.v3Implement.dispatch(this.createProject, inputs);
        else
            return this.createProjectOld(inputs);
    }
    /**
     * "teamsfx init infra" CLI command
     */
    async initInfra(inputs) {
        return this.v3Implement.dispatch(this.initInfra, inputs);
    }
    /**
     * "teamsfx init debug" CLI command
     */
    async initDebug(inputs) {
        return this.v3Implement.dispatch(this.initDebug, inputs);
    }
    async createProjectOld(inputs, ctx) {
        if (!ctx) {
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("ctx for createProject"));
        }
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.create);
        inputs.stage = teamsfx_api_1.Stage.create;
        const context = utils_1.createContextV3();
        const fx = typedi_1.Container.get("fx");
        const res = await fx.create(context, inputs);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
        ctx.projectSettings = context.projectSetting;
        inputs.projectPath = context.projectPath;
        return teamsfx_api_1.ok(context.projectPath);
    }
    async provisionResources(inputs) {
        if (tools_1.isV3Enabled()) {
            return this.v3Implement.dispatch(this.provisionResources, inputs);
        }
        else {
            return this.provisionResourcesOld(inputs);
        }
    }
    async provisionResourcesOld(inputs, ctx) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.provision);
        inputs.stage = teamsfx_api_1.Stage.provision;
        const context = utils_1.createContextV3();
        context.envInfo = ctx.envInfoV3;
        context.projectSetting = ctx.projectSettings;
        context.tokenProvider = globalVars_1.TOOLS.tokenProvider;
        if (context.envInfo.envName === "local") {
            context.envInfo.config.isLocalDebug = true;
        }
        const fx = typedi_1.Container.get("fx");
        const res = await fx.provision(context, inputs);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
        ctx.projectSettings = context.projectSetting;
        ctx.envInfoV3 = context.envInfo;
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    /**
     * Only used to provision Teams app with user provided app package
     * @param inputs
     * @returns teamsAppId on provision success
     */
    async provisionTeamsAppForCLI(inputs) {
        if (!inputs.appPackagePath) {
            return teamsfx_api_1.err(error_1.InvalidInputError("appPackagePath is not defined", inputs));
        }
        const projectSettings = {
            appName: "fake",
            projectId: uuid.v4(),
        };
        const context = {
            userInteraction: globalVars_1.TOOLS.ui,
            logProvider: globalVars_1.TOOLS.logProvider,
            telemetryReporter: globalVars_1.TOOLS.telemetryReporter,
            cryptoProvider: new crypto_1.LocalCrypto(projectSettings.projectId),
            permissionRequestProvider: globalVars_1.TOOLS.permissionRequest,
            projectSetting: projectSettings,
        };
        const appStudioV3 = typedi_1.Container.get(constants_1.ComponentNames.AppManifest);
        return appStudioV3.provisionForCLI(context, inputs, environment_1.newEnvInfoV3(), globalVars_1.TOOLS.tokenProvider);
    }
    async deployArtifacts(inputs) {
        if (tools_1.isV3Enabled()) {
            return this.v3Implement.dispatch(this.deployArtifacts, inputs);
        }
        else {
            return this.deployArtifactsOld(inputs);
        }
    }
    async deployArtifactsOld(inputs, ctx) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.deploy);
        inputs.stage = teamsfx_api_1.Stage.deploy;
        const context = utils_1.createContextV3();
        context.envInfo = ctx.envInfoV3;
        context.projectSetting = ctx.projectSettings;
        context.tokenProvider = globalVars_1.TOOLS.tokenProvider;
        const fx = typedi_1.Container.get("fx");
        const res = await fx.deploy(context, inputs);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
        ctx.projectSettings = context.projectSetting;
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    async localDebug(inputs) {
        inputs.env = environment_1.environmentManager.getLocalEnvName();
        return this.provisionResources(inputs);
    }
    async deployAadManifest(inputs) {
        return this.v3Implement.dispatch(this.deployAadManifest, inputs);
    }
    async publishApplication(inputs) {
        if (tools_1.isV3Enabled()) {
            return this.v3Implement.dispatch(this.publishApplication, inputs);
        }
        else {
            return this.publishApplicationOld(inputs);
        }
    }
    async publishApplicationOld(inputs, ctx) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.publish);
        inputs.stage = teamsfx_api_1.Stage.publish;
        const context = utils_1.createContextV3();
        context.envInfo = ctx.envInfoV3;
        context.projectSetting = ctx.projectSettings;
        context.tokenProvider = globalVars_1.TOOLS.tokenProvider;
        const appManifest = typedi_1.Container.get(constants_1.ComponentNames.AppManifest);
        const res = await appManifest.publish(context, inputs);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
        ctx.projectSettings = context.projectSetting;
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    async addFeature(inputs, ctx) {
        inputs.stage = teamsfx_api_1.Stage.addFeature;
        const context = utils_1.createContextV3(ctx === null || ctx === void 0 ? void 0 : ctx.projectSettings);
        const fx = typedi_1.Container.get("fx");
        const res = await fx.addFeature(context, inputs);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
        ctx.projectSettings = context.projectSetting;
        return teamsfx_api_1.ok(res.value);
    }
    async executeUserTask(func, inputs, ctx) {
        return tools_1.isV3Enabled()
            ? this.v3Implement.dispatchUserTask(this.executeUserTask, func, inputs)
            : this.executeUserTaskOld(func, inputs);
    }
    async executeUserTaskOld(func, inputs, ctx) {
        if (!ctx)
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("executeUserTask context"));
        let res = teamsfx_api_1.ok(undefined);
        const context = utils_1.createContextV3(ctx === null || ctx === void 0 ? void 0 : ctx.projectSettings);
        if (ctx === null || ctx === void 0 ? void 0 : ctx.envInfoV3) {
            context.envInfo = ctx.envInfoV3;
            if (context.envInfo.envName === "local") {
                context.envInfo.config.isLocalDebug = true;
            }
        }
        if (func.method === "addCICDWorkflows") {
            const component = typedi_1.Container.get("cicd");
            inputs[constants_1.AzureSolutionQuestionNames.Features] = constants_1.CicdOptionItem.id;
            res = await component.add(context, inputs);
        }
        else if (func.method === "connectExistingApi") {
            const component = typedi_1.Container.get("api-connector");
            inputs[constants_1.AzureSolutionQuestionNames.Features] = constants_1.ApiConnectionOptionItem.id;
            res = await component.add(context, inputs);
        }
        else if (func.method === "addSso") {
            inputs.stage = teamsfx_api_1.Stage.addFeature;
            inputs[constants_1.AzureSolutionQuestionNames.Features] = constants_1.SingleSignOnOptionItem.id;
            const component = typedi_1.Container.get("sso");
            res = await component.add(context, inputs);
        }
        else if (func.method === "addFeature") {
            inputs.stage = teamsfx_api_1.Stage.addFeature;
            const fx = typedi_1.Container.get("fx");
            res = await fx.addFeature(context, inputs);
        }
        else if (func.method === "getManifestTemplatePath") {
            const path = await ManifestUtils_1.manifestUtils.getTeamsAppManifestPath(inputs.projectPath);
            res = teamsfx_api_1.ok(path);
        }
        else if (func.method === "validateManifest") {
            const component = typedi_1.Container.get("app-manifest");
            res = await component.validate(context, inputs);
        }
        else if (func.method === "buildPackage") {
            const component = typedi_1.Container.get("app-manifest");
            res = await component.build(context, inputs);
        }
        else if (func.method === "updateManifest") {
            const component = typedi_1.Container.get("app-manifest");
            res = await component.deploy(context, inputs);
        }
        else if (func.method === "buildAadManifest") {
            const component = typedi_1.Container.get("aad-app");
            res = await component.buildAadManifest(context, inputs);
        }
        else {
            return teamsfx_api_1.err(new error_1.NotImplementedError(func.method));
        }
        if (res) {
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
            ctx.projectSettings = context.projectSetting;
            return res;
        }
        return res;
    }
    async deployTeamsManifest(inputs) {
        return this.v3Implement.dispatch(this.deployTeamsManifest, inputs);
    }
    /**
     * Warning: this API only works for CLI_HELP, it has no business with interactive run for CLI!
     */
    async getQuestions(stage, inputs) {
        inputs.stage = teamsfx_api_1.Stage.getQuestions;
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.getQuestions);
        const context = utils_1.createContextV3();
        if (stage === teamsfx_api_1.Stage.publish) {
            return await appManifest_1.publishQuestion(inputs);
        }
        else if (stage === teamsfx_api_1.Stage.create) {
            return await questionModel_1.getQuestionsForCreateProjectV2(inputs);
        }
        else if (stage === teamsfx_api_1.Stage.deploy) {
            return await question_2.getQuestionsForDeployV3(context, inputs);
        }
        else if (stage === teamsfx_api_1.Stage.provision) {
            return await question_2.getQuestionsForProvisionV3(inputs);
        }
        else if (stage === teamsfx_api_1.Stage.initDebug) {
            return await question_2.getQuestionsForInit("debug", inputs);
        }
        else if (stage === teamsfx_api_1.Stage.initInfra) {
            return await question_2.getQuestionsForInit("infra", inputs);
        }
        return teamsfx_api_1.ok(undefined);
    }
    async getQuestionsForAddFeature(featureId, inputs) {
        const res = await question_2.getQuestionsForAddFeatureSubCommand(featureId, inputs);
        return res;
    }
    async getQuestionsForUserTask(func, inputs) {
        inputs.stage = teamsfx_api_1.Stage.getQuestions;
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.getQuestions);
        const context = utils_1.createContextV3();
        if (func.method === "addFeature") {
            return await question_2.getQuestionsForAddFeatureV3(context, inputs);
        }
        else if (func.method === "addResource") {
            return await question_2.getQuestionsForAddResourceV3(context, inputs);
        }
        else if (func.method === "addCICDWorkflows") {
            return await cicd_1.addCicdQuestion(context, inputs);
        }
        else if (func.method === "connectExistingApi") {
            const apiConnectorImpl = new ApiConnectorImpl_1.ApiConnectorImpl();
            return await apiConnectorImpl.generateQuestion(context, inputs);
        }
        return teamsfx_api_1.ok(undefined);
    }
    async getSettings(inputs) {
        return this.v3Implement.dispatch(this.getSettings, inputs);
    }
    async getDotEnv(inputs) {
        return this.v3Implement.dispatch(this.getDotEnv, inputs);
    }
    async getProjectConfig(inputs, ctx) {
        var _a;
        if (!ctx)
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("getProjectConfig input stuff"));
        inputs.stage = teamsfx_api_1.Stage.getProjectConfig;
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.getProjectConfig);
        return teamsfx_api_1.ok({
            settings: ctx.projectSettings,
            config: (_a = ctx.envInfoV3) === null || _a === void 0 ? void 0 : _a.state,
        });
    }
    async getProjectConfigV3(inputs, ctx) {
        if (!ctx || !ctx.projectSettings)
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("getProjectConfigV3 input stuff"));
        if (!inputs.projectPath)
            return teamsfx_api_1.ok(undefined);
        inputs.stage = teamsfx_api_1.Stage.getProjectConfig;
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.getProjectConfig);
        const config = {
            projectSettings: ctx.projectSettings,
            envInfos: {},
        };
        const envNamesRes = await environment_1.environmentManager.listAllEnvConfigs(inputs.projectPath);
        if (envNamesRes.isErr()) {
            return teamsfx_api_1.err(envNamesRes.error);
        }
        for (const env of envNamesRes.value) {
            const result = await envInfoLoaderV3_1.loadEnvInfoV3(inputs, ctx.projectSettings, env, false);
            if (result.isErr()) {
                return teamsfx_api_1.err(result.error);
            }
            config.envInfos[env] = result.value;
        }
        return teamsfx_api_1.ok(config);
    }
    async grantPermission(inputs, ctx) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.grantPermission);
        inputs.stage = teamsfx_api_1.Stage.grantPermission;
        const projectPath = inputs.projectPath;
        if (!projectPath) {
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("projectPath"));
        }
        if (ctx && ctx.contextV2 && (tools_1.isV3Enabled() || ctx.envInfoV3)) {
            const context = utils_1.createContextV3(ctx === null || ctx === void 0 ? void 0 : ctx.projectSettings);
            context.envInfo = ctx.envInfoV3;
            const res = await collaborator_1.grantPermission(context, inputs, ctx.envInfoV3, globalVars_1.TOOLS.tokenProvider);
            return res;
        }
        return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("ctx, contextV2, envInfoV3"));
    }
    async checkPermission(inputs, ctx) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.checkPermission);
        inputs.stage = teamsfx_api_1.Stage.checkPermission;
        const projectPath = inputs.projectPath;
        if (!projectPath) {
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("projectPath"));
        }
        if (ctx && ctx.contextV2 && (tools_1.isV3Enabled() || ctx.envInfoV3)) {
            const context = utils_1.createContextV3(ctx === null || ctx === void 0 ? void 0 : ctx.projectSettings);
            context.envInfo = ctx.envInfoV3;
            const res = await collaborator_1.checkPermission(context, inputs, ctx.envInfoV3, globalVars_1.TOOLS.tokenProvider);
            return res;
        }
        return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("ctx, contextV2, envInfoV3"));
    }
    async listCollaborator(inputs, ctx) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.listCollaborator);
        inputs.stage = teamsfx_api_1.Stage.listCollaborator;
        const projectPath = inputs.projectPath;
        if (!projectPath) {
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("projectPath"));
        }
        if (ctx && ctx.contextV2 && (tools_1.isV3Enabled() || ctx.envInfoV3)) {
            const context = utils_1.createContextV3(ctx === null || ctx === void 0 ? void 0 : ctx.projectSettings);
            context.envInfo = ctx.envInfoV3;
            const res = await collaborator_1.listCollaborator(context, inputs, ctx.envInfoV3, globalVars_1.TOOLS.tokenProvider);
            return res;
        }
        return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("ctx, contextV2, envInfoV3"));
    }
    async getSelectedEnv(inputs, ctx) {
        return teamsfx_api_1.ok(inputs.env); //work for both v2 and v3
    }
    async encrypt(plaintext, inputs, ctx) {
        if (!ctx)
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("ctx"));
        if (!ctx.contextV2)
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("ctx.contextV2"));
        return ctx.contextV2.cryptoProvider.encrypt(plaintext);
    }
    async decrypt(ciphertext, inputs, ctx) {
        if (!ctx)
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("ctx"));
        if (!ctx.contextV2)
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("ctx.contextV2"));
        return ctx.contextV2.cryptoProvider.decrypt(ciphertext);
    }
    async buildArtifacts(inputs) {
        throw new error_1.TaskNotSupportError(teamsfx_api_1.Stage.build);
    }
    async createEnv(inputs) {
        if (tools_1.isV3Enabled()) {
            return this.v3Implement.dispatch(this.createEnv, inputs);
        }
        else {
            return this.createEnvOld(inputs);
        }
    }
    async createEnvOld(inputs, ctx) {
        if (!ctx || !inputs.projectPath)
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("createEnv input stuff"));
        const projectSettings = ctx.projectSettings;
        if (!projectSettings) {
            return teamsfx_api_1.ok(teamsfx_api_1.Void);
        }
        const core = ctx.self;
        const createEnvCopyInput = await envInfoLoaderV3_1.askNewEnvironment(ctx, inputs);
        if (!createEnvCopyInput ||
            !createEnvCopyInput.targetEnvName ||
            !createEnvCopyInput.sourceEnvName) {
            return teamsfx_api_1.err(teamsfx_api_1.UserCancelError);
        }
        const createEnvResult = await this.createEnvCopy(createEnvCopyInput.targetEnvName, createEnvCopyInput.sourceEnvName, inputs, core);
        if (createEnvResult.isErr()) {
            return createEnvResult;
        }
        inputs.sourceEnvName = createEnvCopyInput.sourceEnvName;
        inputs.targetEnvName = createEnvCopyInput.targetEnvName;
        if (!local_1.ProjectSettingsHelper.isSpfx(ctx.projectSettings)) {
            await arm_1.copyParameterJson(inputs.projectPath, ctx.projectSettings.appName, inputs.targetEnvName, inputs.sourceEnvName);
        }
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    // a phantom migration method for V3
    async phantomMigrationV3(inputs) {
        return this.v3Implement.dispatch(this.phantomMigrationV3, inputs);
    }
    // a project version check
    async projectVersionCheck(inputs) {
        return this.v3Implement.dispatch(this.projectVersionCheck, inputs);
    }
    async createEnvCopy(targetEnvName, sourceEnvName, inputs, core) {
        // copy env config file
        const targetEnvConfigFilePath = environment_1.environmentManager.getEnvConfigPath(targetEnvName, inputs.projectPath);
        const sourceEnvConfigFilePath = environment_1.environmentManager.getEnvConfigPath(sourceEnvName, inputs.projectPath);
        try {
            await fs_extra_1.default.copy(sourceEnvConfigFilePath, targetEnvConfigFilePath);
        }
        catch (e) {
            return teamsfx_api_1.err(error_1.CopyFileError(e));
        }
        globalVars_1.TOOLS.logProvider.debug(`[core] copy env config file for ${targetEnvName} environment to path ${targetEnvConfigFilePath}`);
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    async activateEnv(inputs, ctx) {
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    // apply the given yaml template to current project.
    async apply(inputs, templatePath, lifecycleName) {
        if (!inputs.projectPath) {
            return teamsfx_api_1.err(error_1.InvalidInputError("invalid projectPath", inputs));
        }
        const projectPath = inputs.projectPath;
        if (!inputs.env) {
            return teamsfx_api_1.err(error_1.InvalidInputError("invalid env", inputs));
        }
        const env = inputs.env;
        const lifecycleName_ = lifecycleName;
        const result = await envUtil_1.envUtil.readEnv(projectPath, env);
        if (result.isErr()) {
            return teamsfx_api_1.err(result.error);
        }
        const parser = new parser_1.YamlParser();
        const maybeProjectModel = await parser.parse(templatePath);
        if (maybeProjectModel.isErr()) {
            return teamsfx_api_1.err(maybeProjectModel.error);
        }
        const projectModel = maybeProjectModel.value;
        const driverContext = {
            azureAccountProvider: globalVars_1.TOOLS.tokenProvider.azureAccountProvider,
            m365TokenProvider: globalVars_1.TOOLS.tokenProvider.m365TokenProvider,
            ui: globalVars_1.TOOLS.ui,
            logProvider: globalVars_1.TOOLS.logProvider,
            telemetryReporter: globalVars_1.TOOLS.telemetryReporter,
            projectPath: projectPath,
            platform: inputs.platform,
        };
        const lifecycle = projectModel[lifecycleName_];
        if (lifecycle) {
            return this.runLifecycle(lifecycle, driverContext, env);
        }
        else {
            await driverContext.logProvider.warning(`No definition found for ${lifecycleName}`);
            return teamsfx_api_1.ok(teamsfx_api_1.Void);
        }
    }
    async runLifecycle(lifecycle, driverContext, env) {
        const r = await lifecycle.execute(driverContext);
        const runResult = r.result;
        if (runResult.isOk()) {
            await driverContext.logProvider.info(`Lifecycle ${lifecycle.name} succeeded`);
            const writeResult = await envUtil_1.envUtil.writeEnv(driverContext.projectPath, env, envUtil_1.envUtil.map2object(runResult.value));
            return writeResult.map(() => teamsfx_api_1.Void);
        }
        else {
            const error = runResult.error;
            if (error.kind === "Failure") {
                await driverContext.logProvider.error(`Failed to run ${lifecycle.name} due to ${error.error.name}: ${error.error.message}`);
                return teamsfx_api_1.err(error.error);
            }
            else {
                try {
                    const failedDriver = error.reason.failedDriver;
                    if (error.reason.kind === "UnresolvedPlaceholders") {
                        const unresolved = error.reason.unresolvedPlaceHolders;
                        await driverContext.logProvider.warning(`Unresolved placeholders: ${unresolved.join(",")} for driver ${failedDriver.uses}`);
                        return teamsfx_api_1.ok(teamsfx_api_1.Void);
                    }
                    else {
                        await driverContext.logProvider.error(`Failed to run ${lifecycle.name} due to ${error.reason.error.name}: ${error.reason.error.message}. Failed driver: ${failedDriver.uses}`);
                        return teamsfx_api_1.err(error.reason.error);
                    }
                }
                finally {
                    await envUtil_1.envUtil.writeEnv(driverContext.projectPath, env, envUtil_1.envUtil.map2object(error.env));
                }
            }
        }
    }
    async _init(inputs, ctx, isInitExistingApp = false) {
        if (!ctx) {
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("ctx for createProject"));
        }
        // validate app name
        const appName = inputs[question_1.CoreQuestionNames.AppName];
        const validateResult = jsonschema.validate(appName, {
            pattern: question_1.ProjectNamePattern,
        });
        if (validateResult.errors && validateResult.errors.length > 0) {
            return teamsfx_api_1.err(error_1.InvalidInputError("invalid app-name", inputs));
        }
        const projectPath = inputs.folder;
        if (!projectPath) {
            return teamsfx_api_1.err(error_1.InvalidInputError("projectPath is empty", inputs));
        }
        if (isInitExistingApp) {
            const folderExist = await fs_extra_1.default.pathExists(projectPath);
            if (folderExist) {
                return teamsfx_api_1.err(new error_1.ProjectFolderExistError(projectPath));
            }
        }
        else {
            const isValid = projectSettingsHelper_1.isValidProject(projectPath);
            if (isValid) {
                return teamsfx_api_1.err(new error_1.OperationNotPermittedError("initialize a project in existing teamsfx project"));
            }
        }
        await fs_extra_1.default.ensureDir(projectPath);
        inputs.projectPath = projectPath;
        // create ProjectSettings
        const projectSettings = projectSettingsHelper_1.newProjectSettings();
        projectSettings.appName = appName;
        projectSettings.components = [];
        ctx.projectSettings = projectSettings;
        // create folder structure
        await fs_extra_1.default.ensureDir(path.join(projectPath, `.${teamsfx_api_1.ConfigFolderName}`));
        await fs_extra_1.default.ensureDir(path.join(await utils_2.getProjectTemplatesFolderPath(projectPath), `${teamsfx_api_1.AppPackageFolderName}`));
        const basicFolderRes = await ensureBasicFolderStructure(inputs, false);
        if (basicFolderRes.isErr()) {
            return teamsfx_api_1.err(basicFolderRes.error);
        }
        // create contextV2
        const context = tools_1.createV2Context(projectSettings);
        ctx.contextV2 = context;
        const appStudioComponent = typedi_1.Container.get(constants_1.ComponentNames.AppManifest);
        // pre-check before initialize
        const preCheckResult = await core_1.preCheck(projectPath);
        if (preCheckResult.isErr()) {
            return teamsfx_api_1.err(preCheckResult.error);
        }
        // init manifest
        const manifestInitRes = await appStudioComponent.init(context, inputs, isInitExistingApp);
        if (manifestInitRes.isErr())
            return teamsfx_api_1.err(manifestInitRes.error);
        const manifestAddcapRes = await appStudioComponent.addCapability(inputs, [{ name: "staticTab", existingApp: true }]);
        if (manifestAddcapRes.isErr())
            return teamsfx_api_1.err(manifestAddcapRes.error);
        // create env config with existing tab's endpoint
        const endpoint = inputs[question_1.CoreQuestionNames.ExistingTabEndpoint];
        const createEnvResult = await envManager_1.createEnvWithName(environment_1.environmentManager.getDefaultEnvName(), projectSettings.appName, inputs, isInitExistingApp ? endpoint : undefined);
        if (createEnvResult.isErr()) {
            return teamsfx_api_1.err(createEnvResult.error);
        }
        const createLocalEnvResult = await envManager_1.createEnvWithName(environment_1.environmentManager.getLocalEnvName(), projectSettings.appName, inputs, isInitExistingApp ? endpoint : undefined);
        if (createLocalEnvResult.isErr()) {
            return teamsfx_api_1.err(createLocalEnvResult.error);
        }
        const sourceReadmePath = path.join(folder_1.getTemplatesFolder(), "core", teamsfx_api_1.DefaultReadme);
        if (await fs_extra_1.default.pathExists(sourceReadmePath)) {
            const targetReadmePath = path.join(projectPath, teamsfx_api_1.DefaultReadme);
            await fs_extra_1.default.copy(sourceReadmePath, targetReadmePath);
        }
        return teamsfx_api_1.ok(inputs.projectPath);
    }
    async init(inputs, ctx) {
        const result = await this._init(inputs, ctx);
        if (result.isOk()) {
            globalVars_1.TOOLS.ui.showMessage("info", localizeUtils_1.getLocalizedString("core.init.successNotice"), false);
        }
        return result;
    }
    async preProvisionForVS(inputs) {
        return this.v3Implement.dispatch(this.preProvisionForVS, inputs);
    }
    async publishInDeveloperPortal(inputs) {
        return this.v3Implement.dispatch(this.publishInDeveloperPortal, inputs);
    }
}
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, contextInjector_1.ContextInjectorMW, projectSettingsWriter_1.ProjectSettingsWriterMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "createProjectOld", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectMigrator_1.ProjectMigratorMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        aadManifestMigration_1.AadManifestMigrationMW,
        projectVersionChecker_1.ProjectVersionCheckerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        videoFilterAppBlocker_1.VideoFilterAppBlockerMW,
        envInfoLoaderV3_1.EnvInfoLoaderMW_V3(false),
        contextInjector_1.ContextInjectorMW,
        projectSettingsWriter_1.ProjectSettingsWriterMW,
        envInfoWriterV3_1.EnvInfoWriterMW_V3(),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "provisionResourcesOld", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectMigrator_1.ProjectMigratorMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        aadManifestMigration_1.AadManifestMigrationMW,
        projectVersionChecker_1.ProjectVersionCheckerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        videoFilterAppBlocker_1.VideoFilterAppBlockerMW,
        envInfoLoaderV3_1.EnvInfoLoaderMW_V3(false),
        contextInjector_1.ContextInjectorMW,
        projectSettingsWriter_1.ProjectSettingsWriterMW,
        envInfoWriterV3_1.EnvInfoWriterMW_V3(),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "deployArtifactsOld", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectMigrator_1.ProjectMigratorMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        aadManifestMigration_1.AadManifestMigrationMW,
        projectVersionChecker_1.ProjectVersionCheckerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        videoFilterAppBlocker_1.VideoFilterAppBlockerMW,
        envInfoLoaderV3_1.EnvInfoLoaderMW_V3(false),
        contextInjector_1.ContextInjectorMW,
        projectSettingsWriter_1.ProjectSettingsWriterMW,
        envInfoWriterV3_1.EnvInfoWriterMW_V3(),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "publishApplicationOld", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectMigrator_1.ProjectMigratorMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        aadManifestMigration_1.AadManifestMigrationMW,
        projectVersionChecker_1.ProjectVersionCheckerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        envInfoLoaderV3_1.EnvInfoLoaderMW_V3(false),
        contextInjector_1.ContextInjectorMW,
        projectSettingsWriter_1.ProjectSettingsWriterMW,
        envInfoWriterV3_1.EnvInfoWriterMW_V3(),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "addFeature", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectMigrator_1.ProjectMigratorMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        aadManifestMigration_1.AadManifestMigrationMW,
        projectVersionChecker_1.ProjectVersionCheckerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        videoFilterAppBlocker_1.VideoFilterAppBlockerMW,
        envInfoLoaderV3_1.EnvInfoLoaderMW_V3(false),
        contextInjector_1.ContextInjectorMW,
        projectSettingsWriter_1.ProjectSettingsWriterMW,
        envInfoWriterV3_1.EnvInfoWriterMW_V3(),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "executeUserTaskOld", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "getQuestions", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "getQuestionsForUserTask", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectMigrator_1.ProjectMigratorMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        aadManifestMigration_1.AadManifestMigrationMW,
        projectVersionChecker_1.ProjectVersionCheckerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        envInfoLoaderV3_1.EnvInfoLoaderMW_V3(false),
        contextInjector_1.ContextInjectorMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "getProjectConfig", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectMigrator_1.ProjectMigratorMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        aadManifestMigration_1.AadManifestMigrationMW,
        projectVersionChecker_1.ProjectVersionCheckerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        contextInjector_1.ContextInjectorMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "getProjectConfigV3", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectMigrator_1.ProjectMigratorMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        aadManifestMigration_1.AadManifestMigrationMW,
        projectVersionChecker_1.ProjectVersionCheckerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        envInfoLoaderV3_1.EnvInfoLoaderMW_V3(false, true),
        questionModel_1.QuestionModelMW,
        contextInjector_1.ContextInjectorMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "grantPermission", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectMigrator_1.ProjectMigratorMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        aadManifestMigration_1.AadManifestMigrationMW,
        projectVersionChecker_1.ProjectVersionCheckerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        envInfoLoaderV3_1.EnvInfoLoaderMW_V3(false, true),
        contextInjector_1.ContextInjectorMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "checkPermission", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectMigrator_1.ProjectMigratorMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        aadManifestMigration_1.AadManifestMigrationMW,
        projectVersionChecker_1.ProjectVersionCheckerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        envInfoLoaderV3_1.EnvInfoLoaderMW_V3(false, true),
        contextInjector_1.ContextInjectorMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "listCollaborator", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        concurrentLocker_1.ConcurrentLockerMW,
        projectSettingsLoader_1.ProjectSettingsLoaderMW,
        envInfoLoaderV3_1.EnvInfoLoaderMW_V3(false),
        contextInjector_1.ContextInjectorMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "getSelectedEnv", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, concurrentLocker_1.ConcurrentLockerMW, projectSettingsLoader_1.ProjectSettingsLoaderMW, contextInjector_1.ContextInjectorMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "encrypt", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, concurrentLocker_1.ConcurrentLockerMW, projectSettingsLoader_1.ProjectSettingsLoaderMW, contextInjector_1.ContextInjectorMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "decrypt", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, concurrentLocker_1.ConcurrentLockerMW, projectSettingsLoader_1.ProjectSettingsLoaderMW, contextInjector_1.ContextInjectorMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "createEnvOld", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, contextInjector_1.ContextInjectorMW, projectSettingsWriter_1.ProjectSettingsWriterMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCore.prototype, "init", null);
exports.FxCore = FxCore;
async function ensureBasicFolderStructure(inputs, createPackageJson = true) {
    if (!inputs.projectPath) {
        return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("projectPath"));
    }
    try {
        if (createPackageJson) {
            const appName = inputs[question_1.CoreQuestionNames.AppName];
            if (inputs.platform !== teamsfx_api_1.Platform.VS) {
                const packageJsonFilePath = path.join(inputs.projectPath, `package.json`);
                const exists = await fs_extra_1.default.pathExists(packageJsonFilePath);
                if (!exists) {
                    await fs_extra_1.default.writeFile(packageJsonFilePath, JSON.stringify({
                        name: appName,
                        version: "0.0.1",
                        description: "",
                        author: "",
                        scripts: {
                            test: 'echo "Error: no test specified" && exit 1',
                        },
                        devDependencies: {
                            "@microsoft/teamsfx-cli": "1.*",
                        },
                        license: "MIT",
                    }, null, 4));
                }
            }
        }
        {
            const gitIgnoreFilePath = path.join(inputs.projectPath, `.gitignore`);
            let lines = [];
            const exists = await fs_extra_1.default.pathExists(gitIgnoreFilePath);
            if (exists) {
                const content = await fs_extra_1.default.readFile(gitIgnoreFilePath, { encoding: "utf8" });
                lines = content.split("\n");
                for (let i = 0; i < lines.length; ++i) {
                    lines[i] = lines[i].trim();
                }
            }
            const gitIgnoreContent = [
                "\n# TeamsFx files",
                "node_modules",
                `.${teamsfx_api_1.ConfigFolderName}/${teamsfx_api_1.InputConfigsFolderName}/${localSettingsProvider_1.localSettingsFileName}`,
                `.${teamsfx_api_1.ConfigFolderName}/${teamsfx_api_1.StatesFolderName}/*.userdata`,
                ".DS_Store",
                ".env.teamsfx.local",
                "subscriptionInfo.json",
                teamsfx_api_1.BuildFolderName,
            ];
            gitIgnoreContent.push(`.${teamsfx_api_1.ConfigFolderName}/${teamsfx_api_1.InputConfigsFolderName}/config.local.json`);
            gitIgnoreContent.push(`.${teamsfx_api_1.ConfigFolderName}/${teamsfx_api_1.StatesFolderName}/state.local.json`);
            if (inputs.platform === teamsfx_api_1.Platform.VS) {
                gitIgnoreContent.push("appsettings.Development.json");
            }
            gitIgnoreContent.forEach((line) => {
                if (!lines.includes(line.trim())) {
                    lines.push(line.trim());
                }
            });
            await fs_extra_1.default.writeFile(gitIgnoreFilePath, lines.join("\n"), { encoding: "utf8" });
        }
    }
    catch (e) {
        return teamsfx_api_1.err(error_1.WriteFileError(e));
    }
    return teamsfx_api_1.ok(null);
}
exports.ensureBasicFolderStructure = ensureBasicFolderStructure;
//# sourceMappingURL=FxCore.js.map