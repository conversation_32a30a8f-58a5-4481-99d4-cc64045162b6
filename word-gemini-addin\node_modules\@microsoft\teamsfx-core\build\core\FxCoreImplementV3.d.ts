import { Func, FxError, Inputs, InputsWithProjectPath, Result, Settings, Tools, Void } from "@microsoft/teamsfx-api";
import { CoreHookContext, PreProvisionResForVS, VersionCheckRes } from "./types";
import "../component/driver/index";
import { DotenvParseOutput } from "dotenv";
export declare class FxCoreV3Implement {
    tools: Tools;
    isFromSample?: boolean;
    settingsVersion?: string;
    constructor(tools: Tools);
    dispatch<Inputs, ExecuteRes>(exec: (inputs: Inputs) => Promise<ExecuteRes>, inputs: Inputs): Promise<ExecuteRes>;
    dispatchUserTask<Inputs, ExecuteRes>(exec: (func: Func, inputs: Inputs) => Promise<ExecuteRes>, func: Func, inputs: Inputs): Promise<ExecuteRes>;
    createProject(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<string, FxError>>;
    initInfra(inputs: Inputs): Promise<Result<undefined, FxError>>;
    initDebug(inputs: Inputs): Promise<Result<undefined, FxError>>;
    provisionResources(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    deployArtifacts(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    deployAadManifest(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    publishApplication(inputs: Inputs): Promise<Result<Void, FxError>>;
    deployTeamsManifest(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    executeUserTask(func: Func, inputs: Inputs, ctx?: CoreHookContext): Promise<Result<any, FxError>>;
    publishInDeveloperPortal(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    getSettings(inputs: InputsWithProjectPath): Promise<Result<Settings, FxError>>;
    getDotEnv(inputs: InputsWithProjectPath, ctx?: CoreHookContext): Promise<Result<DotenvParseOutput | undefined, FxError>>;
    phantomMigrationV3(inputs: Inputs): Promise<Result<Void, FxError>>;
    projectVersionCheck(inputs: Inputs): Promise<Result<VersionCheckRes, FxError>>;
    preProvisionForVS(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<PreProvisionResForVS, FxError>>;
    createEnv(inputs: Inputs, ctx?: CoreHookContext): Promise<Result<Void, FxError>>;
    createEnvCopyV3(targetEnvName: string, sourceEnvName: string, projectPath: string): Promise<Result<Void, FxError>>;
    previewAadManifest(inputs: Inputs): Promise<Result<Void, FxError>>;
}
//# sourceMappingURL=FxCoreImplementV3.d.ts.map