{"version": 3, "file": "FxCoreImplementV3.d.ts", "sourceRoot": "", "sources": ["../../src/core/FxCoreImplementV3.ts"], "names": [], "mappings": "AAQA,OAAO,EAEL,IAAI,EACJ,OAAO,EACP,MAAM,EACN,qBAAqB,EAGrB,MAAM,EACN,QAAQ,EAER,KAAK,EAEL,IAAI,EACL,MAAM,wBAAwB,CAAC;AAmBhC,OAAO,EAAE,eAAe,EAAE,oBAAoB,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAGjF,OAAO,2BAA2B,CAAC;AAYnC,OAAO,EAAE,iBAAiB,EAAE,MAAM,QAAQ,CAAC;AAyB3C,qBAAa,iBAAiB;IAC5B,KAAK,EAAE,KAAK,CAAC;IACb,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,eAAe,CAAC,EAAE,MAAM,CAAC;gBAEb,KAAK,EAAE,KAAK;IAIlB,QAAQ,CAAC,MAAM,EAAE,UAAU,EAC/B,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,OAAO,CAAC,UAAU,CAAC,EAC7C,MAAM,EAAE,MAAM,GACb,OAAO,CAAC,UAAU,CAAC;IAShB,gBAAgB,CAAC,MAAM,EAAE,UAAU,EACvC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,KAAK,OAAO,CAAC,UAAU,CAAC,EACzD,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,MAAM,GACb,OAAO,CAAC,UAAU,CAAC;IAUhB,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAiCtF,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAW9D,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAc9D,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IA8BzF,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAwBtF,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAqCxF,kBAAkB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAiBlE,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAW1F,eAAe,CACnB,IAAI,EAAE,IAAI,EACV,MAAM,EAAE,MAAM,EACd,GAAG,CAAC,EAAE,eAAe,GACpB,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAkC1B,wBAAwB,CAC5B,MAAM,EAAE,MAAM,EACd,GAAG,CAAC,EAAE,eAAe,GACpB,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAO3B,WAAW,CAAC,MAAM,EAAE,qBAAqB,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAK9E,SAAS,CACb,MAAM,EAAE,qBAAqB,EAC7B,GAAG,CAAC,EAAE,eAAe,GACpB,OAAO,CAAC,MAAM,CAAC,iBAAiB,GAAG,SAAS,EAAE,OAAO,CAAC,CAAC;IAKpD,kBAAkB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAKlE,mBAAmB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAoC9E,iBAAiB,CACrB,MAAM,EAAE,MAAM,EACd,GAAG,CAAC,EAAE,eAAe,GACpB,OAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;IAM3C,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAoBhF,eAAe,CACnB,aAAa,EAAE,MAAM,EACrB,aAAa,EAAE,MAAM,EACrB,WAAW,EAAE,MAAM,GAClB,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAqC3B,kBAAkB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;CAiBzE"}