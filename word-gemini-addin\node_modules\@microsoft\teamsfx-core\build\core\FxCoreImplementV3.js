"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.FxCoreV3Implement = void 0;
const tslib_1 = require("tslib");
const os = tslib_1.__importStar(require("os"));
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const path = tslib_1.__importStar(require("path"));
const typedi_1 = require("typedi");
const hooks_1 = require("@feathersjs/hooks");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const constants_1 = require("../component/constants");
const error_1 = require("./error");
const globalVars_1 = require("./globalVars");
const concurrentLocker_1 = require("./middleware/concurrentLocker");
const consolidateLocalRemote_1 = require("./middleware/consolidateLocalRemote");
const contextInjector_1 = require("./middleware/contextInjector");
const envInfoLoaderV3_1 = require("./middleware/envInfoLoaderV3");
const errorHandler_1 = require("./middleware/errorHandler");
const utils_1 = require("../component/utils");
const ManifestUtils_1 = require("../component/resource/appManifest/utils/ManifestUtils");
require("../component/driver/index");
const coordinator_1 = require("../component/coordinator");
const envMW_1 = require("../component/middleware/envMW");
const envUtil_1 = require("../component/utils/envUtil");
const settingsUtil_1 = require("../component/utils/settingsUtil");
const projectMigratorV3_1 = require("./middleware/projectMigratorV3");
const utils_2 = require("../component/resource/appManifest/utils/utils");
const telemetry_1 = require("./telemetry");
const projectSettingsHelper_1 = require("../common/projectSettingsHelper");
const v3MigrationUtils_1 = require("./middleware/utils/v3MigrationUtils");
const questionMW_1 = require("../component/middleware/questionMW");
const questionModel_1 = require("./middleware/questionModel");
const question_1 = require("../component/question");
const developerPortalScaffoldUtils_1 = require("../component/developerPortalScaffoldUtils");
const buildAadManifest_1 = require("../component/driver/aad/utility/buildAadManifest");
const missingEnvInFileError_1 = require("../component/driver/aad/error/missingEnvInFileError");
const localizeUtils_1 = require("../common/localizeUtils");
const versionMetadata_1 = require("../common/versionMetadata");
const pathUtils_1 = require("../component/utils/pathUtils");
const error_2 = require("../component/configManager/error");
const tools_1 = require("../common/tools");
class FxCoreV3Implement {
    constructor(tools) {
        this.tools = tools;
    }
    async dispatch(exec, inputs) {
        const methodName = exec.name;
        if (!this[methodName]) {
            throw new Error("no implement");
        }
        const method = this[methodName];
        return await method.call(this, inputs);
    }
    async dispatchUserTask(exec, func, inputs) {
        const methodName = exec.name;
        if (!this[methodName]) {
            throw new Error("no implement");
        }
        const method = this[methodName];
        return await method.call(this, func, inputs);
    }
    async createProject(inputs, ctx) {
        if (!ctx) {
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("ctx for createProject"));
        }
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.create);
        inputs.stage = teamsfx_api_1.Stage.create;
        const context = utils_1.createContextV3();
        if (developerPortalScaffoldUtils_1.isFromDevPortalInVSC(inputs)) {
            // should never happen as we do same check on Developer Portal.
            if (utils_2.containsUnsupportedFeature(inputs.teamsAppFromTdp)) {
                return teamsfx_api_1.err(error_1.InvalidInputError("Teams app contains unsupported features"));
            }
            else {
                context.telemetryReporter.sendTelemetryEvent(telemetry_1.CoreTelemetryEvent.CreateFromTdpStart, {
                    [telemetry_1.CoreTelemetryProperty.TdpTeamsAppFeatures]: utils_2.getFeaturesFromAppDefinition(inputs.teamsAppFromTdp).join(","),
                    [telemetry_1.CoreTelemetryProperty.TdpTeamsAppId]: inputs.teamsAppFromTdp.teamsAppId,
                });
            }
        }
        const res = await coordinator_1.coordinator.create(context, inputs);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
        ctx.projectSettings = context.projectSetting;
        inputs.projectPath = context.projectPath;
        return teamsfx_api_1.ok(inputs.projectPath);
    }
    async initInfra(inputs) {
        const res = await coordinator_1.coordinator.initInfra(utils_1.createContextV3(), inputs);
        return res;
    }
    async initDebug(inputs) {
        const res = await coordinator_1.coordinator.initDebug(utils_1.createContextV3(), inputs);
        return res;
    }
    async provisionResources(inputs, ctx) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.provision);
        inputs.stage = teamsfx_api_1.Stage.provision;
        const context = utils_1.createDriverContext(inputs);
        try {
            const res = await coordinator_1.coordinator.provision(context, inputs);
            if (res.isOk()) {
                ctx.envVars = res.value;
                return teamsfx_api_1.ok(teamsfx_api_1.Void);
            }
            else {
                // for partial success scenario, output is set in inputs object
                ctx.envVars = inputs.envVars;
                return teamsfx_api_1.err(res.error);
            }
        }
        finally {
            //reset subscription
            try {
                await globalVars_1.TOOLS.tokenProvider.azureAccountProvider.setSubscription("");
            }
            catch (e) { }
        }
    }
    async deployArtifacts(inputs, ctx) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.deploy);
        inputs.stage = teamsfx_api_1.Stage.deploy;
        const context = utils_1.createDriverContext(inputs);
        const res = await coordinator_1.coordinator.deploy(context, inputs);
        if (res.isOk()) {
            ctx.envVars = res.value;
            return teamsfx_api_1.ok(teamsfx_api_1.Void);
        }
        else {
            // for partial success scenario, output is set in inputs object
            ctx.envVars = inputs.envVars;
            return teamsfx_api_1.err(res.error);
        }
    }
    async deployAadManifest(inputs, ctx) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.deployAad);
        inputs.stage = teamsfx_api_1.Stage.deployAad;
        const updateAadClient = typedi_1.Container.get("aadApp/update");
        // In V3, the aad.template.json exist at .fx folder, and output to root build folder.
        const manifestTemplatePath = inputs.AAD_MANIFEST_FILE
            ? inputs.AAD_MANIFEST_FILE
            : path.join(inputs.projectPath, constants_1.AadConstants.DefaultTemplateFileName);
        if (!(await fs_extra_1.default.pathExists(manifestTemplatePath))) {
            return teamsfx_api_1.err(new error_1.NoAadManifestExistError(manifestTemplatePath));
        }
        await fs_extra_1.default.ensureDir(path.join(inputs.projectPath, "build"));
        const manifestOutputPath = path.join(inputs.projectPath, "build", `aad.${inputs.env}.json`);
        const inputArgs = {
            manifestPath: manifestTemplatePath,
            outputFilePath: manifestOutputPath,
        };
        const contextV3 = utils_1.createDriverContext(inputs);
        const res = await updateAadClient.run(inputArgs, contextV3);
        if (res.isErr()) {
            if (res.error instanceof missingEnvInFileError_1.MissingEnvInFileUserError) {
                res.error.message += " " + localizeUtils_1.getDefaultString("error.UpdateAadManifest.MissingEnvHint"); // hint users can run provision/debug to create missing env for our project template
                if (res.error.displayMessage) {
                    res.error.displayMessage +=
                        " " + localizeUtils_1.getLocalizedString("error.UpdateAadManifest.MissingEnvHint");
                }
            }
            return teamsfx_api_1.err(res.error);
        }
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    async publishApplication(inputs) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.publish);
        inputs.stage = teamsfx_api_1.Stage.publish;
        const context = utils_1.createDriverContext(inputs);
        const res = await coordinator_1.coordinator.publish(context, inputs);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    async deployTeamsManifest(inputs, ctx) {
        const context = utils_1.createContextV3(ctx === null || ctx === void 0 ? void 0 : ctx.projectSettings);
        const component = typedi_1.Container.get("app-manifest");
        const res = await component.deployV3(context, inputs);
        if (res.isOk()) {
            ctx.envVars = envUtil_1.envUtil.map2object(res.value);
        }
        return res;
    }
    async executeUserTask(func, inputs, ctx) {
        let res = teamsfx_api_1.ok(undefined);
        const context = utils_1.createDriverContext(inputs);
        if (func.method === "getManifestTemplatePath") {
            const path = await ManifestUtils_1.manifestUtils.getTeamsAppManifestPath(inputs.projectPath);
            res = teamsfx_api_1.ok(path);
        }
        else if (func.method === "validateManifest") {
            const driver = typedi_1.Container.get("teamsApp/validate");
            const args = {
                manifestPath: func.params.manifestTemplatePath,
            };
            res = await driver.run(args, context);
        }
        else if (func.method === "buildPackage") {
            const driver = typedi_1.Container.get("teamsApp/zipAppPackage");
            const args = {
                manifestPath: func.params.manifestTemplatePath,
                outputZipPath: func.params.outputZipPath,
                outputJsonPath: func.params.outputJsonPath,
            };
            res = await driver.run(args, context);
        }
        else if (func.method === "addSso") {
            inputs.stage = teamsfx_api_1.Stage.addFeature;
            inputs[constants_1.AzureSolutionQuestionNames.Features] = constants_1.SingleSignOnOptionItem.id;
            const component = typedi_1.Container.get("sso");
            res = await component.add(context, inputs);
        }
        else if (func.method === "buildAadManifest") {
            res = await this.previewAadManifest(inputs);
        }
        return res;
    }
    async publishInDeveloperPortal(inputs, ctx) {
        globalVars_1.setCurrentStage(teamsfx_api_1.Stage.publishInDeveloperPortal);
        inputs.stage = teamsfx_api_1.Stage.publishInDeveloperPortal;
        const context = utils_1.createContextV3();
        return await coordinator_1.coordinator.publishInDeveloperPortal(context, inputs);
    }
    async getSettings(inputs) {
        return settingsUtil_1.settingsUtil.readSettings(inputs.projectPath);
    }
    async getDotEnv(inputs, ctx) {
        return teamsfx_api_1.ok(ctx === null || ctx === void 0 ? void 0 : ctx.envVars);
    }
    async phantomMigrationV3(inputs) {
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    async projectVersionCheck(inputs) {
        const projectPath = inputs.projectPath || "";
        if (projectSettingsHelper_1.isValidProjectV3(projectPath) || projectSettingsHelper_1.isValidProjectV2(projectPath)) {
            const versionInfo = await v3MigrationUtils_1.getProjectVersionFromPath(projectPath);
            if (!versionInfo.version) {
                return teamsfx_api_1.err(new error_1.InvalidProjectError());
            }
            const trackingId = await v3MigrationUtils_1.getTrackingIdFromPath(projectPath);
            let isSupport;
            if (!tools_1.isV3Enabled()) {
                if (versionInfo.source === versionMetadata_1.VersionSource.projectSettings) {
                    isSupport = versionMetadata_1.VersionState.compatible;
                }
                else {
                    isSupport = versionMetadata_1.VersionState.unsupported;
                }
            }
            else {
                isSupport = v3MigrationUtils_1.getVersionState(versionInfo);
            }
            return teamsfx_api_1.ok({
                currentVersion: versionInfo.version,
                trackingId,
                isSupport,
                versionSource: versionMetadata_1.VersionSource[versionInfo.source],
            });
        }
        else {
            return teamsfx_api_1.err(new error_1.InvalidProjectError());
        }
    }
    async preProvisionForVS(inputs, ctx) {
        const context = utils_1.createDriverContext(inputs);
        return coordinator_1.coordinator.preProvisionForVS(context, inputs);
    }
    async createEnv(inputs, ctx) {
        if (!ctx || !inputs.projectPath)
            return teamsfx_api_1.err(new error_1.ObjectIsUndefinedError("createEnv input stuff"));
        const createEnvCopyInput = await envInfoLoaderV3_1.askNewEnvironment(ctx, inputs);
        if (!createEnvCopyInput ||
            !createEnvCopyInput.targetEnvName ||
            !createEnvCopyInput.sourceEnvName) {
            return teamsfx_api_1.err(teamsfx_api_1.UserCancelError);
        }
        return this.createEnvCopyV3(createEnvCopyInput.targetEnvName, createEnvCopyInput.sourceEnvName, inputs.projectPath);
    }
    async createEnvCopyV3(targetEnvName, sourceEnvName, projectPath) {
        let res = await pathUtils_1.pathUtils.getEnvFilePath(projectPath, sourceEnvName);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
        const sourceDotEnvFile = res.value;
        res = await pathUtils_1.pathUtils.getEnvFilePath(projectPath, targetEnvName);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
        const targetDotEnvFile = res.value;
        if (!sourceDotEnvFile || !targetDotEnvFile)
            return teamsfx_api_1.err(new error_2.InvalidEnvFolderPath("missing 'environmentFolderPath' field or environment folder not exist"));
        const source = await fs_extra_1.default.readFile(sourceDotEnvFile);
        const writeStream = fs_extra_1.default.createWriteStream(targetDotEnvFile);
        source
            .toString()
            .split(/\r?\n/)
            .forEach((line) => {
            const reg = /^([a-zA-Z_][a-zA-Z0-9_]*=)/g;
            const match = reg.exec(line);
            if (match) {
                if (match[1].startsWith("TEAMSFX_ENV=")) {
                    writeStream.write(`TEAMSFX_ENV=${targetEnvName}${os.EOL}`);
                }
                else {
                    writeStream.write(`${match[1]}${os.EOL}`);
                }
            }
            else {
                writeStream.write(`${line.trim()}${os.EOL}`);
            }
        });
        writeStream.end();
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    async previewAadManifest(inputs) {
        const manifestTemplatePath = inputs.AAD_MANIFEST_FILE
            ? inputs.AAD_MANIFEST_FILE
            : path.join(inputs.projectPath, constants_1.AadConstants.DefaultTemplateFileName);
        if (!(await fs_extra_1.default.pathExists(manifestTemplatePath))) {
            return teamsfx_api_1.err(new error_1.NoAadManifestExistError(manifestTemplatePath));
        }
        await fs_extra_1.default.ensureDir(path.join(inputs.projectPath, "build"));
        const manifestOutputPath = path.join(inputs.projectPath, "build", `aad.${inputs.env}.json`);
        const contextV3 = utils_1.createDriverContext(inputs);
        await buildAadManifest_1.buildAadManifest(contextV3, manifestTemplatePath, manifestOutputPath);
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
}
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, questionMW_1.QuestionMW(questionModel_1.getQuestionsForCreateProjectV2), contextInjector_1.ContextInjectorMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "createProject", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        questionMW_1.QuestionMW((inputs) => {
            return question_1.getQuestionsForInit("infra", inputs);
        }),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "initInfra", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        questionMW_1.QuestionMW((inputs) => {
            return question_1.getQuestionsForInit("debug", inputs);
        }),
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "initDebug", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        projectMigratorV3_1.ProjectMigratorMWV3,
        questionMW_1.QuestionMW(question_1.getQuestionsForProvisionV3),
        concurrentLocker_1.ConcurrentLockerMW,
        envMW_1.EnvLoaderMW(false),
        contextInjector_1.ContextInjectorMW,
        envMW_1.EnvWriterMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "provisionResources", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        projectMigratorV3_1.ProjectMigratorMWV3,
        concurrentLocker_1.ConcurrentLockerMW,
        envMW_1.EnvLoaderMW(false),
        contextInjector_1.ContextInjectorMW,
        envMW_1.EnvWriterMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "deployArtifacts", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        projectMigratorV3_1.ProjectMigratorMWV3,
        concurrentLocker_1.ConcurrentLockerMW,
        consolidateLocalRemote_1.ProjectConsolidateMW,
        envMW_1.EnvLoaderMW(false),
        contextInjector_1.ContextInjectorMW,
        envMW_1.EnvWriterMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "deployAadManifest", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, projectMigratorV3_1.ProjectMigratorMWV3, concurrentLocker_1.ConcurrentLockerMW, envMW_1.EnvLoaderMW(false)]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "publishApplication", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        projectMigratorV3_1.ProjectMigratorMWV3,
        concurrentLocker_1.ConcurrentLockerMW,
        envMW_1.EnvLoaderMW(true),
        contextInjector_1.ContextInjectorMW,
        envMW_1.EnvWriterMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "deployTeamsManifest", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, projectMigratorV3_1.ProjectMigratorMWV3, concurrentLocker_1.ConcurrentLockerMW, envMW_1.EnvLoaderMW(false)]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "executeUserTask", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, concurrentLocker_1.ConcurrentLockerMW, contextInjector_1.ContextInjectorMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "publishInDeveloperPortal", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, envMW_1.EnvLoaderMW(true), contextInjector_1.ContextInjectorMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "getDotEnv", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, projectMigratorV3_1.ProjectMigratorMWV3]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "phantomMigrationV3", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "projectVersionCheck", null);
tslib_1.__decorate([
    hooks_1.hooks([
        errorHandler_1.ErrorHandlerMW,
        projectMigratorV3_1.ProjectMigratorMWV3,
        concurrentLocker_1.ConcurrentLockerMW,
        envMW_1.EnvLoaderMW(false),
        contextInjector_1.ContextInjectorMW,
    ]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "preProvisionForVS", null);
tslib_1.__decorate([
    hooks_1.hooks([errorHandler_1.ErrorHandlerMW, concurrentLocker_1.ConcurrentLockerMW, contextInjector_1.ContextInjectorMW]),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], FxCoreV3Implement.prototype, "createEnv", null);
exports.FxCoreV3Implement = FxCoreV3Implement;
//# sourceMappingURL=FxCoreImplementV3.js.map