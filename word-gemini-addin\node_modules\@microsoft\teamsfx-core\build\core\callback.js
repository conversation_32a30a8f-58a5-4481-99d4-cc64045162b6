"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.CallbackRegistry = void 0;
class CallbackRegistry {
    static has(event) {
        return this.registry.has(event);
    }
    static set(event, func) {
        if (!this.registry.has(event)) {
            this.registry.set(event, []);
        }
        const funcs = this.registry.get(event);
        funcs.push(func);
        this.registry.set(event, funcs);
    }
    static get(event) {
        if (this.registry.has(event)) {
            // eslint-disable-next-line  @typescript-eslint/no-non-null-assertion
            return this.registry.get(event);
        }
        else {
            return [];
        }
    }
    static refresh() {
        this.registry = new Map();
    }
}
exports.CallbackRegistry = CallbackRegistry;
CallbackRegistry.registry = new Map();
//# sourceMappingURL=callback.js.map