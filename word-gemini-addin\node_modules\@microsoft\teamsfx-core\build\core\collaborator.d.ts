import { FxE<PERSON>r, Result, TokenProvider, v2, v3, <PERSON>son, Inputs, QTreeNode, ContextV3, M365TokenProvider } from "@microsoft/teamsfx-api";
import { AppIds, ListCollaboratorResult, PermissionsResult } from "../common/permissionInterface";
import { AppUser } from "../component/resource/appManifest/interfaces/appUser";
export declare class CollaborationConstants {
    static readonly TeamsAppId = "teamsAppId";
    static readonly AadObjectId = "aadObjectId";
    static readonly DotEnvFilePath = "dotEnvFilePath";
    static readonly AadObjectIdEnv = "AAD_APP_OBJECT_ID";
    static readonly TeamsAppIdEnv = "TEAMS_APP_ID";
    static readonly TeamsAppTenantIdEnv = "TEAMS_APP_TENANT_ID";
}
export declare class CollaborationUtil {
    static getCurrentUserInfo(m365TokenProvider?: M365TokenProvider): Promise<Result<AppUser, FxError>>;
    static getUserInfo(m365TokenProvider?: M365TokenProvider, email?: string): Promise<AppUser | undefined>;
    static loadDotEnvFile(dotEnvFilePath: string): Promise<Result<{
        [key: string]: string;
    }, FxError>>;
    static getTeamsAppIdAndAadObjectId(inputs: v2.InputsWithProjectPath): Promise<Result<AppIds, FxError>>;
}
export declare function listCollaborator(ctx: ContextV3, inputs: v2.InputsWithProjectPath, envInfo: v3.EnvInfoV3 | undefined, tokenProvider: TokenProvider, telemetryProps?: Json): Promise<Result<ListCollaboratorResult, FxError>>;
export declare function checkPermission(ctx: ContextV3, inputs: v2.InputsWithProjectPath, envInfo: v3.EnvInfoV3 | undefined, tokenProvider: TokenProvider, telemetryProps?: Json): Promise<Result<PermissionsResult, FxError>>;
export declare function grantPermission(ctx: ContextV3, inputs: v2.InputsWithProjectPath, envInfo: v3.EnvInfoV3 | undefined, tokenProvider: TokenProvider, telemetryProps?: Json): Promise<Result<PermissionsResult, FxError>>;
export declare function getQuestionsForGrantPermission(inputs: Inputs): Promise<Result<QTreeNode | undefined, FxError>>;
//# sourceMappingURL=collaborator.d.ts.map