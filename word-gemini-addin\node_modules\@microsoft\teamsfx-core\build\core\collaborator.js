"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.getQuestionsForGrantPermission = exports.grantPermission = exports.checkPermission = exports.listCollaborator = exports.CollaborationUtil = exports.CollaborationConstants = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const typedi_1 = require("typedi");
const permissionInterface_1 = require("../common/permissionInterface");
const tools_1 = require("../common/tools");
const constants_1 = require("../component/constants");
const error_1 = require("./error");
const globalVars_1 = require("./globalVars");
const question_1 = require("../component/question");
const localizeUtils_1 = require("../common/localizeUtils");
const constants_2 = require("../common/constants");
const constants_3 = require("../component/constants");
const projectSettingsHelperV3_1 = require("../common/projectSettingsHelperV3");
const axios_1 = tslib_1.__importDefault(require("axios"));
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const dotenv = tslib_1.__importStar(require("dotenv"));
class CollaborationConstants {
}
exports.CollaborationConstants = CollaborationConstants;
// Collaboartion CLI parameters
CollaborationConstants.TeamsAppId = "teamsAppId";
CollaborationConstants.AadObjectId = "aadObjectId";
CollaborationConstants.DotEnvFilePath = "dotEnvFilePath";
// Collaboration env key
CollaborationConstants.AadObjectIdEnv = "AAD_APP_OBJECT_ID";
CollaborationConstants.TeamsAppIdEnv = "TEAMS_APP_ID";
CollaborationConstants.TeamsAppTenantIdEnv = "TEAMS_APP_TENANT_ID";
class CollaborationUtil {
    static async getCurrentUserInfo(m365TokenProvider) {
        const user = await CollaborationUtil.getUserInfo(m365TokenProvider);
        if (!user) {
            return teamsfx_api_1.err(new teamsfx_api_1.SystemError(constants_1.SolutionSource, constants_1.SolutionError.FailedToRetrieveUserInfo, "Failed to retrieve current user info from graph token."));
        }
        return teamsfx_api_1.ok(user);
    }
    static async getUserInfo(m365TokenProvider, email) {
        const currentUserRes = await (m365TokenProvider === null || m365TokenProvider === void 0 ? void 0 : m365TokenProvider.getJsonObject({ scopes: tools_1.GraphScopes }));
        const currentUser = (currentUserRes === null || currentUserRes === void 0 ? void 0 : currentUserRes.isOk()) ? currentUserRes.value : undefined;
        if (!currentUser) {
            return undefined;
        }
        const tenantId = currentUser["tid"];
        let aadId = currentUser["oid"];
        let userPrincipalName = currentUser["unique_name"];
        let displayName = currentUser["name"];
        const isAdministrator = true;
        if (email) {
            const graphTokenRes = await (m365TokenProvider === null || m365TokenProvider === void 0 ? void 0 : m365TokenProvider.getAccessToken({ scopes: tools_1.GraphScopes }));
            const graphToken = (graphTokenRes === null || graphTokenRes === void 0 ? void 0 : graphTokenRes.isOk()) ? graphTokenRes.value : undefined;
            const instance = axios_1.default.create({
                baseURL: "https://graph.microsoft.com/v1.0",
            });
            instance.defaults.headers.common["Authorization"] = `Bearer ${graphToken}`;
            const res = await instance.get(`/users?$filter=startsWith(mail,'${email}') or startsWith(userPrincipalName, '${email}')`);
            if (!res || !res.data || !res.data.value) {
                return undefined;
            }
            const collaborator = res.data.value.find((user) => {
                var _a, _b;
                return ((_a = user.mail) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === email.toLowerCase() ||
                    ((_b = user.userPrincipalName) === null || _b === void 0 ? void 0 : _b.toLowerCase()) === email.toLowerCase();
            });
            if (!collaborator) {
                return undefined;
            }
            aadId = collaborator.id;
            userPrincipalName = collaborator.userPrincipalName;
            displayName = collaborator.displayName;
        }
        return {
            tenantId,
            aadId,
            userPrincipalName,
            displayName,
            isAdministrator,
        };
    }
    static async loadDotEnvFile(dotEnvFilePath) {
        try {
            const result = {};
            if (!(await fs_extra_1.default.pathExists(dotEnvFilePath))) {
                throw new Error(localizeUtils_1.getLocalizedString("core.collaboration.error.dotEnvFileNotExist"));
            }
            const envs = dotenv.parse(await fs_extra_1.default.readFile(dotEnvFilePath));
            const entries = Object.entries(envs);
            for (const [key, value] of entries) {
                result[key] = value;
            }
            return teamsfx_api_1.ok(result);
        }
        catch (error) {
            return teamsfx_api_1.err(new teamsfx_api_1.UserError(constants_1.SolutionSource, constants_1.SolutionError.FailedToLoadDotEnvFile, localizeUtils_1.getLocalizedString("core.collaboration.error.failedToLoadDotEnvFile", error === null || error === void 0 ? void 0 : error.message)));
        }
    }
    // Priority parameter > dotenv > env
    static async getTeamsAppIdAndAadObjectId(inputs) {
        var _a, _b, _c, _d, _e, _f;
        let teamsAppId, aadObjectId;
        // load from parameter and dotenv only wroks for cli
        if ((inputs === null || inputs === void 0 ? void 0 : inputs.platform) == teamsfx_api_1.Platform.CLI) {
            // 1. Get from parameter
            teamsAppId = (_a = inputs === null || inputs === void 0 ? void 0 : inputs[CollaborationConstants.TeamsAppId]) !== null && _a !== void 0 ? _a : undefined;
            aadObjectId = (_b = inputs === null || inputs === void 0 ? void 0 : inputs[CollaborationConstants.AadObjectId]) !== null && _b !== void 0 ? _b : undefined;
            // Return if getting two app ids
            if (teamsAppId && aadObjectId) {
                return teamsfx_api_1.ok({
                    teamsAppId: teamsAppId,
                    aadObjectId: aadObjectId,
                });
            }
            // 2. Get from dotenv
            if (inputs === null || inputs === void 0 ? void 0 : inputs[CollaborationConstants.DotEnvFilePath]) {
                const loadDotEnvFileResult = await this.loadDotEnvFile(inputs === null || inputs === void 0 ? void 0 : inputs[CollaborationConstants.DotEnvFilePath]);
                if (loadDotEnvFileResult.isErr()) {
                    return teamsfx_api_1.err(loadDotEnvFileResult.error);
                }
                const dotEnv = loadDotEnvFileResult.value;
                teamsAppId = (_c = teamsAppId !== null && teamsAppId !== void 0 ? teamsAppId : dotEnv[CollaborationConstants.TeamsAppIdEnv]) !== null && _c !== void 0 ? _c : undefined;
                aadObjectId = (_d = aadObjectId !== null && aadObjectId !== void 0 ? aadObjectId : dotEnv[CollaborationConstants.AadObjectIdEnv]) !== null && _d !== void 0 ? _d : undefined;
                // Return if getting two app ids
                if (teamsAppId && aadObjectId) {
                    return teamsfx_api_1.ok({
                        teamsAppId: teamsAppId,
                        aadObjectId: aadObjectId,
                    });
                }
            }
        }
        // 3. load from env
        // TODO: load env from context
        teamsAppId = (_e = teamsAppId !== null && teamsAppId !== void 0 ? teamsAppId : process.env[CollaborationConstants.TeamsAppIdEnv]) !== null && _e !== void 0 ? _e : undefined;
        aadObjectId = (_f = aadObjectId !== null && aadObjectId !== void 0 ? aadObjectId : process.env[CollaborationConstants.AadObjectIdEnv]) !== null && _f !== void 0 ? _f : undefined;
        if (!teamsAppId) {
            return teamsfx_api_1.err(new teamsfx_api_1.UserError(constants_1.SolutionSource, constants_1.SolutionError.FailedToGetTeamsAppId, localizeUtils_1.getLocalizedString("core.collaboration.error.failedToGetTeamsAppId", CollaborationConstants.TeamsAppIdEnv)));
        }
        return teamsfx_api_1.ok({
            teamsAppId: teamsAppId,
            aadObjectId: aadObjectId,
        });
    }
}
exports.CollaborationUtil = CollaborationUtil;
async function listCollaborator(ctx, inputs, envInfo, tokenProvider, telemetryProps) {
    var _a, _b, _c, _d, _e, _f, _g;
    const result = await CollaborationUtil.getCurrentUserInfo(tokenProvider.m365TokenProvider);
    if (result.isErr()) {
        return teamsfx_api_1.err(result.error);
    }
    const user = result.value;
    if (!tools_1.isV3Enabled()) {
        const stateResult = getCurrentCollaborationState(envInfo, user);
        if (stateResult.state != permissionInterface_1.CollaborationState.OK) {
            if (inputs.platform === teamsfx_api_1.Platform.CLI && stateResult.message) {
                ctx.userInteraction.showMessage("warn", stateResult.message, false);
            }
            else if (inputs.platform === teamsfx_api_1.Platform.VSCode && stateResult.message) {
                ctx.logProvider.warning(stateResult.message);
            }
            return teamsfx_api_1.ok({
                state: stateResult.state,
                message: stateResult.message,
            });
        }
    }
    let appIds;
    if (tools_1.isV3Enabled()) {
        const getAppIdsResult = await CollaborationUtil.getTeamsAppIdAndAadObjectId(inputs);
        if (getAppIdsResult.isErr()) {
            return teamsfx_api_1.err(getAppIdsResult.error);
        }
        appIds = getAppIdsResult.value;
    }
    const hasAad = tools_1.isV3Enabled() ? appIds.aadObjectId != undefined : projectSettingsHelperV3_1.hasAAD(ctx.projectSetting);
    const appStudio = typedi_1.Container.get(constants_3.ComponentNames.AppManifest);
    const aadPlugin = typedi_1.Container.get(constants_3.ComponentNames.AadApp);
    const appStudioRes = await appStudio.listCollaborator(ctx, inputs, envInfo, tokenProvider.m365TokenProvider, tools_1.isV3Enabled() ? appIds.teamsAppId : undefined);
    if (appStudioRes.isErr())
        return teamsfx_api_1.err(appStudioRes.error);
    const teamsAppOwners = appStudioRes.value;
    const aadRes = hasAad
        ? await aadPlugin.listCollaborator(ctx, tools_1.isV3Enabled() ? appIds.aadObjectId : undefined)
        : teamsfx_api_1.ok([]);
    if (aadRes.isErr())
        return teamsfx_api_1.err(aadRes.error);
    const aadOwners = aadRes.value;
    const collaborators = [];
    const teamsAppId = (_b = (_a = teamsAppOwners[0]) === null || _a === void 0 ? void 0 : _a.resourceId) !== null && _b !== void 0 ? _b : "";
    const aadAppId = (_d = (_c = aadOwners[0]) === null || _c === void 0 ? void 0 : _c.resourceId) !== null && _d !== void 0 ? _d : "";
    const aadAppTenantId = tools_1.isV3Enabled()
        ? user.tenantId
        : (_e = envInfo.state[constants_3.ComponentNames.AppManifest]) === null || _e === void 0 ? void 0 : _e.tenantId;
    for (const teamsAppOwner of teamsAppOwners) {
        const aadOwner = aadOwners.find((owner) => owner.userObjectId === teamsAppOwner.userObjectId);
        collaborators.push({
            // For guest account, aadOwner.userPrincipalName will be user's email, and is easy to read.
            userPrincipalName: (_g = (_f = aadOwner === null || aadOwner === void 0 ? void 0 : aadOwner.userPrincipalName) !== null && _f !== void 0 ? _f : teamsAppOwner.userPrincipalName) !== null && _g !== void 0 ? _g : teamsAppOwner.userObjectId,
            userObjectId: teamsAppOwner.userObjectId,
            isAadOwner: aadOwner ? true : false,
            aadResourceId: aadOwner ? aadOwner.resourceId : undefined,
            teamsAppResourceId: teamsAppOwner.resourceId,
        });
    }
    if (inputs.platform === teamsfx_api_1.Platform.CLI || inputs.platform === teamsfx_api_1.Platform.VSCode) {
        const message = [
            {
                content: localizeUtils_1.getLocalizedString("core.collaboration.ListingM365Permission"),
                color: teamsfx_api_1.Colors.BRIGHT_WHITE,
            },
            {
                content: localizeUtils_1.getLocalizedString("core.collaboration.AccountUsedToCheck"),
                color: teamsfx_api_1.Colors.BRIGHT_WHITE,
            },
            { content: user.userPrincipalName + "\n", color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
            ...getPrintEnvMessage(tools_1.isV3Enabled() ? inputs.env : envInfo.envName, localizeUtils_1.getLocalizedString("core.collaboration.StartingListAllTeamsAppOwners")),
            { content: localizeUtils_1.getLocalizedString("core.collaboration.TenantId"), color: teamsfx_api_1.Colors.BRIGHT_WHITE },
            { content: aadAppTenantId + "\n", color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
            {
                content: localizeUtils_1.getLocalizedString("core.collaboration.M365TeamsAppId"),
                color: teamsfx_api_1.Colors.BRIGHT_WHITE,
            },
            { content: teamsAppId, color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
        ];
        if (hasAad) {
            message.push({
                content: localizeUtils_1.getLocalizedString("core.collaboration.SsoAadAppId"),
                color: teamsfx_api_1.Colors.BRIGHT_WHITE,
            }, { content: aadAppId, color: teamsfx_api_1.Colors.BRIGHT_MAGENTA }, { content: `)\n`, color: teamsfx_api_1.Colors.BRIGHT_WHITE });
        }
        else {
            message.push({ content: ")\n", color: teamsfx_api_1.Colors.BRIGHT_WHITE });
        }
        for (const collaborator of collaborators) {
            message.push({
                content: localizeUtils_1.getLocalizedString("core.collaboration.TeamsAppOwner"),
                color: teamsfx_api_1.Colors.BRIGHT_WHITE,
            }, { content: collaborator.userPrincipalName, color: teamsfx_api_1.Colors.BRIGHT_MAGENTA }, { content: `. `, color: teamsfx_api_1.Colors.BRIGHT_WHITE });
            if (hasAad && !collaborator.isAadOwner) {
                message.push({
                    content: localizeUtils_1.getLocalizedString("core.collaboration.NotOwnerOfSsoAadApp"),
                    color: teamsfx_api_1.Colors.BRIGHT_YELLOW,
                });
            }
            message.push({ content: "\n", color: teamsfx_api_1.Colors.BRIGHT_WHITE });
        }
        if (inputs.platform === teamsfx_api_1.Platform.CLI) {
            ctx.userInteraction.showMessage("info", message, false);
        }
        else if (inputs.platform === teamsfx_api_1.Platform.VSCode) {
            ctx.userInteraction.showMessage("info", localizeUtils_1.getLocalizedString("core.collaboration.ListCollaboratorsSuccess", hasAad ? localizeUtils_1.getLocalizedString("core.collaboration.WithAadApp") : "", constants_2.VSCodeExtensionCommand.showOutputChannel), false);
            ctx.logProvider.info(message);
        }
    }
    const aadOwnerCount = collaborators.filter((collaborator) => collaborator.aadResourceId && collaborator.isAadOwner).length;
    if (telemetryProps) {
        telemetryProps[constants_1.SolutionTelemetryProperty.Env] = tools_1.isV3Enabled()
            ? inputs.env
                ? tools_1.getHashedEnv(inputs.env)
                : undefined
            : tools_1.getHashedEnv(envInfo.envName);
        telemetryProps[constants_1.SolutionTelemetryProperty.CollaboratorCount] = collaborators.length.toString();
        telemetryProps[constants_1.SolutionTelemetryProperty.AadOwnerCount] = aadOwnerCount.toString();
    }
    return teamsfx_api_1.ok({
        collaborators: collaborators,
        state: permissionInterface_1.CollaborationState.OK,
    });
}
exports.listCollaborator = listCollaborator;
function getCurrentCollaborationState(envInfo, user) {
    var _a;
    const provisioned = envInfo.state.solution[constants_1.SOLUTION_PROVISION_SUCCEEDED] === "true" ||
        envInfo.state.solution[constants_1.SOLUTION_PROVISION_SUCCEEDED] === true;
    if (!provisioned) {
        const warningMsg = localizeUtils_1.getLocalizedString("core.collaboration.notProvisioned");
        return {
            state: permissionInterface_1.CollaborationState.NotProvisioned,
            message: warningMsg,
        };
    }
    const aadAppTenantId = (_a = envInfo.state[constants_3.ComponentNames.AppManifest]) === null || _a === void 0 ? void 0 : _a.tenantId;
    if (!aadAppTenantId || user.tenantId != aadAppTenantId) {
        const warningMsg = localizeUtils_1.getLocalizedString("core.collaboration.tenantNotMatch");
        return {
            state: permissionInterface_1.CollaborationState.M365TenantNotMatch,
            message: warningMsg,
        };
    }
    return {
        state: permissionInterface_1.CollaborationState.OK,
    };
}
async function checkPermission(ctx, inputs, envInfo, tokenProvider, telemetryProps) {
    var _a, _b;
    const result = await CollaborationUtil.getCurrentUserInfo(tokenProvider.m365TokenProvider);
    if (result.isErr()) {
        return teamsfx_api_1.err(result.error);
    }
    if (!tools_1.isV3Enabled()) {
        const stateResult = getCurrentCollaborationState(envInfo, result.value);
        if (stateResult.state != permissionInterface_1.CollaborationState.OK) {
            if (inputs.platform === teamsfx_api_1.Platform.CLI && stateResult.message) {
                ctx.userInteraction.showMessage("warn", stateResult.message, false);
            }
            return teamsfx_api_1.ok({
                state: stateResult.state,
                message: stateResult.message,
            });
        }
    }
    const userInfo = result.value;
    if (inputs.platform === teamsfx_api_1.Platform.CLI) {
        // TODO: get tenant id from .env
        const aadAppTenantId = tools_1.isV3Enabled()
            ? userInfo.tenantId
            : (_a = envInfo.state[constants_3.ComponentNames.AppManifest]) === null || _a === void 0 ? void 0 : _a.tenantId;
        const message = [
            {
                content: localizeUtils_1.getLocalizedString("core.collaboration.AccountUsedToCheck"),
                color: teamsfx_api_1.Colors.BRIGHT_WHITE,
            },
            { content: userInfo.userPrincipalName + "\n", color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
            ...getPrintEnvMessage(tools_1.isV3Enabled() ? inputs.env : envInfo.envName, localizeUtils_1.getLocalizedString("core.collaboration.StaringCheckPermission")),
            { content: localizeUtils_1.getLocalizedString("core.collaboration.TenantId"), color: teamsfx_api_1.Colors.BRIGHT_WHITE },
            { content: aadAppTenantId + "\n", color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
        ];
        ctx.userInteraction.showMessage("info", message, false);
    }
    let appIds;
    if (tools_1.isV3Enabled()) {
        const getAppIdsResult = await CollaborationUtil.getTeamsAppIdAndAadObjectId(inputs);
        if (getAppIdsResult.isErr()) {
            return teamsfx_api_1.err(getAppIdsResult.error);
        }
        appIds = getAppIdsResult.value;
    }
    const appStudio = typedi_1.Container.get(constants_3.ComponentNames.AppManifest);
    const aadPlugin = typedi_1.Container.get(constants_3.ComponentNames.AadApp);
    const appStudioRes = await appStudio.checkPermission(ctx, inputs, envInfo, tokenProvider.m365TokenProvider, userInfo, tools_1.isV3Enabled() ? appIds.teamsAppId : undefined);
    if (appStudioRes.isErr()) {
        return teamsfx_api_1.err(appStudioRes.error);
    }
    const permissions = appStudioRes.value;
    const isAadActivated = tools_1.isV3Enabled()
        ? appIds.aadObjectId != undefined
        : projectSettingsHelperV3_1.hasAAD(ctx.projectSetting);
    if (isAadActivated) {
        const aadRes = await aadPlugin.checkPermission(ctx, result.value, tools_1.isV3Enabled() ? appIds.aadObjectId : undefined);
        if (aadRes.isErr())
            return teamsfx_api_1.err(aadRes.error);
        aadRes.value.forEach((r) => {
            permissions.push(r);
        });
    }
    if (inputs.platform === teamsfx_api_1.Platform.CLI) {
        for (const permission of permissions) {
            const message = [
                {
                    content: localizeUtils_1.getLocalizedString("core.collaboration.CheckPermissionResourceId"),
                    color: teamsfx_api_1.Colors.BRIGHT_WHITE,
                },
                {
                    content: (_b = permission.resourceId) !== null && _b !== void 0 ? _b : localizeUtils_1.getLocalizedString("core.collaboration.Undefined"),
                    color: teamsfx_api_1.Colors.BRIGHT_MAGENTA,
                },
                {
                    content: localizeUtils_1.getLocalizedString("core.collaboration.ResourceName"),
                    color: teamsfx_api_1.Colors.BRIGHT_WHITE,
                },
                { content: permission.name, color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
                {
                    content: localizeUtils_1.getLocalizedString("core.collaboration.Permission"),
                    color: teamsfx_api_1.Colors.BRIGHT_WHITE,
                },
                {
                    content: permission.roles
                        ? permission.roles.toString()
                        : localizeUtils_1.getLocalizedString("core.collaboration.Undefined") + "\n",
                    color: teamsfx_api_1.Colors.BRIGHT_MAGENTA,
                },
            ];
            ctx.userInteraction.showMessage("info", message, false);
        }
    }
    const aadPermission = permissions.find((permission) => permission.name === "Azure AD App");
    const teamsAppPermission = permissions.find((permission) => permission.name === "Teams App");
    if (telemetryProps) {
        telemetryProps[constants_1.SolutionTelemetryProperty.AadPermission] = (aadPermission === null || aadPermission === void 0 ? void 0 : aadPermission.roles)
            ? aadPermission.roles.join(";")
            : localizeUtils_1.getLocalizedString("core.collaboration.Undefined");
        telemetryProps[constants_1.SolutionTelemetryProperty.TeamsAppPermission] = (teamsAppPermission === null || teamsAppPermission === void 0 ? void 0 : teamsAppPermission.roles)
            ? teamsAppPermission.roles.join(";")
            : localizeUtils_1.getLocalizedString("core.collaboration.Undefined");
    }
    return teamsfx_api_1.ok({
        state: permissionInterface_1.CollaborationState.OK,
        permissions,
    });
}
exports.checkPermission = checkPermission;
async function grantPermission(ctx, inputs, envInfo, tokenProvider, telemetryProps) {
    var _a, _b;
    const progressBar = ctx.userInteraction.createProgressBar(localizeUtils_1.getLocalizedString("core.collaboration.GrantingPermission"), 1);
    try {
        const result = await CollaborationUtil.getCurrentUserInfo(tokenProvider.m365TokenProvider);
        if (result.isErr()) {
            return teamsfx_api_1.err(result.error);
        }
        if (!tools_1.isV3Enabled()) {
            const stateResult = getCurrentCollaborationState(envInfo, result.value);
            if (stateResult.state != permissionInterface_1.CollaborationState.OK) {
                if (inputs.platform === teamsfx_api_1.Platform.CLI && stateResult.message) {
                    ctx.userInteraction.showMessage("warn", stateResult.message, false);
                }
                else if (inputs.platform === teamsfx_api_1.Platform.VSCode && stateResult.message) {
                    ctx.logProvider.warning(stateResult.message);
                }
                return teamsfx_api_1.ok({
                    state: stateResult.state,
                    message: stateResult.message,
                });
            }
        }
        const email = inputs.email;
        if (!email || email === result.value.userPrincipalName) {
            return teamsfx_api_1.err(new teamsfx_api_1.UserError(error_1.CoreSource, constants_1.SolutionError.EmailCannotBeEmptyOrSame, localizeUtils_1.getDefaultString("core.collaboration.EmailCannotBeEmptyOrSame"), localizeUtils_1.getLocalizedString("core.collaboration.EmailCannotBeEmptyOrSame")));
        }
        const userInfo = await CollaborationUtil.getUserInfo(tokenProvider.m365TokenProvider, email);
        if (!userInfo) {
            return teamsfx_api_1.err(new teamsfx_api_1.UserError(error_1.CoreSource, constants_1.SolutionError.CannotFindUserInCurrentTenant, localizeUtils_1.getDefaultString("core.collaboration.CannotFindUserInCurrentTenant"), localizeUtils_1.getLocalizedString("core.collaboration.CannotFindUserInCurrentTenant")));
        }
        await (progressBar === null || progressBar === void 0 ? void 0 : progressBar.start());
        await (progressBar === null || progressBar === void 0 ? void 0 : progressBar.next(localizeUtils_1.getLocalizedString("core.collaboration.GrantPermissionForUser", email)));
        let appIds;
        if (tools_1.isV3Enabled()) {
            const getAppIdsResult = await CollaborationUtil.getTeamsAppIdAndAadObjectId(inputs);
            if (getAppIdsResult.isErr()) {
                return teamsfx_api_1.err(getAppIdsResult.error);
            }
            appIds = getAppIdsResult.value;
        }
        if (inputs.platform === teamsfx_api_1.Platform.CLI) {
            // TODO: get tenant id from .env
            const aadAppTenantId = tools_1.isV3Enabled()
                ? result.value.tenantId
                : (_a = envInfo.state[constants_3.ComponentNames.AppManifest]) === null || _a === void 0 ? void 0 : _a.tenantId;
            const message = [
                {
                    content: localizeUtils_1.getLocalizedString("core.collaboration.AccountToGrantPermission"),
                    color: teamsfx_api_1.Colors.BRIGHT_WHITE,
                },
                { content: userInfo.userPrincipalName + "\n", color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
                ...getPrintEnvMessage(tools_1.isV3Enabled() ? inputs.env : envInfo.envName, localizeUtils_1.getLocalizedString("core.collaboration.StartingGrantPermission")),
                { content: localizeUtils_1.getLocalizedString("core.collaboration.TenantId"), color: teamsfx_api_1.Colors.BRIGHT_WHITE },
                { content: aadAppTenantId + "\n", color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
            ];
            ctx.userInteraction.showMessage("info", message, false);
        }
        const isAadActivated = tools_1.isV3Enabled()
            ? appIds.aadObjectId != undefined
            : projectSettingsHelperV3_1.hasAAD(ctx.projectSetting);
        const appStudio = typedi_1.Container.get(constants_3.ComponentNames.AppManifest);
        const aadPlugin = typedi_1.Container.get(constants_3.ComponentNames.AadApp);
        const appStudioRes = await appStudio.grantPermission(ctx, inputs, envInfo, tokenProvider.m365TokenProvider, userInfo, tools_1.isV3Enabled() ? appIds.teamsAppId : undefined);
        if (appStudioRes.isErr()) {
            return teamsfx_api_1.err(appStudioRes.error);
        }
        const permissions = appStudioRes.value;
        if (isAadActivated) {
            const aadRes = await aadPlugin.grantPermission(ctx, userInfo, tools_1.isV3Enabled() ? appIds.aadObjectId : undefined);
            if (aadRes.isErr())
                return teamsfx_api_1.err(aadRes.error);
            aadRes.value.forEach((r) => {
                permissions.push(r);
            });
        }
        if (inputs.platform === teamsfx_api_1.Platform.CLI) {
            for (const permission of permissions) {
                const message = [
                    { content: `${(_b = permission.roles) === null || _b === void 0 ? void 0 : _b.join(",")} `, color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
                    {
                        content: localizeUtils_1.getLocalizedString("core.collaboration.PermissionHasBeenGrantTo"),
                        color: teamsfx_api_1.Colors.BRIGHT_WHITE,
                    },
                    { content: permission.name, color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
                    {
                        content: localizeUtils_1.getLocalizedString("core.collaboration.GrantPermissionResourceId"),
                        color: teamsfx_api_1.Colors.BRIGHT_WHITE,
                    },
                    { content: `${permission.resourceId}`, color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
                ];
                ctx.userInteraction.showMessage("info", message, false);
            }
            // Will not show helplink for v3
            if (!tools_1.isV3Enabled() && projectSettingsHelperV3_1.hasSPFxTab(ctx.projectSetting)) {
                ctx.userInteraction.showMessage("info", localizeUtils_1.getLocalizedString("core.collaboration.SharePointTip") +
                    constants_1.SharePointManageSiteAdminHelpLink, false);
            }
            // Will not show helplink for v3
            if (!tools_1.isV3Enabled() && projectSettingsHelperV3_1.hasAzureResourceV3(ctx.projectSetting)) {
                ctx.userInteraction.showMessage("info", localizeUtils_1.getLocalizedString("core.collaboration.AzureTip") + constants_1.AzureRoleAssignmentsHelpLink, false);
            }
        }
        return teamsfx_api_1.ok({
            state: permissionInterface_1.CollaborationState.OK,
            userInfo: userInfo,
            permissions,
        });
    }
    finally {
        await (progressBar === null || progressBar === void 0 ? void 0 : progressBar.end(true));
    }
}
exports.grantPermission = grantPermission;
async function getQuestionsForGrantPermission(inputs) {
    const isDynamicQuestion = teamsfx_api_1.DynamicPlatforms.includes(inputs.platform);
    if (isDynamicQuestion) {
        const jsonObjectRes = await globalVars_1.TOOLS.tokenProvider.m365TokenProvider.getJsonObject({
            scopes: tools_1.AppStudioScopes,
        });
        if (jsonObjectRes.isErr()) {
            return teamsfx_api_1.err(jsonObjectRes.error);
        }
        const jsonObject = jsonObjectRes.value;
        return teamsfx_api_1.ok(new teamsfx_api_1.QTreeNode(question_1.getUserEmailQuestion(jsonObject.upn)));
    }
    return teamsfx_api_1.ok(undefined);
}
exports.getQuestionsForGrantPermission = getQuestionsForGrantPermission;
function getPrintEnvMessage(env, message) {
    return env
        ? [
            {
                content: message,
                color: teamsfx_api_1.Colors.BRIGHT_WHITE,
            },
            { content: `${env}\n`, color: teamsfx_api_1.Colors.BRIGHT_MAGENTA },
        ]
        : [];
}
//# sourceMappingURL=collaborator.js.map