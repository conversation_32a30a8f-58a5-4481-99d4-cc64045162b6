{"version": 3, "file": "collaborator.js", "sourceRoot": "", "sources": ["../../src/core/collaborator.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,wDAsBgC;AAChC,mCAAmC;AACnC,uEASuC;AACvC,2CAA0F;AAC1F,sDAOgC;AAEhC,mCAAqC;AACrC,6CAAqC;AACrC,oDAA6D;AAC7D,2DAA+E;AAC/E,mDAA6D;AAC7D,sDAAwD;AACxD,+EAA2F;AAE3F,0DAA0B;AAE1B,gEAA0B;AAC1B,uDAAiC;AAEjC,MAAa,sBAAsB;;AAAnC,wDAUC;AATC,+BAA+B;AACf,iCAAU,GAAG,YAAY,CAAC;AAC1B,kCAAW,GAAG,aAAa,CAAC;AAC5B,qCAAc,GAAG,gBAAgB,CAAC;AAElD,wBAAwB;AACR,qCAAc,GAAG,mBAAmB,CAAC;AACrC,oCAAa,GAAG,cAAc,CAAC;AAC/B,0CAAmB,GAAG,qBAAqB,CAAC;AAG9D,MAAa,iBAAiB;IAC5B,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAC7B,iBAAqC;QAErC,MAAM,IAAI,GAAG,MAAM,iBAAiB,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;QAEpE,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,iBAAG,CACR,IAAI,yBAAW,CACb,0BAAc,EACd,yBAAa,CAAC,wBAAwB,EACtC,wDAAwD,CACzD,CACF,CAAC;SACH;QAED,OAAO,gBAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,WAAW,CACtB,iBAAqC,EACrC,KAAc;QAEd,MAAM,cAAc,GAAG,MAAM,CAAA,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,aAAa,CAAC,EAAE,MAAM,EAAE,mBAAW,EAAE,CAAC,CAAA,CAAC;QACvF,MAAM,WAAW,GAAG,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,IAAI,EAAE,EAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAE9E,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,SAAS,CAAC;SAClB;QAED,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAW,CAAC;QAC9C,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAW,CAAC;QACzC,IAAI,iBAAiB,GAAG,WAAW,CAAC,aAAa,CAAW,CAAC;QAC7D,IAAI,WAAW,GAAG,WAAW,CAAC,MAAM,CAAW,CAAC;QAChD,MAAM,eAAe,GAAG,IAAI,CAAC;QAE7B,IAAI,KAAK,EAAE;YACT,MAAM,aAAa,GAAG,MAAM,CAAA,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,cAAc,CAAC,EAAE,MAAM,EAAE,mBAAW,EAAE,CAAC,CAAA,CAAC;YACvF,MAAM,UAAU,GAAG,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,IAAI,EAAE,EAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;YAC3E,MAAM,QAAQ,GAAG,eAAK,CAAC,MAAM,CAAC;gBAC5B,OAAO,EAAE,kCAAkC;aAC5C,CAAC,CAAC;YACH,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAU,UAAU,EAAE,CAAC;YAC3E,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,GAAG,CAC5B,mCAAmC,KAAK,wCAAwC,KAAK,IAAI,CAC1F,CAAC;YACF,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;gBACxC,OAAO,SAAS,CAAC;aAClB;YAED,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CACtC,CAAC,IAAS,EAAE,EAAE;;gBACZ,OAAA,CAAA,MAAA,IAAI,CAAC,IAAI,0CAAE,WAAW,EAAE,MAAK,KAAK,CAAC,WAAW,EAAE;oBAChD,CAAA,MAAA,IAAI,CAAC,iBAAiB,0CAAE,WAAW,EAAE,MAAK,KAAK,CAAC,WAAW,EAAE,CAAA;aAAA,CAChE,CAAC;YAEF,IAAI,CAAC,YAAY,EAAE;gBACjB,OAAO,SAAS,CAAC;aAClB;YAED,KAAK,GAAG,YAAY,CAAC,EAAE,CAAC;YACxB,iBAAiB,GAAG,YAAY,CAAC,iBAAiB,CAAC;YACnD,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;SACxC;QAED,OAAO;YACL,QAAQ;YACR,KAAK;YACL,iBAAiB;YACjB,WAAW;YACX,eAAe;SAChB,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,cAAc,CACzB,cAAsB;QAEtB,IAAI;YACF,MAAM,MAAM,GAA8B,EAAE,CAAC;YAC7C,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,EAAE;gBAC1C,MAAM,IAAI,KAAK,CAAC,kCAAkB,CAAC,6CAA6C,CAAC,CAAC,CAAC;aACpF;YAED,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;YAC7D,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACrC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,OAAO,EAAE;gBAClC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;aACrB;YACD,OAAO,gBAAE,CAAC,MAAM,CAAC,CAAC;SACnB;QAAC,OAAO,KAAU,EAAE;YACnB,OAAO,iBAAG,CACR,IAAI,uBAAS,CACX,0BAAc,EACd,yBAAa,CAAC,sBAAsB,EACpC,kCAAkB,CAAC,iDAAiD,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,CAAC,CACtF,CACF,CAAC;SACH;IACH,CAAC;IAED,oCAAoC;IACpC,MAAM,CAAC,KAAK,CAAC,2BAA2B,CACtC,MAAgC;;QAEhC,IAAI,UAAU,EAAE,WAAW,CAAC;QAE5B,oDAAoD;QACpD,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,KAAI,sBAAQ,CAAC,GAAG,EAAE;YACpC,wBAAwB;YACxB,UAAU,GAAG,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,sBAAsB,CAAC,UAAU,CAAC,mCAAI,SAAS,CAAC;YACtE,WAAW,GAAG,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,sBAAsB,CAAC,WAAW,CAAC,mCAAI,SAAS,CAAC;YACxE,gCAAgC;YAChC,IAAI,UAAU,IAAI,WAAW,EAAE;gBAC7B,OAAO,gBAAE,CAAC;oBACR,UAAU,EAAE,UAAU;oBACtB,WAAW,EAAE,WAAW;iBACzB,CAAC,CAAC;aACJ;YAED,qBAAqB;YACrB,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,sBAAsB,CAAC,cAAc,CAAC,EAAE;gBACnD,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,cAAc,CACpD,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAG,sBAAsB,CAAC,cAAc,CAAC,CAChD,CAAC;gBACF,IAAI,oBAAoB,CAAC,KAAK,EAAE,EAAE;oBAChC,OAAO,iBAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;iBACxC;gBAED,MAAM,MAAM,GAAG,oBAAoB,CAAC,KAAK,CAAC;gBAC1C,UAAU,GAAG,MAAA,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,MAAM,CAAC,sBAAsB,CAAC,aAAa,CAAC,mCAAI,SAAS,CAAC;gBACrF,WAAW,GAAG,MAAA,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,mCAAI,SAAS,CAAC;gBACxF,gCAAgC;gBAChC,IAAI,UAAU,IAAI,WAAW,EAAE;oBAC7B,OAAO,gBAAE,CAAC;wBACR,UAAU,EAAE,UAAU;wBACtB,WAAW,EAAE,WAAW;qBACzB,CAAC,CAAC;iBACJ;aACF;SACF;QAED,mBAAmB;QACnB,8BAA8B;QAC9B,UAAU,GAAG,MAAA,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,aAAa,CAAC,mCAAI,SAAS,CAAC;QAC1F,WAAW,GAAG,MAAA,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,cAAc,CAAC,mCAAI,SAAS,CAAC;QAE7F,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,iBAAG,CACR,IAAI,uBAAS,CACX,0BAAc,EACd,yBAAa,CAAC,qBAAqB,EACnC,kCAAkB,CAChB,gDAAgD,EAChD,sBAAsB,CAAC,aAAa,CACrC,CACF,CACF,CAAC;SACH;QAED,OAAO,gBAAE,CAAC;YACR,UAAU,EAAE,UAAU;YACtB,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;IACL,CAAC;CACF;AApKD,8CAoKC;AAEM,KAAK,UAAU,gBAAgB,CACpC,GAAc,EACd,MAAgC,EAChC,OAAiC,EACjC,aAA4B,EAC5B,cAAqB;;IAErB,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,kBAAkB,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;IAC3F,IAAI,MAAM,CAAC,KAAK,EAAE,EAAE;QAClB,OAAO,iBAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC1B;IACD,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;IAC1B,IAAI,CAAC,mBAAW,EAAE,EAAE;QAClB,MAAM,WAAW,GAA6B,4BAA4B,CAAC,OAAQ,EAAE,IAAI,CAAC,CAAC;QAC3F,IAAI,WAAW,CAAC,KAAK,IAAI,wCAAkB,CAAC,EAAE,EAAE;YAC9C,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE;gBAC3D,GAAG,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACrE;iBAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,MAAM,IAAI,WAAW,CAAC,OAAO,EAAE;gBACrE,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;aAC9C;YACD,OAAO,gBAAE,CAAC;gBACR,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B,CAAC,CAAC;SACJ;KACF;IAED,IAAI,MAAc,CAAC;IACnB,IAAI,mBAAW,EAAE,EAAE;QACjB,MAAM,eAAe,GAAG,MAAM,iBAAiB,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QACpF,IAAI,eAAe,CAAC,KAAK,EAAE,EAAE;YAC3B,OAAO,iBAAG,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SACnC;QACD,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC;KAChC;IAED,MAAM,MAAM,GAAG,mBAAW,EAAE,CAAC,CAAC,CAAC,MAAO,CAAC,WAAW,IAAI,SAAS,CAAC,CAAC,CAAC,gCAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC7F,MAAM,SAAS,GAAG,kBAAS,CAAC,GAAG,CAAc,0BAAc,CAAC,WAAW,CAAC,CAAC;IACzE,MAAM,SAAS,GAAG,kBAAS,CAAC,GAAG,CAAS,0BAAc,CAAC,MAAM,CAAC,CAAC;IAC/D,MAAM,YAAY,GAAG,MAAM,SAAS,CAAC,gBAAgB,CACnD,GAAG,EACH,MAAM,EACN,OAAO,EACP,aAAa,CAAC,iBAAiB,EAC/B,mBAAW,EAAE,CAAC,CAAC,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAC/C,CAAC;IACF,IAAI,YAAY,CAAC,KAAK,EAAE;QAAE,OAAO,iBAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACzD,MAAM,cAAc,GAAG,YAAY,CAAC,KAAK,CAAC;IAC1C,MAAM,MAAM,GAAG,MAAM;QACnB,CAAC,CAAC,MAAM,SAAS,CAAC,gBAAgB,CAAC,GAAG,EAAE,mBAAW,EAAE,CAAC,CAAC,CAAC,MAAO,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;QACxF,CAAC,CAAC,gBAAE,CAAC,EAAE,CAAC,CAAC;IACX,IAAI,MAAM,CAAC,KAAK,EAAE;QAAE,OAAO,iBAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,SAAS,GAAe,MAAM,CAAC,KAAK,CAAC;IAC3C,MAAM,aAAa,GAAmB,EAAE,CAAC;IACzC,MAAM,UAAU,GAAW,MAAA,MAAA,cAAc,CAAC,CAAC,CAAC,0CAAE,UAAU,mCAAI,EAAE,CAAC;IAC/D,MAAM,QAAQ,GAAW,MAAA,MAAA,SAAS,CAAC,CAAC,CAAC,0CAAE,UAAU,mCAAI,EAAE,CAAC;IACxD,MAAM,cAAc,GAAG,mBAAW,EAAE;QAClC,CAAC,CAAC,IAAI,CAAC,QAAQ;QACf,CAAC,CAAC,MAAA,OAAQ,CAAC,KAAK,CAAC,0BAAc,CAAC,WAAW,CAAC,0CAAE,QAAQ,CAAC;IAEzD,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;QAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,KAAK,aAAa,CAAC,YAAY,CAAC,CAAC;QAE9F,aAAa,CAAC,IAAI,CAAC;YACjB,2FAA2F;YAC3F,iBAAiB,EACf,MAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,iBAAiB,mCAC3B,aAAa,CAAC,iBAAiB,mCAC/B,aAAa,CAAC,YAAY;YAC5B,YAAY,EAAE,aAAa,CAAC,YAAY;YACxC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YACnC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;YACzD,kBAAkB,EAAE,aAAa,CAAC,UAAU;SAC7C,CAAC,CAAC;KACJ;IAED,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,GAAG,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,MAAM,EAAE;QAC3E,MAAM,OAAO,GAAG;YACd;gBACE,OAAO,EAAE,kCAAkB,CAAC,0CAA0C,CAAC;gBACvE,KAAK,EAAE,oBAAM,CAAC,YAAY;aAC3B;YACD;gBACE,OAAO,EAAE,kCAAkB,CAAC,uCAAuC,CAAC;gBACpE,KAAK,EAAE,oBAAM,CAAC,YAAY;aAC3B;YACD,EAAE,OAAO,EAAE,IAAI,CAAC,iBAAiB,GAAG,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;YACxE,GAAG,kBAAkB,CACnB,mBAAW,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,OAAQ,CAAC,OAAO,EAC7C,kCAAkB,CAAC,kDAAkD,CAAC,CACvE;YACD,EAAE,OAAO,EAAE,kCAAkB,CAAC,6BAA6B,CAAC,EAAE,KAAK,EAAE,oBAAM,CAAC,YAAY,EAAE;YAC1F,EAAE,OAAO,EAAE,cAAc,GAAG,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;YAChE;gBACE,OAAO,EAAE,kCAAkB,CAAC,mCAAmC,CAAC;gBAChE,KAAK,EAAE,oBAAM,CAAC,YAAY;aAC3B;YACD,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;SACtD,CAAC;QAEF,IAAI,MAAM,EAAE;YACV,OAAO,CAAC,IAAI,CACV;gBACE,OAAO,EAAE,kCAAkB,CAAC,gCAAgC,CAAC;gBAC7D,KAAK,EAAE,oBAAM,CAAC,YAAY;aAC3B,EACD,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE,EACnD,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAM,CAAC,YAAY,EAAE,CAC/C,CAAC;SACH;aAAM;YACL,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAM,CAAC,YAAY,EAAE,CAAC,CAAC;SAC9D;QAED,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACxC,OAAO,CAAC,IAAI,CACV;gBACE,OAAO,EAAE,kCAAkB,CAAC,kCAAkC,CAAC;gBAC/D,KAAK,EAAE,oBAAM,CAAC,YAAY;aAC3B,EACD,EAAE,OAAO,EAAE,YAAY,CAAC,iBAAiB,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE,EACzE,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,YAAY,EAAE,CAC9C,CAAC;YAEF,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;gBACtC,OAAO,CAAC,IAAI,CAAC;oBACX,OAAO,EAAE,kCAAkB,CAAC,wCAAwC,CAAC;oBACrE,KAAK,EAAE,oBAAM,CAAC,aAAa;iBAC5B,CAAC,CAAC;aACJ;YAED,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,YAAY,EAAE,CAAC,CAAC;SAC7D;QAED,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,GAAG,EAAE;YACpC,GAAG,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SACzD;aAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,MAAM,EAAE;YAC9C,GAAG,CAAC,eAAe,CAAC,WAAW,CAC7B,MAAM,EACN,kCAAkB,CAChB,6CAA6C,EAC7C,MAAM,CAAC,CAAC,CAAC,kCAAkB,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC,EAAE,EACjE,kCAAsB,CAAC,iBAAiB,CACzC,EACD,KAAK,CACN,CAAC;YACF,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC/B;KACF;IACD,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CACxC,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,aAAa,IAAI,YAAY,CAAC,UAAU,CACxE,CAAC,MAAM,CAAC;IACT,IAAI,cAAc,EAAE;QAClB,cAAc,CAAC,qCAAyB,CAAC,GAAG,CAAC,GAAG,mBAAW,EAAE;YAC3D,CAAC,CAAC,MAAM,CAAC,GAAG;gBACV,CAAC,CAAC,oBAAY,CAAC,MAAM,CAAC,GAAG,CAAC;gBAC1B,CAAC,CAAC,SAAS;YACb,CAAC,CAAC,oBAAY,CAAC,OAAQ,CAAC,OAAO,CAAC,CAAC;QACnC,cAAc,CAAC,qCAAyB,CAAC,iBAAiB,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC9F,cAAc,CAAC,qCAAyB,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;KACpF;IACD,OAAO,gBAAE,CAAC;QACR,aAAa,EAAE,aAAa;QAC5B,KAAK,EAAE,wCAAkB,CAAC,EAAE;KAC7B,CAAC,CAAC;AACL,CAAC;AApKD,4CAoKC;AAED,SAAS,4BAA4B,CACnC,OAAqB,EACrB,IAAa;;IAEb,MAAM,WAAW,GACf,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,wCAA4B,CAAC,KAAK,MAAM;QAC/D,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,wCAA4B,CAAC,KAAK,IAAI,CAAC;IAChE,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,UAAU,GAAG,kCAAkB,CAAC,mCAAmC,CAAC,CAAC;QAC3E,OAAO;YACL,KAAK,EAAE,wCAAkB,CAAC,cAAc;YACxC,OAAO,EAAE,UAAU;SACpB,CAAC;KACH;IAED,MAAM,cAAc,GAAG,MAAA,OAAO,CAAC,KAAK,CAAC,0BAAc,CAAC,WAAW,CAAC,0CAAE,QAAQ,CAAC;IAC3E,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ,IAAK,cAAyB,EAAE;QAClE,MAAM,UAAU,GAAG,kCAAkB,CAAC,mCAAmC,CAAC,CAAC;QAC3E,OAAO;YACL,KAAK,EAAE,wCAAkB,CAAC,kBAAkB;YAC5C,OAAO,EAAE,UAAU;SACpB,CAAC;KACH;IAED,OAAO;QACL,KAAK,EAAE,wCAAkB,CAAC,EAAE;KAC7B,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,eAAe,CACnC,GAAc,EACd,MAAgC,EAChC,OAAiC,EACjC,aAA4B,EAC5B,cAAqB;;IAErB,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,kBAAkB,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;IAC3F,IAAI,MAAM,CAAC,KAAK,EAAE,EAAE;QAClB,OAAO,iBAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC1B;IAED,IAAI,CAAC,mBAAW,EAAE,EAAE;QAClB,MAAM,WAAW,GAAG,4BAA4B,CAAC,OAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QAEzE,IAAI,WAAW,CAAC,KAAK,IAAI,wCAAkB,CAAC,EAAE,EAAE;YAC9C,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE;gBAC3D,GAAG,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACrE;YACD,OAAO,gBAAE,CAAC;gBACR,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B,CAAC,CAAC;SACJ;KACF;IACD,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAgB,CAAC;IAEzC,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,GAAG,EAAE;QACpC,gCAAgC;QAChC,MAAM,cAAc,GAAG,mBAAW,EAAE;YAClC,CAAC,CAAC,QAAQ,CAAC,QAAQ;YACnB,CAAC,CAAC,MAAA,OAAQ,CAAC,KAAK,CAAC,0BAAc,CAAC,WAAW,CAAC,0CAAE,QAAQ,CAAC;QACzD,MAAM,OAAO,GAAG;YACd;gBACE,OAAO,EAAE,kCAAkB,CAAC,uCAAuC,CAAC;gBACpE,KAAK,EAAE,oBAAM,CAAC,YAAY;aAC3B;YACD,EAAE,OAAO,EAAE,QAAQ,CAAC,iBAAiB,GAAG,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;YAC5E,GAAG,kBAAkB,CACnB,mBAAW,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,OAAQ,CAAC,OAAO,EAC7C,kCAAkB,CAAC,2CAA2C,CAAC,CAChE;YACD,EAAE,OAAO,EAAE,kCAAkB,CAAC,6BAA6B,CAAC,EAAE,KAAK,EAAE,oBAAM,CAAC,YAAY,EAAE;YAC1F,EAAE,OAAO,EAAE,cAAc,GAAG,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;SACjE,CAAC;QACF,GAAG,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;KACzD;IAED,IAAI,MAAc,CAAC;IACnB,IAAI,mBAAW,EAAE,EAAE;QACjB,MAAM,eAAe,GAAG,MAAM,iBAAiB,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QACpF,IAAI,eAAe,CAAC,KAAK,EAAE,EAAE;YAC3B,OAAO,iBAAG,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SACnC;QACD,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC;KAChC;IAED,MAAM,SAAS,GAAG,kBAAS,CAAC,GAAG,CAAc,0BAAc,CAAC,WAAW,CAAC,CAAC;IACzE,MAAM,SAAS,GAAG,kBAAS,CAAC,GAAG,CAAS,0BAAc,CAAC,MAAM,CAAC,CAAC;IAC/D,MAAM,YAAY,GAAG,MAAM,SAAS,CAAC,eAAe,CAClD,GAAG,EACH,MAAM,EACN,OAAO,EACP,aAAa,CAAC,iBAAiB,EAC/B,QAAQ,EACR,mBAAW,EAAE,CAAC,CAAC,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAC/C,CAAC;IACF,IAAI,YAAY,CAAC,KAAK,EAAE,EAAE;QACxB,OAAO,iBAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;KAChC;IACD,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC;IACvC,MAAM,cAAc,GAAG,mBAAW,EAAE;QAClC,CAAC,CAAC,MAAO,CAAC,WAAW,IAAI,SAAS;QAClC,CAAC,CAAC,gCAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC/B,IAAI,cAAc,EAAE;QAClB,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,eAAe,CAC5C,GAAG,EACH,MAAM,CAAC,KAAK,EACZ,mBAAW,EAAE,CAAC,CAAC,CAAC,MAAO,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAChD,CAAC;QACF,IAAI,MAAM,CAAC,KAAK,EAAE;YAAE,OAAO,iBAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAqB,EAAE,EAAE;YAC7C,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC;KACJ;IACD,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,GAAG,EAAE;QACpC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;YACpC,MAAM,OAAO,GAAG;gBACd;oBACE,OAAO,EAAE,kCAAkB,CAAC,8CAA8C,CAAC;oBAC3E,KAAK,EAAE,oBAAM,CAAC,YAAY;iBAC3B;gBACD;oBACE,OAAO,EAAE,MAAA,UAAU,CAAC,UAAU,mCAAI,kCAAkB,CAAC,8BAA8B,CAAC;oBACpF,KAAK,EAAE,oBAAM,CAAC,cAAc;iBAC7B;gBACD;oBACE,OAAO,EAAE,kCAAkB,CAAC,iCAAiC,CAAC;oBAC9D,KAAK,EAAE,oBAAM,CAAC,YAAY;iBAC3B;gBACD,EAAE,OAAO,EAAE,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;gBAC1D;oBACE,OAAO,EAAE,kCAAkB,CAAC,+BAA+B,CAAC;oBAC5D,KAAK,EAAE,oBAAM,CAAC,YAAY;iBAC3B;gBACD;oBACE,OAAO,EAAE,UAAU,CAAC,KAAK;wBACvB,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,EAAE;wBAC7B,CAAC,CAAC,kCAAkB,CAAC,8BAA8B,CAAC,GAAG,IAAI;oBAC7D,KAAK,EAAE,oBAAM,CAAC,cAAc;iBAC7B;aACF,CAAC;YACF,GAAG,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SACzD;KACF;IACD,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,KAAK,cAAc,CAAC,CAAC;IAC3F,MAAM,kBAAkB,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;IAC7F,IAAI,cAAc,EAAE;QAClB,cAAc,CAAC,qCAAyB,CAAC,aAAa,CAAC,GAAG,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,KAAK;YAC5E,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;YAC/B,CAAC,CAAC,kCAAkB,CAAC,8BAA8B,CAAC,CAAC;QACvD,cAAc,CAAC,qCAAyB,CAAC,kBAAkB,CAAC,GAAG,CAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,KAAK;YACtF,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;YACpC,CAAC,CAAC,kCAAkB,CAAC,8BAA8B,CAAC,CAAC;KACxD;IACD,OAAO,gBAAE,CAAC;QACR,KAAK,EAAE,wCAAkB,CAAC,EAAE;QAC5B,WAAW;KACZ,CAAC,CAAC;AACL,CAAC;AAjID,0CAiIC;AAEM,KAAK,UAAU,eAAe,CACnC,GAAc,EACd,MAAgC,EAChC,OAAiC,EACjC,aAA4B,EAC5B,cAAqB;;IAErB,MAAM,WAAW,GAAG,GAAG,CAAC,eAAe,CAAC,iBAAiB,CACvD,kCAAkB,CAAC,uCAAuC,CAAC,EAC3D,CAAC,CACF,CAAC;IACF,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,kBAAkB,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QAC3F,IAAI,MAAM,CAAC,KAAK,EAAE,EAAE;YAClB,OAAO,iBAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC1B;QACD,IAAI,CAAC,mBAAW,EAAE,EAAE;YAClB,MAAM,WAAW,GAAG,4BAA4B,CAAC,OAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YACzE,IAAI,WAAW,CAAC,KAAK,IAAI,wCAAkB,CAAC,EAAE,EAAE;gBAC9C,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE;oBAC3D,GAAG,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;iBACrE;qBAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,MAAM,IAAI,WAAW,CAAC,OAAO,EAAE;oBACrE,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;iBAC9C;gBACD,OAAO,gBAAE,CAAC;oBACR,KAAK,EAAE,WAAW,CAAC,KAAK;oBACxB,OAAO,EAAE,WAAW,CAAC,OAAO;iBAC7B,CAAC,CAAC;aACJ;SACF;QACD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE;YACtD,OAAO,iBAAG,CACR,IAAI,uBAAS,CACX,kBAAU,EACV,yBAAa,CAAC,wBAAwB,EACtC,gCAAgB,CAAC,6CAA6C,CAAC,EAC/D,kCAAkB,CAAC,6CAA6C,CAAC,CAClE,CACF,CAAC;SACH;QAED,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,WAAW,CAAC,aAAa,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAE7F,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,iBAAG,CACR,IAAI,uBAAS,CACX,kBAAU,EACV,yBAAa,CAAC,6BAA6B,EAC3C,gCAAgB,CAAC,kDAAkD,CAAC,EACpE,kCAAkB,CAAC,kDAAkD,CAAC,CACvE,CACF,CAAC;SACH;QAED,MAAM,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK,EAAE,CAAA,CAAC;QAC3B,MAAM,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,CAAC,kCAAkB,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC,CAAA,CAAC;QAEhG,IAAI,MAAc,CAAC;QACnB,IAAI,mBAAW,EAAE,EAAE;YACjB,MAAM,eAAe,GAAG,MAAM,iBAAiB,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;YACpF,IAAI,eAAe,CAAC,KAAK,EAAE,EAAE;gBAC3B,OAAO,iBAAG,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;aACnC;YACD,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC;SAChC;QAED,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,GAAG,EAAE;YACpC,gCAAgC;YAChC,MAAM,cAAc,GAAG,mBAAW,EAAE;gBAClC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;gBACvB,CAAC,CAAC,MAAA,OAAQ,CAAC,KAAK,CAAC,0BAAc,CAAC,WAAW,CAAC,0CAAE,QAAQ,CAAC;YACzD,MAAM,OAAO,GAAG;gBACd;oBACE,OAAO,EAAE,kCAAkB,CAAC,6CAA6C,CAAC;oBAC1E,KAAK,EAAE,oBAAM,CAAC,YAAY;iBAC3B;gBACD,EAAE,OAAO,EAAE,QAAQ,CAAC,iBAAiB,GAAG,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;gBAC5E,GAAG,kBAAkB,CACnB,mBAAW,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,OAAQ,CAAC,OAAO,EAC7C,kCAAkB,CAAC,4CAA4C,CAAC,CACjE;gBACD,EAAE,OAAO,EAAE,kCAAkB,CAAC,6BAA6B,CAAC,EAAE,KAAK,EAAE,oBAAM,CAAC,YAAY,EAAE;gBAC1F,EAAE,OAAO,EAAE,cAAc,GAAG,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;aACjE,CAAC;YAEF,GAAG,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;SACzD;QACD,MAAM,cAAc,GAAG,mBAAW,EAAE;YAClC,CAAC,CAAC,MAAO,CAAC,WAAW,IAAI,SAAS;YAClC,CAAC,CAAC,gCAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC/B,MAAM,SAAS,GAAG,kBAAS,CAAC,GAAG,CAAc,0BAAc,CAAC,WAAW,CAAC,CAAC;QACzE,MAAM,SAAS,GAAG,kBAAS,CAAC,GAAG,CAAS,0BAAc,CAAC,MAAM,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,MAAM,SAAS,CAAC,eAAe,CAClD,GAAG,EACH,MAAM,EACN,OAAO,EACP,aAAa,CAAC,iBAAiB,EAC/B,QAAQ,EACR,mBAAW,EAAE,CAAC,CAAC,CAAC,MAAO,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAC/C,CAAC;QACF,IAAI,YAAY,CAAC,KAAK,EAAE,EAAE;YACxB,OAAO,iBAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SAChC;QACD,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC;QACvC,IAAI,cAAc,EAAE;YAClB,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,eAAe,CAC5C,GAAG,EACH,QAAQ,EACR,mBAAW,EAAE,CAAC,CAAC,CAAC,MAAO,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAChD,CAAC;YACF,IAAI,MAAM,CAAC,KAAK,EAAE;gBAAE,OAAO,iBAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAqB,EAAE,EAAE;gBAC7C,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;SACJ;QACD,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,GAAG,EAAE;YACpC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;gBACpC,MAAM,OAAO,GAAG;oBACd,EAAE,OAAO,EAAE,GAAG,MAAA,UAAU,CAAC,KAAK,0CAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;oBAC5E;wBACE,OAAO,EAAE,kCAAkB,CAAC,6CAA6C,CAAC;wBAC1E,KAAK,EAAE,oBAAM,CAAC,YAAY;qBAC3B;oBACD,EAAE,OAAO,EAAE,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;oBAC1D;wBACE,OAAO,EAAE,kCAAkB,CAAC,8CAA8C,CAAC;wBAC3E,KAAK,EAAE,oBAAM,CAAC,YAAY;qBAC3B;oBACD,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;iBACtE,CAAC;gBACF,GAAG,CAAC,eAAe,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;aACzD;YACD,gCAAgC;YAChC,IAAI,CAAC,mBAAW,EAAE,IAAI,oCAAU,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;gBACpD,GAAG,CAAC,eAAe,CAAC,WAAW,CAC7B,MAAM,EACN,kCAAkB,CAAC,kCAAkC,CAAC;oBACpD,6CAAiC,EACnC,KAAK,CACN,CAAC;aACH;YACD,gCAAgC;YAChC,IAAI,CAAC,mBAAW,EAAE,IAAI,4CAAkB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;gBAC5D,GAAG,CAAC,eAAe,CAAC,WAAW,CAC7B,MAAM,EACN,kCAAkB,CAAC,6BAA6B,CAAC,GAAG,wCAA4B,EAChF,KAAK,CACN,CAAC;aACH;SACF;QACD,OAAO,gBAAE,CAAC;YACR,KAAK,EAAE,wCAAkB,CAAC,EAAE;YAC5B,QAAQ,EAAE,QAAQ;YAClB,WAAW;SACZ,CAAC,CAAC;KACJ;YAAS;QACR,MAAM,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,IAAI,CAAC,CAAA,CAAC;KAC9B;AACH,CAAC;AA/JD,0CA+JC;AAEM,KAAK,UAAU,8BAA8B,CAClD,MAAc;IAEd,MAAM,iBAAiB,GAAG,8BAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACrE,IAAI,iBAAiB,EAAE;QACrB,MAAM,aAAa,GAAG,MAAM,kBAAK,CAAC,aAAa,CAAC,iBAAiB,CAAC,aAAa,CAAC;YAC9E,MAAM,EAAE,uBAAe;SACxB,CAAC,CAAC;QACH,IAAI,aAAa,CAAC,KAAK,EAAE,EAAE;YACzB,OAAO,iBAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SACjC;QACD,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC;QACvC,OAAO,gBAAE,CAAC,IAAI,uBAAS,CAAC,+BAAoB,CAAE,UAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KACzE;IACD,OAAO,gBAAE,CAAC,SAAS,CAAC,CAAC;AACvB,CAAC;AAfD,wEAeC;AAED,SAAS,kBAAkB,CAAC,GAAuB,EAAE,OAAe;IAClE,OAAO,GAAG;QACR,CAAC,CAAC;YACE;gBACE,OAAO,EAAE,OAAO;gBAChB,KAAK,EAAE,oBAAM,CAAC,YAAY;aAC3B;YACD,EAAE,OAAO,EAAE,GAAG,GAAG,IAAI,EAAE,KAAK,EAAE,oBAAM,CAAC,cAAc,EAAE;SACtD;QACH,CAAC,CAAC,EAAE,CAAC;AACT,CAAC"}