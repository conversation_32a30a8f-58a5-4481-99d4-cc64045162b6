"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalCrypto = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const cryptr_1 = tslib_1.__importDefault(require("cryptr"));
const error_1 = require("./error");
class LocalCrypto {
    constructor(projectId) {
        this.prefix = "crypto_";
        this.cryptr = new cryptr_1.default(projectId + "_teamsfx");
    }
    encrypt(plaintext) {
        return teamsfx_api_1.ok(this.prefix + this.cryptr.encrypt(plaintext));
    }
    decrypt(ciphertext) {
        if (!ciphertext.startsWith(this.prefix)) {
            // legacy raw secret string
            return teamsfx_api_1.ok(ciphertext);
        }
        try {
            return teamsfx_api_1.ok(this.cryptr.decrypt(ciphertext.substr(this.prefix.length)));
        }
        catch (e) {
            // ciphertext is broken
            return teamsfx_api_1.err(new teamsfx_api_1.SystemError(error_1.CoreSource, "DecryptionError", "Cipher text is broken"));
        }
    }
}
exports.LocalCrypto = LocalCrypto;
//# sourceMappingURL=crypto.js.map