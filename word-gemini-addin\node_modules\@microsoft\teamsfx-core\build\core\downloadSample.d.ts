import { ContextV3, FxError, Inputs, Result } from "@microsoft/teamsfx-api";
import AdmZ<PERSON> from "adm-zip";
import { AxiosResponse } from "axios";
import { CoreHookContext } from "./types";
export declare function fetchCodeZip(url: string, sampleId: string): Promise<Result<AxiosResponse<any> | undefined, FxError>>;
export declare function saveFilesRecursively(zip: AdmZip, appFolder: string, dstPath: string): Promise<void>;
export declare function downloadSampleHook(sampleId: string, sampleAppPath: string): Promise<void>;
export declare function downloadSample(inputs: Inputs, ctx?: CoreHookContext, contextV3?: ContextV3): Promise<Result<string, FxError>>;
//# sourceMappingURL=downloadSample.d.ts.map