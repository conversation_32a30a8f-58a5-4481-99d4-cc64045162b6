"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.downloadSample = exports.downloadSampleHook = exports.saveFilesRecursively = exports.fetchCodeZip = void 0;
const tslib_1 = require("tslib");
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const adm_zip_1 = tslib_1.__importDefault(require("adm-zip"));
const axios_1 = tslib_1.__importDefault(require("axios"));
const fs = tslib_1.__importStar(require("fs-extra"));
const glob_1 = require("glob");
const path = tslib_1.__importStar(require("path"));
const uuid = tslib_1.__importStar(require("uuid"));
const globalVars_1 = require("./globalVars");
const samples_1 = require("../common/samples");
const telemetry_1 = require("../common/telemetry");
const error_1 = require("./error");
const projectSettingsLoader_1 = require("./middleware/projectSettingsLoader");
const question_1 = require("./question");
async function fetchCodeZip(url, sampleId) {
    let retries = 3;
    let result = undefined;
    const error = new error_1.FetchSampleError(sampleId);
    while (retries > 0) {
        retries--;
        try {
            result = await axios_1.default.get(url, {
                responseType: "arraybuffer",
            });
            if (result.status === 200 || result.status === 201) {
                return teamsfx_api_1.ok(result);
            }
        }
        catch (e) {
            await new Promise((resolve) => setTimeout(resolve, 10000));
            if (e.response) {
                error.message += `, status code: ${e.response.status}`;
            }
            else if (e.request) {
                if (e.code === "ENOTFOUND") {
                    error.message += ". Network issue, please check your network connectivity";
                }
                else {
                    error.message += `. Request: ${e.request} failed with error message ${e.message}`;
                }
            }
            else {
                error.message += `. ${e.message}`;
            }
        }
    }
    return teamsfx_api_1.err(error);
}
exports.fetchCodeZip = fetchCodeZip;
async function saveFilesRecursively(zip, appFolder, dstPath) {
    await Promise.all(zip
        .getEntries()
        .filter((entry) => !entry.isDirectory && entry.entryName.includes(`${appFolder}/`))
        .map(async (entry) => {
        const entryPath = entry.entryName.substring(entry.entryName.indexOf(appFolder) + appFolder.length);
        const filePath = path.join(dstPath, entryPath);
        await fs.ensureDir(path.dirname(filePath));
        await fs.writeFile(filePath, entry.getData());
    }));
}
exports.saveFilesRecursively = saveFilesRecursively;
async function downloadSampleHook(sampleId, sampleAppPath) {
    // A temporary solution to avoid duplicate componentId
    if (sampleId === "todo-list-SPFx") {
        const originalId = "c314487b-f51c-474d-823e-a2c3ec82b1ff";
        const componentId = uuid.v4();
        glob_1.glob.glob(`${sampleAppPath}/**/*.json`, { nodir: true, dot: true }, async (err, files) => {
            await Promise.all(files.map(async (file) => {
                let content = (await fs.readFile(file)).toString();
                const reg = new RegExp(originalId, "g");
                content = content.replace(reg, componentId);
                await fs.writeFile(file, content);
            }));
        });
    }
}
exports.downloadSampleHook = downloadSampleHook;
async function downloadSample(inputs, ctx, contextV3) {
    var _a;
    let fxError;
    const progress = globalVars_1.TOOLS.ui.createProgressBar("Fetch sample app", 3);
    await progress.start();
    const telemetryProperties = {
        [telemetry_1.TelemetryProperty.Success]: telemetry_1.TelemetrySuccess.Yes,
        module: "fx-core",
    };
    try {
        const folder = inputs["folder"];
        try {
            await fs.ensureDir(folder);
        }
        catch (e) {
            throw new error_1.ProjectFolderInvalidError(folder);
        }
        const sampleId = inputs[question_1.CoreQuestionNames.Samples];
        if (!(sampleId && folder)) {
            throw error_1.InvalidInputError(`invalid answer for '${question_1.CoreQuestionNames.Samples}'`, inputs);
        }
        telemetryProperties[telemetry_1.TelemetryProperty.SampleAppName] = sampleId;
        const samples = samples_1.sampleProvider.SampleCollection.samples.filter((sample) => sample.id.toLowerCase() === sampleId.toLowerCase());
        if (samples.length === 0) {
            throw error_1.InvalidInputError(`invalid sample id: '${sampleId}'`, inputs);
        }
        const sample = samples[0];
        const url = sample.link;
        let sampleAppPath = path.resolve(folder, sampleId);
        if ((await fs.pathExists(sampleAppPath)) && (await fs.readdir(sampleAppPath)).length > 0) {
            let suffix = 1;
            while (await fs.pathExists(sampleAppPath)) {
                sampleAppPath = `${folder}/${sampleId}_${suffix++}`;
            }
        }
        await progress.next(`Downloading from ${url}`);
        const fetchRes = await fetchCodeZip(url, sample.id);
        if (fetchRes.isErr()) {
            throw fetchRes.error;
        }
        else if (!fetchRes.value) {
            throw new error_1.FetchSampleError(sample.id);
        }
        await progress.next("Unzipping the sample package");
        await saveFilesRecursively(new adm_zip_1.default(fetchRes.value.data), (_a = sample.relativePath) !== null && _a !== void 0 ? _a : sampleId, sampleAppPath);
        await downloadSampleHook(sampleId, sampleAppPath);
        await progress.next("Update project settings");
        const loadInputs = Object.assign(Object.assign({}, inputs), { projectPath: sampleAppPath });
        const projectSettingsRes = await projectSettingsLoader_1.loadProjectSettings(loadInputs, true);
        if (projectSettingsRes.isOk()) {
            const projectSettings = projectSettingsRes.value;
            projectSettings.projectId = inputs.projectId ? inputs.projectId : uuid.v4();
            projectSettings.isFromSample = true;
            inputs.projectId = projectSettings.projectId;
            telemetryProperties[telemetry_1.TelemetryProperty.NewProjectId] = projectSettings.projectId;
            if (ctx)
                ctx.projectSettings = projectSettings;
            if (contextV3)
                contextV3.projectSetting = projectSettings;
            inputs.projectPath = sampleAppPath;
        }
        else {
            telemetryProperties[telemetry_1.TelemetryProperty.NewProjectId] =
                "unknown, failed to set projectId in projectSettings.json";
        }
        await progress.end(true);
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.DownloadSample, telemetryProperties);
        return teamsfx_api_1.ok(sampleAppPath);
    }
    catch (e) {
        fxError = teamsfx_api_1.assembleError(e);
        await progress.end(false);
        telemetryProperties[telemetry_1.TelemetryProperty.Success] = telemetry_1.TelemetrySuccess.No;
        telemetry_1.sendTelemetryErrorEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.DownloadSample, fxError, telemetryProperties);
        return teamsfx_api_1.err(fxError);
    }
}
exports.downloadSample = downloadSample;
//# sourceMappingURL=downloadSample.js.map