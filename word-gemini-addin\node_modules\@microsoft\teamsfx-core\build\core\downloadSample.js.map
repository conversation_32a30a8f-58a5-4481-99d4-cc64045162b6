{"version": 3, "file": "downloadSample.js", "sourceRoot": "", "sources": ["../../src/core/downloadSample.ts"], "names": [], "mappings": ";;;;AAAA,uCAAuC;AACvC,kCAAkC;AAClC,wDAUgC;AAChC,8DAA6B;AAC7B,0DAA6C;AAC7C,qDAA+B;AAC/B,+BAA4B;AAC5B,mDAA6B;AAC7B,mDAA6B;AAC7B,6CAAqC;AACrC,+CAAmD;AACnD,mDAO6B;AAC7B,mCAAyF;AACzF,8EAAyE;AACzE,yCAA+C;AAGxC,KAAK,UAAU,YAAY,CAChC,GAAW,EACX,QAAgB;IAEhB,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,MAAM,GAAG,SAAS,CAAC;IACvB,MAAM,KAAK,GAAG,IAAI,wBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC7C,OAAO,OAAO,GAAG,CAAC,EAAE;QAClB,OAAO,EAAE,CAAC;QACV,IAAI;YACF,MAAM,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC5B,YAAY,EAAE,aAAa;aAC5B,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,KAAK,GAAG,EAAE;gBAClD,OAAO,gBAAE,CAAC,MAAM,CAAC,CAAC;aACnB;SACF;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,OAAO,CAAO,CAAC,OAAmB,EAAgB,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YAC3F,IAAI,CAAC,CAAC,QAAQ,EAAE;gBACd,KAAK,CAAC,OAAO,IAAI,kBAAkB,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;aACxD;iBAAM,IAAI,CAAC,CAAC,OAAO,EAAE;gBACpB,IAAI,CAAC,CAAC,IAAI,KAAK,WAAW,EAAE;oBAC1B,KAAK,CAAC,OAAO,IAAI,yDAAyD,CAAC;iBAC5E;qBAAM;oBACL,KAAK,CAAC,OAAO,IAAI,cAAc,CAAC,CAAC,OAAO,8BAA8B,CAAC,CAAC,OAAO,EAAE,CAAC;iBACnF;aACF;iBAAM;gBACL,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;aACnC;SACF;KACF;IACD,OAAO,iBAAG,CAAC,KAAK,CAAC,CAAC;AACpB,CAAC;AAhCD,oCAgCC;AAEM,KAAK,UAAU,oBAAoB,CACxC,GAAW,EACX,SAAiB,EACjB,OAAe;IAEf,MAAM,OAAO,CAAC,GAAG,CACf,GAAG;SACA,UAAU,EAAE;SACZ,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;SAClF,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QACnB,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,CACzC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,MAAM,CACtD,CAAC;QACF,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC/C,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3C,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IAChD,CAAC,CAAC,CACL,CAAC;AACJ,CAAC;AAlBD,oDAkBC;AAEM,KAAK,UAAU,kBAAkB,CAAC,QAAgB,EAAE,aAAqB;IAC9E,sDAAsD;IACtD,IAAI,QAAQ,KAAK,gBAAgB,EAAE;QACjC,MAAM,UAAU,GAAG,sCAAsC,CAAC;QAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QAC9B,WAAI,CAAC,IAAI,CAAC,GAAG,aAAa,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;YACvF,MAAM,OAAO,CAAC,GAAG,CACf,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;gBACvB,IAAI,OAAO,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACnD,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;gBACxC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;gBAC5C,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACpC,CAAC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;KACJ;AACH,CAAC;AAhBD,gDAgBC;AAEM,KAAK,UAAU,cAAc,CAClC,MAAc,EACd,GAAqB,EACrB,SAAqB;;IAErB,IAAI,OAAO,CAAC;IACZ,MAAM,QAAQ,GAAG,kBAAK,CAAC,EAAE,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;IACnE,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC;IACvB,MAAM,mBAAmB,GAAQ;QAC/B,CAAC,6BAAiB,CAAC,OAAO,CAAC,EAAE,4BAAgB,CAAC,GAAG;QACjD,MAAM,EAAE,SAAS;KAClB,CAAC;IACF,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAW,CAAC;QAC1C,IAAI;YACF,MAAM,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;SAC5B;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,IAAI,iCAAyB,CAAC,MAAM,CAAC,CAAC;SAC7C;QACD,MAAM,QAAQ,GAAG,MAAM,CAAC,4BAAiB,CAAC,OAAO,CAAW,CAAC;QAC7D,IAAI,CAAC,CAAC,QAAQ,IAAI,MAAM,CAAC,EAAE;YACzB,MAAM,yBAAiB,CAAC,uBAAuB,4BAAiB,CAAC,OAAO,GAAG,EAAE,MAAM,CAAC,CAAC;SACtF;QACD,mBAAmB,CAAC,6BAAiB,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC;QAChE,MAAM,OAAO,GAAG,wBAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAC5D,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,CAC/D,CAAC;QACF,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,MAAM,yBAAiB,CAAC,uBAAuB,QAAQ,GAAG,EAAE,MAAM,CAAC,CAAC;SACrE;QACD,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,GAAG,GAAG,MAAM,CAAC,IAAc,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACnD,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;YACxF,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,OAAO,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;gBACzC,aAAa,GAAG,GAAG,MAAM,IAAI,QAAQ,IAAI,MAAM,EAAE,EAAE,CAAC;aACrD;SACF;QACD,MAAM,QAAQ,CAAC,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC,CAAC;QAC/C,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,QAAQ,CAAC,KAAK,EAAE,EAAE;YACpB,MAAM,QAAQ,CAAC,KAAK,CAAC;SACtB;aAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;YAC1B,MAAM,IAAI,wBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;SACvC;QACD,MAAM,QAAQ,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACpD,MAAM,oBAAoB,CACxB,IAAI,iBAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAC/B,MAAA,MAAM,CAAC,YAAY,mCAAI,QAAQ,EAC/B,aAAa,CACd,CAAC;QACF,MAAM,kBAAkB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QAClD,MAAM,QAAQ,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC/C,MAAM,UAAU,mCACX,MAAM,KACT,WAAW,EAAE,aAAa,GAC3B,CAAC;QACF,MAAM,kBAAkB,GAAG,MAAM,2CAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACvE,IAAI,kBAAkB,CAAC,IAAI,EAAE,EAAE;YAC7B,MAAM,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC;YACjD,eAAe,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YAC5E,eAAe,CAAC,YAAY,GAAG,IAAI,CAAC;YACpC,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;YAC7C,mBAAmB,CAAC,6BAAiB,CAAC,YAAY,CAAC,GAAG,eAAe,CAAC,SAAS,CAAC;YAChF,IAAI,GAAG;gBAAE,GAAG,CAAC,eAAe,GAAG,eAAe,CAAC;YAC/C,IAAI,SAAS;gBAAE,SAAS,CAAC,cAAc,GAAG,eAAoC,CAAC;YAC/E,MAAM,CAAC,WAAW,GAAG,aAAa,CAAC;SACpC;aAAM;YACL,mBAAmB,CAAC,6BAAiB,CAAC,YAAY,CAAC;gBACjD,0DAA0D,CAAC;SAC9D;QACD,MAAM,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzB,8BAAkB,CAAC,qBAAS,CAAC,IAAI,EAAE,0BAAc,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;QACvF,OAAO,gBAAE,CAAC,aAAa,CAAC,CAAC;KAC1B;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,GAAG,2BAAa,CAAC,CAAC,CAAC,CAAC;QAC3B,MAAM,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC1B,mBAAmB,CAAC,6BAAiB,CAAC,OAAO,CAAC,GAAG,4BAAgB,CAAC,EAAE,CAAC;QACrE,mCAAuB,CACrB,qBAAS,CAAC,IAAI,EACd,0BAAc,CAAC,cAAc,EAC7B,OAAO,EACP,mBAAmB,CACpB,CAAC;QACF,OAAO,iBAAG,CAAC,OAAO,CAAC,CAAC;KACrB;AACH,CAAC;AAvFD,wCAuFC"}