import { CryptoProvider, EnvConfig, FxError, Result, EnvInfo, Json, v3, Void, InputsWithProjectPath, v2 } from "@microsoft/teamsfx-api";
export interface EnvStateFiles {
    envState: string;
    userDataFile: string;
}
export declare const envPrefix = "$env.";
declare class EnvironmentManager {
    readonly envNameRegex: RegExp;
    readonly envConfigNameRegex: RegExp;
    readonly envStateNameRegex: RegExp;
    readonly schema = "https://aka.ms/teamsfx-env-config-schema";
    readonly envConfigDescription: string;
    private readonly defaultEnvName;
    private readonly ajv;
    private readonly localEnvName;
    constructor();
    loadEnvInfo(projectPath: string, cryptoProvider: CryptoProvider, envName?: string, v3?: boolean): Promise<Result<v3.EnvInfoV3, FxError>>;
    newEnvConfigData(appName: string, existingTabEndpoint?: string): EnvConfig;
    writeEnvConfig(projectPath: string, envConfig: EnvConfig, envName?: string): Promise<Result<string, FxError>>;
    writeEnvState(envData: Map<string, any> | Json, projectPath: string, cryptoProvider: CryptoProvider, envName?: string, isV3?: boolean): Promise<Result<string, FxError>>;
    listAllEnvConfigs(projectPath: string): Promise<Result<Array<string>, FxError>>;
    listRemoteEnvConfigs(projectPath: string, returnErrorIfEmpty?: boolean): Promise<Result<Array<string>, FxError>>;
    checkEnvExist(projectPath: string, env: string): Promise<Result<boolean, FxError>>;
    isEnvConfig(projectPath: string, filePath: string): boolean;
    getDotEnvPath(envName: string, projectPath: string): string;
    getEnvConfigPath(envName: string, projectPath: string): string;
    getEnvStateFilesPath(envName: string, projectPath: string): EnvStateFiles;
    createLocalEnv(projectPath: string, projectAppName?: string): Promise<Result<Void, FxError>>;
    private loadEnvConfig;
    private loadEnvState;
    private expandEnvironmentVariables;
    private getEnvNameFromPath;
    private getConfigFolder;
    private getStatesFolder;
    private getEnvStatesFolder;
    getEnvConfigsFolder(projectPath: string): string;
    private loadUserData;
    private encrypt;
    private decrypt;
    private isEmptyRecord;
    getDefaultEnvName(): string;
    getLocalEnvName(): string;
    resetProvisionState(inputs: InputsWithProjectPath, ctx: v2.Context): Promise<void>;
}
export declare function separateSecretDataV3(envState: any): Record<string, string>;
export declare const environmentManager: EnvironmentManager;
export declare function newEnvInfo(envName?: string, config?: EnvConfig, state?: Map<string, any>): EnvInfo;
export declare function newEnvInfoV3(envName?: string, config?: EnvConfig, state?: v3.ResourceStates): v3.EnvInfoV3;
export {};
//# sourceMappingURL=environment.d.ts.map