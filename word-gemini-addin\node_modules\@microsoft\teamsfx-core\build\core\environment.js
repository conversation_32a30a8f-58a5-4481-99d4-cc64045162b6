"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.newEnvInfoV3 = exports.newEnvInfo = exports.environmentManager = exports.separateSecretDataV3 = exports.envPrefix = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const path_1 = tslib_1.__importStar(require("path"));
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const dotenv = tslib_1.__importStar(require("dotenv"));
const tools_1 = require("../common/tools");
const constants_1 = require("../component/constants");
const telemetry_1 = require("../common/telemetry");
const ajv_1 = tslib_1.__importDefault(require("ajv"));
const draft6MetaSchema = tslib_1.__importStar(require("ajv/dist/refs/json-schema-draft-06.json"));
const envConfigSchema = tslib_1.__importStar(require("@microsoft/teamsfx-api/build/schemas/envConfig.json"));
const constants_2 = require("../common/constants");
const error_1 = require("./error");
const projectSettingsLoader_1 = require("./middleware/projectSettingsLoader");
const utils_1 = require("../component/resource/appManifest/utils/utils");
const typedi_1 = require("typedi");
const lodash_1 = require("lodash");
const migrate_1 = require("../component/migrate");
const crypto_1 = require("./crypto");
const envUtil_1 = require("../component/utils/envUtil");
const localizeUtils_1 = require("../common/localizeUtils");
exports.envPrefix = "$env.";
class EnvironmentManager {
    constructor() {
        this.envNameRegex = /^[\w\d-_]+$/;
        this.envConfigNameRegex = /^config\.(?<envName>[\w\d-_]+)\.json$/i;
        this.envStateNameRegex = /^state\.(?<envName>[\w\d-_]+)\.json$/i;
        this.schema = "https://aka.ms/teamsfx-env-config-schema";
        this.envConfigDescription = `You can customize the TeamsFx config for different environments.` +
            ` Visit https://aka.ms/teamsfx-env-config to learn more about this.`;
        this.defaultEnvName = "dev";
        this.localEnvName = "local";
        this.ajv = new ajv_1.default();
        this.ajv.addMetaSchema(draft6MetaSchema);
    }
    async loadEnvInfo(projectPath, cryptoProvider, envName, v3 = false) {
        if (!(await fs_extra_1.default.pathExists(projectPath))) {
            return teamsfx_api_1.err(new error_1.PathNotExistError(projectPath));
        }
        envName = envName !== null && envName !== void 0 ? envName : this.getDefaultEnvName();
        const configResult = await this.loadEnvConfig(projectPath, envName);
        if (configResult.isErr()) {
            return teamsfx_api_1.err(configResult.error);
        }
        const stateResult = await this.loadEnvState(projectPath, envName, cryptoProvider);
        if (stateResult.isErr()) {
            return teamsfx_api_1.err(stateResult.error);
        }
        return teamsfx_api_1.ok({
            envName,
            config: configResult.value,
            state: stateResult.value,
        });
    }
    newEnvConfigData(appName, existingTabEndpoint) {
        const envConfig = {
            $schema: this.schema,
            description: this.envConfigDescription,
            manifest: {
                appName: {
                    short: appName,
                    full: `Full name for ${appName}`,
                },
                description: {
                    short: `Short description of ${appName}`,
                    full: `Full description of ${appName}`,
                },
                icons: {
                    color: "resources/color.png",
                    outline: "resources/outline.png",
                },
            },
        };
        if (existingTabEndpoint) {
            // Settings to build a static Tab app from existing app.
            envConfig.manifest[constants_2.ManifestVariables.TabContentUrl] = existingTabEndpoint;
            envConfig.manifest[constants_2.ManifestVariables.TabWebsiteUrl] = existingTabEndpoint;
        }
        return envConfig;
    }
    async writeEnvConfig(projectPath, envConfig, envName) {
        if (!(await fs_extra_1.default.pathExists(projectPath))) {
            return teamsfx_api_1.err(new error_1.PathNotExistError(projectPath));
        }
        const envConfigsFolder = this.getEnvConfigsFolder(projectPath);
        if (!(await fs_extra_1.default.pathExists(envConfigsFolder))) {
            await fs_extra_1.default.ensureDir(envConfigsFolder);
        }
        envName = envName !== null && envName !== void 0 ? envName : this.getDefaultEnvName();
        const envConfigPath = this.getEnvConfigPath(envName, projectPath);
        try {
            await fs_extra_1.default.writeFile(envConfigPath, JSON.stringify(envConfig, null, 4));
        }
        catch (error) {
            return teamsfx_api_1.err(error_1.WriteFileError(error));
        }
        return teamsfx_api_1.ok(envConfigPath);
    }
    async writeEnvState(envData, projectPath, cryptoProvider, envName, isV3) {
        if (!(await fs_extra_1.default.pathExists(projectPath))) {
            return teamsfx_api_1.err(new error_1.PathNotExistError(projectPath));
        }
        const envStatesFolder = this.getEnvStatesFolder(projectPath);
        if (!(await fs_extra_1.default.pathExists(envStatesFolder))) {
            await fs_extra_1.default.ensureDir(envStatesFolder);
        }
        envName = envName !== null && envName !== void 0 ? envName : this.getDefaultEnvName();
        const envFiles = this.getEnvStateFilesPath(envName, projectPath);
        let envState = envData instanceof Map ? tools_1.mapToJson(envData) : envData;
        // v3 envState will be converted into v2 for compatibility
        envState = migrate_1.convertEnvStateV3ToV2(envState);
        const secrets = tools_1.separateSecretData(envState);
        this.encrypt(secrets, cryptoProvider);
        try {
            if (!this.isEmptyRecord(envState)) {
                await fs_extra_1.default.writeFile(envFiles.envState, JSON.stringify(envState, null, 4));
            }
            if (!this.isEmptyRecord(secrets)) {
                await fs_extra_1.default.writeFile(envFiles.userDataFile, tools_1.serializeDict(secrets));
            }
        }
        catch (error) {
            return teamsfx_api_1.err(error_1.WriteFileError(error));
        }
        return teamsfx_api_1.ok(envFiles.envState);
    }
    async listAllEnvConfigs(projectPath) {
        if (!(await fs_extra_1.default.pathExists(projectPath))) {
            return teamsfx_api_1.err(new error_1.PathNotExistError(projectPath));
        }
        if (tools_1.isV3Enabled()) {
            const allEnvsRes = await envUtil_1.envUtil.listEnv(projectPath);
            if (allEnvsRes.isErr())
                return teamsfx_api_1.err(allEnvsRes.error);
            return teamsfx_api_1.ok(allEnvsRes.value);
        }
        const envConfigsFolder = this.getEnvConfigsFolder(projectPath);
        if (!(await fs_extra_1.default.pathExists(envConfigsFolder))) {
            return teamsfx_api_1.ok([]);
        }
        const configFiles = await fs_extra_1.default.readdir(envConfigsFolder);
        const envNames = configFiles
            .map((file) => this.getEnvNameFromPath(file))
            .filter((name) => name !== null);
        return teamsfx_api_1.ok(envNames);
    }
    async listRemoteEnvConfigs(projectPath, returnErrorIfEmpty = false) {
        if (!(await fs_extra_1.default.pathExists(projectPath))) {
            return teamsfx_api_1.err(new error_1.PathNotExistError(projectPath));
        }
        if (tools_1.isV3Enabled()) {
            const allEnvsRes = await envUtil_1.envUtil.listEnv(projectPath);
            if (allEnvsRes.isErr())
                return teamsfx_api_1.err(allEnvsRes.error);
            const remoteEnvs = allEnvsRes.value.filter((env) => env !== this.getLocalEnvName());
            if (remoteEnvs.length === 0 && returnErrorIfEmpty)
                return teamsfx_api_1.err(new teamsfx_api_1.UserError({
                    source: "EnvironmentManager",
                    name: "NoEnvFilesError",
                    displayMessage: localizeUtils_1.getLocalizedString("core.error.NoEnvFilesError"),
                    message: localizeUtils_1.getDefaultString("core.error.NoEnvFilesError"),
                }));
            return teamsfx_api_1.ok(remoteEnvs);
        }
        const envConfigsFolder = this.getEnvConfigsFolder(projectPath);
        const configFiles = !(await fs_extra_1.default.pathExists(envConfigsFolder))
            ? []
            : await fs_extra_1.default.readdir(envConfigsFolder);
        const envNames = configFiles
            .map((file) => this.getEnvNameFromPath(file))
            .filter((name) => name !== null && name !== this.getLocalEnvName());
        if (envNames.length === 0 && returnErrorIfEmpty)
            return teamsfx_api_1.err(new teamsfx_api_1.UserError({
                source: "EnvironmentManager",
                name: "NoEnvFilesError",
                displayMessage: localizeUtils_1.getLocalizedString("core.error.NoEnvFilesError"),
                message: localizeUtils_1.getDefaultString("core.error.NoEnvFilesError"),
            }));
        return teamsfx_api_1.ok(envNames);
    }
    async checkEnvExist(projectPath, env) {
        var _a;
        const envList = await exports.environmentManager.listAllEnvConfigs(projectPath);
        if (envList.isErr()) {
            return teamsfx_api_1.err(envList.error);
        }
        if (((_a = envList.value) === null || _a === void 0 ? void 0 : _a.indexOf(env)) >= 0) {
            return teamsfx_api_1.ok(true);
        }
        else {
            return teamsfx_api_1.ok(false);
        }
    }
    isEnvConfig(projectPath, filePath) {
        const fileName = path_1.default.basename(filePath);
        const fileDirname = path_1.default.dirname(filePath);
        const configFolder = this.getEnvConfigsFolder(projectPath);
        const relativeFilePath = path_1.default.relative(configFolder, fileDirname);
        if (relativeFilePath !== "") {
            return false;
        }
        const match = fileName.match(exports.environmentManager.envConfigNameRegex);
        return match !== null;
    }
    getDotEnvPath(envName, projectPath) {
        return path_1.default.join(projectPath, "teamsfx", `.env.${envName}`);
    }
    getEnvConfigPath(envName, projectPath) {
        const basePath = this.getEnvConfigsFolder(projectPath);
        return path_1.default.resolve(basePath, teamsfx_api_1.EnvConfigFileNameTemplate.replace(teamsfx_api_1.EnvNamePlaceholder, envName));
    }
    getEnvStateFilesPath(envName, projectPath) {
        const basePath = this.getEnvStatesFolder(projectPath);
        const envState = path_1.default.resolve(basePath, teamsfx_api_1.EnvStateFileNameTemplate.replace(teamsfx_api_1.EnvNamePlaceholder, envName));
        const userDataFile = path_1.default.resolve(basePath, `${envName}.userdata`);
        return { envState: envState, userDataFile };
    }
    async createLocalEnv(projectPath, projectAppName) {
        const inputs = {
            projectPath: projectPath,
            platform: teamsfx_api_1.Platform.VSCode,
        };
        const projectSettings = await projectSettingsLoader_1.loadProjectSettings(inputs, true);
        if (projectSettings.isOk()) {
            const appName = utils_1.getLocalAppName(projectAppName !== null && projectAppName !== void 0 ? projectAppName : projectSettings.value.appName);
            const newEnvConfig = exports.environmentManager.newEnvConfigData(appName);
            const res = await exports.environmentManager.writeEnvConfig(inputs.projectPath, newEnvConfig, exports.environmentManager.getLocalEnvName());
            if (res.isErr()) {
                return res;
            }
        }
        else {
            return projectSettings;
        }
        return teamsfx_api_1.ok(teamsfx_api_1.Void);
    }
    async loadEnvConfig(projectPath, envName) {
        const envConfigPath = this.getEnvConfigPath(envName, projectPath);
        if (!(await fs_extra_1.default.pathExists(envConfigPath))) {
            if (envName === this.getLocalEnvName()) {
                await this.createLocalEnv(projectPath);
            }
            if (!(await fs_extra_1.default.pathExists(envConfigPath))) {
                return teamsfx_api_1.err(error_1.ProjectEnvNotExistError(envName));
            }
        }
        const validate = this.ajv.compile(envConfigSchema);
        let data;
        try {
            data = await fs_extra_1.default.readFile(envConfigPath, constants_2.ConstantString.UTF8Encoding);
            // resolve environment variables
            data = this.expandEnvironmentVariables(data);
            data = JSON.parse(data);
        }
        catch (error) {
            return teamsfx_api_1.err(error_1.InvalidEnvConfigError(envName, `Failed to read env config JSON: ${error}`));
        }
        if (validate(data)) {
            return teamsfx_api_1.ok(data);
        }
        return teamsfx_api_1.err(error_1.InvalidEnvConfigError(envName, JSON.stringify(validate.errors)));
    }
    async loadEnvState(projectPath, envName, cryptoProvider) {
        const envFiles = this.getEnvStateFilesPath(envName, projectPath);
        const userDataResult = await this.loadUserData(envFiles.userDataFile, cryptoProvider);
        if (userDataResult.isErr()) {
            return teamsfx_api_1.err(userDataResult.error);
        }
        const userData = userDataResult.value;
        if (!(await fs_extra_1.default.pathExists(envFiles.envState))) {
            return teamsfx_api_1.ok({ solution: {} });
        }
        const template = await fs_extra_1.default.readFile(envFiles.envState, { encoding: "utf-8" });
        const result = tools_1.replaceTemplateWithUserData(template, userData);
        let resultJson = JSON.parse(result);
        resultJson = migrate_1.convertEnvStateV2ToV3(resultJson);
        return teamsfx_api_1.ok(resultJson);
    }
    expandEnvironmentVariables(templateContent) {
        if (!templateContent) {
            return templateContent;
        }
        return tools_1.compileHandlebarsTemplateString(templateContent, { $env: process.env });
    }
    getEnvNameFromPath(filePath) {
        const match = this.envConfigNameRegex.exec(filePath);
        if (match != null && match.groups != null) {
            return match.groups.envName;
        }
        return null;
    }
    getConfigFolder(projectPath) {
        return path_1.default.resolve(projectPath, `.${teamsfx_api_1.ConfigFolderName}`);
    }
    getStatesFolder(projectPath) {
        return path_1.default.resolve(this.getConfigFolder(projectPath), teamsfx_api_1.StatesFolderName);
    }
    getEnvStatesFolder(projectPath) {
        return this.getStatesFolder(projectPath);
    }
    getEnvConfigsFolder(projectPath) {
        return path_1.default.resolve(this.getConfigFolder(projectPath), teamsfx_api_1.InputConfigsFolderName);
    }
    async loadUserData(userDataPath, cryptoProvider) {
        if (!(await fs_extra_1.default.pathExists(userDataPath))) {
            return teamsfx_api_1.ok({});
        }
        const content = await fs_extra_1.default.readFile(userDataPath, "UTF-8");
        const secrets = dotenv.parse(content);
        const res = this.decrypt(secrets, cryptoProvider);
        if (res.isErr()) {
            const fxError = res.error;
            const fileName = path_1.basename(userDataPath);
            fxError.message = `Project update failed because of ${fxError.name}(file:${fileName}):${fxError.message}, if your local file '*.userdata' is not modified, please report to us by click 'Report Issue' button.`;
            fxError.userData = `file: ${fileName}\n------------FILE START--------\n${content}\n------------FILE END----------`;
            telemetry_1.sendTelemetryErrorEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.DecryptUserdata, fxError);
        }
        return res;
    }
    encrypt(secrets, cryptoProvider) {
        for (const secretKey of Object.keys(secrets)) {
            if (!tools_1.dataNeedEncryption(secretKey)) {
                continue;
            }
            if (!secrets[secretKey]) {
                delete secrets[secretKey];
                continue;
            }
            const encryptedSecret = cryptoProvider.encrypt(secrets[secretKey]);
            // always success
            if (encryptedSecret.isOk()) {
                secrets[secretKey] = encryptedSecret.value;
            }
        }
        return teamsfx_api_1.ok(secrets);
    }
    decrypt(secrets, cryptoProvider) {
        for (const secretKey of Object.keys(secrets)) {
            if (!tools_1.dataNeedEncryption(secretKey)) {
                continue;
            }
            const secretValue = secrets[secretKey];
            const plaintext = cryptoProvider.decrypt(secretValue);
            if (plaintext.isErr()) {
                return teamsfx_api_1.err(plaintext.error);
            }
            secrets[secretKey] = plaintext.value;
        }
        return teamsfx_api_1.ok(secrets);
    }
    isEmptyRecord(data) {
        return Object.keys(data).length === 0;
    }
    getDefaultEnvName() {
        return this.defaultEnvName;
    }
    getLocalEnvName() {
        return this.localEnvName;
    }
    async resetProvisionState(inputs, ctx) {
        var _a, _b, _c, _d;
        const allEnvRes = await exports.environmentManager.listRemoteEnvConfigs(inputs.projectPath);
        if (allEnvRes.isOk()) {
            for (const env of allEnvRes.value) {
                const loadEnvRes = await this.loadEnvInfo(inputs.projectPath, new crypto_1.LocalCrypto(ctx.projectSetting.projectId), env, true);
                if (loadEnvRes.isOk()) {
                    const envInfo = loadEnvRes.value;
                    if (((_b = (_a = envInfo.state) === null || _a === void 0 ? void 0 : _a.solution) === null || _b === void 0 ? void 0 : _b.provisionSucceeded) === true ||
                        ((_d = (_c = envInfo.state) === null || _c === void 0 ? void 0 : _c.solution) === null || _d === void 0 ? void 0 : _d.provisionSucceeded) === "true") {
                        envInfo.state.solution.provisionSucceeded = false;
                        await exports.environmentManager.writeEnvState(envInfo.state, inputs.projectPath, ctx.cryptoProvider, env, true);
                    }
                }
            }
        }
    }
}
function separateSecretDataV3(envState) {
    const res = {};
    for (const resourceName of Object.keys(envState)) {
        if (resourceName === "solution")
            continue;
        const component = typedi_1.Container.get(resourceName);
        const state = envState[resourceName];
        if (component.secretKeys && component.secretKeys.length > 0) {
            component.secretKeys.forEach((secretKey) => {
                const keyName = component.outputs[secretKey].key;
                const fullKeyName = `${resourceName}.${keyName}`;
                res[keyName] = state[keyName];
                state[secretKey] = `{{${fullKeyName}}}`;
            });
        }
        const outputKeys = component.finalOutputKeys.map((k) => component.outputs[k].key);
        envState[resourceName] = lodash_1.pick(state, outputKeys);
    }
    return res;
}
exports.separateSecretDataV3 = separateSecretDataV3;
exports.environmentManager = new EnvironmentManager();
function newEnvInfo(envName, config, state) {
    return {
        envName: envName !== null && envName !== void 0 ? envName : exports.environmentManager.getDefaultEnvName(),
        config: config !== null && config !== void 0 ? config : {
            manifest: {
                appName: {
                    short: "teamsfx_app",
                },
                description: {
                    short: `Short description of teamsfx_app`,
                    full: `Full description of teamsfx_app`,
                },
                icons: {
                    color: "resources/color.png",
                    outline: "resources/outline.png",
                },
            },
        },
        state: state !== null && state !== void 0 ? state : new Map([[constants_1.GLOBAL_CONFIG, new teamsfx_api_1.ConfigMap()]]),
    };
}
exports.newEnvInfo = newEnvInfo;
function newEnvInfoV3(envName, config, state) {
    return {
        envName: envName !== null && envName !== void 0 ? envName : exports.environmentManager.getDefaultEnvName(),
        config: config !== null && config !== void 0 ? config : {
            manifest: {
                appName: {
                    short: "teamsfx_app",
                },
                description: {
                    short: `Short description of teamsfx_app`,
                    full: `Full description of teamsfx_app`,
                },
                icons: {
                    color: "resources/color.png",
                    outline: "resources/outline.png",
                },
            },
        },
        state: state !== null && state !== void 0 ? state : { solution: {} },
    };
}
exports.newEnvInfoV3 = newEnvInfoV3;
//# sourceMappingURL=environment.js.map