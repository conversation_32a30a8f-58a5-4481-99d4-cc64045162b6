{"version": 3, "file": "environment.js", "sourceRoot": "", "sources": ["../../src/core/environment.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,wDAyBgC;AAChC,qDAAsC;AACtC,gEAA0B;AAC1B,uDAAiC;AACjC,2CAQyB;AACzB,sDAAuD;AACvD,mDAAyF;AACzF,sDAAsB;AACtB,kGAA4E;AAC5E,6GAAuF;AACvF,mDAAwE;AACxE,mCAKiB;AACjB,8EAAyE;AACzE,yEAAgF;AAChF,mCAAmC;AACnC,mCAA8B;AAC9B,kDAAoF;AACpF,qCAAuC;AACvC,wDAAqD;AACrD,2DAA+E;AAOlE,QAAA,SAAS,GAAG,OAAO,CAAC;AAEjC,MAAM,kBAAkB;IActB;QAbgB,iBAAY,GAAG,aAAa,CAAC;QAC7B,uBAAkB,GAAG,wCAAwC,CAAC;QAC9D,sBAAiB,GAAG,uCAAuC,CAAC;QAE5D,WAAM,GAAG,0CAA0C,CAAC;QACpD,yBAAoB,GAClC,kEAAkE;YAClE,oEAAoE,CAAC;QAEtD,mBAAc,GAAG,KAAK,CAAC;QAEvB,iBAAY,GAAG,OAAO,CAAC;QAGtC,IAAI,CAAC,GAAG,GAAG,IAAI,aAAG,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAC3C,CAAC;IAEM,KAAK,CAAC,WAAW,CACtB,WAAmB,EACnB,cAA8B,EAC9B,OAAgB,EAChB,EAAE,GAAG,KAAK;QAEV,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE;YACvC,OAAO,iBAAG,CAAC,IAAI,yBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC;SAChD;QACD,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC9C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACpE,IAAI,YAAY,CAAC,KAAK,EAAE,EAAE;YACxB,OAAO,iBAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SAChC;QACD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QAClF,IAAI,WAAW,CAAC,KAAK,EAAE,EAAE;YACvB,OAAO,iBAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC/B;QACD,OAAO,gBAAE,CAAC;YACR,OAAO;YACP,MAAM,EAAE,YAAY,CAAC,KAAa;YAClC,KAAK,EAAE,WAAW,CAAC,KAA0B;SAC9C,CAAC,CAAC;IACL,CAAC;IAEM,gBAAgB,CAAC,OAAe,EAAE,mBAA4B;QACnE,MAAM,SAAS,GAAc;YAC3B,OAAO,EAAE,IAAI,CAAC,MAAM;YACpB,WAAW,EAAE,IAAI,CAAC,oBAAoB;YACtC,QAAQ,EAAE;gBACR,OAAO,EAAE;oBACP,KAAK,EAAE,OAAO;oBACd,IAAI,EAAE,iBAAiB,OAAO,EAAE;iBACjC;gBACD,WAAW,EAAE;oBACX,KAAK,EAAE,wBAAwB,OAAO,EAAE;oBACxC,IAAI,EAAE,uBAAuB,OAAO,EAAE;iBACvC;gBACD,KAAK,EAAE;oBACL,KAAK,EAAE,qBAAqB;oBAC5B,OAAO,EAAE,uBAAuB;iBACjC;aACF;SACF,CAAC;QAEF,IAAI,mBAAmB,EAAE;YACvB,wDAAwD;YACxD,SAAS,CAAC,QAAQ,CAAC,6BAAiB,CAAC,aAAa,CAAC,GAAG,mBAAmB,CAAC;YAC1E,SAAS,CAAC,QAAQ,CAAC,6BAAiB,CAAC,aAAa,CAAC,GAAG,mBAAmB,CAAC;SAC3E;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,WAAmB,EACnB,SAAoB,EACpB,OAAgB;QAEhB,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE;YACvC,OAAO,iBAAG,CAAC,IAAI,yBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC;SAChD;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC/D,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,EAAE;YAC5C,MAAM,kBAAE,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;SACtC;QAED,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAElE,IAAI;YACF,MAAM,kBAAE,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;SACvE;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,iBAAG,CAAC,sBAAc,CAAC,KAAK,CAAC,CAAC,CAAC;SACnC;QAED,OAAO,gBAAE,CAAC,aAAa,CAAC,CAAC;IAC3B,CAAC;IAEM,KAAK,CAAC,aAAa,CACxB,OAAgC,EAChC,WAAmB,EACnB,cAA8B,EAC9B,OAAgB,EAChB,IAAc;QAEd,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE;YACvC,OAAO,iBAAG,CAAC,IAAI,yBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC;SAChD;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,EAAE;YAC3C,MAAM,kBAAE,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;SACrC;QAED,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAEjE,IAAI,QAAQ,GAAS,OAAO,YAAY,GAAG,CAAC,CAAC,CAAC,iBAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAC3E,0DAA0D;QAC1D,QAAQ,GAAG,+BAAqB,CAAC,QAAQ,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,0BAAkB,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAEtC,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;gBACjC,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;aAC1E;YAED,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;gBAChC,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,EAAE,qBAAa,CAAC,OAAO,CAAC,CAAC,CAAC;aACnE;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,iBAAG,CAAC,sBAAc,CAAC,KAAK,CAAC,CAAC,CAAC;SACnC;QAED,OAAO,gBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,WAAmB;QAChD,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE;YACvC,OAAO,iBAAG,CAAC,IAAI,yBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC;SAChD;QAED,IAAI,mBAAW,EAAE,EAAE;YACjB,MAAM,UAAU,GAAG,MAAM,iBAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,UAAU,CAAC,KAAK,EAAE;gBAAE,OAAO,iBAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACrD,OAAO,gBAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC/D,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,EAAE;YAC5C,OAAO,gBAAE,CAAC,EAAE,CAAC,CAAC;SACf;QAED,MAAM,WAAW,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACvD,MAAM,QAAQ,GAAG,WAAW;aACzB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC5C,MAAM,CAAC,CAAC,IAAI,EAAkB,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAEnD,OAAO,gBAAE,CAAC,QAAQ,CAAC,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,oBAAoB,CAC/B,WAAmB,EACnB,kBAAkB,GAAG,KAAK;QAE1B,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE;YACvC,OAAO,iBAAG,CAAC,IAAI,yBAAiB,CAAC,WAAW,CAAC,CAAC,CAAC;SAChD;QAED,IAAI,mBAAW,EAAE,EAAE;YACjB,MAAM,UAAU,GAAG,MAAM,iBAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,UAAU,CAAC,KAAK,EAAE;gBAAE,OAAO,iBAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACrD,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;YACpF,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,kBAAkB;gBAC/C,OAAO,iBAAG,CACR,IAAI,uBAAS,CAAC;oBACZ,MAAM,EAAE,oBAAoB;oBAC5B,IAAI,EAAE,iBAAiB;oBACvB,cAAc,EAAE,kCAAkB,CAAC,4BAA4B,CAAC;oBAChE,OAAO,EAAE,gCAAgB,CAAC,4BAA4B,CAAC;iBACxD,CAAC,CACH,CAAC;YACJ,OAAO,gBAAE,CAAC,UAAU,CAAC,CAAC;SACvB;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC/D,MAAM,WAAW,GAAG,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC1D,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,MAAM,kBAAE,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACvC,MAAM,QAAQ,GAAG,WAAW;aACzB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;aAC5C,MAAM,CAAC,CAAC,IAAI,EAAkB,EAAE,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QACtF,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,kBAAkB;YAC7C,OAAO,iBAAG,CACR,IAAI,uBAAS,CAAC;gBACZ,MAAM,EAAE,oBAAoB;gBAC5B,IAAI,EAAE,iBAAiB;gBACvB,cAAc,EAAE,kCAAkB,CAAC,4BAA4B,CAAC;gBAChE,OAAO,EAAE,gCAAgB,CAAC,4BAA4B,CAAC;aACxD,CAAC,CACH,CAAC;QACJ,OAAO,gBAAE,CAAC,QAAQ,CAAC,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,WAAmB,EAAE,GAAW;;QACzD,MAAM,OAAO,GAAG,MAAM,0BAAkB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QACxE,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE;YACnB,OAAO,iBAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC3B;QACD,IAAI,CAAA,MAAA,OAAO,CAAC,KAAK,0CAAE,OAAO,CAAC,GAAG,CAAC,KAAI,CAAC,EAAE;YACpC,OAAO,gBAAE,CAAC,IAAI,CAAC,CAAC;SACjB;aAAM;YACL,OAAO,gBAAE,CAAC,KAAK,CAAC,CAAC;SAClB;IACH,CAAC;IAEM,WAAW,CAAC,WAAmB,EAAE,QAAgB;QACtD,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,WAAW,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC3D,MAAM,gBAAgB,GAAG,cAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QAElE,IAAI,gBAAgB,KAAK,EAAE,EAAE;YAC3B,OAAO,KAAK,CAAC;SACd;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,0BAAkB,CAAC,kBAAkB,CAAC,CAAC;QACpE,OAAO,KAAK,KAAK,IAAI,CAAC;IACxB,CAAC;IAEM,aAAa,CAAC,OAAe,EAAE,WAAmB;QACvD,OAAO,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,QAAQ,OAAO,EAAE,CAAC,CAAC;IAC9D,CAAC;IAEM,gBAAgB,CAAC,OAAe,EAAE,WAAmB;QAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QACvD,OAAO,cAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,uCAAyB,CAAC,OAAO,CAAC,gCAAkB,EAAE,OAAO,CAAC,CAAC,CAAC;IAChG,CAAC;IAEM,oBAAoB,CAAC,OAAe,EAAE,WAAmB;QAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;QACtD,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAC3B,QAAQ,EACR,sCAAwB,CAAC,OAAO,CAAC,gCAAkB,EAAE,OAAO,CAAC,CAC9D,CAAC;QACF,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,OAAO,WAAW,CAAC,CAAC;QAEnE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;IAC9C,CAAC;IAEM,KAAK,CAAC,cAAc,CACzB,WAAmB,EACnB,cAAuB;QAEvB,MAAM,MAAM,GAAW;YACrB,WAAW,EAAE,WAAW;YACxB,QAAQ,EAAE,sBAAQ,CAAC,MAAM;SAC1B,CAAC;QACF,MAAM,eAAe,GAAG,MAAM,2CAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAChE,IAAI,eAAe,CAAC,IAAI,EAAE,EAAE;YAC1B,MAAM,OAAO,GAAG,uBAAe,CAAC,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,eAAe,CAAC,KAAK,CAAC,OAAQ,CAAC,CAAC;YAClF,MAAM,YAAY,GAAG,0BAAkB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAClE,MAAM,GAAG,GAAG,MAAM,0BAAkB,CAAC,cAAc,CACjD,MAAM,CAAC,WAAY,EACnB,YAAY,EACZ,0BAAkB,CAAC,eAAe,EAAE,CACrC,CAAC;YACF,IAAI,GAAG,CAAC,KAAK,EAAE,EAAE;gBACf,OAAO,GAAG,CAAC;aACZ;SACF;aAAM;YACL,OAAO,eAAe,CAAC;SACxB;QACD,OAAO,gBAAE,CAAC,kBAAI,CAAC,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,WAAmB,EACnB,OAAe;QAEf,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAClE,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,EAAE;YACzC,IAAI,OAAO,KAAK,IAAI,CAAC,eAAe,EAAE,EAAE;gBACtC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,EAAE;gBACzC,OAAO,iBAAG,CAAC,+BAAuB,CAAC,OAAO,CAAC,CAAC,CAAC;aAC9C;SACF;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAY,eAAe,CAAC,CAAC;QAC9D,IAAI,IAAI,CAAC;QACT,IAAI;YACF,IAAI,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,0BAAc,CAAC,YAAY,CAAC,CAAC;YAErE,gCAAgC;YAChC,IAAI,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,iBAAG,CAAC,6BAAqB,CAAC,OAAO,EAAE,mCAAmC,KAAK,EAAE,CAAC,CAAC,CAAC;SACxF;QAED,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;YAClB,OAAO,gBAAE,CAAC,IAAI,CAAC,CAAC;SACjB;QAED,OAAO,iBAAG,CAAC,6BAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,WAAmB,EACnB,OAAe,EACf,cAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QACjE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QACtF,IAAI,cAAc,CAAC,KAAK,EAAE,EAAE;YAC1B,OAAO,iBAAG,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;SAClC;QACD,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC;QACtC,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE;YAC7C,OAAO,gBAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;SAC7B;QACD,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;QAC7E,MAAM,MAAM,GAAG,mCAA2B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC/D,IAAI,UAAU,GAAS,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC1C,UAAU,GAAG,+BAAqB,CAAC,UAAU,CAAC,CAAC;QAC/C,OAAO,gBAAE,CAAC,UAA+B,CAAC,CAAC;IAC7C,CAAC;IAEO,0BAA0B,CAAC,eAAuB;QACxD,IAAI,CAAC,eAAe,EAAE;YACpB,OAAO,eAAe,CAAC;SACxB;QAED,OAAO,uCAA+B,CAAC,eAAe,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IACjF,CAAC;IAEO,kBAAkB,CAAC,QAAgB;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,EAAE;YACzC,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC;SAC7B;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,eAAe,CAAC,WAAmB;QACzC,OAAO,cAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,8BAAgB,EAAE,CAAC,CAAC;IAC3D,CAAC;IAEO,eAAe,CAAC,WAAmB;QACzC,OAAO,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,8BAAgB,CAAC,CAAC;IAC3E,CAAC;IAEO,kBAAkB,CAAC,WAAmB;QAC5C,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IAC3C,CAAC;IAEM,mBAAmB,CAAC,WAAmB;QAC5C,OAAO,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,oCAAsB,CAAC,CAAC;IACjF,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,YAAoB,EACpB,cAA8B;QAE9B,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,EAAE;YACxC,OAAO,gBAAE,CAAC,EAAE,CAAC,CAAC;SACf;QAED,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QACzD,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAEtC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAClD,IAAI,GAAG,CAAC,KAAK,EAAE,EAAE;YACf,MAAM,OAAO,GAAgB,GAAG,CAAC,KAAK,CAAC;YACvC,MAAM,QAAQ,GAAG,eAAQ,CAAC,YAAY,CAAC,CAAC;YACxC,OAAO,CAAC,OAAO,GAAG,oCAAoC,OAAO,CAAC,IAAI,SAAS,QAAQ,KAAK,OAAO,CAAC,OAAO,wGAAwG,CAAC;YAChN,OAAO,CAAC,QAAQ,GAAG,SAAS,QAAQ,qCAAqC,OAAO,kCAAkC,CAAC;YACnH,mCAAuB,CAAC,qBAAS,CAAC,IAAI,EAAE,0BAAc,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;SAClF;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,OAAO,CACb,OAA+B,EAC/B,cAA8B;QAE9B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAC5C,IAAI,CAAC,0BAAkB,CAAC,SAAS,CAAC,EAAE;gBAClC,SAAS;aACV;YACD,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACvB,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;gBAC1B,SAAS;aACV;YACD,MAAM,eAAe,GAAG,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;YACnE,iBAAiB;YACjB,IAAI,eAAe,CAAC,IAAI,EAAE,EAAE;gBAC1B,OAAO,CAAC,SAAS,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC;aAC5C;SACF;QAED,OAAO,gBAAE,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC;IAEO,OAAO,CACb,OAA+B,EAC/B,cAA8B;QAE9B,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAC5C,IAAI,CAAC,0BAAkB,CAAC,SAAS,CAAC,EAAE;gBAClC,SAAS;aACV;YAED,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,SAAS,CAAC,KAAK,EAAE,EAAE;gBACrB,OAAO,iBAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aAC7B;YAED,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;SACtC;QAED,OAAO,gBAAE,CAAC,OAAO,CAAC,CAAC;IACrB,CAAC;IAEO,aAAa,CAAC,IAAsB;QAC1C,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;IACxC,CAAC;IAEM,iBAAiB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEM,eAAe;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,MAA6B,EAAE,GAAe;;QAC7E,MAAM,SAAS,GAAG,MAAM,0BAAkB,CAAC,oBAAoB,CAAC,MAAM,CAAC,WAAY,CAAC,CAAC;QACrF,IAAI,SAAS,CAAC,IAAI,EAAE,EAAE;YACpB,KAAK,MAAM,GAAG,IAAI,SAAS,CAAC,KAAK,EAAE;gBACjC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CACvC,MAAM,CAAC,WAAW,EAClB,IAAI,oBAAW,CAAC,GAAG,CAAC,cAAc,CAAC,SAAS,CAAC,EAC7C,GAAG,EACH,IAAI,CACL,CAAC;gBACF,IAAI,UAAU,CAAC,IAAI,EAAE,EAAE;oBACrB,MAAM,OAAO,GAAG,UAAU,CAAC,KAAqB,CAAC;oBACjD,IACE,CAAA,MAAA,MAAA,OAAO,CAAC,KAAK,0CAAE,QAAQ,0CAAE,kBAAkB,MAAK,IAAI;wBACpD,CAAA,MAAA,MAAA,OAAO,CAAC,KAAK,0CAAE,QAAQ,0CAAE,kBAAkB,MAAK,MAAM,EACtD;wBACA,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAC;wBAClD,MAAM,0BAAkB,CAAC,aAAa,CACpC,OAAO,CAAC,KAAK,EACb,MAAM,CAAC,WAAW,EAClB,GAAG,CAAC,cAAc,EAClB,GAAG,EACH,IAAI,CACL,CAAC;qBACH;iBACF;aACF;SACF;IACH,CAAC;CACF;AAED,SAAgB,oBAAoB,CAAC,QAAa;IAChD,MAAM,GAAG,GAA2B,EAAE,CAAC;IACvC,KAAK,MAAM,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;QAChD,IAAI,YAAY,KAAK,UAAU;YAAE,SAAS;QAC1C,MAAM,SAAS,GAAG,kBAAS,CAAC,GAAG,CAAgB,YAAY,CAAC,CAAC;QAC7D,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAS,CAAC;QAC7C,IAAI,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3D,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAiB,EAAE,EAAE;gBACjD,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC;gBACjD,MAAM,WAAW,GAAG,GAAG,YAAY,IAAI,OAAO,EAAE,CAAC;gBACjD,GAAG,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC9B,KAAK,CAAC,SAAS,CAAC,GAAG,KAAK,WAAW,IAAI,CAAC;YAC1C,CAAC,CAAC,CAAC;SACJ;QACD,MAAM,UAAU,GAAG,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClF,QAAQ,CAAC,YAAY,CAAC,GAAG,aAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;KAClD;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAlBD,oDAkBC;AAEY,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC;AAE3D,SAAgB,UAAU,CACxB,OAAgB,EAChB,MAAkB,EAClB,KAAwB;IAExB,OAAO;QACL,OAAO,EAAE,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,0BAAkB,CAAC,iBAAiB,EAAE;QAC1D,MAAM,EAAE,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI;YAChB,QAAQ,EAAE;gBACR,OAAO,EAAE;oBACP,KAAK,EAAE,aAAa;iBACrB;gBACD,WAAW,EAAE;oBACX,KAAK,EAAE,kCAAkC;oBACzC,IAAI,EAAE,iCAAiC;iBACxC;gBACD,KAAK,EAAE;oBACL,KAAK,EAAE,qBAAqB;oBAC5B,OAAO,EAAE,uBAAuB;iBACjC;aACF;SACF;QACD,KAAK,EAAE,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,IAAI,GAAG,CAAc,CAAC,CAAC,yBAAa,EAAE,IAAI,uBAAS,EAAE,CAAC,CAAC,CAAC;KACzE,CAAC;AACJ,CAAC;AAxBD,gCAwBC;AAED,SAAgB,YAAY,CAC1B,OAAgB,EAChB,MAAkB,EAClB,KAAyB;IAEzB,OAAO;QACL,OAAO,EAAE,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,0BAAkB,CAAC,iBAAiB,EAAE;QAC1D,MAAM,EAAE,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI;YAChB,QAAQ,EAAE;gBACR,OAAO,EAAE;oBACP,KAAK,EAAE,aAAa;iBACrB;gBACD,WAAW,EAAE;oBACX,KAAK,EAAE,kCAAkC;oBACzC,IAAI,EAAE,iCAAiC;iBACxC;gBACD,KAAK,EAAE;oBACL,KAAK,EAAE,qBAAqB;oBAC5B,OAAO,EAAE,uBAAuB;iBACjC;aACF;SACF;QACD,KAAK,EAAE,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,EAAE,QAAQ,EAAE,EAAE,EAAE;KACjC,CAAC;AACJ,CAAC;AAxBD,oCAwBC"}