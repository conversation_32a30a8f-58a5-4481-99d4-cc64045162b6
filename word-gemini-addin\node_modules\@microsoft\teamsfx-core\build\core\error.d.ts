import { Func, FxError, Inputs, SystemError, UserError, Json, Stage } from "@microsoft/teamsfx-api";
export declare const CoreSource = "Core";
export declare const MigrationSource = "Migration";
export declare class ProjectFolderExistError extends UserError {
    constructor(path: string);
}
export declare class ProjectFolderInvalidError extends UserError {
    constructor(path: string);
}
export declare function WriteFileError(e: Error): SystemError;
export declare function ReadFileError(e: Error): SystemError;
export declare function MigrationError(e: Error, name: string, helpLink?: string): UserError;
export declare function CopyFileError(e: Error): SystemError;
export declare class InitializedFileAlreadyExistError extends UserError {
    constructor(filePath: string);
}
export declare class NoProjectOpenedError extends UserError {
    constructor();
}
export declare class PathNotExistError extends UserError {
    constructor(path: string);
}
export declare class InvalidProjectError extends UserError {
    constructor(msg?: string);
}
export declare class InvalidProjectSettingsFileError extends UserError {
    constructor(msg?: string);
}
export declare class TaskNotSupportError extends SystemError {
    constructor(task: string);
}
export declare class FetchSampleError extends UserError {
    constructor(sampleId: string);
}
export declare function InvalidInputError(reason: string, inputs?: Inputs): UserError;
export declare function FunctionRouterError(func: Func): UserError;
export declare function ContextUpgradeError(error: any, isUserError?: boolean): FxError;
export declare function InvalidStateError(pluginName: string, state: Json): SystemError;
export declare function PluginHasNoTaskImpl(pluginName: string, task: string): SystemError;
export declare function ProjectSettingsUndefinedError(): SystemError;
export declare function MultipleEnvNotEnabledError(): SystemError;
export declare function ProjectEnvNotExistError(env: string): UserError;
export declare function InvalidEnvNameError(): UserError;
export declare function ProjectEnvAlreadyExistError(env: string): FxError;
export declare function InvalidEnvConfigError(env: string, errorMsg: string): UserError;
export declare function NonExistEnvNameError(env: string): UserError;
export declare function ModifiedSecretError(): UserError;
export declare class LoadSolutionError extends SystemError {
    constructor();
}
export declare class NotImplementedError extends SystemError {
    constructor(method: string);
}
export declare class ObjectIsUndefinedError extends SystemError {
    constructor(name: string);
}
export declare function SolutionConfigError(): UserError;
export declare function ProjectSettingError(): UserError;
export declare function UpgradeCanceledError(): UserError;
export declare function UpgradeV3CanceledError(link: string, version: string): UserError;
export declare function ToolkitNotSupportError(): UserError;
export declare function IncompatibleProjectError(messageKey: string): UserError;
export declare function AbandonedProjectError(): UserError;
export declare function ConsolidateCanceledError(): UserError;
export declare function NotJsonError(err: Error): UserError;
export declare function FailedToParseResourceIdError(name: string, resourceId: string): UserError;
export declare function SPFxConfigError(file: string): UserError;
export declare function NpmInstallError(path: string, e: Error): SystemError;
export declare function LoadPluginError(): SystemError;
export declare class OperationNotPermittedError extends UserError {
    constructor(operation: string);
}
export declare class NoCapabilityFoundError extends UserError {
    constructor(operation: Stage);
}
export declare class NoAadManifestExistError extends UserError {
    constructor(filePath: string);
}
export declare class VideoFilterAppRemoteNotSupportedError extends UserError {
    constructor();
}
//# sourceMappingURL=error.d.ts.map