{"version": 3, "file": "error.d.ts", "sourceRoot": "", "sources": ["../../src/core/error.ts"], "names": [], "mappings": "AAIA,OAAO,EAEL,IAAI,EACJ,OAAO,EACP,MAAM,EACN,WAAW,EACX,SAAS,EACT,IAAI,EAGJ,KAAK,EACN,MAAM,wBAAwB,CAAC;AAIhC,eAAO,MAAM,UAAU,SAAS,CAAC;AACjC,eAAO,MAAM,eAAe,cAAc,CAAC;AAE3C,qBAAa,uBAAwB,SAAQ,SAAS;gBACxC,IAAI,EAAE,MAAM;CAOzB;AAED,qBAAa,yBAA0B,SAAQ,SAAS;gBAC1C,IAAI,EAAE,MAAM;CAOzB;AAED,wBAAgB,cAAc,CAAC,CAAC,EAAE,KAAK,GAAG,WAAW,CAMpD;AAED,wBAAgB,aAAa,CAAC,CAAC,EAAE,KAAK,GAAG,WAAW,CAMnD;AAED,wBAAgB,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAQnF;AAED,wBAAgB,aAAa,CAAC,CAAC,EAAE,KAAK,GAAG,WAAW,CAMnD;AAED,qBAAa,gCAAiC,SAAQ,SAAS;gBACjD,QAAQ,EAAE,MAAM;CAO7B;AAED,qBAAa,oBAAqB,SAAQ,SAAS;;CAQlD;AAED,qBAAa,iBAAkB,SAAQ,SAAS;gBAClC,IAAI,EAAE,MAAM;CAOzB;AAED,qBAAa,mBAAoB,SAAQ,SAAS;gBACpC,GAAG,CAAC,EAAE,MAAM;CAOzB;AAED,qBAAa,+BAAgC,SAAQ,SAAS;gBAChD,GAAG,CAAC,EAAE,MAAM;CAOzB;AAED,qBAAa,mBAAoB,SAAQ,WAAW;gBACtC,IAAI,EAAE,MAAM;CAOzB;AAED,qBAAa,gBAAiB,SAAQ,SAAS;gBACjC,QAAQ,EAAE,MAAM;CAO7B;AAED,wBAAgB,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAQ5E;AAED,wBAAgB,mBAAmB,CAAC,IAAI,EAAE,IAAI,GAAG,SAAS,CAQzD;AAED,wBAAgB,mBAAmB,CAAC,KAAK,EAAE,GAAG,EAAE,WAAW,UAAQ,GAAG,OAAO,CAgB5E;AAED,wBAAgB,iBAAiB,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,GAAG,WAAW,CAO9E;AAED,wBAAgB,mBAAmB,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,WAAW,CAOjF;AAED,wBAAgB,6BAA6B,IAAI,WAAW,CAO3D;AAED,wBAAgB,0BAA0B,IAAI,WAAW,CAOxD;AAED,wBAAgB,uBAAuB,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAO9D;AAED,wBAAgB,mBAAmB,IAAI,SAAS,CAO/C;AAED,wBAAgB,2BAA2B,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAOhE;AAED,wBAAgB,qBAAqB,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,SAAS,CAS9E;AAED,wBAAgB,oBAAoB,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAO3D;AAED,wBAAgB,mBAAmB,IAAI,SAAS,CAO/C;AAED,qBAAa,iBAAkB,SAAQ,WAAW;;CASjD;AAED,qBAAa,mBAAoB,SAAQ,WAAW;gBACtC,MAAM,EAAE,MAAM;CAQ3B;AAED,qBAAa,sBAAuB,SAAQ,WAAW;gBACzC,IAAI,EAAE,MAAM;CAQzB;AAED,wBAAgB,mBAAmB,IAAI,SAAS,CAO/C;AAED,wBAAgB,mBAAmB,IAAI,SAAS,CAO/C;AAED,wBAAgB,oBAAoB,IAAI,SAAS,CAOhD;AAED,wBAAgB,sBAAsB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,SAAS,CAO/E;AAED,wBAAgB,sBAAsB,IAAI,SAAS,CAOlD;AAED,wBAAgB,wBAAwB,CAAC,UAAU,EAAE,MAAM,GAAG,SAAS,CAOtE;AAED,wBAAgB,qBAAqB,IAAI,SAAS,CAOjD;AAED,wBAAgB,wBAAwB,IAAI,SAAS,CAQpD;AAED,wBAAgB,YAAY,CAAC,GAAG,EAAE,KAAK,GAAG,SAAS,CAElD;AAED,wBAAgB,4BAA4B,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,SAAS,CAOxF;AAED,wBAAgB,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,CAOvD;AAED,wBAAgB,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,GAAG,WAAW,CAEnE;AAED,wBAAgB,eAAe,IAAI,WAAW,CAO7C;AAED,qBAAa,0BAA2B,SAAQ,SAAS;gBAC3C,SAAS,EAAE,MAAM;CAQ9B;AAED,qBAAa,sBAAuB,SAAQ,SAAS;gBACvC,SAAS,EAAE,KAAK;CAQ7B;AAED,qBAAa,uBAAwB,SAAQ,SAAS;gBACxC,QAAQ,EAAE,MAAM;CAO7B;AAED,qBAAa,qCAAsC,SAAQ,SAAS;;CASnE"}