// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VideoFilterAppRemoteNotSupportedError = exports.NoAadManifestExistError = exports.NoCapabilityFoundError = exports.OperationNotPermittedError = exports.LoadPluginError = exports.NpmInstallError = exports.SPFxConfigError = exports.FailedToParseResourceIdError = exports.NotJsonError = exports.ConsolidateCanceledError = exports.AbandonedProjectError = exports.IncompatibleProjectError = exports.ToolkitNotSupportError = exports.UpgradeV3CanceledError = exports.UpgradeCanceledError = exports.ProjectSettingError = exports.SolutionConfigError = exports.ObjectIsUndefinedError = exports.NotImplementedError = exports.LoadSolutionError = exports.ModifiedSecretError = exports.NonExistEnvNameError = exports.InvalidEnvConfigError = exports.ProjectEnvAlreadyExistError = exports.InvalidEnvNameError = exports.ProjectEnvNotExistError = exports.MultipleEnvNotEnabledError = exports.ProjectSettingsUndefinedError = exports.PluginHasNoTaskImpl = exports.InvalidStateError = exports.ContextUpgradeError = exports.FunctionRouterError = exports.InvalidInputError = exports.FetchSampleError = exports.TaskNotSupportError = exports.InvalidProjectSettingsFileError = exports.InvalidProjectError = exports.PathNotExistError = exports.NoProjectOpenedError = exports.InitializedFileAlreadyExistError = exports.CopyFileError = exports.MigrationError = exports.ReadFileError = exports.WriteFileError = exports.ProjectFolderInvalidError = exports.ProjectFolderExistError = exports.MigrationSource = exports.CoreSource = void 0;
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const constants_1 = require("../common/constants");
const localizeUtils_1 = require("../common/localizeUtils");
exports.CoreSource = "Core";
exports.MigrationSource = "Migration";
class ProjectFolderExistError extends teamsfx_api_1.UserError {
    constructor(path) {
        super({
            message: localizeUtils_1.getDefaultString("error.ProjectFolderExistError", path),
            displayMessage: localizeUtils_1.getLocalizedString("error.ProjectFolderExistError", path),
            source: exports.CoreSource,
        });
    }
}
exports.ProjectFolderExistError = ProjectFolderExistError;
class ProjectFolderInvalidError extends teamsfx_api_1.UserError {
    constructor(path) {
        super({
            message: localizeUtils_1.getDefaultString("error.ProjectFolderInvalidError", path),
            displayMessage: localizeUtils_1.getLocalizedString("error.ProjectFolderInvalidError", path),
            source: exports.CoreSource,
        });
    }
}
exports.ProjectFolderInvalidError = ProjectFolderInvalidError;
function WriteFileError(e) {
    return new teamsfx_api_1.SystemError({
        name: "WriteFileError",
        source: exports.CoreSource,
        error: e,
    });
}
exports.WriteFileError = WriteFileError;
function ReadFileError(e) {
    return new teamsfx_api_1.SystemError({
        name: "ReadFileError",
        source: exports.CoreSource,
        error: e,
    });
}
exports.ReadFileError = ReadFileError;
function MigrationError(e, name, helpLink) {
    return new teamsfx_api_1.UserError({
        name: name,
        source: exports.MigrationSource,
        error: e,
        // the link show to user will be helpLink+ # + source + name
        helpLink: helpLink,
    });
}
exports.MigrationError = MigrationError;
function CopyFileError(e) {
    return new teamsfx_api_1.SystemError({
        name: "CopyFileError",
        source: exports.CoreSource,
        error: e,
    });
}
exports.CopyFileError = CopyFileError;
class InitializedFileAlreadyExistError extends teamsfx_api_1.UserError {
    constructor(filePath) {
        super({
            message: localizeUtils_1.getDefaultString("error.InitializedFileExistError", filePath),
            displayMessage: localizeUtils_1.getLocalizedString("error.InitializedFileExistError", filePath),
            source: exports.CoreSource,
        });
    }
}
exports.InitializedFileAlreadyExistError = InitializedFileAlreadyExistError;
class NoProjectOpenedError extends teamsfx_api_1.UserError {
    constructor() {
        super({
            message: localizeUtils_1.getDefaultString("error.NoProjectOpenedError"),
            displayMessage: localizeUtils_1.getLocalizedString("error.NoProjectOpenedError"),
            source: exports.CoreSource,
        });
    }
}
exports.NoProjectOpenedError = NoProjectOpenedError;
class PathNotExistError extends teamsfx_api_1.UserError {
    constructor(path) {
        super({
            message: localizeUtils_1.getDefaultString("error.PathNotExistError", path),
            displayMessage: localizeUtils_1.getLocalizedString("error.PathNotExistError", path),
            source: exports.CoreSource,
        });
    }
}
exports.PathNotExistError = PathNotExistError;
class InvalidProjectError extends teamsfx_api_1.UserError {
    constructor(msg) {
        super({
            message: localizeUtils_1.getDefaultString("error.InvalidProjectError", msg || ""),
            displayMessage: localizeUtils_1.getLocalizedString("error.InvalidProjectError", msg || ""),
            source: exports.CoreSource,
        });
    }
}
exports.InvalidProjectError = InvalidProjectError;
class InvalidProjectSettingsFileError extends teamsfx_api_1.UserError {
    constructor(msg) {
        super({
            message: localizeUtils_1.getDefaultString("error.InvalidProjectSettingsFileError", msg || ""),
            displayMessage: localizeUtils_1.getLocalizedString("error.InvalidProjectSettingsFileError", msg || ""),
            source: exports.CoreSource,
        });
    }
}
exports.InvalidProjectSettingsFileError = InvalidProjectSettingsFileError;
class TaskNotSupportError extends teamsfx_api_1.SystemError {
    constructor(task) {
        super({
            message: localizeUtils_1.getDefaultString("error.TaskNotSupportError", task),
            displayMessage: localizeUtils_1.getLocalizedString("error.TaskNotSupportError", task),
            source: exports.CoreSource,
        });
    }
}
exports.TaskNotSupportError = TaskNotSupportError;
class FetchSampleError extends teamsfx_api_1.UserError {
    constructor(sampleId) {
        super({
            message: localizeUtils_1.getDefaultString("error.FetchSampleError", sampleId),
            displayMessage: localizeUtils_1.getLocalizedString("error.FetchSampleError", sampleId),
            source: exports.CoreSource,
        });
    }
}
exports.FetchSampleError = FetchSampleError;
function InvalidInputError(reason, inputs) {
    const txt = inputs ? `${reason}, inputs: ${JSON.stringify(inputs)}` : reason;
    return new teamsfx_api_1.UserError(exports.CoreSource, "InvalidInput", localizeUtils_1.getDefaultString("error.InvalidInputError", txt), localizeUtils_1.getLocalizedString("error.InvalidInputError", txt));
}
exports.InvalidInputError = InvalidInputError;
function FunctionRouterError(func) {
    const param = JSON.stringify(func);
    return new teamsfx_api_1.UserError(exports.CoreSource, "FunctionRouterError", localizeUtils_1.getDefaultString("error.FunctionRouterError", param), localizeUtils_1.getLocalizedString("error.FunctionRouterError", param));
}
exports.FunctionRouterError = FunctionRouterError;
function ContextUpgradeError(error, isUserError = false) {
    if (isUserError) {
        return new teamsfx_api_1.UserError({
            name: "ContextUpgradeError",
            message: localizeUtils_1.getDefaultString("error.ContextUpgradeError", error.message),
            displayMessage: localizeUtils_1.getLocalizedString("error.ContextUpgradeError", error.message),
            source: exports.CoreSource,
        });
    }
    else {
        return new teamsfx_api_1.SystemError({
            name: "ContextUpgradeError",
            message: localizeUtils_1.getDefaultString("error.ContextUpgradeError", error.message),
            displayMessage: localizeUtils_1.getLocalizedString("error.ContextUpgradeError", error.message),
            source: exports.CoreSource,
        });
    }
}
exports.ContextUpgradeError = ContextUpgradeError;
function InvalidStateError(pluginName, state) {
    return new teamsfx_api_1.SystemError(exports.CoreSource, "InvalidProfileError", localizeUtils_1.getDefaultString("error.InvalidProfileError", pluginName, JSON.stringify(state)), localizeUtils_1.getLocalizedString("error.InvalidProfileError", pluginName, JSON.stringify(state)));
}
exports.InvalidStateError = InvalidStateError;
function PluginHasNoTaskImpl(pluginName, task) {
    return new teamsfx_api_1.SystemError(exports.CoreSource, "PluginHasNoTaskImpl", localizeUtils_1.getDefaultString("error.PluginHasNoTaskImpl", pluginName, task), localizeUtils_1.getLocalizedString("error.PluginHasNoTaskImpl", pluginName, task));
}
exports.PluginHasNoTaskImpl = PluginHasNoTaskImpl;
function ProjectSettingsUndefinedError() {
    return new teamsfx_api_1.SystemError(exports.CoreSource, "ProjectSettingsUndefinedError", localizeUtils_1.getDefaultString("error.ProjectSettingsUndefinedError"), localizeUtils_1.getLocalizedString("error.ProjectSettingsUndefinedError"));
}
exports.ProjectSettingsUndefinedError = ProjectSettingsUndefinedError;
function MultipleEnvNotEnabledError() {
    return new teamsfx_api_1.SystemError(exports.CoreSource, "MultipleEnvNotEnabledError", localizeUtils_1.getDefaultString("error.MultipleEnvNotEnabledError"), localizeUtils_1.getLocalizedString("error.MultipleEnvNotEnabledError"));
}
exports.MultipleEnvNotEnabledError = MultipleEnvNotEnabledError;
function ProjectEnvNotExistError(env) {
    return new teamsfx_api_1.UserError(exports.CoreSource, "ProjectEnvNotExistError", localizeUtils_1.getDefaultString("error.ProjectEnvNotExistError", env, env), localizeUtils_1.getLocalizedString("error.ProjectEnvNotExistError", env, env));
}
exports.ProjectEnvNotExistError = ProjectEnvNotExistError;
function InvalidEnvNameError() {
    return new teamsfx_api_1.UserError(exports.CoreSource, "InvalidEnvNameError", localizeUtils_1.getDefaultString("error.InvalidEnvNameError"), localizeUtils_1.getLocalizedString("error.InvalidEnvNameError"));
}
exports.InvalidEnvNameError = InvalidEnvNameError;
function ProjectEnvAlreadyExistError(env) {
    return new teamsfx_api_1.UserError(exports.CoreSource, "ProjectEnvAlreadyExistError", localizeUtils_1.getDefaultString("error.ProjectEnvAlreadyExistError", env), localizeUtils_1.getLocalizedString("error.ProjectEnvAlreadyExistError", env));
}
exports.ProjectEnvAlreadyExistError = ProjectEnvAlreadyExistError;
function InvalidEnvConfigError(env, errorMsg) {
    const param1 = teamsfx_api_1.EnvConfigFileNameTemplate.replace(teamsfx_api_1.EnvNamePlaceholder, env);
    const param2 = errorMsg;
    return new teamsfx_api_1.UserError(exports.CoreSource, "InvalidEnvConfigError", localizeUtils_1.getDefaultString("error.InvalidEnvConfigError", param1, param2), localizeUtils_1.getLocalizedString("error.InvalidEnvConfigError", param1, param2));
}
exports.InvalidEnvConfigError = InvalidEnvConfigError;
function NonExistEnvNameError(env) {
    return new teamsfx_api_1.UserError(exports.CoreSource, "NonExistEnvNameError", localizeUtils_1.getDefaultString("error.NonExistEnvNameError", env), localizeUtils_1.getLocalizedString("error.NonExistEnvNameError", env));
}
exports.NonExistEnvNameError = NonExistEnvNameError;
function ModifiedSecretError() {
    return new teamsfx_api_1.UserError(exports.CoreSource, "ModifiedSecretError", localizeUtils_1.getDefaultString("error.ModifiedSecretError"), localizeUtils_1.getLocalizedString("error.ModifiedSecretError"));
}
exports.ModifiedSecretError = ModifiedSecretError;
class LoadSolutionError extends teamsfx_api_1.SystemError {
    constructor() {
        super(exports.CoreSource, new.target.name, localizeUtils_1.getDefaultString("error.LoadSolutionError"), localizeUtils_1.getLocalizedString("error.LoadSolutionError"));
    }
}
exports.LoadSolutionError = LoadSolutionError;
class NotImplementedError extends teamsfx_api_1.SystemError {
    constructor(method) {
        super(exports.CoreSource, new.target.name, localizeUtils_1.getDefaultString("error.NotImplementedError", method), localizeUtils_1.getLocalizedString("error.NotImplementedError", method));
    }
}
exports.NotImplementedError = NotImplementedError;
class ObjectIsUndefinedError extends teamsfx_api_1.SystemError {
    constructor(name) {
        super(exports.CoreSource, new.target.name, localizeUtils_1.getDefaultString("error.NotImplementedError", name), localizeUtils_1.getLocalizedString("error.NotImplementedError", name));
    }
}
exports.ObjectIsUndefinedError = ObjectIsUndefinedError;
function SolutionConfigError() {
    return new teamsfx_api_1.UserError(exports.CoreSource, "SolutionConfigError", localizeUtils_1.getDefaultString("error.SolutionConfigError"), localizeUtils_1.getLocalizedString("error.SolutionConfigError"));
}
exports.SolutionConfigError = SolutionConfigError;
function ProjectSettingError() {
    return new teamsfx_api_1.UserError(exports.CoreSource, "ProjectSettingError", localizeUtils_1.getDefaultString("error.ProjectSettingError"), localizeUtils_1.getLocalizedString("error.ProjectSettingError"));
}
exports.ProjectSettingError = ProjectSettingError;
function UpgradeCanceledError() {
    return new teamsfx_api_1.UserError(exports.CoreSource, "UserCancel", // @see tools.isUserCancelError()
    localizeUtils_1.getDefaultString("error.UpgradeCanceledError"), localizeUtils_1.getLocalizedString("error.UpgradeCanceledError"));
}
exports.UpgradeCanceledError = UpgradeCanceledError;
function UpgradeV3CanceledError(link, version) {
    return new teamsfx_api_1.UserError(exports.CoreSource, "UserCancel", // @see tools.isUserCancelError()
    localizeUtils_1.getDefaultString("error.UpgradeV3CanceledError", link, version), localizeUtils_1.getLocalizedString("error.UpgradeV3CanceledError", link, version));
}
exports.UpgradeV3CanceledError = UpgradeV3CanceledError;
function ToolkitNotSupportError() {
    return new teamsfx_api_1.UserError(exports.CoreSource, "ToolkitNotSupport", localizeUtils_1.getDefaultString("core.migrationV3.CreateNewProject"), localizeUtils_1.getLocalizedString("core.migrationV3.CreateNewProject"));
}
exports.ToolkitNotSupportError = ToolkitNotSupportError;
function IncompatibleProjectError(messageKey) {
    return new teamsfx_api_1.UserError(exports.CoreSource, "IncompatibleProject", localizeUtils_1.getDefaultString(messageKey), localizeUtils_1.getLocalizedString(messageKey));
}
exports.IncompatibleProjectError = IncompatibleProjectError;
function AbandonedProjectError() {
    return new teamsfx_api_1.UserError(exports.CoreSource, "AbandonedProject", localizeUtils_1.getDefaultString("core.migrationV3.abandonedProject"), localizeUtils_1.getLocalizedString("core.migrationV3.abandonedProject"));
}
exports.AbandonedProjectError = AbandonedProjectError;
function ConsolidateCanceledError() {
    return new teamsfx_api_1.UserError(exports.CoreSource, 
    // @see tools.isUserCancelError()
    "UserCancel", localizeUtils_1.getDefaultString("error.ConsolidateCanceledError"), localizeUtils_1.getLocalizedString("error.ConsolidateCanceledError"));
}
exports.ConsolidateCanceledError = ConsolidateCanceledError;
function NotJsonError(err) {
    return new teamsfx_api_1.UserError({ error: err, source: exports.CoreSource });
}
exports.NotJsonError = NotJsonError;
function FailedToParseResourceIdError(name, resourceId) {
    return new teamsfx_api_1.UserError(exports.CoreSource, "FailedToParseResourceIdError", localizeUtils_1.getDefaultString("error.FailedToParseResourceIdError", name, resourceId), localizeUtils_1.getLocalizedString("error.FailedToParseResourceIdError", name, resourceId));
}
exports.FailedToParseResourceIdError = FailedToParseResourceIdError;
function SPFxConfigError(file) {
    return new teamsfx_api_1.UserError(exports.CoreSource, "SPFxConfigError", localizeUtils_1.getDefaultString("error.SPFxConfigError", file), localizeUtils_1.getLocalizedString("error.SPFxConfigError", file));
}
exports.SPFxConfigError = SPFxConfigError;
function NpmInstallError(path, e) {
    return new teamsfx_api_1.SystemError({ error: e, source: exports.CoreSource });
}
exports.NpmInstallError = NpmInstallError;
function LoadPluginError() {
    return new teamsfx_api_1.SystemError(exports.CoreSource, "LoadPluginError", localizeUtils_1.getDefaultString("error.LoadPluginError"), localizeUtils_1.getLocalizedString("error.LoadPluginError"));
}
exports.LoadPluginError = LoadPluginError;
class OperationNotPermittedError extends teamsfx_api_1.UserError {
    constructor(operation) {
        super(exports.CoreSource, new.target.name, localizeUtils_1.getDefaultString("error.OperationNotPermittedError", operation), localizeUtils_1.getLocalizedString("error.OperationNotPermittedError", operation));
    }
}
exports.OperationNotPermittedError = OperationNotPermittedError;
class NoCapabilityFoundError extends teamsfx_api_1.UserError {
    constructor(operation) {
        super({
            source: exports.CoreSource,
            message: localizeUtils_1.getDefaultString("core.deploy.noCapabilityFound", operation),
            displayMessage: localizeUtils_1.getLocalizedString("core.deploy.noCapabilityFound", operation),
            helpLink: constants_1.HelpLinks.HowToAddCapability,
        });
    }
}
exports.NoCapabilityFoundError = NoCapabilityFoundError;
class NoAadManifestExistError extends teamsfx_api_1.UserError {
    constructor(filePath) {
        super({
            source: exports.CoreSource,
            message: localizeUtils_1.getDefaultString("error.aad.AadManifestNotExistError", filePath),
            displayMessage: localizeUtils_1.getLocalizedString("error.aad.AadManifestNotExistError", filePath),
        });
    }
}
exports.NoAadManifestExistError = NoAadManifestExistError;
class VideoFilterAppRemoteNotSupportedError extends teamsfx_api_1.UserError {
    constructor() {
        super({
            source: exports.CoreSource,
            name: VideoFilterAppRemoteNotSupportedError.name,
            message: localizeUtils_1.getLocalizedString("error.VideoFilterAppNotRemoteSupported"),
            displayMessage: localizeUtils_1.getLocalizedString("error.VideoFilterAppNotRemoteSupported"),
        });
    }
}
exports.VideoFilterAppRemoteNotSupportedError = VideoFilterAppRemoteNotSupportedError;
//# sourceMappingURL=error.js.map