{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../src/core/error.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC,YAAY,CAAC;;;AAEb,wDAWgC;AAChC,mDAAgD;AAChD,2DAA+E;AAElE,QAAA,UAAU,GAAG,MAAM,CAAC;AACpB,QAAA,eAAe,GAAG,WAAW,CAAC;AAE3C,MAAa,uBAAwB,SAAQ,uBAAS;IACpD,YAAY,IAAY;QACtB,KAAK,CAAC;YACJ,OAAO,EAAE,gCAAgB,CAAC,+BAA+B,EAAE,IAAI,CAAC;YAChE,cAAc,EAAE,kCAAkB,CAAC,+BAA+B,EAAE,IAAI,CAAC;YACzE,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;IACL,CAAC;CACF;AARD,0DAQC;AAED,MAAa,yBAA0B,SAAQ,uBAAS;IACtD,YAAY,IAAY;QACtB,KAAK,CAAC;YACJ,OAAO,EAAE,gCAAgB,CAAC,iCAAiC,EAAE,IAAI,CAAC;YAClE,cAAc,EAAE,kCAAkB,CAAC,iCAAiC,EAAE,IAAI,CAAC;YAC3E,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;IACL,CAAC;CACF;AARD,8DAQC;AAED,SAAgB,cAAc,CAAC,CAAQ;IACrC,OAAO,IAAI,yBAAW,CAAC;QACrB,IAAI,EAAE,gBAAgB;QACtB,MAAM,EAAE,kBAAU;QAClB,KAAK,EAAE,CAAC;KACT,CAAC,CAAC;AACL,CAAC;AAND,wCAMC;AAED,SAAgB,aAAa,CAAC,CAAQ;IACpC,OAAO,IAAI,yBAAW,CAAC;QACrB,IAAI,EAAE,eAAe;QACrB,MAAM,EAAE,kBAAU;QAClB,KAAK,EAAE,CAAC;KACT,CAAC,CAAC;AACL,CAAC;AAND,sCAMC;AAED,SAAgB,cAAc,CAAC,CAAQ,EAAE,IAAY,EAAE,QAAiB;IACtE,OAAO,IAAI,uBAAS,CAAC;QACnB,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,uBAAe;QACvB,KAAK,EAAE,CAAC;QACR,4DAA4D;QAC5D,QAAQ,EAAE,QAAQ;KACnB,CAAC,CAAC;AACL,CAAC;AARD,wCAQC;AAED,SAAgB,aAAa,CAAC,CAAQ;IACpC,OAAO,IAAI,yBAAW,CAAC;QACrB,IAAI,EAAE,eAAe;QACrB,MAAM,EAAE,kBAAU;QAClB,KAAK,EAAE,CAAC;KACT,CAAC,CAAC;AACL,CAAC;AAND,sCAMC;AAED,MAAa,gCAAiC,SAAQ,uBAAS;IAC7D,YAAY,QAAgB;QAC1B,KAAK,CAAC;YACJ,OAAO,EAAE,gCAAgB,CAAC,iCAAiC,EAAE,QAAQ,CAAC;YACtE,cAAc,EAAE,kCAAkB,CAAC,iCAAiC,EAAE,QAAQ,CAAC;YAC/E,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;IACL,CAAC;CACF;AARD,4EAQC;AAED,MAAa,oBAAqB,SAAQ,uBAAS;IACjD;QACE,KAAK,CAAC;YACJ,OAAO,EAAE,gCAAgB,CAAC,4BAA4B,CAAC;YACvD,cAAc,EAAE,kCAAkB,CAAC,4BAA4B,CAAC;YAChE,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;IACL,CAAC;CACF;AARD,oDAQC;AAED,MAAa,iBAAkB,SAAQ,uBAAS;IAC9C,YAAY,IAAY;QACtB,KAAK,CAAC;YACJ,OAAO,EAAE,gCAAgB,CAAC,yBAAyB,EAAE,IAAI,CAAC;YAC1D,cAAc,EAAE,kCAAkB,CAAC,yBAAyB,EAAE,IAAI,CAAC;YACnE,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;IACL,CAAC;CACF;AARD,8CAQC;AAED,MAAa,mBAAoB,SAAQ,uBAAS;IAChD,YAAY,GAAY;QACtB,KAAK,CAAC;YACJ,OAAO,EAAE,gCAAgB,CAAC,2BAA2B,EAAE,GAAG,IAAI,EAAE,CAAC;YACjE,cAAc,EAAE,kCAAkB,CAAC,2BAA2B,EAAE,GAAG,IAAI,EAAE,CAAC;YAC1E,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;IACL,CAAC;CACF;AARD,kDAQC;AAED,MAAa,+BAAgC,SAAQ,uBAAS;IAC5D,YAAY,GAAY;QACtB,KAAK,CAAC;YACJ,OAAO,EAAE,gCAAgB,CAAC,uCAAuC,EAAE,GAAG,IAAI,EAAE,CAAC;YAC7E,cAAc,EAAE,kCAAkB,CAAC,uCAAuC,EAAE,GAAG,IAAI,EAAE,CAAC;YACtF,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;IACL,CAAC;CACF;AARD,0EAQC;AAED,MAAa,mBAAoB,SAAQ,yBAAW;IAClD,YAAY,IAAY;QACtB,KAAK,CAAC;YACJ,OAAO,EAAE,gCAAgB,CAAC,2BAA2B,EAAE,IAAI,CAAC;YAC5D,cAAc,EAAE,kCAAkB,CAAC,2BAA2B,EAAE,IAAI,CAAC;YACrE,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;IACL,CAAC;CACF;AARD,kDAQC;AAED,MAAa,gBAAiB,SAAQ,uBAAS;IAC7C,YAAY,QAAgB;QAC1B,KAAK,CAAC;YACJ,OAAO,EAAE,gCAAgB,CAAC,wBAAwB,EAAE,QAAQ,CAAC;YAC7D,cAAc,EAAE,kCAAkB,CAAC,wBAAwB,EAAE,QAAQ,CAAC;YACtE,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;IACL,CAAC;CACF;AARD,4CAQC;AAED,SAAgB,iBAAiB,CAAC,MAAc,EAAE,MAAe;IAC/D,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;IAC7E,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,cAAc,EACd,gCAAgB,CAAC,yBAAyB,EAAE,GAAG,CAAC,EAChD,kCAAkB,CAAC,yBAAyB,EAAE,GAAG,CAAC,CACnD,CAAC;AACJ,CAAC;AARD,8CAQC;AAED,SAAgB,mBAAmB,CAAC,IAAU;IAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACnC,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,qBAAqB,EACrB,gCAAgB,CAAC,2BAA2B,EAAE,KAAK,CAAC,EACpD,kCAAkB,CAAC,2BAA2B,EAAE,KAAK,CAAC,CACvD,CAAC;AACJ,CAAC;AARD,kDAQC;AAED,SAAgB,mBAAmB,CAAC,KAAU,EAAE,WAAW,GAAG,KAAK;IACjE,IAAI,WAAW,EAAE;QACf,OAAO,IAAI,uBAAS,CAAC;YACnB,IAAI,EAAE,qBAAqB;YAC3B,OAAO,EAAE,gCAAgB,CAAC,2BAA2B,EAAE,KAAK,CAAC,OAAO,CAAC;YACrE,cAAc,EAAE,kCAAkB,CAAC,2BAA2B,EAAE,KAAK,CAAC,OAAO,CAAC;YAC9E,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;KACJ;SAAM;QACL,OAAO,IAAI,yBAAW,CAAC;YACrB,IAAI,EAAE,qBAAqB;YAC3B,OAAO,EAAE,gCAAgB,CAAC,2BAA2B,EAAE,KAAK,CAAC,OAAO,CAAC;YACrE,cAAc,EAAE,kCAAkB,CAAC,2BAA2B,EAAE,KAAK,CAAC,OAAO,CAAC;YAC9E,MAAM,EAAE,kBAAU;SACnB,CAAC,CAAC;KACJ;AACH,CAAC;AAhBD,kDAgBC;AAED,SAAgB,iBAAiB,CAAC,UAAkB,EAAE,KAAW;IAC/D,OAAO,IAAI,yBAAW,CACpB,kBAAU,EACV,qBAAqB,EACrB,gCAAgB,CAAC,2BAA2B,EAAE,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAChF,kCAAkB,CAAC,2BAA2B,EAAE,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CACnF,CAAC;AACJ,CAAC;AAPD,8CAOC;AAED,SAAgB,mBAAmB,CAAC,UAAkB,EAAE,IAAY;IAClE,OAAO,IAAI,yBAAW,CACpB,kBAAU,EACV,qBAAqB,EACrB,gCAAgB,CAAC,2BAA2B,EAAE,UAAU,EAAE,IAAI,CAAC,EAC/D,kCAAkB,CAAC,2BAA2B,EAAE,UAAU,EAAE,IAAI,CAAC,CAClE,CAAC;AACJ,CAAC;AAPD,kDAOC;AAED,SAAgB,6BAA6B;IAC3C,OAAO,IAAI,yBAAW,CACpB,kBAAU,EACV,+BAA+B,EAC/B,gCAAgB,CAAC,qCAAqC,CAAC,EACvD,kCAAkB,CAAC,qCAAqC,CAAC,CAC1D,CAAC;AACJ,CAAC;AAPD,sEAOC;AAED,SAAgB,0BAA0B;IACxC,OAAO,IAAI,yBAAW,CACpB,kBAAU,EACV,4BAA4B,EAC5B,gCAAgB,CAAC,kCAAkC,CAAC,EACpD,kCAAkB,CAAC,kCAAkC,CAAC,CACvD,CAAC;AACJ,CAAC;AAPD,gEAOC;AAED,SAAgB,uBAAuB,CAAC,GAAW;IACjD,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,yBAAyB,EACzB,gCAAgB,CAAC,+BAA+B,EAAE,GAAG,EAAE,GAAG,CAAC,EAC3D,kCAAkB,CAAC,+BAA+B,EAAE,GAAG,EAAE,GAAG,CAAC,CAC9D,CAAC;AACJ,CAAC;AAPD,0DAOC;AAED,SAAgB,mBAAmB;IACjC,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,qBAAqB,EACrB,gCAAgB,CAAC,2BAA2B,CAAC,EAC7C,kCAAkB,CAAC,2BAA2B,CAAC,CAChD,CAAC;AACJ,CAAC;AAPD,kDAOC;AAED,SAAgB,2BAA2B,CAAC,GAAW;IACrD,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,6BAA6B,EAC7B,gCAAgB,CAAC,mCAAmC,EAAE,GAAG,CAAC,EAC1D,kCAAkB,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAC7D,CAAC;AACJ,CAAC;AAPD,kEAOC;AAED,SAAgB,qBAAqB,CAAC,GAAW,EAAE,QAAgB;IACjE,MAAM,MAAM,GAAG,uCAAyB,CAAC,OAAO,CAAC,gCAAkB,EAAE,GAAG,CAAC,CAAC;IAC1E,MAAM,MAAM,GAAG,QAAQ,CAAC;IACxB,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,uBAAuB,EACvB,gCAAgB,CAAC,6BAA6B,EAAE,MAAM,EAAE,MAAM,CAAC,EAC/D,kCAAkB,CAAC,6BAA6B,EAAE,MAAM,EAAE,MAAM,CAAC,CAClE,CAAC;AACJ,CAAC;AATD,sDASC;AAED,SAAgB,oBAAoB,CAAC,GAAW;IAC9C,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,sBAAsB,EACtB,gCAAgB,CAAC,4BAA4B,EAAE,GAAG,CAAC,EACnD,kCAAkB,CAAC,4BAA4B,EAAE,GAAG,CAAC,CACtD,CAAC;AACJ,CAAC;AAPD,oDAOC;AAED,SAAgB,mBAAmB;IACjC,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,qBAAqB,EACrB,gCAAgB,CAAC,2BAA2B,CAAC,EAC7C,kCAAkB,CAAC,2BAA2B,CAAC,CAChD,CAAC;AACJ,CAAC;AAPD,kDAOC;AAED,MAAa,iBAAkB,SAAQ,yBAAW;IAChD;QACE,KAAK,CACH,kBAAU,EACV,GAAG,CAAC,MAAM,CAAC,IAAI,EACf,gCAAgB,CAAC,yBAAyB,CAAC,EAC3C,kCAAkB,CAAC,yBAAyB,CAAC,CAC9C,CAAC;IACJ,CAAC;CACF;AATD,8CASC;AAED,MAAa,mBAAoB,SAAQ,yBAAW;IAClD,YAAY,MAAc;QACxB,KAAK,CACH,kBAAU,EACV,GAAG,CAAC,MAAM,CAAC,IAAI,EACf,gCAAgB,CAAC,2BAA2B,EAAE,MAAM,CAAC,EACrD,kCAAkB,CAAC,2BAA2B,EAAE,MAAM,CAAC,CACxD,CAAC;IACJ,CAAC;CACF;AATD,kDASC;AAED,MAAa,sBAAuB,SAAQ,yBAAW;IACrD,YAAY,IAAY;QACtB,KAAK,CACH,kBAAU,EACV,GAAG,CAAC,MAAM,CAAC,IAAI,EACf,gCAAgB,CAAC,2BAA2B,EAAE,IAAI,CAAC,EACnD,kCAAkB,CAAC,2BAA2B,EAAE,IAAI,CAAC,CACtD,CAAC;IACJ,CAAC;CACF;AATD,wDASC;AAED,SAAgB,mBAAmB;IACjC,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,qBAAqB,EACrB,gCAAgB,CAAC,2BAA2B,CAAC,EAC7C,kCAAkB,CAAC,2BAA2B,CAAC,CAChD,CAAC;AACJ,CAAC;AAPD,kDAOC;AAED,SAAgB,mBAAmB;IACjC,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,qBAAqB,EACrB,gCAAgB,CAAC,2BAA2B,CAAC,EAC7C,kCAAkB,CAAC,2BAA2B,CAAC,CAChD,CAAC;AACJ,CAAC;AAPD,kDAOC;AAED,SAAgB,oBAAoB;IAClC,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,YAAY,EAAE,iCAAiC;IAC/C,gCAAgB,CAAC,4BAA4B,CAAC,EAC9C,kCAAkB,CAAC,4BAA4B,CAAC,CACjD,CAAC;AACJ,CAAC;AAPD,oDAOC;AAED,SAAgB,sBAAsB,CAAC,IAAY,EAAE,OAAe;IAClE,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,YAAY,EAAE,iCAAiC;IAC/C,gCAAgB,CAAC,8BAA8B,EAAE,IAAI,EAAE,OAAO,CAAC,EAC/D,kCAAkB,CAAC,8BAA8B,EAAE,IAAI,EAAE,OAAO,CAAC,CAClE,CAAC;AACJ,CAAC;AAPD,wDAOC;AAED,SAAgB,sBAAsB;IACpC,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,mBAAmB,EACnB,gCAAgB,CAAC,mCAAmC,CAAC,EACrD,kCAAkB,CAAC,mCAAmC,CAAC,CACxD,CAAC;AACJ,CAAC;AAPD,wDAOC;AAED,SAAgB,wBAAwB,CAAC,UAAkB;IACzD,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,qBAAqB,EACrB,gCAAgB,CAAC,UAAU,CAAC,EAC5B,kCAAkB,CAAC,UAAU,CAAC,CAC/B,CAAC;AACJ,CAAC;AAPD,4DAOC;AAED,SAAgB,qBAAqB;IACnC,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,kBAAkB,EAClB,gCAAgB,CAAC,mCAAmC,CAAC,EACrD,kCAAkB,CAAC,mCAAmC,CAAC,CACxD,CAAC;AACJ,CAAC;AAPD,sDAOC;AAED,SAAgB,wBAAwB;IACtC,OAAO,IAAI,uBAAS,CAClB,kBAAU;IACV,iCAAiC;IACjC,YAAY,EACZ,gCAAgB,CAAC,gCAAgC,CAAC,EAClD,kCAAkB,CAAC,gCAAgC,CAAC,CACrD,CAAC;AACJ,CAAC;AARD,4DAQC;AAED,SAAgB,YAAY,CAAC,GAAU;IACrC,OAAO,IAAI,uBAAS,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,kBAAU,EAAE,CAAC,CAAC;AAC3D,CAAC;AAFD,oCAEC;AAED,SAAgB,4BAA4B,CAAC,IAAY,EAAE,UAAkB;IAC3E,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,8BAA8B,EAC9B,gCAAgB,CAAC,oCAAoC,EAAE,IAAI,EAAE,UAAU,CAAC,EACxE,kCAAkB,CAAC,oCAAoC,EAAE,IAAI,EAAE,UAAU,CAAC,CAC3E,CAAC;AACJ,CAAC;AAPD,oEAOC;AAED,SAAgB,eAAe,CAAC,IAAY;IAC1C,OAAO,IAAI,uBAAS,CAClB,kBAAU,EACV,iBAAiB,EACjB,gCAAgB,CAAC,uBAAuB,EAAE,IAAI,CAAC,EAC/C,kCAAkB,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAClD,CAAC;AACJ,CAAC;AAPD,0CAOC;AAED,SAAgB,eAAe,CAAC,IAAY,EAAE,CAAQ;IACpD,OAAO,IAAI,yBAAW,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,kBAAU,EAAE,CAAC,CAAC;AAC3D,CAAC;AAFD,0CAEC;AAED,SAAgB,eAAe;IAC7B,OAAO,IAAI,yBAAW,CACpB,kBAAU,EACV,iBAAiB,EACjB,gCAAgB,CAAC,uBAAuB,CAAC,EACzC,kCAAkB,CAAC,uBAAuB,CAAC,CAC5C,CAAC;AACJ,CAAC;AAPD,0CAOC;AAED,MAAa,0BAA2B,SAAQ,uBAAS;IACvD,YAAY,SAAiB;QAC3B,KAAK,CACH,kBAAU,EACV,GAAG,CAAC,MAAM,CAAC,IAAI,EACf,gCAAgB,CAAC,kCAAkC,EAAE,SAAS,CAAC,EAC/D,kCAAkB,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAClE,CAAC;IACJ,CAAC;CACF;AATD,gEASC;AAED,MAAa,sBAAuB,SAAQ,uBAAS;IACnD,YAAY,SAAgB;QAC1B,KAAK,CAAC;YACJ,MAAM,EAAE,kBAAU;YAClB,OAAO,EAAE,gCAAgB,CAAC,+BAA+B,EAAE,SAAS,CAAC;YACrE,cAAc,EAAE,kCAAkB,CAAC,+BAA+B,EAAE,SAAS,CAAC;YAC9E,QAAQ,EAAE,qBAAS,CAAC,kBAAkB;SACvC,CAAC,CAAC;IACL,CAAC;CACF;AATD,wDASC;AAED,MAAa,uBAAwB,SAAQ,uBAAS;IACpD,YAAY,QAAgB;QAC1B,KAAK,CAAC;YACJ,MAAM,EAAE,kBAAU;YAClB,OAAO,EAAE,gCAAgB,CAAC,oCAAoC,EAAE,QAAQ,CAAC;YACzE,cAAc,EAAE,kCAAkB,CAAC,oCAAoC,EAAE,QAAQ,CAAC;SACnF,CAAC,CAAC;IACL,CAAC;CACF;AARD,0DAQC;AAED,MAAa,qCAAsC,SAAQ,uBAAS;IAClE;QACE,KAAK,CAAC;YACJ,MAAM,EAAE,kBAAU;YAClB,IAAI,EAAE,qCAAqC,CAAC,IAAI;YAChD,OAAO,EAAE,kCAAkB,CAAC,wCAAwC,CAAC;YACrE,cAAc,EAAE,kCAAkB,CAAC,wCAAwC,CAAC;SAC7E,CAAC,CAAC;IACL,CAAC;CACF;AATD,sFASC"}