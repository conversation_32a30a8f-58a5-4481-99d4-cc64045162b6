"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateAadManifestTemplate = void 0;
const tslib_1 = require("tslib");
const folder_1 = require("../folder");
const fs = tslib_1.__importStar(require("fs-extra"));
const os = tslib_1.__importStar(require("os"));
const tools_1 = require("../common/tools");
const constants_1 = require("../component/constants");
const workflow_1 = require("../component/workflow");
const projectSettingsHelper_1 = require("../common/local/projectSettingsHelper");
const projectSettingsHelper_2 = require("../common/projectSettingsHelper");
const constants_2 = require("../component/resource/aadApp/constants");
async function generateAadManifestTemplate(projectFolder, projectSettings, requiredResourceAccess = undefined, updateCapabilities = false) {
    const templatesFolder = folder_1.getTemplatesFolder();
    const appDir = await tools_1.getAppDirectory(projectFolder);
    const aadManifestTemplate = `${templatesFolder}/${constants_2.Constants.aadManifestTemplateFolder}/${constants_2.Constants.aadManifestTemplateName}`;
    await fs.ensureDir(appDir);
    const isVs = projectSettingsHelper_2.isVSProject(projectSettings);
    const aadManifestPath = `${appDir}/${constants_2.Constants.aadManifestTemplateName}`;
    let aadJson;
    if (await fs.pathExists(aadManifestPath)) {
        aadJson = await fs.readJSON(aadManifestPath);
    }
    else {
        aadJson = await fs.readJSON(aadManifestTemplate);
    }
    if (!aadJson.replyUrlsWithType) {
        aadJson.replyUrlsWithType = [];
    }
    if (requiredResourceAccess) {
        aadJson.requiredResourceAccess = requiredResourceAccess;
    }
    const hasTab = projectSettingsHelper_1.ProjectSettingsHelper.includeFrontend(projectSettings);
    if (hasTab) {
        const tabRedirectUrl1 = "{{state.fx-resource-aad-app-for-teams.frontendEndpoint}}/auth-end.html";
        if (!isRedirectUrlExist(aadJson.replyUrlsWithType, tabRedirectUrl1, "Web")) {
            aadJson.replyUrlsWithType.push({
                url: tabRedirectUrl1,
                type: "Web",
            });
        }
        const tabRedirectUrl2 = "{{state.fx-resource-aad-app-for-teams.frontendEndpoint}}/auth-end.html?clientId={{state.fx-resource-aad-app-for-teams.clientId}}";
        if (!isRedirectUrlExist(aadJson.replyUrlsWithType, tabRedirectUrl2, "Spa")) {
            aadJson.replyUrlsWithType.push({
                url: tabRedirectUrl2,
                type: "Spa",
            });
        }
        const tabRedirectUrl3 = "{{state.fx-resource-aad-app-for-teams.frontendEndpoint}}/blank-auth-end.html";
        if (!isRedirectUrlExist(aadJson.replyUrlsWithType, tabRedirectUrl3, "Spa")) {
            aadJson.replyUrlsWithType.push({
                url: tabRedirectUrl3,
                type: "Spa",
            });
        }
    }
    const hasBot = projectSettingsHelper_1.ProjectSettingsHelper.includeBot(projectSettings);
    if (hasBot) {
        const botRedirectUrl = isVs
            ? "{{state.fx-resource-aad-app-for-teams.botEndpoint}}/bot-auth-end.html"
            : "{{state.fx-resource-aad-app-for-teams.botEndpoint}}/auth-end.html";
        if (!isRedirectUrlExist(aadJson.replyUrlsWithType, botRedirectUrl, "Web")) {
            aadJson.replyUrlsWithType.push({
                url: botRedirectUrl,
                type: "Web",
            });
        }
    }
    // }
    if (updateCapabilities) {
        if (projectSettings.solutionSettings.capabilities.includes("Tab") &&
            !projectSettings.solutionSettings.capabilities.includes("TabSSO")) {
            projectSettings.solutionSettings.capabilities.push("TabSSO");
        }
        if (projectSettings.solutionSettings.capabilities.includes("Bot") &&
            !projectSettings.solutionSettings.capabilities.includes("BotSSO")) {
            projectSettings.solutionSettings.capabilities.push("BotSSO");
        }
        const tabConfig = workflow_1.getComponent(projectSettings, constants_1.ComponentNames.TeamsTab);
        if (tabConfig) {
            tabConfig.sso = true;
        }
        const botConfig = workflow_1.getComponent(projectSettings, constants_1.ComponentNames.TeamsTab);
        if (botConfig) {
            botConfig.sso = true;
        }
    }
    await fs.writeJSON(`${appDir}/${constants_2.Constants.aadManifestTemplateName}`, aadJson, {
        spaces: 4,
        EOL: os.EOL,
    });
}
exports.generateAadManifestTemplate = generateAadManifestTemplate;
function isRedirectUrlExist(replyUrls, url, type) {
    return (replyUrls.filter((item) => item.url === url && item.type === type).length > 0);
}
function updateRedirectUrlV3(aadJson, projectSetting) {
    const teamsTabComponent = workflow_1.getComponent(projectSetting, constants_1.ComponentNames.TeamsTab);
    if (teamsTabComponent) {
        const tabRedirectUrl1 = "{{state.aad-app.frontendEndpoint}}/auth-end.html";
        if (!isRedirectUrlExist(aadJson.replyUrlsWithType, tabRedirectUrl1, "Web")) {
            aadJson.replyUrlsWithType.push({
                url: tabRedirectUrl1,
                type: "Web",
            });
        }
        const tabRedirectUrl2 = "{{state.aad-app.frontendEndpoint}}/auth-end.html?clientId={{state.aad-app.clientId}}";
        if (!isRedirectUrlExist(aadJson.replyUrlsWithType, tabRedirectUrl2, "Spa")) {
            aadJson.replyUrlsWithType.push({
                url: tabRedirectUrl2,
                type: "Spa",
            });
        }
        const tabRedirectUrl3 = "{{state.aad-app.frontendEndpoint}}/blank-auth-end.html";
        if (!isRedirectUrlExist(aadJson.replyUrlsWithType, tabRedirectUrl3, "Spa")) {
            aadJson.replyUrlsWithType.push({
                url: tabRedirectUrl3,
                type: "Spa",
            });
        }
    }
    const teamsBotComponent = workflow_1.getComponent(projectSetting, constants_1.ComponentNames.TeamsBot);
    if (teamsBotComponent) {
        const botRedirectUrl = "{{state.aad-app.botEndpoint}}/auth-end.html";
        if (!isRedirectUrlExist(aadJson.replyUrlsWithType, botRedirectUrl, "Web")) {
            aadJson.replyUrlsWithType.push({
                url: botRedirectUrl,
                type: "Web",
            });
        }
    }
}
//# sourceMappingURL=generateAadManifestTemplate.js.map