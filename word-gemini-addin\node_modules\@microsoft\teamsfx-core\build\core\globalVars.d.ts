import { LogProvider, Stage, Tools } from "@microsoft/teamsfx-api";
export declare function isVsCallingCli(): boolean;
export declare let Logger: LogProvider;
export declare let currentStage: Stage;
export declare let TOOLS: Tools;
export declare let Locale: string | undefined;
export declare const isVS = false;
export declare function setTools(tools: Tools): void;
export declare function setLocale(locale?: string): void;
export declare function setCurrentStage(stage: Stage): void;
export declare class GlobalVars {
    isVS?: boolean;
    teamsAppId: string;
    m365TenantId: string;
    trackingId?: string;
}
export declare const globalVars: GlobalVars;
//# sourceMappingURL=globalVars.d.ts.map