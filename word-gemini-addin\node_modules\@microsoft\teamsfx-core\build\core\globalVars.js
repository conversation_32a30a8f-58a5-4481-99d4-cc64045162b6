"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.globalVars = exports.GlobalVars = exports.setCurrentStage = exports.setLocale = exports.setTools = exports.isVS = exports.Locale = exports.TOOLS = exports.currentStage = exports.Logger = exports.isVsCallingCli = void 0;
const constants_1 = require("../common/constants");
function featureFlagEnabled(flagName) {
    const flag = process.env[flagName];
    if (flag !== undefined && flag.toLowerCase() === "true") {
        return true;
    }
    else {
        return false;
    }
}
function isVsCallingCli() {
    return featureFlagEnabled(constants_1.FeatureFlagName.VSCallingCLI);
}
exports.isVsCallingCli = isVsCallingCli;
exports.isVS = false;
function setTools(tools) {
    exports.TOOLS = tools;
    exports.Logger = tools.logProvider;
}
exports.setTools = setTools;
function setLocale(locale) {
    exports.Locale = locale;
}
exports.setLocale = setLocale;
function setCurrentStage(stage) {
    exports.currentStage = stage;
}
exports.setCurrentStage = setCurrentStage;
class GlobalVars {
    constructor() {
        this.isVS = false;
        this.teamsAppId = "";
        this.m365TenantId = "";
    }
}
exports.GlobalVars = GlobalVars;
exports.globalVars = new GlobalVars();
//# sourceMappingURL=globalVars.js.map