import { HookContext, Middleware } from "@feathersjs/hooks";
import { FxError, Result } from "@microsoft/teamsfx-api";
export interface ErrorHandleOption {
    error?: FxError;
    startFn?: (ctx: HookContext) => Promise<Result<any, FxError>>;
    endFn?: (ctx: HookContext) => Promise<Result<any, FxError>>;
    telemetry?: {
        component: string;
        eventName?: string;
        properties?: Record<string, string>;
    };
}
export declare function CommonErrorHandlerMW(option?: ErrorHandleOption): Middleware;
//# sourceMappingURL=CommonErrorHandlerMW.d.ts.map