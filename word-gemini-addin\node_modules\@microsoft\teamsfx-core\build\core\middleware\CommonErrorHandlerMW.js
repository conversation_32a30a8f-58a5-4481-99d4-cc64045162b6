// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonErrorHandlerMW = void 0;
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const lodash_1 = require("lodash");
const telemetry_1 = require("../../common/telemetry");
const globalVars_1 = require("../globalVars");
function CommonErrorHandlerMW(option) {
    return async (ctx, next) => {
        try {
            if (option === null || option === void 0 ? void 0 : option.startFn) {
                await (option === null || option === void 0 ? void 0 : option.startFn(ctx));
            }
            if (option === null || option === void 0 ? void 0 : option.telemetry) {
                const event = option.telemetry.eventName
                    ? option.telemetry.eventName + "-start"
                    : lodash_1.kebabCase(ctx.method) + "-start";
                if (!option.telemetry.properties) {
                    option.telemetry.properties = {};
                    ctx.arguments.push(option.telemetry.properties);
                }
                telemetry_1.sendTelemetryEvent(option.telemetry.component, event, option.telemetry.properties);
            }
            await next();
            if (option === null || option === void 0 ? void 0 : option.endFn) {
                await (option === null || option === void 0 ? void 0 : option.endFn(ctx));
            }
            if (option === null || option === void 0 ? void 0 : option.telemetry) {
                const event = option.telemetry.eventName
                    ? option.telemetry.eventName
                    : lodash_1.kebabCase(ctx.method);
                const result = ctx.result;
                option.telemetry.properties[telemetry_1.TelemetryProperty.Success] = result.isOk()
                    ? telemetry_1.TelemetrySuccess.Yes
                    : telemetry_1.TelemetrySuccess.No;
                option.telemetry.properties[telemetry_1.TelemetryProperty.AppId] = globalVars_1.globalVars.teamsAppId || "";
                result.isOk()
                    ? telemetry_1.sendTelemetryEvent(option.telemetry.component, event, option.telemetry.properties)
                    : telemetry_1.sendTelemetryErrorEvent(option.telemetry.component, event, result.error, option.telemetry.properties);
            }
        }
        catch (e) {
            const error = (option === null || option === void 0 ? void 0 : option.error) ? option.error : teamsfx_api_1.assembleError(e);
            ctx.error = error;
            if (option === null || option === void 0 ? void 0 : option.endFn) {
                await (option === null || option === void 0 ? void 0 : option.endFn(ctx));
            }
            ctx.result = teamsfx_api_1.err(error);
            if (option === null || option === void 0 ? void 0 : option.telemetry) {
                const event = option.telemetry.eventName
                    ? option.telemetry.eventName
                    : lodash_1.kebabCase(ctx.method);
                option.telemetry.properties[telemetry_1.TelemetryProperty.Success] = telemetry_1.TelemetrySuccess.No;
                option.telemetry.properties[telemetry_1.TelemetryProperty.AppId] = globalVars_1.globalVars.teamsAppId || "";
                telemetry_1.sendTelemetryErrorEvent(option.telemetry.component, event, error, option.telemetry.properties);
            }
        }
    };
}
exports.CommonErrorHandlerMW = CommonErrorHandlerMW;
//# sourceMappingURL=CommonErrorHandlerMW.js.map