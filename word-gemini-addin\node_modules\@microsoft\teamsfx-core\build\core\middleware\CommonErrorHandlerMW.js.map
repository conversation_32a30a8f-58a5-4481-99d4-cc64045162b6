{"version": 3, "file": "CommonErrorHandlerMW.js", "sourceRoot": "", "sources": ["../../../src/core/middleware/CommonErrorHandlerMW.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC,YAAY,CAAC;;;AAGb,wDAA6E;AAC7E,mCAAmC;AACnC,sDAKgC;AAChC,8CAA2C;AAa3C,SAAgB,oBAAoB,CAAC,MAA0B;IAC7D,OAAO,KAAK,EAAE,GAAgB,EAAE,IAAkB,EAAE,EAAE;QACpD,IAAI;YACF,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,EAAE;gBACnB,MAAM,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,CAAC,GAAG,CAAC,CAAA,CAAC;aAC5B;YACD,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,EAAE;gBACrB,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS;oBACtC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,GAAG,QAAQ;oBACvC,CAAC,CAAC,kBAAS,CAAC,GAAG,CAAC,MAAO,CAAC,GAAG,QAAQ,CAAC;gBACtC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE;oBAChC,MAAM,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC;oBACjC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;iBACjD;gBACD,8BAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;aACpF;YACD,MAAM,IAAI,EAAE,CAAC;YACb,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,EAAE;gBACjB,MAAM,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,CAAC,GAAG,CAAC,CAAA,CAAC;aAC1B;YACD,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,EAAE;gBACrB,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS;oBACtC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS;oBAC5B,CAAC,CAAC,kBAAS,CAAC,GAAG,CAAC,MAAO,CAAC,CAAC;gBAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,MAA8B,CAAC;gBAClD,MAAM,CAAC,SAAS,CAAC,UAAW,CAAC,6BAAiB,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE;oBACrE,CAAC,CAAC,4BAAgB,CAAC,GAAG;oBACtB,CAAC,CAAC,4BAAgB,CAAC,EAAE,CAAC;gBACxB,MAAM,CAAC,SAAS,CAAC,UAAW,CAAC,6BAAiB,CAAC,KAAK,CAAC,GAAG,uBAAU,CAAC,UAAU,IAAI,EAAE,CAAC;gBACpF,MAAM,CAAC,IAAI,EAAE;oBACX,CAAC,CAAC,8BAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;oBACpF,CAAC,CAAC,mCAAuB,CACrB,MAAM,CAAC,SAAS,CAAC,SAAS,EAC1B,KAAK,EACL,MAAM,CAAC,KAAK,EACZ,MAAM,CAAC,SAAS,CAAC,UAAU,CAC5B,CAAC;aACP;SACF;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,KAAK,GAAG,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,EAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,2BAAa,CAAC,CAAC,CAAC,CAAC;YAC9D,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;YAClB,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,EAAE;gBACjB,MAAM,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,KAAK,CAAC,GAAG,CAAC,CAAA,CAAC;aAC1B;YACD,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,KAAK,CAAC,CAAC;YACxB,IAAI,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,EAAE;gBACrB,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS;oBACtC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS;oBAC5B,CAAC,CAAC,kBAAS,CAAC,GAAG,CAAC,MAAO,CAAC,CAAC;gBAC3B,MAAM,CAAC,SAAS,CAAC,UAAW,CAAC,6BAAiB,CAAC,OAAO,CAAC,GAAG,4BAAgB,CAAC,EAAE,CAAC;gBAC9E,MAAM,CAAC,SAAS,CAAC,UAAW,CAAC,6BAAiB,CAAC,KAAK,CAAC,GAAG,uBAAU,CAAC,UAAU,IAAI,EAAE,CAAC;gBACpF,mCAAuB,CACrB,MAAM,CAAC,SAAS,CAAC,SAAS,EAC1B,KAAK,EACL,KAAK,EACL,MAAM,CAAC,SAAS,CAAC,UAAU,CAC5B,CAAC;aACH;SACF;IACH,CAAC,CAAC;AACJ,CAAC;AA5DD,oDA4DC"}