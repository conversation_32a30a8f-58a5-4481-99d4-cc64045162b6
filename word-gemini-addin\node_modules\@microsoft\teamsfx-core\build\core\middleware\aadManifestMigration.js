"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.AadManifestMigrationMW = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const error_1 = require("../error");
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const path_1 = tslib_1.__importDefault(require("path"));
const telemetry_1 = require("../../common/telemetry");
const globalVars_1 = require("../globalVars");
const localizeUtils_1 = require("../../common/localizeUtils");
const folder_1 = require("../../folder");
const projectSettingsLoader_1 = require("./projectSettingsLoader");
const projectMigrator_1 = require("./projectMigrator");
const consolidateLocalRemote_1 = require("./consolidateLocalRemote");
const os = tslib_1.__importStar(require("os"));
const MigrationUtils_1 = require("./utils/MigrationUtils");
const LearnMore = localizeUtils_1.getLocalizedString("core.option.learnMore");
const LearnMoreLink = "https://aka.ms/teamsfx-aad-manifest";
let userCancelFlag = false;
const backupFolder = ".backup";
const methods = new Set(["getProjectConfig", "checkPermission"]);
const upgradeReportName = "aad-manifest-change-logs.md";
const AadManifestMigrationMW = async (ctx, next) => {
    if (await projectMigrator_1.needMigrateToArmAndMultiEnv(ctx)) {
        await next();
    }
    else if (await consolidateLocalRemote_1.needConsolidateLocalRemote(ctx)) {
        await next();
    }
    else if ((await MigrationUtils_1.needMigrateToAadManifest(ctx)) && checkMethod(ctx)) {
        await upgrade(ctx, next);
    }
    else {
        await next();
    }
};
exports.AadManifestMigrationMW = AadManifestMigrationMW;
async function upgrade(ctx, next) {
    try {
        await migrate(ctx);
        await next();
    }
    catch (error) {
        telemetry_1.sendTelemetryErrorEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectAadManifestMigrationError, teamsfx_api_1.assembleError(error, error_1.CoreSource));
        throw error;
    }
}
async function migrate(ctx) {
    telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectAadManifestMigrationStart);
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    const fileList = [];
    const loadRes = await projectSettingsLoader_1.loadProjectSettings(inputs, true);
    if (loadRes.isErr()) {
        ctx.result = teamsfx_api_1.err(loadRes.error);
        return false;
    }
    const projectSettings = loadRes.value;
    const projectSettingsPath = path_1.default.join(inputs.projectPath, ".fx", "configs", "projectSettings.json");
    try {
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectAadManifestMigrationAddAADTemplateStart);
        const projectSettingsJsonOld = await fs_extra_1.default.readJson(projectSettingsPath);
        // make sure this function use upgraded version of projectsettings for V3
        await MigrationUtils_1.generateAadManifest(inputs.projectPath, projectSettings);
        const aadManifestPath = path_1.default.join(inputs.projectPath, "templates", "appPackage", "aad.template.json");
        fileList.push(aadManifestPath);
        await fs_extra_1.default.writeJSON(projectSettingsPath, projectSettings, { spaces: 4, EOL: os.EOL });
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectAadManifestMigrationAddAADTemplate);
        // backup
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectAadManifestMigrationBackupStart);
        const backupPath = path_1.default.join(inputs.projectPath, backupFolder);
        await fs_extra_1.default.ensureDir(path_1.default.join(backupPath, ".fx", "configs"));
        await fs_extra_1.default.writeJSON(path_1.default.join(backupPath, ".fx", "configs", "projectSettings.json"), projectSettingsJsonOld, { spaces: 4, EOL: os.EOL });
        fileList.push(path_1.default.join(backupPath, ".fx", "configs", "projectSettings.json"));
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectAadManifestMigrationBackup);
    }
    catch (e) {
        for (const item of fileList) {
            await fs_extra_1.default.remove(item);
        }
        await fs_extra_1.default.writeJSON(projectSettingsPath, projectSettings, { spaces: 4, EOL: os.EOL });
        throw e;
    }
    postMigration(inputs);
    generateUpgradeReport(path_1.default.join(inputs.projectPath, backupFolder));
    telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectAadManifestMigration);
    return true;
}
function checkMethod(ctx) {
    if (ctx.method && methods.has(ctx.method) && userCancelFlag)
        return false;
    userCancelFlag = ctx.method != undefined && methods.has(ctx.method);
    return true;
}
async function postMigration(inputs) {
    if (inputs.platform === teamsfx_api_1.Platform.VSCode) {
        const res = await (globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.showMessage("info", localizeUtils_1.getLocalizedString("core.aadManifestMigration.outputMsg"), false, "OK", LearnMore));
        const answer = (res === null || res === void 0 ? void 0 : res.isOk()) ? res.value : undefined;
        if (answer === LearnMore) {
            globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.openUrl(LearnMoreLink);
        }
    }
    else {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.info(localizeUtils_1.getLocalizedString("core.aadManifestMigration.SuccessMessage", LearnMoreLink));
    }
}
async function generateUpgradeReport(backupFolder) {
    try {
        const target = path_1.default.join(backupFolder, upgradeReportName);
        const source = path_1.default.resolve(path_1.default.join(folder_1.getResourceFolder(), upgradeReportName));
        await fs_extra_1.default.copyFile(source, target);
    }
    catch (error) {
        // do nothing
    }
}
//# sourceMappingURL=aadManifestMigration.js.map