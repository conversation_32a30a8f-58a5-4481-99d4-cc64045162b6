{"version": 3, "file": "aadManifestMigration.js", "sourceRoot": "", "sources": ["../../../src/core/middleware/aadManifestMigration.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,wDAA8E;AAC9E,oCAAsC;AAEtC,gEAA0B;AAC1B,wDAAwB;AACxB,sDAKgC;AAEhC,8CAAsC;AACtC,8DAAgE;AAChE,yCAAiD;AACjD,mEAA8D;AAC9D,uDAAgE;AAChE,qEAAsE;AACtE,+CAAyB;AACzB,2DAAuF;AAEvF,MAAM,SAAS,GAAG,kCAAkB,CAAC,uBAAuB,CAAC,CAAC;AAC9D,MAAM,aAAa,GAAG,qCAAqC,CAAC;AAC5D,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3B,MAAM,YAAY,GAAG,SAAS,CAAC;AAC/B,MAAM,OAAO,GAAgB,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAC9E,MAAM,iBAAiB,GAAG,6BAA6B,CAAC;AAEjD,MAAM,sBAAsB,GAAe,KAAK,EACrD,GAAoB,EACpB,IAAkB,EAClB,EAAE;IACF,IAAI,MAAM,6CAA2B,CAAC,GAAG,CAAC,EAAE;QAC1C,MAAM,IAAI,EAAE,CAAC;KACd;SAAM,IAAI,MAAM,mDAA0B,CAAC,GAAG,CAAC,EAAE;QAChD,MAAM,IAAI,EAAE,CAAC;KACd;SAAM,IAAI,CAAC,MAAM,yCAAwB,CAAC,GAAG,CAAC,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;QACpE,MAAM,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;KAC1B;SAAM;QACL,MAAM,IAAI,EAAE,CAAC;KACd;AACH,CAAC,CAAC;AAbW,QAAA,sBAAsB,0BAajC;AAEF,KAAK,UAAU,OAAO,CAAC,GAAoB,EAAE,IAAkB;IAC7D,IAAI;QACF,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;QACnB,MAAM,IAAI,EAAE,CAAC;KACd;IAAC,OAAO,KAAK,EAAE;QACd,mCAAuB,CACrB,qBAAS,CAAC,IAAI,EACd,0BAAc,CAAC,gCAAgC,EAC/C,2BAAa,CAAC,KAAK,EAAE,kBAAU,CAAC,CACjC,CAAC;QACF,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAED,KAAK,UAAU,OAAO,CAAC,GAAoB;IACzC,8BAAkB,CAAC,qBAAS,CAAC,IAAI,EAAE,0BAAc,CAAC,gCAAgC,CAAC,CAAC;IACpF,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC;IACjE,MAAM,QAAQ,GAAkB,EAAE,CAAC;IACnC,MAAM,OAAO,GAAG,MAAM,2CAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACxD,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE;QACnB,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAChC,OAAO,KAAK,CAAC;KACd;IAED,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC;IACtC,MAAM,mBAAmB,GAAG,cAAI,CAAC,IAAI,CACnC,MAAM,CAAC,WAAqB,EAC5B,KAAK,EACL,SAAS,EACT,sBAAsB,CACvB,CAAC;IAEF,IAAI;QACF,8BAAkB,CAChB,qBAAS,CAAC,IAAI,EACd,0BAAc,CAAC,8CAA8C,CAC9D,CAAC;QACF,MAAM,sBAAsB,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;QAEtE,yEAAyE;QACzE,MAAM,oCAAmB,CAAC,MAAM,CAAC,WAAY,EAAE,eAAe,CAAC,CAAC;QAEhE,MAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAC/B,MAAM,CAAC,WAAqB,EAC5B,WAAW,EACX,YAAY,EACZ,mBAAmB,CACpB,CAAC;QACF,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAE/B,MAAM,kBAAE,CAAC,SAAS,CAAC,mBAAmB,EAAE,eAAe,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;QAErF,8BAAkB,CAAC,qBAAS,CAAC,IAAI,EAAE,0BAAc,CAAC,yCAAyC,CAAC,CAAC;QAE7F,SAAS;QACT,8BAAkB,CAAC,qBAAS,CAAC,IAAI,EAAE,0BAAc,CAAC,sCAAsC,CAAC,CAAC;QAE1F,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAqB,EAAE,YAAY,CAAC,CAAC;QACzE,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;QAE5D,MAAM,kBAAE,CAAC,SAAS,CAChB,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,sBAAsB,CAAC,EAC/D,sBAAsB,EACtB,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAC3B,CAAC;QACF,QAAQ,CAAC,IAAI,CAAC,cAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,sBAAsB,CAAC,CAAC,CAAC;QAC/E,8BAAkB,CAAC,qBAAS,CAAC,IAAI,EAAE,0BAAc,CAAC,iCAAiC,CAAC,CAAC;KACtF;IAAC,OAAO,CAAC,EAAE;QACV,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;YAC3B,MAAM,kBAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACvB;QACD,MAAM,kBAAE,CAAC,SAAS,CAAC,mBAAmB,EAAE,eAAe,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;QACrF,MAAM,CAAC,CAAC;KACT;IAED,aAAa,CAAC,MAAM,CAAC,CAAC;IAEtB,qBAAqB,CAAC,cAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAqB,EAAE,YAAY,CAAC,CAAC,CAAC;IAE7E,8BAAkB,CAAC,qBAAS,CAAC,IAAI,EAAE,0BAAc,CAAC,2BAA2B,CAAC,CAAC;IAE/E,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,WAAW,CAAC,GAAoB;IACvC,IAAI,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,cAAc;QAAE,OAAO,KAAK,CAAC;IAC1E,cAAc,GAAG,GAAG,CAAC,MAAM,IAAI,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACpE,OAAO,IAAI,CAAC;AACd,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,MAAc;IACzC,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,MAAM,EAAE;QACvC,MAAM,GAAG,GAAG,MAAM,CAAA,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,EAAE,CAAC,WAAW,CACrC,MAAM,EACN,kCAAkB,CAAC,qCAAqC,CAAC,EACzD,KAAK,EACL,IAAI,EACJ,SAAS,CACV,CAAA,CAAC;QACF,MAAM,MAAM,GAAG,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,EAAE,EAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QACnD,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SAClC;KACF;SAAM;QACL,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,IAAI,CACrB,kCAAkB,CAAC,0CAA0C,EAAE,aAAa,CAAC,CAC9E,CAAC;KACH;AACH,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,YAAoB;IACvD,IAAI;QACF,MAAM,MAAM,GAAG,cAAI,CAAC,IAAI,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;QAC1D,MAAM,MAAM,GAAG,cAAI,CAAC,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,0BAAiB,EAAE,EAAE,iBAAiB,CAAC,CAAC,CAAC;QAC/E,MAAM,kBAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACnC;IAAC,OAAO,KAAK,EAAE;QACd,aAAa;KACd;AACH,CAAC"}