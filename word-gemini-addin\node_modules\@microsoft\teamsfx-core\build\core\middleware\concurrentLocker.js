// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLockFolder = exports.ConcurrentLockerMW = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const fs = tslib_1.__importStar(require("fs-extra"));
const path = tslib_1.__importStar(require("path"));
const proper_lockfile_1 = require("proper-lockfile");
const globalVars_1 = require("../globalVars");
const telemetry_1 = require("../../common/telemetry");
const callback_1 = require("../callback");
const error_1 = require("../error");
const projectSettingsLoader_1 = require("./projectSettingsLoader");
const crypto_1 = tslib_1.__importDefault(require("crypto"));
const os = tslib_1.__importStar(require("os"));
const tools_1 = require("../../common/tools");
const projectSettingsHelper_1 = require("../../common/projectSettingsHelper");
let doingTask = undefined;
const ConcurrentLockerMW = async (ctx, next) => {
    var _a, _b;
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    if (projectSettingsLoader_1.shouldIgnored(ctx)) {
        await next();
        return;
    }
    if (!inputs.projectPath) {
        ctx.result = teamsfx_api_1.err(new error_1.NoProjectOpenedError());
        return;
    }
    if (!(await fs.pathExists(inputs.projectPath))) {
        ctx.result = teamsfx_api_1.err(new error_1.PathNotExistError(inputs.projectPath));
        return;
    }
    let configFolder = "";
    if (projectSettingsHelper_1.isValidProjectV3(inputs.projectPath)) {
        configFolder = path.join(inputs.projectPath);
    }
    else if (projectSettingsHelper_1.isValidProjectV2(inputs.projectPath)) {
        configFolder = path.join(inputs.projectPath, `.${teamsfx_api_1.ConfigFolderName}`);
    }
    else {
        ctx.result = teamsfx_api_1.err(new error_1.InvalidProjectError(configFolder));
        return;
    }
    const lockFileDir = getLockFolder(inputs.projectPath);
    const lockfilePath = path.join(lockFileDir, `${teamsfx_api_1.ConfigFolderName}.lock`);
    await fs.ensureDir(lockFileDir);
    const taskName = `${ctx.method} ${ctx.method === "executeUserTask" ? ctx.arguments[0].method : ""}`;
    let acquired = false;
    let retryNum = 0;
    for (let i = 0; i < 10; ++i) {
        try {
            await proper_lockfile_1.lock(configFolder, { lockfilePath: lockfilePath });
            acquired = true;
            globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.debug(`[core] success to acquire lock for task ${taskName} on: ${configFolder}`);
            for (const f of callback_1.CallbackRegistry.get(teamsfx_api_1.CoreCallbackEvent.lock)) {
                f();
            }
            try {
                doingTask = taskName;
                if (retryNum > 0) {
                    // failed for some try and finally success
                    telemetry_1.sendTelemetryErrorEvent(error_1.CoreSource, "concurrent-operation", new teamsfx_api_1.ConcurrentError(error_1.CoreSource), { retry: retryNum + "", acquired: "true", doing: doingTask, todo: taskName });
                }
                await next();
            }
            finally {
                await proper_lockfile_1.unlock(configFolder, { lockfilePath: lockfilePath });
                for (const f of callback_1.CallbackRegistry.get(teamsfx_api_1.CoreCallbackEvent.unlock)) {
                    f();
                }
                globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.debug(`[core] lock released on ${configFolder}`);
                doingTask = undefined;
            }
            break;
        }
        catch (e) {
            if (e["code"] === "ELOCKED") {
                await tools_1.waitSeconds(1);
                ++retryNum;
                continue;
            }
            throw e;
        }
    }
    if (!acquired) {
        const log = `[core] failed to acquire lock for task ${taskName} on: ${configFolder}`;
        if (inputs.loglevel && inputs.loglevel === "Debug") {
            (_a = globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider) === null || _a === void 0 ? void 0 : _a.debug(log);
        }
        else {
            (_b = globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider) === null || _b === void 0 ? void 0 : _b.error(log);
        }
        // failed for 10 times and finally failed
        telemetry_1.sendTelemetryErrorEvent(error_1.CoreSource, "concurrent-operation", new teamsfx_api_1.ConcurrentError(error_1.CoreSource), {
            retry: retryNum + "",
            acquired: "false",
            doing: doingTask || "",
            todo: taskName,
        });
        ctx.result = teamsfx_api_1.err(new teamsfx_api_1.ConcurrentError(error_1.CoreSource));
    }
};
exports.ConcurrentLockerMW = ConcurrentLockerMW;
function getLockFolder(projectPath) {
    return path.join(os.tmpdir(), `${teamsfx_api_1.ProductName}-${crypto_1.default.createHash("md5").update(projectPath).digest("hex")}`);
}
exports.getLockFolder = getLockFolder;
//# sourceMappingURL=concurrentLocker.js.map