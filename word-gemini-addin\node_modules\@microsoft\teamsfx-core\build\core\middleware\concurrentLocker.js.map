{"version": 3, "file": "concurrentLocker.js", "sourceRoot": "", "sources": ["../../../src/core/middleware/concurrentLocker.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC,YAAY,CAAC;;;;AAGb,wDAQgC;AAChC,qDAA+B;AAC/B,mDAA6B;AAC7B,qDAA+C;AAC/C,8CAAsC;AACtC,sDAAiE;AACjE,0CAA+C;AAC/C,oCAAoG;AACpG,mEAAwD;AACxD,4DAA4B;AAC5B,+CAAyB;AACzB,8CAAiD;AACjD,8EAAwF;AAExF,IAAI,SAAS,GAAuB,SAAS,CAAC;AACvC,MAAM,kBAAkB,GAAe,KAAK,EAAE,GAAgB,EAAE,IAAkB,EAAE,EAAE;;IAC3F,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC;IACjE,IAAI,qCAAa,CAAC,GAAG,CAAC,EAAE;QACtB,MAAM,IAAI,EAAE,CAAC;QACb,OAAO;KACR;IACD,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;QACvB,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,IAAI,4BAAoB,EAAE,CAAC,CAAC;QAC7C,OAAO;KACR;IACD,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE;QAC9C,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,IAAI,yBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;QAC5D,OAAO;KACR;IACD,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,IAAI,wCAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;QACxC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;KAC9C;SAAM,IAAI,wCAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;QAC/C,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,8BAAgB,EAAE,CAAC,CAAC;KACtE;SAAM;QACL,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,IAAI,2BAAmB,CAAC,YAAY,CAAC,CAAC,CAAC;QACxD,OAAO;KACR;IAED,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACtD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,8BAAgB,OAAO,CAAC,CAAC;IACxE,MAAM,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;IAChC,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC,MAAM,IAC5B,GAAG,CAAC,MAAM,KAAK,iBAAiB,CAAC,CAAC,CAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EACzE,EAAE,CAAC;IACH,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE;QAC3B,IAAI;YACF,MAAM,sBAAI,CAAC,YAAY,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC,CAAC;YACzD,QAAQ,GAAG,IAAI,CAAC;YAChB,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,KAAK,CACtB,2CAA2C,QAAQ,QAAQ,YAAY,EAAE,CAC1E,CAAC;YACF,KAAK,MAAM,CAAC,IAAI,2BAAgB,CAAC,GAAG,CAAC,+BAAiB,CAAC,IAAI,CAAC,EAAE;gBAC5D,CAAC,EAAE,CAAC;aACL;YACD,IAAI;gBACF,SAAS,GAAG,QAAQ,CAAC;gBACrB,IAAI,QAAQ,GAAG,CAAC,EAAE;oBAChB,0CAA0C;oBAC1C,mCAAuB,CACrB,kBAAU,EACV,sBAAsB,EACtB,IAAI,6BAAe,CAAC,kBAAU,CAAC,EAC/B,EAAE,KAAK,EAAE,QAAQ,GAAG,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,CAC7E,CAAC;iBACH;gBACD,MAAM,IAAI,EAAE,CAAC;aACd;oBAAS;gBACR,MAAM,wBAAM,CAAC,YAAY,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC,CAAC;gBAC3D,KAAK,MAAM,CAAC,IAAI,2BAAgB,CAAC,GAAG,CAAC,+BAAiB,CAAC,MAAM,CAAC,EAAE;oBAC9D,CAAC,EAAE,CAAC;iBACL;gBACD,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,KAAK,CAAC,2BAA2B,YAAY,EAAE,CAAC,CAAC;gBACpE,SAAS,GAAG,SAAS,CAAC;aACvB;YACD,MAAM;SACP;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;gBAC3B,MAAM,mBAAW,CAAC,CAAC,CAAC,CAAC;gBACrB,EAAE,QAAQ,CAAC;gBACX,SAAS;aACV;YACD,MAAM,CAAC,CAAC;SACT;KACF;IACD,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,GAAG,GAAG,0CAA0C,QAAQ,QAAQ,YAAY,EAAE,CAAC;QACrF,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,KAAK,OAAO,EAAE;YAClD,MAAA,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,0CAAE,KAAK,CAAC,GAAG,CAAC,CAAC;SAChC;aAAM;YACL,MAAA,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,0CAAE,KAAK,CAAC,GAAG,CAAC,CAAC;SAChC;QACD,yCAAyC;QACzC,mCAAuB,CAAC,kBAAU,EAAE,sBAAsB,EAAE,IAAI,6BAAe,CAAC,kBAAU,CAAC,EAAE;YAC3F,KAAK,EAAE,QAAQ,GAAG,EAAE;YACpB,QAAQ,EAAE,OAAO;YACjB,KAAK,EAAE,SAAS,IAAI,EAAE;YACtB,IAAI,EAAE,QAAQ;SACf,CAAC,CAAC;QACH,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,IAAI,6BAAe,CAAC,kBAAU,CAAC,CAAC,CAAC;KACnD;AACH,CAAC,CAAC;AAxFW,QAAA,kBAAkB,sBAwF7B;AAEF,SAAgB,aAAa,CAAC,WAAmB;IAC/C,OAAO,IAAI,CAAC,IAAI,CACd,EAAE,CAAC,MAAM,EAAE,EACX,GAAG,yBAAW,IAAI,gBAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC/E,CAAC;AACJ,CAAC;AALD,sCAKC"}