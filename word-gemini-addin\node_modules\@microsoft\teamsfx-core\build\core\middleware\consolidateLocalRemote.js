"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.needConsolidateLocalRemote = exports.ProjectConsolidateMW = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const tools_1 = require("../../common/tools");
const environment_1 = require("../environment");
const error_1 = require("../error");
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const os = tslib_1.__importStar(require("os"));
const path_1 = tslib_1.__importDefault(require("path"));
const utils_1 = require("../../component/resource/appManifest/utils/utils");
const telemetry_1 = require("../../common/telemetry");
const globalVars_1 = require("../globalVars");
const localizeUtils_1 = require("../../common/localizeUtils");
const folder_1 = require("../../folder");
const projectSettingsLoader_1 = require("./projectSettingsLoader");
const projectMigrator_1 = require("./projectMigrator");
const util = tslib_1.__importStar(require("util"));
const constants_1 = require("../../component/resource/spfx/utils/constants");
const MigrationUtils_1 = require("./utils/MigrationUtils");
const ManifestUtils_1 = require("../../component/resource/appManifest/utils/ManifestUtils");
const upgradeButton = localizeUtils_1.getLocalizedString("core.option.upgrade");
const LearnMore = localizeUtils_1.getLocalizedString("core.option.learnMore");
const LearnMoreLinkWithAADManifest = "https://aka.ms/teamsfx-unify-config-and-aad-manifest-guide";
const UnifyManifestLearMoreLink = "https://aka.ms/teamsfx-unify-local-remote-manifest-guide";
let userCancelFlag = false;
const backupFolder = ".backup";
const methods = new Set(["getProjectConfig", "checkPermission"]);
const upgradeWithAadManifestReportName = "unify-config-and-aad-manifest-change-logs.md";
const componentIdRegex = /(?<=componentId=)([a-z0-9-]*)(?=%26)/;
const manifestRegex = /{{{.*}}}|{{.*}}|{.*}/g;
const ignoreKeys = new Set([
    "name",
    "contentUrl",
    "configurationUrl",
    "manifestVersion",
    "$schema",
    "description",
]);
let needMigrateAadManifest = false;
const ProjectConsolidateMW = async (ctx, next) => {
    if (await projectMigrator_1.needMigrateToArmAndMultiEnv(ctx)) {
        next();
    }
    else if ((await needConsolidateLocalRemote(ctx)) && checkMethod(ctx)) {
        needMigrateAadManifest = await MigrationUtils_1.needMigrateToAadManifest(ctx);
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateNotificationStart, {
            [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
        });
        let showModal = true;
        if (ctx.method && methods.has(ctx.method)) {
            showModal = false;
        }
        if (showModal) {
            await upgrade(ctx, next, true);
        }
        else {
            upgrade(ctx, next, false);
            await next();
        }
    }
    else {
        await next();
    }
};
exports.ProjectConsolidateMW = ProjectConsolidateMW;
async function upgrade(ctx, next, showModal) {
    let answer = undefined;
    do {
        const res = await (globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.showMessage("warn", needMigrateAadManifest
            ? localizeUtils_1.getLocalizedString("core.consolidateLocalRemoteWithAadManifest.Message")
            : localizeUtils_1.getLocalizedString("core.consolidateLocalRemote.Message"), showModal, upgradeButton, LearnMore));
        answer = (res === null || res === void 0 ? void 0 : res.isOk()) ? res.value : undefined;
        if (answer === LearnMore) {
            globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.openUrl(LearnMoreLinkWithAADManifest);
        }
    } while (answer === LearnMore);
    if (!answer || answer != upgradeButton) {
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateNotification, {
            [telemetry_1.TelemetryProperty.Status]: telemetry_1.ProjectMigratorStatus.Cancel,
            [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
        });
        ctx.result = teamsfx_api_1.err(error_1.ConsolidateCanceledError());
        outputCancelMessage(ctx);
        return;
    }
    telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateNotification, {
        [telemetry_1.TelemetryProperty.Status]: telemetry_1.ProjectMigratorStatus.OK,
        [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
    });
    try {
        await consolidateLocalRemote(ctx);
        if (showModal) {
            await next();
        }
    }
    catch (error) {
        telemetry_1.sendTelemetryErrorEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateError, teamsfx_api_1.assembleError(error, error_1.CoreSource), {
            [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
        });
        throw error;
    }
}
// check if manifest.template.json exist
async function needConsolidateLocalRemote(ctx) {
    const lastArg = ctx.arguments[ctx.arguments.length - 1];
    const inputs = lastArg === ctx ? ctx.arguments[ctx.arguments.length - 2] : lastArg;
    if (!inputs.projectPath) {
        return false;
    }
    const fxExist = await fs_extra_1.default.pathExists(path_1.default.join(inputs.projectPath, ".fx"));
    if (!fxExist) {
        return false;
    }
    const appDir = await tools_1.getAppDirectory(inputs.projectPath);
    const consolidateManifestExist = await fs_extra_1.default.pathExists(path_1.default.join(appDir, "manifest.template.json"));
    if (!consolidateManifestExist) {
        return true;
    }
    return false;
}
exports.needConsolidateLocalRemote = needConsolidateLocalRemote;
function outputCancelMessage(ctx) {
    globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Upgrade cancelled.`);
    const lastArg = ctx.arguments[ctx.arguments.length - 1];
    const inputs = lastArg === ctx ? ctx.arguments[ctx.arguments.length - 2] : lastArg;
    if (inputs.platform === teamsfx_api_1.Platform.VSCode) {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Notice upgrade to new configuration files is a must-have to continue to use current version Teams Toolkit. If you are not ready to upgrade and want to continue to use the old version Teams Toolkit, please find Teams Toolkit in Extension and install the version <= 3.7.0`);
    }
    else {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Notice upgrade to new configuration files is a must-have to continue to use current version Teams Toolkit CLI. If you want to upgrade, please trigger this command again.`);
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] If you are not ready to upgrade and want to continue to use the old version Teams Toolkit CLI, please install the version <= 3.7.0`);
    }
}
async function consolidateLocalRemote(ctx) {
    telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateUpgradeStart, {
        [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
    });
    const lastArg = ctx.arguments[ctx.arguments.length - 1];
    const inputs = lastArg === ctx ? ctx.arguments[ctx.arguments.length - 2] : lastArg;
    const fileList = [];
    const removeMap = new Map();
    const loadRes = await projectSettingsLoader_1.loadProjectSettings(inputs, true);
    if (loadRes.isErr()) {
        ctx.result = teamsfx_api_1.err(loadRes.error);
        return false;
    }
    const projectSettings = loadRes.value;
    const projectSettingsPath = path_1.default.join(inputs.projectPath, ".fx", "configs", "projectSettings.json");
    try {
        // add local environment
        const appName = utils_1.getLocalAppName(projectSettings.appName);
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateAddLocalEnvStart, {
            [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
        });
        const newEnvConfig = environment_1.environmentManager.newEnvConfigData(appName);
        const writeEnvResult = await environment_1.environmentManager.writeEnvConfig(inputs.projectPath, newEnvConfig, environment_1.environmentManager.getLocalEnvName());
        if (writeEnvResult.isErr()) {
            throw teamsfx_api_1.err(writeEnvResult.error);
        }
        fileList.push(path_1.default.join(inputs.projectPath, ".fx", "configs", "env.local.json"));
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateAddLocalEnv, {
            [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
        });
        // add consolidate manifest
        let manifest;
        const remoteManifestFile = path_1.default.join(inputs.projectPath, "templates", "appPackage", "manifest.remote.template.json");
        const remoteManifestExist = await fs_extra_1.default.pathExists(remoteManifestFile);
        if (remoteManifestExist) {
            if (tools_1.isSPFxProject(projectSettings)) {
                telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateAddSPFXManifestStart);
                const manifestTemplatePath = await ManifestUtils_1.getManifestTemplatePath(inputs.projectPath);
                const manifestString = (await fs_extra_1.default.readFile(remoteManifestFile)).toString();
                manifest = JSON.parse(manifestString);
                let componentId = "";
                if ((manifest === null || manifest === void 0 ? void 0 : manifest.staticTabs) && manifest.staticTabs.length > 0) {
                    manifest.staticTabs.forEach((item) => {
                        componentId = item.entityId;
                        if ((item.contentUrl && componentId === undefined) || componentId === "") {
                            componentId = replaceSPFxComponentId(item.contentUrl);
                        }
                        const contentUrl = util.format(constants_1.ManifestTemplate.REMOTE_CONTENT_URL, componentId, componentId);
                        item.contentUrl = contentUrl;
                    });
                }
                if ((manifest === null || manifest === void 0 ? void 0 : manifest.configurableTabs) && manifest.configurableTabs.length > 0) {
                    manifest.configurableTabs.forEach((item) => {
                        if ((item.configurationUrl && componentId === undefined) || componentId === "") {
                            componentId = replaceSPFxComponentId(item.configurationUrl);
                        }
                        const configurationUrl = util.format(constants_1.ManifestTemplate.REMOTE_CONFIGURATION_URL, componentId, componentId);
                        item.configurationUrl = configurationUrl;
                    });
                }
                await fs_extra_1.default.writeFile(manifestTemplatePath, JSON.stringify(manifest, null, 4));
                telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateAddSPFXManifest);
            }
            else {
                telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateCopyAzureManifestStart, {
                    [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
                });
                const manifestTemplatePath = await ManifestUtils_1.getManifestTemplatePath(inputs.projectPath);
                await fs_extra_1.default.copyFile(remoteManifestFile, manifestTemplatePath);
                telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateCopyAzureManifest, {
                    [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
                });
            }
        }
        fileList.push(path_1.default.join(inputs.projectPath, "templates", "appPackage", "template.manifest.json"));
        // copy and remove old configs
        const backupPath = path_1.default.join(inputs.projectPath, backupFolder);
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateBackupConfigStart, {
            [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
        });
        const localSettingsFile = path_1.default.join(inputs.projectPath, ".fx", "configs", "localSettings.json");
        let moveFiles = "";
        if (await fs_extra_1.default.pathExists(localSettingsFile)) {
            await fs_extra_1.default.copy(localSettingsFile, path_1.default.join(backupPath, ".fx", "configs", "localSettings.json"), { overwrite: true });
            await fs_extra_1.default.remove(localSettingsFile);
            moveFiles += "localSettings.json,";
            removeMap.set(path_1.default.join(backupPath, ".fx", "configs", "localSettings.json"), localSettingsFile);
        }
        const localManifestFile = path_1.default.join(inputs.projectPath, "templates", "appPackage", "manifest.local.template.json");
        if ((await fs_extra_1.default.pathExists(localManifestFile)) && (await fs_extra_1.default.pathExists(remoteManifestFile))) {
            await compareLocalAndRemoteManifest(localManifestFile, remoteManifestFile);
        }
        if (await fs_extra_1.default.pathExists(localManifestFile)) {
            await fs_extra_1.default.copy(localManifestFile, path_1.default.join(backupPath, "templates", "appPackage", "manifest.local.template.json"), { overwrite: true });
            await fs_extra_1.default.remove(localManifestFile);
            moveFiles += "manifest.local.template.json,";
            removeMap.set(path_1.default.join(backupPath, "templates", "appPackage", "manifest.local.template.json"), localManifestFile);
        }
        if (await fs_extra_1.default.pathExists(remoteManifestFile)) {
            await fs_extra_1.default.copy(remoteManifestFile, path_1.default.join(backupPath, "templates", "appPackage", "manifest.remote.template.json"), { overwrite: true });
            await fs_extra_1.default.remove(remoteManifestFile);
            moveFiles += "manifest.remote.template.json,";
            removeMap.set(path_1.default.join(backupPath, "templates", "appPackage", "manifest.remote.template.json"), remoteManifestFile);
        }
        if (needMigrateAadManifest) {
            await MigrationUtils_1.generateAadManifest(inputs.projectPath, projectSettings);
            const aadManifestPath = path_1.default.join(inputs.projectPath, "templates", "appPackage", "aad.template.json");
            fileList.push(aadManifestPath);
            await fs_extra_1.default.writeJSON(projectSettingsPath, projectSettings, { spaces: 4, EOL: os.EOL });
            moveFiles += "projectSettings.json,";
            await fs_extra_1.default.ensureDir(path_1.default.join(backupPath, ".fx", "configs"));
            await fs_extra_1.default.writeJSON(path_1.default.join(backupPath, ".fx", "configs", "projectSettings.json"), projectSettings, { spaces: 4, EOL: os.EOL });
            removeMap.set(path_1.default.join(backupPath, ".fx", "configs", "projectSettings.json"), projectSettingsPath);
        }
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateBackupConfig, {
            [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
        });
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateUpgrade, {
            [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
        });
        postConsolidate(inputs.projectPath, ctx, inputs, backupFolder, moveFiles);
    }
    catch (e) {
        for (const item of removeMap.entries()) {
            await fs_extra_1.default.copy(item[0], item[1]);
        }
        for (const item of fileList) {
            await fs_extra_1.default.remove(item);
        }
        await fs_extra_1.default.remove(path_1.default.join(inputs.projectPath, backupFolder));
        throw e;
    }
    generateUpgradeReport(path_1.default.join(inputs.projectPath, backupFolder));
    return true;
}
function checkMethod(ctx) {
    if (ctx.method && methods.has(ctx.method) && userCancelFlag)
        return false;
    userCancelFlag = ctx.method != undefined && methods.has(ctx.method);
    return true;
}
async function postConsolidate(projectPath, ctx, inputs, backupFolder, moveFiles) {
    telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateGuideStart, {
        [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
    });
    await updateGitIgnore(projectPath, globalVars_1.TOOLS.logProvider, backupFolder);
    if (moveFiles.length > 0) {
        moveFiles = moveFiles.substring(0, moveFiles.length - 1);
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Upgrade success! Old ${moveFiles} have been backed up to the .backup folder and you can delete it.`);
    }
    if (inputs.platform !== teamsfx_api_1.Platform.VSCode) {
        const msg = needMigrateAadManifest
            ? localizeUtils_1.getLocalizedString("core.consolidateLocalRemoteWithAadManifest.SuccessMessage")
            : localizeUtils_1.getLocalizedString("core.consolidateLocalRemote.SuccessMessage");
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.info(msg);
    }
}
async function updateGitIgnore(projectPath, log, backupFolder) {
    // add config.local.json to .gitignore
    await projectMigrator_1.addPathToGitignore(projectPath, `${projectPath}/.${teamsfx_api_1.ConfigFolderName}/${teamsfx_api_1.InputConfigsFolderName}/config.local.json`, log);
    // add state.local.json to .gitignore
    await projectMigrator_1.addPathToGitignore(projectPath, `${projectPath}/.${teamsfx_api_1.ConfigFolderName}/${teamsfx_api_1.StatesFolderName}/state.local.json`, log);
    if (backupFolder) {
        await projectMigrator_1.addPathToGitignore(projectPath, `${projectPath}/${backupFolder}`, log);
    }
}
async function generateUpgradeReport(backupFolder) {
    try {
        const reportName = upgradeWithAadManifestReportName;
        const target = path_1.default.join(backupFolder, reportName);
        const source = path_1.default.resolve(path_1.default.join(folder_1.getResourceFolder(), reportName));
        await fs_extra_1.default.copyFile(source, target);
    }
    catch (error) {
        // do nothing
    }
}
function replaceSPFxComponentId(content) {
    const match = componentIdRegex.exec(content);
    if (match) {
        return match[0];
    }
    return "";
}
async function compareLocalAndRemoteManifest(localManifestFile, remoteManifestFile) {
    try {
        const localManifestString = (await fs_extra_1.default.readFile(localManifestFile))
            .toString()
            .replace(manifestRegex, "");
        const remoteManifestString = (await fs_extra_1.default.readFile(remoteManifestFile))
            .toString()
            .replace(manifestRegex, "");
        const localManifestJson = JSON.parse(localManifestString);
        const remoteManifestJson = JSON.parse(remoteManifestString);
        if (!diff(localManifestJson, remoteManifestJson)) {
            notifyToUpdateManifest();
        }
    }
    catch (error) {
        telemetry_1.sendTelemetryErrorEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectConsolidateCheckManifestError, teamsfx_api_1.assembleError(error, error_1.CoreSource), {
            [telemetry_1.TelemetryProperty.NeedMigrateAadManifest]: needMigrateAadManifest ? "true" : "false",
        });
    }
}
async function notifyToUpdateManifest() {
    const res = await (globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.showMessage("warn", localizeUtils_1.getLocalizedString("core.consolidateLocalRemote.DifferentManifest"), false, "OK", LearnMore));
    const answer = (res === null || res === void 0 ? void 0 : res.isOk()) ? res.value : undefined;
    if (answer === LearnMore) {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.openUrl(UnifyManifestLearMoreLink);
    }
}
function diff(a, b) {
    const keys = Object.keys(a);
    const bKeys = Object.keys(b);
    if (keys.length != bKeys.length) {
        return false;
    }
    let aValue, bValue, key;
    for (key of keys) {
        if (ignoreKeys.has(key)) {
            continue;
        }
        aValue = a[key];
        bValue = b[key];
        if (isObject(aValue) && isObject(bValue)) {
            if (!diff(aValue, bValue)) {
                return false;
            }
        }
        else {
            if (aValue !== bValue) {
                return false;
            }
        }
    }
    return true;
}
function isObject(o) {
    return typeof o === "object" && !!o;
}
//# sourceMappingURL=consolidateLocalRemote.js.map