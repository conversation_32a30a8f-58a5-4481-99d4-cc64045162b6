import { Middleware } from "@feathersjs/hooks/lib";
import { FxError, Inputs, Json, ProjectSettings, QTreeNode, Result, Tools, v2, v3 } from "@microsoft/teamsfx-api";
import { CoreHookContext } from "../types";
export declare let lastUsedEnv: string | undefined;
export declare type CreateEnvCopyInput = {
    targetEnvName: string;
    sourceEnvName: string;
};
export declare function EnvInfoLoaderMW_V3(skip: boolean, ignoreLocalEnv?: boolean): Middleware;
export declare function upgradeProgrammingLanguage(solutionConfig: Json, projectSettings: ProjectSettings): void;
export declare function upgradeDefaultFunctionName(solutionConfig: Json, projectSettings: ProjectSettings): void;
export declare function loadEnvInfoV3(inputs: v2.InputsWithProjectPath, projectSettings: ProjectSettings, targetEnvName?: string, ignoreEnvInfo?: boolean): Promise<Result<v3.EnvInfoV3, FxError>>;
export declare function getTargetEnvName(skip: boolean, inputs: Inputs, ctx: CoreHookContext): Promise<Result<string, FxError>>;
export declare function askTargetEnvironment(tools: Tools, inputs: Inputs): Promise<Result<string, FxError>>;
export declare function askNewEnvironment(ctx: CoreHookContext, inputs: Inputs): Promise<CreateEnvCopyInput | undefined>;
export declare function useUserSetEnv(projectPath: string, env: string): Promise<Result<string, FxError>>;
export declare function getQuestionsForTargetEnv(inputs: Inputs, lastUsed?: string): Promise<Result<QTreeNode | undefined, FxError>>;
//# sourceMappingURL=envInfoLoaderV3.d.ts.map