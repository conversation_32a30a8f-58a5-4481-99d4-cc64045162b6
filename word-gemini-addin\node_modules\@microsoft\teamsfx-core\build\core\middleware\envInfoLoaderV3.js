"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.getQuestionsForTargetEnv = exports.useUserSetEnv = exports.askNewEnvironment = exports.askTargetEnvironment = exports.getTargetEnvName = exports.loadEnvInfoV3 = exports.upgradeDefaultFunctionName = exports.upgradeProgrammingLanguage = exports.EnvInfoLoaderMW_V3 = exports.lastUsedEnv = void 0;
const tslib_1 = require("tslib");
const lodash_1 = tslib_1.__importDefault(require("lodash"));
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const tools_1 = require("../../common/tools");
const constants_1 = require("../../component/constants");
const envMW_1 = require("../../component/middleware/envMW");
const crypto_1 = require("../crypto");
const environment_1 = require("../environment");
const error_1 = require("../error");
const globalVars_1 = require("../globalVars");
const question_1 = require("../question");
const projectSettingsLoader_1 = require("./projectSettingsLoader");
const newTargetEnvNameOption = "+ new environment";
const lastUsedMark = " (last used)";
function EnvInfoLoaderMW_V3(skip, ignoreLocalEnv = false) {
    return async (ctx, next) => {
        var _a, _b, _c, _d;
        if (projectSettingsLoader_1.shouldIgnored(ctx)) {
            await next();
            return;
        }
        const inputs = ctx.arguments[ctx.arguments.length - 1];
        if (tools_1.isV3Enabled()) {
            const envBefore = lodash_1.default.cloneDeep(process.env);
            try {
                await envMW_1.envLoaderMWImpl(inputs.ignoreLocalEnv || ignoreLocalEnv ? false : true, ctx, next);
                return;
            }
            finally {
                const keys = Object.keys(process.env);
                for (const k of keys) {
                    if (!(k in envBefore)) {
                        delete process.env[k];
                    }
                    else {
                        process.env[k] = envBefore[k];
                    }
                }
            }
        }
        if (!ctx.projectSettings) {
            ctx.result = teamsfx_api_1.err(error_1.ProjectSettingsUndefinedError());
            return;
        }
        if (!inputs.projectPath) {
            ctx.result = teamsfx_api_1.err(new error_1.NoProjectOpenedError());
            return;
        }
        // make sure inputs.env always has value so telemetry can use it.
        if (inputs.stage === teamsfx_api_1.Stage.debug)
            inputs.ignoreEnvInfo = false; // for local debug v3, envInfo should not be ignored
        const envRes = await getTargetEnvName(skip, inputs, ctx);
        if (envRes.isErr()) {
            ctx.result = teamsfx_api_1.err(envRes.error);
            return;
        }
        inputs.env = envRes.value;
        const result = await loadEnvInfoV3(inputs, ctx.projectSettings, inputs.env, skip || inputs.ignoreEnvInfo);
        if (result.isErr()) {
            ctx.result = teamsfx_api_1.err(result.error);
            return;
        }
        ctx.envInfoV3 = result.value;
        upgradeProgrammingLanguage(ctx.envInfoV3.state, ctx.projectSettings);
        upgradeDefaultFunctionName(ctx.envInfoV3.state, ctx.projectSettings);
        // set globalVars for teamsAppId and m365TenantId
        const appManifestKey = constants_1.ComponentNames.AppManifest;
        globalVars_1.globalVars.teamsAppId = (_b = (_a = ctx.envInfoV3.state) === null || _a === void 0 ? void 0 : _a[appManifestKey]) === null || _b === void 0 ? void 0 : _b.teamsAppId;
        globalVars_1.globalVars.m365TenantId = (_d = (_c = ctx.envInfoV3.state) === null || _c === void 0 ? void 0 : _c[appManifestKey]) === null || _d === void 0 ? void 0 : _d.m365TenantId;
        await next();
    };
}
exports.EnvInfoLoaderMW_V3 = EnvInfoLoaderMW_V3;
function upgradeProgrammingLanguage(solutionConfig, projectSettings) {
    var _a;
    const programmingLanguage = (_a = solutionConfig.solution) === null || _a === void 0 ? void 0 : _a.programmingLanguage;
    if (programmingLanguage) {
        // add programmingLanguage in project settings
        projectSettings.programmingLanguage = programmingLanguage;
        // remove programmingLanguage in solution config
        solutionConfig.solution.programmingLanguage = undefined;
    }
}
exports.upgradeProgrammingLanguage = upgradeProgrammingLanguage;
function upgradeDefaultFunctionName(solutionConfig, projectSettings) {
    var _a;
    // upgrade defaultFunctionName if exists.
    const defaultFunctionName = (_a = solutionConfig.solution) === null || _a === void 0 ? void 0 : _a.defaultFunctionName;
    if (defaultFunctionName) {
        // add defaultFunctionName in project settings
        projectSettings.defaultFunctionName = defaultFunctionName;
        // remove defaultFunctionName in function plugin's config
        solutionConfig.solution.defaultFunctionName = undefined;
    }
}
exports.upgradeDefaultFunctionName = upgradeDefaultFunctionName;
async function loadEnvInfoV3(inputs, projectSettings, targetEnvName, ignoreEnvInfo = false) {
    const cryptoProvider = new crypto_1.LocalCrypto(projectSettings.projectId);
    let envInfo;
    // in pre-multi-env case, envInfo is always loaded.
    if (ignoreEnvInfo) {
        envInfo = environment_1.newEnvInfoV3();
        envInfo.envName = "";
    }
    else {
        // ensure backwards compatibility:
        // project id will be generated for previous TeamsFx project.
        // Decrypting the secrets in *.userdata with generated project id works because secrets doesn't have prefix.
        const envDataResult = await environment_1.environmentManager.loadEnvInfo(inputs.projectPath, cryptoProvider, targetEnvName, true);
        if (envDataResult.isErr()) {
            return teamsfx_api_1.err(envDataResult.error);
        }
        envInfo = envDataResult.value;
    }
    return teamsfx_api_1.ok(envInfo);
}
exports.loadEnvInfoV3 = loadEnvInfoV3;
async function getTargetEnvName(skip, inputs, ctx) {
    let targetEnvName;
    if (!skip && !inputs.ignoreEnvInfo) {
        // TODO: This is a workaround for collabrator & manifest preview feature to programmatically load an env in extension.
        if (inputs.env) {
            const result = await useUserSetEnv(inputs.projectPath, inputs.env);
            if (result.isErr()) {
                ctx.result = result;
                return teamsfx_api_1.err(result.error);
            }
            targetEnvName = result.value;
        }
        else {
            const result = await askTargetEnvironment(globalVars_1.TOOLS, inputs);
            if (result.isErr()) {
                ctx.result = teamsfx_api_1.err(result.error);
                return teamsfx_api_1.err(result.error);
            }
            targetEnvName = result.value;
            globalVars_1.TOOLS.logProvider.info(`[${targetEnvName}] is selected as the target environment to ${ctx.method}`);
            exports.lastUsedEnv = targetEnvName;
        }
    }
    else {
        targetEnvName = environment_1.environmentManager.getDefaultEnvName();
    }
    return teamsfx_api_1.ok(targetEnvName);
}
exports.getTargetEnvName = getTargetEnvName;
async function askTargetEnvironment(tools, inputs) {
    const getQuestionRes = await getQuestionsForTargetEnv(inputs, exports.lastUsedEnv);
    if (getQuestionRes.isErr()) {
        tools.logProvider.error(`[core:env] failed to get questions for target environment: ${getQuestionRes.error.message}`);
        return teamsfx_api_1.err(getQuestionRes.error);
    }
    tools.logProvider.debug(`[core:env] success to get questions for target environment.`);
    const node = getQuestionRes.value;
    if (node) {
        const res = await teamsfx_api_1.traverse(node, inputs, tools.ui);
        if (res.isErr()) {
            tools.logProvider.debug(`[core:env] failed to run question model for target environment.`);
            return teamsfx_api_1.err(res.error);
        }
    }
    if (!inputs.targetEnvName) {
        return teamsfx_api_1.err(teamsfx_api_1.UserCancelError);
    }
    let targetEnvName = inputs.targetEnvName;
    if (targetEnvName.endsWith(lastUsedMark)) {
        targetEnvName = targetEnvName.slice(0, targetEnvName.indexOf(lastUsedMark));
    }
    return teamsfx_api_1.ok(targetEnvName);
}
exports.askTargetEnvironment = askTargetEnvironment;
async function askNewEnvironment(ctx, inputs) {
    const getQuestionRes = await getQuestionsForNewEnv(inputs, exports.lastUsedEnv);
    if (getQuestionRes.isErr()) {
        globalVars_1.TOOLS.logProvider.error(`[core:env] failed to get questions for target environment: ${getQuestionRes.error.message}`);
        ctx.result = teamsfx_api_1.err(getQuestionRes.error);
        return undefined;
    }
    globalVars_1.TOOLS.logProvider.debug(`[core:env] success to get questions for target environment.`);
    const node = getQuestionRes.value;
    if (node) {
        const res = await teamsfx_api_1.traverse(node, inputs, globalVars_1.TOOLS.ui);
        if (res.isErr()) {
            globalVars_1.TOOLS.logProvider.debug(`[core:env] failed to run question model for target environment.`);
            ctx.result = teamsfx_api_1.err(res.error);
            return undefined;
        }
    }
    const sourceEnvName = inputs.sourceEnvName;
    let selectedEnvName;
    if (sourceEnvName === null || sourceEnvName === void 0 ? void 0 : sourceEnvName.endsWith(lastUsedMark)) {
        selectedEnvName = sourceEnvName.slice(0, sourceEnvName.indexOf(lastUsedMark));
    }
    else {
        selectedEnvName = sourceEnvName;
    }
    return {
        targetEnvName: inputs.newTargetEnvName,
        sourceEnvName: selectedEnvName,
    };
}
exports.askNewEnvironment = askNewEnvironment;
async function useUserSetEnv(projectPath, env) {
    let checkEnv = await environment_1.environmentManager.checkEnvExist(projectPath, env);
    if (checkEnv.isErr()) {
        return teamsfx_api_1.err(checkEnv.error);
    }
    let envExists = checkEnv.value;
    if (!envExists) {
        if (env === environment_1.environmentManager.getLocalEnvName()) {
            await environment_1.environmentManager.createLocalEnv(projectPath);
            checkEnv = await environment_1.environmentManager.checkEnvExist(projectPath, env);
            if (checkEnv.isErr()) {
                return teamsfx_api_1.err(checkEnv.error);
            }
            envExists = checkEnv.value;
        }
        if (!envExists) {
            return teamsfx_api_1.err(error_1.ProjectEnvNotExistError(env));
        }
    }
    return teamsfx_api_1.ok(env);
}
exports.useUserSetEnv = useUserSetEnv;
async function getQuestionsForTargetEnv(inputs, lastUsed) {
    if (!inputs.projectPath) {
        return teamsfx_api_1.err(new error_1.NoProjectOpenedError());
    }
    const envProfilesResult = await environment_1.environmentManager.listRemoteEnvConfigs(inputs.projectPath, true);
    if (envProfilesResult.isErr()) {
        return teamsfx_api_1.err(envProfilesResult.error);
    }
    const envList = reOrderEnvironments(envProfilesResult.value, lastUsed);
    const selectEnv = question_1.QuestionSelectTargetEnvironment();
    selectEnv.staticOptions = envList;
    const node = new teamsfx_api_1.QTreeNode(selectEnv);
    const childNode = new teamsfx_api_1.QTreeNode(question_1.getQuestionNewTargetEnvironmentName(inputs.projectPath));
    childNode.condition = { equals: newTargetEnvNameOption };
    node.addChild(childNode);
    return teamsfx_api_1.ok(node.trim());
}
exports.getQuestionsForTargetEnv = getQuestionsForTargetEnv;
async function getQuestionsForNewEnv(inputs, lastUsed) {
    if (!inputs.projectPath) {
        return teamsfx_api_1.err(new error_1.NoProjectOpenedError());
    }
    const group = new teamsfx_api_1.QTreeNode({ type: "group" });
    const newEnvNameNode = new teamsfx_api_1.QTreeNode(question_1.getQuestionNewTargetEnvironmentName(inputs.projectPath));
    group.addChild(newEnvNameNode);
    const envProfilesResult = await environment_1.environmentManager.listRemoteEnvConfigs(inputs.projectPath, true);
    if (envProfilesResult.isErr()) {
        return teamsfx_api_1.err(envProfilesResult.error);
    }
    const envList = reOrderEnvironments(envProfilesResult.value, lastUsed);
    const selectSourceEnv = question_1.QuestionSelectSourceEnvironment();
    selectSourceEnv.staticOptions = envList;
    selectSourceEnv.default = lastUsed + lastUsedMark;
    const selectSourceEnvNode = new teamsfx_api_1.QTreeNode(selectSourceEnv);
    group.addChild(selectSourceEnvNode);
    return teamsfx_api_1.ok(group.trim());
}
function reOrderEnvironments(environments, lastUsed) {
    if (!lastUsed) {
        return environments;
    }
    const index = environments.indexOf(lastUsed);
    if (index === -1) {
        return environments;
    }
    return [lastUsed + lastUsedMark]
        .concat(environments.slice(0, index))
        .concat(environments.slice(index + 1));
}
//# sourceMappingURL=envInfoLoaderV3.js.map