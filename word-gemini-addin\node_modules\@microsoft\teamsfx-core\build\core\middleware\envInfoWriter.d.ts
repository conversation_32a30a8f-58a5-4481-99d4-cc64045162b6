import { Middleware } from "@feathersjs/hooks";
import { FxError, Result } from "@microsoft/teamsfx-api";
/**
 * This middleware will help to persist environment state even if lifecycle task throws Error.
 */
export declare function EnvInfoWriterMW(skip?: boolean): Middleware;
export declare function shouldSkipWriteEnvInfo(res: Result<any, FxError>): boolean;
//# sourceMappingURL=envInfoWriter.d.ts.map