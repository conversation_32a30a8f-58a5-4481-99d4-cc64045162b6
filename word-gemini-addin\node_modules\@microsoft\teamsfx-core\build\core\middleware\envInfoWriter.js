// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.shouldSkipWriteEnvInfo = exports.EnvInfoWriterMW = void 0;
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const constants_1 = require("../../component/constants");
const environment_1 = require("../environment");
const globalVars_1 = require("../globalVars");
const projectSettingsLoader_1 = require("./projectSettingsLoader");
/**
 * This middleware will help to persist environment state even if lifecycle task throws Error.
 */
function EnvInfoWriterMW(skip = false) {
    return async (ctx, next) => {
        if (ctx.method === "localDebug" || ctx.method === "localDebugV2") {
            skip = false;
        }
        let error1 = undefined;
        try {
            await next();
            const res = ctx.result;
            if (shouldSkipWriteEnvInfo(res)) {
                return;
            }
        }
        catch (e) {
            if (e["name"] === "CancelProvision")
                throw e;
            error1 = e;
        }
        let error2 = undefined;
        try {
            await writeEnvInfo(ctx, skip);
        }
        catch (e) {
            error2 = e;
        }
        if (error1)
            throw error1;
        if (error2)
            throw error2;
    };
}
exports.EnvInfoWriterMW = EnvInfoWriterMW;
function shouldSkipWriteEnvInfo(res) {
    return res.isErr() && !!res.error.userData && !!res.error.userData.shouldSkipWriteEnvInfo;
}
exports.shouldSkipWriteEnvInfo = shouldSkipWriteEnvInfo;
async function writeEnvInfo(ctx, skip) {
    if (projectSettingsLoader_1.shouldIgnored(ctx) || skip) {
        return;
    }
    const lastArg = ctx.arguments[ctx.arguments.length - 1];
    const inputs = lastArg === ctx ? ctx.arguments[ctx.arguments.length - 2] : lastArg;
    if (!inputs.projectPath ||
        inputs.ignoreConfigPersist === true ||
        inputs.ignoreEnvInfo === true ||
        teamsfx_api_1.StaticPlatforms.includes(inputs.platform))
        return;
    const envInfoV2 = ctx.envInfoV2;
    if (!envInfoV2)
        return;
    const state = envInfoV2.state;
    if (state === undefined)
        return;
    // DO NOT persist local debug plugin config.
    if (state[constants_1.PluginNames.LDEBUG]) {
        delete state[constants_1.PluginNames.LDEBUG];
    }
    const envStatePath = await environment_1.environmentManager.writeEnvState(envInfoV2.state, inputs.projectPath, ctx.contextV2.cryptoProvider, envInfoV2.envName);
    if (envStatePath.isOk()) {
        globalVars_1.TOOLS.logProvider.debug(`[core] persist env state: ${envStatePath.value}`);
    }
}
//# sourceMappingURL=envInfoWriter.js.map