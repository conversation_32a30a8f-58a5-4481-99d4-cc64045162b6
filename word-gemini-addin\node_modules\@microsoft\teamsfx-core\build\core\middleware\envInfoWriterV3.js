// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnvInfoWriterMW_V3 = void 0;
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const environment_1 = require("../environment");
const globalVars_1 = require("../globalVars");
const envInfoWriter_1 = require("./envInfoWriter");
const projectSettingsLoader_1 = require("./projectSettingsLoader");
/**
 * This middleware will help to persist environment state even if lifecycle task throws Error.
 */
function EnvInfoWriterMW_V3(skip = false) {
    return async (ctx, next) => {
        let error1 = undefined;
        try {
            await next();
            const res = ctx.result;
            if (envInfoWriter_1.shouldSkipWriteEnvInfo(res)) {
                return;
            }
        }
        catch (e) {
            if (e["name"] === "CancelProvision")
                throw e;
            error1 = e;
        }
        let error2 = undefined;
        try {
            await writeEnvInfo(ctx, skip);
        }
        catch (e) {
            error2 = e;
        }
        if (error1)
            throw error1;
        if (error2)
            throw error2;
    };
}
exports.EnvInfoWriterMW_V3 = EnvInfoWriterMW_V3;
async function writeEnvInfo(ctx, skip) {
    if (projectSettingsLoader_1.shouldIgnored(ctx) || skip) {
        return;
    }
    const lastArg = ctx.arguments[ctx.arguments.length - 1];
    const inputs = lastArg === ctx ? ctx.arguments[ctx.arguments.length - 2] : lastArg;
    if (!inputs.projectPath ||
        inputs.ignoreConfigPersist === true ||
        inputs.ignoreEnvInfo === true ||
        teamsfx_api_1.StaticPlatforms.includes(inputs.platform))
        return;
    if (ctx.contextV2 && ctx.envInfoV3) {
        const envInfoV3 = ctx.envInfoV3;
        if (!envInfoV3)
            return;
        const envState = envInfoV3.state;
        if (envState === undefined)
            return;
        const envStatePath = await environment_1.environmentManager.writeEnvState(envState, inputs.projectPath, ctx.contextV2.cryptoProvider, envInfoV3.envName, true);
        if (envStatePath.isOk()) {
            globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.debug(`[core] persist env state: ${envStatePath.value}`);
        }
    }
}
//# sourceMappingURL=envInfoWriterV3.js.map