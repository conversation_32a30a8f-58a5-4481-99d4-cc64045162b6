{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../../src/core/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC,YAAY,CAAC;;;AAGb,wDAA4F;AAC5F,8CAA0C;AAE1C;;;GAGG;AACI,MAAM,cAAc,GAAe,KAAK,EAAE,GAAgB,EAAE,IAAkB,EAAE,EAAE;IACvF,qCAAqC;IACrC,8EAA8E;IAC9E,MAAM;IACN,+CAA+C;IAC/C,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC;IACjE,IAAI,MAAM,CAAC,MAAM;QAAE,sBAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC5C,IAAI;QACF,6CAA6C;QAC7C,wDAAwD;QACxD,oCAAoC;QACpC,WAAW;QACX,mCAAmC;QACnC,IAAI;QACJ,qCAAqC;QACrC,MAAM,IAAI,EAAE,CAAC;QACb,mFAAmF;QACnF,wDAAwD;QACxD,oCAAoC;QACpC,WAAW;QACX,mCAAmC;QACnC,IAAI;KACL;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,OAAO,GAAG,2BAAa,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,OAAO,YAAY,yBAAW,EAAE;YAClC,OAAO,GAAG,MAAM,qBAAqB,CAAC,OAAO,CAAC,CAAC;SAChD;QACD,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,OAAO,CAAC,CAAC;KAC3B;AACH,CAAC,CAAC;AA7BW,QAAA,cAAc,kBA6BzB;AAEF,MAAM,IAAI,GACR,mMAAmM,CAAC;AACtM,MAAM,IAAI,GAAG,6EAA6E,CAAC;AAC3F,MAAM,IAAI,GAAG,yCAAyC,CAAC;AACvD,MAAM,IAAI,GACR,yJAAyJ,CAAC;AAC5J,MAAM,IAAI,GACR,uIAAuI,CAAC;AAC1I,MAAM,IAAI,GACR,sGAAsG,CAAC;AACzG,MAAM,IAAI,GAAG,4CAA4C,CAAC;AAC1D,MAAM,IAAI,GACR,4FAA4F,CAAC;AAC/F,MAAM,IAAI,GAAG,kDAAkD,CAAC;AAChE,MAAM,KAAK,GAAG,4BAA4B,CAAC;AAC3C,MAAM,KAAK,GAAG,kCAAkC,CAAC;AACjD,MAAM,KAAK,GAAG,6BAA6B,CAAC;AAC5C,qDAAqD;AACrD,kDAAkD;AAClD,yCAAyC;AACzC,kDAAkD;AAElD,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAEzF,KAAK,UAAU,qBAAqB,CAAC,GAAgB;IACnD,MAAM,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC;IACxB,IAAI,CAAC,GAAG;QAAE,OAAO,GAAG,CAAC;IACrB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;QACtB,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;YAC1B,MAAM,SAAS,GAAG,IAAI,uBAAS,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;YACnE,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;YAC5B,OAAO,SAAS,CAAC;SAClB;KACF;IACD,OAAO,GAAG,CAAC;AACb,CAAC"}