import { FxError, InputsWith<PERSON><PERSON><PERSON><PERSON>ath, Log<PERSON>rovider, ProjectSettingsV3, Result } from "@microsoft/teamsfx-api";
import { Middleware } from "@feathersjs/hooks/lib";
import "../../component/registerService";
import { CoreHookContext } from "../types";
export declare const learnMoreText: string;
export declare const upgradeButton: string;
export declare class ArmParameters {
    static readonly FEStorageName = "frontendHostingStorageName";
    static readonly IdentityName = "userAssignedIdentityName";
    static readonly SQLServer = "sqlServerName";
    static readonly SQLDatabase = "sqlDatabaseName";
    static readonly SimpleAuthSku = "simpleAuthSku";
    static readonly functionServerName = "functionServerfarmsName";
    static readonly functionStorageName = "functionStorageName";
    static readonly functionAppName = "functionWebappName";
    static readonly botWebAppSku = "botWebAppSKU";
    static readonly SimpleAuthWebAppName = "simpleAuthWebAppName";
    static readonly SimpleAuthServerFarm = "simpleAuthServerFarmsName";
    static readonly ApimServiceName = "apimServiceName";
    static readonly ApimProductName = "apimProductName";
    static readonly ApimOauthServerName = "apimOauthServerName";
}
export declare const ProjectMigratorMW: Middleware;
export declare function outputCancelMessage(ctx: CoreHookContext): void;
export declare function checkMethod(ctx: CoreHookContext): boolean;
export declare function checkUserTasks(ctx: CoreHookContext): boolean;
export declare class LocalDebugConfigKeys {
    static readonly LocalAuthEndpoint: string;
    static readonly LocalTabEndpoint: string;
    static readonly LocalTabDomain: string;
    static readonly TrustDevelopmentCertificate: string;
    static readonly LocalFunctionEndpoint: string;
    static readonly LocalBotEndpoint: string;
    static readonly LocalBotDomain: string;
}
export declare function addPathToGitignore(projectPath: string, ignoredPath: string, log: LogProvider): Promise<void>;
export declare function needMigrateToArmAndMultiEnv(ctx: CoreHookContext): Promise<boolean>;
export declare function migrateArm(ctx: CoreHookContext): Promise<void>;
export declare function generateBicepsV3(projectSettings: ProjectSettingsV3, inputs: InputsWithProjectPath): Promise<Result<undefined, FxError>>;
//# sourceMappingURL=projectMigrator.d.ts.map