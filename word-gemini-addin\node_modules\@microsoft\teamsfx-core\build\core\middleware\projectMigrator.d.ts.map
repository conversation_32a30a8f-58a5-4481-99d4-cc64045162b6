{"version": 3, "file": "projectMigrator.d.ts", "sourceRoot": "", "sources": ["../../../src/core/middleware/projectMigrator.ts"], "names": [], "mappings": "AAGA,OAAO,EAUL,OAAO,EAGP,qBAAqB,EACrB,WAAW,EAIX,iBAAiB,EACjB,MAAM,EAIP,MAAM,wBAAwB,CAAC;AAahC,OAAO,EAAE,UAAU,EAAgB,MAAM,uBAAuB,CAAC;AAIjE,OAAO,iCAAiC,CAAC;AAoCzC,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AA4B3C,eAAO,MAAM,aAAa,QAA8C,CAAC;AACzE,eAAO,MAAM,aAAa,QAA4C,CAAC;AA8CvE,qBAAa,aAAa;IACxB,MAAM,CAAC,QAAQ,CAAC,aAAa,gCAAgC;IAC7D,MAAM,CAAC,QAAQ,CAAC,YAAY,8BAA8B;IAC1D,MAAM,CAAC,QAAQ,CAAC,SAAS,mBAAmB;IAC5C,MAAM,CAAC,QAAQ,CAAC,WAAW,qBAAqB;IAChD,MAAM,CAAC,QAAQ,CAAC,aAAa,mBAAmB;IAChD,MAAM,CAAC,QAAQ,CAAC,kBAAkB,6BAA6B;IAC/D,MAAM,CAAC,QAAQ,CAAC,mBAAmB,yBAAyB;IAC5D,MAAM,CAAC,QAAQ,CAAC,eAAe,wBAAwB;IACvD,MAAM,CAAC,QAAQ,CAAC,YAAY,kBAAkB;IAC9C,MAAM,CAAC,QAAQ,CAAC,oBAAoB,0BAA0B;IAC9D,MAAM,CAAC,QAAQ,CAAC,oBAAoB,+BAA+B;IACnE,MAAM,CAAC,QAAQ,CAAC,eAAe,qBAAqB;IACpD,MAAM,CAAC,QAAQ,CAAC,eAAe,qBAAqB;IACpD,MAAM,CAAC,QAAQ,CAAC,mBAAmB,yBAAyB;CAC7D;AAED,eAAO,MAAM,iBAAiB,EAAE,UA+C/B,CAAC;AAEF,wBAAgB,mBAAmB,CAAC,GAAG,EAAE,eAAe,QAkBvD;AAED,wBAAgB,WAAW,CAAC,GAAG,EAAE,eAAe,GAAG,OAAO,CAKzD;AAED,wBAAgB,cAAc,CAAC,GAAG,EAAE,eAAe,GAAG,OAAO,CAO5D;AA8bD,qBAAa,oBAAoB;IAC/B,gBAAuB,iBAAiB,EAAE,MAAM,CAAuB;IAEvE,gBAAuB,gBAAgB,EAAE,MAAM,CAAsB;IACrE,gBAAuB,cAAc,EAAE,MAAM,CAAoB;IACjE,gBAAuB,2BAA2B,EAAE,MAAM,CAAkB;IAE5E,gBAAuB,qBAAqB,EAAE,MAAM,CAA2B;IAE/E,gBAAuB,gBAAgB,EAAE,MAAM,CAAsB;IACrE,gBAAuB,cAAc,EAAE,MAAM,CAAoB;CAClE;AAgID,wBAAsB,kBAAkB,CACtC,WAAW,EAAE,MAAM,EACnB,WAAW,EAAE,MAAM,EACnB,GAAG,EAAE,WAAW,GACf,OAAO,CAAC,IAAI,CAAC,CAGf;AAkED,wBAAsB,2BAA2B,CAAC,GAAG,EAAE,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC,CA0BxF;AAED,wBAAsB,UAAU,CAAC,GAAG,EAAE,eAAe,iBAGpD;AAyFD,wBAAsB,gBAAgB,CACpC,eAAe,EAAE,iBAAiB,EAClC,MAAM,EAAE,qBAAqB,GAC5B,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CA0JrC"}