"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateBicepsV3 = exports.migrateArm = exports.needMigrateToArmAndMultiEnv = exports.addPathToGitignore = exports.LocalDebugConfigKeys = exports.checkUserTasks = exports.checkMethod = exports.outputCancelMessage = exports.ProjectMigratorMW = exports.ArmParameters = exports.upgradeButton = exports.learnMoreText = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const tools_1 = require("../../common/tools");
const environment_1 = require("../environment");
const folder_1 = require("../../folder");
const globalState_1 = require("../../common/globalState");
const error_1 = require("../error");
const localSettingsProvider_1 = require("../../common/localSettingsProvider");
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const path_1 = tslib_1.__importDefault(require("path"));
const os_1 = tslib_1.__importDefault(require("os"));
require("../../component/registerService");
const constants_1 = require("../../component/constants");
const projectSettingsLoader_1 = require("./projectSettingsLoader");
const constants_2 = require("../../common/constants");
const constants_3 = require("../../component/resource/appManifest/constants");
const utils_1 = require("../../component/resource/appManifest/utils/utils");
const telemetry_1 = require("../../common/telemetry");
const dotenv = tslib_1.__importStar(require("dotenv"));
const constants_4 = require("../../component/resource/spfx/utils/constants");
const utils_2 = require("../../component/resource/spfx/utils/utils");
const localEnvProvider_1 = require("../../common/local/localEnvProvider");
const globalVars_1 = require("../globalVars");
const localizeUtils_1 = require("../../common/localizeUtils");
const utils_3 = require("../../common/utils");
const typedi_1 = require("typedi");
const workflow_1 = require("../../component/workflow");
const utils_4 = require("../../component/utils");
const lodash_1 = require("lodash");
const migrate_1 = require("../../component/migrate");
const projectSettingsHelperV3_1 = require("../../common/projectSettingsHelperV3");
const programmingLanguage = "programmingLanguage";
const defaultFunctionName = "defaultFunctionName";
exports.learnMoreText = localizeUtils_1.getLocalizedString("core.option.learnMore");
exports.upgradeButton = localizeUtils_1.getLocalizedString("core.option.upgrade");
const solutionName = "solution";
const subscriptionId = "subscriptionId";
const resourceGroupName = "resourceGroupName";
const parameterFileNameTemplate = "<EMAIL>";
const learnMoreLink = "https://aka.ms/teamsfx-migration-guide";
const manualUpgradeLink = `${learnMoreLink}#upgrade-your-project-manually`;
const upgradeReportName = `upgrade-change-logs.md`;
const AadSecret = "{{ $env.AAD_APP_CLIENT_SECRET }}";
const ChangeLogsFlag = "openUpgradeChangelogs";
const AADClientSecretFlag = "NeedToSetAADClientSecretEnv";
const gitignoreFileName = ".gitignore";
let fromReloadFlag = false;
class EnvConfigName {
}
EnvConfigName.StorageName = "storageName";
EnvConfigName.Identity = "identity";
EnvConfigName.IdentityId = "identityId";
EnvConfigName.IdentityName = "identityName";
EnvConfigName.IdentityResourceId = "identityResourceId";
EnvConfigName.IdentityClientId = "identityClientId";
EnvConfigName.SqlEndpoint = "sqlEndpoint";
EnvConfigName.SqlResourceId = "sqlResourceId";
EnvConfigName.SqlDataBase = "databaseName";
EnvConfigName.SqlSkipAddingUser = "skipAddingUser";
EnvConfigName.SkuName = "skuName";
EnvConfigName.AppServicePlanName = "appServicePlanName";
EnvConfigName.StorageAccountName = "storageAccountName";
EnvConfigName.StorageResourceId = "storageResourceId";
EnvConfigName.FuncAppName = "functionAppName";
EnvConfigName.FunctionAppResourceId = "functionAppResourceId";
EnvConfigName.Endpoint = "endpoint";
EnvConfigName.ServiceName = "serviceName";
EnvConfigName.ProductId = "productId";
EnvConfigName.OAuthServerId = "oAuthServerId";
EnvConfigName.ServiceResourceId = "serviceResourceId";
EnvConfigName.ProductResourceId = "productResourceId";
EnvConfigName.AuthServerResourceId = "authServerResourceId";
EnvConfigName.AadSkipProvision = "skipProvision";
EnvConfigName.OAuthScopeId = "oauth2PermissionScopeId";
EnvConfigName.ClientId = "clientId";
EnvConfigName.ClientSecret = "clientSecret";
EnvConfigName.ObjectId = "objectId";
class ArmParameters {
}
exports.ArmParameters = ArmParameters;
ArmParameters.FEStorageName = "frontendHostingStorageName";
ArmParameters.IdentityName = "userAssignedIdentityName";
ArmParameters.SQLServer = "sqlServerName";
ArmParameters.SQLDatabase = "sqlDatabaseName";
ArmParameters.SimpleAuthSku = "simpleAuthSku";
ArmParameters.functionServerName = "functionServerfarmsName";
ArmParameters.functionStorageName = "functionStorageName";
ArmParameters.functionAppName = "functionWebappName";
ArmParameters.botWebAppSku = "botWebAppSKU";
ArmParameters.SimpleAuthWebAppName = "simpleAuthWebAppName";
ArmParameters.SimpleAuthServerFarm = "simpleAuthServerFarmsName";
ArmParameters.ApimServiceName = "apimServiceName";
ArmParameters.ApimProductName = "apimProductName";
ArmParameters.ApimOauthServerName = "apimOauthServerName";
const ProjectMigratorMW = async (ctx, next) => {
    if ((await needMigrateToArmAndMultiEnv(ctx)) && checkMethod(ctx)) {
        if (!checkUserTasks(ctx)) {
            ctx.result = teamsfx_api_1.ok(undefined);
            return;
        }
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorNotificationStart);
        const res = await (globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.showMessage("warn", localizeUtils_1.getLocalizedString("core.migrationToArmAndMultiEnv.Message"), true, exports.upgradeButton));
        const answer = (res === null || res === void 0 ? void 0 : res.isOk()) ? res.value : undefined;
        if (!answer || answer != exports.upgradeButton) {
            telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorNotification, {
                [telemetry_1.TelemetryProperty.Status]: telemetry_1.ProjectMigratorStatus.Cancel,
            });
            ctx.result = teamsfx_api_1.err(error_1.UpgradeCanceledError());
            outputCancelMessage(ctx);
            return;
        }
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorNotification, {
            [telemetry_1.TelemetryProperty.Status]: telemetry_1.ProjectMigratorStatus.OK,
        });
        try {
            await migrateToArmAndMultiEnv(ctx);
            // return ok for the lifecycle functions to prevent breaking error handling logic.
            ctx.result = teamsfx_api_1.ok({});
        }
        catch (error) {
            // Strictly speaking, this telemetry event is not required because errorHandlerMW will send error telemetry anyway.
            // But it makes it easier to separate projectMigratorMW errors from other provision errors.
            telemetry_1.sendTelemetryErrorEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorError, teamsfx_api_1.assembleError(error, error_1.CoreSource));
            throw error;
        }
    }
    else {
        // continue next step only when:
        // 1. no need to upgrade the project;
        // 2. no need to update Teams Toolkit version;
        await next();
    }
};
exports.ProjectMigratorMW = ProjectMigratorMW;
function outputCancelMessage(ctx) {
    globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Upgrade cancelled.`);
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    if (inputs.platform === teamsfx_api_1.Platform.VSCode) {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Notice upgrade to new configuration files is a must-have to continue to use current version Teams Toolkit. If you want to upgrade, please run command (Teams: Upgrade project) or click the “Upgrade project” button on tree view to trigger the upgrade.`);
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core]If you are not ready to upgrade and want to continue to use the old version Teams Toolkit, please find Teams Toolkit in Extension and install the version <= 2.10.0`);
    }
    else {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Notice upgrade to new configuration files is a must-have to continue to use current version Teams Toolkit CLI. If you want to upgrade, please trigger this command again.`);
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core]If you are not ready to upgrade and want to continue to use the old version Teams Toolkit CLI, please install the version <= 2.10.0`);
    }
}
exports.outputCancelMessage = outputCancelMessage;
function checkMethod(ctx) {
    const methods = new Set(["getProjectConfig", "checkPermission"]);
    if (ctx.method && methods.has(ctx.method) && fromReloadFlag)
        return false;
    fromReloadFlag = ctx.method != undefined && methods.has(ctx.method);
    return true;
}
exports.checkMethod = checkMethod;
function checkUserTasks(ctx) {
    var _a;
    const userTaskArgs = new Set(["getProgrammingLanguage", "getLocalDebugEnvs"]);
    const userTaskMethod = (_a = ctx.arguments[0]) === null || _a === void 0 ? void 0 : _a["method"];
    if (ctx.method === "executeUserTask" && userTaskArgs.has(userTaskMethod)) {
        return false;
    }
    return true;
}
exports.checkUserTasks = checkUserTasks;
async function getOldProjectInfoForTelemetry(projectPath) {
    try {
        const inputs = {
            projectPath: projectPath,
            // not used by `loadProjectSettings` but the type `Inputs` requires it.
            platform: teamsfx_api_1.Platform.VSCode,
        };
        const loadRes = await projectSettingsLoader_1.loadProjectSettings(inputs, false);
        if (loadRes.isErr()) {
            return {};
        }
        const projectSettings = loadRes.value;
        const solutionSettings = projectSettings.solutionSettings;
        const hostType = solutionSettings === null || solutionSettings === void 0 ? void 0 : solutionSettings.hostType;
        const result = { [telemetry_1.TelemetryProperty.HostType]: hostType };
        if (hostType === constants_1.HostTypeOptionAzure().id || hostType === constants_1.HostTypeOptionSPFx().id) {
            result[telemetry_1.TelemetryProperty.ActivePlugins] = JSON.stringify(solutionSettings.activeResourcePlugins);
            result[telemetry_1.TelemetryProperty.Capabilities] = JSON.stringify(solutionSettings.capabilities);
        }
        if (hostType === constants_1.HostTypeOptionAzure().id) {
            const azureSolutionSettings = solutionSettings;
            result[telemetry_1.TelemetryProperty.AzureResources] = JSON.stringify(azureSolutionSettings.azureResources);
        }
        return result;
    }
    catch (error) {
        // ignore telemetry errors
        return {};
    }
}
async function migrateToArmAndMultiEnv(ctx) {
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    const projectPath = inputs.projectPath;
    const telemetryProperties = await getOldProjectInfoForTelemetry(projectPath);
    telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorMigrateStart, telemetryProperties);
    try {
        await preCheckKeyFiles(projectPath, ctx);
    }
    catch (err) {
        telemetry_1.sendTelemetryErrorEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorPrecheckFailed, teamsfx_api_1.assembleError(err, error_1.CoreSource));
        return;
    }
    let backupFolder;
    try {
        backupFolder = await getBackupFolder(projectPath);
        await backup(projectPath, backupFolder);
        await updateConfig(ctx);
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorMigrateMultiEnvStart);
        const projectSettings = await migrateMultiEnv(projectPath, globalVars_1.TOOLS.logProvider);
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorMigrateMultiEnv);
        ctx.projectSettings = projectSettings;
        if (!tools_1.isSPFxProject(projectSettings)) {
            telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorMigrateArmStart);
            await migrateArm(ctx);
            telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorMigrateArm);
        }
    }
    catch (err) {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.error(`[core] Failed to upgrade project, error: '${err}'`);
        await handleError(projectPath, ctx, backupFolder);
        throw err;
    }
    await postMigration(projectPath, ctx, inputs, backupFolder);
}
async function getManifestPath(fx, projectPath) {
    if (await fs_extra_1.default.pathExists(path_1.default.join(projectPath, teamsfx_api_1.AppPackageFolderName, constants_3.REMOTE_MANIFEST))) {
        return path_1.default.join(projectPath, teamsfx_api_1.AppPackageFolderName, constants_3.REMOTE_MANIFEST);
    }
    // 2.3.2<= version <= 2.4.1
    if (await fs_extra_1.default.pathExists(path_1.default.join(fx, teamsfx_api_1.AppPackageFolderName, constants_3.REMOTE_MANIFEST))) {
        return path_1.default.join(fx, teamsfx_api_1.AppPackageFolderName, constants_3.REMOTE_MANIFEST);
    }
    // 2.0.1 <= version <= 2.3.1
    return path_1.default.join(fx, constants_3.REMOTE_MANIFEST);
}
async function preCheckKeyFiles(projectPath, ctx) {
    const fx = path_1.default.join(projectPath, `.${teamsfx_api_1.ConfigFolderName}`);
    const manifestPath = await getManifestPath(fx, projectPath);
    await preReadJsonFile(path_1.default.join(fx, "env.default.json"));
    await preReadJsonFile(path_1.default.join(fx, "settings.json"));
    await preReadJsonFile(manifestPath);
}
async function preReadJsonFile(path) {
    try {
        await fs_extra_1.default.readJson(path);
    }
    catch (err) {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.error(`'${path}' doesn't exist or is not in json format. Please fix it and try again by running command (Teams: Upgrade project).`);
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`Read this wiki(${learnMoreLink}) for the FAQ.`);
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.showMessage("info", localizeUtils_1.getLocalizedString("core.migrationToArmAndMultiEnv.PreCheckErrorMessage", path), false, exports.learnMoreText).then((result) => {
            const userSelected = result.isOk() ? result.value : undefined;
            if (userSelected === exports.learnMoreText) {
                globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.openUrl(manualUpgradeLink);
            }
        });
        throw error_1.NotJsonError(err);
    }
}
async function handleError(projectPath, ctx, backupFolder) {
    try {
        await cleanup(projectPath, backupFolder);
    }
    catch (e) {
        // try my best to cleanup
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.error(`[core] Failed to cleanup the backup, error: '${e}'`);
    }
    globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.showMessage("info", localizeUtils_1.getLocalizedString("core.migrationToArmAndMultiEnv.ErrorMessage"), false, exports.learnMoreText).then((result) => {
        const userSelected = result.isOk() ? result.value : undefined;
        if (userSelected === exports.learnMoreText) {
            globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.openUrl(manualUpgradeLink);
        }
    });
}
async function generateUpgradeReport(backupFolder) {
    try {
        if (backupFolder) {
            const target = path_1.default.join(backupFolder, upgradeReportName);
            const source = path_1.default.resolve(path_1.default.join(folder_1.getResourceFolder(), upgradeReportName));
            await fs_extra_1.default.copyFile(source, target);
        }
    }
    catch (error) {
        // do nothing
    }
}
async function postMigration(projectPath, ctx, inputs, backupFolder) {
    var _a, _b;
    await removeOldProjectFiles(projectPath);
    telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorMigrate);
    telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorGuideStart);
    await generateUpgradeReport(backupFolder);
    await updateGitIgnore(projectPath, globalVars_1.TOOLS.logProvider, backupFolder);
    globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Upgrade success! All old files in .fx and appPackage folder have been backed up to the .backup folder and you can delete it. Read this wiki(${learnMoreLink}) if you want to restore your configuration files or learn more about this upgrade.`);
    globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Read upgrade-change-logs.md to learn about details for this upgrade.`);
    if (inputs.platform === teamsfx_api_1.Platform.VSCode) {
        await globalState_1.globalStateUpdate(ChangeLogsFlag, true);
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorGuide, {
            [telemetry_1.TelemetryProperty.Status]: telemetry_1.ProjectMigratorGuideStatus.Reload,
        });
        await ((_b = globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : (_a = globalVars_1.TOOLS.ui).reload) === null || _b === void 0 ? void 0 : _b.call(_a));
    }
    else {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.info(localizeUtils_1.getLocalizedString("core.migrationToArmAndMultiEnv.SuccessMessage"));
    }
}
async function updateGitIgnore(projectPath, log, backupFolder) {
    // add .fx/configs/localSetting.json to .gitignore
    const localSettingsProvider = new localSettingsProvider_1.LocalSettingsProvider(projectPath);
    await addPathToGitignore(projectPath, localSettingsProvider.localSettingsFilePath, log);
    // add .fx/subscriptionInfo.json to .gitignore
    const subscriptionInfoPath = path_1.default.join(projectPath, `.${teamsfx_api_1.ConfigFolderName}`, "subscriptionInfo.json");
    await addPathToGitignore(projectPath, subscriptionInfoPath, log);
    // add build/ to .gitignore
    const buildFolder = path_1.default.join(projectPath, teamsfx_api_1.BuildFolderName);
    await addPathToGitignore(projectPath, buildFolder, log);
    // add **/.env.teamsfx.local to .gitignore
    const envLocal = "**/" + localEnvProvider_1.LocalEnvProvider.LocalEnvFileName;
    await addItemToGitignore(projectPath, envLocal, log);
    // add .fx/states/*.userdata to .gitignore
    const userdata = `.${teamsfx_api_1.ConfigFolderName}/${teamsfx_api_1.StatesFolderName}/*.userdata`;
    await addItemToGitignore(projectPath, userdata, log);
    if (backupFolder) {
        await addPathToGitignore(projectPath, backupFolder, log);
    }
}
async function generateRemoteTemplate(manifestString) {
    manifestString = manifestString.replace(new RegExp("{version}", "g"), "1.0.0");
    manifestString = manifestString.replace(new RegExp("{baseUrl}", "g"), "{{{state.fx-resource-frontend-hosting.endpoint}}}");
    manifestString = manifestString.replace(new RegExp("{appClientId}", "g"), "{{state.fx-resource-aad-app-for-teams.clientId}}");
    manifestString = manifestString.replace(new RegExp("{webApplicationInfoResource}", "g"), "{{{state.fx-resource-aad-app-for-teams.applicationIdUris}}}");
    manifestString = manifestString.replace(new RegExp("{botId}", "g"), "{{state.fx-resource-bot.botId}}");
    const manifest = JSON.parse(manifestString);
    manifest.name.short = "{{config.manifest.appName.short}}";
    manifest.name.full = "{{config.manifest.appName.full}}";
    manifest.id = "{{state.fx-resource-appstudio.teamsAppId}}";
    return manifest;
}
async function generateLocalTemplate(manifestString, isSPFx, log) {
    manifestString = manifestString.replace(new RegExp("{version}", "g"), "1.0.0");
    manifestString = manifestString.replace(new RegExp("{baseUrl}", "g"), "{{{localSettings.frontend.tabEndpoint}}}");
    manifestString = manifestString.replace(new RegExp("{appClientId}", "g"), "{{localSettings.auth.clientId}}");
    manifestString = manifestString.replace(new RegExp("{webApplicationInfoResource}", "g"), "{{{localSettings.auth.applicationIdUris}}}");
    manifestString = manifestString.replace(new RegExp("{botId}", "g"), "{{localSettings.bot.botId}}");
    const manifest = JSON.parse(manifestString);
    manifest.name.full =
        (manifest.name.full ? manifest.name.full : manifest.name.short) + "-local-debug";
    manifest.name.short = utils_1.getLocalAppName(manifest.name.short);
    manifest.id = "{{localSettings.teamsApp.teamsAppId}}";
    // SPFx teams workbench url needs to be updated
    if (isSPFx) {
        if (manifest.configurableTabs) {
            for (const [index, tab] of manifest.configurableTabs.entries()) {
                const reg = /[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}/;
                const result = tab.configurationUrl.match(reg);
                if (result && result.length > 0) {
                    const componentID = result[0];
                    tab.configurationUrl = `https://{teamSiteDomain}{teamSitePath}/_layouts/15/TeamsLogon.aspx?SPFX=true&dest={teamSitePath}/_layouts/15/TeamsWorkBench.aspx%3FcomponentId=${componentID}%26openPropertyPane=true%26teams%26forceLocale={locale}%26loadSPFX%3Dtrue%26debugManifestsFile%3Dhttps%3A%2F%2Flocalhost%3A4321%2Ftemp%2Fmanifests.js`;
                }
                else {
                    const message = `[core] Cannot find componentID in configurableTabs[${index}].configrationUrl, Teams workbench debug may fail.`;
                    log.warning(message);
                }
            }
        }
        if (manifest.staticTabs) {
            for (const tab of manifest.staticTabs) {
                const componentID = tab.entityId;
                tab.contentUrl = `https://{teamSiteDomain}/_layouts/15/TeamsLogon.aspx?SPFX=true&dest={teamSitePath}/_layouts/15/TeamsWorkBench.aspx%3FcomponentId=${componentID}%26teams%26personal%26forceLocale={locale}%26loadSPFX%3Dtrue%26debugManifestsFile%3Dhttps%3A%2F%2Flocalhost%3A4321%2Ftemp%2Fmanifests.js`;
            }
        }
    }
    return manifest;
}
async function getManifest(sourceManifestFile) {
    return await fs_extra_1.default.readJson(sourceManifestFile);
}
async function copyManifest(projectPath, fx, target) {
    if (await fs_extra_1.default.pathExists(path_1.default.join(projectPath, teamsfx_api_1.AppPackageFolderName))) {
        await fs_extra_1.default.copy(path_1.default.join(projectPath, teamsfx_api_1.AppPackageFolderName), target);
    }
    else if (await fs_extra_1.default.pathExists(path_1.default.join(fx, teamsfx_api_1.AppPackageFolderName))) {
        // version <= 2.4.1
        await fs_extra_1.default.copy(path_1.default.join(fx, teamsfx_api_1.AppPackageFolderName), target);
    }
    else {
        // version <= 2.3.1
        await fs_extra_1.default.copy(path_1.default.join(fx, constants_3.REMOTE_MANIFEST), path_1.default.join(target, constants_3.REMOTE_MANIFEST));
        const manifest = await getManifest(path_1.default.join(target, constants_3.REMOTE_MANIFEST));
        const color = (await fs_extra_1.default.pathExists(path_1.default.join(fx, "color.png")))
            ? "color.png"
            : manifest.icons.color;
        const outline = (await fs_extra_1.default.pathExists(path_1.default.join(fx, "outline.png")))
            ? "outline.png"
            : manifest.icons.outline;
        if (color !== "" && (await fs_extra_1.default.pathExists(path_1.default.join(fx, color)))) {
            await fs_extra_1.default.copy(path_1.default.join(fx, color), path_1.default.join(target, color));
        }
        if (outline !== "" && (await fs_extra_1.default.pathExists(path_1.default.join(fx, outline)))) {
            await fs_extra_1.default.copy(path_1.default.join(fx, outline), path_1.default.join(target, outline));
        }
    }
}
async function migrateProjectSettings(projectPath) {
    const loadRes = await projectSettingsLoader_1.loadProjectSettingsByProjectPath(projectPath, false);
    if (loadRes.isErr()) {
        throw error_1.ProjectSettingError();
    }
    const projectSettings = loadRes.value;
    if (projectSettingsHelperV3_1.hasAzureResourceV3(projectSettings, true)) {
        if (!workflow_1.getComponent(projectSettings, constants_1.ComponentNames.Identity)) {
            projectSettings.components.push({ name: constants_1.ComponentNames.Identity });
        }
    }
    return projectSettings;
}
async function migrateMultiEnv(projectPath, log) {
    var _a, _b;
    const { fx, fxConfig, templateAppPackage, fxState } = await getMultiEnvFolders(projectPath);
    const { hasFrontend, hasBackend, hasBot, hasBotCapability, hasMessageExtensionCapability, isSPFx, hasProvision, } = await queryProjectStatus(fx);
    //localSettings.json
    const localSettingsProvider = new localSettingsProvider_1.LocalSettingsProvider(projectPath);
    await localSettingsProvider.save(localSettingsProvider.init(hasFrontend, hasBackend, hasBot));
    const projectSettings = await migrateProjectSettings(projectPath);
    const projectSettingsPath = projectSettingsLoader_1.getProjectSettingsPath(projectPath);
    const configDevJsonFilePath = path_1.default.join(fxConfig, "config.dev.json");
    const envDefaultFilePath = path_1.default.join(fx, "env.default.json");
    await ensureProjectSettings(projectSettings, envDefaultFilePath);
    await fs_extra_1.default.writeFile(projectSettingsPath, JSON.stringify(projectSettings, null, 4));
    const appName = projectSettings.appName;
    //config.dev.json
    const configDev = getConfigDevJson(appName);
    // migrate skipAddingSqlUser
    const envDefault = await fs_extra_1.default.readJson(envDefaultFilePath);
    if ((_a = envDefault[constants_2.ResourcePlugins.AzureSQL]) === null || _a === void 0 ? void 0 : _a[EnvConfigName.SqlSkipAddingUser]) {
        configDev["skipAddingSqlUser"] =
            envDefault[constants_2.ResourcePlugins.AzureSQL][EnvConfigName.SqlSkipAddingUser];
    }
    if (((_b = envDefault[constants_2.ResourcePlugins.Aad]) === null || _b === void 0 ? void 0 : _b[EnvConfigName.AadSkipProvision]) === "true") {
        configDev.auth = {};
        if (envDefault[constants_2.ResourcePlugins.Aad][EnvConfigName.OAuthScopeId]) {
            configDev.auth.accessAsUserScopeId =
                envDefault[constants_2.ResourcePlugins.Aad][EnvConfigName.OAuthScopeId];
        }
        if (envDefault[constants_2.ResourcePlugins.Aad][EnvConfigName.ObjectId]) {
            configDev.auth.objectId = envDefault[constants_2.ResourcePlugins.Aad][EnvConfigName.ObjectId];
        }
        if (envDefault[constants_2.ResourcePlugins.Aad][EnvConfigName.ClientId]) {
            configDev.auth.clientId = envDefault[constants_2.ResourcePlugins.Aad][EnvConfigName.ClientId];
        }
        if (envDefault[constants_2.ResourcePlugins.Aad][EnvConfigName.ClientSecret]) {
            await globalState_1.globalStateUpdate(AADClientSecretFlag, envDefault[constants_2.ResourcePlugins.Aad][EnvConfigName.ClientSecret]);
            configDev.auth.clientSecret = AadSecret;
        }
    }
    await fs_extra_1.default.writeFile(configDevJsonFilePath, JSON.stringify(configDev, null, 4));
    // appPackage
    await copyManifest(projectPath, fx, templateAppPackage);
    const sourceManifestFile = path_1.default.join(templateAppPackage, constants_3.REMOTE_MANIFEST);
    const manifest = await getManifest(sourceManifestFile);
    await fs_extra_1.default.remove(sourceManifestFile);
    // generate manifest.remote.template.json
    const targetRemoteManifestFile = path_1.default.join(templateAppPackage, constants_3.MANIFEST_TEMPLATE);
    const remoteManifest = await generateRemoteTemplate(JSON.stringify(manifest));
    await fs_extra_1.default.writeFile(targetRemoteManifestFile, JSON.stringify(remoteManifest, null, 4));
    // generate manifest.local.template.json
    const localManifest = await generateLocalTemplate(JSON.stringify(manifest), isSPFx, log);
    const targetLocalManifestFile = path_1.default.join(templateAppPackage, constants_3.MANIFEST_LOCAL);
    await fs_extra_1.default.writeFile(targetLocalManifestFile, JSON.stringify(localManifest, null, 4));
    if (isSPFx) {
        const replaceMap = new Map();
        const packageSolutionFile = `${projectPath}/SPFx/config/package-solution.json`;
        if (!(await fs_extra_1.default.pathExists(packageSolutionFile))) {
            throw error_1.SPFxConfigError(packageSolutionFile);
        }
        const solutionConfig = await fs_extra_1.default.readJson(packageSolutionFile);
        replaceMap.set(constants_4.PlaceHolders.componentId, solutionConfig.solution.id);
        replaceMap.set(constants_4.PlaceHolders.componentNameUnescaped, localManifest.packageName);
        await utils_2.Utils.configure(targetLocalManifestFile, replaceMap);
    }
    // state.dev.json / dev.userdata
    const devState = path_1.default.join(fxState, "state.dev.json");
    await fs_extra_1.default.copy(path_1.default.join(fx, "new.env.default.json"), devState);
    const devUserData = path_1.default.join(fxState, "dev.userdata");
    if (await fs_extra_1.default.pathExists(path_1.default.join(fx, "default.userdata"))) {
        await fs_extra_1.default.copy(path_1.default.join(fx, "default.userdata"), devUserData);
    }
    await removeExpiredFields(devState, devUserData);
    return projectSettings;
}
class LocalDebugConfigKeys {
}
exports.LocalDebugConfigKeys = LocalDebugConfigKeys;
LocalDebugConfigKeys.LocalAuthEndpoint = "localAuthEndpoint";
LocalDebugConfigKeys.LocalTabEndpoint = "localTabEndpoint";
LocalDebugConfigKeys.LocalTabDomain = "localTabDomain";
LocalDebugConfigKeys.TrustDevelopmentCertificate = "trustDevCert";
LocalDebugConfigKeys.LocalFunctionEndpoint = "localFunctionEndpoint";
LocalDebugConfigKeys.LocalBotEndpoint = "localBotEndpoint";
LocalDebugConfigKeys.LocalBotDomain = "localBotDomain";
async function removeExpiredFields(devState, devUserData) {
    const stateData = await fs_extra_1.default.readJson(devState);
    if (stateData[constants_1.PluginNames.SOLUTION] && stateData[constants_1.PluginNames.SOLUTION]["remoteTeamsAppId"]) {
        stateData[constants_1.PluginNames.APPST]["teamsAppId"] =
            stateData[constants_1.PluginNames.SOLUTION]["remoteTeamsAppId"];
    }
    const expiredStateKeys = [
        [constants_1.PluginNames.LDEBUG, ""],
        // for version 2.0.1
        [constants_1.PluginNames.FUNC, defaultFunctionName],
        [constants_1.PluginNames.SOLUTION, programmingLanguage],
        [constants_1.PluginNames.SOLUTION, defaultFunctionName],
        [constants_1.PluginNames.SOLUTION, "localDebugTeamsAppId"],
        [constants_1.PluginNames.SOLUTION, "remoteTeamsAppId"],
        [constants_1.PluginNames.AAD, "local_clientId"],
        [constants_1.PluginNames.AAD, "local_objectId"],
        [constants_1.PluginNames.AAD, "local_tenantId"],
        [constants_1.PluginNames.AAD, "local_clientSecret"],
        [constants_1.PluginNames.AAD, "local_oauth2PermissionScopeId"],
        [constants_1.PluginNames.AAD, "local_applicationIdUris"],
        [constants_1.PluginNames.SA, "filePath"],
        [constants_1.PluginNames.SA, "environmentVariableParams"],
    ];
    for (const [k, v] of expiredStateKeys) {
        if (stateData[k]) {
            if (!v) {
                delete stateData[k];
            }
            else if (stateData[k][v]) {
                delete stateData[k][v];
            }
        }
    }
    await fs_extra_1.default.writeFile(devState, JSON.stringify(stateData, null, 4), { encoding: "UTF-8" });
    if (await fs_extra_1.default.pathExists(devUserData)) {
        const secrets = dotenv.parse(await fs_extra_1.default.readFile(devUserData, "UTF-8"));
        for (const [_, value] of Object.entries(LocalDebugConfigKeys)) {
            deleteUserDataKey(secrets, `${constants_1.PluginNames.LDEBUG}.${value}`);
        }
        deleteUserDataKey(secrets, `${constants_1.PluginNames.AAD}.local_clientSecret`);
        await fs_extra_1.default.writeFile(devUserData, tools_1.serializeDict(secrets), { encoding: "UTF-8" });
    }
}
function deleteUserDataKey(secrets, key) {
    if (secrets[key]) {
        delete secrets[key];
    }
}
function getConfigDevJson(appName) {
    return environment_1.environmentManager.newEnvConfigData(appName);
}
async function queryProjectStatus(fx) {
    var _a;
    const settings = await fs_extra_1.default.readJson(path_1.default.join(fx, "settings.json"));
    const settingsV3 = migrate_1.convertProjectSettingsV2ToV3(settings, "");
    const envDefaultJson = await fs_extra_1.default.readJson(path_1.default.join(fx, "env.default.json"));
    const hasFrontend = projectSettingsHelperV3_1.hasAzureTab(settingsV3);
    const hasBackend = projectSettingsHelperV3_1.hasApi(settingsV3);
    const hasBot = projectSettingsHelperV3_1.hasBot(settingsV3);
    const hasBotCapability = settings.solutionSettings.capabilities.includes(constants_1.BotOptionItem().id);
    const hasMessageExtensionCapability = settings.solutionSettings.capabilities.includes(constants_1.MessageExtensionItem().id);
    const isSPFx = projectSettingsHelperV3_1.hasSPFxTab(settingsV3);
    const hasProvision = (_a = envDefaultJson.solution) === null || _a === void 0 ? void 0 : _a.provisionSucceeded;
    return {
        hasFrontend,
        hasBackend,
        hasBot,
        hasBotCapability,
        hasMessageExtensionCapability,
        isSPFx,
        hasProvision,
    };
}
async function getMultiEnvFolders(projectPath) {
    const fx = path_1.default.join(projectPath, `.${teamsfx_api_1.ConfigFolderName}`);
    const fxConfig = path_1.default.join(fx, teamsfx_api_1.InputConfigsFolderName);
    const templateAppPackage = path_1.default.join(await utils_3.getProjectTemplatesFolderPath(projectPath), teamsfx_api_1.AppPackageFolderName);
    const fxState = path_1.default.join(fx, teamsfx_api_1.StatesFolderName);
    await fs_extra_1.default.ensureDir(fxConfig);
    await fs_extra_1.default.ensureDir(templateAppPackage);
    return { fx, fxConfig, templateAppPackage, fxState };
}
async function getBackupFolder(projectPath) {
    const backupName = ".backup";
    const backupPath = path_1.default.join(projectPath, backupName);
    if (!(await fs_extra_1.default.pathExists(backupPath))) {
        return backupPath;
    }
    // avoid conflict(rarely)
    return path_1.default.join(projectPath, `.teamsfx${backupName}`);
}
async function backup(projectPath, backupFolder) {
    const fx = path_1.default.join(projectPath, `.${teamsfx_api_1.ConfigFolderName}`);
    const backupFx = path_1.default.join(backupFolder, `.${teamsfx_api_1.ConfigFolderName}`);
    const backupAppPackage = path_1.default.join(backupFolder, teamsfx_api_1.AppPackageFolderName);
    await fs_extra_1.default.ensureDir(backupFx);
    await fs_extra_1.default.ensureDir(backupAppPackage);
    const fxFiles = [
        "env.default.json",
        "default.userdata",
        "settings.json",
        "local.env",
        "subscriptionInfo.json",
    ];
    for (const file of fxFiles) {
        if (await fs_extra_1.default.pathExists(path_1.default.join(fx, file))) {
            await fs_extra_1.default.copy(path_1.default.join(fx, file), path_1.default.join(backupFx, file));
        }
    }
    await copyManifest(projectPath, fx, backupAppPackage);
}
// append folder path to .gitignore under the project root.
async function addPathToGitignore(projectPath, ignoredPath, log) {
    const relativePath = path_1.default.relative(projectPath, ignoredPath).replace(/\\/g, "/");
    await addItemToGitignore(projectPath, relativePath, log);
}
exports.addPathToGitignore = addPathToGitignore;
// append item to .gitignore under the project root.
async function addItemToGitignore(projectPath, item, log) {
    const gitignorePath = path_1.default.join(projectPath, gitignoreFileName);
    try {
        await fs_extra_1.default.ensureFile(gitignorePath);
        const gitignoreContent = await fs_extra_1.default.readFile(gitignorePath, "UTF-8");
        if (gitignoreContent.indexOf(item) === -1) {
            const appendedContent = os_1.default.EOL + item;
            await fs_extra_1.default.appendFile(gitignorePath, appendedContent);
        }
    }
    catch (_a) {
        log.warning(`[core] Failed to add '${item}' to '${gitignorePath}', please do it manually.`);
    }
}
async function removeOldProjectFiles(projectPath) {
    const fx = path_1.default.join(projectPath, `.${teamsfx_api_1.ConfigFolderName}`);
    await fs_extra_1.default.remove(path_1.default.join(fx, "env.default.json"));
    await fs_extra_1.default.remove(path_1.default.join(fx, "default.userdata"));
    await fs_extra_1.default.remove(path_1.default.join(fx, "settings.json"));
    await fs_extra_1.default.remove(path_1.default.join(fx, "local.env"));
    await fs_extra_1.default.remove(path_1.default.join(projectPath, teamsfx_api_1.AppPackageFolderName));
    await fs_extra_1.default.remove(path_1.default.join(fx, "new.env.default.json"));
    // version <= 2.4.1, remove .fx/appPackage.
    await fs_extra_1.default.remove(path_1.default.join(fx, teamsfx_api_1.AppPackageFolderName));
    // version <= 3.2.1
    await fs_extra_1.default.remove(path_1.default.join(fx, constants_3.REMOTE_MANIFEST));
    await fs_extra_1.default.remove(path_1.default.join(fx, "color.png"));
    await fs_extra_1.default.remove(path_1.default.join(fx, "outline.png"));
}
async function ensureProjectSettings(settings, envDefaultPath) {
    var _a, _b;
    if (!settings.programmingLanguage || !settings.defaultFunctionName) {
        const envDefault = await fs_extra_1.default.readJson(envDefaultPath);
        settings.programmingLanguage =
            settings.programmingLanguage || ((_a = envDefault[constants_1.PluginNames.SOLUTION]) === null || _a === void 0 ? void 0 : _a[programmingLanguage]);
        settings.defaultFunctionName =
            settings.defaultFunctionName || ((_b = envDefault[constants_1.PluginNames.FUNC]) === null || _b === void 0 ? void 0 : _b[defaultFunctionName]);
    }
    settings.version = "2.0.0";
}
async function cleanup(projectPath, backupFolder) {
    const { _, fxConfig, templateAppPackage, fxState } = await getMultiEnvFolders(projectPath);
    await fs_extra_1.default.remove(fxConfig);
    await fs_extra_1.default.remove(templateAppPackage);
    await fs_extra_1.default.remove(fxState);
    await fs_extra_1.default.remove(path_1.default.join(templateAppPackage, ".."));
    if (await fs_extra_1.default.pathExists(path_1.default.join(fxConfig, "..", "new.env.default.json"))) {
        await fs_extra_1.default.remove(path_1.default.join(fxConfig, "..", "new.env.default.json"));
    }
    if (backupFolder) {
        await fs_extra_1.default.remove(backupFolder);
    }
}
async function needMigrateToArmAndMultiEnv(ctx) {
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    if (!inputs.projectPath) {
        return false;
    }
    const fxExist = await fs_extra_1.default.pathExists(path_1.default.join(inputs.projectPath, ".fx"));
    if (!fxExist) {
        return false;
    }
    const parameterEnvFileName = parameterFileNameTemplate.replace("@envName", environment_1.environmentManager.getDefaultEnvName());
    const envFileExist = await fs_extra_1.default.pathExists(path_1.default.join(inputs.projectPath, ".fx", "env.default.json"));
    const configDirExist = await fs_extra_1.default.pathExists(path_1.default.join(inputs.projectPath, ".fx", "configs"));
    const armParameterExist = await fs_extra_1.default.pathExists(path_1.default.join(inputs.projectPath, ".fx", "configs", parameterEnvFileName));
    if (envFileExist && (!armParameterExist || !configDirExist)) {
        return true;
    }
    return false;
}
exports.needMigrateToArmAndMultiEnv = needMigrateToArmAndMultiEnv;
async function migrateArm(ctx) {
    await generateArmTemplatesFiles(ctx);
    await generateArmParameterJson(ctx);
}
exports.migrateArm = migrateArm;
async function updateConfig(ctx) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    const fx = path_1.default.join(inputs.projectPath, `.${teamsfx_api_1.ConfigFolderName}`);
    const envConfig = await fs_extra_1.default.readJson(path_1.default.join(fx, "env.default.json"));
    if (envConfig[constants_2.ResourcePlugins.Bot]) {
        delete envConfig[constants_2.ResourcePlugins.Bot];
        envConfig[constants_2.ResourcePlugins.Bot] = { wayToRegisterBot: "create-new" };
        envConfig.solution.provisionSucceeded = false;
    }
    let needUpdate = false;
    let configPrefix = "";
    if (envConfig[solutionName][subscriptionId] && envConfig[solutionName][resourceGroupName]) {
        configPrefix = `/subscriptions/${envConfig[solutionName][subscriptionId]}/resourcegroups/${envConfig[solutionName][resourceGroupName]}`;
        needUpdate = true;
    }
    if (needUpdate && ((_a = envConfig[constants_2.ResourcePlugins.FrontendHosting]) === null || _a === void 0 ? void 0 : _a[EnvConfigName.StorageName])) {
        envConfig[constants_2.ResourcePlugins.FrontendHosting][EnvConfigName.StorageResourceId] = `${configPrefix}/providers/Microsoft.Storage/storageAccounts/${envConfig[constants_2.ResourcePlugins.FrontendHosting][EnvConfigName.StorageName]}`;
    }
    if (needUpdate && ((_b = envConfig[constants_2.ResourcePlugins.AzureSQL]) === null || _b === void 0 ? void 0 : _b[EnvConfigName.SqlEndpoint])) {
        envConfig[constants_2.ResourcePlugins.AzureSQL][EnvConfigName.SqlResourceId] = `${configPrefix}/providers/Microsoft.Sql/servers/${envConfig[constants_2.ResourcePlugins.AzureSQL][EnvConfigName.SqlEndpoint].split(".database.windows.net")[0]}`;
    }
    if (needUpdate && ((_c = envConfig[constants_2.ResourcePlugins.Function]) === null || _c === void 0 ? void 0 : _c[EnvConfigName.FuncAppName])) {
        envConfig[constants_2.ResourcePlugins.Function][EnvConfigName.FunctionAppResourceId] = `${configPrefix}/providers/Microsoft.Web/sites/${envConfig[constants_2.ResourcePlugins.Function][EnvConfigName.FuncAppName]}`;
        delete envConfig[constants_2.ResourcePlugins.Function][EnvConfigName.FuncAppName];
        if (envConfig[constants_2.ResourcePlugins.Function][EnvConfigName.StorageAccountName]) {
            delete envConfig[constants_2.ResourcePlugins.Function][EnvConfigName.StorageAccountName];
        }
        if (envConfig[constants_2.ResourcePlugins.Function][EnvConfigName.AppServicePlanName]) {
            delete envConfig[constants_2.ResourcePlugins.Function][EnvConfigName.AppServicePlanName];
        }
    }
    if (needUpdate && ((_d = envConfig[constants_2.ResourcePlugins.Identity]) === null || _d === void 0 ? void 0 : _d[EnvConfigName.Identity])) {
        envConfig[constants_2.ResourcePlugins.Identity][EnvConfigName.IdentityResourceId] = `${configPrefix}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/${envConfig[constants_2.ResourcePlugins.Identity][EnvConfigName.Identity]}`;
        envConfig[constants_2.ResourcePlugins.Identity][EnvConfigName.IdentityName] =
            envConfig[constants_2.ResourcePlugins.Identity][EnvConfigName.Identity];
        delete envConfig[constants_2.ResourcePlugins.Identity][EnvConfigName.Identity];
    }
    if (needUpdate && ((_e = envConfig[constants_2.ResourcePlugins.Identity]) === null || _e === void 0 ? void 0 : _e[EnvConfigName.IdentityId])) {
        envConfig[constants_2.ResourcePlugins.Identity][EnvConfigName.IdentityClientId] =
            envConfig[constants_2.ResourcePlugins.Identity][EnvConfigName.IdentityId];
        delete envConfig[constants_2.ResourcePlugins.Identity][EnvConfigName.IdentityId];
    }
    if (needUpdate && ((_f = envConfig[constants_2.ResourcePlugins.Apim]) === null || _f === void 0 ? void 0 : _f[EnvConfigName.ServiceName])) {
        envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.ServiceResourceId] = `${configPrefix}/providers/Microsoft.ApiManagement/service/${envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.ServiceName]}`;
        delete envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.ServiceName];
        if ((_g = envConfig[constants_2.ResourcePlugins.Apim]) === null || _g === void 0 ? void 0 : _g[EnvConfigName.ProductId]) {
            envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.ProductResourceId] = `${envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.ServiceResourceId]}/products/${envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.ProductId]}`;
            delete envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.ProductId];
        }
        if ((_h = envConfig[constants_2.ResourcePlugins.Apim]) === null || _h === void 0 ? void 0 : _h[EnvConfigName.OAuthServerId]) {
            envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.AuthServerResourceId] = `${envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.ServiceResourceId]}/authorizationServers/${envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.OAuthServerId]}`;
            delete envConfig[constants_2.ResourcePlugins.Apim][EnvConfigName.OAuthServerId];
        }
    }
    await fs_extra_1.default.writeFile(path_1.default.join(fx, "new.env.default.json"), JSON.stringify(envConfig, null, 4));
}
async function generateBicepsV3(projectSettings, inputs) {
    //init bicep folder
    {
        const bicepComponent = typedi_1.Container.get("bicep");
        const res = await bicepComponent.init(inputs.projectPath);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
    }
    const biceps = [];
    const context = utils_4.createContextV3(projectSettings);
    // teams-tab
    {
        const config = workflow_1.getComponent(projectSettings, constants_1.ComponentNames.TeamsTab);
        if (config) {
            const hosting = config.hosting || constants_1.ComponentNames.AzureStorage;
            const hostingComponent = typedi_1.Container.get(hosting);
            const clonedInputs = lodash_1.cloneDeep(inputs);
            lodash_1.assign(clonedInputs, {
                componentId: constants_1.ComponentNames.TeamsTab,
                scenario: constants_1.Scenarios.Tab,
            });
            const res = await hostingComponent.generateBicep(context, clonedInputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
            res.value.forEach((b) => biceps.push(b));
        }
    }
    // teams-bot
    {
        const config = workflow_1.getComponent(projectSettings, constants_1.ComponentNames.TeamsBot);
        if (config) {
            // bot hosting
            const hosting = config.hosting || constants_1.ComponentNames.AzureWebApp;
            {
                const hostingComponent = typedi_1.Container.get(hosting);
                const clonedInputs = lodash_1.cloneDeep(inputs);
                lodash_1.assign(clonedInputs, {
                    componentId: constants_1.ComponentNames.TeamsBot,
                    scenario: constants_1.Scenarios.Bot,
                });
                const res = await hostingComponent.generateBicep(context, clonedInputs);
                if (res.isErr())
                    return teamsfx_api_1.err(res.error);
                res.value.forEach((b) => biceps.push(b));
            }
            // bot service
            {
                const clonedInputs = lodash_1.cloneDeep(inputs);
                lodash_1.assign(clonedInputs, {
                    hosting: hosting,
                    scenario: constants_1.Scenarios.Bot,
                });
                const botService = typedi_1.Container.get(constants_1.ComponentNames.BotService);
                const res = await botService.generateBicep(context, clonedInputs);
                if (res.isErr())
                    return teamsfx_api_1.err(res.error);
                res.value.forEach((b) => biceps.push(b));
            }
        }
    }
    // teams-api
    {
        const config = workflow_1.getComponent(projectSettings, constants_1.ComponentNames.TeamsApi);
        if (config) {
            const clonedInputs = lodash_1.cloneDeep(inputs);
            lodash_1.assign(clonedInputs, {
                componentId: constants_1.ComponentNames.TeamsApi,
                hosting: constants_1.ComponentNames.Function,
                scenario: constants_1.Scenarios.Api,
            });
            const functionComponent = typedi_1.Container.get(constants_1.ComponentNames.Function);
            const res = await functionComponent.generateBicep(context, clonedInputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
            res.value.forEach((b) => biceps.push(b));
        }
    }
    // identity
    {
        const identity = workflow_1.getComponent(projectSettings, constants_1.ComponentNames.Identity);
        if (identity) {
            const clonedInputs = lodash_1.cloneDeep(inputs);
            lodash_1.assign(clonedInputs, {
                componentId: "",
                scenario: "",
            });
            const identityComponent = typedi_1.Container.get(constants_1.ComponentNames.Identity);
            const res = await identityComponent.generateBicep(context, clonedInputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
            res.value.forEach((b) => biceps.push(b));
        }
    }
    // apim
    {
        const config = workflow_1.getComponent(projectSettings, constants_1.ComponentNames.APIM);
        if (config) {
            const resource = typedi_1.Container.get(constants_1.ComponentNames.APIM);
            const res = await resource.generateBicep(context, inputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
            res.value.forEach((b) => biceps.push(b));
        }
    }
    // keyvault
    {
        const config = workflow_1.getComponent(projectSettings, constants_1.ComponentNames.KeyVault);
        if (config) {
            const resource = typedi_1.Container.get(constants_1.ComponentNames.KeyVault);
            const res = await resource.generateBicep(context, inputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
            res.value.forEach((b) => biceps.push(b));
        }
    }
    // sql
    {
        const config = workflow_1.getComponent(projectSettings, constants_1.ComponentNames.AzureSQL);
        if (config) {
            const provisionType = "server";
            const clonedInputs = lodash_1.cloneDeep(inputs);
            clonedInputs.provisionType = provisionType;
            const sqlResource = typedi_1.Container.get(constants_1.ComponentNames.AzureSQL);
            const res = await sqlResource.generateBicep(context, clonedInputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
            res.value.forEach((b) => biceps.push(b));
        }
    }
    // aad
    {
        const config = workflow_1.getComponent(projectSettings, constants_1.ComponentNames.AadApp);
        if (config) {
            const aadApp = typedi_1.Container.get(constants_1.ComponentNames.AadApp);
            const res = await aadApp.generateBicep(context, inputs);
            if (res.isErr())
                return teamsfx_api_1.err(res.error);
            res.value.forEach((b) => biceps.push(b));
        }
    }
    // persist bicep
    {
        const bicepRes = await utils_4.bicepUtils.persistBiceps(inputs.projectPath, utils_3.convertToAlphanumericOnly(context.projectSetting.appName), biceps);
        if (bicepRes.isErr())
            return bicepRes;
    }
    //  generate config bicep
    {
        const res = await utils_4.generateConfigBiceps(context, inputs);
        if (res.isErr())
            return teamsfx_api_1.err(res.error);
    }
    return teamsfx_api_1.ok(undefined);
}
exports.generateBicepsV3 = generateBicepsV3;
async function generateArmTemplatesFiles(ctx) {
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    const fx = path_1.default.join(inputs.projectPath, `.${teamsfx_api_1.ConfigFolderName}`);
    const fxConfig = path_1.default.join(fx, teamsfx_api_1.InputConfigsFolderName);
    const templateAzure = path_1.default.join(inputs.projectPath, "templates", "azure");
    await fs_extra_1.default.ensureDir(fxConfig);
    await fs_extra_1.default.ensureDir(templateAzure);
    let projectSettings = ctx.projectSettings;
    if (!projectSettings) {
        projectSettings = await migrateProjectSettings(inputs.projectPath);
    }
    const genRes = await generateBicepsV3(projectSettings, inputs);
    if (genRes.isErr())
        throw genRes.error;
    const parameterEnvFileName = parameterFileNameTemplate.replace("@envName", environment_1.environmentManager.getDefaultEnvName());
    if (!(await fs_extra_1.default.pathExists(path_1.default.join(fxConfig, parameterEnvFileName)))) {
        throw new teamsfx_api_1.SystemError(error_1.CoreSource, "GenerateArmTemplateFailed", `Failed to generate ${parameterEnvFileName} on migration`);
    }
}
async function generateArmParameterJson(ctx) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u;
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    const fx = path_1.default.join(inputs.projectPath, `.${teamsfx_api_1.ConfigFolderName}`);
    const fxConfig = path_1.default.join(fx, teamsfx_api_1.InputConfigsFolderName);
    const envConfig = await fs_extra_1.default.readJson(path_1.default.join(fx, "env.default.json"));
    const parameterEnvFileName = parameterFileNameTemplate.replace("@envName", environment_1.environmentManager.getDefaultEnvName());
    const targetJson = await fs_extra_1.default.readJson(path_1.default.join(fxConfig, parameterEnvFileName));
    const parameterObj = targetJson["parameters"]["provisionParameters"]["value"];
    // frontend hosting
    if ((_a = envConfig[constants_2.ResourcePlugins.FrontendHosting]) === null || _a === void 0 ? void 0 : _a[EnvConfigName.StorageName]) {
        parameterObj[ArmParameters.FEStorageName] =
            envConfig[constants_2.ResourcePlugins.FrontendHosting][EnvConfigName.StorageName];
    }
    // manage identity
    if ((_b = envConfig[constants_2.ResourcePlugins.Identity]) === null || _b === void 0 ? void 0 : _b[EnvConfigName.Identity]) {
        // Teams Toolkit <= 2.7
        parameterObj[ArmParameters.IdentityName] =
            envConfig[constants_2.ResourcePlugins.Identity][EnvConfigName.Identity];
    }
    else if ((_c = envConfig[constants_2.ResourcePlugins.Identity]) === null || _c === void 0 ? void 0 : _c[EnvConfigName.IdentityName]) {
        // Teams Toolkit >= 2.8
        parameterObj[ArmParameters.IdentityName] =
            envConfig[constants_2.ResourcePlugins.Identity][EnvConfigName.IdentityName];
    }
    // azure SQL
    if ((_d = envConfig[constants_2.ResourcePlugins.AzureSQL]) === null || _d === void 0 ? void 0 : _d[EnvConfigName.SqlEndpoint]) {
        parameterObj[ArmParameters.SQLServer] =
            envConfig[constants_2.ResourcePlugins.AzureSQL][EnvConfigName.SqlEndpoint].split(".database.windows.net")[0];
    }
    if ((_e = envConfig[constants_2.ResourcePlugins.AzureSQL]) === null || _e === void 0 ? void 0 : _e[EnvConfigName.SqlDataBase]) {
        parameterObj[ArmParameters.SQLDatabase] =
            envConfig[constants_2.ResourcePlugins.AzureSQL][EnvConfigName.SqlDataBase];
    }
    // SimpleAuth
    if ((_f = envConfig[constants_2.ResourcePlugins.SimpleAuth]) === null || _f === void 0 ? void 0 : _f[EnvConfigName.SkuName]) {
        parameterObj[ArmParameters.SimpleAuthSku] =
            envConfig[constants_2.ResourcePlugins.SimpleAuth][EnvConfigName.SkuName];
    }
    if ((_g = envConfig[constants_2.ResourcePlugins.SimpleAuth]) === null || _g === void 0 ? void 0 : _g[EnvConfigName.Endpoint]) {
        const simpleAuthHost = new URL((_h = envConfig[constants_2.ResourcePlugins.SimpleAuth]) === null || _h === void 0 ? void 0 : _h[EnvConfigName.Endpoint])
            .hostname;
        const simpleAuthName = simpleAuthHost.split(".")[0];
        parameterObj[ArmParameters.SimpleAuthWebAppName] = parameterObj[ArmParameters.SimpleAuthServerFarm] = simpleAuthName;
    }
    // Function
    if ((_j = envConfig[constants_2.ResourcePlugins.Function]) === null || _j === void 0 ? void 0 : _j[EnvConfigName.AppServicePlanName]) {
        parameterObj[ArmParameters.functionServerName] =
            envConfig[constants_2.ResourcePlugins.Function][EnvConfigName.AppServicePlanName];
    }
    if ((_k = envConfig[constants_2.ResourcePlugins.Function]) === null || _k === void 0 ? void 0 : _k[EnvConfigName.StorageAccountName]) {
        parameterObj[ArmParameters.functionStorageName] =
            envConfig[constants_2.ResourcePlugins.Function][EnvConfigName.StorageAccountName];
    }
    if ((_l = envConfig[constants_2.ResourcePlugins.Function]) === null || _l === void 0 ? void 0 : _l[EnvConfigName.FuncAppName]) {
        parameterObj[ArmParameters.functionAppName] =
            envConfig[constants_2.ResourcePlugins.Function][EnvConfigName.FuncAppName];
    }
    // Bot
    if ((_m = envConfig[constants_2.ResourcePlugins.Bot]) === null || _m === void 0 ? void 0 : _m[EnvConfigName.SkuName]) {
        parameterObj[ArmParameters.botWebAppSku] =
            (_o = envConfig[constants_2.ResourcePlugins.Bot]) === null || _o === void 0 ? void 0 : _o[EnvConfigName.SkuName];
    }
    // Apim
    if ((_p = envConfig[constants_2.ResourcePlugins.Apim]) === null || _p === void 0 ? void 0 : _p[EnvConfigName.ServiceName]) {
        parameterObj[ArmParameters.ApimServiceName] =
            (_q = envConfig[constants_2.ResourcePlugins.Apim]) === null || _q === void 0 ? void 0 : _q[EnvConfigName.ServiceName];
    }
    if ((_r = envConfig[constants_2.ResourcePlugins.Apim]) === null || _r === void 0 ? void 0 : _r[EnvConfigName.ProductId]) {
        parameterObj[ArmParameters.ApimProductName] =
            (_s = envConfig[constants_2.ResourcePlugins.Apim]) === null || _s === void 0 ? void 0 : _s[EnvConfigName.ProductId];
    }
    if ((_t = envConfig[constants_2.ResourcePlugins.Apim]) === null || _t === void 0 ? void 0 : _t[EnvConfigName.OAuthServerId]) {
        parameterObj[ArmParameters.ApimOauthServerName] =
            (_u = envConfig[constants_2.ResourcePlugins.Apim]) === null || _u === void 0 ? void 0 : _u[EnvConfigName.OAuthServerId];
    }
    await fs_extra_1.default.writeFile(path_1.default.join(fxConfig, parameterEnvFileName), JSON.stringify(targetJson, null, 4));
}
//# sourceMappingURL=projectMigrator.js.map