import { Middleware } from "@feathersjs/hooks/lib";
import { CoreHookContext } from "../types";
import { MigrationContext } from "./utils/migrationContext";
import { VersionForMigration } from "./types";
export declare const Constants: {
    vscodeProvisionBicepPath: string;
    launchJsonPath: string;
    tasksJsonPath: string;
    reportName: string;
    envWriteOption: {
        encoding: string;
        flag: string;
    };
    envFilePrefix: string;
};
export declare const learnMoreLink = "https://aka.ms/teams-toolkit-5.0-upgrade";
export declare const errorNames: {
    appPackageNotExist: string;
    manifestTemplateNotExist: string;
};
export declare const ProjectMigratorMWV3: Middleware;
export declare function wrapRunMigration(context: MigrationContext, exec: (context: MigrationContext) => void): Promise<void>;
export declare function migrate(context: MigrationContext): Promise<void>;
export declare function checkVersionForMigration(ctx: CoreHookContext): Promise<VersionForMigration>;
export declare function generateAppYml(context: MigrationContext): Promise<void>;
export declare function updateLaunchJson(context: MigrationContext): Promise<void>;
export declare function manifestsMigration(context: MigrationContext): Promise<void>;
export declare function azureParameterMigration(context: MigrationContext): Promise<void>;
export declare function askUserConfirm(ctx: CoreHookContext, versionForMigration: VersionForMigration): Promise<boolean>;
export declare function popupMessage(versionForMigration: VersionForMigration): Promise<string | undefined>;
export declare function generateLocalConfig(context: MigrationContext): Promise<void>;
export declare function configsMigration(context: MigrationContext): Promise<void>;
export declare function statesMigration(context: MigrationContext): Promise<void>;
export declare function userdataMigration(context: MigrationContext): Promise<void>;
export declare function debugMigration(context: MigrationContext): Promise<void>;
export declare function checkapimPluginExists(pjSettings: any): boolean;
export declare function generateApimPluginEnvContent(context: MigrationContext): Promise<void>;
export declare function updateGitignore(context: MigrationContext): Promise<void>;
//# sourceMappingURL=projectMigratorV3.d.ts.map