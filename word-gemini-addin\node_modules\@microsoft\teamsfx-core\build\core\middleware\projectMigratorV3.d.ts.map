{"version": 3, "file": "projectMigratorV3.d.ts", "sourceRoot": "", "sources": ["../../../src/core/middleware/projectMigratorV3.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,UAAU,EAAgB,MAAM,uBAAuB,CAAC;AACjE,OAAO,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AAC3C,OAAO,EAAgB,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AA0E1E,OAAO,EAAE,mBAAmB,EAAE,MAAM,SAAS,CAAC;AAI9C,eAAO,MAAM,SAAS;;;;;;;;;;CAWrB,CAAC;AAEF,eAAO,MAAM,aAAa,6CAA6C,CAAC;AACxE,eAAO,MAAM,UAAU;;;CAGtB,CAAC;AAmBF,eAAO,MAAM,mBAAmB,EAAE,UAsCjC,CAAC;AAEF,wBAAsB,gBAAgB,CACpC,OAAO,EAAE,gBAAgB,EACzB,IAAI,EAAE,CAAC,OAAO,EAAE,gBAAgB,KAAK,IAAI,GACxC,OAAO,CAAC,IAAI,CAAC,CAoCf;AAiBD,wBAAsB,OAAO,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CAItE;AAMD,wBAAsB,wBAAwB,CAAC,GAAG,EAAE,eAAe,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAWjG;AAED,wBAAsB,cAAc,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CAiB7E;AAED,wBAAsB,gBAAgB,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CAW/E;AAWD,wBAAsB,kBAAkB,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CA2EjF;AAED,wBAAsB,uBAAuB,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CA2BtF;AAED,wBAAsB,cAAc,CAClC,GAAG,EAAE,eAAe,EACpB,mBAAmB,EAAE,mBAAmB,GACvC,OAAO,CAAC,OAAO,CAAC,CAyBlB;AAED,wBAAsB,YAAY,CAChC,mBAAmB,EAAE,mBAAmB,GACvC,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,CAQ7B;AAED,wBAAsB,mBAAmB,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CAKlF;AAED,wBAAsB,gBAAgB,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CA2D/E;AAED,wBAAsB,eAAe,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CA6C9E;AAED,wBAAsB,iBAAiB,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CAoChF;AAED,wBAAsB,cAAc,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CA+D7E;AAED,wBAAgB,qBAAqB,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAQ9D;AAED,wBAAsB,4BAA4B,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CAyC3F;AAED,wBAAsB,eAAe,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,CAgB9E"}