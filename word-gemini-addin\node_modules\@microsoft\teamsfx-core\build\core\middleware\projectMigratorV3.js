"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateGitignore = exports.generateApimPluginEnvContent = exports.checkapimPluginExists = exports.debugMigration = exports.userdataMigration = exports.statesMigration = exports.configsMigration = exports.generateLocalConfig = exports.popupMessage = exports.askUserConfirm = exports.azureParameterMigration = exports.manifestsMigration = exports.updateLaunchJson = exports.generateAppYml = exports.checkVersionForMigration = exports.migrate = exports.wrapRunMigration = exports.ProjectMigratorMWV3 = exports.errorNames = exports.learnMoreLink = exports.Constants = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const migrationContext_1 = require("./utils/migrationContext");
const projectMigrator_1 = require("./projectMigrator");
const path = tslib_1.__importStar(require("path"));
const projectSettingsLoader_1 = require("./projectSettingsLoader");
const telemetry_1 = require("../../common/telemetry");
const constants_1 = require("../../component/constants");
const globalVars_1 = require("../globalVars");
const error_1 = require("../error");
const appYmlGenerator_1 = require("./utils/appYmlGenerator");
const fs = tslib_1.__importStar(require("fs-extra"));
const constants_2 = require("../../component/resource/appManifest/constants");
const MigrationUtils_1 = require("./utils/MigrationUtils");
const v3MigrationUtils_1 = require("./utils/v3MigrationUtils");
const commentJson = tslib_1.__importStar(require("comment-json"));
const debugMigrationContext_1 = require("./utils/debug/debugMigrationContext");
const debugV3MigrationUtils_1 = require("./utils/debug/debugV3MigrationUtils");
const taskMigrator_1 = require("./utils/debug/taskMigrator");
const appLocalYmlGenerator_1 = require("./utils/debug/appLocalYmlGenerator");
const os_1 = require("os");
const folder_1 = require("../../folder");
const versionMetadata_1 = require("../../common/versionMetadata");
const tools_1 = require("../../common/tools");
const environment_1 = require("../environment");
const localizeUtils_1 = require("../../common/localizeUtils");
exports.Constants = {
    vscodeProvisionBicepPath: "./templates/azure/provision.bicep",
    launchJsonPath: ".vscode/launch.json",
    tasksJsonPath: ".vscode/tasks.json",
    reportName: "migrationReport.md",
    envWriteOption: {
        // .env.{env} file might be already exist, use append mode (flag: a+)
        encoding: "utf8",
        flag: "a+",
    },
    envFilePrefix: ".env.",
};
exports.learnMoreLink = "https://aka.ms/teams-toolkit-5.0-upgrade";
exports.errorNames = {
    appPackageNotExist: "AppPackageNotExist",
    manifestTemplateNotExist: "ManifestTemplateNotExist",
};
const migrationMessageButtons = [projectMigrator_1.learnMoreText, projectMigrator_1.upgradeButton];
const subMigrations = [
    preMigration,
    manifestsMigration,
    generateAppYml,
    generateLocalConfig,
    configsMigration,
    statesMigration,
    userdataMigration,
    generateApimPluginEnvContent,
    updateLaunchJson,
    azureParameterMigration,
    debugMigration,
    updateGitignore,
];
const ProjectMigratorMWV3 = async (ctx, next) => {
    const versionForMigration = await checkVersionForMigration(ctx);
    // abandoned v3 project which will not be supported. Show user the message to create new project.
    if (versionForMigration.source === versionMetadata_1.VersionSource.settings) {
        await (globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.showMessage("warn", localizeUtils_1.getLocalizedString("core.migrationV3.abandonedProject"), true));
        ctx.result = teamsfx_api_1.err(error_1.AbandonedProjectError());
    }
    else if (versionForMigration.state === versionMetadata_1.VersionState.upgradeable && projectMigrator_1.checkMethod(ctx)) {
        if (!projectMigrator_1.checkUserTasks(ctx)) {
            ctx.result = teamsfx_api_1.ok(undefined);
            return;
        }
        if (!tools_1.isMigrationV3Enabled()) {
            await (globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.showMessage("warn", localizeUtils_1.getLocalizedString("core.migrationV3.CreateNewProject"), true));
            ctx.result = teamsfx_api_1.err(error_1.ToolkitNotSupportError());
            return false;
        }
        const skipUserConfirm = v3MigrationUtils_1.getParameterFromCxt(ctx, "skipUserConfirm");
        if (!skipUserConfirm && !(await askUserConfirm(ctx, versionForMigration))) {
            return;
        }
        const migrationContext = await migrationContext_1.MigrationContext.create(ctx);
        await wrapRunMigration(migrationContext, migrate);
        ctx.result = teamsfx_api_1.ok(undefined);
    }
    else {
        // continue next step only when:
        // 1. no need to upgrade the project;
        // 2. no need to update Teams Toolkit version;
        await next();
    }
};
exports.ProjectMigratorMWV3 = ProjectMigratorMWV3;
async function wrapRunMigration(context, exec) {
    try {
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorMigrateStartV3);
        await exec(context);
        await showSummaryReport(context);
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorMigrateV3, context.telemetryProperties);
    }
    catch (error) {
        let fxError;
        if (error instanceof teamsfx_api_1.UserError || error instanceof teamsfx_api_1.SystemError) {
            fxError = error;
        }
        else {
            if (!(error instanceof Error)) {
                error = new Error(error.toString());
            }
            fxError = new teamsfx_api_1.SystemError({
                error,
                source: telemetry_1.Component.core,
                name: constants_1.ErrorConstants.unhandledError,
                message: error.message,
                displayMessage: error.message,
            });
        }
        telemetry_1.sendTelemetryErrorEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorV3Error, fxError, context.telemetryProperties);
        await rollbackMigration(context);
        throw error;
    }
    await context.removeFxV2();
}
exports.wrapRunMigration = wrapRunMigration;
async function rollbackMigration(context) {
    await context.cleanModifiedPaths();
    await context.restoreBackup();
    await context.cleanBackup();
}
async function showSummaryReport(context) {
    var _a, _b;
    const summaryPath = path.join(context.backupPath, exports.Constants.reportName);
    const templatePath = path.join(folder_1.getTemplatesFolder(), "core/v3Migration", exports.Constants.reportName);
    const content = await fs.readFile(templatePath);
    await fs.writeFile(summaryPath, content);
    await ((_b = (_a = globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui) === null || _a === void 0 ? void 0 : _a.openFile) === null || _b === void 0 ? void 0 : _b.call(_a, summaryPath));
}
async function migrate(context) {
    for (const subMigration of subMigrations) {
        await subMigration(context);
    }
}
exports.migrate = migrate;
async function preMigration(context) {
    await context.backup(versionMetadata_1.MetadataV2.configFolder);
}
async function checkVersionForMigration(ctx) {
    const versionInfo = await v3MigrationUtils_1.getProjectVersion(ctx);
    const versionState = v3MigrationUtils_1.getVersionState(versionInfo);
    const platform = v3MigrationUtils_1.getParameterFromCxt(ctx, "platform", teamsfx_api_1.Platform.VSCode);
    return {
        currentVersion: versionInfo.version,
        source: versionInfo.source,
        state: versionState,
        platform: platform,
    };
}
exports.checkVersionForMigration = checkVersionForMigration;
async function generateAppYml(context) {
    var _a;
    const bicepContent = await v3MigrationUtils_1.readBicepContent(context);
    const oldProjectSettings = await loadProjectSettings(context.projectPath);
    const appYmlGenerator = new appYmlGenerator_1.AppYmlGenerator(oldProjectSettings, bicepContent, context.projectPath);
    const appYmlString = await appYmlGenerator.generateAppYml();
    await context.fsWriteFile(versionMetadata_1.MetadataV3.configFile, appYmlString);
    if (((_a = oldProjectSettings.programmingLanguage) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === "csharp") {
        const placeholderMappings = await debugV3MigrationUtils_1.getPlaceholderMappings(context);
        const appLocalYmlString = await appYmlGenerator.generateAppLocalYml(placeholderMappings);
        await context.fsWriteFile(versionMetadata_1.MetadataV3.localConfigFile, appLocalYmlString);
    }
}
exports.generateAppYml = generateAppYml;
async function updateLaunchJson(context) {
    const launchJsonPath = path.join(context.projectPath, exports.Constants.launchJsonPath);
    if (await fs.pathExists(launchJsonPath)) {
        await context.backup(exports.Constants.launchJsonPath);
        const launchJsonContent = await fs.readFile(launchJsonPath, "utf8");
        const result = launchJsonContent
            .replace(/\${teamsAppId}/g, "${dev:teamsAppId}") // TODO: set correct default env if user deletes dev, wait for other PR to get env list utility
            .replace(/\${localTeamsAppId}/g, "${local:teamsAppId}")
            .replace(/\${localTeamsAppInternalId}/g, "${local:teamsAppInternalId}"); // For M365 apps
        await context.fsWriteFile(exports.Constants.launchJsonPath, result);
    }
}
exports.updateLaunchJson = updateLaunchJson;
async function loadProjectSettings(projectPath) {
    const oldProjectSettings = await projectSettingsLoader_1.loadProjectSettingsByProjectPathV2(projectPath, true, true);
    if (oldProjectSettings.isOk()) {
        return oldProjectSettings.value;
    }
    else {
        throw oldProjectSettings.error;
    }
}
async function manifestsMigration(context) {
    // Backup templates/appPackage
    const oldAppPackageFolderPath = path.join(v3MigrationUtils_1.getTemplateFolderPath(context), teamsfx_api_1.AppPackageFolderName);
    const oldAppPackageFolderBackupRes = await context.backup(oldAppPackageFolderPath);
    if (!oldAppPackageFolderBackupRes) {
        // templates/appPackage does not exists
        // invalid teamsfx project
        throw error_1.MigrationError(new Error("templates/appPackage does not exist"), exports.errorNames.appPackageNotExist, exports.learnMoreLink);
    }
    // Ensure appPackage
    await context.fsEnsureDir(teamsfx_api_1.AppPackageFolderName);
    // Copy templates/appPackage/resources
    const oldResourceFolderPath = path.join(oldAppPackageFolderPath, "resources");
    const oldResourceFolderExists = await fs.pathExists(path.join(context.projectPath, oldResourceFolderPath));
    if (oldResourceFolderExists) {
        const resourceFolderPath = path.join(teamsfx_api_1.AppPackageFolderName, "resources");
        await context.fsCopy(oldResourceFolderPath, resourceFolderPath);
    }
    // Read Bicep
    const bicepContent = await v3MigrationUtils_1.readBicepContent(context);
    // Read capability project settings
    const projectSettings = await loadProjectSettings(context.projectPath);
    const capabilities = v3MigrationUtils_1.getCapabilitySsoStatus(projectSettings);
    const appIdUri = v3MigrationUtils_1.generateAppIdUri(capabilities);
    const isSpfx = tools_1.isSPFxProject(projectSettings);
    // Read Teams app manifest and save to templates/appPackage/manifest.json
    const oldManifestPath = path.join(oldAppPackageFolderPath, constants_2.MANIFEST_TEMPLATE_CONSOLIDATE);
    const oldManifestExists = await fs.pathExists(path.join(context.projectPath, oldManifestPath));
    if (oldManifestExists) {
        const manifestPath = path.join(teamsfx_api_1.AppPackageFolderName, versionMetadata_1.MetadataV3.teamsManifestFileName);
        let oldManifest = await fs.readFile(path.join(context.projectPath, oldManifestPath), "utf8");
        oldManifest = v3MigrationUtils_1.replaceAppIdUri(oldManifest, appIdUri);
        const manifest = MigrationUtils_1.replacePlaceholdersForV3(oldManifest, bicepContent);
        if (isSpfx) {
            await v3MigrationUtils_1.updateAndSaveManifestForSpfx(context, manifest);
        }
        else {
            await context.fsWriteFile(manifestPath, manifest);
        }
    }
    else {
        // templates/appPackage/manifest.template.json does not exist
        throw error_1.MigrationError(new Error("templates/appPackage/manifest.template.json does not exist"), exports.errorNames.manifestTemplateNotExist, exports.learnMoreLink);
    }
    // Read AAD app manifest and save to ./aad.manifest.template.json
    const oldAadManifestPath = path.join(oldAppPackageFolderPath, "aad.template.json");
    const oldAadManifestExists = await fs.pathExists(path.join(context.projectPath, oldAadManifestPath));
    if (oldAadManifestExists) {
        let oldAadManifest = await fs.readFile(path.join(context.projectPath, oldAadManifestPath), "utf-8");
        oldAadManifest = v3MigrationUtils_1.replaceAppIdUri(oldAadManifest, appIdUri);
        const aadManifest = MigrationUtils_1.replacePlaceholdersForV3(oldAadManifest, bicepContent);
        await context.fsWriteFile("aad.manifest.template.json", aadManifest);
    }
    await context.fsRemove(oldAppPackageFolderPath);
}
exports.manifestsMigration = manifestsMigration;
async function azureParameterMigration(context) {
    // Ensure `.fx/configs` exists
    const configFolderPath = path.join(".fx", teamsfx_api_1.InputConfigsFolderName);
    const configFolderPathExists = await context.fsPathExists(configFolderPath);
    if (!configFolderPathExists) {
        // Keep same practice now. Needs dicussion whether to throw error.
        return;
    }
    // Read Bicep
    const azureFolderPath = path.join(v3MigrationUtils_1.getTemplateFolderPath(context), "azure");
    const bicepContent = await v3MigrationUtils_1.readBicepContent(context);
    const fileNames = v3MigrationUtils_1.fsReadDirSync(context, configFolderPath);
    for (const fileName of fileNames) {
        if (!fileName.startsWith("azure.parameters.")) {
            continue;
        }
        const content = await fs.readFile(path.join(context.projectPath, configFolderPath, fileName), "utf-8");
        const newContent = MigrationUtils_1.replacePlaceholdersForV3(content, bicepContent);
        await context.fsWriteFile(path.join(azureFolderPath, fileName), newContent);
    }
}
exports.azureParameterMigration = azureParameterMigration;
async function askUserConfirm(ctx, versionForMigration) {
    telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorNotificationStart);
    let answer;
    do {
        answer = await popupMessage(versionForMigration);
        if (answer === projectMigrator_1.learnMoreText) {
            globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.openUrl(exports.learnMoreLink);
        }
    } while (answer === projectMigrator_1.learnMoreText);
    if (!answer || !migrationMessageButtons.includes(answer)) {
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorNotification, {
            [telemetry_1.TelemetryProperty.Status]: telemetry_1.ProjectMigratorStatus.Cancel,
        });
        const link = v3MigrationUtils_1.getDownloadLinkByVersionAndPlatform(versionForMigration.currentVersion, versionForMigration.platform);
        ctx.result = teamsfx_api_1.err(error_1.UpgradeV3CanceledError(link, versionForMigration.currentVersion));
        v3MigrationUtils_1.outputCancelMessage(versionForMigration.currentVersion, versionForMigration.platform);
        return false;
    }
    telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.ProjectMigratorNotification, {
        [telemetry_1.TelemetryProperty.Status]: telemetry_1.ProjectMigratorStatus.OK,
    });
    return true;
}
exports.askUserConfirm = askUserConfirm;
async function popupMessage(versionForMigration) {
    const res = await (globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.ui.showMessage("warn", v3MigrationUtils_1.migrationNotificationMessage(versionForMigration), true, ...migrationMessageButtons));
    return (res === null || res === void 0 ? void 0 : res.isOk()) ? res.value : undefined;
}
exports.popupMessage = popupMessage;
async function generateLocalConfig(context) {
    if (!(await context.fsPathExists(path.join(".fx", "configs", "config.local.json")))) {
        const oldProjectSettings = await loadProjectSettings(context.projectPath);
        await environment_1.environmentManager.createLocalEnv(context.projectPath, oldProjectSettings.appName);
    }
}
exports.generateLocalConfig = generateLocalConfig;
async function configsMigration(context) {
    // general
    if (await context.fsPathExists(path.join(".fx", "configs"))) {
        // if ./fx/states/ exists
        const fileNames = v3MigrationUtils_1.fsReadDirSync(context, path.join(".fx", "configs")); // search all files, get file names
        for (const fileName of fileNames)
            if (fileName.startsWith("config.")) {
                const fileRegex = new RegExp("(config\\.)([a-zA-Z0-9_-]*)(\\.json)", "g"); // state.*.json
                const fileNamesArray = fileRegex.exec(fileName);
                if (fileNamesArray != null) {
                    // get envName
                    const envName = fileNamesArray[2];
                    // create .env.{env} file if not exist
                    await context.fsEnsureDir(versionMetadata_1.MetadataV3.defaultEnvironmentFolder);
                    if (!(await context.fsPathExists(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName))))
                        await context.fsCreateFile(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName));
                    const obj = await v3MigrationUtils_1.readJsonFile(context, path.join(".fx", "configs", "config." + envName + ".json"));
                    if (obj["manifest"]) {
                        const bicepContent = await v3MigrationUtils_1.readBicepContent(context);
                        const teamsfx_env = fs
                            .readFileSync(path.join(context.projectPath, versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName))
                            .toString()
                            .includes("TEAMSFX_ENV=")
                            ? ""
                            : "TEAMSFX_ENV=" + envName + os_1.EOL;
                        // convert every name and add the env name at the first line
                        const envData = teamsfx_env +
                            v3MigrationUtils_1.jsonObjectNamesConvertV3(obj["manifest"], "manifest.", "", MigrationUtils_1.FileType.CONFIG, bicepContent);
                        await context.fsWriteFile(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName), envData, exports.Constants.envWriteOption);
                    }
                }
            }
    }
}
exports.configsMigration = configsMigration;
async function statesMigration(context) {
    // general
    if (await context.fsPathExists(path.join(".fx", "states"))) {
        // if ./fx/states/ exists
        const fileNames = v3MigrationUtils_1.fsReadDirSync(context, path.join(".fx", "states")); // search all files, get file names
        for (const fileName of fileNames)
            if (fileName.startsWith("state.")) {
                const fileRegex = new RegExp("(state\\.)([a-zA-Z0-9_-]*)(\\.json)", "g"); // state.*.json
                const fileNamesArray = fileRegex.exec(fileName);
                if (fileNamesArray != null) {
                    // get envName
                    const envName = fileNamesArray[2];
                    // create .env.{env} file if not exist
                    await context.fsEnsureDir(versionMetadata_1.MetadataV3.defaultEnvironmentFolder);
                    if (!(await context.fsPathExists(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName))))
                        await context.fsCreateFile(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName));
                    const obj = await v3MigrationUtils_1.readJsonFile(context, path.join(".fx", "states", "state." + envName + ".json"));
                    if (obj) {
                        const bicepContent = await v3MigrationUtils_1.readBicepContent(context);
                        // convert every name
                        const envData = v3MigrationUtils_1.jsonObjectNamesConvertV3(obj, "state.", "", MigrationUtils_1.FileType.STATE, bicepContent);
                        await context.fsWriteFile(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName), envData, exports.Constants.envWriteOption);
                    }
                }
            }
    }
}
exports.statesMigration = statesMigration;
async function userdataMigration(context) {
    // general
    if (await context.fsPathExists(path.join(".fx", "states"))) {
        // if ./fx/states/ exists
        const fileNames = v3MigrationUtils_1.fsReadDirSync(context, path.join(".fx", "states")); // search all files, get file names
        for (const fileName of fileNames)
            if (fileName.endsWith(".userdata")) {
                const fileRegex = new RegExp("([a-zA-Z0-9_-]*)(\\.userdata)", "g"); // state.*.json
                const fileNamesArray = fileRegex.exec(fileName);
                if (fileNamesArray != null) {
                    // get envName
                    const envName = fileNamesArray[1];
                    // create .env.{env} file if not exist
                    await context.fsEnsureDir(versionMetadata_1.MetadataV3.defaultEnvironmentFolder);
                    if (!(await context.fsPathExists(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName))))
                        await context.fsCreateFile(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName));
                    const bicepContent = await v3MigrationUtils_1.readBicepContent(context);
                    const envData = await v3MigrationUtils_1.readAndConvertUserdata(context, path.join(".fx", "states", fileName), bicepContent);
                    await context.fsWriteFile(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName), envData, exports.Constants.envWriteOption);
                }
            }
    }
}
exports.userdataMigration = userdataMigration;
async function debugMigration(context) {
    // Backup vscode/tasks.json
    await context.backup(exports.Constants.tasksJsonPath);
    // Read .vscode/tasks.json
    const tasksJsonContent = await debugV3MigrationUtils_1.readJsonCommentFile(path.join(context.projectPath, exports.Constants.tasksJsonPath));
    if (!debugV3MigrationUtils_1.isCommentObject(tasksJsonContent) || !Array.isArray(tasksJsonContent["tasks"])) {
        // Invalid tasks.json content
        return;
    }
    // Migrate .vscode/tasks.json
    const migrateTaskFuncs = [
        taskMigrator_1.migrateTransparentPrerequisite,
        taskMigrator_1.migrateTransparentNpmInstall,
        taskMigrator_1.migrateTransparentLocalTunnel,
        taskMigrator_1.migrateSetUpTab,
        taskMigrator_1.migrateSetUpBot,
        taskMigrator_1.migrateSetUpSSO,
        taskMigrator_1.migratePrepareManifest,
        taskMigrator_1.migrateValidateDependencies,
        taskMigrator_1.migrateBackendExtensionsInstall,
        taskMigrator_1.migrateFrontendStart,
        taskMigrator_1.migrateAuthStart,
        taskMigrator_1.migrateBotStart,
        taskMigrator_1.migrateBackendWatch,
        taskMigrator_1.migrateBackendStart,
        taskMigrator_1.migratePreDebugCheck,
        taskMigrator_1.migrateValidateLocalPrerequisites,
        taskMigrator_1.migrateNgrokStartTask,
        taskMigrator_1.migrateNgrokStartCommand,
    ];
    const oldProjectSettings = await loadProjectSettings(context.projectPath);
    const placeholderMappings = await debugV3MigrationUtils_1.getPlaceholderMappings(context);
    const debugContext = new debugMigrationContext_1.DebugMigrationContext(context, tasksJsonContent["tasks"], oldProjectSettings, placeholderMappings);
    for (const func of migrateTaskFuncs) {
        await func(debugContext);
    }
    // Write .vscode/tasks.json
    await context.fsWriteFile(exports.Constants.tasksJsonPath, commentJson.stringify(tasksJsonContent, null, 4));
    // Generate app.local.yml
    const appYmlGenerator = new appLocalYmlGenerator_1.AppLocalYmlGenerator(oldProjectSettings, debugContext.appYmlConfig, placeholderMappings);
    const appYmlString = await appYmlGenerator.generateAppYml();
    await context.fsWriteFile(versionMetadata_1.MetadataV3.localConfigFile, appYmlString);
}
exports.debugMigration = debugMigration;
function checkapimPluginExists(pjSettings) {
    if (pjSettings && pjSettings["components"]) {
        for (const obj of pjSettings["components"])
            if (Object.keys(obj).includes("name") && obj["name"] === "apim")
                return true;
        return false;
    }
    else {
        return false;
    }
}
exports.checkapimPluginExists = checkapimPluginExists;
async function generateApimPluginEnvContent(context) {
    // general
    if (await context.fsPathExists(path.join(".fx", "configs", "projectSettings.json"))) {
        const projectSettingsContent = fs.readJsonSync(path.join(context.projectPath, ".fx", "configs", "projectSettings.json"));
        // judge if apim plugin exists
        if (checkapimPluginExists(projectSettingsContent)) {
            const fileNames = v3MigrationUtils_1.fsReadDirSync(context, path.join(".fx", "configs"));
            for (const fileName of fileNames)
                if (fileName.startsWith("config.")) {
                    const fileRegex = new RegExp("(config.)([a-zA-Z0-9_-]*)(.json)", "g"); // state.*.json
                    const fileNamesArray = fileRegex.exec(fileName);
                    if (fileNamesArray != null) {
                        // get envName
                        const envName = fileNamesArray[2];
                        if (envName != "local") {
                            await context.fsEnsureDir(versionMetadata_1.MetadataV3.defaultEnvironmentFolder);
                            if (!(await context.fsPathExists(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName))))
                                await context.fsCreateFile(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName));
                            const apimPluginAppendContent = "APIM__PUBLISHEREMAIL= # Teams Toolkit does not record your mail to protect your privacy, please fill your mail address here before provision to avoid failures" +
                                os_1.EOL +
                                "APIM__PUBLISHERNAME= # Teams Toolkit does not record your name to protect your privacy, please fill your name here before provision to avoid failures" +
                                os_1.EOL;
                            await context.fsWriteFile(path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + envName), apimPluginAppendContent, exports.Constants.envWriteOption);
                        }
                    }
                }
        }
    }
}
exports.generateApimPluginEnvContent = generateApimPluginEnvContent;
async function updateGitignore(context) {
    const gitignoreFile = ".gitignore";
    const ignoreFileExist = await context.backup(gitignoreFile);
    if (!ignoreFileExist) {
        context.fsCreateFile(gitignoreFile);
    }
    let ignoreFileContent = await fs.readFile(path.join(context.projectPath, gitignoreFile), "utf8");
    ignoreFileContent +=
        os_1.EOL + path.join(versionMetadata_1.MetadataV3.defaultEnvironmentFolder, exports.Constants.envFilePrefix + "*");
    ignoreFileContent += os_1.EOL + `${migrationContext_1.backupFolder}/*`;
    await context.fsWriteFile(gitignoreFile, ignoreFileContent);
}
exports.updateGitignore = updateGitignore;
//# sourceMappingURL=projectMigratorV3.js.map