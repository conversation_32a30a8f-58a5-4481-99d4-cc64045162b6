import { Middleware } from "@feathersjs/hooks/lib";
import { FxError, Inputs, ProjectSettings, Result, SolutionContext, Tools } from "@microsoft/teamsfx-api";
import { CoreHookContext } from "../types";
export declare const ProjectSettingsLoaderMW: Middleware;
export declare function loadProjectSettings(inputs: Inputs, isMultiEnvEnabled?: boolean): Promise<Result<ProjectSettings, FxError>>;
export declare function loadProjectSettingsByProjectPath(projectPath: string, isMultiEnvEnabled?: boolean): Promise<Result<ProjectSettings, FxError>>;
export declare function loadProjectSettingsByProjectPathV2(projectPath: string, isMultiEnvEnabled?: boolean, onlyV2?: boolean): Promise<Result<ProjectSettings, FxError>>;
export declare function newSolutionContext(tools: Tools, inputs: Inputs): Promise<SolutionContext>;
export declare function shouldIgnored(ctx: CoreHookContext): boolean;
export declare function getProjectSettingsPath(projectPath: string): string;
export declare function getProjectSettingPathV3(projectPath: string): string;
export declare function getProjectSettingPathV2(projectPath: string): string;
//# sourceMappingURL=projectSettingsLoader.d.ts.map