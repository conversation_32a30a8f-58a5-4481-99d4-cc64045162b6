"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProjectSettingPathV2 = exports.getProjectSettingPathV3 = exports.getProjectSettingsPath = exports.shouldIgnored = exports.newSolutionContext = exports.loadProjectSettingsByProjectPathV2 = exports.loadProjectSettingsByProjectPath = exports.loadProjectSettings = exports.ProjectSettingsLoaderMW = void 0;
const tslib_1 = require("tslib");
const fs = tslib_1.__importStar(require("fs-extra"));
const path = tslib_1.__importStar(require("path"));
const uuid = tslib_1.__importStar(require("uuid"));
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const projectSettingsHelper_1 = require("../../common/projectSettingsHelper");
const telemetry_1 = require("../../common/telemetry");
const tools_1 = require("../../common/tools");
const crypto_1 = require("../crypto");
const environment_1 = require("../environment");
const error_1 = require("../error");
const globalVars_1 = require("../globalVars");
const permissionRequest_1 = require("../permissionRequest");
const migrate_1 = require("../../component/migrate");
const versionMetadata_1 = require("../../common/versionMetadata");
const settingsUtil_1 = require("../../component/utils/settingsUtil");
const ProjectSettingsLoaderMW = async (ctx, next) => {
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    if (!shouldIgnored(ctx)) {
        if (!inputs.projectPath) {
            ctx.result = teamsfx_api_1.err(new error_1.NoProjectOpenedError());
            return;
        }
        const projectPathExist = await fs.pathExists(inputs.projectPath);
        if (!projectPathExist) {
            ctx.result = teamsfx_api_1.err(new error_1.PathNotExistError(inputs.projectPath));
            return;
        }
        const loadRes = await loadProjectSettings(inputs, true);
        if (loadRes.isErr()) {
            ctx.result = teamsfx_api_1.err(loadRes.error);
            return;
        }
        const projectSettings = loadRes.value;
        const validRes = projectSettingsHelper_1.validateProjectSettings(projectSettings);
        if (validRes) {
            ctx.result = teamsfx_api_1.err(new error_1.InvalidProjectSettingsFileError(validRes));
            return;
        }
        ctx.projectSettings = projectSettings;
        ctx.self.isFromSample = projectSettings.isFromSample === true;
        ctx.self.settingsVersion = projectSettings.version;
        ctx.self.tools.cryptoProvider = new crypto_1.LocalCrypto(projectSettings.projectId);
        ctx.contextV2 = tools_1.createV2Context(projectSettings);
        // set global variable once project settings is loaded
        globalVars_1.globalVars.isVS = projectSettingsHelper_1.isVSProject(projectSettings);
    }
    await next();
};
exports.ProjectSettingsLoaderMW = ProjectSettingsLoaderMW;
async function loadProjectSettings(inputs, isMultiEnvEnabled = false) {
    if (!inputs.projectPath) {
        return teamsfx_api_1.err(new error_1.NoProjectOpenedError());
    }
    return await loadProjectSettingsByProjectPath(inputs.projectPath, isMultiEnvEnabled);
}
exports.loadProjectSettings = loadProjectSettings;
async function loadProjectSettingsByProjectPath(projectPath, isMultiEnvEnabled = false) {
    try {
        if (tools_1.isV3Enabled()) {
            const readSettingsResult = await settingsUtil_1.settingsUtil.readSettings(projectPath, true);
            if (readSettingsResult.isOk()) {
                const projectSettings = {
                    projectId: readSettingsResult.value.trackingId,
                    version: readSettingsResult.value.version,
                };
                return teamsfx_api_1.ok(projectSettings);
            }
            else {
                return teamsfx_api_1.err(readSettingsResult.error);
            }
        }
        else {
            return await loadProjectSettingsByProjectPathV2(projectPath, isMultiEnvEnabled);
        }
    }
    catch (e) {
        return teamsfx_api_1.err(error_1.ReadFileError(e));
    }
}
exports.loadProjectSettingsByProjectPath = loadProjectSettingsByProjectPath;
// export this for V2 -> V3 migration purpose
async function loadProjectSettingsByProjectPathV2(projectPath, isMultiEnvEnabled = false, onlyV2 = false) {
    let settingsFile;
    if (onlyV2) {
        settingsFile = getProjectSettingPathV2(projectPath);
    }
    else {
        settingsFile = isMultiEnvEnabled
            ? getProjectSettingsPath(projectPath)
            : path.resolve(projectPath, `.${teamsfx_api_1.ConfigFolderName}`, "settings.json");
    }
    const projectSettings = await fs.readJson(settingsFile);
    if (!projectSettings.projectId) {
        projectSettings.projectId = uuid.v4();
        telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.FillProjectId, {
            [telemetry_1.TelemetryProperty.ProjectId]: projectSettings.projectId,
        });
    }
    globalVars_1.globalVars.isVS = projectSettingsHelper_1.isVSProject(projectSettings);
    return teamsfx_api_1.ok(migrate_1.convertProjectSettingsV2ToV3(projectSettings, projectPath));
}
exports.loadProjectSettingsByProjectPathV2 = loadProjectSettingsByProjectPathV2;
async function newSolutionContext(tools, inputs) {
    const projectSettings = {
        appName: "",
        programmingLanguage: "",
        projectId: uuid.v4(),
        solutionSettings: {
            name: "fx-solution-azure",
            version: "1.0.0",
        },
    };
    const solutionContext = Object.assign(Object.assign(Object.assign({ projectSettings: projectSettings, envInfo: environment_1.newEnvInfo(), root: inputs.projectPath || "" }, tools), tools.tokenProvider), { answers: inputs, cryptoProvider: new crypto_1.LocalCrypto(projectSettings.projectId), permissionRequestProvider: inputs.projectPath
            ? new permissionRequest_1.PermissionRequestFileProvider(inputs.projectPath)
            : undefined });
    return solutionContext;
}
exports.newSolutionContext = newSolutionContext;
function shouldIgnored(ctx) {
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    const method = ctx.method;
    let isCreate = false;
    if (method === "getQuestions") {
        const task = ctx.arguments[0];
        isCreate = task === teamsfx_api_1.Stage.create;
    }
    return teamsfx_api_1.StaticPlatforms.includes(inputs.platform) || isCreate || inputs.ignoreLockByUT;
}
exports.shouldIgnored = shouldIgnored;
function getProjectSettingsPath(projectPath) {
    if (tools_1.isV3Enabled()) {
        return getProjectSettingPathV3(projectPath);
    }
    else {
        return getProjectSettingPathV2(projectPath);
    }
}
exports.getProjectSettingsPath = getProjectSettingsPath;
function getProjectSettingPathV3(projectPath) {
    return path.resolve(projectPath, versionMetadata_1.MetadataV3.configFile);
}
exports.getProjectSettingPathV3 = getProjectSettingPathV3;
function getProjectSettingPathV2(projectPath) {
    return path.resolve(projectPath, `.${teamsfx_api_1.ConfigFolderName}`, teamsfx_api_1.InputConfigsFolderName, teamsfx_api_1.ProjectSettingsFileName);
}
exports.getProjectSettingPathV2 = getProjectSettingPathV2;
//# sourceMappingURL=projectSettingsLoader.js.map