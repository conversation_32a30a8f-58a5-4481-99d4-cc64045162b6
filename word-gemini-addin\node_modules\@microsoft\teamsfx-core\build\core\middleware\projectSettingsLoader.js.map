{"version": 3, "file": "projectSettingsLoader.js", "sourceRoot": "", "sources": ["../../../src/core/middleware/projectSettingsLoader.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,qDAA+B;AAC/B,mDAA6B;AAC7B,mDAA6B;AAG7B,wDAiBgC;AAEhC,8EAA0F;AAC1F,sDAKgC;AAChC,8CAAkE;AAClE,sCAAwC;AACxC,gDAA4C;AAC5C,oCAKkB;AAClB,8CAA2C;AAC3C,4DAAqE;AAErE,qDAAuE;AACvE,kEAA0D;AAC1D,qEAAkE;AAE3D,MAAM,uBAAuB,GAAe,KAAK,EACtD,GAAoB,EACpB,IAAkB,EAClB,EAAE;IACF,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC;IACjE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;QACvB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACvB,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,IAAI,4BAAoB,EAAE,CAAC,CAAC;YAC7C,OAAO;SACR;QACD,MAAM,gBAAgB,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACjE,IAAI,CAAC,gBAAgB,EAAE;YACrB,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,IAAI,yBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;YAC5D,OAAO;SACR;QACD,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxD,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE;YACnB,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAChC,OAAO;SACR;QAED,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC;QAEtC,MAAM,QAAQ,GAAG,+CAAuB,CAAC,eAAe,CAAC,CAAC;QAC1D,IAAI,QAAQ,EAAE;YACZ,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,IAAI,uCAA+B,CAAC,QAAQ,CAAC,CAAC,CAAC;YAChE,OAAO;SACR;QACD,GAAG,CAAC,eAAe,GAAG,eAAe,CAAC;QACrC,GAAG,CAAC,IAAY,CAAC,YAAY,GAAG,eAAe,CAAC,YAAY,KAAK,IAAI,CAAC;QACtE,GAAG,CAAC,IAAY,CAAC,eAAe,GAAG,eAAe,CAAC,OAAO,CAAC;QAC3D,GAAG,CAAC,IAAY,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,oBAAW,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QACpF,GAAG,CAAC,SAAS,GAAG,uBAAe,CAAC,eAAe,CAAC,CAAC;QACjD,sDAAsD;QACtD,uBAAU,CAAC,IAAI,GAAG,mCAAW,CAAC,eAAe,CAAC,CAAC;KAChD;IAED,MAAM,IAAI,EAAE,CAAC;AACf,CAAC,CAAC;AAtCW,QAAA,uBAAuB,2BAsClC;AAEK,KAAK,UAAU,mBAAmB,CACvC,MAAc,EACd,iBAAiB,GAAG,KAAK;IAEzB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;QACvB,OAAO,iBAAG,CAAC,IAAI,4BAAoB,EAAE,CAAC,CAAC;KACxC;IACD,OAAO,MAAM,gCAAgC,CAAC,MAAM,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AACvF,CAAC;AARD,kDAQC;AAEM,KAAK,UAAU,gCAAgC,CACpD,WAAmB,EACnB,iBAAiB,GAAG,KAAK;IAEzB,IAAI;QACF,IAAI,mBAAW,EAAE,EAAE;YACjB,MAAM,kBAAkB,GAAG,MAAM,2BAAY,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC9E,IAAI,kBAAkB,CAAC,IAAI,EAAE,EAAE;gBAC7B,MAAM,eAAe,GAAoB;oBACvC,SAAS,EAAE,kBAAkB,CAAC,KAAK,CAAC,UAAU;oBAC9C,OAAO,EAAE,kBAAkB,CAAC,KAAK,CAAC,OAAO;iBAC1C,CAAC;gBACF,OAAO,gBAAE,CAAC,eAAe,CAAC,CAAC;aAC5B;iBAAM;gBACL,OAAO,iBAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;aACtC;SACF;aAAM;YACL,OAAO,MAAM,kCAAkC,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;SACjF;KACF;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,iBAAG,CAAC,qBAAa,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9B;AACH,CAAC;AAtBD,4EAsBC;AAED,6CAA6C;AACtC,KAAK,UAAU,kCAAkC,CACtD,WAAmB,EACnB,iBAAiB,GAAG,KAAK,EACzB,MAAM,GAAG,KAAK;IAEd,IAAI,YAAY,CAAC;IACjB,IAAI,MAAM,EAAE;QACV,YAAY,GAAG,uBAAuB,CAAC,WAAW,CAAC,CAAC;KACrD;SAAM;QACL,YAAY,GAAG,iBAAiB;YAC9B,CAAC,CAAC,sBAAsB,CAAC,WAAW,CAAC;YACrC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,8BAAgB,EAAE,EAAE,eAAe,CAAC,CAAC;KACxE;IAED,MAAM,eAAe,GAAoB,MAAM,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACzE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;QAC9B,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;QACtC,8BAAkB,CAAC,qBAAS,CAAC,IAAI,EAAE,0BAAc,CAAC,aAAa,EAAE;YAC/D,CAAC,6BAAiB,CAAC,SAAS,CAAC,EAAE,eAAe,CAAC,SAAS;SACzD,CAAC,CAAC;KACJ;IACD,uBAAU,CAAC,IAAI,GAAG,mCAAW,CAAC,eAAe,CAAC,CAAC;IAC/C,OAAO,gBAAE,CAAC,sCAA4B,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC,CAAC;AACxE,CAAC;AAvBD,gFAuBC;AAEM,KAAK,UAAU,kBAAkB,CAAC,KAAY,EAAE,MAAc;IACnE,MAAM,eAAe,GAAoB;QACvC,OAAO,EAAE,EAAE;QACX,mBAAmB,EAAE,EAAE;QACvB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;QACpB,gBAAgB,EAAE;YAChB,IAAI,EAAE,mBAAmB;YACzB,OAAO,EAAE,OAAO;SACjB;KACF,CAAC;IACF,MAAM,eAAe,+CACnB,eAAe,EAAE,eAAe,EAChC,OAAO,EAAE,wBAAU,EAAE,EACrB,IAAI,EAAE,MAAM,CAAC,WAAW,IAAI,EAAE,IAC3B,KAAK,GACL,KAAK,CAAC,aAAa,KACtB,OAAO,EAAE,MAAM,EACf,cAAc,EAAE,IAAI,oBAAW,CAAC,eAAe,CAAC,SAAS,CAAC,EAC1D,yBAAyB,EAAE,MAAM,CAAC,WAAW;YAC3C,CAAC,CAAC,IAAI,iDAA6B,CAAC,MAAM,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,SAAS,GACd,CAAC;IACF,OAAO,eAAe,CAAC;AACzB,CAAC;AAvBD,gDAuBC;AAED,SAAgB,aAAa,CAAC,GAAoB;IAChD,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC;IACjE,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAE1B,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,MAAM,KAAK,cAAc,EAAE;QAC7B,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAU,CAAC;QACvC,QAAQ,GAAG,IAAI,KAAK,mBAAK,CAAC,MAAM,CAAC;KAClC;IAED,OAAO,6BAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,QAAQ,IAAI,MAAM,CAAC,cAAc,CAAC;AACxF,CAAC;AAXD,sCAWC;AAED,SAAgB,sBAAsB,CAAC,WAAmB;IACxD,IAAI,mBAAW,EAAE,EAAE;QACjB,OAAO,uBAAuB,CAAC,WAAW,CAAC,CAAC;KAC7C;SAAM;QACL,OAAO,uBAAuB,CAAC,WAAW,CAAC,CAAC;KAC7C;AACH,CAAC;AAND,wDAMC;AAED,SAAgB,uBAAuB,CAAC,WAAmB;IACzD,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,4BAAU,CAAC,UAAU,CAAC,CAAC;AAC1D,CAAC;AAFD,0DAEC;AAED,SAAgB,uBAAuB,CAAC,WAAmB;IACzD,OAAO,IAAI,CAAC,OAAO,CACjB,WAAW,EACX,IAAI,8BAAgB,EAAE,EACtB,oCAAsB,EACtB,qCAAuB,CACxB,CAAC;AACJ,CAAC;AAPD,0DAOC"}