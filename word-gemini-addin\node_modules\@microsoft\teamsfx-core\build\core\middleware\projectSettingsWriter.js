// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectSettingsWriterMW = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const fs = tslib_1.__importStar(require("fs-extra"));
const tools_1 = require("../../common/tools");
const migrate_1 = require("../../component/migrate");
const error_1 = require("../error");
const globalVars_1 = require("../globalVars");
const projectSettingsLoader_1 = require("./projectSettingsLoader");
/**
 * This middleware will help to persist project settings if necessary.
 */
const ProjectSettingsWriterMW = async (ctx, next) => {
    await next();
    if (!projectSettingsLoader_1.shouldIgnored(ctx)) {
        const lastArg = ctx.arguments[ctx.arguments.length - 1];
        const inputs = lastArg === ctx ? ctx.arguments[ctx.arguments.length - 2] : lastArg;
        if (!inputs.projectPath ||
            inputs.ignoreConfigPersist === true ||
            teamsfx_api_1.StaticPlatforms.includes(inputs.platform))
            return;
        let projectSettings = ctx.projectSettings;
        if (projectSettings === undefined)
            return;
        try {
            if (!tools_1.isV3Enabled()) {
                projectSettings = migrate_1.convertProjectSettingsV3ToV2(projectSettings);
                const solutionSettings = projectSettings.solutionSettings;
                if (solutionSettings) {
                    if (!solutionSettings.activeResourcePlugins)
                        solutionSettings.activeResourcePlugins = [];
                    if (!solutionSettings.azureResources)
                        solutionSettings.azureResources = [];
                }
                const settingFile = projectSettingsLoader_1.getProjectSettingsPath(inputs.projectPath);
                await fs.writeFile(settingFile, JSON.stringify(projectSettings, null, 4));
                globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.debug(`[core] persist project setting file: ${settingFile}`);
            }
        }
        catch (e) {
            if (ctx.result.isOk()) {
                ctx.result = teamsfx_api_1.err(error_1.WriteFileError(e));
            }
        }
    }
};
exports.ProjectSettingsWriterMW = ProjectSettingsWriterMW;
//# sourceMappingURL=projectSettingsWriter.js.map