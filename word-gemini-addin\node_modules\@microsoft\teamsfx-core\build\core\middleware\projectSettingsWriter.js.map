{"version": 3, "file": "projectSettingsWriter.js", "sourceRoot": "", "sources": ["../../../src/core/middleware/projectSettingsWriter.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC,YAAY,CAAC;;;;AAGb,wDAQgC;AAChC,qDAA+B;AAC/B,8CAAiD;AACjD,qDAAuE;AACvE,oCAA0C;AAC1C,8CAAsC;AAEtC,mEAAgF;AAEhF;;GAEG;AACI,MAAM,uBAAuB,GAAe,KAAK,EACtD,GAAoB,EACpB,IAAkB,EAClB,EAAE;IACF,MAAM,IAAI,EAAE,CAAC;IACb,IAAI,CAAC,qCAAa,CAAC,GAAG,CAAC,EAAE;QACvB,MAAM,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,MAAM,GAAW,OAAO,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAC3F,IACE,CAAC,MAAM,CAAC,WAAW;YACnB,MAAM,CAAC,mBAAmB,KAAK,IAAI;YACnC,6BAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC;YAEzC,OAAO;QACT,IAAI,eAAe,GAAG,GAAG,CAAC,eAAe,CAAC;QAC1C,IAAI,eAAe,KAAK,SAAS;YAAE,OAAO;QAC1C,IAAI;YACF,IAAI,CAAC,mBAAW,EAAE,EAAE;gBAClB,eAAe,GAAG,sCAA4B,CAAC,eAAoC,CAAC,CAAC;gBACrF,MAAM,gBAAgB,GAAG,eAAe,CAAC,gBAAgB,CAAC;gBAC1D,IAAI,gBAAgB,EAAE;oBACpB,IAAI,CAAC,gBAAgB,CAAC,qBAAqB;wBAAE,gBAAgB,CAAC,qBAAqB,GAAG,EAAE,CAAC;oBACzF,IAAI,CAAC,gBAAgB,CAAC,cAAc;wBAAE,gBAAgB,CAAC,cAAc,GAAG,EAAE,CAAC;iBAC5E;gBAED,MAAM,WAAW,GAAG,8CAAsB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAC/D,MAAM,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC1E,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,KAAK,CAAC,wCAAwC,WAAW,EAAE,CAAC,CAAC;aACjF;SACF;QAAC,OAAO,CAAC,EAAE;YACV,IAAK,GAAG,CAAC,MAA+B,CAAC,IAAI,EAAE,EAAE;gBAC/C,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,sBAAc,CAAC,CAAC,CAAC,CAAC,CAAC;aACrC;SACF;KACF;AACH,CAAC,CAAC;AAnCW,QAAA,uBAAuB,2BAmClC"}