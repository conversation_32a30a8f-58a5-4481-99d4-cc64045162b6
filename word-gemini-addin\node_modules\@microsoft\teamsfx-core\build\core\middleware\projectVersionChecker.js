"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectVersionCheckerMW = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const globalVars_1 = require("../globalVars");
const localizeUtils_1 = require("../../common/localizeUtils");
const semver_1 = tslib_1.__importDefault(require("semver"));
const tools_1 = require("../../common/tools");
const v3MigrationUtils_1 = require("./utils/v3MigrationUtils");
const versionMetadata_1 = require("../../common/versionMetadata");
const projectMigrator_1 = require("./projectMigrator");
const projectMigratorV3_1 = require("./projectMigratorV3");
const telemetry_1 = require("../../common/telemetry");
const error_1 = require("../error");
let userCancelFlag = false;
const methods = new Set(["getProjectConfig", "checkPermission"]);
const ProjectVersionCheckerMW = async (ctx, next) => {
    const versionInfo = await v3MigrationUtils_1.getProjectVersion(ctx);
    if ((await needToShowUpdateDialog(ctx, versionInfo)) && checkMethod(ctx)) {
        const errRes = await showDialog(ctx);
        ctx.result = teamsfx_api_1.err(errRes);
        return;
    }
    await next();
};
exports.ProjectVersionCheckerMW = ProjectVersionCheckerMW;
async function needToShowUpdateDialog(ctx, versionInfo) {
    if (tools_1.isV3Enabled()) {
        if (versionInfo.source === versionMetadata_1.VersionSource.teamsapp && semver_1.default.gte(versionInfo.version, "2.0.0")) {
            return true;
        }
    }
    else {
        if (versionInfo.source !== versionMetadata_1.VersionSource.projectSettings) {
            telemetry_1.sendTelemetryEvent(telemetry_1.Component.core, telemetry_1.TelemetryEvent.DisplayToolingUpdateNotification, {
                [telemetry_1.TelemetryProperty.ToolkitVersion]: "V2",
            });
            return true;
        }
    }
    return false;
}
// TODO: add url for download proper toolkit version
async function showDialog(ctx) {
    const lastArg = ctx.arguments[ctx.arguments.length - 1];
    const inputs = lastArg === ctx ? ctx.arguments[ctx.arguments.length - 2] : lastArg;
    if (inputs.platform === teamsfx_api_1.Platform.VSCode) {
        const messageKey = "core.projectVersionChecker.incompatibleProject";
        const message = localizeUtils_1.getLocalizedString(messageKey);
        globalVars_1.TOOLS.ui.showMessage("warn", message, false, projectMigrator_1.learnMoreText).then((res) => {
            if (res.isOk() && res.value === projectMigrator_1.learnMoreText) {
                globalVars_1.TOOLS.ui.openUrl(versionMetadata_1.MetadataV2.updateToolkitLink);
            }
        });
        return error_1.IncompatibleProjectError(messageKey);
    }
    else if (inputs.platform === teamsfx_api_1.Platform.CLI) {
        const messageKey = "core.projectVersionChecker.cliUseNewVersion";
        globalVars_1.TOOLS.logProvider.warning(localizeUtils_1.getLocalizedString(messageKey));
        return error_1.IncompatibleProjectError(messageKey);
    }
    else {
        const messageKey = "core.projectVersionChecker.incompatibleProject";
        const message = localizeUtils_1.getLocalizedString(messageKey);
        globalVars_1.TOOLS.ui.showMessage("warn", message, false, projectMigrator_1.learnMoreText).then((res) => {
            if (res.isOk() && res.value === projectMigrator_1.learnMoreText) {
                globalVars_1.TOOLS.ui.openUrl(projectMigratorV3_1.learnMoreLink);
            }
        });
        return error_1.IncompatibleProjectError(messageKey);
    }
}
function checkMethod(ctx) {
    if (ctx.method && methods.has(ctx.method) && userCancelFlag)
        return false;
    userCancelFlag = ctx.method != undefined && methods.has(ctx.method);
    return true;
}
//# sourceMappingURL=projectVersionChecker.js.map