{"version": 3, "file": "projectVersionChecker.js", "sourceRoot": "", "sources": ["../../../src/core/middleware/projectVersionChecker.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,wDAAwE;AAGxE,8CAAsC;AACtC,8DAAgE;AAChE,4DAA4B;AAC5B,8CAAiD;AACjD,+DAA6D;AAC7D,kEAAsF;AACtF,uDAAkD;AAClD,2DAAoD;AACpD,sDAKgC;AAChC,oCAAoD;AAEpD,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3B,MAAM,OAAO,GAAgB,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAEvE,MAAM,uBAAuB,GAAe,KAAK,EACtD,GAAoB,EACpB,IAAkB,EAClB,EAAE;IACF,MAAM,WAAW,GAAG,MAAM,oCAAiB,CAAC,GAAG,CAAC,CAAC;IACjD,IAAI,CAAC,MAAM,sBAAsB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;QACxE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC;QACrC,GAAG,CAAC,MAAM,GAAG,iBAAG,CAAC,MAAM,CAAC,CAAC;QACzB,OAAO;KACR;IAED,MAAM,IAAI,EAAE,CAAC;AACf,CAAC,CAAC;AAZW,QAAA,uBAAuB,2BAYlC;AAEF,KAAK,UAAU,sBAAsB,CAAC,GAAoB,EAAE,WAAwB;IAClF,IAAI,mBAAW,EAAE,EAAE;QACjB,IAAI,WAAW,CAAC,MAAM,KAAK,+BAAa,CAAC,QAAQ,IAAI,gBAAM,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YAC7F,OAAO,IAAI,CAAC;SACb;KACF;SAAM;QACL,IAAI,WAAW,CAAC,MAAM,KAAK,+BAAa,CAAC,eAAe,EAAE;YACxD,8BAAkB,CAAC,qBAAS,CAAC,IAAI,EAAE,0BAAc,CAAC,gCAAgC,EAAE;gBAClF,CAAC,6BAAiB,CAAC,cAAc,CAAC,EAAE,IAAI;aACzC,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,oDAAoD;AACpD,KAAK,UAAU,UAAU,CAAC,GAAoB;IAC5C,MAAM,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACxD,MAAM,MAAM,GAAW,OAAO,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IAC3F,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,MAAM,EAAE;QACvC,MAAM,UAAU,GAAG,gDAAgD,CAAC;QACpE,MAAM,OAAO,GAAG,kCAAkB,CAAC,UAAU,CAAC,CAAC;QAC/C,kBAAK,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,+BAAa,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACvE,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,KAAK,KAAK,+BAAa,EAAE;gBAC7C,kBAAK,CAAC,EAAE,CAAC,OAAO,CAAC,4BAAU,CAAC,iBAAiB,CAAC,CAAC;aAChD;QACH,CAAC,CAAC,CAAC;QACH,OAAO,gCAAwB,CAAC,UAAU,CAAC,CAAC;KAC7C;SAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,GAAG,EAAE;QAC3C,MAAM,UAAU,GAAG,6CAA6C,CAAC;QACjE,kBAAK,CAAC,WAAW,CAAC,OAAO,CAAC,kCAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;QAC1D,OAAO,gCAAwB,CAAC,UAAU,CAAC,CAAC;KAC7C;SAAM;QACL,MAAM,UAAU,GAAG,gDAAgD,CAAC;QACpE,MAAM,OAAO,GAAG,kCAAkB,CAAC,UAAU,CAAC,CAAC;QAC/C,kBAAK,CAAC,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,+BAAa,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACvE,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,KAAK,KAAK,+BAAa,EAAE;gBAC7C,kBAAK,CAAC,EAAE,CAAC,OAAO,CAAC,iCAAa,CAAC,CAAC;aACjC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,gCAAwB,CAAC,UAAU,CAAC,CAAC;KAC7C;AACH,CAAC;AAED,SAAS,WAAW,CAAC,GAAoB;IACvC,IAAI,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,cAAc;QAAE,OAAO,KAAK,CAAC;IAC1E,cAAc,GAAG,GAAG,CAAC,MAAM,IAAI,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACpE,OAAO,IAAI,CAAC;AACd,CAAC"}