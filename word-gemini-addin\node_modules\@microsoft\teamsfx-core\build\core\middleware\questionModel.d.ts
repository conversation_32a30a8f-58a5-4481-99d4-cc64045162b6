import { Middleware } from "@feathersjs/hooks";
import { FxError, Inputs, QTreeNode, Result } from "@microsoft/teamsfx-api";
/**
 * This middleware will help to collect input from question flow
 */
export declare const QuestionModelMW: Middleware;
export declare function desensitize(node: QTreeNode, input: Inputs): Inputs;
export declare function traverseToCollectPasswordNodes(node: QTreeNode, names: Set<string>): void;
export declare function getQuestionsForCreateProjectV2(inputs: Inputs): Promise<Result<QTreeNode | undefined, FxError>>;
export declare function addOfficeAddinQuestions(node: QTreeNode): void;
//# sourceMappingURL=questionModel.d.ts.map