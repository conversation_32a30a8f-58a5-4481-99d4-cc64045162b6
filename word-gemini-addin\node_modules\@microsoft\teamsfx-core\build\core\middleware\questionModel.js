"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.addOfficeAddinQuestions = exports.getQuestionsForCreateProjectV2 = exports.traverseToCollectPasswordNodes = exports.desensitize = exports.QuestionModelMW = void 0;
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const featureFlags_1 = require("../../common/featureFlags");
const tools_1 = require("../../common/tools");
const spfx_1 = require("../../component/feature/spfx");
const question_1 = require("../../component/question");
const constants_1 = require("../../component/constants");
const collaborator_1 = require("../collaborator");
const globalVars_1 = require("../globalVars");
const question_2 = require("../question");
const utils_1 = require("../../component/resource/appManifest/utils/utils");
const utils_2 = require("../../common/utils");
const question_3 = require("../../component/generator/officeAddin/question");
const developerPortalScaffoldUtils_1 = require("../../component/developerPortalScaffoldUtils");
/**
 * This middleware will help to collect input from question flow
 */
const QuestionModelMW = async (ctx, next) => {
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    const method = ctx.method;
    let getQuestionRes = teamsfx_api_1.ok(undefined);
    if (method === "grantPermission") {
        getQuestionRes = await collaborator_1.getQuestionsForGrantPermission(inputs);
    }
    if (getQuestionRes.isErr()) {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.error(`[core] failed to get questions for ${method}: ${getQuestionRes.error.message}`);
        ctx.result = teamsfx_api_1.err(getQuestionRes.error);
        return;
    }
    globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.debug(`[core] success to get questions for ${method}`);
    const node = getQuestionRes.value;
    if (node) {
        const res = await teamsfx_api_1.traverse(node, inputs, globalVars_1.TOOLS.ui, globalVars_1.TOOLS.telemetryReporter);
        if (res.isErr()) {
            globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.debug(`[core] failed to run question model for ${method}`);
            ctx.result = teamsfx_api_1.err(res.error);
            return;
        }
        const desensitized = desensitize(node, inputs);
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.info(`[core] success to run question model for ${method}, answers:${JSON.stringify(desensitized)}`);
    }
    await next();
};
exports.QuestionModelMW = QuestionModelMW;
function desensitize(node, input) {
    const copy = tools_1.deepCopy(input);
    const names = new Set();
    traverseToCollectPasswordNodes(node, names);
    for (const name of names) {
        copy[name] = "******";
    }
    return copy;
}
exports.desensitize = desensitize;
function traverseToCollectPasswordNodes(node, names) {
    if (node.data.type === "text" && node.data.password === true) {
        names.add(node.data.name);
    }
    for (const child of node.children || []) {
        traverseToCollectPasswordNodes(child, names);
    }
}
exports.traverseToCollectPasswordNodes = traverseToCollectPasswordNodes;
async function getQuestionsForCreateProjectWithoutDotNet(inputs) {
    var _a, _b;
    if (developerPortalScaffoldUtils_1.isFromDevPortalInVSC(inputs)) {
        // If toolkit is activated by a request from Developer Portal, we will always create a project from scratch.
        inputs[question_2.CoreQuestionNames.CreateFromScratch] = question_2.ScratchOptionYesVSC().id;
        inputs[question_2.CoreQuestionNames.Capabilities] = developerPortalScaffoldUtils_1.getTemplateId(inputs.teamsAppFromTdp);
    }
    const node = new teamsfx_api_1.QTreeNode(question_2.getCreateNewOrFromSampleQuestion(inputs.platform));
    // create new
    const createNew = new teamsfx_api_1.QTreeNode({ type: "group" });
    node.addChild(createNew);
    createNew.condition = { equals: question_2.ScratchOptionYes().id };
    // capabilities
    let capNode;
    if (featureFlags_1.isPreviewFeaturesEnabled()) {
        const capQuestion = question_2.createCapabilityQuestionPreview(inputs);
        capNode = new teamsfx_api_1.QTreeNode(capQuestion);
    }
    else {
        const capQuestion = question_2.createCapabilityQuestion();
        capNode = new teamsfx_api_1.QTreeNode(capQuestion);
    }
    createNew.addChild(capNode);
    const triggerNodeRes = await question_1.getNotificationTriggerQuestionNode(inputs);
    if (triggerNodeRes.isErr())
        return teamsfx_api_1.err(triggerNodeRes.error);
    if (triggerNodeRes.value) {
        capNode.addChild(triggerNodeRes.value);
    }
    const spfxNode = await spfx_1.getSPFxScaffoldQuestion();
    if (spfxNode) {
        spfxNode.condition = { equals: constants_1.TabSPFxItem().id };
        capNode.addChild(spfxNode);
    }
    // Language
    const programmingLanguage = new teamsfx_api_1.QTreeNode(question_2.ProgrammingLanguageQuestion);
    if (featureFlags_1.isPreviewFeaturesEnabled()) {
        programmingLanguage.condition = {
            notEquals: constants_1.ExistingTabOptionItem().id,
        };
    }
    else {
        programmingLanguage.condition = {
            minItems: 1,
            excludes: constants_1.ExistingTabOptionItem().id,
        };
    }
    capNode.addChild(programmingLanguage);
    // existing tab endpoint
    if (tools_1.isExistingTabAppEnabled()) {
        const existingTabEndpoint = new teamsfx_api_1.QTreeNode(question_2.ExistingTabEndpointQuestion());
        existingTabEndpoint.condition = {
            equals: constants_1.ExistingTabOptionItem().id,
        };
        capNode.addChild(existingTabEndpoint);
    }
    createNew.addChild(new teamsfx_api_1.QTreeNode(question_2.QuestionRootFolder()));
    const defaultName = !((_a = inputs.teamsAppFromTdp) === null || _a === void 0 ? void 0 : _a.appName)
        ? undefined
        : utils_2.convertToAlphanumericOnly((_b = inputs.teamsAppFromTdp) === null || _b === void 0 ? void 0 : _b.appName);
    createNew.addChild(new teamsfx_api_1.QTreeNode(question_2.createAppNameQuestion(defaultName)));
    if (developerPortalScaffoldUtils_1.isFromDevPortalInVSC(inputs)) {
        const updateTabUrls = await getQuestionsForUpdateStaticTabUrls(inputs.teamsAppFromTdp);
        if (updateTabUrls) {
            createNew.addChild(updateTabUrls);
        }
        const updateBotIds = await getQuestionsForUpdateBotIds(inputs.teamsAppFromTdp);
        if (updateBotIds) {
            createNew.addChild(updateBotIds);
        }
    }
    // create from sample
    const sampleNode = new teamsfx_api_1.QTreeNode(question_2.SampleSelect());
    node.addChild(sampleNode);
    sampleNode.condition = { equals: question_2.ScratchOptionNo().id };
    sampleNode.addChild(new teamsfx_api_1.QTreeNode(question_2.QuestionRootFolder()));
    if (featureFlags_1.isOfficeAddinEnabled()) {
        addOfficeAddinQuestions(node);
    }
    return teamsfx_api_1.ok(node.trim());
}
async function getQuestionsForCreateProjectWithDotNet(inputs) {
    const runtimeNode = new teamsfx_api_1.QTreeNode(question_2.getRuntimeQuestion());
    const maybeNode = await getQuestionsForCreateProjectWithoutDotNet(inputs);
    if (maybeNode.isErr()) {
        return teamsfx_api_1.err(maybeNode.error);
    }
    const node = maybeNode.value;
    if (node) {
        node.condition = {
            equals: question_2.RuntimeOptionNodeJs().id,
        };
        runtimeNode.addChild(node);
    }
    const dotnetNode = new teamsfx_api_1.QTreeNode({ type: "group" });
    dotnetNode.condition = {
        equals: question_2.RuntimeOptionDotNet().id,
    };
    runtimeNode.addChild(dotnetNode);
    const dotnetCapNode = new teamsfx_api_1.QTreeNode(question_2.createCapabilityForDotNet());
    dotnetNode.addChild(dotnetCapNode);
    const triggerNodeRes = await question_1.getNotificationTriggerQuestionNode(inputs);
    if (triggerNodeRes.isErr())
        return teamsfx_api_1.err(triggerNodeRes.error);
    if (triggerNodeRes.value) {
        dotnetCapNode.addChild(triggerNodeRes.value);
    }
    const spfxNode = await spfx_1.getSPFxScaffoldQuestion();
    if (spfxNode) {
        spfxNode.condition = { equals: constants_1.TabSPFxItem().id };
        dotnetCapNode.addChild(spfxNode);
    }
    dotnetCapNode.addChild(new teamsfx_api_1.QTreeNode(question_2.ProgrammingLanguageQuestionForDotNet));
    // only CLI need folder input
    if (teamsfx_api_1.CLIPlatforms.includes(inputs.platform)) {
        runtimeNode.addChild(new teamsfx_api_1.QTreeNode(question_2.QuestionRootFolder()));
    }
    runtimeNode.addChild(new teamsfx_api_1.QTreeNode(question_2.createAppNameQuestion()));
    return teamsfx_api_1.ok(runtimeNode.trim());
}
async function getQuestionsForUpdateStaticTabUrls(appDefinition) {
    if (!utils_1.isPersonalApp(appDefinition)) {
        return undefined;
    }
    const updateTabUrls = new teamsfx_api_1.QTreeNode({ type: "group" });
    const tabs = appDefinition.staticTabs;
    const tabsWithContentUrls = tabs.filter((o) => !!o.contentUrl);
    const tabsWithWebsiteUrls = tabs.filter((o) => !!o.websiteUrl);
    if (tabsWithWebsiteUrls.length > 0) {
        updateTabUrls.addChild(new teamsfx_api_1.QTreeNode(question_2.tabsWebsitetUrlQuestion(tabsWithWebsiteUrls)));
    }
    if (tabsWithContentUrls.length > 0) {
        updateTabUrls.addChild(new teamsfx_api_1.QTreeNode(question_2.tabsContentUrlQuestion(tabsWithContentUrls)));
    }
    return updateTabUrls;
}
async function getQuestionsForUpdateBotIds(appDefinition) {
    if (!utils_1.needBotCode(appDefinition)) {
        return undefined;
    }
    const bots = appDefinition.bots;
    const messageExtensions = appDefinition.messagingExtensions;
    // can add only one bot. If existing, the length is 1.
    const botId = !!bots && bots.length > 0 ? bots[0].botId : undefined;
    // can add only one message extension. If existing, the length is 1.
    const messageExtensionId = !!messageExtensions && messageExtensions.length > 0 ? messageExtensions[0].botId : undefined;
    return new teamsfx_api_1.QTreeNode(question_2.BotIdsQuestion(botId, messageExtensionId));
}
async function getQuestionsForCreateProjectV2(inputs) {
    if (featureFlags_1.isCLIDotNetEnabled() && teamsfx_api_1.CLIPlatforms.includes(inputs.platform)) {
        return getQuestionsForCreateProjectWithDotNet(inputs);
    }
    else {
        return getQuestionsForCreateProjectWithoutDotNet(inputs);
    }
}
exports.getQuestionsForCreateProjectV2 = getQuestionsForCreateProjectV2;
function addOfficeAddinQuestions(node) {
    const createNewAddin = new teamsfx_api_1.QTreeNode({ type: "group" });
    createNewAddin.condition = { equals: question_2.CreateNewOfficeAddinOption().id };
    node.addChild(createNewAddin);
    const capNode = new teamsfx_api_1.QTreeNode(question_2.createCapabilityForOfficeAddin());
    createNewAddin.addChild(capNode);
    capNode.addChild(question_3.getQuestionsForScaffolding());
    createNewAddin.addChild(new teamsfx_api_1.QTreeNode(question_2.QuestionRootFolder()));
    createNewAddin.addChild(new teamsfx_api_1.QTreeNode(question_2.createAppNameQuestion()));
}
exports.addOfficeAddinQuestions = addOfficeAddinQuestions;
//# sourceMappingURL=questionModel.js.map