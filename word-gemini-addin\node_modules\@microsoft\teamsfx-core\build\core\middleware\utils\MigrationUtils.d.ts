import { FxError, Result } from "@microsoft/teamsfx-api";
import { CoreHookContext } from "../../types";
import { RequiredResourceAccess } from "../../../component/resource/aadApp/interfaces/AADManifest";
export interface Permission {
    resource: string;
    delegated: string[];
    application: string[];
}
export declare function permissionsToRequiredResourceAccess(permissions: Permission[]): RequiredResourceAccess[] | undefined;
export declare function generateAadManifest(projectPath: string, projectSettingsJson: any): Promise<void>;
export declare function needMigrateToAadManifest(ctx: CoreHookContext): Promise<boolean>;
export declare enum FileType {
    STATE = 0,
    CONFIG = 1,
    USERDATA = 2
}
export declare const fixedNamingsV3: {
    [key: string]: string;
};
export declare const provisionOutputNamingsV3: string[];
export declare const nameMappingV3: {
    [key: string]: string;
};
export declare const pluginIdMappingV3: {
    [key: string]: string;
};
export declare const secretKeys: string[];
export declare function namingConverterV3(name: string, type: FileType, bicepContent: string, needsRename?: boolean): Result<string, FxError>;
export declare function convertPluginId(name: string): string;
export declare function replacePlaceholdersForV3(content: string, bicepContent: string): string;
//# sourceMappingURL=MigrationUtils.d.ts.map