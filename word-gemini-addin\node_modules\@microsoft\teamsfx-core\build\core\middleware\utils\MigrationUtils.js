"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.replacePlaceholdersForV3 = exports.convertPluginId = exports.namingConverterV3 = exports.secretKeys = exports.pluginIdMappingV3 = exports.nameMappingV3 = exports.provisionOutputNamingsV3 = exports.fixedNamingsV3 = exports.FileType = exports.needMigrateToAadManifest = exports.generateAadManifest = exports.permissionsToRequiredResourceAccess = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const path_1 = tslib_1.__importDefault(require("path"));
const tools_1 = require("../../../common/tools");
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const localizeUtils_1 = require("../../../common/localizeUtils");
const globalVars_1 = require("../../globalVars");
const generateAadManifestTemplate_1 = require("../../generateAadManifestTemplate");
const constants_1 = require("../../../component/constants");
const error_1 = require("../../error");
function permissionsToRequiredResourceAccess(permissions) {
    const result = [];
    try {
        permissions.forEach((permission) => {
            const res = {
                resourceAppId: permission.resource,
                resourceAccess: permission.application
                    .map((item) => {
                    return { id: item, type: "Role" };
                })
                    .concat(permission.delegated.map((item) => {
                    return { id: item, type: "Scope" };
                })),
            };
            result.push(res);
        });
    }
    catch (err) {
        return undefined;
    }
    return result;
}
exports.permissionsToRequiredResourceAccess = permissionsToRequiredResourceAccess;
async function generateAadManifest(projectPath, projectSettingsJson) {
    const permissionFilePath = path_1.default.join(projectPath, "permissions.json");
    // add aad.template.file
    const permissions = (await fs_extra_1.default.readJson(permissionFilePath));
    const requiredResourceAccess = permissionsToRequiredResourceAccess(permissions);
    if (!requiredResourceAccess) {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(localizeUtils_1.getLocalizedString("core.aadManifestMigration.ParsePermissionsFailedWarning"));
    }
    await generateAadManifestTemplate_1.generateAadManifestTemplate(projectPath, projectSettingsJson, requiredResourceAccess, true);
}
exports.generateAadManifest = generateAadManifest;
async function needMigrateToAadManifest(ctx) {
    var _a, _b;
    try {
        if (!tools_1.isAadManifestEnabled()) {
            return false;
        }
        const inputs = ctx.arguments[ctx.arguments.length - 1];
        if (!inputs.projectPath) {
            return false;
        }
        const fxExist = await fs_extra_1.default.pathExists(path_1.default.join(inputs.projectPath, ".fx"));
        if (!fxExist) {
            return false;
        }
        const aadManifestTemplateExist = await fs_extra_1.default.pathExists(path_1.default.join(inputs.projectPath, "templates", "appPackage", "aad.template.json"));
        if (aadManifestTemplateExist) {
            return false;
        }
        const permissionFileExist = await fs_extra_1.default.pathExists(path_1.default.join(inputs.projectPath, "permissions.json"));
        if (!permissionFileExist) {
            return false;
        }
        const projectSettingsJson = await fs_extra_1.default.readJson(path_1.default.join(inputs.projectPath, ".fx", "configs", "projectSettings.json"));
        const aadPluginIsActive = (_b = (_a = projectSettingsJson.solutionSettings) === null || _a === void 0 ? void 0 : _a.activeResourcePlugins) === null || _b === void 0 ? void 0 : _b.includes(constants_1.PluginNames.AAD);
        if (!aadPluginIsActive) {
            return false;
        }
        return true;
    }
    catch (err) {
        return false;
    }
}
exports.needMigrateToAadManifest = needMigrateToAadManifest;
var FileType;
(function (FileType) {
    FileType[FileType["STATE"] = 0] = "STATE";
    FileType[FileType["CONFIG"] = 1] = "CONFIG";
    FileType[FileType["USERDATA"] = 2] = "USERDATA";
})(FileType = exports.FileType || (exports.FileType = {}));
exports.fixedNamingsV3 = {
    "state.solution.subscriptionId": "AZURE_SUBSCRIPTION_ID",
    "state.solution.resourceGroupName": "AZURE_RESOURCE_GROUP_NAME",
    "state.solution.resourceNameSuffix": "RESOURCE_SUFFIX",
    "state.fx-resource-appstudio.tenantId": "TEAMS_APP_TENANT_ID",
    "state.fx-resource-appstudio.teamsAppId": "TEAMS_APP_ID",
    "state.fx-resource-aad-app-for-teams.clientId": "AAD_APP_CLIENT_ID",
    "state.fx-resource-aad-app-for-teams.clientSecret": "SECRET_AAD_APP_CLIENT_SECRET",
    "state.fx-resource-aad-app-for-teams.objectId": "AAD_APP_OBJECT_ID",
    "state.fx-resource-aad-app-for-teams.oauth2PermissionScopeId": "AAD_APP_ACCESS_AS_USER_PERMISSION_ID",
    "state.fx-resource-aad-app-for-teams.tenantId": "AAD_APP_TENANT_ID",
    "state.fx-resource-aad-app-for-teams.oauthHost": "AAD_APP_OAUTH_AUTHORITY_HOST",
    "state.fx-resource-aad-app-for-teams.oauthAuthority": "AAD_APP_OAUTH_AUTHORITY",
    "state.fx-resource-bot.botId": "BOT_ID",
    "state.fx-resource-bot.botPassword": "SECRET_BOT_PASSWORD",
    "state.fx-resource-frontend-hosting.sslCertFile": "SSL_CRT_FILE",
    "state.fx-resource-frontend-hosting.sslKeyFile": "SSL_KEY_FILE",
    "state.fx-resource-apim.publisherEmail": "APIM__PUBLISHEREMAIL",
    "state.fx-resource-apim.publisherName": "APIM__PUBLISHERNAME",
};
exports.provisionOutputNamingsV3 = [
    "state.fx-resource-frontend-hosting.indexPath",
    "state.fx-resource-frontend-hosting.domain",
    "state.fx-resource-frontend-hosting.endpoint",
    "state.fx-resource-frontend-hosting.storageResourceId",
    "state.fx-resource-frontend-hosting.resourceId",
    "state.fx-resource-azure-sql.sqlResourceId",
    "state.fx-resource-azure-sql.sqlEndpoint",
    "state.fx-resource-azure-sql.databaseName",
    "state.fx-resource-apim.productResourceId",
    "state.fx-resource-apim.serviceResourceId",
    "state.fx-resource-apim.authServerResourceId",
    "state.fx-resource-bot.skuName",
    "state.fx-resource-bot.siteName",
    "state.fx-resource-bot.domain",
    "state.fx-resource-bot.appServicePlanName",
    "state.fx-resource-bot.resourceId",
    "state.fx-resource-bot.functionAppResourceId",
    "state.fx-resource-bot.botWebAppResourceId",
    "state.fx-resource-bot.siteEndpoint",
    "state.fx-resource-identity.identityName",
    "state.fx-resource-identity.identityResourceId",
    "state.fx-resource-identity.identityClientId",
    "state.fx-resource-function.sku",
    "state.fx-resource-function.appName",
    "state.fx-resource-function.domain",
    "state.fx-resource-function.appServicePlanName",
    "state.fx-resource-function.functionAppResourceId",
    "state.fx-resource-function.functionEndpoint",
    "state.fx-resource-key-vault.m365ClientSecretReference",
    "state.fx-resource-key-vault.botClientSecretReference",
];
exports.nameMappingV3 = {
    "state.fx-resource-aad-app-for-teams.botEndpoint": "state.fx-resource-bot.siteEndpoint",
    "state.fx-resource-aad-app-for-teams.frontendEndpoint": "state.fx-resource-frontend-hosting.endpoint",
};
exports.pluginIdMappingV3 = {
    "fx-resource-frontend-hosting": "teams-tab",
    "fx-resource-function": "teams-api",
    "fx-resource-identity": "identity",
    "fx-resource-bot": "teams-bot",
    "fx-resource-key-vault": "key-vault",
    "fx-resource-azure-sql": "azure-sql",
    "fx-resource-apim": "apim",
    "fx-resource-aad-app-for-teams": "aad-app",
    "fx-resource-appstudio": "app-manifest",
    "fx-resource-simple-auth": "simple-auth",
};
exports.secretKeys = [
    "state.fx-resource-aad-app-for-teams.clientSecret",
    "state.fx-resource-bot.botPassword",
    "state.fx-resource-apim.apimClientAADClientSecret",
    "state.fx-resource-azure-sql.adminPassword",
];
const secretPrefix = "SECRET_";
const configPrefix = "CONFIG__";
const provisionOutputPrefix = "PROVISIONOUTPUT__";
function generateOutputNameRegexForPlugin(pluginId) {
    return new RegExp("output +(\\S+) +object += +{" + // Mataches start of output declaration and capture output name. Example: output functionOutput object = {
        "[^{]*" + // Matches everything between '{' and plugin id declaration. For example: comments, extra properties. Will match multilines.
        `teamsFxPluginId: +\'${pluginId}\'`, // Matches given plugin id declaration
    "g");
}
function namingConverterV3(name, type, bicepContent, needsRename = false) {
    try {
        // Convert state.aad-app.clientId to state.fx-resource-aad-app-for-teams.clientId
        name = convertPluginId(name);
        // Needs to map certain values only when migrating manifest
        if (needsRename && Object.keys(exports.nameMappingV3).includes(name)) {
            name = exports.nameMappingV3[name];
        }
        if (Object.keys(exports.fixedNamingsV3).includes(name)) {
            return teamsfx_api_1.ok(exports.fixedNamingsV3[name]);
        }
        else if (exports.provisionOutputNamingsV3.some((element, index, array) => {
            // for sql, may have key like: state.fx-resource-azure-sql.databaseName_xxx
            return name.startsWith(element);
        }) &&
            bicepContent) {
            return teamsfx_api_1.ok(provisionOutputNamingConverterV3(name, bicepContent, type));
        }
        else {
            return teamsfx_api_1.ok(commonNamingConverterV3(name, type));
        }
    }
    catch (error) {
        return teamsfx_api_1.err(new teamsfx_api_1.SystemError(error_1.CoreSource, "FailedToConvertV2ConfigNameToV3", error === null || error === void 0 ? void 0 : error.message));
    }
}
exports.namingConverterV3 = namingConverterV3;
// convert x-xx.xxx.xxx to x_xx__xxx__xxx
function commonNamingConverterV3(name, type) {
    const names = name.split(".");
    const res = names.join("__").replace(/\-/g, "_").toUpperCase();
    switch (type) {
        case FileType.CONFIG:
            return `${configPrefix}${res}`;
        case FileType.USERDATA:
            if (res.startsWith("STATE__"))
                return `${secretPrefix}${res.substring(res.indexOf("STATE__") + 7)}`;
            else
                return `${secretPrefix}${res}`;
        case FileType.STATE:
        default:
            return res;
    }
}
function provisionOutputNamingConverterV3(name, bicepContent, type) {
    const names = name.split(".");
    const pluginNames = [names[1], exports.pluginIdMappingV3[names[1]]];
    const keyName = names[2];
    let outputName = "";
    for (const pluginName of pluginNames) {
        const pluginRegex = generateOutputNameRegexForPlugin(pluginName);
        let outputNames = pluginRegex.exec(bicepContent);
        if (outputNames !== null) {
            // if have multiple sql database
            if ("fx-resource-azure-sql" === pluginNames[0] &&
                keyName.startsWith("databaseName") &&
                keyName.includes("_")) {
                // database name may be: databaseName_xxxxxx
                const suffix = keyName.split("_")[1];
                do {
                    if (outputNames &&
                        outputNames[1] &&
                        outputNames[1].includes("_") &&
                        suffix === outputNames[1].split("_")[1]) {
                        outputName = outputNames[1];
                        break;
                    }
                } while ((outputNames = pluginRegex.exec(bicepContent)));
            }
            else {
                outputName = outputNames[1];
            }
            if (outputName) {
                break;
            }
        }
    }
    if (!outputName) {
        return commonNamingConverterV3(name, type);
    }
    return `${provisionOutputPrefix}${outputName}__${keyName}`.toUpperCase();
}
function convertPluginId(name) {
    const nameArray = name.split(".");
    if (!nameArray || nameArray.length <= 1) {
        return name;
    }
    const pluginId = nameArray[1];
    if (Object.values(exports.pluginIdMappingV3).includes(pluginId)) {
        const convertedPluginId = Object.keys(exports.pluginIdMappingV3).find((key) => exports.pluginIdMappingV3[key] === pluginId);
        name = name.replace(pluginId, convertedPluginId);
    }
    return name;
}
exports.convertPluginId = convertPluginId;
function replacePlaceholdersForV3(content, bicepContent) {
    const placeholderRegex = /{{+ *[a-zA-Z_.-][a-zA-Z0-9_.-]* *}}+/g;
    const placeholders = content.match(placeholderRegex);
    if (placeholders) {
        for (const placeholder of placeholders) {
            const envNameV2 = placeholder.replace(/\{/g, "").replace(/\}/g, "");
            const envNameV3 = namingConverterV3(envNameV2, exports.secretKeys.includes(convertPluginId(envNameV2)) ? FileType.USERDATA : FileType.STATE, bicepContent, true);
            if (envNameV3.isOk()) {
                content = content.replace(placeholder, `$\{\{${envNameV3.value}\}\}`);
            }
            else {
                throw envNameV3.error;
            }
        }
    }
    return content;
}
exports.replacePlaceholdersForV3 = replacePlaceholdersForV3;
//# sourceMappingURL=MigrationUtils.js.map