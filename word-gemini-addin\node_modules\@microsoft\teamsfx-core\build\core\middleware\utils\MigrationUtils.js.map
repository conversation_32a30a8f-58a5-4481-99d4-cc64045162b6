{"version": 3, "file": "MigrationUtils.js", "sourceRoot": "", "sources": ["../../../../src/core/middleware/utils/MigrationUtils.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,wDAAuF;AACvF,wDAAwB;AACxB,iDAA6D;AAE7D,gEAA0B;AAC1B,iEAAmE;AACnE,iDAAyC;AACzC,mFAAgF;AAChF,4DAA2D;AAE3D,uCAAyC;AAQzC,SAAgB,mCAAmC,CACjD,WAAyB;IAEzB,MAAM,MAAM,GAA6B,EAAE,CAAC;IAC5C,IAAI;QACF,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACjC,MAAM,GAAG,GAA2B;gBAClC,aAAa,EAAE,UAAU,CAAC,QAAQ;gBAClC,cAAc,EAAE,UAAU,CAAC,WAAW;qBACnC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;oBACZ,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;gBACpC,CAAC,CAAC;qBACD,MAAM,CACL,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;oBAChC,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;gBACrC,CAAC,CAAC,CACH;aACJ,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;KACJ;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAzBD,kFAyBC;AAEM,KAAK,UAAU,mBAAmB,CACvC,WAAmB,EACnB,mBAAwB;IAExB,MAAM,kBAAkB,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;IAEtE,wBAAwB;IACxB,MAAM,WAAW,GAAG,CAAC,MAAM,kBAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAiB,CAAC;IAE5E,MAAM,sBAAsB,GAAG,mCAAmC,CAAC,WAAW,CAAC,CAAC;IAChF,IAAI,CAAC,sBAAsB,EAAE;QAC3B,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,OAAO,CACxB,kCAAkB,CAAC,yDAAyD,CAAC,CAC9E,CAAC;KACH;IAED,MAAM,yDAA2B,CAAC,WAAW,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,IAAI,CAAC,CAAC;AACpG,CAAC;AAjBD,kDAiBC;AAEM,KAAK,UAAU,wBAAwB,CAAC,GAAoB;;IACjE,IAAI;QACF,IAAI,CAAC,4BAAoB,EAAE,EAAE;YAC3B,OAAO,KAAK,CAAC;SACd;QAED,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC;QACjE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACvB,OAAO,KAAK,CAAC;SACd;QACD,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAqB,EAAE,KAAK,CAAC,CAAC,CAAC;QACpF,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,KAAK,CAAC;SACd;QAED,MAAM,wBAAwB,GAAG,MAAM,kBAAE,CAAC,UAAU,CAClD,cAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAqB,EAAE,WAAW,EAAE,YAAY,EAAE,mBAAmB,CAAC,CACxF,CAAC;QAEF,IAAI,wBAAwB,EAAE;YAC5B,OAAO,KAAK,CAAC;SACd;QAED,MAAM,mBAAmB,GAAG,MAAM,kBAAE,CAAC,UAAU,CAC7C,cAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAqB,EAAE,kBAAkB,CAAC,CAC5D,CAAC;QAEF,IAAI,CAAC,mBAAmB,EAAE;YACxB,OAAO,KAAK,CAAC;SACd;QAED,MAAM,mBAAmB,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAC3C,cAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAqB,EAAE,KAAK,EAAE,SAAS,EAAE,sBAAsB,CAAC,CAClF,CAAC;QACF,MAAM,iBAAiB,GAAG,MAAA,MAAA,mBAAmB,CAAC,gBAAgB,0CAAE,qBAAqB,0CAAE,QAAQ,CAC7F,uBAAW,CAAC,GAAG,CAChB,CAAC;QAEF,IAAI,CAAC,iBAAiB,EAAE;YACtB,OAAO,KAAK,CAAC;SACd;QAED,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,GAAG,EAAE;QACZ,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AA9CD,4DA8CC;AAED,IAAY,QAIX;AAJD,WAAY,QAAQ;IAClB,yCAAK,CAAA;IACL,2CAAM,CAAA;IACN,+CAAQ,CAAA;AACV,CAAC,EAJW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAInB;AACY,QAAA,cAAc,GAA8B;IACvD,+BAA+B,EAAE,uBAAuB;IACxD,kCAAkC,EAAE,2BAA2B;IAC/D,mCAAmC,EAAE,iBAAiB;IACtD,sCAAsC,EAAE,qBAAqB;IAC7D,wCAAwC,EAAE,cAAc;IACxD,8CAA8C,EAAE,mBAAmB;IACnE,kDAAkD,EAAE,8BAA8B;IAClF,8CAA8C,EAAE,mBAAmB;IACnE,6DAA6D,EAC3D,sCAAsC;IACxC,8CAA8C,EAAE,mBAAmB;IACnE,+CAA+C,EAAE,8BAA8B;IAC/E,oDAAoD,EAAE,yBAAyB;IAC/E,6BAA6B,EAAE,QAAQ;IACvC,mCAAmC,EAAE,qBAAqB;IAC1D,gDAAgD,EAAE,cAAc;IAChE,+CAA+C,EAAE,cAAc;IAC/D,uCAAuC,EAAE,sBAAsB;IAC/D,sCAAsC,EAAE,qBAAqB;CAC9D,CAAC;AACW,QAAA,wBAAwB,GAAa;IAChD,8CAA8C;IAC9C,2CAA2C;IAC3C,6CAA6C;IAC7C,sDAAsD;IACtD,+CAA+C;IAC/C,2CAA2C;IAC3C,yCAAyC;IACzC,0CAA0C;IAC1C,0CAA0C;IAC1C,0CAA0C;IAC1C,6CAA6C;IAC7C,+BAA+B;IAC/B,gCAAgC;IAChC,8BAA8B;IAC9B,0CAA0C;IAC1C,kCAAkC;IAClC,6CAA6C;IAC7C,2CAA2C;IAC3C,oCAAoC;IACpC,yCAAyC;IACzC,+CAA+C;IAC/C,6CAA6C;IAC7C,gCAAgC;IAChC,oCAAoC;IACpC,mCAAmC;IACnC,+CAA+C;IAC/C,kDAAkD;IAClD,6CAA6C;IAC7C,uDAAuD;IACvD,sDAAsD;CACvD,CAAC;AACW,QAAA,aAAa,GAA8B;IACtD,iDAAiD,EAAE,oCAAoC;IACvF,sDAAsD,EACpD,6CAA6C;CAChD,CAAC;AACW,QAAA,iBAAiB,GAA8B;IAC1D,8BAA8B,EAAE,WAAW;IAC3C,sBAAsB,EAAE,WAAW;IACnC,sBAAsB,EAAE,UAAU;IAClC,iBAAiB,EAAE,WAAW;IAC9B,uBAAuB,EAAE,WAAW;IACpC,uBAAuB,EAAE,WAAW;IACpC,kBAAkB,EAAE,MAAM;IAC1B,+BAA+B,EAAE,SAAS;IAC1C,uBAAuB,EAAE,cAAc;IACvC,yBAAyB,EAAE,aAAa;CACzC,CAAC;AACW,QAAA,UAAU,GAAG;IACxB,kDAAkD;IAClD,mCAAmC;IACnC,kDAAkD;IAClD,2CAA2C;CAC5C,CAAC;AACF,MAAM,YAAY,GAAG,SAAS,CAAC;AAC/B,MAAM,YAAY,GAAG,UAAU,CAAC;AAChC,MAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAElD,SAAS,gCAAgC,CAAC,QAAgB;IACxD,OAAO,IAAI,MAAM,CACf,8BAA8B,GAAG,0GAA0G;QACzI,OAAO,GAAG,4HAA4H;QACtI,uBAAuB,QAAQ,IAAI,EAAE,sCAAsC;IAC7E,GAAG,CACJ,CAAC;AACJ,CAAC;AAED,SAAgB,iBAAiB,CAC/B,IAAY,EACZ,IAAc,EACd,YAAoB,EACpB,WAAW,GAAG,KAAK;IAEnB,IAAI;QACF,iFAAiF;QACjF,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;QAE7B,2DAA2D;QAC3D,IAAI,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,qBAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC5D,IAAI,GAAG,qBAAa,CAAC,IAAI,CAAC,CAAC;SAC5B;QACD,IAAI,MAAM,CAAC,IAAI,CAAC,sBAAc,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC9C,OAAO,gBAAE,CAAC,sBAAc,CAAC,IAAI,CAAC,CAAC,CAAC;SACjC;aAAM,IACL,gCAAwB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACtD,2EAA2E;YAC3E,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC;YACF,YAAY,EACZ;YACA,OAAO,gBAAE,CAAC,gCAAgC,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC;SACvE;aAAM;YACL,OAAO,gBAAE,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;SAChD;KACF;IAAC,OAAO,KAAU,EAAE;QACnB,OAAO,iBAAG,CAAC,IAAI,yBAAW,CAAC,kBAAU,EAAE,iCAAiC,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,CAAC,CAAC,CAAC;KAC5F;AACH,CAAC;AA9BD,8CA8BC;AAED,yCAAyC;AACzC,SAAS,uBAAuB,CAAC,IAAY,EAAE,IAAc;IAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;IAC/D,QAAQ,IAAI,EAAE;QACZ,KAAK,QAAQ,CAAC,MAAM;YAClB,OAAO,GAAG,YAAY,GAAG,GAAG,EAAE,CAAC;QACjC,KAAK,QAAQ,CAAC,QAAQ;YACpB,IAAI,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC3B,OAAO,GAAG,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;;gBAClE,OAAO,GAAG,YAAY,GAAG,GAAG,EAAE,CAAC;QACtC,KAAK,QAAQ,CAAC,KAAK,CAAC;QACpB;YACE,OAAO,GAAG,CAAC;KACd;AACH,CAAC;AAED,SAAS,gCAAgC,CACvC,IAAY,EACZ,YAAoB,EACpB,IAAc;IAEd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,yBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAEzB,IAAI,UAAU,GAAG,EAAE,CAAC;IAEpB,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;QACpC,MAAM,WAAW,GAAG,gCAAgC,CAAC,UAAU,CAAC,CAAC;QACjE,IAAI,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,WAAW,KAAK,IAAI,EAAE;YACxB,gCAAgC;YAChC,IACE,uBAAuB,KAAK,WAAW,CAAC,CAAC,CAAC;gBAC1C,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC;gBAClC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EACrB;gBACA,4CAA4C;gBAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,GAAG;oBACD,IACE,WAAW;wBACX,WAAW,CAAC,CAAC,CAAC;wBACd,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;wBAC5B,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EACvC;wBACA,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;wBAC5B,MAAM;qBACP;iBACF,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE;aAC1D;iBAAM;gBACL,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;aAC7B;YAED,IAAI,UAAU,EAAE;gBACd,MAAM;aACP;SACF;KACF;IAED,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KAC5C;IAED,OAAO,GAAG,qBAAqB,GAAG,UAAU,KAAK,OAAO,EAAE,CAAC,WAAW,EAAE,CAAC;AAC3E,CAAC;AAED,SAAgB,eAAe,CAAC,IAAY;IAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;QACvC,OAAO,IAAI,CAAC;KACb;IACD,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;IAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,yBAAiB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACvD,MAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,yBAAiB,CAAC,CAAC,IAAI,CAC3D,CAAC,GAAG,EAAE,EAAE,CAAC,yBAAiB,CAAC,GAAG,CAAC,KAAK,QAAQ,CAC7C,CAAC;QACF,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,iBAAkB,CAAC,CAAC;KACnD;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAbD,0CAaC;AAED,SAAgB,wBAAwB,CAAC,OAAe,EAAE,YAAoB;IAC5E,MAAM,gBAAgB,GAAG,uCAAuC,CAAC;IACjE,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAErD,IAAI,YAAY,EAAE;QAChB,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;YACtC,MAAM,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACpE,MAAM,SAAS,GAAG,iBAAiB,CACjC,SAAS,EACT,kBAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EACpF,YAAY,EACZ,IAAI,CACL,CAAC;YACF,IAAI,SAAS,CAAC,IAAI,EAAE,EAAE;gBACpB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,SAAS,CAAC,KAAK,MAAM,CAAC,CAAC;aACvE;iBAAM;gBACL,MAAM,SAAS,CAAC,KAAK,CAAC;aACvB;SACF;KACF;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAtBD,4DAsBC"}