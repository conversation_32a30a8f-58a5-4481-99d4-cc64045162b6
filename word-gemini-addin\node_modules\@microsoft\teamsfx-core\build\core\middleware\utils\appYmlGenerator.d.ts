import { ProjectSettings } from "@microsoft/teamsfx-api";
import { DebugPlaceholderMapping } from "./debug/debugV3MigrationUtils";
export declare abstract class BaseAppYmlGenerator {
    protected oldProjectSettings: ProjectSettings;
    protected abstract handlebarsContext: any;
    constructor(oldProjectSettings: ProjectSettings);
    protected buildHandlebarsTemplate(templateName: string): Promise<string>;
}
export declare class AppYmlGenerator extends BaseAppYmlGenerator {
    private bicepContent;
    private projectPath;
    protected handlebarsContext: {
        activePlugins: Record<string, boolean>;
        placeholderMappings: Record<string, string>;
        aadAppName: string | undefined;
        teamsAppName: string | undefined;
        appName: string | undefined;
        isFunctionBot: boolean;
        isWebAppBot: boolean;
        isTypescript: boolean;
        defaultFunctionName: string | undefined;
        environmentFolder: string | undefined;
        projectId: string | undefined;
    };
    constructor(oldProjectSettings: ProjectSettings, bicepContent: string, projectPath: string);
    generateAppYml(): Promise<string>;
    generateAppLocalYml(placeholderMappings: DebugPlaceholderMapping): Promise<string>;
    private generateCommonHandlerbarsContext;
    private generateAzureHandlebarsContext;
    private setPlaceholderMapping;
}
//# sourceMappingURL=appYmlGenerator.d.ts.map