"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppYmlGenerator = exports.BaseAppYmlGenerator = void 0;
const tslib_1 = require("tslib");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const MigrationUtils_1 = require("./MigrationUtils");
const path = tslib_1.__importStar(require("path"));
const fs = tslib_1.__importStar(require("fs-extra"));
const handlebars = tslib_1.__importStar(require("handlebars"));
const folder_1 = require("../../../folder");
const versionMetadata_1 = require("../../../common/versionMetadata");
class BaseAppYmlGenerator {
    constructor(oldProjectSettings) {
        this.oldProjectSettings = oldProjectSettings;
    }
    async buildHandlebarsTemplate(templateName) {
        const templatePath = path.join(folder_1.getTemplatesFolder(), "core/v3Migration", templateName);
        const templateString = await fs.readFile(templatePath, "utf8");
        const template = handlebars.compile(templateString);
        return template(this.handlebarsContext);
    }
}
exports.BaseAppYmlGenerator = BaseAppYmlGenerator;
class AppYmlGenerator extends BaseAppYmlGenerator {
    constructor(oldProjectSettings, bicepContent, projectPath) {
        super(oldProjectSettings);
        this.bicepContent = bicepContent;
        this.projectPath = projectPath;
        this.handlebarsContext = {
            activePlugins: {},
            placeholderMappings: {},
            aadAppName: undefined,
            teamsAppName: undefined,
            appName: undefined,
            isFunctionBot: false,
            isWebAppBot: false,
            isTypescript: false,
            defaultFunctionName: undefined,
            environmentFolder: undefined,
            projectId: undefined,
        };
    }
    async generateAppYml() {
        var _a;
        await this.generateCommonHandlerbarsContext();
        const solutionSettings = this.oldProjectSettings.solutionSettings;
        if (solutionSettings.hostType.toLowerCase() === "azure") {
            await this.generateAzureHandlebarsContext();
            switch ((_a = this.oldProjectSettings.programmingLanguage) === null || _a === void 0 ? void 0 : _a.toLowerCase()) {
                case "javascript":
                case "typescript":
                    return await this.buildHandlebarsTemplate("js.ts.app.yml");
                case "csharp":
                    return await this.buildHandlebarsTemplate("csharp.app.yml");
            }
        }
        else if (solutionSettings.hostType.toLowerCase() === "spfx") {
            return await this.buildHandlebarsTemplate("spfx.app.yml");
        }
        throw new Error("The current tooling cannot upgrade your project temporary. Please raise an issue in GitHub for your project.");
    }
    async generateAppLocalYml(placeholderMappings) {
        var _a;
        this.handlebarsContext.placeholderMappings = placeholderMappings;
        await this.generateAzureHandlebarsContext();
        const solutionSettings = this.oldProjectSettings.solutionSettings;
        if (solutionSettings.hostType === "Azure") {
            switch ((_a = this.oldProjectSettings.programmingLanguage) === null || _a === void 0 ? void 0 : _a.toLowerCase()) {
                case "csharp":
                    return await this.buildHandlebarsTemplate("csharp.app.local.yml");
            }
        }
        throw new Error("The current tooling cannot upgrade your project temporary. Please raise an issue in GitHub for your project.");
    }
    async generateCommonHandlerbarsContext() {
        var _a;
        // project setting information
        this.handlebarsContext.appName = this.oldProjectSettings.appName;
        const azureSolutionSettings = this.oldProjectSettings.solutionSettings;
        for (const activePlugin of azureSolutionSettings.activeResourcePlugins) {
            this.handlebarsContext.activePlugins[activePlugin] = true; // convert array items to object properties to simplify handlebars template
        }
        // app names
        const aadManifestPath = path.join(this.projectPath, "aad.manifest.template.json");
        if (await fs.pathExists(aadManifestPath)) {
            const aadManifest = await fs.readJson(path.join(this.projectPath, "aad.manifest.template.json"));
            this.handlebarsContext.aadAppName = aadManifest.name;
        }
        const teamsAppManifestPath = path.join(this.projectPath, teamsfx_api_1.AppPackageFolderName, versionMetadata_1.MetadataV3.teamsManifestFileName);
        if (await fs.pathExists(teamsAppManifestPath)) {
            const teamsAppManifest = await fs.readJson(path.join(this.projectPath, teamsfx_api_1.AppPackageFolderName, versionMetadata_1.MetadataV3.teamsManifestFileName));
            this.handlebarsContext.teamsAppName = teamsAppManifest.name.short;
        }
        // programming language
        this.handlebarsContext.isTypescript =
            ((_a = this.oldProjectSettings.programmingLanguage) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === "typescript";
        // default function name
        this.handlebarsContext.defaultFunctionName = this.oldProjectSettings.defaultFunctionName;
        // projectId
        this.handlebarsContext.projectId = this.oldProjectSettings.projectId;
        // env folder
        this.handlebarsContext.environmentFolder = versionMetadata_1.MetadataV3.defaultEnvironmentFolder;
    }
    async generateAzureHandlebarsContext() {
        // isFunctionBot
        const pluginSettings = this.oldProjectSettings.pluginSettings;
        if (pluginSettings &&
            pluginSettings["fx-resource-bot"] &&
            pluginSettings["fx-resource-bot"]["host-type"] === "azure-function") {
            this.handlebarsContext.isFunctionBot = true;
        }
        // isWebAppBot and the resourceId in bicep should be "botWebAppResourceId", then map state.fx-resource-bot.botWebAppResourceId
        if (pluginSettings &&
            pluginSettings["fx-resource-bot"] &&
            pluginSettings["fx-resource-bot"]["host-type"] === "app-service" &&
            this.bicepContent.includes("botWebAppResourceId")) {
            this.handlebarsContext.isWebAppBot = true;
        }
        // placeholders
        this.setPlaceholderMapping("state.fx-resource-frontend-hosting.storageResourceId");
        this.setPlaceholderMapping("state.fx-resource-frontend-hosting.endpoint");
        this.setPlaceholderMapping("state.fx-resource-frontend-hosting.resourceId");
        this.setPlaceholderMapping("state.fx-resource-frontend-hosting.indexPath");
        this.setPlaceholderMapping("state.fx-resource-bot.resourceId");
        this.setPlaceholderMapping("state.fx-resource-bot.functionAppResourceId");
        this.setPlaceholderMapping("state.fx-resource-bot.botWebAppResourceId");
        this.setPlaceholderMapping("state.fx-resource-function.functionAppResourceId");
        this.setPlaceholderMapping("state.fx-resource-function.functionEndpoint");
    }
    setPlaceholderMapping(placeholder) {
        const result = MigrationUtils_1.namingConverterV3(placeholder, MigrationUtils_1.FileType.STATE, this.bicepContent);
        if (result.isOk()) {
            this.handlebarsContext.placeholderMappings[placeholder] = result.value;
        }
        // ignore non-exist placeholder
    }
}
exports.AppYmlGenerator = AppYmlGenerator;
//# sourceMappingURL=appYmlGenerator.js.map