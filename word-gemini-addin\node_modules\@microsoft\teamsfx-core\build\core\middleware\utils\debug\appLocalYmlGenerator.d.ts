import { ProjectSettings } from "@microsoft/teamsfx-api";
import { BuildArgs } from "../../../../component/driver/interface/buildAndDeployArgs";
import { InstallToolArgs } from "../../../../component/driver/prerequisite/interfaces/InstallToolArgs";
import { BaseAppYmlGenerator } from "../appYmlGenerator";
import { DebugPlaceholderMapping } from "./debugV3MigrationUtils";
export declare class AppLocalYmlConfig {
    registerApp?: {
        aad?: boolean;
        teamsApp?: boolean;
    };
    provision?: {
        bot?: {
            messagingEndpoint: string;
            isM365?: boolean;
        };
    };
    configureApp?: {
        tab?: {
            domain?: string;
            endpoint?: string;
        };
        aad?: boolean;
        teamsApp?: {
            appPackagePath?: string;
        };
    };
    deploy?: {
        tools?: InstallToolArgs;
        npmCommands?: BuildArgs[];
        dotnetCommand?: BuildArgs;
        tab?: {
            port?: number;
        };
        bot?: boolean;
        sso?: boolean;
        ssoTab?: {
            functionName?: string;
        };
        ssoBot?: boolean;
        ssoFunction?: boolean;
        frontendStart?: {
            sso?: boolean;
            functionName?: string;
        };
        authStart?: {
            appsettingsPath: string;
        };
        botStart?: {
            tab?: boolean;
            function?: boolean;
            sso?: boolean;
        };
        backendStart?: boolean;
    };
}
export declare class AppLocalYmlGenerator extends BaseAppYmlGenerator {
    protected handlebarsContext: {
        config: AppLocalYmlConfig;
        placeholderMappings: DebugPlaceholderMapping;
    };
    constructor(oldProjectSettings: ProjectSettings, config: AppLocalYmlConfig, placeholderMappings: DebugPlaceholderMapping);
    generateAppYml(): Promise<string>;
    private generateHandlerbarsContext;
}
//# sourceMappingURL=appLocalYmlGenerator.d.ts.map