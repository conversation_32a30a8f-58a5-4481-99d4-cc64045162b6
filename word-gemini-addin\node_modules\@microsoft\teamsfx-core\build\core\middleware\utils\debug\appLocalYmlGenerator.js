"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppLocalYmlGenerator = exports.AppLocalYmlConfig = void 0;
const appYmlGenerator_1 = require("../appYmlGenerator");
const debugV3MigrationUtils_1 = require("./debugV3MigrationUtils");
class AppLocalYmlConfig {
}
exports.AppLocalYmlConfig = AppLocalYmlConfig;
class AppLocalYmlGenerator extends appYmlGenerator_1.BaseAppYmlGenerator {
    constructor(oldProjectSettings, config, placeholderMappings) {
        super(oldProjectSettings);
        this.handlebarsContext = {
            config: config,
            placeholderMappings: placeholderMappings,
        };
    }
    async generateAppYml() {
        var _a;
        this.generateHandlerbarsContext();
        switch ((_a = this.oldProjectSettings.programmingLanguage) === null || _a === void 0 ? void 0 : _a.toLowerCase()) {
            case "javascript":
            case "typescript":
            default:
                // only support js/ts at first
                return await this.buildHandlebarsTemplate("js.ts.app.local.yml");
        }
    }
    async generateHandlerbarsContext() {
        var _a, _b;
        let functionName = undefined;
        if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeFunction(this.oldProjectSettings)) {
            functionName =
                debugV3MigrationUtils_1.OldProjectSettingsHelper.getFunctionName(this.oldProjectSettings) || "getUserProfile";
        }
        if ((_a = this.handlebarsContext.config.provision) === null || _a === void 0 ? void 0 : _a.bot) {
            this.handlebarsContext.config.provision.bot.isM365 = this.oldProjectSettings.isM365;
        }
        if ((_b = this.handlebarsContext.config.deploy) === null || _b === void 0 ? void 0 : _b.sso) {
            if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeTab(this.oldProjectSettings)) {
                this.handlebarsContext.config.deploy.ssoTab = {
                    functionName,
                };
            }
            if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeBot(this.oldProjectSettings)) {
                this.handlebarsContext.config.deploy.ssoBot = true;
            }
            if (functionName) {
                this.handlebarsContext.config.deploy.ssoFunction = true;
            }
        }
    }
}
exports.AppLocalYmlGenerator = AppLocalYmlGenerator;
//# sourceMappingURL=appLocalYmlGenerator.js.map