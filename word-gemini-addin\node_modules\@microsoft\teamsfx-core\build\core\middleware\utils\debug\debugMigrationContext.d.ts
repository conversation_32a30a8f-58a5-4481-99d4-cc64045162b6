import { ProjectSettings } from "@microsoft/teamsfx-api";
import { CommentArray, CommentJSONValue } from "comment-json";
import { MigrationContext } from "../migrationContext";
import { AppLocalYmlConfig } from "./appLocalYmlGenerator";
import { DebugPlaceholderMapping } from "./debugV3MigrationUtils";
export declare class DebugMigrationContext {
    migrationContext: MigrationContext;
    tasks: CommentArray<CommentJSONValue>;
    appYmlConfig: AppLocalYmlConfig;
    oldProjectSettings: ProjectSettings;
    placeholderMapping: DebugPlaceholderMapping;
    generatedLabels: string[];
    constructor(migrationContext: MigrationContext, tasks: CommentArray<CommentJSONValue>, oldProjectSettings: ProjectSettings, placeholderMapping: DebugPlaceholderMapping);
}
//# sourceMappingURL=debugMigrationContext.d.ts.map