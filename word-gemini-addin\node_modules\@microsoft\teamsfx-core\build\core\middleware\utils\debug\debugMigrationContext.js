"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.DebugMigrationContext = void 0;
const appLocalYmlGenerator_1 = require("./appLocalYmlGenerator");
class DebugMigrationContext {
    constructor(migrationContext, tasks, oldProjectSettings, placeholderMapping) {
        this.generatedLabels = [];
        this.migrationContext = migrationContext;
        this.tasks = tasks;
        this.appYmlConfig = new appLocalYmlGenerator_1.AppLocalYmlConfig();
        this.oldProjectSettings = oldProjectSettings;
        this.placeholderMapping = placeholderMapping;
    }
}
exports.DebugMigrationContext = DebugMigrationContext;
//# sourceMappingURL=debugMigrationContext.js.map