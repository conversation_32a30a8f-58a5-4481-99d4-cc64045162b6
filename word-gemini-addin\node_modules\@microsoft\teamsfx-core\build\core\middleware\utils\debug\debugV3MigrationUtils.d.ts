import { CommentArray, CommentJ<PERSON>NValue, CommentObject } from "comment-json";
import { MigrationContext } from "../migrationContext";
import { ProjectSettings } from "@microsoft/teamsfx-api";
export declare function readJsonCommentFile(filepath: string): Promise<CommentJSONValue | undefined>;
export declare function isCommentObject(data: CommentJSONValue | undefined): data is CommentObject;
export declare function isCommentArray(data: CommentJSONValue | undefined): data is CommentArray<CommentJSONValue>;
export interface DebugPlaceholderMapping {
    tabDomain?: string;
    tabEndpoint?: string;
    tabIndexPath?: string;
    botDomain?: string;
    botEndpoint?: string;
}
export declare function getPlaceholderMappings(context: MigrationContext): Promise<DebugPlaceholderMapping>;
export declare class OldProjectSettingsHelper {
    static includeTab(oldProjectSettings: ProjectSettings): boolean;
    static includeBot(oldProjectSettings: ProjectSettings): boolean;
    static includeFunction(oldProjectSettings: ProjectSettings): boolean;
    static includeFuncHostedBot(oldProjectSettings: ProjectSettings): boolean;
    static includeSSO(oldProjectSettings: ProjectSettings): boolean;
    static getFunctionName(oldProjectSettings: ProjectSettings): string | undefined;
    private static includePlugin;
}
export declare function updateLocalEnv(context: MigrationContext, envs: {
    [key: string]: string;
}): Promise<void>;
export declare function generateLabel(base: string, existingLabels: string[]): string;
export declare function createResourcesTask(label: string): CommentJSONValue;
export declare function setUpLocalProjectsTask(label: string): CommentJSONValue;
export declare function startFrontendTask(label: string): CommentJSONValue;
export declare function startAuthTask(label: string): CommentJSONValue;
export declare function watchBackendTask(label: string): CommentJSONValue;
export declare function startBackendTask(label: string, programmingLanguage?: string): CommentJSONValue;
export declare function startBotTask(label: string, programmingLanguage?: string): CommentJSONValue;
//# sourceMappingURL=debugV3MigrationUtils.d.ts.map