"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.startBotTask = exports.startBackendTask = exports.watchBackendTask = exports.startAuthTask = exports.startFrontendTask = exports.setUpLocalProjectsTask = exports.createResourcesTask = exports.generateLabel = exports.updateLocalEnv = exports.OldProjectSettingsHelper = exports.getPlaceholderMappings = exports.isCommentArray = exports.isCommentObject = exports.readJsonCommentFile = void 0;
const tslib_1 = require("tslib");
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const comment_json_1 = require("comment-json");
const MigrationUtils_1 = require("../MigrationUtils");
const v3MigrationUtils_1 = require("../v3MigrationUtils");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const dotenv = tslib_1.__importStar(require("dotenv"));
const os = tslib_1.__importStar(require("os"));
const path = tslib_1.__importStar(require("path"));
async function readJsonCommentFile(filepath) {
    if (await fs_extra_1.default.pathExists(filepath)) {
        const content = await fs_extra_1.default.readFile(filepath);
        const data = comment_json_1.parse(content.toString());
        return data;
    }
}
exports.readJsonCommentFile = readJsonCommentFile;
function isCommentObject(data) {
    return typeof data === "object" && !Array.isArray(data) && !!data;
}
exports.isCommentObject = isCommentObject;
function isCommentArray(data) {
    return Array.isArray(data);
}
exports.isCommentArray = isCommentArray;
async function getPlaceholderMappings(context) {
    const bicepContent = await v3MigrationUtils_1.readBicepContent(context);
    const getName = (name) => {
        const res = MigrationUtils_1.namingConverterV3(name, MigrationUtils_1.FileType.STATE, bicepContent);
        return res.isOk() ? res.value : undefined;
    };
    return {
        tabDomain: getName("state.fx-resource-frontend-hosting.domain"),
        tabEndpoint: getName("state.fx-resource-frontend-hosting.endpoint"),
        tabIndexPath: getName("state.fx-resource-frontend-hosting.indexPath"),
        botDomain: getName("state.fx-resource-bot.domain"),
        botEndpoint: getName("state.fx-resource-bot.siteEndpoint"),
    };
}
exports.getPlaceholderMappings = getPlaceholderMappings;
class OldProjectSettingsHelper {
    static includeTab(oldProjectSettings) {
        return this.includePlugin(oldProjectSettings, "fx-resource-frontend-hosting");
    }
    static includeBot(oldProjectSettings) {
        return this.includePlugin(oldProjectSettings, "fx-resource-bot");
    }
    static includeFunction(oldProjectSettings) {
        return this.includePlugin(oldProjectSettings, "fx-resource-function");
    }
    static includeFuncHostedBot(oldProjectSettings) {
        var _a, _b;
        return (this.includePlugin(oldProjectSettings, "fx-resource-bot") &&
            ((_b = (_a = oldProjectSettings.pluginSettings) === null || _a === void 0 ? void 0 : _a["fx-resource-bot"]) === null || _b === void 0 ? void 0 : _b["host-type"]) === "azure-function");
    }
    static includeSSO(oldProjectSettings) {
        return this.includePlugin(oldProjectSettings, "fx-resource-aad-app-for-teams");
    }
    static getFunctionName(oldProjectSettings) {
        return oldProjectSettings.defaultFunctionName;
    }
    static includePlugin(oldProjectSettings, pluginName) {
        const azureSolutionSettings = oldProjectSettings.solutionSettings;
        return azureSolutionSettings.activeResourcePlugins.includes(pluginName);
    }
}
exports.OldProjectSettingsHelper = OldProjectSettingsHelper;
async function updateLocalEnv(context, envs) {
    if (Object.keys(envs).length === 0) {
        return;
    }
    await context.fsEnsureDir(teamsfx_api_1.SettingsFolderName);
    const localEnvPath = path.join(teamsfx_api_1.SettingsFolderName, ".env.local");
    if (!(await context.fsPathExists(localEnvPath))) {
        await context.fsCreateFile(localEnvPath);
    }
    const existingEnvs = dotenv.parse(await fs_extra_1.default.readFile(path.join(context.projectPath, localEnvPath)));
    const content = Object.entries(Object.assign(Object.assign({}, existingEnvs), envs))
        .map(([key, value]) => `${key}=${value}`)
        .join(os.EOL);
    await context.fsWriteFile(localEnvPath, content, {
        encoding: "utf-8",
    });
}
exports.updateLocalEnv = updateLocalEnv;
function generateLabel(base, existingLabels) {
    let prefix = 0;
    while (true) {
        const generatedLabel = base + (prefix > 0 ? ` ${prefix.toString()}` : "");
        if (!existingLabels.includes(generatedLabel)) {
            return generatedLabel;
        }
        prefix += 1;
    }
}
exports.generateLabel = generateLabel;
function createResourcesTask(label) {
    const comment = `{
    // Create the debug resources.
    // See https://aka.ms/teamsfx-provision-task to know the details and how to customize the args.
  }`;
    const task = {
        label,
        type: "teamsfx",
        command: "provision",
        args: {
            template: "${workspaceFolder}/teamsapp.local.yml",
            env: "local",
        },
    };
    return comment_json_1.assign(comment_json_1.parse(comment), task);
}
exports.createResourcesTask = createResourcesTask;
function setUpLocalProjectsTask(label) {
    const comment = `{
    // Build project.
    // See https://aka.ms/teamsfx-deploy-task to know the details and how to customize the args.
  }`;
    const task = {
        label,
        type: "teamsfx",
        command: "deploy",
        args: {
            template: "${workspaceFolder}/teamsapp.local.yml",
            env: "local",
        },
    };
    return comment_json_1.assign(comment_json_1.parse(comment), task);
}
exports.setUpLocalProjectsTask = setUpLocalProjectsTask;
function startFrontendTask(label) {
    const task = {
        label,
        type: "shell",
        command: "npx env-cmd --silent -f .localSettings react-scripts start",
        isBackground: true,
        options: {
            cwd: "${workspaceFolder}/tabs",
        },
        problemMatcher: {
            pattern: {
                regexp: "^.*$",
                file: 0,
                location: 1,
                message: 2,
            },
            background: {
                activeOnStart: true,
                beginsPattern: ".*",
                endsPattern: "Compiled|Failed|compiled|failed",
            },
        },
    };
    return comment_json_1.assign(comment_json_1.parse("{}"), task);
}
exports.startFrontendTask = startFrontendTask;
function startAuthTask(label) {
    const task = {
        label,
        type: "shell",
        command: "dotnet Microsoft.TeamsFx.SimpleAuth.dll",
        isBackground: true,
        options: {
            cwd: path.join(os.homedir(), ".fx", "localauth"),
            env: {
                ASPNETCORE_ENVIRONMENT: "Development",
                PATH: "${command:fx-extension.get-dotnet-path}${env:PATH}",
            },
        },
        problemMatcher: {
            pattern: [
                {
                    regexp: "^.*$",
                    file: 0,
                    location: 1,
                    message: 2,
                },
            ],
            background: {
                activeOnStart: true,
                beginsPattern: ".*",
                endsPattern: ".*",
            },
        },
    };
    return comment_json_1.assign(comment_json_1.parse("{}"), task);
}
exports.startAuthTask = startAuthTask;
function watchBackendTask(label) {
    const task = {
        label,
        type: "shell",
        command: "tsc --watch",
        isBackground: true,
        options: {
            cwd: "${workspaceFolder}/api",
        },
        problemMatcher: "$tsc-watch",
        presentation: {
            reveal: "silent",
        },
    };
    return comment_json_1.assign(comment_json_1.parse("{}"), task);
}
exports.watchBackendTask = watchBackendTask;
function startBackendTask(label, programmingLanguage) {
    programmingLanguage = programmingLanguage || "javascript";
    const command = `npx env-cmd --silent -f .localSettings func start --${programmingLanguage} --language-worker="--inspect=9229" --port "7071" --cors "*"`;
    const task = {
        label,
        type: "shell",
        command,
        isBackground: true,
        options: {
            cwd: "${workspaceFolder}/api",
            env: {
                PATH: "${command:fx-extension.get-func-path}${env:PATH}",
            },
        },
        problemMatcher: {
            pattern: {
                regexp: "^.*$",
                file: 0,
                location: 1,
                message: 2,
            },
            background: {
                activeOnStart: true,
                beginsPattern: "^.*(Job host stopped|signaling restart).*$",
                endsPattern: "^.*(Worker process started and initialized|Host lock lease acquired by instance ID).*$",
            },
        },
        presentation: {
            reveal: "silent",
        },
    };
    return comment_json_1.assign(comment_json_1.parse("{}"), task);
}
exports.startBackendTask = startBackendTask;
function startBotTask(label, programmingLanguage) {
    const command = programmingLanguage === "typescript"
        ? "npx env-cmd --silent -f .localSettings nodemon --inspect=9239 --signal SIGINT -r ts-node/register index.ts"
        : "npx env-cmd --silent -f .localSettings nodemon --inspect=9239 --signal SIGINT index.js";
    const task = {
        label,
        type: "shell",
        command,
        isBackground: true,
        options: {
            cwd: "${workspaceFolder}/bot",
        },
        problemMatcher: {
            pattern: [
                {
                    regexp: "^.*$",
                    file: 0,
                    location: 1,
                    message: 2,
                },
            ],
            background: {
                activeOnStart: true,
                beginsPattern: "[nodemon] starting",
                endsPattern: "restify listening to|Bot/ME service listening at|[nodemon] app crashed",
            },
        },
    };
    return comment_json_1.assign(comment_json_1.parse("{}"), task);
}
exports.startBotTask = startBotTask;
//# sourceMappingURL=debugV3MigrationUtils.js.map