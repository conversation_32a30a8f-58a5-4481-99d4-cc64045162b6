{"version": 3, "file": "debugV3MigrationUtils.js", "sourceRoot": "", "sources": ["../../../../../src/core/middleware/utils/debug/debugV3MigrationUtils.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,gEAA0B;AAC1B,+CAA4F;AAC5F,sDAAgE;AAEhE,0DAAuD;AACvD,wDAAoG;AACpG,uDAAiC;AACjC,+CAAyB;AACzB,mDAA6B;AAEtB,KAAK,UAAU,mBAAmB,CAAC,QAAgB;IACxD,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC5C,MAAM,IAAI,GAAG,oBAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC;KACb;AACH,CAAC;AAND,kDAMC;AAED,SAAgB,eAAe,CAAC,IAAkC;IAChE,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACpE,CAAC;AAFD,0CAEC;AAED,SAAgB,cAAc,CAC5B,IAAkC;IAElC,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC7B,CAAC;AAJD,wCAIC;AAUM,KAAK,UAAU,sBAAsB,CAC1C,OAAyB;IAEzB,MAAM,YAAY,GAAG,MAAM,mCAAgB,CAAC,OAAO,CAAC,CAAC;IACrD,MAAM,OAAO,GAAG,CAAC,IAAY,EAAE,EAAE;QAC/B,MAAM,GAAG,GAAG,kCAAiB,CAAC,IAAI,EAAE,yBAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAClE,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;IAC5C,CAAC,CAAC;IACF,OAAO;QACL,SAAS,EAAE,OAAO,CAAC,2CAA2C,CAAC;QAC/D,WAAW,EAAE,OAAO,CAAC,6CAA6C,CAAC;QACnE,YAAY,EAAE,OAAO,CAAC,8CAA8C,CAAC;QACrE,SAAS,EAAE,OAAO,CAAC,8BAA8B,CAAC;QAClD,WAAW,EAAE,OAAO,CAAC,oCAAoC,CAAC;KAC3D,CAAC;AACJ,CAAC;AAfD,wDAeC;AAED,MAAa,wBAAwB;IAC5B,MAAM,CAAC,UAAU,CAAC,kBAAmC;QAC1D,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,8BAA8B,CAAC,CAAC;IAChF,CAAC;IAEM,MAAM,CAAC,UAAU,CAAC,kBAAmC;QAC1D,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;IACnE,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,kBAAmC;QAC/D,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,CAAC;IACxE,CAAC;IAEM,MAAM,CAAC,oBAAoB,CAAC,kBAAmC;;QACpE,OAAO,CACL,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;YACzD,CAAA,MAAA,MAAA,kBAAkB,CAAC,cAAc,0CAAG,iBAAiB,CAAC,0CAAG,WAAW,CAAC,MAAK,gBAAgB,CAC3F,CAAC;IACJ,CAAC;IAEM,MAAM,CAAC,UAAU,CAAC,kBAAmC;QAC1D,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,+BAA+B,CAAC,CAAC;IACjF,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,kBAAmC;QAC/D,OAAO,kBAAkB,CAAC,mBAAmB,CAAC;IAChD,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,kBAAmC,EAAE,UAAkB;QAClF,MAAM,qBAAqB,GAAG,kBAAkB,CAAC,gBAAyC,CAAC;QAC3F,OAAO,qBAAqB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC1E,CAAC;CACF;AAhCD,4DAgCC;AAEM,KAAK,UAAU,cAAc,CAClC,OAAyB,EACzB,IAA+B;IAE/B,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,OAAO;KACR;IACD,MAAM,OAAO,CAAC,WAAW,CAAC,gCAAkB,CAAC,CAAC;IAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,gCAAkB,EAAE,YAAY,CAAC,CAAC;IACjE,IAAI,CAAC,CAAC,MAAM,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,EAAE;QAC/C,MAAM,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;KAC1C;IACD,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAC/B,MAAM,kBAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAChE,CAAC;IACF,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,iCAAM,YAAY,GAAK,IAAI,EAAG;SACzD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;SACxC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAChB,MAAM,OAAO,CAAC,WAAW,CAAC,YAAY,EAAE,OAAO,EAAE;QAC/C,QAAQ,EAAE,OAAO;KAClB,CAAC,CAAC;AACL,CAAC;AArBD,wCAqBC;AAED,SAAgB,aAAa,CAAC,IAAY,EAAE,cAAwB;IAClE,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,OAAO,IAAI,EAAE;QACX,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YAC5C,OAAO,cAAc,CAAC;SACvB;QACD,MAAM,IAAI,CAAC,CAAC;KACb;AACH,CAAC;AATD,sCASC;AAED,SAAgB,mBAAmB,CAAC,KAAa;IAC/C,MAAM,OAAO,GAAG;;;IAGd,CAAC;IACH,MAAM,IAAI,GAAG;QACX,KAAK;QACL,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,WAAW;QACpB,IAAI,EAAE;YACJ,QAAQ,EAAE,uCAAuC;YACjD,GAAG,EAAE,OAAO;SACb;KACF,CAAC;IACF,OAAO,qBAAM,CAAC,oBAAK,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;AACtC,CAAC;AAfD,kDAeC;AAED,SAAgB,sBAAsB,CAAC,KAAa;IAClD,MAAM,OAAO,GAAG;;;IAGd,CAAC;IACH,MAAM,IAAI,GAAG;QACX,KAAK;QACL,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,QAAQ;QACjB,IAAI,EAAE;YACJ,QAAQ,EAAE,uCAAuC;YACjD,GAAG,EAAE,OAAO;SACb;KACF,CAAC;IACF,OAAO,qBAAM,CAAC,oBAAK,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;AACtC,CAAC;AAfD,wDAeC;AAED,SAAgB,iBAAiB,CAAC,KAAa;IAC7C,MAAM,IAAI,GAAG;QACX,KAAK;QACL,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,4DAA4D;QACrE,YAAY,EAAE,IAAI;QAClB,OAAO,EAAE;YACP,GAAG,EAAE,yBAAyB;SAC/B;QACD,cAAc,EAAE;YACd,OAAO,EAAE;gBACP,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,CAAC;gBACP,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,CAAC;aACX;YACD,UAAU,EAAE;gBACV,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,iCAAiC;aAC/C;SACF;KACF,CAAC;IACF,OAAO,qBAAM,CAAC,oBAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC;AAxBD,8CAwBC;AAED,SAAgB,aAAa,CAAC,KAAa;IACzC,MAAM,IAAI,GAAG;QACX,KAAK;QACL,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,yCAAyC;QAClD,YAAY,EAAE,IAAI;QAClB,OAAO,EAAE;YACP,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,WAAW,CAAC;YAChD,GAAG,EAAE;gBACH,sBAAsB,EAAE,aAAa;gBACrC,IAAI,EAAE,oDAAoD;aAC3D;SACF;QACD,cAAc,EAAE;YACd,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,CAAC;oBACP,QAAQ,EAAE,CAAC;oBACX,OAAO,EAAE,CAAC;iBACX;aACF;YACD,UAAU,EAAE;gBACV,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,IAAI;aAClB;SACF;KACF,CAAC;IACF,OAAO,qBAAM,CAAC,oBAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC;AA9BD,sCA8BC;AAED,SAAgB,gBAAgB,CAAC,KAAa;IAC5C,MAAM,IAAI,GAAG;QACX,KAAK;QACL,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,aAAa;QACtB,YAAY,EAAE,IAAI;QAClB,OAAO,EAAE;YACP,GAAG,EAAE,wBAAwB;SAC9B;QACD,cAAc,EAAE,YAAY;QAC5B,YAAY,EAAE;YACZ,MAAM,EAAE,QAAQ;SACjB;KACF,CAAC;IACF,OAAO,qBAAM,CAAC,oBAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC;AAfD,4CAeC;AAED,SAAgB,gBAAgB,CAAC,KAAa,EAAE,mBAA4B;IAC1E,mBAAmB,GAAG,mBAAmB,IAAI,YAAY,CAAC;IAC1D,MAAM,OAAO,GAAG,uDAAuD,mBAAmB,8DAA8D,CAAC;IACzJ,MAAM,IAAI,GAAG;QACX,KAAK;QACL,IAAI,EAAE,OAAO;QACb,OAAO;QACP,YAAY,EAAE,IAAI;QAClB,OAAO,EAAE;YACP,GAAG,EAAE,wBAAwB;YAC7B,GAAG,EAAE;gBACH,IAAI,EAAE,kDAAkD;aACzD;SACF;QACD,cAAc,EAAE;YACd,OAAO,EAAE;gBACP,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,CAAC;gBACP,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,CAAC;aACX;YACD,UAAU,EAAE;gBACV,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,4CAA4C;gBAC3D,WAAW,EACT,wFAAwF;aAC3F;SACF;QACD,YAAY,EAAE;YACZ,MAAM,EAAE,QAAQ;SACjB;KACF,CAAC;IACF,OAAO,qBAAM,CAAC,oBAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC;AAjCD,4CAiCC;AAED,SAAgB,YAAY,CAAC,KAAa,EAAE,mBAA4B;IACtE,MAAM,OAAO,GACX,mBAAmB,KAAK,YAAY;QAClC,CAAC,CAAC,4GAA4G;QAC9G,CAAC,CAAC,wFAAwF,CAAC;IAC/F,MAAM,IAAI,GAAG;QACX,KAAK;QACL,IAAI,EAAE,OAAO;QACb,OAAO;QACP,YAAY,EAAE,IAAI;QAClB,OAAO,EAAE;YACP,GAAG,EAAE,wBAAwB;SAC9B;QACD,cAAc,EAAE;YACd,OAAO,EAAE;gBACP;oBACE,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,CAAC;oBACP,QAAQ,EAAE,CAAC;oBACX,OAAO,EAAE,CAAC;iBACX;aACF;YACD,UAAU,EAAE;gBACV,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,oBAAoB;gBACnC,WAAW,EAAE,wEAAwE;aACtF;SACF;KACF,CAAC;IACF,OAAO,qBAAM,CAAC,oBAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC;AA9BD,oCA8BC"}