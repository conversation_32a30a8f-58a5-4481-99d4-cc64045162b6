import { DebugMigrationContext } from "./debugMigrationContext";
export declare function migrateTransparentPrerequisite(context: DebugMigrationContext): Promise<void>;
export declare function migrateTransparentLocalTunnel(context: DebugMigrationContext): Promise<void>;
export declare function migrateTransparentNpmInstall(context: DebugMigrationContext): Promise<void>;
export declare function migrateSetUpTab(context: DebugMigrationContext): Promise<void>;
export declare function migrateSetUpBot(context: DebugMigrationContext): Promise<void>;
export declare function migrateSetUpSSO(context: DebugMigrationContext): Promise<void>;
export declare function migratePrepareManifest(context: DebugMigrationContext): Promise<void>;
export declare function migrateValidateDependencies(context: DebugMigrationContext): Promise<void>;
export declare function migrateBackendExtensionsInstall(context: DebugMigrationContext): Promise<void>;
export declare function migrateFrontendStart(context: DebugMigrationContext): Promise<void>;
export declare function migrateAuthStart(context: DebugMigrationContext): Promise<void>;
export declare function migrateBotStart(context: DebugMigrationContext): Promise<void>;
export declare function migrateBackendWatch(context: DebugMigrationContext): Promise<void>;
export declare function migrateBackendStart(context: DebugMigrationContext): Promise<void>;
export declare function migrateValidateLocalPrerequisites(context: DebugMigrationContext): Promise<void>;
export declare function migratePreDebugCheck(context: DebugMigrationContext): Promise<void>;
export declare function migrateNgrokStartTask(context: DebugMigrationContext): Promise<void>;
export declare function migrateNgrokStartCommand(context: DebugMigrationContext): Promise<void>;
//# sourceMappingURL=taskMigrator.d.ts.map