"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.migrateNgrokStartCommand = exports.migrateNgrokStartTask = exports.migratePreDebugCheck = exports.migrateValidateLocalPrerequisites = exports.migrateBackendStart = exports.migrateBackendWatch = exports.migrateBotStart = exports.migrateAuthStart = exports.migrateFrontendStart = exports.migrateBackendExtensionsInstall = exports.migrateValidateDependencies = exports.migratePrepareManifest = exports.migrateSetUpSSO = exports.migrateSetUpBot = exports.migrateSetUpTab = exports.migrateTransparentNpmInstall = exports.migrateTransparentLocalTunnel = exports.migrateTransparentPrerequisite = void 0;
const tslib_1 = require("tslib");
const comment_json_1 = require("comment-json");
const local_1 = require("../../../../common/local");
const debugV3MigrationUtils_1 = require("./debugV3MigrationUtils");
const crypto_1 = require("../../../crypto");
const os = tslib_1.__importStar(require("os"));
const path = tslib_1.__importStar(require("path"));
async function migrateTransparentPrerequisite(context) {
    for (const task of context.tasks) {
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "teamsfx") ||
            !(task["command"] === local_1.TaskCommand.checkPrerequisites)) {
            continue;
        }
        if (debugV3MigrationUtils_1.isCommentObject(task["args"]) && debugV3MigrationUtils_1.isCommentArray(task["args"]["prerequisites"])) {
            const newPrerequisites = [];
            const toolsArgs = {};
            for (const prerequisite of task["args"]["prerequisites"]) {
                if (prerequisite === local_1.Prerequisite.nodejs) {
                    newPrerequisites.push(`"${local_1.Prerequisite.nodejs}", // Validate if Node.js is installed.`);
                }
                else if (prerequisite === local_1.Prerequisite.m365Account) {
                    newPrerequisites.push(`"${local_1.Prerequisite.m365Account}", // Sign-in prompt for Microsoft 365 account, then validate if the account enables the sideloading permission.`);
                }
                else if (prerequisite === local_1.Prerequisite.portOccupancy) {
                    newPrerequisites.push(`"${local_1.Prerequisite.portOccupancy}", // Validate available ports to ensure those debug ones are not occupied.`);
                }
                else if (prerequisite === local_1.Prerequisite.func) {
                    toolsArgs.func = true;
                }
                else if (prerequisite === local_1.Prerequisite.devCert) {
                    toolsArgs.devCert = { trust: true };
                }
                else if (prerequisite === local_1.Prerequisite.dotnet) {
                    toolsArgs.dotnet = true;
                }
            }
            task["args"]["prerequisites"] = comment_json_1.parse(`[
        ${newPrerequisites.join("\n  ")}
      ]`);
            if (Object.keys(toolsArgs).length > 0) {
                if (!context.appYmlConfig.deploy) {
                    context.appYmlConfig.deploy = {};
                }
                context.appYmlConfig.deploy.tools = toolsArgs;
            }
        }
    }
}
exports.migrateTransparentPrerequisite = migrateTransparentPrerequisite;
async function migrateTransparentLocalTunnel(context) {
    for (const task of context.tasks) {
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "teamsfx") ||
            !(task["command"] === local_1.TaskCommand.startLocalTunnel)) {
            continue;
        }
        if (debugV3MigrationUtils_1.isCommentObject(task["args"])) {
            const comment = `
        {
          // Keep consistency with upgraded configuration.
        }
      `;
            task["args"]["env"] = "local";
            task["args"]["output"] = comment_json_1.assign(comment_json_1.parse(comment), {
                endpoint: context.placeholderMapping.botEndpoint,
                domain: context.placeholderMapping.botDomain,
            });
        }
    }
}
exports.migrateTransparentLocalTunnel = migrateTransparentLocalTunnel;
async function migrateTransparentNpmInstall(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "teamsfx") ||
            !(task["command"] === local_1.TaskCommand.npmInstall)) {
            ++index;
            continue;
        }
        if (debugV3MigrationUtils_1.isCommentObject(task["args"]) && debugV3MigrationUtils_1.isCommentArray(task["args"]["projects"])) {
            for (const npmArgs of task["args"]["projects"]) {
                if (!debugV3MigrationUtils_1.isCommentObject(npmArgs) || !(typeof npmArgs["cwd"] === "string")) {
                    continue;
                }
                const npmInstallArg = { args: "install" };
                npmInstallArg.workingDirectory = npmArgs["cwd"].replace("${workspaceFolder}", ".");
                if (typeof npmArgs["npmInstallArgs"] === "string") {
                    npmInstallArg.args = `install ${npmArgs["npmInstallArgs"]}`;
                }
                else if (debugV3MigrationUtils_1.isCommentArray(npmArgs["npmInstallArgs"]) &&
                    npmArgs["npmInstallArgs"].length > 0) {
                    npmInstallArg.args = `install ${npmArgs["npmInstallArgs"].join(" ")}`;
                }
                if (!context.appYmlConfig.deploy) {
                    context.appYmlConfig.deploy = {};
                }
                if (!context.appYmlConfig.deploy.npmCommands) {
                    context.appYmlConfig.deploy.npmCommands = [];
                }
                context.appYmlConfig.deploy.npmCommands.push(npmInstallArg);
            }
        }
        if (typeof task["label"] === "string") {
            // TODO: remove preLaunchTask in launch.json
            replaceInDependsOn(task["label"], context.tasks);
        }
        context.tasks.splice(index, 1);
    }
}
exports.migrateTransparentNpmInstall = migrateTransparentNpmInstall;
async function migrateSetUpTab(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "teamsfx") ||
            !(task["command"] === local_1.TaskCommand.setUpTab)) {
            ++index;
            continue;
        }
        if (typeof task["label"] !== "string") {
            ++index;
            continue;
        }
        let url = new URL("https://localhost:53000");
        if (debugV3MigrationUtils_1.isCommentObject(task["args"]) && typeof task["args"]["baseUrl"] === "string") {
            try {
                url = new URL(task["args"]["baseUrl"]);
            }
            catch (_a) { }
        }
        if (!context.appYmlConfig.configureApp) {
            context.appYmlConfig.configureApp = {};
        }
        if (!context.appYmlConfig.configureApp.tab) {
            context.appYmlConfig.configureApp.tab = {};
        }
        context.appYmlConfig.configureApp.tab.domain = url.host;
        context.appYmlConfig.configureApp.tab.endpoint = url.origin;
        if (!context.appYmlConfig.deploy) {
            context.appYmlConfig.deploy = {};
        }
        if (!context.appYmlConfig.deploy.tab) {
            context.appYmlConfig.deploy.tab = {};
        }
        context.appYmlConfig.deploy.tab.port = parseInt(url.port);
        const label = task["label"];
        index = handleProvisionAndDeploy(context, index, label);
    }
}
exports.migrateSetUpTab = migrateSetUpTab;
async function migrateSetUpBot(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "teamsfx") ||
            !(task["command"] === local_1.TaskCommand.setUpBot)) {
            ++index;
            continue;
        }
        if (typeof task["label"] !== "string") {
            ++index;
            continue;
        }
        if (!context.appYmlConfig.provision) {
            context.appYmlConfig.provision = {};
        }
        context.appYmlConfig.provision.bot = {
            messagingEndpoint: `$\{{${context.placeholderMapping.botEndpoint}}}/api/messages`,
        };
        if (!context.appYmlConfig.deploy) {
            context.appYmlConfig.deploy = {};
        }
        context.appYmlConfig.deploy.bot = true;
        const envs = {};
        if (debugV3MigrationUtils_1.isCommentObject(task["args"])) {
            if (task["args"]["botId"] && typeof task["args"]["botId"] === "string") {
                envs["BOT_ID"] = task["args"]["botId"];
            }
            if (task["args"]["botPassword"] && typeof task["args"]["botPassword"] === "string") {
                const envReferencePattern = /^\$\{env:(.*)\}$/;
                const matchResult = task["args"]["botPassword"].match(envReferencePattern);
                const botPassword = matchResult ? process.env[matchResult[1]] : task["args"]["botPassword"];
                if (botPassword) {
                    const cryptoProvider = new crypto_1.LocalCrypto(context.oldProjectSettings.projectId);
                    const result = cryptoProvider.encrypt(botPassword);
                    if (result.isOk()) {
                        envs["SECRET_BOT_PASSWORD"] = result.value;
                    }
                }
            }
            if (task["args"]["botMessagingEndpoint"] &&
                typeof task["args"]["botMessagingEndpoint"] === "string") {
                if (task["args"]["botMessagingEndpoint"].startsWith("http")) {
                    context.appYmlConfig.provision.bot.messagingEndpoint =
                        task["args"]["botMessagingEndpoint"];
                }
                else if (task["args"]["botMessagingEndpoint"].startsWith("/")) {
                    context.appYmlConfig.provision.bot.messagingEndpoint = `$\{{${context.placeholderMapping.botEndpoint}}}${task["args"]["botMessagingEndpoint"]}`;
                }
            }
        }
        await debugV3MigrationUtils_1.updateLocalEnv(context.migrationContext, envs);
        const label = task["label"];
        index = handleProvisionAndDeploy(context, index, label);
    }
}
exports.migrateSetUpBot = migrateSetUpBot;
async function migrateSetUpSSO(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "teamsfx") ||
            !(task["command"] === local_1.TaskCommand.setUpSSO)) {
            ++index;
            continue;
        }
        if (typeof task["label"] !== "string") {
            ++index;
            continue;
        }
        if (!context.appYmlConfig.registerApp) {
            context.appYmlConfig.registerApp = {};
        }
        context.appYmlConfig.registerApp.aad = true;
        if (!context.appYmlConfig.configureApp) {
            context.appYmlConfig.configureApp = {};
        }
        context.appYmlConfig.configureApp.aad = true;
        if (!context.appYmlConfig.deploy) {
            context.appYmlConfig.deploy = {};
        }
        context.appYmlConfig.deploy.sso = true;
        const envs = {};
        if (debugV3MigrationUtils_1.isCommentObject(task["args"])) {
            if (task["args"]["objectId"] && typeof task["args"]["objectId"] === "string") {
                envs["AAD_APP_OBJECT_ID"] = task["args"]["objectId"];
            }
            if (task["args"]["clientId"] && typeof task["args"]["clientId"] === "string") {
                envs["AAD_APP_CLIENT_ID"] = task["args"]["clientId"];
            }
            if (task["args"]["clientSecret"] && typeof task["args"]["clientSecret"] === "string") {
                const envReferencePattern = /^\$\{env:(.*)\}$/;
                const matchResult = task["args"]["clientSecret"].match(envReferencePattern);
                const clientSecret = matchResult
                    ? process.env[matchResult[1]]
                    : task["args"]["clientSecret"];
                if (clientSecret) {
                    const cryptoProvider = new crypto_1.LocalCrypto(context.oldProjectSettings.projectId);
                    const result = cryptoProvider.encrypt(clientSecret);
                    if (result.isOk()) {
                        envs["SECRET_AAD_APP_CLIENT_SECRET"] = result.value;
                    }
                }
            }
            if (task["args"]["accessAsUserScopeId"] &&
                typeof task["args"]["accessAsUserScopeId"] === "string") {
                envs["AAD_APP_ACCESS_AS_USER_PERMISSION_ID"] = task["args"]["accessAsUserScopeId"];
            }
        }
        await debugV3MigrationUtils_1.updateLocalEnv(context.migrationContext, envs);
        const label = task["label"];
        index = handleProvisionAndDeploy(context, index, label);
    }
}
exports.migrateSetUpSSO = migrateSetUpSSO;
async function migratePrepareManifest(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "teamsfx") ||
            !(task["command"] === local_1.TaskCommand.prepareManifest)) {
            ++index;
            continue;
        }
        if (typeof task["label"] !== "string") {
            ++index;
            continue;
        }
        let appPackagePath = undefined;
        if (debugV3MigrationUtils_1.isCommentObject(task["args"]) && typeof task["args"]["appPackagePath"] === "string") {
            appPackagePath = task["args"]["appPackagePath"];
        }
        if (!appPackagePath) {
            if (!context.appYmlConfig.registerApp) {
                context.appYmlConfig.registerApp = {};
            }
            context.appYmlConfig.registerApp.teamsApp = true;
        }
        if (!context.appYmlConfig.configureApp) {
            context.appYmlConfig.configureApp = {};
        }
        if (!context.appYmlConfig.configureApp.teamsApp) {
            context.appYmlConfig.configureApp.teamsApp = {};
        }
        context.appYmlConfig.configureApp.teamsApp.appPackagePath = appPackagePath;
        const label = task["label"];
        index = handleProvisionAndDeploy(context, index, label);
    }
}
exports.migratePrepareManifest = migratePrepareManifest;
async function migrateValidateDependencies(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "shell") ||
            !(typeof task["command"] === "string") ||
            !task["command"].includes("${command:fx-extension.validate-dependencies}")) {
            ++index;
            continue;
        }
        const newTask = generatePrerequisiteTask(task, context);
        context.tasks.splice(index, 1, newTask);
        ++index;
        const toolsArgs = {};
        if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeTab(context.oldProjectSettings)) {
            toolsArgs.devCert = {
                trust: true,
            };
            if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeSSO(context.oldProjectSettings)) {
                toolsArgs.dotnet = true;
            }
        }
        if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeFunction(context.oldProjectSettings)) {
            toolsArgs.func = true;
            toolsArgs.dotnet = true;
        }
        if (Object.keys(toolsArgs).length > 0) {
            if (!context.appYmlConfig.deploy) {
                context.appYmlConfig.deploy = {};
            }
            context.appYmlConfig.deploy.tools = toolsArgs;
        }
    }
}
exports.migrateValidateDependencies = migrateValidateDependencies;
async function migrateBackendExtensionsInstall(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "shell") ||
            !(typeof task["command"] === "string" &&
                task["command"].includes("${command:fx-extension.backend-extensions-install}"))) {
            ++index;
            continue;
        }
        if (!context.appYmlConfig.deploy) {
            context.appYmlConfig.deploy = {};
        }
        context.appYmlConfig.deploy.dotnetCommand = {
            args: "build extensions.csproj -o ./bin --ignore-failed-sources",
            workingDirectory: `./${local_1.FolderName.Function}`,
            execPath: "${{DOTNET_PATH}}",
        };
        const label = task["label"];
        if (typeof label === "string") {
            replaceInDependsOn(label, context.tasks);
        }
        context.tasks.splice(index, 1);
    }
}
exports.migrateBackendExtensionsInstall = migrateBackendExtensionsInstall;
async function migrateFrontendStart(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (debugV3MigrationUtils_1.isCommentObject(task) &&
            ((typeof task["dependsOn"] === "string" && task["dependsOn"] === "teamsfx: frontend start") ||
                (debugV3MigrationUtils_1.isCommentArray(task["dependsOn"]) &&
                    task["dependsOn"].includes("teamsfx: frontend start")))) {
            const newLabel = debugV3MigrationUtils_1.generateLabel("Start frontend", getLabels(context.tasks));
            const newTask = debugV3MigrationUtils_1.startFrontendTask(newLabel);
            context.tasks.splice(index + 1, 0, newTask);
            replaceInDependsOn("teamsfx: frontend start", context.tasks, newLabel);
            if (!context.appYmlConfig.deploy) {
                context.appYmlConfig.deploy = {};
            }
            context.appYmlConfig.deploy.frontendStart = {
                sso: debugV3MigrationUtils_1.OldProjectSettingsHelper.includeSSO(context.oldProjectSettings),
                functionName: debugV3MigrationUtils_1.OldProjectSettingsHelper.getFunctionName(context.oldProjectSettings),
            };
            if (!context.appYmlConfig.deploy.npmCommands) {
                context.appYmlConfig.deploy.npmCommands = [];
            }
            const existing = context.appYmlConfig.deploy.npmCommands.find((value) => value.args === "install -D env-cmd");
            if (!existing) {
                context.appYmlConfig.deploy.npmCommands.push({
                    args: "install -D env-cmd",
                    workingDirectory: ".",
                });
            }
            break;
        }
        else {
            ++index;
        }
    }
}
exports.migrateFrontendStart = migrateFrontendStart;
async function migrateAuthStart(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (debugV3MigrationUtils_1.isCommentObject(task) &&
            ((typeof task["dependsOn"] === "string" && task["dependsOn"] === "teamsfx: auth start") ||
                (debugV3MigrationUtils_1.isCommentArray(task["dependsOn"]) && task["dependsOn"].includes("teamsfx: auth start")))) {
            const newLabel = debugV3MigrationUtils_1.generateLabel("Start auth", getLabels(context.tasks));
            const newTask = debugV3MigrationUtils_1.startAuthTask(newLabel);
            context.tasks.splice(index + 1, 0, newTask);
            replaceInDependsOn("teamsfx: auth start", context.tasks, newLabel);
            if (!context.appYmlConfig.deploy) {
                context.appYmlConfig.deploy = {};
            }
            context.appYmlConfig.deploy.authStart = {
                appsettingsPath: path.join(os.homedir(), ".fx", "localauth", "appsettings.Development.json"),
            };
            break;
        }
        else {
            ++index;
        }
    }
}
exports.migrateAuthStart = migrateAuthStart;
async function migrateBotStart(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (debugV3MigrationUtils_1.isCommentObject(task) &&
            ((typeof task["dependsOn"] === "string" && task["dependsOn"] === "teamsfx: bot start") ||
                (debugV3MigrationUtils_1.isCommentArray(task["dependsOn"]) && task["dependsOn"].includes("teamsfx: bot start")))) {
            const newLabel = debugV3MigrationUtils_1.generateLabel("Start bot", getLabels(context.tasks));
            const newTask = debugV3MigrationUtils_1.startBotTask(newLabel, context.oldProjectSettings.programmingLanguage);
            context.tasks.splice(index + 1, 0, newTask);
            replaceInDependsOn("teamsfx: bot start", context.tasks, newLabel);
            if (!context.appYmlConfig.deploy) {
                context.appYmlConfig.deploy = {};
            }
            context.appYmlConfig.deploy.botStart = {
                tab: debugV3MigrationUtils_1.OldProjectSettingsHelper.includeTab(context.oldProjectSettings),
                function: debugV3MigrationUtils_1.OldProjectSettingsHelper.includeFunction(context.oldProjectSettings),
                sso: debugV3MigrationUtils_1.OldProjectSettingsHelper.includeSSO(context.oldProjectSettings),
            };
            if (!context.appYmlConfig.deploy.npmCommands) {
                context.appYmlConfig.deploy.npmCommands = [];
            }
            const existing = context.appYmlConfig.deploy.npmCommands.find((value) => value.args === "install -D env-cmd");
            if (!existing) {
                context.appYmlConfig.deploy.npmCommands.push({
                    args: "install -D env-cmd",
                    workingDirectory: ".",
                });
            }
            break;
        }
        else {
            ++index;
        }
    }
}
exports.migrateBotStart = migrateBotStart;
async function migrateBackendWatch(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (debugV3MigrationUtils_1.isCommentObject(task) &&
            ((typeof task["dependsOn"] === "string" && task["dependsOn"] === "teamsfx: backend watch") ||
                (debugV3MigrationUtils_1.isCommentArray(task["dependsOn"]) && task["dependsOn"].includes("teamsfx: backend watch")))) {
            const newLabel = debugV3MigrationUtils_1.generateLabel("Watch backend", getLabels(context.tasks));
            const newTask = debugV3MigrationUtils_1.watchBackendTask(newLabel);
            context.tasks.splice(index + 1, 0, newTask);
            replaceInDependsOn("teamsfx: backend watch", context.tasks, newLabel);
            break;
        }
        else {
            ++index;
        }
    }
}
exports.migrateBackendWatch = migrateBackendWatch;
async function migrateBackendStart(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (debugV3MigrationUtils_1.isCommentObject(task) &&
            ((typeof task["dependsOn"] === "string" && task["dependsOn"] === "teamsfx: backend start") ||
                (debugV3MigrationUtils_1.isCommentArray(task["dependsOn"]) && task["dependsOn"].includes("teamsfx: backend start")))) {
            const newLabel = debugV3MigrationUtils_1.generateLabel("Start backend", getLabels(context.tasks));
            const newTask = debugV3MigrationUtils_1.startBackendTask(newLabel, context.oldProjectSettings.programmingLanguage);
            context.tasks.splice(index + 1, 0, newTask);
            replaceInDependsOn("teamsfx: backend start", context.tasks, newLabel);
            if (!context.appYmlConfig.deploy) {
                context.appYmlConfig.deploy = {};
            }
            context.appYmlConfig.deploy.backendStart = true;
            if (!context.appYmlConfig.deploy.npmCommands) {
                context.appYmlConfig.deploy.npmCommands = [];
            }
            const existing = context.appYmlConfig.deploy.npmCommands.find((value) => value.args === "install -D env-cmd");
            if (!existing) {
                context.appYmlConfig.deploy.npmCommands.push({
                    args: "install -D env-cmd",
                    workingDirectory: ".",
                });
            }
            break;
        }
        else {
            ++index;
        }
    }
}
exports.migrateBackendStart = migrateBackendStart;
async function migrateValidateLocalPrerequisites(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "shell") ||
            !(typeof task["command"] === "string" &&
                task["command"].includes("${command:fx-extension.validate-local-prerequisites}"))) {
            ++index;
            continue;
        }
        const newTask = generatePrerequisiteTask(task, context);
        context.tasks.splice(index, 1, newTask);
        ++index;
        const toolsArgs = {};
        const npmCommands = [];
        let dotnetCommand;
        if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeTab(context.oldProjectSettings)) {
            toolsArgs.devCert = {
                trust: true,
            };
            npmCommands.push({
                args: `install ${local_1.defaultNpmInstallArg}`,
                workingDirectory: `./${local_1.FolderName.Frontend}`,
            });
        }
        if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeFunction(context.oldProjectSettings)) {
            toolsArgs.func = true;
            toolsArgs.dotnet = true;
            npmCommands.push({
                args: `install ${local_1.defaultNpmInstallArg}`,
                workingDirectory: `./${local_1.FolderName.Function}`,
            });
            dotnetCommand = {
                args: "build extensions.csproj -o ./bin --ignore-failed-sources",
                workingDirectory: `./${local_1.FolderName.Function}`,
                execPath: "${{DOTNET_PATH}}",
            };
        }
        if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeBot(context.oldProjectSettings)) {
            if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeFuncHostedBot(context.oldProjectSettings)) {
                toolsArgs.func = true;
            }
            npmCommands.push({
                args: `install ${local_1.defaultNpmInstallArg}`,
                workingDirectory: `./${local_1.FolderName.Bot}`,
            });
        }
        if (Object.keys(toolsArgs).length > 0 || npmCommands.length > 0 || dotnetCommand) {
            if (!context.appYmlConfig.deploy) {
                context.appYmlConfig.deploy = {};
            }
            if (Object.keys(toolsArgs).length > 0) {
                context.appYmlConfig.deploy.tools = toolsArgs;
            }
            if (npmCommands.length > 0) {
                context.appYmlConfig.deploy.npmCommands = npmCommands;
            }
            context.appYmlConfig.deploy.dotnetCommand = dotnetCommand;
        }
    }
}
exports.migrateValidateLocalPrerequisites = migrateValidateLocalPrerequisites;
async function migratePreDebugCheck(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "shell") ||
            !(typeof task["command"] === "string" &&
                task["command"].includes("${command:fx-extension.pre-debug-check}"))) {
            ++index;
            continue;
        }
        if (!context.appYmlConfig.registerApp) {
            context.appYmlConfig.registerApp = {};
        }
        if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeSSO(context.oldProjectSettings)) {
            context.appYmlConfig.registerApp.aad = true;
        }
        context.appYmlConfig.registerApp.teamsApp = true;
        if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeBot(context.oldProjectSettings)) {
            if (!context.appYmlConfig.provision) {
                context.appYmlConfig.provision = {};
            }
            context.appYmlConfig.provision.bot = {
                messagingEndpoint: `$\{{${context.placeholderMapping.botEndpoint}}}/api/messages`,
            };
        }
        if (!context.appYmlConfig.configureApp) {
            context.appYmlConfig.configureApp = {};
        }
        if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeTab(context.oldProjectSettings)) {
            context.appYmlConfig.configureApp.tab = {
                domain: "localhost:53000",
                endpoint: "https://localhost:53000",
            };
        }
        if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeSSO(context.oldProjectSettings)) {
            context.appYmlConfig.configureApp.aad = true;
        }
        if (!context.appYmlConfig.configureApp.teamsApp) {
            context.appYmlConfig.configureApp.teamsApp = {};
        }
        const validateLocalPrerequisitesTask = context.tasks.find((_task) => debugV3MigrationUtils_1.isCommentObject(_task) &&
            _task["type"] === "shell" &&
            typeof _task["command"] === "string" &&
            _task["command"].includes("${command:fx-extension.validate-local-prerequisites}"));
        if (validateLocalPrerequisitesTask) {
            if (!context.appYmlConfig.deploy) {
                context.appYmlConfig.deploy = {};
            }
            if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeTab(context.oldProjectSettings)) {
                context.appYmlConfig.deploy.tab = {
                    port: 53000,
                };
            }
            if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeBot(context.oldProjectSettings)) {
                context.appYmlConfig.deploy.bot = true;
            }
            if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeSSO(context.oldProjectSettings)) {
                context.appYmlConfig.deploy.sso = true;
            }
        }
        const existingLabels = getLabels(context.tasks);
        const createResourcesLabel = debugV3MigrationUtils_1.generateLabel("Create resources", existingLabels);
        const setUpLocalProjectsLabel = debugV3MigrationUtils_1.generateLabel("Build project", existingLabels);
        task["dependsOn"] = new comment_json_1.CommentArray(createResourcesLabel, setUpLocalProjectsLabel);
        task["dependsOrder"] = "sequence";
        const createResources = debugV3MigrationUtils_1.createResourcesTask(createResourcesLabel);
        context.tasks.splice(index + 1, 0, createResources);
        const setUpLocalProjects = debugV3MigrationUtils_1.setUpLocalProjectsTask(setUpLocalProjectsLabel);
        context.tasks.splice(index + 2, 0, setUpLocalProjects);
        delete task["type"];
        delete task["command"];
        delete task["presentation"];
        break;
    }
}
exports.migratePreDebugCheck = migratePreDebugCheck;
async function migrateNgrokStartTask(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (debugV3MigrationUtils_1.isCommentObject(task) &&
            ((typeof task["dependsOn"] === "string" && task["dependsOn"] === "teamsfx: ngrok start") ||
                (debugV3MigrationUtils_1.isCommentArray(task["dependsOn"]) && task["dependsOn"].includes("teamsfx: ngrok start")))) {
            const newTask = generateLocalTunnelTask(context);
            context.tasks.splice(index + 1, 0, newTask);
            break;
        }
        else {
            ++index;
        }
    }
    replaceInDependsOn("teamsfx: ngrok start", context.tasks, local_1.TaskLabel.StartLocalTunnel);
}
exports.migrateNgrokStartTask = migrateNgrokStartTask;
async function migrateNgrokStartCommand(context) {
    let index = 0;
    while (index < context.tasks.length) {
        const task = context.tasks[index];
        if (!debugV3MigrationUtils_1.isCommentObject(task) ||
            !(task["type"] === "teamsfx") ||
            !(task["command"] === "ngrok start")) {
            ++index;
            continue;
        }
        const newTask = generateLocalTunnelTask(context, task);
        context.tasks.splice(index, 1, newTask);
        ++index;
    }
}
exports.migrateNgrokStartCommand = migrateNgrokStartCommand;
function generatePrerequisiteTask(task, context) {
    const comment = `{
    // Check if all required prerequisites are installed and will install them if not.
    // See https://aka.ms/teamsfx-check-prerequisites-task to know the details and how to customize the args.
  }`;
    const newTask = comment_json_1.assign(comment_json_1.parse(comment), task);
    newTask["type"] = "teamsfx";
    newTask["command"] = "debug-check-prerequisites";
    const prerequisites = [
        `"${local_1.Prerequisite.nodejs}", // Validate if Node.js is installed.`,
        `"${local_1.Prerequisite.m365Account}", // Sign-in prompt for Microsoft 365 account, then validate if the account enables the sideloading permission.`,
        `"${local_1.Prerequisite.portOccupancy}", // Validate available ports to ensure those debug ones are not occupied.`,
    ];
    const prerequisitesComment = `
  [
    ${prerequisites.join("\n  ")}
  ]`;
    const ports = [];
    if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeTab(context.oldProjectSettings)) {
        ports.push(`${local_1.TaskDefaultValue.checkPrerequisites.ports.tabService}, // tab service port`);
    }
    if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeBot(context.oldProjectSettings)) {
        ports.push(`${local_1.TaskDefaultValue.checkPrerequisites.ports.botService}, // bot service port`);
        ports.push(`${local_1.TaskDefaultValue.checkPrerequisites.ports.botDebug}, // bot inspector port for Node.js debugger`);
    }
    if (debugV3MigrationUtils_1.OldProjectSettingsHelper.includeFunction(context.oldProjectSettings)) {
        ports.push(`${local_1.TaskDefaultValue.checkPrerequisites.ports.backendService}, // backend service port`);
        ports.push(`${local_1.TaskDefaultValue.checkPrerequisites.ports.backendDebug}, // backend inspector port for Node.js debugger`);
    }
    const portsComment = `
  [
    ${ports.join("\n  ")}
  ]
  `;
    const args = {
        prerequisites: comment_json_1.parse(prerequisitesComment),
        portOccupancy: comment_json_1.parse(portsComment),
    };
    newTask["args"] = args;
    return newTask;
}
function generateLocalTunnelTask(context, task) {
    const comment = `{
      // Start the local tunnel service to forward public ngrok URL to local port and inspect traffic.
      // See https://aka.ms/teamsfx-local-tunnel-task for the detailed args definitions,
      // as well as samples to:
      //   - use your own ngrok command / configuration / binary
      //   - use your own tunnel solution
      //   - provide alternatives if ngrok does not work on your dev machine
    }`;
    const placeholderComment = `
    {
      // Keep consistency with upgraded configuration.
    }
  `;
    const newTask = comment_json_1.assign(task !== null && task !== void 0 ? task : comment_json_1.parse(`{"label": "${local_1.TaskLabel.StartLocalTunnel}"}`), {
        type: "teamsfx",
        command: local_1.TaskCommand.startLocalTunnel,
        args: {
            ngrokArgs: local_1.TaskDefaultValue.startLocalTunnel.ngrokArgs,
            env: "local",
            output: comment_json_1.assign(comment_json_1.parse(placeholderComment), {
                endpoint: context.placeholderMapping.botEndpoint,
                domain: context.placeholderMapping.botDomain,
            }),
        },
        isBackground: true,
        problemMatcher: "$teamsfx-local-tunnel-watch",
    });
    return comment_json_1.assign(comment_json_1.parse(comment), newTask);
}
function handleProvisionAndDeploy(context, index, label) {
    context.tasks.splice(index, 1);
    const existingLabels = getLabels(context.tasks);
    const generatedBefore = context.generatedLabels.find((value) => value.startsWith("Create resources"));
    const createResourcesLabel = generatedBefore || debugV3MigrationUtils_1.generateLabel("Create resources", existingLabels);
    const setUpLocalProjectsLabel = context.generatedLabels.find((value) => value.startsWith("Build project")) ||
        debugV3MigrationUtils_1.generateLabel("Build project", existingLabels);
    if (!generatedBefore) {
        context.generatedLabels.push(createResourcesLabel);
        const createResources = debugV3MigrationUtils_1.createResourcesTask(createResourcesLabel);
        context.tasks.splice(index, 0, createResources);
        ++index;
        context.generatedLabels.push(setUpLocalProjectsLabel);
        const setUpLocalProjects = debugV3MigrationUtils_1.setUpLocalProjectsTask(setUpLocalProjectsLabel);
        context.tasks.splice(index, 0, setUpLocalProjects);
        ++index;
    }
    replaceInDependsOn(label, context.tasks, createResourcesLabel, setUpLocalProjectsLabel);
    return index;
}
function replaceInDependsOn(label, tasks, ...replacements) {
    for (const task of tasks) {
        if (debugV3MigrationUtils_1.isCommentObject(task) && task["dependsOn"]) {
            if (typeof task["dependsOn"] === "string") {
                if (task["dependsOn"] === label) {
                    if (replacements.length > 0) {
                        task["dependsOn"] = new comment_json_1.CommentArray(...replacements);
                    }
                    else {
                        delete task["dependsOn"];
                    }
                }
            }
            else if (Array.isArray(task["dependsOn"])) {
                const index = task["dependsOn"].findIndex((value) => value === label);
                if (index !== -1) {
                    if (replacements.length > 0 && !task["dependsOn"].includes(replacements[0])) {
                        task["dependsOn"].splice(index, 1, ...replacements);
                    }
                    else {
                        task["dependsOn"].splice(index, 1);
                    }
                }
            }
        }
    }
}
function getLabels(tasks) {
    const labels = [];
    for (const task of tasks) {
        if (debugV3MigrationUtils_1.isCommentObject(task) && typeof task["label"] === "string") {
            labels.push(task["label"]);
        }
    }
    return labels;
}
//# sourceMappingURL=taskMigrator.js.map