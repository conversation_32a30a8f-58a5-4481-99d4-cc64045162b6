/// <reference types="node" />
import { CopyOptions, EnsureOptions, PathLike, WriteFileOptions } from "fs-extra";
import { CoreHookContext } from "../../types";
export declare const backupFolder = ".backup";
export interface MigrationContext extends CoreHookContext {
    backup(path: string): Promise<boolean>;
    fsEnsureDir(path: string, options?: EnsureOptions | number): Promise<void>;
    fsCopy(src: string, dest: string, options?: CopyOptions): Promise<void>;
    fsCreateFile(file: string): Promise<void>;
    fsWriteFile(file: PathLike | number, data: any, options?: WriteFileOptions | string): Promise<void>;
    addReport(report: string): void;
    addTelemetryProperties(properties: Record<string, string>): void;
}
export declare class MigrationContext {
    private modifiedPaths;
    private reports;
    telemetryProperties: Record<string, string>;
    backupPath: string;
    projectPath: string;
    static create(ctx: CoreHookContext): Promise<MigrationContext>;
    private constructor();
    addModifiedPath(path: string): void;
    getModifiedPaths(): string[];
    cleanModifiedPaths(): Promise<void>;
    restoreBackup(): Promise<void>;
    cleanBackup(): Promise<void>;
    removeFxV2(): Promise<void>;
    fsPathExists(_path: string): Promise<boolean>;
    fsRemove(_path: string): Promise<void>;
}
//# sourceMappingURL=migrationContext.d.ts.map