"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrationContext = exports.backupFolder = void 0;
const tslib_1 = require("tslib");
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const path_1 = tslib_1.__importDefault(require("path"));
const versionMetadata_1 = require("../../../common/versionMetadata");
const v3MigrationUtils_1 = require("./v3MigrationUtils");
exports.backupFolder = ".backup";
class MigrationContext {
    constructor(ctx) {
        this.modifiedPaths = [];
        this.reports = [];
        this.telemetryProperties = {};
        this.backupPath = "";
        this.projectPath = "";
        Object.assign(this, ctx, {});
        this.projectPath = v3MigrationUtils_1.getParameterFromCxt(ctx, "projectPath");
        this.backupPath = path_1.default.join(this.projectPath, exports.backupFolder);
    }
    static async create(ctx) {
        const context = new MigrationContext(ctx);
        await fs_extra_1.default.ensureDir(context.backupPath);
        return context;
    }
    async backup(_path) {
        const srcPath = path_1.default.join(this.projectPath, _path);
        if (await fs_extra_1.default.pathExists(srcPath)) {
            await fs_extra_1.default.copy(srcPath, path_1.default.join(this.backupPath, _path));
            return true;
        }
        return false;
    }
    async fsEnsureDir(_path, options) {
        const srcPath = path_1.default.join(this.projectPath, _path);
        const parentPath = path_1.default.dirname(srcPath);
        if (!(await fs_extra_1.default.pathExists(parentPath))) {
            await this.fsEnsureDir(path_1.default.relative(this.projectPath, parentPath), options);
        }
        if (!(await fs_extra_1.default.pathExists(srcPath))) {
            await fs_extra_1.default.ensureDir(srcPath, options);
            this.addModifiedPath(_path);
        }
    }
    async fsCopy(src, dest, options) {
        await fs_extra_1.default.copy(path_1.default.join(this.projectPath, src), path_1.default.join(this.projectPath, dest), options);
        this.addModifiedPath(dest);
    }
    async fsCreateFile(file) {
        await fs_extra_1.default.createFile(path_1.default.join(this.projectPath, file));
        this.addModifiedPath(file);
    }
    async fsWriteFile(file, data, options) {
        await fs_extra_1.default.writeFile(path_1.default.join(this.projectPath, file), data, options);
        this.addModifiedPath(file);
    }
    addModifiedPath(path) {
        if (!this.modifiedPaths.includes(path)) {
            this.modifiedPaths.push(path);
        }
    }
    getModifiedPaths() {
        return this.modifiedPaths.slice();
    }
    async cleanModifiedPaths() {
        for (const modifiedPath of this.modifiedPaths.reverse()) {
            await this.fsRemove(modifiedPath);
        }
        this.modifiedPaths.length = 0;
    }
    async restoreBackup() {
        const paths = await fs_extra_1.default.readdir(this.backupPath);
        await Promise.all(paths.map(async (_path) => {
            await fs_extra_1.default.copy(path_1.default.join(this.backupPath, _path), path_1.default.join(this.projectPath, _path));
        }));
    }
    async cleanBackup() {
        await this.fsRemove(exports.backupFolder);
    }
    async removeFxV2() {
        await this.fsRemove(versionMetadata_1.MetadataV2.configFolder);
    }
    async fsPathExists(_path) {
        return await fs_extra_1.default.pathExists(path_1.default.join(this.projectPath, _path));
    }
    async fsRemove(_path) {
        return await fs_extra_1.default.remove(path_1.default.join(this.projectPath, _path));
    }
    addReport(report) {
        this.reports.push(report);
    }
    addTelemetryProperties(properties) {
        this.telemetryProperties = Object.assign(Object.assign({}, properties), this.telemetryProperties);
    }
}
exports.MigrationContext = MigrationContext;
//# sourceMappingURL=migrationContext.js.map