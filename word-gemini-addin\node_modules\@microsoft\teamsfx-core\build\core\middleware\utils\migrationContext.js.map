{"version": 3, "file": "migrationContext.js", "sourceRoot": "", "sources": ["../../../../src/core/middleware/utils/migrationContext.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAGlC,gEAAsF;AACtF,wDAAwB;AACxB,qEAA6D;AAE7D,yDAAyD;AAE5C,QAAA,YAAY,GAAG,SAAS,CAAC;AAetC,MAAa,gBAAgB;IAa3B,YAAoB,GAAoB;QAZhC,kBAAa,GAAa,EAAE,CAAC;QAC7B,YAAO,GAAa,EAAE,CAAC;QAC/B,wBAAmB,GAA2B,EAAE,CAAC;QACjD,eAAU,GAAG,EAAE,CAAC;QAChB,gBAAW,GAAG,EAAE,CAAC;QASf,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;QAC7B,IAAI,CAAC,WAAW,GAAG,sCAAmB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;QAC3D,IAAI,CAAC,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,oBAAY,CAAC,CAAC;IAC9D,CAAC;IAVD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAoB;QACtC,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,kBAAE,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACvC,OAAO,OAAO,CAAC;IACjB,CAAC;IAQD,KAAK,CAAC,MAAM,CAAC,KAAa;QACxB,MAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACnD,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,kBAAE,CAAC,IAAI,CAAC,OAAO,EAAE,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAa,EAAE,OAAgC;QAC/D,MAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE;YACtC,MAAM,IAAI,CAAC,WAAW,CAAC,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;SAC9E;QACD,IAAI,CAAC,CAAC,MAAM,kBAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE;YACnC,MAAM,kBAAE,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACrC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SAC7B;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,GAAW,EAAE,IAAY,EAAE,OAAqB;QAC3D,MAAM,kBAAE,CAAC,IAAI,CAAC,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;QAC5F,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,IAAY;QAC7B,MAAM,kBAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,IAAS,EAAE,OAAmC;QAC5E,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACrE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,eAAe,CAAC,IAAY;QAC1B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC/B;IACH,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE;YACvD,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;SACnC;QACD,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,MAAM,OAAO,CAAC,GAAG,CACf,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACxB,MAAM,kBAAE,CAAC,IAAI,CAAC,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;QACvF,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,IAAI,CAAC,QAAQ,CAAC,oBAAY,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,QAAQ,CAAC,4BAAU,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAa;QAC9B,OAAO,MAAM,kBAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,KAAa;QAC1B,OAAO,MAAM,kBAAE,CAAC,MAAM,CAAC,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED,SAAS,CAAC,MAAc;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED,sBAAsB,CAAC,UAAkC;QACvD,IAAI,CAAC,mBAAmB,mCAAQ,UAAU,GAAK,IAAI,CAAC,mBAAmB,CAAE,CAAC;IAC5E,CAAC;CACF;AAxGD,4CAwGC"}