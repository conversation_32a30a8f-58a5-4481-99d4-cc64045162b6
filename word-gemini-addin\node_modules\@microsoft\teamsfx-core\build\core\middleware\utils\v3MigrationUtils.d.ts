import { MigrationContext } from "./migrationContext";
import { FileType } from "./MigrationUtils";
import { Platform, ProjectSettings } from "@microsoft/teamsfx-api";
import { CoreHookContext } from "../../types";
import { VersionInfo, VersionState } from "../../../common/versionMetadata";
import { VersionForMigration } from "../types";
export declare function readJsonFile(context: MigrationContext, filePath: string): Promise<any>;
export declare function readBicepContent(context: MigrationContext): Promise<any>;
export declare function getTemplateFolderPath(context: MigrationContext): string;
export declare function fsReadDirSync(context: MigrationContext, _path: string): string[];
export declare function jsonObjectNamesConvertV3(obj: any, prefix: string, parentKeyName: string, filetype: FileType, bicepContent: any): string;
export declare function getProjectVersion(ctx: CoreHookContext): Promise<VersionInfo>;
export declare function migrationNotificationMessage(versionForMigration: VersionForMigration): string;
export declare function getDownloadLinkByVersionAndPlatform(version: string, platform: Platform): string;
export declare function outputCancelMessage(version: string, platform: Platform): void;
export declare function getProjectVersionFromPath(projectPath: string): Promise<VersionInfo>;
export declare function getTrackingIdFromPath(projectPath: string): Promise<string>;
export declare function getVersionState(info: VersionInfo): VersionState;
export declare function getParameterFromCxt(ctx: CoreHookContext, key: string, defaultValue?: string): string;
export declare function getToolkitVersionLink(platform: Platform, projectVersion: string): string;
export declare function getCapabilitySsoStatus(projectSettings: ProjectSettings): {
    TabSso: boolean;
    BotSso: boolean;
};
export declare function generateAppIdUri(capabilities: {
    TabSso: boolean;
    BotSso: boolean;
}): string;
export declare function replaceAppIdUri(manifest: string, appIdUri: string): string;
export declare function readAndConvertUserdata(context: MigrationContext, filePath: string, bicepContent: any): Promise<string>;
export declare function updateAndSaveManifestForSpfx(context: MigrationContext, manifest: string): Promise<void>;
//# sourceMappingURL=v3MigrationUtils.d.ts.map