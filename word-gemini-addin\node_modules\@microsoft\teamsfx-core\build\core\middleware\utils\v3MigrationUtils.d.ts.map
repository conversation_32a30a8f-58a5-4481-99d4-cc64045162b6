{"version": 3, "file": "v3MigrationUtils.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/middleware/utils/v3MigrationUtils.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD,OAAO,EAAE,QAAQ,EAAqB,MAAM,kBAAkB,CAAC;AAE/D,OAAO,EAIL,QAAQ,EACR,eAAe,EAEhB,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAG9C,OAAO,EAKL,WAAW,EAEX,YAAY,EACb,MAAM,iCAAiC,CAAC;AACzC,OAAO,EAAE,mBAAmB,EAAE,MAAM,UAAU,CAAC;AAM/C,wBAAsB,YAAY,CAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAM5F;AAGD,wBAAsB,gBAAgB,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,CAM9E;AAGD,wBAAgB,qBAAqB,CAAC,OAAO,EAAE,gBAAgB,GAAG,MAAM,CAGvE;AAGD,wBAAgB,aAAa,CAAC,OAAO,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,CAGhF;AAWD,wBAAgB,wBAAwB,CACtC,GAAG,EAAE,GAAG,EACR,MAAM,EAAE,MAAM,EACd,aAAa,EAAE,MAAM,EACrB,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,GAAG,GAChB,MAAM,CAoBR;AAED,wBAAsB,iBAAiB,CAAC,GAAG,EAAE,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,CAGlF;AAED,wBAAgB,4BAA4B,CAAC,mBAAmB,EAAE,mBAAmB,GAAG,MAAM,CAS7F;AAED,wBAAgB,mCAAmC,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,MAAM,CAQ/F;AAED,wBAAgB,mBAAmB,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI,CAyB7E;AAED,wBAAsB,yBAAyB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,CAoCzF;AAED,wBAAsB,qBAAqB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAkBhF;AAED,wBAAgB,eAAe,CAAC,IAAI,EAAE,WAAW,GAAG,YAAY,CAW/D;AAED,wBAAgB,mBAAmB,CACjC,GAAG,EAAE,eAAe,EACpB,GAAG,EAAE,MAAM,EACX,YAAY,CAAC,EAAE,MAAM,GACpB,MAAM,CAIR;AAED,wBAAgB,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,GAAG,MAAM,CAExF;AAED,wBAAgB,sBAAsB,CAAC,eAAe,EAAE,eAAe,GAAG;IACxE,MAAM,EAAE,OAAO,CAAC;IAChB,MAAM,EAAE,OAAO,CAAC;CACjB,CAoBA;AAED,wBAAgB,gBAAgB,CAAC,YAAY,EAAE;IAAE,MAAM,EAAE,OAAO,CAAC;IAAC,MAAM,EAAE,OAAO,CAAA;CAAE,GAAG,MAAM,CAU3F;AAED,wBAAgB,eAAe,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM,CAO1E;AAED,wBAAsB,sBAAsB,CAC1C,OAAO,EAAE,gBAAgB,EACzB,QAAQ,EAAE,MAAM,EAChB,YAAY,EAAE,GAAG,GAChB,OAAO,CAAC,MAAM,CAAC,CAejB;AAED,wBAAsB,4BAA4B,CAChD,OAAO,EAAE,gBAAgB,EACzB,QAAQ,EAAE,MAAM,GACf,OAAO,CAAC,IAAI,CAAC,CAqCf"}