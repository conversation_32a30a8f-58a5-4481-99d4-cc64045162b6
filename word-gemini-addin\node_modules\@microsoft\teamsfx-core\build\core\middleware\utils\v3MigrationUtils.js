"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateAndSaveManifestForSpfx = exports.readAndConvertUserdata = exports.replaceAppIdUri = exports.generateAppIdUri = exports.getCapabilitySsoStatus = exports.getToolkitVersionLink = exports.getParameterFromCxt = exports.getVersionState = exports.getTrackingIdFromPath = exports.getProjectVersionFromPath = exports.outputCancelMessage = exports.getDownloadLinkByVersionAndPlatform = exports.migrationNotificationMessage = exports.getProjectVersion = exports.jsonObjectNamesConvertV3 = exports.fsReadDirSync = exports.getTemplateFolderPath = exports.readBicepContent = exports.readJsonFile = void 0;
const tslib_1 = require("tslib");
const path_1 = tslib_1.__importDefault(require("path"));
const fs_extra_1 = tslib_1.__importDefault(require("fs-extra"));
const lodash_1 = require("lodash");
const MigrationUtils_1 = require("./MigrationUtils");
const os_1 = require("os");
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const semver_1 = tslib_1.__importDefault(require("semver"));
const projectSettingsLoader_1 = require("../projectSettingsLoader");
const versionMetadata_1 = require("../../../common/versionMetadata");
const localizeUtils_1 = require("../../../common/localizeUtils");
const globalVars_1 = require("../../globalVars");
const settingsUtil_1 = require("../../../component/utils/settingsUtil");
// read json files in states/ folder
async function readJsonFile(context, filePath) {
    const filepath = path_1.default.join(context.projectPath, filePath);
    if (await fs_extra_1.default.pathExists(filepath)) {
        const obj = fs_extra_1.default.readJson(filepath);
        return obj;
    }
}
exports.readJsonFile = readJsonFile;
// read bicep file content
async function readBicepContent(context) {
    const bicepFilePath = path_1.default.join(getTemplateFolderPath(context), "azure", "provision.bicep");
    const bicepFileExists = await context.fsPathExists(bicepFilePath);
    return bicepFileExists
        ? fs_extra_1.default.readFileSync(path_1.default.join(context.projectPath, bicepFilePath), "utf8")
        : "";
}
exports.readBicepContent = readBicepContent;
// get template folder path
function getTemplateFolderPath(context) {
    const inputs = context.arguments[context.arguments.length - 1];
    return inputs.platform === teamsfx_api_1.Platform.VS ? "Templates" : "templates";
}
exports.getTemplateFolderPath = getTemplateFolderPath;
// read file names list under the given path
function fsReadDirSync(context, _path) {
    const dirPath = path_1.default.join(context.projectPath, _path);
    return fs_extra_1.default.readdirSync(dirPath);
}
exports.fsReadDirSync = fsReadDirSync;
// env variables in this list will be only convert into .env.{env} when migrating {env}.userdata
const skipList = [
    "state.fx-resource-aad-app-for-teams.clientSecret",
    "state.fx-resource-bot.botPassword",
    "state.fx-resource-apim.apimClientAADClientSecret",
    "state.fx-resource-azure-sql.adminPassword",
];
// convert any obj names if can be converted (used in states and configs migration)
function jsonObjectNamesConvertV3(obj, prefix, parentKeyName, filetype, bicepContent) {
    let returnData = "";
    if (lodash_1.isObject(obj)) {
        for (const keyName of Object.keys(obj)) {
            returnData +=
                parentKeyName === ""
                    ? jsonObjectNamesConvertV3(obj[keyName], prefix, prefix + keyName, filetype, bicepContent)
                    : jsonObjectNamesConvertV3(obj[keyName], prefix, parentKeyName + "." + keyName, filetype, bicepContent);
        }
    }
    else if (!skipList.includes(parentKeyName)) {
        const res = MigrationUtils_1.namingConverterV3(parentKeyName, filetype, bicepContent);
        if (res.isOk())
            return res.value + "=" + obj + os_1.EOL;
    }
    else
        return "";
    return returnData;
}
exports.jsonObjectNamesConvertV3 = jsonObjectNamesConvertV3;
async function getProjectVersion(ctx) {
    const projectPath = getParameterFromCxt(ctx, "projectPath", "");
    return await getProjectVersionFromPath(projectPath);
}
exports.getProjectVersion = getProjectVersion;
function migrationNotificationMessage(versionForMigration) {
    if (versionForMigration.platform === teamsfx_api_1.Platform.VS) {
        return localizeUtils_1.getLocalizedString("core.migrationV3.VS.Message", "Visual Studio 2022 17.5 Preview");
    }
    const res = localizeUtils_1.getLocalizedString("core.migrationV3.Message", versionMetadata_1.MetadataV2.platformVersion[versionForMigration.platform]);
    return res;
}
exports.migrationNotificationMessage = migrationNotificationMessage;
function getDownloadLinkByVersionAndPlatform(version, platform) {
    let anchorInLink = "vscode";
    if (platform === teamsfx_api_1.Platform.VS) {
        anchorInLink = "visual-studio";
    }
    else if (platform === teamsfx_api_1.Platform.CLI) {
        anchorInLink = "cli";
    }
    return `${versionMetadata_1.Metadata.versionMatchLink}#${anchorInLink}`;
}
exports.getDownloadLinkByVersionAndPlatform = getDownloadLinkByVersionAndPlatform;
function outputCancelMessage(version, platform) {
    globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Upgrade cancelled.`);
    const link = getDownloadLinkByVersionAndPlatform(version, platform);
    if (platform === teamsfx_api_1.Platform.VSCode) {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Notice upgrade to new configuration files is a must-have to continue to use current version Teams Toolkit. If you want to upgrade, please run command (Teams: Upgrade project) or click the “Upgrade project” button on tree view to trigger the upgrade.`);
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core]If you are not ready to upgrade and want to continue to use the old version Teams Toolkit ${versionMetadata_1.MetadataV2.platformVersion[platform]}, please find it in ${link} and install it.`);
    }
    else if (platform === teamsfx_api_1.Platform.VS) {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Notice upgrade to new configuration files is a must-have to continue to use current version Teams Toolkit. If you want to upgrade, please trigger this command again.`);
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core]If you are not ready to upgrade and want to continue to use the old version Teams Toolkit ${versionMetadata_1.MetadataV2.platformVersion[platform]}, please find it in ${link} and install it.`);
    }
    else {
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core] Notice upgrade to new configuration files is a must-have to continue to use current version Teams Toolkit CLI. If you want to upgrade, please trigger this command again.`);
        globalVars_1.TOOLS === null || globalVars_1.TOOLS === void 0 ? void 0 : globalVars_1.TOOLS.logProvider.warning(`[core]If you are not ready to upgrade and want to continue to use the old version Teams Toolkit CLI ${versionMetadata_1.MetadataV2.platformVersion[platform]}, please find it in ${link} and install it.`);
    }
}
exports.outputCancelMessage = outputCancelMessage;
async function getProjectVersionFromPath(projectPath) {
    const v3path = projectSettingsLoader_1.getProjectSettingPathV3(projectPath);
    if (await fs_extra_1.default.pathExists(v3path)) {
        const readSettingsResult = await settingsUtil_1.settingsUtil.readSettings(projectPath, false);
        if (readSettingsResult.isOk()) {
            return {
                version: readSettingsResult.value.version || "",
                source: versionMetadata_1.VersionSource.teamsapp,
            };
        }
        else {
            throw readSettingsResult.error;
        }
    }
    const v2path = projectSettingsLoader_1.getProjectSettingPathV2(projectPath);
    if (await fs_extra_1.default.pathExists(v2path)) {
        const settings = await fs_extra_1.default.readJson(v2path);
        return {
            version: settings.version || "",
            source: versionMetadata_1.VersionSource.projectSettings,
        };
    }
    const abandonedPath = path_1.default.resolve(projectPath, versionMetadata_1.MetadataV3Abandoned.configFolder, versionMetadata_1.MetadataV3Abandoned.configFile);
    if (await fs_extra_1.default.pathExists(abandonedPath)) {
        return {
            version: versionMetadata_1.MetadataV3Abandoned.configFolder,
            source: versionMetadata_1.VersionSource.settings,
        };
    }
    return {
        version: "",
        source: versionMetadata_1.VersionSource.unknown,
    };
}
exports.getProjectVersionFromPath = getProjectVersionFromPath;
async function getTrackingIdFromPath(projectPath) {
    const v3path = projectSettingsLoader_1.getProjectSettingPathV3(projectPath);
    if (await fs_extra_1.default.pathExists(v3path)) {
        const readSettingsResult = await settingsUtil_1.settingsUtil.readSettings(projectPath, false);
        if (readSettingsResult.isOk()) {
            return readSettingsResult.value.trackingId;
        }
        else {
            return "";
        }
    }
    const v2path = projectSettingsLoader_1.getProjectSettingPathV2(projectPath);
    if (await fs_extra_1.default.pathExists(v2path)) {
        const settings = await fs_extra_1.default.readJson(v2path);
        if (settings.projectId) {
            return settings.projectId || "";
        }
    }
    return "";
}
exports.getTrackingIdFromPath = getTrackingIdFromPath;
function getVersionState(info) {
    if (info.source === versionMetadata_1.VersionSource.projectSettings &&
        semver_1.default.gte(info.version, versionMetadata_1.MetadataV2.projectVersion) &&
        semver_1.default.lte(info.version, versionMetadata_1.MetadataV2.projectMaxVersion)) {
        return versionMetadata_1.VersionState.upgradeable;
    }
    else if (info.source === versionMetadata_1.VersionSource.teamsapp && info.version === versionMetadata_1.MetadataV3.projectVersion) {
        return versionMetadata_1.VersionState.compatible;
    }
    return versionMetadata_1.VersionState.unsupported;
}
exports.getVersionState = getVersionState;
function getParameterFromCxt(ctx, key, defaultValue) {
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    const value = inputs[key] || defaultValue || "";
    return value;
}
exports.getParameterFromCxt = getParameterFromCxt;
function getToolkitVersionLink(platform, projectVersion) {
    return versionMetadata_1.Metadata.versionMatchLink;
}
exports.getToolkitVersionLink = getToolkitVersionLink;
function getCapabilitySsoStatus(projectSettings) {
    let tabSso, botSso;
    if (projectSettings.components) {
        tabSso = projectSettings.components.some((component, index, obj) => {
            return component.name === "teams-tab" && component.sso == true;
        });
        botSso = projectSettings.components.some((component, index, obj) => {
            return component.name === "teams-bot" && component.sso == true;
        });
    }
    else {
        // For projects that does not componentize.
        const capabilities = projectSettings.solutionSettings.capabilities;
        tabSso = capabilities.includes("TabSso");
        botSso = capabilities.includes("BotSso");
    }
    return {
        TabSso: tabSso,
        BotSso: botSso,
    };
}
exports.getCapabilitySsoStatus = getCapabilitySsoStatus;
function generateAppIdUri(capabilities) {
    if (capabilities.TabSso && !capabilities.BotSso) {
        return "api://{{state.fx-resource-frontend-hosting.domain}}/{{state.fx-resource-aad-app-for-teams.clientId}}";
    }
    else if (capabilities.TabSso && capabilities.BotSso) {
        return "api://{{state.fx-resource-frontend-hosting.domain}}/botid-{{state.fx-resource-bot.botId}}";
    }
    else if (!capabilities.TabSso && capabilities.BotSso) {
        return "api://botid-{{state.fx-resource-bot.botId}}";
    }
    else {
        return "api://{{state.fx-resource-aad-app-for-teams.clientId}}";
    }
}
exports.generateAppIdUri = generateAppIdUri;
function replaceAppIdUri(manifest, appIdUri) {
    const appIdUriRegex = /{{+ *state\.fx\-resource\-aad\-app\-for\-teams\.applicationIdUris *}}+/g;
    if (manifest.match(appIdUriRegex)) {
        manifest = manifest.replace(appIdUriRegex, appIdUri);
    }
    return manifest;
}
exports.replaceAppIdUri = replaceAppIdUri;
async function readAndConvertUserdata(context, filePath, bicepContent) {
    let returnAnswer = "";
    const userdataContent = await fs_extra_1.default.readFile(path_1.default.join(context.projectPath, filePath), "utf8");
    const lines = userdataContent.split(os_1.EOL);
    for (const line of lines) {
        if (line && line != "") {
            // in case that there are "="s in secrets
            const key_value = line.split("=");
            const res = MigrationUtils_1.namingConverterV3("state." + key_value[0], MigrationUtils_1.FileType.USERDATA, bicepContent);
            if (res.isOk())
                returnAnswer += res.value + "=" + key_value.slice(1).join("=") + os_1.EOL;
        }
    }
    return returnAnswer;
}
exports.readAndConvertUserdata = readAndConvertUserdata;
async function updateAndSaveManifestForSpfx(context, manifest) {
    const remoteTemplatePath = path_1.default.join(teamsfx_api_1.AppPackageFolderName, versionMetadata_1.MetadataV3.teamsManifestFileName);
    const localTemplatePath = path_1.default.join(teamsfx_api_1.AppPackageFolderName, "manifest.local.json");
    const contentRegex = /\"\{\{\^config\.isLocalDebug\}\}.*\{\{\/config\.isLocalDebug\}\}\"/g;
    const remoteRegex = /\{\{\^config\.isLocalDebug\}\}.*\{\{\/config\.isLocalDebug\}\}\{/g;
    const localRegex = /\}\{\{\#config\.isLocalDebug\}\}.*\{\{\/config\.isLocalDebug\}\}/g;
    let remoteTemplate = manifest, localTemplate = manifest;
    // Replace contentUrls
    const placeholders = manifest.match(contentRegex);
    if (placeholders) {
        for (const placeholder of placeholders) {
            // Replace with local and remote url
            // Will only replace if one match found
            const remoteUrl = placeholder.match(remoteRegex);
            if (remoteUrl && remoteUrl.length == 1) {
                remoteTemplate = remoteTemplate.replace(placeholder, `"${remoteUrl[0].substring(24, remoteUrl[0].length - 25)}"`);
            }
            const localUrl = placeholder.match(localRegex);
            if (localUrl && localUrl.length == 1) {
                localTemplate = localTemplate.replace(placeholder, `"${localUrl[0].substring(25, localUrl[0].length - 24)}"`);
            }
        }
    }
    await context.fsWriteFile(remoteTemplatePath, remoteTemplate);
    await context.fsWriteFile(localTemplatePath, localTemplate);
}
exports.updateAndSaveManifestForSpfx = updateAndSaveManifestForSpfx;
//# sourceMappingURL=v3MigrationUtils.js.map