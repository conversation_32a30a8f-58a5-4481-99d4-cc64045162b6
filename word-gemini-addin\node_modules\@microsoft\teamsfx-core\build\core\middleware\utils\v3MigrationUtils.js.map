{"version": 3, "file": "v3MigrationUtils.js", "sourceRoot": "", "sources": ["../../../../src/core/middleware/utils/v3MigrationUtils.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,wDAAwB;AACxB,gEAA0B;AAE1B,mCAAkC;AAClC,qDAA+D;AAC/D,2BAAyB;AACzB,wDAOgC;AAEhC,4DAA4B;AAC5B,oEAA4F;AAC5F,qEAQyC;AAEzC,iEAAmE;AACnE,iDAAyC;AACzC,wEAAqE;AAErE,oCAAoC;AAC7B,KAAK,UAAU,YAAY,CAAC,OAAyB,EAAE,QAAgB;IAC5E,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAC1D,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACjC,MAAM,GAAG,GAAG,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAClC,OAAO,GAAG,CAAC;KACZ;AACH,CAAC;AAND,oCAMC;AAED,0BAA0B;AACnB,KAAK,UAAU,gBAAgB,CAAC,OAAyB;IAC9D,MAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;IAC5F,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;IAClE,OAAO,eAAe;QACpB,CAAC,CAAC,kBAAE,CAAC,YAAY,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,aAAa,CAAC,EAAE,MAAM,CAAC;QACxE,CAAC,CAAC,EAAE,CAAC;AACT,CAAC;AAND,4CAMC;AAED,2BAA2B;AAC3B,SAAgB,qBAAqB,CAAC,OAAyB;IAC7D,MAAM,MAAM,GAAW,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACvE,OAAO,MAAM,CAAC,QAAQ,KAAK,sBAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC;AACrE,CAAC;AAHD,sDAGC;AAED,4CAA4C;AAC5C,SAAgB,aAAa,CAAC,OAAyB,EAAE,KAAa;IACpE,MAAM,OAAO,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACtD,OAAO,kBAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACjC,CAAC;AAHD,sCAGC;AAED,gGAAgG;AAChG,MAAM,QAAQ,GAAG;IACf,kDAAkD;IAClD,mCAAmC;IACnC,kDAAkD;IAClD,2CAA2C;CAC5C,CAAC;AAEF,mFAAmF;AACnF,SAAgB,wBAAwB,CACtC,GAAQ,EACR,MAAc,EACd,aAAqB,EACrB,QAAkB,EAClB,YAAiB;IAEjB,IAAI,UAAU,GAAG,EAAE,CAAC;IACpB,IAAI,iBAAQ,CAAC,GAAG,CAAC,EAAE;QACjB,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACtC,UAAU;gBACR,aAAa,KAAK,EAAE;oBAClB,CAAC,CAAC,wBAAwB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,EAAE,QAAQ,EAAE,YAAY,CAAC;oBAC1F,CAAC,CAAC,wBAAwB,CACtB,GAAG,CAAC,OAAO,CAAC,EACZ,MAAM,EACN,aAAa,GAAG,GAAG,GAAG,OAAO,EAC7B,QAAQ,EACR,YAAY,CACb,CAAC;SACT;KACF;SAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;QAC5C,MAAM,GAAG,GAAG,kCAAiB,CAAC,aAAa,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QACrE,IAAI,GAAG,CAAC,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,QAAG,CAAC;KACpD;;QAAM,OAAO,EAAE,CAAC;IACjB,OAAO,UAAU,CAAC;AACpB,CAAC;AA1BD,4DA0BC;AAEM,KAAK,UAAU,iBAAiB,CAAC,GAAoB;IAC1D,MAAM,WAAW,GAAG,mBAAmB,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;IAChE,OAAO,MAAM,yBAAyB,CAAC,WAAW,CAAC,CAAC;AACtD,CAAC;AAHD,8CAGC;AAED,SAAgB,4BAA4B,CAAC,mBAAwC;IACnF,IAAI,mBAAmB,CAAC,QAAQ,KAAK,sBAAQ,CAAC,EAAE,EAAE;QAChD,OAAO,kCAAkB,CAAC,6BAA6B,EAAE,iCAAiC,CAAC,CAAC;KAC7F;IACD,MAAM,GAAG,GAAG,kCAAkB,CAC5B,0BAA0B,EAC1B,4BAAU,CAAC,eAAe,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CACzD,CAAC;IACF,OAAO,GAAG,CAAC;AACb,CAAC;AATD,oEASC;AAED,SAAgB,mCAAmC,CAAC,OAAe,EAAE,QAAkB;IACrF,IAAI,YAAY,GAAG,QAAQ,CAAC;IAC5B,IAAI,QAAQ,KAAK,sBAAQ,CAAC,EAAE,EAAE;QAC5B,YAAY,GAAG,eAAe,CAAC;KAChC;SAAM,IAAI,QAAQ,KAAK,sBAAQ,CAAC,GAAG,EAAE;QACpC,YAAY,GAAG,KAAK,CAAC;KACtB;IACD,OAAO,GAAG,0BAAQ,CAAC,gBAAgB,IAAI,YAAY,EAAE,CAAC;AACxD,CAAC;AARD,kFAQC;AAED,SAAgB,mBAAmB,CAAC,OAAe,EAAE,QAAkB;IACrE,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;IACxD,MAAM,IAAI,GAAG,mCAAmC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACpE,IAAI,QAAQ,KAAK,sBAAQ,CAAC,MAAM,EAAE;QAChC,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,OAAO,CACxB,kQAAkQ,CACnQ,CAAC;QACF,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,OAAO,CACxB,mGAAmG,4BAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,uBAAuB,IAAI,kBAAkB,CACrL,CAAC;KACH;SAAM,IAAI,QAAQ,KAAK,sBAAQ,CAAC,EAAE,EAAE;QACnC,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,OAAO,CACxB,8KAA8K,CAC/K,CAAC;QACF,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,OAAO,CACxB,mGAAmG,4BAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,uBAAuB,IAAI,kBAAkB,CACrL,CAAC;KACH;SAAM;QACL,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,OAAO,CACxB,kLAAkL,CACnL,CAAC;QACF,kBAAK,aAAL,kBAAK,uBAAL,kBAAK,CAAE,WAAW,CAAC,OAAO,CACxB,uGAAuG,4BAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,uBAAuB,IAAI,kBAAkB,CACzL,CAAC;KACH;AACH,CAAC;AAzBD,kDAyBC;AAEM,KAAK,UAAU,yBAAyB,CAAC,WAAmB;IACjE,MAAM,MAAM,GAAG,+CAAuB,CAAC,WAAW,CAAC,CAAC;IACpD,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC/B,MAAM,kBAAkB,GAAG,MAAM,2BAAY,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAC/E,IAAI,kBAAkB,CAAC,IAAI,EAAE,EAAE;YAC7B,OAAO;gBACL,OAAO,EAAE,kBAAkB,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE;gBAC/C,MAAM,EAAE,+BAAa,CAAC,QAAQ;aAC/B,CAAC;SACH;aAAM;YACL,MAAM,kBAAkB,CAAC,KAAK,CAAC;SAChC;KACF;IACD,MAAM,MAAM,GAAG,+CAAuB,CAAC,WAAW,CAAC,CAAC;IACpD,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC/B,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3C,OAAO;YACL,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,EAAE;YAC/B,MAAM,EAAE,+BAAa,CAAC,eAAe;SACtC,CAAC;KACH;IACD,MAAM,aAAa,GAAG,cAAI,CAAC,OAAO,CAChC,WAAW,EACX,qCAAmB,CAAC,YAAY,EAChC,qCAAmB,CAAC,UAAU,CAC/B,CAAC;IACF,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;QACtC,OAAO;YACL,OAAO,EAAE,qCAAmB,CAAC,YAAY;YACzC,MAAM,EAAE,+BAAa,CAAC,QAAQ;SAC/B,CAAC;KACH;IACD,OAAO;QACL,OAAO,EAAE,EAAE;QACX,MAAM,EAAE,+BAAa,CAAC,OAAO;KAC9B,CAAC;AACJ,CAAC;AApCD,8DAoCC;AAEM,KAAK,UAAU,qBAAqB,CAAC,WAAmB;IAC7D,MAAM,MAAM,GAAG,+CAAuB,CAAC,WAAW,CAAC,CAAC;IACpD,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC/B,MAAM,kBAAkB,GAAG,MAAM,2BAAY,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAC/E,IAAI,kBAAkB,CAAC,IAAI,EAAE,EAAE;YAC7B,OAAO,kBAAkB,CAAC,KAAK,CAAC,UAAU,CAAC;SAC5C;aAAM;YACL,OAAO,EAAE,CAAC;SACX;KACF;IACD,MAAM,MAAM,GAAG,+CAAuB,CAAC,WAAW,CAAC,CAAC;IACpD,IAAI,MAAM,kBAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC/B,MAAM,QAAQ,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,QAAQ,CAAC,SAAS,EAAE;YACtB,OAAO,QAAQ,CAAC,SAAS,IAAI,EAAE,CAAC;SACjC;KACF;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAlBD,sDAkBC;AAED,SAAgB,eAAe,CAAC,IAAiB;IAC/C,IACE,IAAI,CAAC,MAAM,KAAK,+BAAa,CAAC,eAAe;QAC7C,gBAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,4BAAU,CAAC,cAAc,CAAC;QACnD,gBAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,4BAAU,CAAC,iBAAiB,CAAC,EACtD;QACA,OAAO,8BAAY,CAAC,WAAW,CAAC;KACjC;SAAM,IAAI,IAAI,CAAC,MAAM,KAAK,+BAAa,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,KAAK,4BAAU,CAAC,cAAc,EAAE;QAC/F,OAAO,8BAAY,CAAC,UAAU,CAAC;KAChC;IACD,OAAO,8BAAY,CAAC,WAAW,CAAC;AAClC,CAAC;AAXD,0CAWC;AAED,SAAgB,mBAAmB,CACjC,GAAoB,EACpB,GAAW,EACX,YAAqB;IAErB,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAW,CAAC;IACjE,MAAM,KAAK,GAAI,MAAM,CAAC,GAAG,CAAY,IAAI,YAAY,IAAI,EAAE,CAAC;IAC5D,OAAO,KAAK,CAAC;AACf,CAAC;AARD,kDAQC;AAED,SAAgB,qBAAqB,CAAC,QAAkB,EAAE,cAAsB;IAC9E,OAAO,0BAAQ,CAAC,gBAAgB,CAAC;AACnC,CAAC;AAFD,sDAEC;AAED,SAAgB,sBAAsB,CAAC,eAAgC;IAIrE,IAAI,MAAM,EAAE,MAAM,CAAC;IACnB,IAAK,eAAqC,CAAC,UAAU,EAAE;QACrD,MAAM,GAAI,eAAqC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;YACxF,OAAO,SAAS,CAAC,IAAI,KAAK,WAAW,IAAI,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC;QACjE,CAAC,CAAC,CAAC;QACH,MAAM,GAAI,eAAqC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;YACxF,OAAO,SAAS,CAAC,IAAI,KAAK,WAAW,IAAI,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC;QACjE,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,2CAA2C;QAC3C,MAAM,YAAY,GAAI,eAAe,CAAC,gBAA0C,CAAC,YAAY,CAAC;QAC9F,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;KAC1C;IAED,OAAO;QACL,MAAM,EAAE,MAAM;QACd,MAAM,EAAE,MAAM;KACf,CAAC;AACJ,CAAC;AAvBD,wDAuBC;AAED,SAAgB,gBAAgB,CAAC,YAAkD;IACjF,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;QAC/C,OAAO,sGAAsG,CAAC;KAC/G;SAAM,IAAI,YAAY,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,EAAE;QACrD,OAAO,2FAA2F,CAAC;KACpG;SAAM,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM,EAAE;QACtD,OAAO,6CAA6C,CAAC;KACtD;SAAM;QACL,OAAO,wDAAwD,CAAC;KACjE;AACH,CAAC;AAVD,4CAUC;AAED,SAAgB,eAAe,CAAC,QAAgB,EAAE,QAAgB;IAChE,MAAM,aAAa,GAAG,yEAAyE,CAAC;IAChG,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;QACjC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;KACtD;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAPD,0CAOC;AAEM,KAAK,UAAU,sBAAsB,CAC1C,OAAyB,EACzB,QAAgB,EAChB,YAAiB;IAEjB,IAAI,YAAY,GAAG,EAAE,CAAC;IAEtB,MAAM,eAAe,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;IAC5F,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,QAAG,CAAC,CAAC;IACzC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE;YACtB,yCAAyC;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,GAAG,GAAG,kCAAiB,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,yBAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YACxF,IAAI,GAAG,CAAC,IAAI,EAAE;gBAAE,YAAY,IAAI,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,QAAG,CAAC;SACtF;KACF;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AAnBD,wDAmBC;AAEM,KAAK,UAAU,4BAA4B,CAChD,OAAyB,EACzB,QAAgB;IAEhB,MAAM,kBAAkB,GAAG,cAAI,CAAC,IAAI,CAAC,kCAAoB,EAAE,4BAAU,CAAC,qBAAqB,CAAC,CAAC;IAC7F,MAAM,iBAAiB,GAAG,cAAI,CAAC,IAAI,CAAC,kCAAoB,EAAE,qBAAqB,CAAC,CAAC;IAEjF,MAAM,YAAY,GAAG,qEAAqE,CAAC;IAC3F,MAAM,WAAW,GAAG,mEAAmE,CAAC;IACxF,MAAM,UAAU,GAAG,mEAAmE,CAAC;IAEvF,IAAI,cAAc,GAAG,QAAQ,EAC3B,aAAa,GAAG,QAAQ,CAAC;IAE3B,sBAAsB;IACtB,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAClD,IAAI,YAAY,EAAE;QAChB,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;YACtC,oCAAoC;YACpC,uCAAuC;YACvC,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YACjD,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;gBACtC,cAAc,GAAG,cAAc,CAAC,OAAO,CACrC,WAAW,EACX,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,CAC5D,CAAC;aACH;YAED,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC/C,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,IAAI,CAAC,EAAE;gBACpC,aAAa,GAAG,aAAa,CAAC,OAAO,CACnC,WAAW,EACX,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,CAC1D,CAAC;aACH;SACF;KACF;IAED,MAAM,OAAO,CAAC,WAAW,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;IAC9D,MAAM,OAAO,CAAC,WAAW,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;AAC9D,CAAC;AAxCD,oEAwCC"}