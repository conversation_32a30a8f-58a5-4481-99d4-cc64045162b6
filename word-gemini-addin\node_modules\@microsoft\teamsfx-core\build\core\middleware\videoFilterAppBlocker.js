// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VideoFilterAppBlockerMW = void 0;
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const tools_1 = require("../../common/tools");
const error_1 = require("../error");
const userTasksToBlock = [
    // Teams: Add features
    {
        namespace: "fx-solution-azure",
        method: "addFeature",
    },
    // Teams: Zip Teams metadata package
    {
        namespace: "fx-solution-azure",
        method: "buildPackage",
    },
    // Teams: Validate manifest file
    {
        namespace: "fx-solution-azure",
        method: "validateManifest",
    },
];
async function shouldBlockExecution(ctx) {
    const inputs = ctx.arguments[ctx.arguments.length - 1];
    if (!inputs.projectPath) {
        return false;
    }
    if (ctx.method === "executeUserTask") {
        let shouldBlockUserTask = false;
        const func = ctx.arguments[0];
        for (const item of userTasksToBlock) {
            if ((func === null || func === void 0 ? void 0 : func.namespace) === item.namespace && (func === null || func === void 0 ? void 0 : func.method) === item.method) {
                shouldBlockUserTask = true;
                break;
            }
        }
        if (!shouldBlockUserTask) {
            return false;
        }
    }
    const result = await tools_1.isVideoFilterProject(inputs.projectPath);
    // Ignore errors and assume this project is not a video filter project
    return result.isOk() && result.value;
}
/**
 * This middleware will block remote operations (provision/deploy/...) since we don't support these operations for now.
 */
const VideoFilterAppBlockerMW = async (ctx, next) => {
    let shouldBlock;
    try {
        shouldBlock = await shouldBlockExecution(ctx);
    }
    catch (e) {
        // Ignore errors and assume this project is not a video filter project
        shouldBlock = false;
    }
    if (shouldBlock) {
        ctx.result = teamsfx_api_1.err(new error_1.VideoFilterAppRemoteNotSupportedError());
        return;
    }
    else {
        await next();
    }
};
exports.VideoFilterAppBlockerMW = VideoFilterAppBlockerMW;
//# sourceMappingURL=videoFilterAppBlocker.js.map