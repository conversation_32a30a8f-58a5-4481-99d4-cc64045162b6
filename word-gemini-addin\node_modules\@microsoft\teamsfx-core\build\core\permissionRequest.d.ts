import { PermissionRequestProvider, Result, FxError } from "@microsoft/teamsfx-api";
export declare class PermissionRequestFileProvider implements PermissionRequestProvider {
    private rootPath;
    readonly permissionFileName = "permissions.json";
    constructor(rootPath: string);
    checkPermissionRequest(): Promise<Result<undefined, FxError>>;
    getPermissionRequest(): Promise<Result<string, FxError>>;
}
//# sourceMappingURL=permissionRequest.d.ts.map