"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionRequestFileProvider = void 0;
const tslib_1 = require("tslib");
const fs = tslib_1.__importStar(require("fs-extra"));
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const constants_1 = require("../component/constants");
const error_1 = require("./error");
class PermissionRequestFileProvider {
    constructor(rootPath) {
        this.permissionFileName = "permissions.json";
        this.rootPath = rootPath;
    }
    async checkPermissionRequest() {
        const path = `${this.rootPath}/${this.permissionFileName}`;
        if (!(await fs.pathExists(path))) {
            return teamsfx_api_1.err(new teamsfx_api_1.UserError(error_1.CoreSource, constants_1.SolutionError.MissingPermissionsJson, `${this.permissionFileName} is missing`));
        }
        return teamsfx_api_1.ok(undefined);
    }
    async getPermissionRequest() {
        this.checkPermissionRequest();
        const permissionRequest = await fs.readJSON(`${this.rootPath}/${this.permissionFileName}`);
        return teamsfx_api_1.ok(JSON.stringify(permissionRequest));
    }
}
exports.PermissionRequestFileProvider = PermissionRequestFileProvider;
//# sourceMappingURL=permissionRequest.js.map