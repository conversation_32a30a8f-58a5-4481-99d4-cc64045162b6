{"version": 3, "file": "permissionRequest.js", "sourceRoot": "", "sources": ["../../src/core/permissionRequest.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAElC,qDAA+B;AAC/B,wDAOgC;AAChC,sDAAuD;AACvD,mCAAqC;AAErC,MAAa,6BAA6B;IAIxC,YAAY,QAAgB;QAFZ,uBAAkB,GAAG,kBAAkB,CAAC;QAGtD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAEM,KAAK,CAAC,sBAAsB;QACjC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3D,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE;YAChC,OAAO,iBAAG,CACR,IAAI,uBAAS,CACX,kBAAU,EACV,yBAAa,CAAC,sBAAsB,EACpC,GAAG,IAAI,CAAC,kBAAkB,aAAa,CACxC,CACF,CAAC;SACH;QAED,OAAO,gBAAE,CAAC,SAAS,CAAC,CAAC;IACvB,CAAC;IAEM,KAAK,CAAC,oBAAoB;QAC/B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,MAAM,iBAAiB,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC3F,OAAO,gBAAE,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;IAC/C,CAAC;CACF;AA7BD,sEA6BC"}