import { FolderQuestion, OptionItem, Platform, SingleSelectQuestion, TextInputQuestion, FuncQuestion, Inputs, MultiSelectQuestion } from "@microsoft/teamsfx-api";
import { StaticTab } from "../component/resource/appManifest/interfaces/staticTab";
export declare enum CoreQuestionNames {
    AppName = "app-name",
    DefaultAppNameFunc = "default-app-name-func",
    Folder = "folder",
    ProjectPath = "projectPath",
    ProgrammingLanguage = "programming-language",
    Capabilities = "capabilities",
    Features = "features",
    Solution = "solution",
    CreateFromScratch = "scratch",
    Runtime = "runtime",
    Samples = "samples",
    Stage = "stage",
    SubStage = "substage",
    SourceEnvName = "sourceEnvName",
    TargetEnvName = "targetEnvName",
    TargetResourceGroupName = "targetResourceGroupName",
    NewResourceGroupName = "newResourceGroupName",
    NewResourceGroupLocation = "newResourceGroupLocation",
    NewTargetEnvName = "newTargetEnvName",
    ExistingTabEndpoint = "existing-tab-endpoint",
    SafeProjectName = "safeProjectName",
    ReplaceContentUrl = "replaceContentUrl",
    ReplaceWebsiteUrl = "replaceWebsiteUrl",
    AppPackagePath = "appPackagePath",
    ReplaceBotIds = "replaceBotIds"
}
export declare const ProjectNamePattern = "^(?=(.*[\\da-zA-Z]){2})[a-zA-Z][^\"<>:\\?/*&|\0-\u001F]*[^\"\\s.<>:\\?/*&|\0-\u001F]$";
export declare function createAppNameQuestion(defaultAppName?: string, validateProjectPathExistence?: boolean): TextInputQuestion;
export declare const DefaultAppNameFunc: FuncQuestion;
export declare function QuestionRootFolder(): FolderQuestion;
export declare const ProgrammingLanguageQuestionForDotNet: SingleSelectQuestion;
export declare const ProgrammingLanguageQuestion: SingleSelectQuestion;
export declare function handleSelectionConflict<T>(sets: Set<T>[], previous: Set<T>, current: Set<T>): Set<T>;
export declare function validateConflict<T>(sets: Set<T>[], current: Set<T>): string | undefined;
export declare function createCapabilityQuestion(): MultiSelectQuestion;
export declare function createCapabilityForDotNet(): SingleSelectQuestion;
export declare function createCapabilityQuestionPreview(inputs?: Inputs): SingleSelectQuestion;
export declare function validateCapabilities(inputs: string[]): string | undefined;
export declare function onChangeSelectionForCapabilities(currentSelectedIds: Set<string>, previousSelectedIds: Set<string>): Promise<Set<string>>;
export declare function QuestionSelectTargetEnvironment(): SingleSelectQuestion;
export declare function getQuestionNewTargetEnvironmentName(projectPath: string): TextInputQuestion;
export declare function QuestionSelectSourceEnvironment(): SingleSelectQuestion;
export declare function QuestionSelectResourceGroup(): SingleSelectQuestion;
export declare function newResourceGroupNameQuestion(existingResourceGroupNames: string[]): TextInputQuestion;
export declare function QuestionNewResourceGroupName(): TextInputQuestion;
export declare function QuestionNewResourceGroupLocation(): SingleSelectQuestion;
export declare function ScratchOptionYesVSC(): OptionItem;
export declare function ScratchOptionNoVSC(): OptionItem;
export declare function RuntimeOptionNodeJs(): OptionItem;
export declare function RuntimeOptionDotNet(): OptionItem;
export declare function ScratchOptionYes(): OptionItem;
export declare function ScratchOptionNo(): OptionItem;
export declare function getRuntimeQuestion(): SingleSelectQuestion;
export declare function getCreateNewOrFromSampleQuestion(platform: Platform): SingleSelectQuestion;
export declare function SampleSelect(): SingleSelectQuestion;
export declare function ExistingTabEndpointQuestion(): TextInputQuestion;
export declare const defaultTabLocalHostUrl = "https://localhost:53000/index.html#/tab";
export declare const tabsContentUrlQuestion: (tabs: StaticTab[]) => MultiSelectQuestion;
export declare const tabsWebsitetUrlQuestion: (tabs: StaticTab[]) => MultiSelectQuestion;
export declare const tabContentUrlOptionItem: (tab: StaticTab) => OptionItem;
export declare const tabWebsiteUrlOptionItem: (tab: StaticTab) => OptionItem;
export declare const BotIdsQuestion: (botId: string | undefined, messageExtensionBotId: string | undefined) => MultiSelectQuestion;
export declare const botOptionItem: (isMessageExtension: boolean) => OptionItem;
export declare function CreateNewOfficeAddinOption(): OptionItem;
export declare function createCapabilityForOfficeAddin(): SingleSelectQuestion;
//# sourceMappingURL=question.d.ts.map