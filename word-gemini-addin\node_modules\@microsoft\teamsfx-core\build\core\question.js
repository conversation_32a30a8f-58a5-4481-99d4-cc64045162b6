"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCapabilityForOfficeAddin = exports.CreateNewOfficeAddinOption = exports.botOptionItem = exports.BotIdsQuestion = exports.tabWebsiteUrlOptionItem = exports.tabContentUrlOptionItem = exports.tabsWebsitetUrlQuestion = exports.tabsContentUrlQuestion = exports.defaultTabLocalHostUrl = exports.ExistingTabEndpointQuestion = exports.SampleSelect = exports.getCreateNewOrFromSampleQuestion = exports.getRuntimeQuestion = exports.ScratchOptionNo = exports.ScratchOptionYes = exports.RuntimeOptionDotNet = exports.RuntimeOptionNodeJs = exports.ScratchOptionNoVSC = exports.ScratchOptionYesVSC = exports.QuestionNewResourceGroupLocation = exports.QuestionNewResourceGroupName = exports.newResourceGroupNameQuestion = exports.QuestionSelectResourceGroup = exports.QuestionSelectSourceEnvironment = exports.getQuestionNewTargetEnvironmentName = exports.QuestionSelectTargetEnvironment = exports.onChangeSelectionForCapabilities = exports.validateCapabilities = exports.createCapabilityQuestionPreview = exports.createCapabilityForDotNet = exports.createCapabilityQuestion = exports.validateConflict = exports.handleSelectionConflict = exports.ProgrammingLanguageQuestion = exports.ProgrammingLanguageQuestionForDotNet = exports.QuestionRootFolder = exports.DefaultAppNameFunc = exports.createAppNameQuestion = exports.ProjectNamePattern = exports.CoreQuestionNames = void 0;
const tslib_1 = require("tslib");
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
const jsonschema = tslib_1.__importStar(require("jsonschema"));
const path = tslib_1.__importStar(require("path"));
const fs = tslib_1.__importStar(require("fs-extra"));
const os = tslib_1.__importStar(require("os"));
const environment_1 = require("./environment");
const constants_1 = require("../common/constants");
const samples_1 = require("../common/samples");
const tools_1 = require("../common/tools");
const featureFlags_1 = require("../common/featureFlags");
const localizeUtils_1 = require("../common/localizeUtils");
const constants_2 = require("../component/constants");
const developerPortalScaffoldUtils_1 = require("../component/developerPortalScaffoldUtils");
const question_1 = require("../component/generator/officeAddin/question");
var CoreQuestionNames;
(function (CoreQuestionNames) {
    CoreQuestionNames["AppName"] = "app-name";
    CoreQuestionNames["DefaultAppNameFunc"] = "default-app-name-func";
    CoreQuestionNames["Folder"] = "folder";
    CoreQuestionNames["ProjectPath"] = "projectPath";
    CoreQuestionNames["ProgrammingLanguage"] = "programming-language";
    CoreQuestionNames["Capabilities"] = "capabilities";
    CoreQuestionNames["Features"] = "features";
    CoreQuestionNames["Solution"] = "solution";
    CoreQuestionNames["CreateFromScratch"] = "scratch";
    CoreQuestionNames["Runtime"] = "runtime";
    CoreQuestionNames["Samples"] = "samples";
    CoreQuestionNames["Stage"] = "stage";
    CoreQuestionNames["SubStage"] = "substage";
    CoreQuestionNames["SourceEnvName"] = "sourceEnvName";
    CoreQuestionNames["TargetEnvName"] = "targetEnvName";
    CoreQuestionNames["TargetResourceGroupName"] = "targetResourceGroupName";
    CoreQuestionNames["NewResourceGroupName"] = "newResourceGroupName";
    CoreQuestionNames["NewResourceGroupLocation"] = "newResourceGroupLocation";
    CoreQuestionNames["NewTargetEnvName"] = "newTargetEnvName";
    CoreQuestionNames["ExistingTabEndpoint"] = "existing-tab-endpoint";
    CoreQuestionNames["SafeProjectName"] = "safeProjectName";
    CoreQuestionNames["ReplaceContentUrl"] = "replaceContentUrl";
    CoreQuestionNames["ReplaceWebsiteUrl"] = "replaceWebsiteUrl";
    CoreQuestionNames["AppPackagePath"] = "appPackagePath";
    CoreQuestionNames["ReplaceBotIds"] = "replaceBotIds";
})(CoreQuestionNames = exports.CoreQuestionNames || (exports.CoreQuestionNames = {}));
exports.ProjectNamePattern = '^(?=(.*[\\da-zA-Z]){2})[a-zA-Z][^"<>:\\?/*&|\u0000-\u001F]*[^"\\s.<>:\\?/*&|\u0000-\u001F]$';
function createAppNameQuestion(defaultAppName, validateProjectPathExistence = true) {
    const question = {
        type: "text",
        name: CoreQuestionNames.AppName,
        title: "Application name",
        default: defaultAppName,
        validation: {
            validFunc: async (input, previousInputs) => {
                const schema = {
                    pattern: exports.ProjectNamePattern,
                    maxLength: 30,
                };
                const appName = input;
                const validateResult = jsonschema.validate(appName, schema);
                if (validateResult.errors && validateResult.errors.length > 0) {
                    if (validateResult.errors[0].name === "pattern") {
                        return localizeUtils_1.getLocalizedString("core.QuestionAppName.validation.pattern");
                    }
                    if (validateResult.errors[0].name === "maxLength") {
                        return localizeUtils_1.getLocalizedString("core.QuestionAppName.validation.maxlength");
                    }
                }
                if (validateProjectPathExistence && previousInputs && previousInputs.folder) {
                    const folder = previousInputs.folder;
                    if (folder) {
                        const projectPath = path.resolve(folder, appName);
                        const exists = await fs.pathExists(projectPath);
                        if (exists)
                            return localizeUtils_1.getLocalizedString("core.QuestionAppName.validation.pathExist", projectPath);
                    }
                }
                return undefined;
            },
        },
        placeholder: "Application name",
    };
    return question;
}
exports.createAppNameQuestion = createAppNameQuestion;
exports.DefaultAppNameFunc = {
    type: "func",
    name: CoreQuestionNames.DefaultAppNameFunc,
    func: (inputs) => {
        var _a;
        const appName = path.basename((_a = inputs.projectPath) !== null && _a !== void 0 ? _a : "");
        const schema = {
            pattern: exports.ProjectNamePattern,
            maxLength: 30,
        };
        const validateResult = jsonschema.validate(appName, schema);
        if (validateResult.errors && validateResult.errors.length > 0) {
            return undefined;
        }
        return appName;
    },
};
function QuestionRootFolder() {
    return {
        type: "folder",
        name: CoreQuestionNames.Folder,
        title: localizeUtils_1.getLocalizedString("core.question.workspaceFolder.title"),
        placeholder: localizeUtils_1.getLocalizedString("core.question.workspaceFolder.placeholder"),
        default: path.join(os.homedir(), constants_1.ConstantString.RootFolder),
    };
}
exports.QuestionRootFolder = QuestionRootFolder;
exports.ProgrammingLanguageQuestionForDotNet = {
    name: CoreQuestionNames.ProgrammingLanguage,
    title: "Programming Language",
    type: "singleSelect",
    staticOptions: [{ id: "csharp", label: "C#" }],
    skipSingleOption: true,
};
exports.ProgrammingLanguageQuestion = {
    name: CoreQuestionNames.ProgrammingLanguage,
    title: "Programming Language",
    type: "singleSelect",
    staticOptions: [
        { id: "javascript", label: "JavaScript" },
        { id: "typescript", label: "TypeScript" },
    ],
    dynamicOptions: (inputs) => {
        if (inputs.platform === teamsfx_api_1.Platform.VS) {
            return [{ id: "csharp", label: "C#" }];
        }
        const capabilities = inputs[CoreQuestionNames.Capabilities];
        if (capabilities && capabilities.includes && capabilities.includes(constants_2.TabSPFxItem().id))
            return [{ id: "typescript", label: "TypeScript" }];
        return [
            { id: "javascript", label: "JavaScript" },
            { id: "typescript", label: "TypeScript" },
        ];
    },
    skipSingleOption: true,
    default: (inputs) => {
        if (featureFlags_1.isPreviewFeaturesEnabled()) {
            const capability = inputs[CoreQuestionNames.Capabilities];
            if (capability && capability === constants_2.TabSPFxItem().id) {
                return "typescript";
            }
            const feature = inputs[CoreQuestionNames.Features];
            if (feature && feature === constants_2.TabSPFxItem().id) {
                return "typescript";
            }
        }
        else {
            const capabilities = inputs[CoreQuestionNames.Capabilities];
            if (capabilities && capabilities.includes && capabilities.includes(constants_2.TabSPFxItem().id))
                return "typescript";
        }
        return "javascript";
    },
    placeholder: (inputs) => {
        if (featureFlags_1.isPreviewFeaturesEnabled()) {
            const capability = inputs[CoreQuestionNames.Capabilities];
            if (capability && capability === constants_2.TabSPFxItem().id) {
                return localizeUtils_1.getLocalizedString("core.ProgrammingLanguageQuestion.placeholder.spfx");
            }
            const feature = inputs[CoreQuestionNames.Features];
            if (feature && feature === constants_2.TabSPFxItem().id) {
                return localizeUtils_1.getLocalizedString("core.ProgrammingLanguageQuestion.placeholder.spfx");
            }
        }
        else {
            const capabilities = inputs[CoreQuestionNames.Capabilities];
            if (capabilities && capabilities.includes && capabilities.includes(constants_2.TabSPFxItem().id))
                return localizeUtils_1.getLocalizedString("core.ProgrammingLanguageQuestion.placeholder.spfx");
        }
        return localizeUtils_1.getLocalizedString("core.ProgrammingLanguageQuestion.placeholder");
    },
};
function hasCapability(items, optionItem) {
    return items.includes(optionItem.id) || items.includes(optionItem.label);
}
function setIntersect(set1, set2) {
    return new Set([...set1].filter((item) => set2.has(item)));
}
function setDiff(set1, set2) {
    return new Set([...set1].filter((item) => !set2.has(item)));
}
function setUnion(...sets) {
    return new Set([].concat(...sets.map((set) => [...set])));
}
// Each set is mutually exclusive. Handle conflict by removing items conflicting with the newly added items.
// Assuming intersection of all sets are empty sets and no conflicts in newly added items.
//
// For example: sets = [[1, 2], [3, 4]], previous = [1, 2, 5], current = [1, 2, 4, 5].
// So the newly added one is [4]. Remove all items from `current` that conflict with [4].
// Result = [4, 5].
function handleSelectionConflict(sets, previous, current) {
    const allSets = setUnion(...sets);
    const addedItems = setDiff(current, previous);
    for (const set of sets) {
        if (setIntersect(set, addedItems).size > 0) {
            return setUnion(setIntersect(set, current), setDiff(current, allSets));
        }
    }
    // If newly added items are not in any sets, do nothing.
    return current;
}
exports.handleSelectionConflict = handleSelectionConflict;
function validateConflict(sets, current) {
    const all = setUnion(...sets);
    const currentIntersectAll = setIntersect(all, current);
    for (const set of sets) {
        if (setIntersect(set, current).size > 0) {
            const currentIntersectSet = setIntersect(set, current);
            if (currentIntersectSet.size < currentIntersectAll.size) {
                return localizeUtils_1.getLocalizedString("core.capability.validation", `[${Array.from(current).join(", ")}]`, Array.from(sets)
                    .map((set) => `[${Array.from(set).join(", ")}]`)
                    .join(", "));
            }
        }
    }
    return undefined;
}
exports.validateConflict = validateConflict;
function createCapabilityQuestion() {
    let staticOptions;
    if (featureFlags_1.isBotNotificationEnabled()) {
        // new capabilities question order
        const newBots = [
            constants_2.NotificationOptionItem(),
            constants_2.CommandAndResponseOptionItem(),
            constants_2.WorkflowOptionItem(),
        ];
        staticOptions = [
            ...newBots,
            ...(tools_1.isExistingTabAppEnabled() ? [constants_2.ExistingTabOptionItem()] : []),
            ...(tools_1.isAadManifestEnabled() ? [constants_2.TabNonSsoItem()] : []),
            ...[constants_2.TabNewUIOptionItem(), constants_2.TabSPFxNewUIItem(), constants_2.MessageExtensionNewUIItem()],
            ...(tools_1.isM365AppEnabled() ? [constants_2.M365SsoLaunchPageOptionItem(), constants_2.M365SearchAppOptionItem()] : []),
        ];
    }
    else {
        staticOptions = [
            ...[constants_2.TabOptionItem(), constants_2.BotOptionItem(), constants_2.MessageExtensionItem(), constants_2.TabSPFxItem()],
            ...(tools_1.isAadManifestEnabled() ? [constants_2.TabNonSsoItem()] : []),
            ...(tools_1.isExistingTabAppEnabled() ? [constants_2.ExistingTabOptionItem()] : []),
            ...(tools_1.isM365AppEnabled() ? [constants_2.M365SsoLaunchPageOptionItem(), constants_2.M365SearchAppOptionItem()] : []),
        ];
    }
    return {
        name: CoreQuestionNames.Capabilities,
        title: featureFlags_1.isBotNotificationEnabled()
            ? localizeUtils_1.getLocalizedString("core.createCapabilityQuestion.titleNew")
            : localizeUtils_1.getLocalizedString("core.createCapabilityQuestion.title"),
        type: "multiSelect",
        staticOptions: staticOptions,
        default: featureFlags_1.isBotNotificationEnabled()
            ? [constants_2.CommandAndResponseOptionItem().id]
            : [constants_2.TabOptionItem().id],
        placeholder: localizeUtils_1.getLocalizedString("core.createCapabilityQuestion.placeholder"),
        validation: {
            validFunc: validateCapabilities,
        },
        onDidChangeSelection: onChangeSelectionForCapabilities,
    };
}
exports.createCapabilityQuestion = createCapabilityQuestion;
function createCapabilityForDotNet() {
    const staticOptions = [
        constants_2.NotificationOptionItem(),
        constants_2.CommandAndResponseOptionItem(),
        constants_2.TabOptionItem(),
        constants_2.MessageExtensionItem(),
    ];
    return {
        name: CoreQuestionNames.Capabilities,
        title: localizeUtils_1.getLocalizedString("core.createCapabilityQuestion.title"),
        type: "singleSelect",
        staticOptions: staticOptions,
        placeholder: localizeUtils_1.getLocalizedString("core.createCapabilityQuestion.placeholder"),
    };
}
exports.createCapabilityForDotNet = createCapabilityForDotNet;
function createCapabilityQuestionPreview(inputs) {
    // AB test for notification/command/workflow bot, dashboard tab template naming
    const notificationOptionItem = constants_2.NotificationOptionItem();
    const commandAndResponseOptionItem = constants_2.CommandAndResponseOptionItem();
    const workflowOptionItem = constants_2.WorkflowOptionItem();
    const dashboardOptionItem = constants_2.DashboardOptionItem();
    if (inputs === null || inputs === void 0 ? void 0 : inputs.taskOrientedTemplateNaming) {
        notificationOptionItem.label = `$(hubot) ${localizeUtils_1.getLocalizedString("core.NotificationOption.label.abTest")}`;
        notificationOptionItem.detail = localizeUtils_1.getLocalizedString("core.NotificationOption.detail.abTest");
        commandAndResponseOptionItem.label = `$(hubot) ${localizeUtils_1.getLocalizedString("core.CommandAndResponseOption.label.abTest")}`;
        commandAndResponseOptionItem.detail = localizeUtils_1.getLocalizedString("core.CommandAndResponseOption.detail.abTest");
        workflowOptionItem.label = `$(hubot) ${localizeUtils_1.getLocalizedString("core.WorkflowOption.label.abTest")}`;
        workflowOptionItem.detail = localizeUtils_1.getLocalizedString("core.WorkflowOption.detail.abTest");
        dashboardOptionItem.label = `$(browser) ${localizeUtils_1.getLocalizedString("core.DashboardOption.label.abTest")}`;
        dashboardOptionItem.detail = localizeUtils_1.getLocalizedString("core.DashboardOption.detail.abTest");
    }
    // AB test for in product doc
    if (inputs === null || inputs === void 0 ? void 0 : inputs.inProductDoc) {
        workflowOptionItem.data = "cardActionResponse";
        workflowOptionItem.buttons = [
            {
                iconPath: "file-code",
                tooltip: localizeUtils_1.getLocalizedString("core.option.inProduct"),
                command: "fx-extension.openTutorial",
            },
        ];
    }
    // new capabilities question order
    const newBots = [notificationOptionItem, commandAndResponseOptionItem, workflowOptionItem];
    const newTabs = [dashboardOptionItem];
    const staticOptions = [
        ...newBots,
        ...newTabs,
        constants_2.TabNewUIOptionItem(),
        constants_2.TabSPFxNewUIItem(),
        constants_2.TabNonSsoItem(),
        constants_2.BotNewUIOptionItem(),
        constants_2.MessageExtensionNewUIItem(),
        constants_2.M365SsoLaunchPageOptionItem(),
        constants_2.M365SearchAppOptionItem(),
    ];
    if (tools_1.isExistingTabAppEnabled()) {
        staticOptions.splice(newBots.length, 0, constants_2.ExistingTabOptionItem());
    }
    return {
        name: CoreQuestionNames.Capabilities,
        title: localizeUtils_1.getLocalizedString("core.createCapabilityQuestion.titleNew"),
        type: "singleSelect",
        staticOptions: staticOptions,
        placeholder: localizeUtils_1.getLocalizedString("core.createCapabilityQuestion.placeholder"),
        forgetLastValue: true,
    };
}
exports.createCapabilityQuestionPreview = createCapabilityQuestionPreview;
function validateCapabilities(inputs) {
    if (inputs.length === 0) {
        return localizeUtils_1.getLocalizedString("core.createCapabilityQuestion.placeholder");
    }
    const set = new Set();
    inputs.forEach((i) => set.add(i));
    let result = validateConflict([
        new Set([constants_2.BotOptionItem().id, constants_2.MessageExtensionItem().id]),
        new Set([constants_2.NotificationOptionItem().id]),
        new Set([constants_2.CommandAndResponseOptionItem().id]),
        new Set([constants_2.WorkflowOptionItem().id]),
    ], set);
    if (result)
        return result;
    result = validateConflict([
        new Set([
            constants_2.TabOptionItem().id,
            constants_2.TabNonSsoItem().id,
            constants_2.BotOptionItem().id,
            constants_2.MessageExtensionItem().id,
            constants_2.NotificationOptionItem().id,
            constants_2.CommandAndResponseOptionItem().id,
            constants_2.WorkflowOptionItem().id,
        ]),
        new Set([constants_2.TabSPFxItem().id]),
    ], set);
    if (result)
        return result;
    result = validateConflict([new Set([constants_2.TabOptionItem().id]), new Set([constants_2.TabNonSsoItem().id])], set);
    if (result)
        return result;
    result = validateConflict([
        new Set([
            constants_2.TabOptionItem().id,
            constants_2.TabNonSsoItem().id,
            constants_2.TabSPFxItem().id,
            constants_2.BotOptionItem().id,
            constants_2.MessageExtensionItem().id,
            constants_2.NotificationOptionItem().id,
            constants_2.CommandAndResponseOptionItem().id,
            constants_2.WorkflowOptionItem().id,
            constants_2.ExistingTabOptionItem().id,
        ]),
        new Set([constants_2.M365SsoLaunchPageOptionItem().id]),
        new Set([constants_2.M365SearchAppOptionItem().id]),
    ], set);
    return result;
}
exports.validateCapabilities = validateCapabilities;
async function onChangeSelectionForCapabilities(currentSelectedIds, previousSelectedIds) {
    let result = handleSelectionConflict([
        new Set([constants_2.BotOptionItem().id, constants_2.MessageExtensionItem().id]),
        new Set([constants_2.NotificationOptionItem().id]),
        new Set([constants_2.CommandAndResponseOptionItem().id]),
        new Set([constants_2.WorkflowOptionItem().id]),
    ], previousSelectedIds, currentSelectedIds);
    result = handleSelectionConflict([
        new Set([
            constants_2.TabOptionItem().id,
            constants_2.TabNonSsoItem().id,
            constants_2.BotOptionItem().id,
            constants_2.MessageExtensionItem().id,
            constants_2.NotificationOptionItem().id,
            constants_2.CommandAndResponseOptionItem().id,
            constants_2.WorkflowOptionItem().id,
        ]),
        new Set([constants_2.TabSPFxItem().id]),
        new Set([constants_2.ExistingTabOptionItem().id]),
        new Set([constants_2.M365SsoLaunchPageOptionItem().id]),
        new Set([constants_2.M365SearchAppOptionItem().id]),
    ], previousSelectedIds, result);
    result = handleSelectionConflict([new Set([constants_2.TabOptionItem().id]), new Set([constants_2.TabNonSsoItem().id])], previousSelectedIds, result);
    return result;
}
exports.onChangeSelectionForCapabilities = onChangeSelectionForCapabilities;
function QuestionSelectTargetEnvironment() {
    return {
        type: "singleSelect",
        name: CoreQuestionNames.TargetEnvName,
        title: localizeUtils_1.getLocalizedString("core.QuestionSelectTargetEnvironment.title"),
        staticOptions: [],
        skipSingleOption: true,
        forgetLastValue: true,
    };
}
exports.QuestionSelectTargetEnvironment = QuestionSelectTargetEnvironment;
function getQuestionNewTargetEnvironmentName(projectPath) {
    const WINDOWS_MAX_PATH_LENGTH = 260;
    return {
        type: "text",
        name: CoreQuestionNames.NewTargetEnvName,
        title: localizeUtils_1.getLocalizedString("core.getQuestionNewTargetEnvironmentName.title"),
        validation: {
            validFunc: async (input) => {
                const targetEnvName = input;
                const match = targetEnvName.match(environment_1.environmentManager.envNameRegex);
                if (!match) {
                    return localizeUtils_1.getLocalizedString("core.getQuestionNewTargetEnvironmentName.validation1");
                }
                const envFilePath = environment_1.environmentManager.getEnvConfigPath(targetEnvName, projectPath);
                if (os.type() === "Windows_NT" && envFilePath.length >= WINDOWS_MAX_PATH_LENGTH) {
                    return localizeUtils_1.getLocalizedString("core.getQuestionNewTargetEnvironmentName.validation2");
                }
                if (targetEnvName === teamsfx_api_1.LocalEnvironmentName) {
                    return localizeUtils_1.getLocalizedString("core.getQuestionNewTargetEnvironmentName.validation3", teamsfx_api_1.LocalEnvironmentName);
                }
                const envConfigs = await environment_1.environmentManager.listRemoteEnvConfigs(projectPath, true);
                if (envConfigs.isErr()) {
                    return localizeUtils_1.getLocalizedString("core.getQuestionNewTargetEnvironmentName.validation4");
                }
                const found = envConfigs.value.find((env) => env.localeCompare(targetEnvName, undefined, { sensitivity: "base" }) === 0) !== undefined;
                if (found) {
                    return localizeUtils_1.getLocalizedString("core.getQuestionNewTargetEnvironmentName.validation5", targetEnvName);
                }
                else {
                    return undefined;
                }
            },
        },
        placeholder: localizeUtils_1.getLocalizedString("core.getQuestionNewTargetEnvironmentName.placeholder"),
    };
}
exports.getQuestionNewTargetEnvironmentName = getQuestionNewTargetEnvironmentName;
function QuestionSelectSourceEnvironment() {
    return {
        type: "singleSelect",
        name: CoreQuestionNames.SourceEnvName,
        title: localizeUtils_1.getLocalizedString("core.QuestionSelectSourceEnvironment.title"),
        staticOptions: [],
        skipSingleOption: true,
        forgetLastValue: true,
    };
}
exports.QuestionSelectSourceEnvironment = QuestionSelectSourceEnvironment;
function QuestionSelectResourceGroup() {
    return {
        type: "singleSelect",
        name: CoreQuestionNames.TargetResourceGroupName,
        title: localizeUtils_1.getLocalizedString("core.QuestionSelectResourceGroup.title"),
        staticOptions: [],
        skipSingleOption: true,
        forgetLastValue: true,
    };
}
exports.QuestionSelectResourceGroup = QuestionSelectResourceGroup;
function newResourceGroupNameQuestion(existingResourceGroupNames) {
    const question = QuestionNewResourceGroupName();
    question.validation = {
        validFunc: (input) => {
            const name = input;
            // https://docs.microsoft.com/en-us/rest/api/resources/resource-groups/create-or-update#uri-parameters
            const match = name.match(/^[-\w._()]+$/);
            if (!match) {
                return localizeUtils_1.getLocalizedString("core.QuestionNewResourceGroupName.validation");
            }
            // To avoid the issue in CLI that using async func for validation and filter will make users input answers twice,
            // we check the existence of a resource group from the list rather than call the api directly for now.
            // Bug: https://msazure.visualstudio.com/Microsoft%20Teams%20Extensibility/_workitems/edit/15066282
            // GitHub issue: https://github.com/SBoudrias/Inquirer.js/issues/1136
            const maybeExist = existingResourceGroupNames.findIndex((o) => o.toLowerCase() === input.toLowerCase()) >= 0;
            if (maybeExist) {
                return `resource group already exists: ${name}`;
            }
            // const maybeExist = await resourceGroupHelper.checkResourceGroupExistence(name, rmClient);
            // if (maybeExist.isErr()) {
            //   return maybeExist.error.message;
            // }
            // if (maybeExist.value) {
            //   return `resource group already exists: ${name}`;
            // }
            return undefined;
        },
    };
    return question;
}
exports.newResourceGroupNameQuestion = newResourceGroupNameQuestion;
function QuestionNewResourceGroupName() {
    return {
        type: "text",
        name: CoreQuestionNames.NewResourceGroupName,
        title: localizeUtils_1.getLocalizedString("core.QuestionNewResourceGroupName.title"),
        placeholder: localizeUtils_1.getLocalizedString("core.QuestionNewResourceGroupName.placeholder"),
        // default resource group name will change with env name
        forgetLastValue: true,
    };
}
exports.QuestionNewResourceGroupName = QuestionNewResourceGroupName;
function QuestionNewResourceGroupLocation() {
    return {
        type: "singleSelect",
        name: CoreQuestionNames.NewResourceGroupLocation,
        title: localizeUtils_1.getLocalizedString("core.QuestionNewResourceGroupLocation.title"),
        staticOptions: [],
    };
}
exports.QuestionNewResourceGroupLocation = QuestionNewResourceGroupLocation;
function ScratchOptionYesVSC() {
    const label = featureFlags_1.isOfficeAddinEnabled()
        ? localizeUtils_1.getLocalizedString("core.ScratchOptionYesVSC.officeAddin.label")
        : localizeUtils_1.getLocalizedString("core.ScratchOptionYesVSC.label");
    return {
        id: "yes",
        label: `$(new-folder) ${label}`,
        detail: localizeUtils_1.getLocalizedString("core.ScratchOptionYesVSC.detail"),
    };
}
exports.ScratchOptionYesVSC = ScratchOptionYesVSC;
function ScratchOptionNoVSC() {
    return {
        id: "no",
        label: `$(heart) ${localizeUtils_1.getLocalizedString("core.ScratchOptionNoVSC.label")}`,
        detail: localizeUtils_1.getLocalizedString("core.ScratchOptionNoVSC.detail"),
    };
}
exports.ScratchOptionNoVSC = ScratchOptionNoVSC;
function RuntimeOptionNodeJs() {
    return {
        id: "node",
        label: "Node.js",
        detail: localizeUtils_1.getLocalizedString("core.RuntimeOptionNodeJS.detail"),
    };
}
exports.RuntimeOptionNodeJs = RuntimeOptionNodeJs;
function RuntimeOptionDotNet() {
    return {
        id: "dotnet",
        label: ".NET Core",
        detail: localizeUtils_1.getLocalizedString("core.RuntimeOptionDotNet.detail"),
    };
}
exports.RuntimeOptionDotNet = RuntimeOptionDotNet;
function ScratchOptionYes() {
    return {
        id: "yes",
        label: localizeUtils_1.getLocalizedString("core.ScratchOptionYes.label"),
        detail: localizeUtils_1.getLocalizedString("core.ScratchOptionYes.detail"),
    };
}
exports.ScratchOptionYes = ScratchOptionYes;
function ScratchOptionNo() {
    return {
        id: "no",
        label: localizeUtils_1.getLocalizedString("core.ScratchOptionNo.label"),
        detail: localizeUtils_1.getLocalizedString("core.ScratchOptionNo.detail"),
    };
}
exports.ScratchOptionNo = ScratchOptionNo;
// This question should only exist on CLI
function getRuntimeQuestion() {
    return {
        type: "singleSelect",
        name: CoreQuestionNames.Runtime,
        title: localizeUtils_1.getLocalizedString("core.getRuntimeQuestion.title"),
        staticOptions: [RuntimeOptionNodeJs(), RuntimeOptionDotNet()],
        default: RuntimeOptionNodeJs().id,
        placeholder: localizeUtils_1.getLocalizedString("core.getRuntimeQuestion.placeholder"),
    };
}
exports.getRuntimeQuestion = getRuntimeQuestion;
function getCreateNewOrFromSampleQuestion(platform) {
    const staticOptions = [];
    if (platform === teamsfx_api_1.Platform.VSCode) {
        staticOptions.push(ScratchOptionYesVSC());
        if (featureFlags_1.isOfficeAddinEnabled()) {
            staticOptions.push(CreateNewOfficeAddinOption());
        }
        staticOptions.push(ScratchOptionNoVSC());
    }
    else {
        staticOptions.push(ScratchOptionYes());
        staticOptions.push(ScratchOptionNo());
    }
    return {
        type: "singleSelect",
        name: CoreQuestionNames.CreateFromScratch,
        title: localizeUtils_1.getLocalizedString("core.getCreateNewOrFromSampleQuestion.title"),
        staticOptions,
        default: ScratchOptionYes().id,
        placeholder: localizeUtils_1.getLocalizedString("core.getCreateNewOrFromSampleQuestion.placeholder"),
        skipSingleOption: true,
        forgetLastValue: true,
    };
}
exports.getCreateNewOrFromSampleQuestion = getCreateNewOrFromSampleQuestion;
function SampleSelect() {
    return {
        type: "singleSelect",
        name: CoreQuestionNames.Samples,
        title: localizeUtils_1.getLocalizedString("core.SampleSelect.title"),
        staticOptions: samples_1.sampleProvider.SampleCollection.samples.map((sample) => {
            return {
                id: sample.id,
                label: sample.title,
                description: `${sample.time} • ${sample.configuration}`,
                detail: sample.shortDescription,
                data: sample.link,
            };
        }),
        placeholder: localizeUtils_1.getLocalizedString("core.SampleSelect.placeholder"),
        buttons: [
            {
                icon: "library",
                tooltip: localizeUtils_1.getLocalizedString("core.SampleSelect.buttons.viewSamples"),
                command: "fx-extension.openSamples",
            },
        ],
    };
}
exports.SampleSelect = SampleSelect;
function ExistingTabEndpointQuestion() {
    return {
        type: "text",
        name: CoreQuestionNames.ExistingTabEndpoint,
        title: localizeUtils_1.getLocalizedString("core.ExistingTabEndpointQuestion.title"),
        default: "https://localhost:3000",
        placeholder: localizeUtils_1.getLocalizedString("core.ExistingTabEndpointQuestion.placeholder"),
        validation: {
            validFunc: async (endpoint) => {
                const match = endpoint.match(/^https:\/\/[\S]+$/i);
                if (!match) {
                    return localizeUtils_1.getLocalizedString("core.ExistingTabEndpointQuestion.validation");
                }
                return undefined;
            },
        },
    };
}
exports.ExistingTabEndpointQuestion = ExistingTabEndpointQuestion;
exports.defaultTabLocalHostUrl = "https://localhost:53000/index.html#/tab";
const tabsContentUrlQuestion = (tabs) => {
    return {
        type: "multiSelect",
        name: CoreQuestionNames.ReplaceContentUrl,
        title: localizeUtils_1.getLocalizedString("core.updateContentUrlQuestion.title"),
        staticOptions: tabs.map((o) => exports.tabContentUrlOptionItem(o)),
        default: tabs.map((o) => o.name),
        placeholder: localizeUtils_1.getLocalizedString("core.updateUrlQuestion.placeholder"),
        forgetLastValue: true,
    };
};
exports.tabsContentUrlQuestion = tabsContentUrlQuestion;
const tabsWebsitetUrlQuestion = (tabs) => {
    return {
        type: "multiSelect",
        name: CoreQuestionNames.ReplaceWebsiteUrl,
        title: localizeUtils_1.getLocalizedString("core.updateWebsiteUrlQuestion.title"),
        staticOptions: tabs.map((o) => exports.tabWebsiteUrlOptionItem(o)),
        default: tabs.map((o) => o.name),
        placeholder: localizeUtils_1.getLocalizedString("core.updateUrlQuestion.placeholder"),
        forgetLastValue: true,
    };
};
exports.tabsWebsitetUrlQuestion = tabsWebsitetUrlQuestion;
const tabContentUrlOptionItem = (tab) => {
    return {
        id: tab.name,
        label: tab.name,
        detail: localizeUtils_1.getLocalizedString("core.updateContentUrlOption.description", tab.contentUrl, exports.defaultTabLocalHostUrl),
    };
};
exports.tabContentUrlOptionItem = tabContentUrlOptionItem;
const tabWebsiteUrlOptionItem = (tab) => {
    return {
        id: tab.name,
        label: tab.name,
        detail: localizeUtils_1.getLocalizedString("core.updateWebsiteUrlOption.description", tab.websiteUrl, exports.defaultTabLocalHostUrl),
    };
};
exports.tabWebsiteUrlOptionItem = tabWebsiteUrlOptionItem;
const BotIdsQuestion = (botId, messageExtensionBotId) => {
    const defaultIds = [];
    const options = [];
    if (botId) {
        defaultIds.push(developerPortalScaffoldUtils_1.answerToRepaceBotId);
        options.push(exports.botOptionItem(false));
    }
    if (messageExtensionBotId) {
        defaultIds.push(developerPortalScaffoldUtils_1.answerToReplaceMessageExtensionBotId);
        options.push(exports.botOptionItem(true));
    }
    return {
        type: "multiSelect",
        name: CoreQuestionNames.ReplaceBotIds,
        title: localizeUtils_1.getLocalizedString("core.updateBotIdsQuestion.title"),
        staticOptions: options,
        default: defaultIds,
        placeholder: localizeUtils_1.getLocalizedString("core.updateBotIdsQuestion.placeholder"),
        forgetLastValue: true,
    };
};
exports.BotIdsQuestion = BotIdsQuestion;
const botOptionItem = (isMessageExtension) => {
    return {
        id: isMessageExtension ? developerPortalScaffoldUtils_1.answerToReplaceMessageExtensionBotId : developerPortalScaffoldUtils_1.answerToRepaceBotId,
        label: isMessageExtension ? "Message extension" : "Bot",
    };
};
exports.botOptionItem = botOptionItem;
function CreateNewOfficeAddinOption() {
    return {
        id: "newAddin",
        label: `$(new-folder) ${localizeUtils_1.getLocalizedString("core.NewOfficeAddinOptionVSC.label")}`,
        detail: localizeUtils_1.getLocalizedString("core.NewOfficeAddinOptionVSC.detail"),
    };
}
exports.CreateNewOfficeAddinOption = CreateNewOfficeAddinOption;
function createCapabilityForOfficeAddin() {
    return {
        name: CoreQuestionNames.Capabilities,
        title: localizeUtils_1.getLocalizedString("core.createCapabilityQuestion.title"),
        type: "singleSelect",
        staticOptions: [...question_1.OfficeAddinItems(), question_1.ImportAddinProjectItem()],
        placeholder: localizeUtils_1.getLocalizedString("core.createCapabilityQuestion.placeholder"),
        skipSingleOption: true,
    };
}
exports.createCapabilityForOfficeAddin = createCapabilityForOfficeAddin;
//# sourceMappingURL=question.js.map