import { FxError, TelemetryReporter } from "@microsoft/teamsfx-api";
export declare const CoreTelemetryComponentName = "core";
export declare enum CoreTelemetryEvent {
    CreateStart = "create-start",
    Create = "create",
    CreateFromTdpStart = "create-tdp-start"
}
export declare enum CoreTelemetryProperty {
    Component = "component",
    Capabilities = "capabilities",
    Success = "success",
    ErrorCode = "error-code",
    ErrorMessage = "error-message",
    TdpTeamsAppId = "tdp-teams-app-id",
    TdpTeamsAppFeatures = "tdp-teams-app-features"
}
export declare enum CoreTelemetrySuccess {
    Yes = "yes",
    No = "no"
}
export declare function sendErrorTelemetryThenReturnError(eventName: string, error: FxError, reporter?: TelemetryReporter, properties?: {
    [p: string]: string;
}, measurements?: {
    [p: string]: number;
}, errorProps?: string[]): FxError;
//# sourceMappingURL=telemetry.d.ts.map