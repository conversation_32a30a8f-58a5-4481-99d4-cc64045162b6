"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendErrorTelemetryThenReturnError = exports.CoreTelemetrySuccess = exports.CoreTelemetryProperty = exports.CoreTelemetryEvent = exports.CoreTelemetryComponentName = void 0;
const teamsfx_api_1 = require("@microsoft/teamsfx-api");
exports.CoreTelemetryComponentName = "core";
var CoreTelemetryEvent;
(function (CoreTelemetryEvent) {
    CoreTelemetryEvent["CreateStart"] = "create-start";
    CoreTelemetryEvent["Create"] = "create";
    CoreTelemetryEvent["CreateFromTdpStart"] = "create-tdp-start";
})(CoreTelemetryEvent = exports.CoreTelemetryEvent || (exports.CoreTelemetryEvent = {}));
var CoreTelemetryProperty;
(function (CoreTelemetryProperty) {
    CoreTelemetryProperty["Component"] = "component";
    CoreTelemetryProperty["Capabilities"] = "capabilities";
    CoreTelemetryProperty["Success"] = "success";
    CoreTelemetryProperty["ErrorCode"] = "error-code";
    CoreTelemetryProperty["ErrorMessage"] = "error-message";
    CoreTelemetryProperty["TdpTeamsAppId"] = "tdp-teams-app-id";
    CoreTelemetryProperty["TdpTeamsAppFeatures"] = "tdp-teams-app-features";
})(CoreTelemetryProperty = exports.CoreTelemetryProperty || (exports.CoreTelemetryProperty = {}));
var CoreTelemetrySuccess;
(function (CoreTelemetrySuccess) {
    CoreTelemetrySuccess["Yes"] = "yes";
    CoreTelemetrySuccess["No"] = "no";
})(CoreTelemetrySuccess = exports.CoreTelemetrySuccess || (exports.CoreTelemetrySuccess = {}));
function sendErrorTelemetryThenReturnError(eventName, error, reporter, properties, measurements, errorProps) {
    if (!properties) {
        properties = {};
    }
    if (CoreTelemetryProperty.Component in properties === false) {
        properties[CoreTelemetryProperty.Component] = exports.CoreTelemetryComponentName;
    }
    properties[CoreTelemetryProperty.Success] = CoreTelemetrySuccess.No;
    if (error instanceof teamsfx_api_1.UserError) {
        properties["error-type"] = "user";
    }
    else {
        properties["error-type"] = "system";
    }
    properties["error-code"] = `${error.source}.${error.name}`;
    properties["error-message"] = error.message;
    reporter === null || reporter === void 0 ? void 0 : reporter.sendTelemetryErrorEvent(eventName, properties, measurements, errorProps);
    return error;
}
exports.sendErrorTelemetryThenReturnError = sendErrorTelemetryThenReturnError;
//# sourceMappingURL=telemetry.js.map