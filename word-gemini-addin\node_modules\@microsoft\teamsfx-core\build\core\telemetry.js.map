{"version": 3, "file": "telemetry.js", "sourceRoot": "", "sources": ["../../src/core/telemetry.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,wDAA+E;AAElE,QAAA,0BAA0B,GAAG,MAAM,CAAC;AAEjD,IAAY,kBAIX;AAJD,WAAY,kBAAkB;IAC5B,kDAA4B,CAAA;IAC5B,uCAAiB,CAAA;IACjB,6DAAuC,CAAA;AACzC,CAAC,EAJW,kBAAkB,GAAlB,0BAAkB,KAAlB,0BAAkB,QAI7B;AAED,IAAY,qBAQX;AARD,WAAY,qBAAqB;IAC/B,gDAAuB,CAAA;IACvB,sDAA6B,CAAA;IAC7B,4CAAmB,CAAA;IACnB,iDAAwB,CAAA;IACxB,uDAA8B,CAAA;IAC9B,2DAAkC,CAAA;IAClC,uEAA8C,CAAA;AAChD,CAAC,EARW,qBAAqB,GAArB,6BAAqB,KAArB,6BAAqB,QAQhC;AAED,IAAY,oBAGX;AAHD,WAAY,oBAAoB;IAC9B,mCAAW,CAAA;IACX,iCAAS,CAAA;AACX,CAAC,EAHW,oBAAoB,GAApB,4BAAoB,KAApB,4BAAoB,QAG/B;AAED,SAAgB,iCAAiC,CAC/C,SAAiB,EACjB,KAAc,EACd,QAA4B,EAC5B,UAAoC,EACpC,YAAsC,EACtC,UAAqB;IAErB,IAAI,CAAC,UAAU,EAAE;QACf,UAAU,GAAG,EAAE,CAAC;KACjB;IAED,IAAI,qBAAqB,CAAC,SAAS,IAAI,UAAU,KAAK,KAAK,EAAE;QAC3D,UAAU,CAAC,qBAAqB,CAAC,SAAS,CAAC,GAAG,kCAA0B,CAAC;KAC1E;IAED,UAAU,CAAC,qBAAqB,CAAC,OAAO,CAAC,GAAG,oBAAoB,CAAC,EAAE,CAAC;IACpE,IAAI,KAAK,YAAY,uBAAS,EAAE;QAC9B,UAAU,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;KACnC;SAAM;QACL,UAAU,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC;KACrC;IAED,UAAU,CAAC,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;IAC3D,UAAU,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;IAE5C,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,uBAAuB,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;IACnF,OAAO,KAAK,CAAC;AACf,CAAC;AA5BD,8EA4BC"}