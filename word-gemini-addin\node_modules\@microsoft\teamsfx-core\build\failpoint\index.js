"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
// Please don't edit. This file is copied from packages/failpoint-ts/src
// We don't want failpoint-ts to be a package.json dependency.
// We tried to soft link the code, and it works well on linux. However, soft-linked git files don't naturally work on Windows.
tslib_1.__exportStar(require("./runtime"), exports);
tslib_1.__exportStar(require("./marker"), exports);
//# sourceMappingURL=index.js.map