{"version": 3, "file": "runtime.js", "sourceRoot": "", "sources": ["../../src/failpoint/runtime.ts"], "names": [], "mappings": ";AAAA,wEAAwE;AACxE,8DAA8D;AAC9D,8HAA8H;;;AAIjH,QAAA,YAAY,GAAG,oBAAoB,CAAC;AAEjD;;;;;;GAMG;AACH,SAAgB,QAAQ,CAAC,aAAqB;IAC5C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAY,CAAC,CAAC;IACtC,IAAI,CAAC,GAAG,EAAE;QACR,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;QAC5C,OAAO,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;KACjD;IAED,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE5B,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;IAC5D,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,SAAS,CAAC;KAClB;IAED,MAAM,KAAK,GAAsB,UAAU,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAClE,qBAAqB,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAChD,OAAO,KAAK,CAAC;AACf,CAAC;AApBD,4BAoBC;AAED,MAAM,qBAAqB,GAAmC,IAAI,GAAG,EAAE,CAAC;AAExE,SAAgB,mBAAmB;IACjC,qBAAqB,CAAC,KAAK,EAAE,CAAC;AAChC,CAAC;AAFD,kDAEC;AAED,2FAA2F;AAC3F,6CAA6C;AAC7C,SAAS,UAAU,CAAC,IAAY,EAAE,IAAY;IAC5C,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;KACzC;IAED,MAAM,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;IAE1B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;QAC5D,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,mBAAmB,IAAI,EAAE,CAAC,CAAC;KAClE;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC5C,kGAAkG;IAClG,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACzB,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,mBAAmB,IAAI,iBAAiB,CAAC,CAAC;SACjF;QACD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;KAC1C;SAAM,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE;QACnF,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;KACxE;SAAM,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO,EAAE;QAChD,MAAM,MAAM,GAAY,KAAK,KAAK,MAAM,CAAC;QACzC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;KAC3C;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,kBAAkB,IAAI,mBAAmB,IAAI,EAAE,CAAC,CAAC;KAClE;AACH,CAAC"}