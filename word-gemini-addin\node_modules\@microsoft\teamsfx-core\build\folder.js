// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getResourceFolder = exports.getTemplatesFolder = void 0;
const tslib_1 = require("tslib");
const path = tslib_1.__importStar(require("path"));
function getTemplatesFolder() {
    return path.resolve(__dirname, "../templates");
}
exports.getTemplatesFolder = getTemplatesFolder;
function getResourceFolder() {
    return path.resolve(__dirname, "../resource");
}
exports.getResourceFolder = getResourceFolder;
//# sourceMappingURL=folder.js.map