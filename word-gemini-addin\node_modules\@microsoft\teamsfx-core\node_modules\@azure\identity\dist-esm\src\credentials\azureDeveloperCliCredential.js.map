{"version": 3, "file": "azureDeveloperCliCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/azureDeveloperCliCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAE/E,OAAO,EAAE,0BAA0B,EAAE,MAAM,WAAW,CAAC;AACvD,OAAO,aAAa,MAAM,eAAe,CAAC;AAC1C,OAAO,EACL,aAAa,EACb,yBAAyB,EACzB,mCAAmC,GACpC,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,+BAA+B,EAAE,MAAM,oBAAoB,CAAC;AAErE;;;GAGG;AACH,MAAM,CAAC,MAAM,+BAA+B,GAAG;IAC7C;;OAEG;IACH,iBAAiB;QACf,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;YAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC3B,MAAM,IAAI,KAAK,CACb,4EAA4E,CAC7E,CAAC;aACH;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;SAC/B;aAAM;YACL,OAAO,MAAM,CAAC;SACf;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,iBAAiB,CACrB,MAAgB,EAChB,QAAiB,EACjB,OAAgB;QAEhB,IAAI,aAAa,GAAa,EAAE,CAAC;QACjC,IAAI,QAAQ,EAAE;YACZ,aAAa,GAAG,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;SAC3C;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI;gBACF,aAAa,CAAC,QAAQ,CACpB,KAAK,EACL;oBACE,MAAM;oBACN,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,GAAG,MAAM,CAAC,MAAM,CACd,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,EAC1D,EAAE,CACH;oBACD,GAAG,aAAa;iBACjB,EACD;oBACE,GAAG,EAAE,+BAA+B,CAAC,iBAAiB,EAAE;oBACxD,OAAO;iBACR,EACD,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;oBACxB,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;gBACrC,CAAC,CACF,CAAC;aACH;YAAC,OAAO,GAAQ,EAAE;gBACjB,MAAM,CAAC,GAAG,CAAC,CAAC;aACb;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC;AAEF,MAAM,MAAM,GAAG,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;AAE/D;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,OAAO,2BAA2B;IAKtC;;;;;;;OAOG;IACH,YAAY,OAA4C;QACtD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,EAAE;YACrB,aAAa,CAAC,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC;SACnC;QACD,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QACF,IAAI,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,CAAC;IAC7C,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,UAA2B,EAAE;QAE7B,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,OAAO,EACP,IAAI,CAAC,4BAA4B,CAClC,CAAC;QACF,IAAI,QAAQ,EAAE;YACZ,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACjC;QACD,IAAI,SAAmB,CAAC;QACxB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,SAAS,GAAG,CAAC,MAAM,CAAC,CAAC;SACtB;aAAM;YACL,SAAS,GAAG,MAAM,CAAC;SACpB;QACD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,MAAM,EAAE,CAAC,CAAC;QAEnD,OAAO,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EAAE,OAAO,EAAE,KAAK,IAAI,EAAE;;YACrF,IAAI;gBACF,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBAC1B,+BAA+B,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,+BAA+B,CAAC,iBAAiB,CACjE,SAAS,EACT,QAAQ,EACR,IAAI,CAAC,OAAO,CACb,CAAC;gBACF,MAAM,kBAAkB,GACtB,CAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,KAAK,CAAC,yCAAyC,CAAC;qBAC5D,MAAA,GAAG,CAAC,MAAM,0CAAE,KAAK,CAAC,8CAA8C,CAAC,CAAA,CAAC;gBACpE,MAAM,iBAAiB,GACrB,CAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,KAAK,CAAC,mBAAmB,CAAC;qBACtC,MAAA,GAAG,CAAC,MAAM,0CAAE,UAAU,CAAC,yBAAyB,CAAC,CAAA,CAAC;gBAEpD,IAAI,iBAAiB,IAAI,CAAC,GAAG,CAAC,KAAK,IAAK,GAAG,CAAC,KAAa,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE;oBAC5E,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,wKAAwK,CACzK,CAAC;oBACF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;oBACjD,MAAM,KAAK,CAAC;iBACb;gBAED,IAAI,kBAAkB,EAAE;oBACtB,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,+NAA+N,CAChO,CAAC;oBACF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;oBACjD,MAAM,KAAK,CAAC;iBACb;gBAED,IAAI;oBACF,MAAM,IAAI,GAAyC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBAC1E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC5C,OAAO;wBACL,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,kBAAkB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;qBACvD,CAAC;iBACH;gBAAC,OAAO,CAAM,EAAE;oBACf,IAAI,GAAG,CAAC,MAAM,EAAE;wBACd,MAAM,IAAI,0BAA0B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;qBAClD;oBACD,MAAM,CAAC,CAAC;iBACT;aACF;YAAC,OAAO,GAAQ,EAAE;gBACjB,MAAM,KAAK,GACT,GAAG,CAAC,IAAI,KAAK,4BAA4B;oBACvC,CAAC,CAAC,GAAG;oBACL,CAAC,CAAC,IAAI,0BAA0B,CAC3B,GAAa,CAAC,OAAO,IAAI,yDAAyD,CACpF,CAAC;gBACR,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBACjD,MAAM,KAAK,CAAC;aACb;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError, formatSuccess } from \"../util/logging\";\nimport { AzureDeveloperCliCredentialOptions } from \"./azureDeveloperCliCredentialOptions\";\nimport { CredentialUnavailableError } from \"../errors\";\nimport child_process from \"child_process\";\nimport {\n  checkTenantId,\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { tracingClient } from \"../util/tracing\";\nimport { ensureValidScopeForDevTimeCreds } from \"../util/scopeUtils\";\n\n/**\n * Mockable reference to the Developer CLI credential cliCredentialFunctions\n * @internal\n */\nexport const developerCliCredentialInternals = {\n  /**\n   * @internal\n   */\n  getSafeWorkingDir(): string {\n    if (process.platform === \"win32\") {\n      if (!process.env.SystemRoot) {\n        throw new Error(\n          \"Azure Developer CLI credential expects a 'SystemRoot' environment variable\"\n        );\n      }\n      return process.env.SystemRoot;\n    } else {\n      return \"/bin\";\n    }\n  },\n\n  /**\n   * Gets the access token from Azure Developer CLI\n   * @param scopes - The scopes to use when getting the token\n   * @internal\n   */\n  async getAzdAccessToken(\n    scopes: string[],\n    tenantId?: string,\n    timeout?: number\n  ): Promise<{ stdout: string; stderr: string; error: Error | null }> {\n    let tenantSection: string[] = [];\n    if (tenantId) {\n      tenantSection = [\"--tenant-id\", tenantId];\n    }\n    return new Promise((resolve, reject) => {\n      try {\n        child_process.execFile(\n          \"azd\",\n          [\n            \"auth\",\n            \"token\",\n            \"--output\",\n            \"json\",\n            ...scopes.reduce<string[]>(\n              (previous, current) => previous.concat(\"--scope\", current),\n              []\n            ),\n            ...tenantSection,\n          ],\n          {\n            cwd: developerCliCredentialInternals.getSafeWorkingDir(),\n            timeout,\n          },\n          (error, stdout, stderr) => {\n            resolve({ stdout, stderr, error });\n          }\n        );\n      } catch (err: any) {\n        reject(err);\n      }\n    });\n  },\n};\n\nconst logger = credentialLogger(\"AzureDeveloperCliCredential\");\n\n/**\n * Azure Developer CLI is a command-line interface tool that allows developers to create, manage, and deploy\n * resources in Azure. It's built on top of the Azure CLI and provides additional functionality specific\n * to Azure developers. It allows users to authenticate as a user and/or a service principal against\n * <a href=\"https://learn.microsoft.com/azure/active-directory/fundamentals/\">Azure Active Directory (Azure AD)\n * </a>. The AzureDeveloperCliCredential authenticates in a development environment and acquires a token on behalf of\n * the logged-in user or service principal in the Azure Developer CLI. It acts as the Azure Developer CLI logged in user or\n * service principal and executes an Azure CLI command underneath to authenticate the application against\n * Azure Active Directory.\n *\n * <h2> Configure AzureDeveloperCliCredential </h2>\n *\n * To use this credential, the developer needs to authenticate locally in Azure Developer CLI using one of the\n * commands below:\n *\n * <ol>\n *     <li>Run \"azd auth login\" in Azure Developer CLI to authenticate interactively as a user.</li>\n *     <li>Run \"azd auth login --client-id clientID --client-secret clientSecret\n *     --tenant-id tenantID\" to authenticate as a service principal.</li>\n * </ol>\n *\n * You may need to repeat this process after a certain time period, depending on the refresh token validity in your\n * organization. Generally, the refresh token validity period is a few weeks to a few months.\n * AzureDeveloperCliCredential will prompt you to sign in again.\n */\nexport class AzureDeveloperCliCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private timeout?: number;\n\n  /**\n   * Creates an instance of the {@link AzureDeveloperCliCredential}.\n   *\n   * To use this credential, ensure that you have already logged\n   * in via the 'azd' tool using the command \"azd auth login\" from the commandline.\n   *\n   * @param options - Options, to optionally allow multi-tenant requests.\n   */\n  constructor(options?: AzureDeveloperCliCredentialOptions) {\n    if (options?.tenantId) {\n      checkTenantId(logger, options?.tenantId);\n      this.tenantId = options?.tenantId;\n    }\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n    this.timeout = options?.processTimeoutInMs;\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AccessToken> {\n    const tenantId = processMultiTenantRequest(\n      this.tenantId,\n      options,\n      this.additionallyAllowedTenantIds\n    );\n    if (tenantId) {\n      checkTenantId(logger, tenantId);\n    }\n    let scopeList: string[];\n    if (typeof scopes === \"string\") {\n      scopeList = [scopes];\n    } else {\n      scopeList = scopes;\n    }\n    logger.getToken.info(`Using the scopes ${scopes}`);\n\n    return tracingClient.withSpan(`${this.constructor.name}.getToken`, options, async () => {\n      try {\n        scopeList.forEach((scope) => {\n          ensureValidScopeForDevTimeCreds(scope, logger);\n        });\n        const obj = await developerCliCredentialInternals.getAzdAccessToken(\n          scopeList,\n          tenantId,\n          this.timeout\n        );\n        const isNotLoggedInError =\n          obj.stderr?.match(\"not logged in, run `azd login` to login\") ||\n          obj.stderr?.match(\"not logged in, run `azd auth login` to login\");\n        const isNotInstallError =\n          obj.stderr?.match(\"azd:(.*)not found\") ||\n          obj.stderr?.startsWith(\"'azd' is not recognized\");\n\n        if (isNotInstallError || (obj.error && (obj.error as any).code === \"ENOENT\")) {\n          const error = new CredentialUnavailableError(\n            \"Azure Developer CLI couldn't be found. To mitigate this issue, see the troubleshooting guidelines at https://aka.ms/azsdk/js/identity/azdevclicredential/troubleshoot.\"\n          );\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n\n        if (isNotLoggedInError) {\n          const error = new CredentialUnavailableError(\n            \"Please run 'azd auth login' from a command prompt to authenticate before using this credential. For more information, see the troubleshooting guidelines at https://aka.ms/azsdk/js/identity/azdevclicredential/troubleshoot.\"\n          );\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n\n        try {\n          const resp: { token: string; expiresOn: string } = JSON.parse(obj.stdout);\n          logger.getToken.info(formatSuccess(scopes));\n          return {\n            token: resp.token,\n            expiresOnTimestamp: new Date(resp.expiresOn).getTime(),\n          };\n        } catch (e: any) {\n          if (obj.stderr) {\n            throw new CredentialUnavailableError(obj.stderr);\n          }\n          throw e;\n        }\n      } catch (err: any) {\n        const error =\n          err.name === \"CredentialUnavailableError\"\n            ? err\n            : new CredentialUnavailableError(\n                (err as Error).message || \"Unknown error while trying to retrieve the access token\"\n              );\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n    });\n  }\n}\n"]}