{"version": 3, "file": "azurePowerShellCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/azurePowerShellCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAC/E,OAAO,EAAE,+BAA+B,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEvF,OAAO,EAAE,0BAA0B,EAAE,MAAM,WAAW,CAAC;AACvD,OAAO,EACL,aAAa,EACb,yBAAyB,EACzB,mCAAmC,GACpC,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,MAAM,MAAM,GAAG,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;AAE7D,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;AAE/C;;;;GAIG;AACH,MAAM,UAAU,aAAa,CAAC,WAAmB;IAC/C,IAAI,SAAS,EAAE;QACb,OAAO,GAAG,WAAW,MAAM,CAAC;KAC7B;SAAM;QACL,OAAO,WAAW,CAAC;KACpB;AACH,CAAC;AAED;;;;GAIG;AACH,KAAK,UAAU,WAAW,CAAC,QAAoB,EAAE,OAAgB;IAC/D,MAAM,OAAO,GAAa,EAAE,CAAC;IAE7B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;QAC9B,MAAM,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC,GAAG,OAAO,CAAC;QACtC,MAAM,MAAM,GAAG,CAAC,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE;YAC5D,QAAQ,EAAE,MAAM;YAChB,OAAO;SACR,CAAC,CAAW,CAAC;QACd,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACtB;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B,KAAK,EAAE,gCAAgC;IACvC,SAAS,EACP,uIAAuI;CAC1I,CAAC;AAEF;;;GAGG;AACH,MAAM,CAAC,MAAM,6BAA6B,GAAG;IAC3C,KAAK,EACH,8FAA8F;IAChG,SAAS,EAAE,4KAA4K;IACvL,YAAY,EAAE,4FAA4F;CAC3G,CAAC;AAEF,mDAAmD;AACnD,MAAM,YAAY,GAA4C,CAAC,GAAU,EAAE,EAAE,CAC3E,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,gBAAgB,CAAC,KAAK,MAAM,CAAC,CAAC;AAEzD,qDAAqD;AACrD,MAAM,mBAAmB,GAA4C,CAAC,GAAU,EAAE,EAAE,CAClF,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;AAEhD;;;;GAIG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;AAEpD,IAAI,SAAS,EAAE;IACb,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC;CAChD;AAED;;;;GAIG;AACH,MAAM,OAAO,yBAAyB;IAKpC;;;;;;;;;;OAUG;IACH,YAAY,OAA0C;QACpD,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,EAAE;YACrB,aAAa,CAAC,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC;SACnC;QACD,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QACF,IAAI,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,6BAA6B,CACzC,QAAgB,EAChB,QAAiB,EACjB,OAAgB;QAEhB,uDAAuD;QACvD,KAAK,MAAM,iBAAiB,IAAI,CAAC,GAAG,YAAY,CAAC,EAAE;YACjD,IAAI;gBACF,MAAM,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;aACzD;YAAC,OAAO,CAAM,EAAE;gBACf,gFAAgF;gBAChF,YAAY,CAAC,KAAK,EAAE,CAAC;gBACrB,SAAS;aACV;YAED,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,QAAQ,EAAE;gBACZ,aAAa,GAAG,cAAc,QAAQ,GAAG,CAAC;aAC3C;YAED,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC;gBAChC;oBACE,iBAAiB;oBACjB,YAAY;oBACZ,iBAAiB;oBACjB,UAAU;oBACV,2DAA2D;iBAC5D;gBACD;oBACE,iBAAiB;oBACjB,YAAY;oBACZ,iBAAiB;oBACjB,UAAU;oBACV,qBAAqB,aAAa,kBAAkB,QAAQ,oBAAoB;iBACjF;aACF,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI;gBACF,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;aAC3B;YAAC,OAAO,CAAM,EAAE;gBACf,MAAM,IAAI,KAAK,CAAC,8DAA8D,MAAM,EAAE,CAAC,CAAC;aACzF;SACF;QAED,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;IAC9F,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,UAA2B,EAAE;QAE7B,OAAO,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EAAE,OAAO,EAAE,KAAK,IAAI,EAAE;YACrF,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,OAAO,EACP,IAAI,CAAC,4BAA4B,CAClC,CAAC;YACF,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9D,IAAI,QAAQ,EAAE;gBACZ,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;aACjC;YACD,IAAI;gBACF,+BAA+B,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC/C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,KAAK,EAAE,CAAC,CAAC;gBACjD,MAAM,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACzC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5F,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC5C,OAAO;oBACL,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,kBAAkB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;iBAC3D,CAAC;aACH;YAAC,OAAO,GAAQ,EAAE;gBACjB,IAAI,mBAAmB,CAAC,GAAG,CAAC,EAAE;oBAC5B,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAAC,6BAA6B,CAAC,SAAS,CAAC,CAAC;oBACtF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBAChD,MAAM,KAAK,CAAC;iBACb;qBAAM,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;oBAC5B,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;oBAClF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBAChD,MAAM,KAAK,CAAC;iBACb;gBACD,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,GAAG,GAAG,KAAK,6BAA6B,CAAC,YAAY,EAAE,CACxD,CAAC;gBACF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;gBAChD,MAAM,KAAK,CAAC;aACb;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError, formatSuccess } from \"../util/logging\";\nimport { ensureValidScopeForDevTimeCreds, getScopeResource } from \"../util/scopeUtils\";\nimport { AzurePowerShellCredentialOptions } from \"./azurePowerShellCredentialOptions\";\nimport { CredentialUnavailableError } from \"../errors\";\nimport {\n  checkTenantId,\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { processUtils } from \"../util/processUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"AzurePowerShellCredential\");\n\nconst isWindows = process.platform === \"win32\";\n\n/**\n * Returns a platform-appropriate command name by appending \".exe\" on Windows.\n *\n * @internal\n */\nexport function formatCommand(commandName: string): string {\n  if (isWindows) {\n    return `${commandName}.exe`;\n  } else {\n    return commandName;\n  }\n}\n\n/**\n * Receives a list of commands to run, executes them, then returns the outputs.\n * If anything fails, an error is thrown.\n * @internal\n */\nasync function runCommands(commands: string[][], timeout?: number): Promise<string[]> {\n  const results: string[] = [];\n\n  for (const command of commands) {\n    const [file, ...parameters] = command;\n    const result = (await processUtils.execFile(file, parameters, {\n      encoding: \"utf8\",\n      timeout,\n    })) as string;\n    results.push(result);\n  }\n\n  return results;\n}\n\n/**\n * Known PowerShell errors\n * @internal\n */\nexport const powerShellErrors = {\n  login: \"Run Connect-AzAccount to login\",\n  installed:\n    \"The specified module 'Az.Accounts' with version '2.2.0' was not loaded because no valid module file was found in any module directory\",\n};\n\n/**\n * Messages to use when throwing in this credential.\n * @internal\n */\nexport const powerShellPublicErrorMessages = {\n  login:\n    \"Please run 'Connect-AzAccount' from PowerShell to authenticate before using this credential.\",\n  installed: `The 'Az.Account' module >= 2.2.0 is not installed. Install the Azure Az PowerShell module with: \"Install-Module -Name Az -Scope CurrentUser -Repository PSGallery -Force\".`,\n  troubleshoot: `To troubleshoot, visit https://aka.ms/azsdk/js/identity/powershellcredential/troubleshoot.`,\n};\n\n// PowerShell Azure User not logged in error check.\nconst isLoginError: (err: Error) => RegExpMatchArray | null = (err: Error) =>\n  err.message.match(`(.*)${powerShellErrors.login}(.*)`);\n\n// Az Module not Installed in Azure PowerShell check.\nconst isNotInstalledError: (err: Error) => RegExpMatchArray | null = (err: Error) =>\n  err.message.match(powerShellErrors.installed);\n\n/**\n * The PowerShell commands to be tried, in order.\n *\n * @internal\n */\nexport const commandStack = [formatCommand(\"pwsh\")];\n\nif (isWindows) {\n  commandStack.push(formatCommand(\"powershell\"));\n}\n\n/**\n * This credential will use the currently logged-in user information from the\n * Azure PowerShell module. To do so, it will read the user access token and\n * expire time with Azure PowerShell command `Get-AzAccessToken -ResourceUrl {ResourceScope}`\n */\nexport class AzurePowerShellCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private timeout?: number;\n\n  /**\n   * Creates an instance of the {@link AzurePowerShellCredential}.\n   *\n   * To use this credential:\n   * - Install the Azure Az PowerShell module with:\n   *   `Install-Module -Name Az -Scope CurrentUser -Repository PSGallery -Force`.\n   * - You have already logged in to Azure PowerShell using the command\n   * `Connect-AzAccount` from the command line.\n   *\n   * @param options - Options, to optionally allow multi-tenant requests.\n   */\n  constructor(options?: AzurePowerShellCredentialOptions) {\n    if (options?.tenantId) {\n      checkTenantId(logger, options?.tenantId);\n      this.tenantId = options?.tenantId;\n    }\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n    this.timeout = options?.processTimeoutInMs;\n  }\n\n  /**\n   * Gets the access token from Azure PowerShell\n   * @param resource - The resource to use when getting the token\n   */\n  private async getAzurePowerShellAccessToken(\n    resource: string,\n    tenantId?: string,\n    timeout?: number\n  ): Promise<{ Token: string; ExpiresOn: string }> {\n    // Clone the stack to avoid mutating it while iterating\n    for (const powerShellCommand of [...commandStack]) {\n      try {\n        await runCommands([[powerShellCommand, \"/?\"]], timeout);\n      } catch (e: any) {\n        // Remove this credential from the original stack so that we don't try it again.\n        commandStack.shift();\n        continue;\n      }\n\n      let tenantSection = \"\";\n      if (tenantId) {\n        tenantSection = `-TenantId \"${tenantId}\"`;\n      }\n\n      const results = await runCommands([\n        [\n          powerShellCommand,\n          \"-NoProfile\",\n          \"-NonInteractive\",\n          \"-Command\",\n          \"Import-Module Az.Accounts -MinimumVersion 2.2.0 -PassThru\",\n        ],\n        [\n          powerShellCommand,\n          \"-NoProfile\",\n          \"-NonInteractive\",\n          \"-Command\",\n          `Get-AzAccessToken ${tenantSection} -ResourceUrl \"${resource}\" | ConvertTo-Json`,\n        ],\n      ]);\n\n      const result = results[1];\n      try {\n        return JSON.parse(result);\n      } catch (e: any) {\n        throw new Error(`Unable to parse the output of PowerShell. Received output: ${result}`);\n      }\n    }\n\n    throw new Error(`Unable to execute PowerShell. Ensure that it is installed in your system`);\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If the authentication cannot be performed through PowerShell, a {@link CredentialUnavailableError} will be thrown.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AccessToken> {\n    return tracingClient.withSpan(`${this.constructor.name}.getToken`, options, async () => {\n      const tenantId = processMultiTenantRequest(\n        this.tenantId,\n        options,\n        this.additionallyAllowedTenantIds\n      );\n      const scope = typeof scopes === \"string\" ? scopes : scopes[0];\n      if (tenantId) {\n        checkTenantId(logger, tenantId);\n      }\n      try {\n        ensureValidScopeForDevTimeCreds(scope, logger);\n        logger.getToken.info(`Using the scope ${scope}`);\n        const resource = getScopeResource(scope);\n        const response = await this.getAzurePowerShellAccessToken(resource, tenantId, this.timeout);\n        logger.getToken.info(formatSuccess(scopes));\n        return {\n          token: response.Token,\n          expiresOnTimestamp: new Date(response.ExpiresOn).getTime(),\n        };\n      } catch (err: any) {\n        if (isNotInstalledError(err)) {\n          const error = new CredentialUnavailableError(powerShellPublicErrorMessages.installed);\n          logger.getToken.info(formatError(scope, error));\n          throw error;\n        } else if (isLoginError(err)) {\n          const error = new CredentialUnavailableError(powerShellPublicErrorMessages.login);\n          logger.getToken.info(formatError(scope, error));\n          throw error;\n        }\n        const error = new CredentialUnavailableError(\n          `${err}. ${powerShellPublicErrorMessages.troubleshoot}`\n        );\n        logger.getToken.info(formatError(scope, error));\n        throw error;\n      }\n    });\n  }\n}\n"]}