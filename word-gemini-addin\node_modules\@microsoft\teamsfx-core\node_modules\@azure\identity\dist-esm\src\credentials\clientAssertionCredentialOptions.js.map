{"version": 3, "file": "clientAssertionCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/clientAssertionCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AuthorityValidationOptions } from \"./authorityValidationOptions\";\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\n\n/**\n * Options for the {@link ClientAssertionCredential}\n */\nexport interface ClientAssertionCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    AuthorityValidationOptions {}\n"]}