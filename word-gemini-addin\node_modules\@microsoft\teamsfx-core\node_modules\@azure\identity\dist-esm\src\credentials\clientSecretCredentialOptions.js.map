{"version": 3, "file": "clientSecretCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/clientSecretCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AuthorityValidationOptions } from \"./authorityValidationOptions\";\nimport { CredentialPersistenceOptions } from \"./credentialPersistenceOptions\";\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\n\n/**\n * Optional parameters for the {@link ClientSecretCredential} class.\n */\nexport interface ClientSecretCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    CredentialPersistenceOptions,\n    AuthorityValidationOptions {\n  // TODO: Export again once we're ready to release this feature.\n  // /**\n  //  * Specifies a regional authority. Please refer to the {@link RegionalAuthority} type for the accepted values.\n  //  * If {@link RegionalAuthority.AutoDiscoverRegion} is specified, we will try to discover the regional authority endpoint.\n  //  * If the property is not specified, the credential uses the global authority endpoint.\n  //  */\n  // regionalAuthority?: string;\n}\n"]}