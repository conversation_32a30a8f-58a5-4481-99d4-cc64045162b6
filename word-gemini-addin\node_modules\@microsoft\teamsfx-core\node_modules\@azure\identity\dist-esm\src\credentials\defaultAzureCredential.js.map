{"version": 3, "file": "defaultAzureCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/defaultAzureCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAOlC,OAAO,EACL,yBAAyB,GAG1B,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AACxE,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAEhE,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAC5E,OAAO,EAAE,0BAA0B,EAAE,MAAM,8BAA8B,CAAC;AAe1E;;;;;GAKG;AACH,MAAM,OAAO,gCAAiC,SAAQ,yBAAyB;IAK7E,2DAA2D;IAC3D,sIAAsI;IACtI,YAAY,OAAuC;;QACjD,MAAM,uBAAuB,GAC3B,MAAC,OAAiD,aAAjD,OAAO,uBAAP,OAAO,CAA4C,uBAAuB,mCAC3E,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;QAC9B,MAAM,wBAAwB,GAC5B,MAAC,OAAiD,aAAjD,OAAO,uBAAP,OAAO,CAA4C,wBAAwB,mCAC5E,uBAAuB,CAAC;QAC1B,MAAM,iBAAiB,GAAI,OAAmD,aAAnD,OAAO,uBAAP,OAAO,CAC9B,yBAAyB,CAAC;QAC9B,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;QAC5D,MAAM,QAAQ,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,mCAAI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;QAClE,yFAAyF;QACzF,IAAI,iBAAiB,EAAE;YACrB,MAAM,gCAAgC,mCACjC,OAAO,KACV,UAAU,EAAE,iBAAiB,GAC9B,CAAC;YACF,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACzC;aAAM,IAAI,YAAY,IAAI,wBAAwB,EAAE;YACnD,MAAM,iCAAiC,mCAClC,OAAO,KACV,QAAQ,EAAE,QAAQ,GACnB,CAAC;YACF,KAAK,CAAC,wBAAwB,EAAE,iCAAiC,CAAC,CAAC;SACpE;aAAM,IAAI,uBAAuB,EAAE;YAClC,MAAM,4BAA4B,mCAC7B,OAAO,KACV,QAAQ,EAAE,uBAAuB,GAClC,CAAC;YACF,KAAK,CAAC,4BAA4B,CAAC,CAAC;SACrC;aAAM;YACL,KAAK,CAAC,OAAO,CAAC,CAAC;SAChB;IACH,CAAC;CACF;AACD;;;;;GAKG;AACH,MAAM,OAAO,iCAAkC,SAAQ,0BAA0B;IAG/E,2DAA2D;IAC3D,sIAAsI;IACtI,YAAY,OAAuC;;QACjD,MAAM,uBAAuB,GAC3B,MAAC,OAAiD,aAAjD,OAAO,uBAAP,OAAO,CAA4C,uBAAuB,mCAC3E,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;QAC9B,MAAM,wBAAwB,GAC5B,MAAC,OAAiD,aAAjD,OAAO,uBAAP,OAAO,CAA4C,wBAAwB,mCAC5E,uBAAuB,CAAC;QAC1B,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;QAC5D,MAAM,QAAQ,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,mCAAI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;QAClE,IAAI,YAAY,IAAI,wBAAwB,EAAE;YAC5C,MAAM,iCAAiC,mCAClC,OAAO,KACV,QAAQ,EACR,QAAQ,EAAE,wBAAwB,EAClC,aAAa,EAAE,YAAY,GAC5B,CAAC;YACF,KAAK,CAAC,iCAAiC,CAAC,CAAC;SAC1C;aAAM,IAAI,QAAQ,EAAE;YACnB,MAAM,mCAAmC,mCACpC,OAAO,KACV,QAAQ,GACT,CAAC;YACF,KAAK,CAAC,mCAAmC,CAAC,CAAC;SAC5C;aAAM;YACL,KAAK,CAAC,OAA4C,CAAC,CAAC;SACrD;IACH,CAAC;CACF;AAED,MAAM,OAAO,kCAAmC,SAAQ,2BAA2B;IACjF,YAAY,OAAuC;QACjD,KAAK,iBACH,kBAAkB,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,IAC5C,OAAO,EACV,CAAC;IACL,CAAC;CACF;AACD,MAAM,OAAO,yBAA0B,SAAQ,kBAAkB;IAC/D,YAAY,OAAuC;QACjD,KAAK,iBACH,kBAAkB,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,IAC5C,OAAO,EACV,CAAC;IACL,CAAC;CACF;AAED,MAAM,OAAO,gCAAiC,SAAQ,yBAAyB;IAC7E,YAAY,OAAuC;QACjD,KAAK,iBACH,kBAAkB,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,IAC5C,OAAO,EACV,CAAC;IACL,CAAC;CACF;AAED,MAAM,CAAC,MAAM,kBAAkB,GAAmC;IAChE,qBAAqB;IACrB,iCAAiC;IACjC,gCAAgC;IAChC,yBAAyB;IACzB,gCAAgC;IAChC,kCAAkC;CACnC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,sBAAuB,SAAQ,sBAAsB;IAmEhE,YACE,OAGyC;QAEzC,KAAK,CAAC,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  DefaultAzureCredentialClientIdOptions,\n  DefaultAzureCredentialOptions,\n  DefaultAzureCredentialResourceIdOptions,\n} from \"./defaultAzureCredentialOptions\";\nimport {\n  ManagedIdentityCredential,\n  ManagedIdentityCredentialClientIdOptions,\n  ManagedIdentityCredentialResourceIdOptions,\n} from \"./managedIdentityCredential\";\nimport { AzureCliCredential } from \"./azureCliCredential\";\nimport { AzurePowerShellCredential } from \"./azurePowerShellCredential\";\nimport { ChainedTokenCredential } from \"./chainedTokenCredential\";\nimport { EnvironmentCredential } from \"./environmentCredential\";\nimport { TokenCredential } from \"@azure/core-auth\";\nimport { AzureDeveloperCliCredential } from \"./azureDeveloperCliCredential\";\nimport { WorkloadIdentityCredential } from \"./workloadIdentityCredential\";\nimport { WorkloadIdentityCredentialOptions } from \"./workloadIdentityCredentialOptions\";\n\n/**\n * The type of a class that implements TokenCredential and accepts either\n * {@link DefaultAzureCredentialClientIdOptions} or\n * {@link DefaultAzureCredentialResourceIdOptions} or\n * {@link DefaultAzureCredentialOptions}.\n */\ninterface DefaultCredentialConstructor {\n  new (options?: DefaultAzureCredentialOptions): TokenCredential;\n  new (options?: DefaultAzureCredentialResourceIdOptions): TokenCredential;\n  new (options?: DefaultAzureCredentialClientIdOptions): TokenCredential;\n}\n\n/**\n * A shim around ManagedIdentityCredential that adapts it to accept\n * `DefaultAzureCredentialOptions`.\n *\n * @internal\n */\nexport class DefaultManagedIdentityCredential extends ManagedIdentityCredential {\n  // Constructor overload with just client id options\n  constructor(options?: DefaultAzureCredentialClientIdOptions);\n  // Constructor overload with just resource id options\n  constructor(options?: DefaultAzureCredentialResourceIdOptions);\n  // Constructor overload with just the other default options\n  // Last constructor overload with Union of all options not required since the above two constructor overloads have optional properties\n  constructor(options?: DefaultAzureCredentialOptions) {\n    const managedIdentityClientId =\n      (options as DefaultAzureCredentialClientIdOptions)?.managedIdentityClientId ??\n      process.env.AZURE_CLIENT_ID;\n    const workloadIdentityClientId =\n      (options as DefaultAzureCredentialClientIdOptions)?.workloadIdentityClientId ??\n      managedIdentityClientId;\n    const managedResourceId = (options as DefaultAzureCredentialResourceIdOptions)\n      ?.managedIdentityResourceId;\n    const workloadFile = process.env.AZURE_FEDERATED_TOKEN_FILE;\n    const tenantId = options?.tenantId ?? process.env.AZURE_TENANT_ID;\n    // ManagedIdentityCredential throws if both the resourceId and the clientId are provided.\n    if (managedResourceId) {\n      const managedIdentityResourceIdOptions: ManagedIdentityCredentialResourceIdOptions = {\n        ...options,\n        resourceId: managedResourceId,\n      };\n      super(managedIdentityResourceIdOptions);\n    } else if (workloadFile && workloadIdentityClientId) {\n      const workloadIdentityCredentialOptions: DefaultAzureCredentialOptions = {\n        ...options,\n        tenantId: tenantId,\n      };\n      super(workloadIdentityClientId, workloadIdentityCredentialOptions);\n    } else if (managedIdentityClientId) {\n      const managedIdentityClientOptions: ManagedIdentityCredentialClientIdOptions = {\n        ...options,\n        clientId: managedIdentityClientId,\n      };\n      super(managedIdentityClientOptions);\n    } else {\n      super(options);\n    }\n  }\n}\n/**\n * A shim around WorkloadIdentityCredential that adapts it to accept\n * `DefaultAzureCredentialOptions`.\n *\n * @internal\n */\nexport class DefaultWorkloadIdentityCredential extends WorkloadIdentityCredential {\n  // Constructor overload with just client id options\n  constructor(options?: DefaultAzureCredentialClientIdOptions);\n  // Constructor overload with just the other default options\n  // Last constructor overload with Union of all options not required since the above two constructor overloads have optional properties\n  constructor(options?: DefaultAzureCredentialOptions) {\n    const managedIdentityClientId =\n      (options as DefaultAzureCredentialClientIdOptions)?.managedIdentityClientId ??\n      process.env.AZURE_CLIENT_ID;\n    const workloadIdentityClientId =\n      (options as DefaultAzureCredentialClientIdOptions)?.workloadIdentityClientId ??\n      managedIdentityClientId;\n    const workloadFile = process.env.AZURE_FEDERATED_TOKEN_FILE;\n    const tenantId = options?.tenantId ?? process.env.AZURE_TENANT_ID;\n    if (workloadFile && workloadIdentityClientId) {\n      const workloadIdentityCredentialOptions: WorkloadIdentityCredentialOptions = {\n        ...options,\n        tenantId,\n        clientId: workloadIdentityClientId,\n        tokenFilePath: workloadFile,\n      };\n      super(workloadIdentityCredentialOptions);\n    } else if (tenantId) {\n      const workloadIdentityClientTenantOptions: WorkloadIdentityCredentialOptions = {\n        ...options,\n        tenantId,\n      };\n      super(workloadIdentityClientTenantOptions);\n    } else {\n      super(options as WorkloadIdentityCredentialOptions);\n    }\n  }\n}\n\nexport class DefaultAzureDeveloperCliCredential extends AzureDeveloperCliCredential {\n  constructor(options?: DefaultAzureCredentialOptions) {\n    super({\n      processTimeoutInMs: options?.processTimeoutInMs,\n      ...options,\n    });\n  }\n}\nexport class DefaultAzureCliCredential extends AzureCliCredential {\n  constructor(options?: DefaultAzureCredentialOptions) {\n    super({\n      processTimeoutInMs: options?.processTimeoutInMs,\n      ...options,\n    });\n  }\n}\n\nexport class DefaultAzurePowershellCredential extends AzurePowerShellCredential {\n  constructor(options?: DefaultAzureCredentialOptions) {\n    super({\n      processTimeoutInMs: options?.processTimeoutInMs,\n      ...options,\n    });\n  }\n}\n\nexport const defaultCredentials: DefaultCredentialConstructor[] = [\n  EnvironmentCredential,\n  DefaultWorkloadIdentityCredential,\n  DefaultManagedIdentityCredential,\n  DefaultAzureCliCredential,\n  DefaultAzurePowershellCredential,\n  DefaultAzureDeveloperCliCredential,\n];\n\n/**\n * Provides a default {@link ChainedTokenCredential} configuration that should\n * work for most applications that use the Azure SDK.\n */\nexport class DefaultAzureCredential extends ChainedTokenCredential {\n  /**\n   * Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialClientIdOptions}\n   *\n   * This credential provides a default {@link ChainedTokenCredential} configuration that should\n   * work for most applications that use the Azure SDK.\n   *\n   * The following credential types will be tried, in order:\n   *\n   * - {@link EnvironmentCredential}\n   * - {@link WorkloadIdentityCredential}\n   * - {@link ManagedIdentityCredential}\n   * - {@link AzureCliCredential}\n   * - {@link AzurePowerShellCredential}\n   * - {@link AzureDeveloperCliCredential}\n   *\n   * Consult the documentation of these credential types for more information\n   * on how they attempt authentication.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialClientIdOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialClientIdOptions);\n\n  /**\n   *  Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialResourceIdOptions}\n   *\n   * This credential provides a default {@link ChainedTokenCredential} configuration that should\n   * work for most applications that use the Azure SDK.\n   *\n   * The following credential types will be tried, in order:\n   *\n   * - {@link EnvironmentCredential}\n   * - {@link WorkloadIdentityCredential}\n   * - {@link ManagedIdentityCredential}\n   * - {@link AzureCliCredential}\n   * - {@link AzurePowerShellCredential}\n   * - {@link AzureDeveloperCliCredential}\n   *\n   * Consult the documentation of these credential types for more information\n   * on how they attempt authentication.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialResourceIdOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialResourceIdOptions);\n\n  /**\n   * Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialOptions}\n   *\n   * This credential provides a default {@link ChainedTokenCredential} configuration that should\n   * work for most applications that use the Azure SDK.\n   *\n   * The following credential types will be tried, in order:\n   *\n   * - {@link EnvironmentCredential}\n   * - {@link WorkloadIdentityCredential}\n   * - {@link ManagedIdentityCredential}\n   * - {@link AzureCliCredential}\n   * - {@link AzurePowerShellCredential}\n   * - {@link AzureDeveloperCliCredential}\n   *\n   * Consult the documentation of these credential types for more information\n   * on how they attempt authentication.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialOptions);\n\n  constructor(\n    options?:\n      | DefaultAzureCredentialOptions\n      | DefaultAzureCredentialResourceIdOptions\n      | DefaultAzureCredentialClientIdOptions\n  ) {\n    super(...defaultCredentials.map((ctor) => new ctor(options)));\n  }\n}\n"]}