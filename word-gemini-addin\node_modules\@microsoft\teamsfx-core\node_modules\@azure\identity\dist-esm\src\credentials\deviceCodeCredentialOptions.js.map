{"version": 3, "file": "deviceCodeCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/deviceCodeCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { CredentialPersistenceOptions } from \"./credentialPersistenceOptions\";\nimport { InteractiveCredentialOptions } from \"./interactiveCredentialOptions\";\n\n/**\n * Provides the user code and verification URI where the code must be\n * entered.  Also provides a message to display to the user which\n * contains an instruction with these details.\n */\nexport interface DeviceCodeInfo {\n  /**\n   * The device code that the user must enter into the verification page.\n   */\n  userCode: string;\n\n  /**\n   * The verification URI to which the user must navigate to enter the device\n   * code.\n   */\n  verificationUri: string;\n\n  /**\n   * A message that may be shown to the user to instruct them on how to enter\n   * the device code in the page specified by the verification URI.\n   */\n  message: string;\n}\n\n/**\n * Defines the signature of a callback which will be passed to\n * DeviceCodeCredential for the purpose of displaying authentication\n * details to the user.\n */\nexport type DeviceCodePromptCallback = (deviceCodeInfo: DeviceCodeInfo) => void;\n\n/**\n * Defines options for the InteractiveBrowserCredential class for Node.js.\n */\nexport interface DeviceCodeCredentialOptions\n  extends InteractiveCredentialOptions,\n    CredentialPersistenceOptions {\n  /**\n   * The Azure Active Directory tenant (directory) ID.\n   */\n  tenantId?: string;\n  /**\n   * The client (application) ID of an App Registration in the tenant.\n   */\n  clientId?: string;\n  /**\n   * A callback function that will be invoked to show {@link DeviceCodeInfo} to the user.\n   * If left unassigned, we will automatically log the device code information\n   * and the authentication instructions in the console.\n   */\n  userPromptCallback?: DeviceCodePromptCallback;\n}\n"]}