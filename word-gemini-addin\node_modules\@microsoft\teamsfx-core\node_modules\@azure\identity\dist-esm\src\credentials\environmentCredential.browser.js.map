{"version": 3, "file": "environmentCredential.browser.js", "sourceRoot": "", "sources": ["../../../src/credentials/environmentCredential.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAEhE,MAAM,wBAAwB,GAAG,IAAI,KAAK,CACxC,wDAAwD,CACzD,CAAC;AACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;AAEzD;;;GAGG;AACH,MAAM,OAAO,qBAAqB;IAChC;;OAEG;IACH;QACE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QACvD,MAAM,wBAAwB,CAAC;IACjC,CAAC;IAED,QAAQ;QACN,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAC,CAAC;QAChE,MAAM,wBAAwB,CAAC;IACjC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError } from \"../util/logging\";\n\nconst BrowserNotSupportedError = new Error(\n  \"EnvironmentCredential is not supported in the browser.\"\n);\nconst logger = credentialLogger(\"EnvironmentCredential\");\n\n/**\n * Enables authentication to Azure Active Directory using client secret\n * details configured in environment variables\n */\nexport class EnvironmentCredential implements TokenCredential {\n  /**\n   * Only available in Node.js\n   */\n  constructor() {\n    logger.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n\n  getToken(): Promise<AccessToken | null> {\n    logger.getToken.info(formatError(\"\", BrowserNotSupportedError));\n    throw BrowserNotSupportedError;\n  }\n}\n"]}