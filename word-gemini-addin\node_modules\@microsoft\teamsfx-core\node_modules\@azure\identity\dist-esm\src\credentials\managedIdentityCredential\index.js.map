{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAE7D,OAAO,EACL,mBAAmB,EACnB,2BAA2B,EAC3B,0BAA0B,GAC3B,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAClF,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAEpC,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAA8B,6BAA6B,EAAE,MAAM,kBAAkB,CAAC;AAC7F,OAAO,EAAE,uBAAuB,EAAE,MAAM,iBAAiB,CAAC;AAE1D,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AACnD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,MAAM,MAAM,GAAG,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;AA4B7D;;;;;;;GAOG;AACH,MAAM,OAAO,yBAAyB;IA6BpC;;;OAGG;IACH,YACE,iBAG8C,EAC9C,OAAgC;;QAlC1B,0BAAqB,GAAmB,IAAI,CAAC;QAG7C,kCAA6B,GAAY,KAAK,CAAC;QAiCrD,IAAI,QAA4C,CAAC;QACjD,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;YACzC,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC;YAClC,QAAQ,GAAG,OAAO,CAAC;SACpB;aAAM;YACL,IAAI,CAAC,QAAQ,GAAI,iBAA8D,aAA9D,iBAAiB,uBAAjB,iBAAiB,CAA+C,QAAQ,CAAC;YAC1F,QAAQ,GAAG,iBAAiB,CAAC;SAC9B;QACD,IAAI,CAAC,UAAU,GAAI,QAAuD,aAAvD,QAAQ,uBAAR,QAAQ,CAAiD,UAAU,CAAC;QACvF,wBAAwB;QACxB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,MAAM,IAAI,KAAK,CACb,GAAG,yBAAyB,CAAC,IAAI,kEAAkE,CACpG,CAAC;SACH;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAI,CAAC,yBAAyB,GAAG,IAAI,cAAc,iCAC9C,QAAQ,KACX,YAAY,EAAE;gBACZ,UAAU,EAAE,CAAC;aACd,IACD,CAAC;QAEH;;WAEG;QACH,IAAI,CAAC,eAAe,GAAG,IAAI,6BAA6B,CAAC;YACvD,IAAI,EAAE;gBACJ,SAAS,EAAE,oDAAoD;gBAC/D,QAAQ,EAAE,MAAA,IAAI,CAAC,QAAQ,mCAAI,uBAAuB;gBAClD,YAAY,EAAE,cAAc;gBAC5B,sBAAsB,EACpB,w7BAAw7B;gBAC17B,iBAAiB,EACf,6gDAA6gD;gBAC/gD,kBAAkB,EAAE,EAAE;aACvB;YACD,MAAM,EAAE;gBACN,aAAa,EAAE;oBACb,QAAQ,EAAE,eAAe,CAAC,WAAW,EAAE,CAAC;iBACzC;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAIO,KAAK,CAAC,kBAAkB,CAC9B,MAAyB,EACzB,eAAiC;QAEjC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QAED,MAAM,IAAI,GAAG;YACX,MAAM;YACN,SAAS;YACT,iBAAiB;YACjB,iBAAiB;YACjB,aAAa;YACb,gBAAgB,EAAE;YAClB,OAAO;SACR,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,IACE,MAAM,GAAG,CAAC,WAAW,CAAC;gBACpB,MAAM;gBACN,cAAc,EAAE,IAAI,CAAC,yBAAyB;gBAC9C,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,eAAe;aAChB,CAAC,EACF;gBACA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;gBACrB,OAAO,GAAG,CAAC;aACZ;SACF;QAED,MAAM,IAAI,0BAA0B,CAClC,GAAG,yBAAyB,CAAC,IAAI,gCAAgC,CAClE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,2BAA2B,CACvC,MAAyB,EACzB,eAAiC;QAEjC,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,aAAa,CAAC,SAAS,CACtD,GAAG,yBAAyB,CAAC,IAAI,8BAA8B,EAC/D,eAAe,CAChB,CAAC;QAEF,IAAI;YACF,oGAAoG;YACpG,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAC3E,OAAO,YAAY,CAAC,QAAQ,CAC1B;gBACE,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,MAAM;gBACN,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,EACD,cAAc,CACf,CAAC;SACH;QAAC,OAAO,GAAQ,EAAE;YACjB,IAAI,CAAC,SAAS,CAAC;gBACb,MAAM,EAAE,OAAO;gBACf,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YACH,MAAM,GAAG,CAAC;SACX;gBAAS;YACR,IAAI,CAAC,GAAG,EAAE,CAAC;SACZ;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,OAAyB;QAEzB,IAAI,MAAM,GAAuB,IAAI,CAAC;QACtC,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,aAAa,CAAC,SAAS,CACtD,GAAG,yBAAyB,CAAC,IAAI,WAAW,EAC5C,OAAO,CACR,CAAC;QACF,IAAI;YACF,mDAAmD;YACnD,mDAAmD;YACnD,sDAAsD;YACtD,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE;gBACvC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;gBAC3E,IAAI,YAAY,CAAC,IAAI,KAAK,kBAAkB,EAAE;oBAC5C,MAAM,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;iBACzE;qBAAM;oBACL,MAAM,kBAAkB,GAA+B;wBACrD,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE;wBACrD,QAAQ,EAAE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,KAAI,kBAAkB;wBACjD,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;wBACjD,MAAM,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM;qBACxB,CAAC;oBAEF,mEAAmE;oBACnE,IAAI,CAAC,6BAA6B,EAAE,CAAC;oBACrC,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,8BAA8B,mBACjF,kBAAkB,EACrB,CAAC;oBACH,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,oBAAoB,IAAI,SAAS,CAAC,CAAC;iBACvE;gBACD,IAAI,MAAM,KAAK,IAAI,EAAE;oBACnB,+CAA+C;oBAC/C,2CAA2C;oBAC3C,8DAA8D;oBAC9D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;oBAElC,qGAAqG;oBACrG,yFAAyF;oBACzF,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,yEAAyE,CAC1E,CAAC;oBACF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;oBACjD,MAAM,KAAK,CAAC;iBACb;gBAED,iFAAiF;gBACjF,0EAA0E;gBAC1E,iCAAiC;gBACjC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;aACpC;iBAAM;gBACL,iEAAiE;gBACjE,2EAA2E;gBAC3E,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,0DAA0D,CAC3D,CAAC;gBACF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBACjD,MAAM,KAAK,CAAC;aACb;YAED,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5C,OAAO,MAAM,CAAC;SACf;QAAC,OAAO,GAAQ,EAAE;YACjB,2DAA2D;YAC3D,8EAA8E;YAC9E,IAAI,GAAG,CAAC,IAAI,KAAK,6BAA6B,EAAE;gBAC9C,MAAM,GAAG,CAAC;aACX;YAED,uCAAuC;YACvC,uDAAuD;YACvD,+DAA+D;YAC/D,uEAAuE;YACvE,kCAAkC;YAElC,IAAI,CAAC,SAAS,CAAC;gBACb,MAAM,EAAE,OAAO;gBACf,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,wCAAwC;YACxC,sDAAsD;YACtD,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,EAAE;gBAC9B,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,GAAG,yBAAyB,CAAC,IAAI,gDAAgD,GAAG,CAAC,OAAO,EAAE,CAC/F,CAAC;gBAEF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBACjD,MAAM,KAAK,CAAC;aACb;YAED,sCAAsC;YACtC,sDAAsD;YACtD,IAAI,GAAG,CAAC,IAAI,KAAK,cAAc,EAAE;gBAC/B,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,GAAG,yBAAyB,CAAC,IAAI,+DAA+D,GAAG,CAAC,OAAO,EAAE,CAC9G,CAAC;gBAEF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBACjD,MAAM,KAAK,CAAC;aACb;YACD,wEAAwE;YACxE,gFAAgF;YAChF,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE;gBAC1B,MAAM,IAAI,0BAA0B,CAClC,GAAG,yBAAyB,CAAC,IAAI,yFAAyF,GAAG,CAAC,OAAO,EAAE,CACxI,CAAC;aACH;YAED,gKAAgK;YAChK,4CAA4C;YAC5C,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE;gBAC9C,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,4DAA4D,CAAC,EAAE;oBACtF,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,GAAG,yBAAyB,CAAC,IAAI,gDAAgD,GAAG,CAAC,OAAO,EAAE,CAC/F,CAAC;oBAEF,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;oBACjD,MAAM,KAAK,CAAC;iBACb;aACF;YAED,kFAAkF;YAClF,8DAA8D;YAC9D,IAAI,GAAG,CAAC,UAAU,KAAK,SAAS,EAAE;gBAChC,MAAM,IAAI,0BAA0B,CAClC,GAAG,yBAAyB,CAAC,IAAI,oCAAoC,GAAG,CAAC,OAAO,EAAE,CACnF,CAAC;aACH;YAED,0CAA0C;YAC1C,MAAM,IAAI,mBAAmB,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC5C,KAAK,EAAE,GAAG,yBAAyB,CAAC,IAAI,yBAAyB;gBACjE,iBAAiB,EAAE,GAAG,CAAC,OAAO;aAC/B,CAAC,CAAC;SACJ;gBAAS;YACR,sFAAsF;YACtF,IAAI,CAAC,GAAG,EAAE,CAAC;SACZ;IACH,CAAC;IAED;;;;OAIG;IACK,YAAY,CAClB,MAAyB,EACzB,MAAmB,EACnB,eAAiC;QAEjC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;QAC3D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5C,OAAO;YACL,KAAK,EAAE,MAAO,CAAC,WAAY;YAC3B,kBAAkB,EAAE,MAAO,CAAC,SAAU,CAAC,OAAO,EAAE;SACjD,CAAC;IACJ,CAAC;IAED;;;OAGG;IACK,oBAAoB,CAC1B,MAAyB,EACzB,SAAqB,EACrB,eAAiC;QAEjC,MAAM,KAAK,GAAG,CAAC,OAAe,EAAS,EAAE;YACvC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,OAAO,IAAI,2BAA2B,CAAC;gBACrC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBACjD,eAAe;gBACf,OAAO;aACR,CAAC,CAAC;QACL,CAAC,CAAC;QACF,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;SAC5B;QACD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YACxB,MAAM,KAAK,CAAC,uCAAuC,CAAC,CAAC;SACtD;QACD,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAC1B,MAAM,KAAK,CAAC,yCAAyC,CAAC,CAAC;SACxD;IACH,CAAC;IAEO,6BAA6B;QACnC,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACvC,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,KAAK,EAAE,0BAA0B,EAAE,EAAE;gBAC5E,MAAM,CAAC,IAAI,CACT,gDAAgD,IAAI,CAAC,SAAS,CAC5D,0BAA0B,CAC3B,EAAE,CACJ,CAAC;gBACF,MAAM,eAAe,qBAChB,0BAA0B,CAC9B,CAAC;gBACF,MAAM,CAAC,IAAI,CACT,oDAAoD,IAAI,CAAC,SAAS,CAChE,0BAA0B,CAAC,MAAM,CAClC,0BAA0B,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,CAC7D,CAAC;gBACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CACxD,0BAA0B,CAAC,MAAM,EACjC,eAAe,CAChB,CAAC;gBAEF,IAAI,WAAW,EAAE;oBACf,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;oBAEhE,MAAM,gBAAgB,GAAG,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,kBAAkB;wBACtD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;wBAClE,CAAC,CAAC,CAAC,CAAC;oBACN,OAAO;wBACL,WAAW,EAAE,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK;wBAC/B,gBAAgB;qBACjB,CAAC;iBACH;qBAAM;oBACL,MAAM,CAAC,IAAI,CACT,6EAA6E,CAC9E,CAAC;oBACF,OAAO;wBACL,WAAW,EAAE,0BAA0B;wBACvC,gBAAgB,EAAE,CAAC;qBACpB,CAAC;iBACH;YACH,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;SAC3C;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\n\nimport { IdentityClient } from \"../../client/identityClient\";\nimport { TokenCredentialOptions } from \"../../tokenCredentialOptions\";\nimport {\n  AuthenticationError,\n  AuthenticationRequiredError,\n  CredentialUnavailableError,\n} from \"../../errors\";\nimport { credentialLogger, formatError, formatSuccess } from \"../../util/logging\";\nimport { appServiceMsi2017 } from \"./appServiceMsi2017\";\nimport { tracingClient } from \"../../util/tracing\";\nimport { cloudShellMsi } from \"./cloudShellMsi\";\nimport { imdsMsi } from \"./imdsMsi\";\nimport { MSI, MSIToken } from \"./models\";\nimport { arcMsi } from \"./arcMsi\";\nimport { tokenExchangeMsi } from \"./tokenExchangeMsi\";\nimport { fabricMsi } from \"./fabricMsi\";\nimport { appServiceMsi2019 } from \"./appServiceMsi2019\";\nimport { AppTokenProviderParameters, ConfidentialClientApplication } from \"@azure/msal-node\";\nimport { DeveloperSignOnClientId } from \"../../constants\";\nimport { MsalResult, MsalToken } from \"../../msal/types\";\nimport { getMSALLogLevel } from \"../../msal/utils\";\nimport { getLogLevel } from \"@azure/logger\";\n\nconst logger = credentialLogger(\"ManagedIdentityCredential\");\n\n/**\n * Options to send on the {@link ManagedIdentityCredential} constructor.\n * This variation supports `clientId` and not `resourceId`, since only one of both is supported.\n */\nexport interface ManagedIdentityCredentialClientIdOptions extends TokenCredentialOptions {\n  /**\n   * The client ID of the user - assigned identity, or app registration(when working with AKS pod - identity).\n   */\n  clientId?: string;\n}\n\n/**\n * Options to send on the {@link ManagedIdentityCredential} constructor.\n * This variation supports `resourceId` and not `clientId`, since only one of both is supported.\n */\nexport interface ManagedIdentityCredentialResourceIdOptions extends TokenCredentialOptions {\n  /**\n   * Allows specifying a custom resource Id.\n   * In scenarios such as when user assigned identities are created using an ARM template,\n   * where the resource Id of the identity is known but the client Id can't be known ahead of time,\n   * this parameter allows programs to use these user assigned identities\n   * without having to first determine the client Id of the created identity.\n   */\n  resourceId: string;\n}\n\n/**\n * Attempts authentication using a managed identity available at the deployment environment.\n * This authentication type works in Azure VMs, App Service instances, Azure Functions applications,\n * Azure Kubernetes Services, Azure Service Fabric instances and inside of the Azure Cloud Shell.\n *\n * More information about configuring managed identities can be found here:\n * https://learn.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview\n */\nexport class ManagedIdentityCredential implements TokenCredential {\n  private identityClient: IdentityClient;\n  private clientId: string | undefined;\n  private resourceId: string | undefined;\n  private isEndpointUnavailable: boolean | null = null;\n  private isAvailableIdentityClient: IdentityClient;\n  private confidentialApp: ConfidentialClientApplication;\n  private isAppTokenProviderInitialized: boolean = false;\n\n  /**\n   * Creates an instance of ManagedIdentityCredential with the client ID of a\n   * user-assigned identity, or app registration (when working with AKS pod-identity).\n   *\n   * @param clientId - The client ID of the user-assigned identity, or app registration (when working with AKS pod-identity).\n   * @param options - Options for configuring the client which makes the access token request.\n   */\n  constructor(clientId: string, options?: TokenCredentialOptions);\n  /**\n   * Creates an instance of ManagedIdentityCredential with clientId\n   *\n   * @param options - Options for configuring the client which makes the access token request.\n   */\n  constructor(options?: ManagedIdentityCredentialClientIdOptions);\n  /**\n   * Creates an instance of ManagedIdentityCredential with Resource Id\n   *\n   * @param options - Options for configuring the resource which makes the access token request.\n   */\n  constructor(options?: ManagedIdentityCredentialResourceIdOptions);\n  /**\n   * @internal\n   * @hidden\n   */\n  constructor(\n    clientIdOrOptions?:\n      | string\n      | ManagedIdentityCredentialClientIdOptions\n      | ManagedIdentityCredentialResourceIdOptions,\n    options?: TokenCredentialOptions\n  ) {\n    let _options: TokenCredentialOptions | undefined;\n    if (typeof clientIdOrOptions === \"string\") {\n      this.clientId = clientIdOrOptions;\n      _options = options;\n    } else {\n      this.clientId = (clientIdOrOptions as ManagedIdentityCredentialClientIdOptions)?.clientId;\n      _options = clientIdOrOptions;\n    }\n    this.resourceId = (_options as ManagedIdentityCredentialResourceIdOptions)?.resourceId;\n    // For JavaScript users.\n    if (this.clientId && this.resourceId) {\n      throw new Error(\n        `${ManagedIdentityCredential.name} - Client Id and Resource Id can't be provided at the same time.`\n      );\n    }\n    this.identityClient = new IdentityClient(_options);\n    this.isAvailableIdentityClient = new IdentityClient({\n      ..._options,\n      retryOptions: {\n        maxRetries: 0,\n      },\n    });\n\n    /**  authority host validation and metadata discovery to be skipped in managed identity\n     * since this wasn't done previously before adding token cache support\n     */\n    this.confidentialApp = new ConfidentialClientApplication({\n      auth: {\n        authority: \"https://login.microsoftonline.com/managed_identity\",\n        clientId: this.clientId ?? DeveloperSignOnClientId,\n        clientSecret: \"dummy-secret\",\n        cloudDiscoveryMetadata:\n          '{\"tenant_discovery_endpoint\":\"https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration\",\"api-version\":\"1.1\",\"metadata\":[{\"preferred_network\":\"login.microsoftonline.com\",\"preferred_cache\":\"login.windows.net\",\"aliases\":[\"login.microsoftonline.com\",\"login.windows.net\",\"login.microsoft.com\",\"sts.windows.net\"]},{\"preferred_network\":\"login.partner.microsoftonline.cn\",\"preferred_cache\":\"login.partner.microsoftonline.cn\",\"aliases\":[\"login.partner.microsoftonline.cn\",\"login.chinacloudapi.cn\"]},{\"preferred_network\":\"login.microsoftonline.de\",\"preferred_cache\":\"login.microsoftonline.de\",\"aliases\":[\"login.microsoftonline.de\"]},{\"preferred_network\":\"login.microsoftonline.us\",\"preferred_cache\":\"login.microsoftonline.us\",\"aliases\":[\"login.microsoftonline.us\",\"login.usgovcloudapi.net\"]},{\"preferred_network\":\"login-us.microsoftonline.com\",\"preferred_cache\":\"login-us.microsoftonline.com\",\"aliases\":[\"login-us.microsoftonline.com\"]}]}',\n        authorityMetadata:\n          '{\"token_endpoint\":\"https://login.microsoftonline.com/common/oauth2/v2.0/token\",\"token_endpoint_auth_methods_supported\":[\"client_secret_post\",\"private_key_jwt\",\"client_secret_basic\"],\"jwks_uri\":\"https://login.microsoftonline.com/common/discovery/v2.0/keys\",\"response_modes_supported\":[\"query\",\"fragment\",\"form_post\"],\"subject_types_supported\":[\"pairwise\"],\"id_token_signing_alg_values_supported\":[\"RS256\"],\"response_types_supported\":[\"code\",\"id_token\",\"code id_token\",\"id_token token\"],\"scopes_supported\":[\"openid\",\"profile\",\"email\",\"offline_access\"],\"issuer\":\"https://login.microsoftonline.com/{tenantid}/v2.0\",\"request_uri_parameter_supported\":false,\"userinfo_endpoint\":\"https://graph.microsoft.com/oidc/userinfo\",\"authorization_endpoint\":\"https://login.microsoftonline.com/common/oauth2/v2.0/authorize\",\"device_authorization_endpoint\":\"https://login.microsoftonline.com/common/oauth2/v2.0/devicecode\",\"http_logout_supported\":true,\"frontchannel_logout_supported\":true,\"end_session_endpoint\":\"https://login.microsoftonline.com/common/oauth2/v2.0/logout\",\"claims_supported\":[\"sub\",\"iss\",\"cloud_instance_name\",\"cloud_instance_host_name\",\"cloud_graph_host_name\",\"msgraph_host\",\"aud\",\"exp\",\"iat\",\"auth_time\",\"acr\",\"nonce\",\"preferred_username\",\"name\",\"tid\",\"ver\",\"at_hash\",\"c_hash\",\"email\"],\"kerberos_endpoint\":\"https://login.microsoftonline.com/common/kerberos\",\"tenant_region_scope\":null,\"cloud_instance_name\":\"microsoftonline.com\",\"cloud_graph_host_name\":\"graph.windows.net\",\"msgraph_host\":\"graph.microsoft.com\",\"rbac_url\":\"https://pas.windows.net\"}',\n        clientCapabilities: [],\n      },\n      system: {\n        loggerOptions: {\n          logLevel: getMSALLogLevel(getLogLevel()),\n        },\n      },\n    });\n  }\n\n  private cachedMSI: MSI | undefined;\n\n  private async cachedAvailableMSI(\n    scopes: string | string[],\n    getTokenOptions?: GetTokenOptions\n  ): Promise<MSI> {\n    if (this.cachedMSI) {\n      return this.cachedMSI;\n    }\n\n    const MSIs = [\n      arcMsi,\n      fabricMsi,\n      appServiceMsi2019,\n      appServiceMsi2017,\n      cloudShellMsi,\n      tokenExchangeMsi(),\n      imdsMsi,\n    ];\n\n    for (const msi of MSIs) {\n      if (\n        await msi.isAvailable({\n          scopes,\n          identityClient: this.isAvailableIdentityClient,\n          clientId: this.clientId,\n          resourceId: this.resourceId,\n          getTokenOptions,\n        })\n      ) {\n        this.cachedMSI = msi;\n        return msi;\n      }\n    }\n\n    throw new CredentialUnavailableError(\n      `${ManagedIdentityCredential.name} - No MSI credential available`\n    );\n  }\n\n  private async authenticateManagedIdentity(\n    scopes: string | string[],\n    getTokenOptions?: GetTokenOptions\n  ): Promise<MSIToken | null> {\n    const { span, updatedOptions } = tracingClient.startSpan(\n      `${ManagedIdentityCredential.name}.authenticateManagedIdentity`,\n      getTokenOptions\n    );\n\n    try {\n      // Determining the available MSI, and avoiding checking for other MSIs while the program is running.\n      const availableMSI = await this.cachedAvailableMSI(scopes, updatedOptions);\n      return availableMSI.getToken(\n        {\n          identityClient: this.identityClient,\n          scopes,\n          clientId: this.clientId,\n          resourceId: this.resourceId,\n        },\n        updatedOptions\n      );\n    } catch (err: any) {\n      span.setStatus({\n        status: \"error\",\n        error: err,\n      });\n      throw err;\n    } finally {\n      span.end();\n    }\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   * If an unexpected error occurs, an {@link AuthenticationError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options?: GetTokenOptions\n  ): Promise<AccessToken> {\n    let result: AccessToken | null = null;\n    const { span, updatedOptions } = tracingClient.startSpan(\n      `${ManagedIdentityCredential.name}.getToken`,\n      options\n    );\n    try {\n      // isEndpointAvailable can be true, false, or null,\n      // If it's null, it means we don't yet know whether\n      // the endpoint is available and need to check for it.\n      if (this.isEndpointUnavailable !== true) {\n        const availableMSI = await this.cachedAvailableMSI(scopes, updatedOptions);\n        if (availableMSI.name === \"tokenExchangeMsi\") {\n          result = await this.authenticateManagedIdentity(scopes, updatedOptions);\n        } else {\n          const appTokenParameters: AppTokenProviderParameters = {\n            correlationId: this.identityClient.getCorrelationId(),\n            tenantId: options?.tenantId || \"managed_identity\",\n            scopes: Array.isArray(scopes) ? scopes : [scopes],\n            claims: options?.claims,\n          };\n\n          // Added a check to see if SetAppTokenProvider was already defined.\n          this.initializeSetAppTokenProvider();\n          const authenticationResult = await this.confidentialApp.acquireTokenByClientCredential({\n            ...appTokenParameters,\n          });\n          result = this.handleResult(scopes, authenticationResult || undefined);\n        }\n        if (result === null) {\n          // If authenticateManagedIdentity returns null,\n          // it means no MSI endpoints are available.\n          // If so, we avoid trying to reach to them in future requests.\n          this.isEndpointUnavailable = true;\n\n          // It also means that the endpoint answered with either 200 or 201 (see the sendTokenRequest method),\n          // yet we had no access token. For this reason, we'll throw once with a specific message:\n          const error = new CredentialUnavailableError(\n            \"The managed identity endpoint was reached, yet no tokens were received.\"\n          );\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n\n        // Since `authenticateManagedIdentity` didn't throw, and the result was not null,\n        // We will assume that this endpoint is reachable from this point forward,\n        // and avoid pinging again to it.\n        this.isEndpointUnavailable = false;\n      } else {\n        // We've previously determined that the endpoint was unavailable,\n        // either because it was unreachable or permanently unable to authenticate.\n        const error = new CredentialUnavailableError(\n          \"The managed identity endpoint is not currently available\"\n        );\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n\n      logger.getToken.info(formatSuccess(scopes));\n      return result;\n    } catch (err: any) {\n      // CredentialUnavailable errors are expected to reach here.\n      // We intend them to bubble up, so that DefaultAzureCredential can catch them.\n      if (err.name === \"AuthenticationRequiredError\") {\n        throw err;\n      }\n\n      // Expected errors to reach this point:\n      // - Errors coming from a method unexpectedly breaking.\n      // - When identityClient.sendTokenRequest throws, in which case\n      //   if the status code was 400, it means that the endpoint is working,\n      //   but no identity is available.\n\n      span.setStatus({\n        status: \"error\",\n        error: err,\n      });\n\n      // If either the network is unreachable,\n      // we can safely assume the credential is unavailable.\n      if (err.code === \"ENETUNREACH\") {\n        const error = new CredentialUnavailableError(\n          `${ManagedIdentityCredential.name}: Unavailable. Network unreachable. Message: ${err.message}`\n        );\n\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n\n      // If either the host was unreachable,\n      // we can safely assume the credential is unavailable.\n      if (err.code === \"EHOSTUNREACH\") {\n        const error = new CredentialUnavailableError(\n          `${ManagedIdentityCredential.name}: Unavailable. No managed identity endpoint found. Message: ${err.message}`\n        );\n\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n      // If err.statusCode has a value of 400, it comes from sendTokenRequest,\n      // and it means that the endpoint is working, but that no identity is available.\n      if (err.statusCode === 400) {\n        throw new CredentialUnavailableError(\n          `${ManagedIdentityCredential.name}: The managed identity endpoint is indicating there's no available identity. Message: ${err.message}`\n        );\n      }\n\n      // This is a special case for Docker Desktop which responds with a 403 with a message that contains \"A socket operation was attempted to an unreachable network\"\n      // rather than just timing out, as expected.\n      if (err.statusCode === 403 || err.code === 403) {\n        if (err.message.includes(\"A socket operation was attempted to an unreachable network\")) {\n          const error = new CredentialUnavailableError(\n            `${ManagedIdentityCredential.name}: Unavailable. Network unreachable. Message: ${err.message}`\n          );\n\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n      }\n\n      // If the error has no status code, we can assume there was no available identity.\n      // This will throw silently during any ChainedTokenCredential.\n      if (err.statusCode === undefined) {\n        throw new CredentialUnavailableError(\n          `${ManagedIdentityCredential.name}: Authentication failed. Message ${err.message}`\n        );\n      }\n\n      // Any other error should break the chain.\n      throw new AuthenticationError(err.statusCode, {\n        error: `${ManagedIdentityCredential.name} authentication failed.`,\n        error_description: err.message,\n      });\n    } finally {\n      // Finally is always called, both if we return and if we throw in the above try/catch.\n      span.end();\n    }\n  }\n\n  /**\n   * Handles the MSAL authentication result.\n   * If the result has an account, we update the local account reference.\n   * If the token received is invalid, an error will be thrown depending on what's missing.\n   */\n  private handleResult(\n    scopes: string | string[],\n    result?: MsalResult,\n    getTokenOptions?: GetTokenOptions\n  ): AccessToken {\n    this.ensureValidMsalToken(scopes, result, getTokenOptions);\n    logger.getToken.info(formatSuccess(scopes));\n    return {\n      token: result!.accessToken!,\n      expiresOnTimestamp: result!.expiresOn!.getTime(),\n    };\n  }\n\n  /**\n   * Ensures the validity of the MSAL token\n   * @internal\n   */\n  private ensureValidMsalToken(\n    scopes: string | string[],\n    msalToken?: MsalToken,\n    getTokenOptions?: GetTokenOptions\n  ): void {\n    const error = (message: string): Error => {\n      logger.getToken.info(message);\n      return new AuthenticationRequiredError({\n        scopes: Array.isArray(scopes) ? scopes : [scopes],\n        getTokenOptions,\n        message,\n      });\n    };\n    if (!msalToken) {\n      throw error(\"No response\");\n    }\n    if (!msalToken.expiresOn) {\n      throw error(`Response had no \"expiresOn\" property.`);\n    }\n    if (!msalToken.accessToken) {\n      throw error(`Response had no \"accessToken\" property.`);\n    }\n  }\n\n  private initializeSetAppTokenProvider(): void {\n    if (!this.isAppTokenProviderInitialized) {\n      this.confidentialApp.SetAppTokenProvider(async (appTokenProviderParameters) => {\n        logger.info(\n          `SetAppTokenProvider invoked with parameters- ${JSON.stringify(\n            appTokenProviderParameters\n          )}`\n        );\n        const getTokenOptions: GetTokenOptions = {\n          ...appTokenProviderParameters,\n        };\n        logger.info(\n          `authenticateManagedIdentity invoked with scopes- ${JSON.stringify(\n            appTokenProviderParameters.scopes\n          )} and getTokenOptions - ${JSON.stringify(getTokenOptions)}`\n        );\n        const resultToken = await this.authenticateManagedIdentity(\n          appTokenProviderParameters.scopes,\n          getTokenOptions\n        );\n\n        if (resultToken) {\n          logger.info(`SetAppTokenProvider will save the token in cache`);\n\n          const expiresInSeconds = resultToken?.expiresOnTimestamp\n            ? Math.floor((resultToken.expiresOnTimestamp - Date.now()) / 1000)\n            : 0;\n          return {\n            accessToken: resultToken?.token,\n            expiresInSeconds,\n          };\n        } else {\n          logger.info(\n            `SetAppTokenProvider token has \"no_access_token_returned\" as the saved token`\n          );\n          return {\n            accessToken: \"no_access_token_returned\",\n            expiresInSeconds: 0,\n          };\n        }\n      });\n      this.isAppTokenProviderInitialized = true;\n    }\n  }\n}\n"]}