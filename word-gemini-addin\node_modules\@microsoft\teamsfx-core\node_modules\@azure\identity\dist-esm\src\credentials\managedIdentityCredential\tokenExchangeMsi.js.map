{"version": 3, "file": "tokenExchangeMsi.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/tokenExchangeMsi.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,0BAA0B,EAAE,MAAM,+BAA+B,CAAC;AAC3E,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAGtD,MAAM,OAAO,GAAG,4CAA4C,CAAC;AAC7D,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAEzC;;GAEG;AACH,MAAM,UAAU,gBAAgB;IAC9B,OAAO;QACL,IAAI,EAAE,kBAAkB;QACxB,KAAK,CAAC,WAAW,CAAC,EAAE,QAAQ,EAAE;YAC5B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;YACxB,MAAM,MAAM,GAAG,OAAO,CACpB,CAAC,QAAQ,IAAI,GAAG,CAAC,eAAe,CAAC;gBAC/B,GAAG,CAAC,eAAe;gBACnB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CACzC,CAAC;YACF,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,CAAC,IAAI,CACT,GAAG,OAAO,qKAAqK,CAChL,CAAC;aACH;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,KAAK,CAAC,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE;YAErC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,aAAa,CAAC;YAC3C,MAAM,oCAAoC,GAAG,EAAE,CAAC;YAChD,MAAM,0BAA0B,GAAG,IAAI,0BAA0B,CAAC,8BAChE,QAAQ,EACR,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EACrC,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAClD,oCAAoC,KACvC,wBAAwB,EAAE,IAAI,GACM,CAAC,CAAC;YACxC,MAAM,KAAK,GAAG,MAAM,0BAA0B,CAAC,QAAQ,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YACjF,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { MSI, MSIConfiguration } from \"./models\";\nimport { WorkloadIdentityCredential } from \"../workloadIdentityCredential\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { WorkloadIdentityCredentialOptions } from \"../workloadIdentityCredentialOptions\";\n\nconst msiName = \"ManagedIdentityCredential - Token Exchange\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Defines how to determine whether the token exchange MSI is available, and also how to retrieve a token from the token exchange MSI.\n */\nexport function tokenExchangeMsi(): MSI {\n  return {\n    name: \"tokenExchangeMsi\",\n    async isAvailable({ clientId }): Promise<boolean> {\n      const env = process.env;\n      const result = Boolean(\n        (clientId || env.AZURE_CLIENT_ID) &&\n          env.AZURE_TENANT_ID &&\n          process.env.AZURE_FEDERATED_TOKEN_FILE\n      );\n      if (!result) {\n        logger.info(\n          `${msiName}: Unavailable. The environment variables needed are: AZURE_CLIENT_ID (or the client ID sent through the parameters), AZURE_TENANT_ID and AZURE_FEDERATED_TOKEN_FILE`\n        );\n      }\n      return result;\n    },\n    async getToken(\n      configuration: MSIConfiguration,\n      getTokenOptions: GetTokenOptions = {}\n    ): Promise<AccessToken | null> {\n      const { scopes, clientId } = configuration;\n      const identityClientTokenCredentialOptions = {};\n      const workloadIdentityCredential = new WorkloadIdentityCredential({\n        clientId,\n        tenantId: process.env.AZURE_TENANT_ID,\n        tokenFilePath: process.env.AZURE_FEDERATED_TOKEN_FILE,\n        ...identityClientTokenCredentialOptions,\n        disableInstanceDiscovery: true,\n      } as WorkloadIdentityCredentialOptions);\n      const token = await workloadIdentityCredential.getToken(scopes, getTokenOptions);\n      return token;\n    },\n  };\n}\n"]}