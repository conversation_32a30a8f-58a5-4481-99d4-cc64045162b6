{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../src/credentials/managedIdentityCredential/utils.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAEjD;;;;;;;;GAQG;AACH,MAAM,UAAU,mBAAmB,CAAC,MAAyB;IAC3D,IAAI,KAAK,GAAG,EAAE,CAAC;IACf,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACzB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,OAAO;SACR;QAED,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;KACnB;SAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACrC,KAAK,GAAG,MAAM,CAAC;KAChB;IAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QACvC,OAAO,KAAK,CAAC;KACd;IAED,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC;AAChE,CAAC;AAeD;;;GAGG;AACH,MAAM,UAAU,wBAAwB,CAAC,IAA6B;IACpE,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;QACvC,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC/B;IAED,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;QACvC,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YACpB,OAAO,QAAQ,GAAG,IAAI,CAAC;SACxB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YAClB,OAAO,MAAM,CAAC;SACf;KACF;IAED,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;QACvC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KAC5C;IAED,MAAM,IAAI,KAAK,CACb,2DAA2D,IAAI,CAAC,UAAU,kBAAkB,IAAI,CAAC,UAAU,GAAG,CAC/G,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { DefaultScopeSuffix } from \"./constants\";\n\n/**\n * Most MSIs send requests to the IMDS endpoint, or a similar endpoint.\n * These are GET requests that require sending a `resource` parameter on the query.\n * This resource can be derived from the scopes received through the getToken call, as long as only one scope is received.\n * Multiple scopes assume that the resulting token will have access to multiple resources, which won't be the case.\n *\n * For that reason, when we encounter multiple scopes, we return undefined.\n * It's up to the individual MSI implementations to throw the errors (which helps us provide less generic errors).\n */\nexport function mapScopesToResource(scopes: string | string[]): string | undefined {\n  let scope = \"\";\n  if (Array.isArray(scopes)) {\n    if (scopes.length !== 1) {\n      return;\n    }\n\n    scope = scopes[0];\n  } else if (typeof scopes === \"string\") {\n    scope = scopes;\n  }\n\n  if (!scope.endsWith(DefaultScopeSuffix)) {\n    return scope;\n  }\n\n  return scope.substr(0, scope.lastIndexOf(DefaultScopeSuffix));\n}\n\n/**\n * Internal type roughly matching the raw responses of the authentication endpoints.\n *\n * @internal\n */\nexport interface TokenResponseParsedBody {\n  access_token?: string;\n  refresh_token?: string;\n  expires_in: number;\n  expires_on?: number | string;\n  refresh_in?: number;\n}\n\n/**\n * Given a token response, return the expiration timestamp as the number of milliseconds from the Unix epoch.\n * @param body - A parsed response body from the authentication endpoint.\n */\nexport function parseExpirationTimestamp(body: TokenResponseParsedBody): number {\n  if (typeof body.expires_on === \"number\") {\n    return body.expires_on * 1000;\n  }\n\n  if (typeof body.expires_on === \"string\") {\n    const asNumber = +body.expires_on;\n    if (!isNaN(asNumber)) {\n      return asNumber * 1000;\n    }\n\n    const asDate = Date.parse(body.expires_on);\n    if (!isNaN(asDate)) {\n      return asDate;\n    }\n  }\n\n  if (typeof body.expires_in === \"number\") {\n    return Date.now() + body.expires_in * 1000;\n  }\n\n  throw new Error(\n    `Failed to parse token expiration from body. expires_in=\"${body.expires_in}\", expires_on=\"${body.expires_on}\"`\n  );\n}\n"]}