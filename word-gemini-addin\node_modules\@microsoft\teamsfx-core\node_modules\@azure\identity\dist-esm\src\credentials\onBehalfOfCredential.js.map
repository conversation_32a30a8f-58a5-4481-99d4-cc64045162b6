{"version": 3, "file": "onBehalfOfCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/onBehalfOfCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC,OAAO,EACL,yBAAyB,EACzB,mCAAmC,GACpC,MAAM,uBAAuB,CAAC;AAG/B,OAAO,EAAE,cAAc,EAAE,MAAM,kCAAkC,CAAC;AAElE,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,MAAM,cAAc,GAAG,sBAAsB,CAAC;AAC9C,MAAM,MAAM,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAEhD;;GAEG;AACH,MAAM,OAAO,oBAAoB;IAyD/B,YAAoB,OAAoC;QAApC,YAAO,GAAP,OAAO,CAA6B;QACtD,MAAM,EAAE,YAAY,EAAE,GAAG,OAA4C,CAAC;QACtE,MAAM,EAAE,eAAe,EAAE,GAAG,OAAiD,CAAC;QAC9E,MAAM,EACJ,QAAQ,EACR,QAAQ,EACR,kBAAkB,EAClB,0BAA0B,EAAE,4BAA4B,GACzD,GAAG,OAAO,CAAC;QACZ,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,IAAI,eAAe,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACvF,MAAM,IAAI,KAAK,CACb,GAAG,cAAc,yGAAyG,CAC3H,CAAC;SACH;QAED,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,4BAA4B,CAC7B,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,cAAc,iCAC7B,IAAI,CAAC,OAAO,KACf,MAAM,EACN,sBAAsB,EAAE,IAAI,CAAC,OAAO,IACpC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,aAAa,CAAC,QAAQ,CAAC,GAAG,cAAc,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE;YACxF,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjC,MAAM,CACP,CAAC;YAEF,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,QAAS,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  OnBehalfOfCredentialCertificateOptions,\n  OnBehalfOfCredentialOptions,\n  OnBehalfOfCredentialSecretOptions,\n} from \"./onBehalfOfCredentialOptions\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { CredentialPersistenceOptions } from \"./credentialPersistenceOptions\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { MsalOnBehalfOf } from \"../msal/nodeFlows/msalOnBehalfOf\";\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst credentialName = \"OnBehalfOfCredential\";\nconst logger = credentialLogger(credentialName);\n\n/**\n * Enables authentication to Microsoft Entra ID using the [On Behalf Of flow](https://learn.microsoft.com/azure/active-directory/develop/v2-oauth2-on-behalf-of-flow).\n */\nexport class OnBehalfOfCredential implements TokenCredential {\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n  /**\n   * Creates an instance of the {@link OnBehalfOfCredential} with the details\n   * needed to authenticate against Azure Active Directory with path to a PEM certificate,\n   * and an user assertion.\n   *\n   * Example using the `KeyClient` from [\\@azure/keyvault-keys](https://www.npmjs.com/package/\\@azure/keyvault-keys):\n   *\n   * ```ts\n   * const tokenCredential = new OnBehalfOfCredential({\n   *   tenantId,\n   *   clientId,\n   *   certificatePath: \"/path/to/certificate.pem\",\n   *   userAssertionToken: \"access-token\"\n   * });\n   * const client = new KeyClient(\"vault-url\", tokenCredential);\n   *\n   * await client.getKey(\"key-name\");\n   * ```\n   *\n   * @param options - Optional parameters, generally common across credentials.\n   */\n  constructor(\n    options: OnBehalfOfCredentialCertificateOptions &\n      MultiTenantTokenCredentialOptions &\n      CredentialPersistenceOptions\n  );\n  /**\n   * Creates an instance of the {@link OnBehalfOfCredential} with the details\n   * needed to authenticate against Azure Active Directory with a client\n   * secret and an user assertion.\n   *\n   * Example using the `KeyClient` from [\\@azure/keyvault-keys](https://www.npmjs.com/package/\\@azure/keyvault-keys):\n   *\n   * ```ts\n   * const tokenCredential = new OnBehalfOfCredential({\n   *   tenantId,\n   *   clientId,\n   *   clientSecret,\n   *   userAssertionToken: \"access-token\"\n   * });\n   * const client = new KeyClient(\"vault-url\", tokenCredential);\n   *\n   * await client.getKey(\"key-name\");\n   * ```\n   *\n   * @param options - Optional parameters, generally common across credentials.\n   */\n  constructor(\n    options: OnBehalfOfCredentialSecretOptions &\n      MultiTenantTokenCredentialOptions &\n      CredentialPersistenceOptions\n  );\n\n  constructor(private options: OnBehalfOfCredentialOptions) {\n    const { clientSecret } = options as OnBehalfOfCredentialSecretOptions;\n    const { certificatePath } = options as OnBehalfOfCredentialCertificateOptions;\n    const {\n      tenantId,\n      clientId,\n      userAssertionToken,\n      additionallyAllowedTenants: additionallyAllowedTenantIds,\n    } = options;\n    if (!tenantId || !clientId || !(clientSecret || certificatePath) || !userAssertionToken) {\n      throw new Error(\n        `${credentialName}: tenantId, clientId, clientSecret (or certificatePath) and userAssertionToken are required parameters.`\n      );\n    }\n\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      additionallyAllowedTenantIds\n    );\n\n    this.msalFlow = new MsalOnBehalfOf({\n      ...this.options,\n      logger,\n      tokenCredentialOptions: this.options,\n    });\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure the underlying network requests.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(`${credentialName}.getToken`, options, async (newOptions) => {\n      newOptions.tenantId = processMultiTenantRequest(\n        this.tenantId,\n        newOptions,\n        this.additionallyAllowedTenantIds,\n        logger\n      );\n\n      const arrayScopes = ensureScopes(scopes);\n      return this.msalFlow!.getToken(arrayScopes, newOptions);\n    });\n  }\n}\n"]}