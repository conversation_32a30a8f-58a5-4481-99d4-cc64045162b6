{"version": 3, "file": "usernamePasswordCredential.browser.js", "sourceRoot": "", "sources": ["../../../src/credentials/usernamePasswordCredential.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,aAAa,EACb,yBAAyB,EACzB,mCAAmC,GACpC,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,MAAM,2BAA2B,CAAC;AACrF,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAClE,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAE1D,OAAO,EAAE,8BAA8B,EAAE,MAAM,+BAA+B,CAAC;AAC/E,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,MAAM,MAAM,GAAG,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;AAE9D;;;;;GAKG;AACH,MAAM,OAAO,0BAA0B;IAQrC;;;;;;;;;;OAUG;IACH,YACE,cAAsB,EACtB,QAAgB,EAChB,QAAgB,EAChB,QAAgB,EAChB,OAA2C;QAE3C,aAAa,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QAEtC,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;QAC/B,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,QAAQ,CACnB,MAAyB,EACzB,UAA2B,EAAE;QAE7B,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,CAClC,CAAC;YACF,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE/B,MAAM,SAAS,GAAG,8BAA8B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChE,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;gBACjC,aAAa,EAAE,OAAO;gBACtB,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,IAAI,CAAC,QAAQ;gBACxB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,KAAK,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;aAC9D,CAAC,CAAC;YACH,MAAM,WAAW,GAAG,qBAAqB,CAAC;gBACxC,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,EAAE;gBACzE,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE;gBACvB,OAAO,EAAE,iBAAiB,CAAC;oBACzB,MAAM,EAAE,kBAAkB;oBAC1B,cAAc,EAAE,mCAAmC;iBACpD,CAAC;gBACF,WAAW,EAAE,OAAO,IAAI,OAAO,CAAC,WAAW;gBAC3C,cAAc,EAAE,UAAU,CAAC,cAAc;aAC1C,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC9E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5C,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;QAC9D,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  checkTenantId,\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { createHttpHeaders, createPipelineRequest } from \"@azure/core-rest-pipeline\";\nimport { credentialLogger, formatSuccess } from \"../util/logging\";\nimport { IdentityClient } from \"../client/identityClient\";\nimport { UsernamePasswordCredentialOptions } from \"./usernamePasswordCredentialOptions\";\nimport { getIdentityTokenEndpointSuffix } from \"../util/identityTokenEndpoint\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"UsernamePasswordCredential\");\n\n/**\n * Enables authentication to Azure Active Directory with a user's\n * username and password. This credential requires a high degree of\n * trust so you should only use it when other, more secure credential\n * types can't be used.\n */\nexport class UsernamePasswordCredential implements TokenCredential {\n  private identityClient: IdentityClient;\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private clientId: string;\n  private username: string;\n  private password: string;\n\n  /**\n   * Creates an instance of the UsernamePasswordCredential with the details\n   * needed to authenticate against Azure Active Directory with a username\n   * and password.\n   *\n   * @param tenantIdOrName - The Azure Active Directory tenant (directory) ID or name.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param username - The user account's e-mail address (user name).\n   * @param password - The user account's account password\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantIdOrName: string,\n    clientId: string,\n    username: string,\n    password: string,\n    options?: UsernamePasswordCredentialOptions\n  ) {\n    checkTenantId(logger, tenantIdOrName);\n\n    this.identityClient = new IdentityClient(options);\n    this.tenantId = tenantIdOrName;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n    this.clientId = clientId;\n    this.username = username;\n    this.password = password;\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if\n   * successful.  If authentication cannot be performed at this time, this method may\n   * return null.  If an error occurs during authentication, an {@link AuthenticationError}\n   * containing failure details will be thrown.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AccessToken | null> {\n    return tracingClient.withSpan(\n      \"UsernamePasswordCredential.getToken\",\n      options,\n      async (newOptions) => {\n        const tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds\n        );\n        newOptions.tenantId = tenantId;\n\n        const urlSuffix = getIdentityTokenEndpointSuffix(this.tenantId);\n        const params = new URLSearchParams({\n          response_type: \"token\",\n          grant_type: \"password\",\n          client_id: this.clientId,\n          username: this.username,\n          password: this.password,\n          scope: typeof scopes === \"string\" ? scopes : scopes.join(\" \"),\n        });\n        const webResource = createPipelineRequest({\n          url: `${this.identityClient.authorityHost}/${this.tenantId}/${urlSuffix}`,\n          method: \"POST\",\n          body: params.toString(),\n          headers: createHttpHeaders({\n            Accept: \"application/json\",\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n          }),\n          abortSignal: options && options.abortSignal,\n          tracingOptions: newOptions.tracingOptions,\n        });\n\n        const tokenResponse = await this.identityClient.sendTokenRequest(webResource);\n        logger.getToken.info(formatSuccess(scopes));\n        return (tokenResponse && tokenResponse.accessToken) || null;\n      }\n    );\n  }\n}\n"]}