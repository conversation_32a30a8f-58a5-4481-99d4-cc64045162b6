{"version": 3, "file": "usernamePasswordCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/usernamePasswordCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,yBAAyB,EACzB,mCAAmC,GACpC,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,oBAAoB,EAAE,MAAM,wCAAwC,CAAC;AAE9E,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,MAAM,MAAM,GAAG,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;AAE9D;;;;;GAKG;AACH,MAAM,OAAO,0BAA0B;IAKrC;;;;;;;;;;OAUG;IACH,YACE,QAAgB,EAChB,QAAgB,EAChB,QAAgB,EAChB,QAAgB,EAChB,UAA6C,EAAE;QAE/C,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACpD,MAAM,IAAI,KAAK,CACb,iMAAiM,CAClM,CAAC;SACH;QAED,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAoB,iCACnC,OAAO,KACV,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,QAAQ,EACR,sBAAsB,EAAE,OAAO,IAAI,EAAE,IACrC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EACnC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjC,MAAM,CACP,CAAC;YAEF,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QACzD,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { MsalUsernamePassword } from \"../msal/nodeFlows/msalUsernamePassword\";\nimport { UsernamePasswordCredentialOptions } from \"./usernamePasswordCredentialOptions\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"UsernamePasswordCredential\");\n\n/**\n * Enables authentication to Azure Active Directory with a user's\n * username and password. This credential requires a high degree of\n * trust so you should only use it when other, more secure credential\n * types can't be used.\n */\nexport class UsernamePasswordCredential implements TokenCredential {\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n\n  /**\n   * Creates an instance of the UsernamePasswordCredential with the details\n   * needed to authenticate against Azure Active Directory with a username\n   * and password.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory).\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param username - The user account's e-mail address (user name).\n   * @param password - The user account's account password\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    username: string,\n    password: string,\n    options: UsernamePasswordCredentialOptions = {}\n  ) {\n    if (!tenantId || !clientId || !username || !password) {\n      throw new Error(\n        \"UsernamePasswordCredential: tenantId, clientId, username and password are required parameters. To troubleshoot, visit https://aka.ms/azsdk/js/identity/usernamepasswordcredential/troubleshoot.\"\n      );\n    }\n\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n\n    this.msalFlow = new MsalUsernamePassword({\n      ...options,\n      logger,\n      clientId,\n      tenantId,\n      username,\n      password,\n      tokenCredentialOptions: options || {},\n    });\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the user provided the option `disableAutomaticAuthentication`,\n   * once the token can't be retrieved silently,\n   * this method won't attempt to request user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n          logger\n        );\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalFlow.getToken(arrayScopes, newOptions);\n      }\n    );\n  }\n}\n"]}