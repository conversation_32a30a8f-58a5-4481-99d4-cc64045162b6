{"version": 3, "file": "usernamePasswordCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/usernamePasswordCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AuthorityValidationOptions } from \"./authorityValidationOptions\";\nimport { CredentialPersistenceOptions } from \"./credentialPersistenceOptions\";\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\n\n/**\n * Defines options for the {@link UsernamePasswordCredential} class.\n */\nexport interface UsernamePasswordCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    CredentialPersistenceOptions,\n    AuthorityValidationOptions {}\n"]}