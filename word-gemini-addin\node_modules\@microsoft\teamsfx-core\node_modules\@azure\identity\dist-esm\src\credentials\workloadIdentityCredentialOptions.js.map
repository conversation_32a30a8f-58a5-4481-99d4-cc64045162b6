{"version": 3, "file": "workloadIdentityCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/workloadIdentityCredentialOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AuthorityValidationOptions } from \"./authorityValidationOptions\";\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\n\n/**\n * Options for the {@link WorkloadIdentityCredential}\n */\nexport interface WorkloadIdentityCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    AuthorityValidationOptions {\n  /**\n   * ID of the application's Azure Active Directory tenant. Also called its directory ID.\n   */\n  tenantId?: string;\n  /**\n   * The client ID of an Azure AD app registration.\n   */\n  clientId?: string;\n  /**\n   * The path to a file containing a Kubernetes service account token that authenticates the identity.\n   */\n  tokenFilePath?: string;\n}\n"]}