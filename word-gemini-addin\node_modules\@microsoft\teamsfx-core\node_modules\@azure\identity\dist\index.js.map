{"version": 3, "file": "index.js", "sources": ["../src/errors.ts", "../src/util/logging.ts", "../src/constants.ts", "../src/msal/utils.ts", "../src/util/processMultiTenantRequest.ts", "../src/util/tenantIdUtils.ts", "../src/util/identityTokenEndpoint.ts", "../src/util/tracing.ts", "../src/credentials/managedIdentityCredential/constants.ts", "../src/credentials/managedIdentityCredential/utils.ts", "../src/client/identityClient.ts", "../src/regionalAuthority.ts", "../src/msal/nodeFlows/msalNodeCommon.ts", "../src/credentials/visualStudioCodeCredential.ts", "../src/plugins/consumer.ts", "../src/credentials/managedIdentityCredential/appServiceMsi2017.ts", "../src/credentials/managedIdentityCredential/cloudShellMsi.ts", "../src/credentials/managedIdentityCredential/imdsMsi.ts", "../src/credentials/managedIdentityCredential/arcMsi.ts", "../src/msal/nodeFlows/msalClientAssertion.ts", "../src/credentials/clientAssertionCredential.ts", "../src/credentials/workloadIdentityCredential.ts", "../src/credentials/managedIdentityCredential/tokenExchangeMsi.ts", "../src/credentials/managedIdentityCredential/fabricMsi.ts", "../src/credentials/managedIdentityCredential/appServiceMsi2019.ts", "../src/credentials/managedIdentityCredential/index.ts", "../src/util/scopeUtils.ts", "../src/credentials/azureCliCredential.ts", "../src/util/processUtils.ts", "../src/credentials/azurePowerShellCredential.ts", "../src/credentials/chainedTokenCredential.ts", "../src/msal/nodeFlows/msalClientCertificate.ts", "../src/credentials/clientCertificateCredential.ts", "../src/msal/nodeFlows/msalClientSecret.ts", "../src/credentials/clientSecretCredential.ts", "../src/msal/nodeFlows/msalUsernamePassword.ts", "../src/credentials/usernamePasswordCredential.ts", "../src/credentials/environmentCredential.ts", "../src/credentials/azureDeveloperCliCredential.ts", "../src/credentials/defaultAzureCredential.ts", "../src/msal/nodeFlows/msalOpenBrowser.ts", "../src/credentials/interactiveBrowserCredential.ts", "../src/msal/nodeFlows/msalDeviceCode.ts", "../src/credentials/deviceCodeCredential.ts", "../src/msal/nodeFlows/msalAuthorizationCode.ts", "../src/credentials/authorizationCodeCredential.ts", "../src/msal/nodeFlows/msalOnBehalfOf.ts", "../src/credentials/onBehalfOfCredential.ts", "../src/index.ts"], "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { GetTokenOptions } from \"@azure/core-auth\";\n\n/**\n * See the official documentation for more details:\n *\n * https://learn.microsoft.com/en-us/azure/active-directory/develop/v1-protocols-oauth-code#error-response-1\n *\n * NOTE: This documentation is for v1 OAuth support but the same error\n * response details still apply to v2.\n */\nexport interface ErrorResponse {\n  /**\n   * The string identifier for the error.\n   */\n  error: string;\n\n  /**\n   * The error's description.\n   */\n  errorDescription: string;\n\n  /**\n   * An array of codes pertaining to the error(s) that occurred.\n   */\n  errorCodes?: number[];\n\n  /**\n   * The timestamp at which the error occurred.\n   */\n  timestamp?: string;\n\n  /**\n   * The trace identifier for this error occurrence.\n   */\n  traceId?: string;\n\n  /**\n   * The correlation ID to be used for tracking the source of the error.\n   */\n  correlationId?: string;\n}\n\n/**\n * Used for internal deserialization of OAuth responses. Public model is ErrorResponse\n * @internal\n */\nexport interface OAuthErrorResponse {\n  error: string;\n  error_description: string;\n  error_codes?: number[];\n  timestamp?: string;\n  trace_id?: string;\n  correlation_id?: string;\n}\n\nfunction isErrorResponse(errorResponse: any): errorResponse is OAuthErrorResponse {\n  return (\n    errorResponse &&\n    typeof errorResponse.error === \"string\" &&\n    typeof errorResponse.error_description === \"string\"\n  );\n}\n\n/**\n * The Error.name value of an CredentialUnavailable\n */\nexport const CredentialUnavailableErrorName = \"CredentialUnavailableError\";\n\n/**\n * This signifies that the credential that was tried in a chained credential\n * was not available to be used as the credential. Rather than treating this as\n * an error that should halt the chain, it's caught and the chain continues\n */\nexport class CredentialUnavailableError extends Error {\n  constructor(message?: string) {\n    super(message);\n    this.name = CredentialUnavailableErrorName;\n  }\n}\n\n/**\n * The Error.name value of an AuthenticationError\n */\nexport const AuthenticationErrorName = \"AuthenticationError\";\n\n/**\n * Provides details about a failure to authenticate with Azure Active\n * Directory.  The `errorResponse` field contains more details about\n * the specific failure.\n */\nexport class AuthenticationError extends Error {\n  /**\n   * The HTTP status code returned from the authentication request.\n   */\n  public readonly statusCode: number;\n\n  /**\n   * The error response details.\n   */\n  public readonly errorResponse: ErrorResponse;\n\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  constructor(statusCode: number, errorBody: object | string | undefined | null) {\n    let errorResponse: ErrorResponse = {\n      error: \"unknown\",\n      errorDescription: \"An unknown error occurred and no additional details are available.\",\n    };\n\n    if (isErrorResponse(errorBody)) {\n      errorResponse = convertOAuthErrorResponseToErrorResponse(errorBody);\n    } else if (typeof errorBody === \"string\") {\n      try {\n        // Most error responses will contain JSON-formatted error details\n        // in the response body\n        const oauthErrorResponse: OAuthErrorResponse = JSON.parse(errorBody);\n        errorResponse = convertOAuthErrorResponseToErrorResponse(oauthErrorResponse);\n      } catch (e: any) {\n        if (statusCode === 400) {\n          errorResponse = {\n            error: \"authority_not_found\",\n            errorDescription: \"The specified authority URL was not found.\",\n          };\n        } else {\n          errorResponse = {\n            error: \"unknown_error\",\n            errorDescription: `An unknown error has occurred. Response body:\\n\\n${errorBody}`,\n          };\n        }\n      }\n    } else {\n      errorResponse = {\n        error: \"unknown_error\",\n        errorDescription: \"An unknown error occurred and no additional details are available.\",\n      };\n    }\n\n    super(\n      `${errorResponse.error} Status code: ${statusCode}\\nMore details:\\n${errorResponse.errorDescription}`\n    );\n    this.statusCode = statusCode;\n    this.errorResponse = errorResponse;\n\n    // Ensure that this type reports the correct name\n    this.name = AuthenticationErrorName;\n  }\n}\n\n/**\n * The Error.name value of an AggregateAuthenticationError\n */\nexport const AggregateAuthenticationErrorName = \"AggregateAuthenticationError\";\n\n/**\n * Provides an `errors` array containing {@link AuthenticationError} instance\n * for authentication failures from credentials in a {@link ChainedTokenCredential}.\n */\nexport class AggregateAuthenticationError extends Error {\n  /**\n   * The array of error objects that were thrown while trying to authenticate\n   * with the credentials in a {@link ChainedTokenCredential}.\n   */\n  public errors: any[];\n\n  constructor(errors: any[], errorMessage?: string) {\n    const errorDetail = errors.join(\"\\n\");\n    super(`${errorMessage}\\n${errorDetail}`);\n    this.errors = errors;\n\n    // Ensure that this type reports the correct name\n    this.name = AggregateAuthenticationErrorName;\n  }\n}\n\nfunction convertOAuthErrorResponseToErrorResponse(errorBody: OAuthErrorResponse): ErrorResponse {\n  return {\n    error: errorBody.error,\n    errorDescription: errorBody.error_description,\n    correlationId: errorBody.correlation_id,\n    errorCodes: errorBody.error_codes,\n    timestamp: errorBody.timestamp,\n    traceId: errorBody.trace_id,\n  };\n}\n\n/**\n * Optional parameters to the {@link AuthenticationRequiredError}\n */\nexport interface AuthenticationRequiredErrorOptions {\n  /**\n   * The list of scopes for which the token will have access.\n   */\n  scopes: string[];\n  /**\n   * The options passed to the getToken request.\n   */\n  getTokenOptions?: GetTokenOptions;\n  /**\n   * The message of the error.\n   */\n  message?: string;\n}\n\n/**\n * Error used to enforce authentication after trying to retrieve a token silently.\n */\nexport class AuthenticationRequiredError extends Error {\n  /**\n   * The list of scopes for which the token will have access.\n   */\n  public scopes: string[];\n  /**\n   * The options passed to the getToken request.\n   */\n  public getTokenOptions?: GetTokenOptions;\n\n  constructor(\n    /**\n     * Optional parameters. A message can be specified. The {@link GetTokenOptions} of the request can also be specified to more easily associate the error with the received parameters.\n     */\n    options: AuthenticationRequiredErrorOptions\n  ) {\n    super(options.message);\n    this.scopes = options.scopes;\n    this.getTokenOptions = options.getTokenOptions;\n    this.name = \"AuthenticationRequiredError\";\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AzureLogger, createClientLogger } from \"@azure/logger\";\n\n/**\n * The AzureLogger used for all clients within the identity package\n */\nexport const logger = createClientLogger(\"identity\");\n\ninterface EnvironmentAccumulator {\n  missing: string[];\n  assigned: string[];\n}\n\n/**\n * Separates a list of environment variable names into a plain object with two arrays: an array of missing environment variables and another array with assigned environment variables.\n * @param supportedEnvVars - List of environment variable names\n */\nexport function processEnvVars(supportedEnvVars: string[]): EnvironmentAccumulator {\n  return supportedEnvVars.reduce(\n    (acc: EnvironmentAccumulator, envVariable: string) => {\n      if (process.env[envVariable]) {\n        acc.assigned.push(envVariable);\n      } else {\n        acc.missing.push(envVariable);\n      }\n      return acc;\n    },\n    { missing: [], assigned: [] }\n  );\n}\n\n/**\n * Based on a given list of environment variable names,\n * logs the environment variables currently assigned during the usage of a credential that goes by the given name.\n * @param credentialName - Name of the credential in use\n * @param supportedEnvVars - List of environment variables supported by that credential\n */\nexport function logEnvVars(credentialName: string, supportedEnvVars: string[]): void {\n  const { assigned } = processEnvVars(supportedEnvVars);\n  logger.info(\n    `${credentialName} => Found the following environment variables: ${assigned.join(\", \")}`\n  );\n}\n\n/**\n * Formatting the success event on the credentials\n */\nexport function formatSuccess(scope: string | string[]): string {\n  return `SUCCESS. Scopes: ${Array.isArray(scope) ? scope.join(\", \") : scope}.`;\n}\n\n/**\n * Formatting the success event on the credentials\n */\nexport function formatError(scope: string | string[] | undefined, error: Error | string): string {\n  let message = \"ERROR.\";\n  if (scope?.length) {\n    message += ` Scopes: ${Array.isArray(scope) ? scope.join(\", \") : scope}.`;\n  }\n  return `${message} Error message: ${typeof error === \"string\" ? error : error.message}.`;\n}\n\n/**\n * A CredentialLoggerInstance is a logger properly formatted to work in a credential's constructor, and its methods.\n */\nexport interface CredentialLoggerInstance {\n  title: string;\n  fullTitle: string;\n  info(message: string): void;\n  warning(message: string): void;\n  verbose(message: string): void;\n  /**\n   * The logging functions for warning and error are intentionally left out, since we want the identity logging to be at the info level.\n   * Otherwise, they would look like:\n   *\n   *   warning(message: string): void;\n   *   error(err: Error): void;\n   */\n}\n\n/**\n * Generates a CredentialLoggerInstance.\n *\n * It logs with the format:\n *\n *   `[title] => [message]`\n *\n */\nexport function credentialLoggerInstance(\n  title: string,\n  parent?: CredentialLoggerInstance,\n  log: AzureLogger = logger\n): CredentialLoggerInstance {\n  const fullTitle = parent ? `${parent.fullTitle} ${title}` : title;\n\n  function info(message: string): void {\n    log.info(`${fullTitle} =>`, message);\n  }\n\n  function warning(message: string): void {\n    log.warning(`${fullTitle} =>`, message);\n  }\n\n  function verbose(message: string): void {\n    log.verbose(`${fullTitle} =>`, message);\n  }\n  return {\n    title,\n    fullTitle,\n    info,\n    warning,\n    verbose,\n  };\n}\n\n/**\n * A CredentialLogger is a logger declared at the credential's constructor, and used at any point in the credential.\n * It has all the properties of a CredentialLoggerInstance, plus other logger instances, one per method.\n */\nexport interface CredentialLogger extends CredentialLoggerInstance {\n  parent: AzureLogger;\n  getToken: CredentialLoggerInstance;\n}\n\n/**\n * Generates a CredentialLogger, which is a logger declared at the credential's constructor, and used at any point in the credential.\n * It has all the properties of a CredentialLoggerInstance, plus other logger instances, one per method.\n *\n * It logs with the format:\n *\n *   `[title] => [message]`\n *   `[title] => getToken() => [message]`\n *\n */\nexport function credentialLogger(title: string, log: AzureLogger = logger): CredentialLogger {\n  const credLogger = credentialLoggerInstance(title, undefined, log);\n  return {\n    ...credLogger,\n    parent: log,\n    getToken: credentialLoggerInstance(\"=> getToken()\", credLogger, log),\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Current version of the `@azure/identity` package.\n */\n\nexport const SDK_VERSION = `3.4.2`;\n\n/**\n * The default client ID for authentication\n * @internal\n */\n// TODO: temporary - this is the Azure CLI clientID - we'll replace it when\n// Developer Sign On application is available\n// https://github.com/Azure/azure-sdk-for-net/blob/main/sdk/identity/Azure.Identity/src/Constants.cs#L9\nexport const DeveloperSignOnClientId = \"04b07795-8ddb-461a-bbee-02f9e1bf7b46\";\n\n/**\n * The default tenant for authentication\n * @internal\n */\nexport const DefaultTenantId = \"common\";\n\n/**\n * A list of known Azure authority hosts\n */\nexport enum AzureAuthorityHosts {\n  /**\n   * China-based Azure Authority Host\n   */\n  AzureChina = \"https://login.chinacloudapi.cn\",\n  /**\n   * Germany-based Azure Authority Host\n   */\n  AzureGermany = \"https://login.microsoftonline.de\",\n  /**\n   * US Government Azure Authority Host\n   */\n  AzureGovernment = \"https://login.microsoftonline.us\",\n  /**\n   * Public Cloud Azure Authority Host\n   */\n  AzurePublicCloud = \"https://login.microsoftonline.com\",\n}\n\n/**\n * The default authority host.\n */\nexport const DefaultAuthorityHost = AzureAuthorityHosts.AzurePublicCloud;\n\n/**\n * Allow acquiring tokens for any tenant for multi-tentant auth.\n */\nexport const ALL_TENANTS: string[] = [\"*\"];\n\nexport const CACHE_CAE_SUFFIX = \".cae\";\nexport const CACHE_NON_CAE_SUFFIX = \".nocae\";\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msal<PERSON><PERSON>mon from \"@azure/msal-node\";\n\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { AuthenticationRecord, MsalAccountInfo, MsalResult, MsalToken } from \"./types\";\nimport { AuthenticationRequiredError, CredentialUnavailableError } from \"../errors\";\nimport { CredentialLogger, formatError, formatSuccess } from \"../util/logging\";\nimport { DefaultAuthorityHost, DefaultTenantId } from \"../constants\";\nimport { AbortError } from \"@azure/abort-controller\";\nimport { MsalFlowOptions } from \"./flows\";\nimport { isNode, randomUUID } from \"@azure/core-util\";\nimport { AzureLogLevel } from \"@azure/logger\";\n\nexport interface ILoggerCallback {\n  (level: msalCommon.LogLevel, message: string, containsPii: boolean): void;\n}\n\n/**\n * Latest AuthenticationRecord version\n * @internal\n */\nconst LatestAuthenticationRecordVersion = \"1.0\";\n\n/**\n * Ensures the validity of the MSAL token\n * @internal\n */\nexport function ensureValidMsalToken(\n  scopes: string | string[],\n  logger: CredentialLogger,\n  msalToken?: MsalToken,\n  getTokenOptions?: GetTokenOptions\n): void {\n  const error = (message: string): Error => {\n    logger.getToken.info(message);\n    return new AuthenticationRequiredError({\n      scopes: Array.isArray(scopes) ? scopes : [scopes],\n      getTokenOptions,\n      message,\n    });\n  };\n  if (!msalToken) {\n    throw error(\"No response\");\n  }\n  if (!msalToken.expiresOn) {\n    throw error(`Response had no \"expiresOn\" property.`);\n  }\n  if (!msalToken.accessToken) {\n    throw error(`Response had no \"accessToken\" property.`);\n  }\n}\n\n/**\n * Generates a valid authority by combining a host with a tenantId.\n * @internal\n */\nexport function getAuthority(tenantId: string, host?: string): string {\n  if (!host) {\n    host = DefaultAuthorityHost;\n  }\n  if (new RegExp(`${tenantId}/?$`).test(host)) {\n    return host;\n  }\n  if (host.endsWith(\"/\")) {\n    return host + tenantId;\n  } else {\n    return `${host}/${tenantId}`;\n  }\n}\n\n/**\n * Generates the known authorities.\n * If the Tenant Id is `adfs`, the authority can't be validated since the format won't match the expected one.\n * For that reason, we have to force MSAL to disable validating the authority\n * by sending it within the known authorities in the MSAL configuration.\n * @internal\n */\nexport function getKnownAuthorities(\n  tenantId: string,\n  authorityHost: string,\n  disableInstanceDiscovery?: boolean\n): string[] {\n  if ((tenantId === \"adfs\" && authorityHost) || disableInstanceDiscovery) {\n    return [authorityHost];\n  }\n  return [];\n}\n\n/**\n * Generates a logger that can be passed to the MSAL clients.\n * @param logger - The logger of the credential.\n * @internal\n */\nexport const defaultLoggerCallback: (\n  logger: CredentialLogger,\n  platform?: \"Node\" | \"Browser\"\n) => ILoggerCallback =\n  (logger: CredentialLogger, platform: \"Node\" | \"Browser\" = isNode ? \"Node\" : \"Browser\") =>\n  (level, message, containsPii): void => {\n    if (containsPii) {\n      return;\n    }\n    switch (level) {\n      case msalCommon.LogLevel.Error:\n        logger.info(`MSAL ${platform} V2 error: ${message}`);\n        return;\n      case msalCommon.LogLevel.Info:\n        logger.info(`MSAL ${platform} V2 info message: ${message}`);\n        return;\n      case msalCommon.LogLevel.Verbose:\n        logger.info(`MSAL ${platform} V2 verbose message: ${message}`);\n        return;\n      case msalCommon.LogLevel.Warning:\n        logger.info(`MSAL ${platform} V2 warning: ${message}`);\n        return;\n    }\n  };\n\n/**\n * @internal\n */\nexport function getMSALLogLevel(logLevel: AzureLogLevel | undefined): msalCommon.LogLevel {\n  switch (logLevel) {\n    case \"error\":\n      return msalCommon.LogLevel.Error;\n    case \"info\":\n      return msalCommon.LogLevel.Info;\n    case \"verbose\":\n      return msalCommon.LogLevel.Verbose;\n    case \"warning\":\n      return msalCommon.LogLevel.Warning;\n    default:\n      // default msal logging level should be Info\n      return msalCommon.LogLevel.Info;\n  }\n}\n\n/**\n * The common utility functions for the MSAL clients.\n * Defined as a class so that the classes extending this one can have access to its methods and protected properties.\n *\n * It keeps track of a logger and an in-memory copy of the AuthenticationRecord.\n *\n * @internal\n */\nexport class MsalBaseUtilities {\n  protected logger: CredentialLogger;\n  protected account: AuthenticationRecord | undefined;\n\n  constructor(options: MsalFlowOptions) {\n    this.logger = options.logger;\n    this.account = options.authenticationRecord;\n  }\n\n  /**\n   * Generates a UUID\n   */\n  generateUuid(): string {\n    return randomUUID();\n  }\n\n  /**\n   * Handles the MSAL authentication result.\n   * If the result has an account, we update the local account reference.\n   * If the token received is invalid, an error will be thrown depending on what's missing.\n   */\n  protected handleResult(\n    scopes: string | string[],\n    clientId: string,\n    result?: MsalResult,\n    getTokenOptions?: GetTokenOptions\n  ): AccessToken {\n    if (result?.account) {\n      this.account = msalToPublic(clientId, result.account);\n    }\n    ensureValidMsalToken(scopes, this.logger, result, getTokenOptions);\n    this.logger.getToken.info(formatSuccess(scopes));\n    return {\n      token: result!.accessToken!,\n      expiresOnTimestamp: result!.expiresOn!.getTime(),\n    };\n  }\n\n  /**\n   * Handles MSAL errors.\n   */\n  protected handleError(scopes: string[], error: Error, getTokenOptions?: GetTokenOptions): Error {\n    if (\n      error.name === \"AuthError\" ||\n      error.name === \"ClientAuthError\" ||\n      error.name === \"BrowserAuthError\"\n    ) {\n      const msalError = error as msalCommon.AuthError;\n      switch (msalError.errorCode) {\n        case \"endpoints_resolution_error\":\n          this.logger.info(formatError(scopes, error.message));\n          return new CredentialUnavailableError(error.message);\n        case \"device_code_polling_cancelled\":\n          return new AbortError(\"The authentication has been aborted by the caller.\");\n        case \"consent_required\":\n        case \"interaction_required\":\n        case \"login_required\":\n          this.logger.info(\n            formatError(scopes, `Authentication returned errorCode ${msalError.errorCode}`)\n          );\n          break;\n        default:\n          this.logger.info(formatError(scopes, `Failed to acquire token: ${error.message}`));\n          break;\n      }\n    }\n    if (\n      error.name === \"ClientConfigurationError\" ||\n      error.name === \"BrowserConfigurationAuthError\" ||\n      error.name === \"AbortError\"\n    ) {\n      return error;\n    }\n    return new AuthenticationRequiredError({ scopes, getTokenOptions, message: error.message });\n  }\n}\n\n// transformations.ts\n\nexport function publicToMsal(account: AuthenticationRecord): msalCommon.AccountInfo {\n  const [environment] = account.authority.match(/([a-z]*\\.[a-z]*\\.[a-z]*)/) || [\"\"];\n  return {\n    ...account,\n    localAccountId: account.homeAccountId,\n    environment,\n  };\n}\n\nexport function msalToPublic(clientId: string, account: MsalAccountInfo): AuthenticationRecord {\n  const record = {\n    authority: getAuthority(account.tenantId, account.environment),\n    homeAccountId: account.homeAccountId,\n    tenantId: account.tenantId || DefaultTenantId,\n    username: account.username,\n    clientId,\n    version: LatestAuthenticationRecordVersion,\n  };\n  return record;\n}\n\n/**\n * Serializes an `AuthenticationRecord` into a string.\n *\n * The output of a serialized authentication record will contain the following properties:\n *\n * - \"authority\"\n * - \"homeAccountId\"\n * - \"clientId\"\n * - \"tenantId\"\n * - \"username\"\n * - \"version\"\n *\n * To later convert this string to a serialized `AuthenticationRecord`, please use the exported function `deserializeAuthenticationRecord()`.\n */\nexport function serializeAuthenticationRecord(record: AuthenticationRecord): string {\n  return JSON.stringify(record);\n}\n\n/**\n * Deserializes a previously serialized authentication record from a string into an object.\n *\n * The input string must contain the following properties:\n *\n * - \"authority\"\n * - \"homeAccountId\"\n * - \"clientId\"\n * - \"tenantId\"\n * - \"username\"\n * - \"version\"\n *\n * If the version we receive is unsupported, an error will be thrown.\n *\n * At the moment, the only available version is: \"1.0\", which is always set when the authentication record is serialized.\n *\n * @param serializedRecord - Authentication record previously serialized into string.\n * @returns AuthenticationRecord.\n */\nexport function deserializeAuthenticationRecord(serializedRecord: string): AuthenticationRecord {\n  const parsed: AuthenticationRecord & { version?: string } = JSON.parse(serializedRecord);\n\n  if (parsed.version && parsed.version !== LatestAuthenticationRecordVersion) {\n    throw Error(\"Unsupported AuthenticationRecord version\");\n  }\n\n  return parsed;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { GetTokenOptions } from \"@azure/core-auth\";\nimport { CredentialUnavailableError } from \"../errors\";\nimport { CredentialLogger } from \"./logging\";\n\nfunction createConfigurationErrorMessage(tenantId: string): string {\n  return `The current credential is not configured to acquire tokens for tenant ${tenantId}. To enable acquiring tokens for this tenant add it to the AdditionallyAllowedTenants on the credential options, or add \"*\" to AdditionallyAllowedTenants to allow acquiring tokens for any tenant.`;\n}\n\n/**\n * Of getToken contains a tenantId, this functions allows picking this tenantId as the appropriate for authentication,\n * unless multitenant authentication has been disabled through the AZURE_IDENTITY_DISABLE_MULTITENANTAUTH (on Node.js),\n * or unless the original tenant Id is `adfs`.\n * @internal\n */\nexport function processMultiTenantRequest(\n  tenantId?: string,\n  getTokenOptions?: GetTokenOptions,\n  additionallyAllowedTenantIds: string[] = [],\n  logger?: CredentialLogger\n): string | undefined {\n  let resolvedTenantId: string | undefined;\n  if (process.env.AZURE_IDENTITY_DISABLE_MULTITENANTAUTH) {\n    resolvedTenantId = tenantId;\n  } else if (tenantId === \"adfs\") {\n    resolvedTenantId = tenantId;\n  } else {\n    resolvedTenantId = getTokenOptions?.tenantId ?? tenantId;\n  }\n  if (\n    tenantId &&\n    resolvedTenantId !== tenantId &&\n    !additionallyAllowedTenantIds.includes(\"*\") &&\n    !additionallyAllowedTenantIds.some((t) => t.localeCompare(resolvedTenantId!) === 0)\n  ) {\n    const message = createConfigurationErrorMessage(tenantId);\n    logger?.info(message);\n    throw new CredentialUnavailableError(message);\n  }\n\n  return resolvedTenantId;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { ALL_TENANTS, DeveloperSignOnClientId } from \"../constants\";\nimport { CredentialLogger, formatError } from \"./logging\";\nexport { processMultiTenantRequest } from \"./processMultiTenantRequest\";\n\n/**\n * @internal\n */\nexport function checkTenantId(logger: CredentialLogger, tenantId: string): void {\n  if (!tenantId.match(/^[0-9a-zA-Z-.]+$/)) {\n    const error = new Error(\n      \"Invalid tenant id provided. You can locate your tenant id by following the instructions listed here: https://learn.microsoft.com/partner-center/find-ids-and-domain-names.\"\n    );\n    logger.info(formatError(\"\", error));\n    throw error;\n  }\n}\n\n/**\n * @internal\n */\nexport function resolveTenantId(\n  logger: CredentialLogger,\n  tenantId?: string,\n  clientId?: string\n): string {\n  if (tenantId) {\n    checkTenantId(logger, tenantId);\n    return tenantId;\n  }\n  if (!clientId) {\n    clientId = DeveloperSignOnClientId;\n  }\n  if (clientId !== DeveloperSignOnClientId) {\n    return \"common\";\n  }\n  return \"organizations\";\n}\n\n/**\n * @internal\n */\nexport function resolveAdditionallyAllowedTenantIds(\n  additionallyAllowedTenants?: string[]\n): string[] {\n  if (!additionallyAllowedTenants || additionallyAllowedTenants.length === 0) {\n    return [];\n  }\n\n  if (additionallyAllowedTenants.includes(\"*\")) {\n    return ALL_TENANTS;\n  }\n\n  return additionallyAllowedTenants;\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport function getIdentityTokenEndpointSuffix(tenantId: string): string {\n  if (tenantId === \"adfs\") {\n    return \"oauth2/token\";\n  } else {\n    return \"oauth2/v2.0/token\";\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { SDK_VERSION } from \"../constants\";\nimport { createTracingClient } from \"@azure/core-tracing\";\n\n/**\n * Creates a span using the global tracer.\n * @internal\n */\nexport const tracingClient = createTracingClient({\n  namespace: \"Microsoft.AAD\",\n  packageName: \"@azure/identity\",\n  packageVersion: SDK_VERSION,\n});\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport const DefaultScopeSuffix = \"/.default\";\nexport const imdsHost = \"http://***************\";\nexport const imdsEndpointPath = \"/metadata/identity/oauth2/token\";\nexport const imdsApiVersion = \"2018-02-01\";\nexport const azureArcAPIVersion = \"2019-11-01\";\nexport const azureFabricVersion = \"2019-07-01-preview\";\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { DefaultScopeSuffix } from \"./constants\";\n\n/**\n * Most MSIs send requests to the IMDS endpoint, or a similar endpoint.\n * These are GET requests that require sending a `resource` parameter on the query.\n * This resource can be derived from the scopes received through the getToken call, as long as only one scope is received.\n * Multiple scopes assume that the resulting token will have access to multiple resources, which won't be the case.\n *\n * For that reason, when we encounter multiple scopes, we return undefined.\n * It's up to the individual MSI implementations to throw the errors (which helps us provide less generic errors).\n */\nexport function mapScopesToResource(scopes: string | string[]): string | undefined {\n  let scope = \"\";\n  if (Array.isArray(scopes)) {\n    if (scopes.length !== 1) {\n      return;\n    }\n\n    scope = scopes[0];\n  } else if (typeof scopes === \"string\") {\n    scope = scopes;\n  }\n\n  if (!scope.endsWith(DefaultScopeSuffix)) {\n    return scope;\n  }\n\n  return scope.substr(0, scope.lastIndexOf(DefaultScopeSuffix));\n}\n\n/**\n * Internal type roughly matching the raw responses of the authentication endpoints.\n *\n * @internal\n */\nexport interface TokenResponseParsedBody {\n  access_token?: string;\n  refresh_token?: string;\n  expires_in: number;\n  expires_on?: number | string;\n  refresh_in?: number;\n}\n\n/**\n * Given a token response, return the expiration timestamp as the number of milliseconds from the Unix epoch.\n * @param body - A parsed response body from the authentication endpoint.\n */\nexport function parseExpirationTimestamp(body: TokenResponseParsedBody): number {\n  if (typeof body.expires_on === \"number\") {\n    return body.expires_on * 1000;\n  }\n\n  if (typeof body.expires_on === \"string\") {\n    const asNumber = +body.expires_on;\n    if (!isNaN(asNumber)) {\n      return asNumber * 1000;\n    }\n\n    const asDate = Date.parse(body.expires_on);\n    if (!isNaN(asDate)) {\n      return asDate;\n    }\n  }\n\n  if (typeof body.expires_in === \"number\") {\n    return Date.now() + body.expires_in * 1000;\n  }\n\n  throw new Error(\n    `Failed to parse token expiration from body. expires_in=\"${body.expires_in}\", expires_on=\"${body.expires_on}\"`\n  );\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport type { INetworkModule, NetworkRequestOptions, NetworkResponse } from \"@azure/msal-node\";\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { ServiceClient } from \"@azure/core-client\";\nimport { isNode } from \"@azure/core-util\";\nimport {\n  PipelineRequest,\n  PipelineResponse,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { AbortController, AbortSignalLike } from \"@azure/abort-controller\";\nimport { AuthenticationError, AuthenticationErrorName } from \"../errors\";\nimport { getIdentityTokenEndpointSuffix } from \"../util/identityTokenEndpoint\";\nimport { DefaultAuthorityHost, SDK_VERSION } from \"../constants\";\nimport { tracingClient } from \"../util/tracing\";\nimport { logger } from \"../util/logging\";\nimport { TokenCredentialOptions } from \"../tokenCredentialOptions\";\nimport {\n  TokenResponseParsedBody,\n  parseExpirationTimestamp,\n} from \"../credentials/managedIdentityCredential/utils\";\n\nconst noCorrelationId = \"noCorrelationId\";\n\n/**\n * An internal type used to communicate details of a token request's\n * response that should not be sent back as part of the access token.\n */\nexport interface TokenResponse {\n  /**\n   * The AccessToken to be returned from getToken.\n   */\n  accessToken: AccessToken;\n  /**\n   * The refresh token if the 'offline_access' scope was used.\n   */\n  refreshToken?: string;\n}\n\n/**\n * @internal\n */\nexport function getIdentityClientAuthorityHost(options?: TokenCredentialOptions): string {\n  // The authorityHost can come from options or from the AZURE_AUTHORITY_HOST environment variable.\n  let authorityHost = options?.authorityHost;\n\n  // The AZURE_AUTHORITY_HOST environment variable can only be provided in Node.js.\n  if (isNode) {\n    authorityHost = authorityHost ?? process.env.AZURE_AUTHORITY_HOST;\n  }\n\n  // If the authorityHost is not provided, we use the default one from the public cloud: https://login.microsoftonline.com\n  return authorityHost ?? DefaultAuthorityHost;\n}\n\n/**\n * The network module used by the Identity credentials.\n *\n * It allows for credentials to abort any pending request independently of the MSAL flow,\n * by calling to the `abortRequests()` method.\n *\n */\nexport class IdentityClient extends ServiceClient implements INetworkModule {\n  public authorityHost: string;\n  private allowLoggingAccountIdentifiers?: boolean;\n  private abortControllers: Map<string, AbortController[] | undefined>;\n  // used for WorkloadIdentity\n  private tokenCredentialOptions: TokenCredentialOptions;\n\n  constructor(options?: TokenCredentialOptions) {\n    const packageDetails = `azsdk-js-identity/${SDK_VERSION}`;\n    const userAgentPrefix = options?.userAgentOptions?.userAgentPrefix\n      ? `${options.userAgentOptions.userAgentPrefix} ${packageDetails}`\n      : `${packageDetails}`;\n\n    const baseUri = getIdentityClientAuthorityHost(options);\n    if (!baseUri.startsWith(\"https:\")) {\n      throw new Error(\"The authorityHost address must use the 'https' protocol.\");\n    }\n\n    super({\n      requestContentType: \"application/json; charset=utf-8\",\n      retryOptions: {\n        maxRetries: 3,\n      },\n      ...options,\n      userAgentOptions: {\n        userAgentPrefix,\n      },\n      baseUri,\n    });\n\n    this.authorityHost = baseUri;\n    this.abortControllers = new Map();\n    this.allowLoggingAccountIdentifiers = options?.loggingOptions?.allowLoggingAccountIdentifiers;\n    // used for WorkloadIdentity\n    this.tokenCredentialOptions = { ...options };\n  }\n\n  async sendTokenRequest(request: PipelineRequest): Promise<TokenResponse | null> {\n    logger.info(`IdentityClient: sending token request to [${request.url}]`);\n    const response = await this.sendRequest(request);\n\n    if (response.bodyAsText && (response.status === 200 || response.status === 201)) {\n      const parsedBody: TokenResponseParsedBody = JSON.parse(response.bodyAsText);\n\n      if (!parsedBody.access_token) {\n        return null;\n      }\n\n      this.logIdentifiers(response);\n\n      const token = {\n        accessToken: {\n          token: parsedBody.access_token,\n          expiresOnTimestamp: parseExpirationTimestamp(parsedBody),\n        },\n        refreshToken: parsedBody.refresh_token,\n      };\n\n      logger.info(\n        `IdentityClient: [${request.url}] token acquired, expires on ${token.accessToken.expiresOnTimestamp}`\n      );\n      return token;\n    } else {\n      const error = new AuthenticationError(response.status, response.bodyAsText);\n      logger.warning(\n        `IdentityClient: authentication error. HTTP status: ${response.status}, ${error.errorResponse.errorDescription}`\n      );\n      throw error;\n    }\n  }\n\n  async refreshAccessToken(\n    tenantId: string,\n    clientId: string,\n    scopes: string,\n    refreshToken: string | undefined,\n    clientSecret: string | undefined,\n    options: GetTokenOptions = {}\n  ): Promise<TokenResponse | null> {\n    if (refreshToken === undefined) {\n      return null;\n    }\n    logger.info(\n      `IdentityClient: refreshing access token with client ID: ${clientId}, scopes: ${scopes} started`\n    );\n\n    const refreshParams = {\n      grant_type: \"refresh_token\",\n      client_id: clientId,\n      refresh_token: refreshToken,\n      scope: scopes,\n    };\n\n    if (clientSecret !== undefined) {\n      (refreshParams as any).client_secret = clientSecret;\n    }\n\n    const query = new URLSearchParams(refreshParams);\n\n    return tracingClient.withSpan(\n      \"IdentityClient.refreshAccessToken\",\n      options,\n      async (updatedOptions) => {\n        try {\n          const urlSuffix = getIdentityTokenEndpointSuffix(tenantId);\n          const request = createPipelineRequest({\n            url: `${this.authorityHost}/${tenantId}/${urlSuffix}`,\n            method: \"POST\",\n            body: query.toString(),\n            abortSignal: options.abortSignal,\n            headers: createHttpHeaders({\n              Accept: \"application/json\",\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\n            }),\n            tracingOptions: updatedOptions.tracingOptions,\n          });\n\n          const response = await this.sendTokenRequest(request);\n          logger.info(`IdentityClient: refreshed token for client ID: ${clientId}`);\n          return response;\n        } catch (err: any) {\n          if (\n            err.name === AuthenticationErrorName &&\n            err.errorResponse.error === \"interaction_required\"\n          ) {\n            // It's likely that the refresh token has expired, so\n            // return null so that the credential implementation will\n            // initiate the authentication flow again.\n            logger.info(`IdentityClient: interaction required for client ID: ${clientId}`);\n            return null;\n          } else {\n            logger.warning(\n              `IdentityClient: failed refreshing token for client ID: ${clientId}: ${err}`\n            );\n            throw err;\n          }\n        }\n      }\n    );\n  }\n\n  // Here is a custom layer that allows us to abort requests that go through MSAL,\n  // since MSAL doesn't allow us to pass options all the way through.\n\n  generateAbortSignal(correlationId: string): AbortSignalLike {\n    const controller = new AbortController();\n    const controllers = this.abortControllers.get(correlationId) || [];\n    controllers.push(controller);\n    this.abortControllers.set(correlationId, controllers);\n    const existingOnAbort = controller.signal.onabort;\n    controller.signal.onabort = (...params) => {\n      this.abortControllers.set(correlationId, undefined);\n      if (existingOnAbort) {\n        existingOnAbort(...params);\n      }\n    };\n    return controller.signal;\n  }\n\n  abortRequests(correlationId?: string): void {\n    const key = correlationId || noCorrelationId;\n    const controllers = [\n      ...(this.abortControllers.get(key) || []),\n      // MSAL passes no correlation ID to the get requests...\n      ...(this.abortControllers.get(noCorrelationId) || []),\n    ];\n    if (!controllers.length) {\n      return;\n    }\n    for (const controller of controllers) {\n      controller.abort();\n    }\n    this.abortControllers.set(key, undefined);\n  }\n\n  getCorrelationId(options?: NetworkRequestOptions): string {\n    const parameter = options?.body\n      ?.split(\"&\")\n      .map((part) => part.split(\"=\"))\n      .find(([key]) => key === \"client-request-id\");\n    return parameter && parameter.length ? parameter[1] || noCorrelationId : noCorrelationId;\n  }\n\n  // The MSAL network module methods follow\n\n  async sendGetRequestAsync<T>(\n    url: string,\n    options?: NetworkRequestOptions\n  ): Promise<NetworkResponse<T>> {\n    const request = createPipelineRequest({\n      url,\n      method: \"GET\",\n      body: options?.body,\n      headers: createHttpHeaders(options?.headers),\n      abortSignal: this.generateAbortSignal(noCorrelationId),\n    });\n\n    const response = await this.sendRequest(request);\n\n    this.logIdentifiers(response);\n\n    return {\n      body: response.bodyAsText ? JSON.parse(response.bodyAsText) : undefined,\n      headers: response.headers.toJSON(),\n      status: response.status,\n    };\n  }\n\n  async sendPostRequestAsync<T>(\n    url: string,\n    options?: NetworkRequestOptions\n  ): Promise<NetworkResponse<T>> {\n    const request = createPipelineRequest({\n      url,\n      method: \"POST\",\n      body: options?.body,\n      headers: createHttpHeaders(options?.headers),\n      // MSAL doesn't send the correlation ID on the get requests.\n      abortSignal: this.generateAbortSignal(this.getCorrelationId(options)),\n    });\n\n    const response = await this.sendRequest(request);\n\n    this.logIdentifiers(response);\n\n    return {\n      body: response.bodyAsText ? JSON.parse(response.bodyAsText) : undefined,\n      headers: response.headers.toJSON(),\n      status: response.status,\n    };\n  }\n\n  /**\n   *\n   * @internal\n   */\n  getTokenCredentialOptions(): TokenCredentialOptions {\n    return this.tokenCredentialOptions;\n  }\n  /**\n   * If allowLoggingAccountIdentifiers was set on the constructor options\n   * we try to log the account identifiers by parsing the received access token.\n   *\n   * The account identifiers we try to log are:\n   * - `appid`: The application or Client Identifier.\n   * - `upn`: User Principal Name.\n   *   - It might not be available in some authentication scenarios.\n   *   - If it's not available, we put a placeholder: \"No User Principal Name available\".\n   * - `tid`: Tenant Identifier.\n   * - `oid`: Object Identifier of the authenticated user.\n   */\n  private logIdentifiers(response: PipelineResponse): void {\n    if (!this.allowLoggingAccountIdentifiers || !response.bodyAsText) {\n      return;\n    }\n    const unavailableUpn = \"No User Principal Name available\";\n    try {\n      const parsed = (response as any).parsedBody || JSON.parse(response.bodyAsText);\n      const accessToken = parsed.access_token;\n      if (!accessToken) {\n        // Without an access token allowLoggingAccountIdentifiers isn't useful.\n        return;\n      }\n      const base64Metadata = accessToken.split(\".\")[1];\n      const { appid, upn, tid, oid } = JSON.parse(\n        Buffer.from(base64Metadata, \"base64\").toString(\"utf8\")\n      );\n\n      logger.info(\n        `[Authenticated account] Client ID: ${appid}. Tenant ID: ${tid}. User Principal Name: ${\n          upn || unavailableUpn\n        }. Object ID (user): ${oid}`\n      );\n    } catch (e: any) {\n      logger.warning(\n        \"allowLoggingAccountIdentifiers was set, but we couldn't log the account information. Error:\",\n        e.message\n      );\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Helps specify a regional authority, or \"AutoDiscoverRegion\" to auto-detect the region.\n */\nexport enum RegionalAuthority {\n  /** Instructs MSAL to attempt to discover the region */\n  AutoDiscoverRegion = \"AutoDiscoverRegion\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'westus' region. */\n  USWest = \"westus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'westus2' region. */\n  USWest2 = \"westus2\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'centralus' region. */\n  USCentral = \"centralus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'eastus' region. */\n  USEast = \"eastus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'eastus2' region. */\n  USEast2 = \"eastus2\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'northcentralus' region. */\n  USNorthCentral = \"northcentralus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'southcentralus' region. */\n  USSouthCentral = \"southcentralus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'westcentralus' region. */\n  USWestCentral = \"westcentralus\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'canadacentral' region. */\n  CanadaCentral = \"canadacentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'canadaeast' region. */\n  CanadaEast = \"canadaeast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'brazilsouth' region. */\n  BrazilSouth = \"brazilsouth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'northeurope' region. */\n  EuropeNorth = \"northeurope\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'westeurope' region. */\n  EuropeWest = \"westeurope\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'uksouth' region. */\n  UKSouth = \"uksouth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'ukwest' region. */\n  UKWest = \"ukwest\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'francecentral' region. */\n  FranceCentral = \"francecentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'francesouth' region. */\n  FranceSouth = \"francesouth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'switzerlandnorth' region. */\n  SwitzerlandNorth = \"switzerlandnorth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'switzerlandwest' region. */\n  SwitzerlandWest = \"switzerlandwest\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'germanynorth' region. */\n  GermanyNorth = \"germanynorth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'germanywestcentral' region. */\n  GermanyWestCentral = \"germanywestcentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'norwaywest' region. */\n  NorwayWest = \"norwaywest\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'norwayeast' region. */\n  NorwayEast = \"norwayeast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'eastasia' region. */\n  AsiaEast = \"eastasia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'southeastasia' region. */\n  AsiaSouthEast = \"southeastasia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'japaneast' region. */\n  JapanEast = \"japaneast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'japanwest' region. */\n  JapanWest = \"japanwest\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'australiaeast' region. */\n  AustraliaEast = \"australiaeast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'australiasoutheast' region. */\n  AustraliaSouthEast = \"australiasoutheast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'australiacentral' region. */\n  AustraliaCentral = \"australiacentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'australiacentral2' region. */\n  AustraliaCentral2 = \"australiacentral2\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'centralindia' region. */\n  IndiaCentral = \"centralindia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'southindia' region. */\n  IndiaSouth = \"southindia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'westindia' region. */\n  IndiaWest = \"westindia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'koreasouth' region. */\n  KoreaSouth = \"koreasouth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'koreacentral' region. */\n  KoreaCentral = \"koreacentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'uaecentral' region. */\n  UAECentral = \"uaecentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'uaenorth' region. */\n  UAENorth = \"uaenorth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'southafricanorth' region. */\n  SouthAfricaNorth = \"southafricanorth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'southafricawest' region. */\n  SouthAfricaWest = \"southafricawest\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'chinanorth' region. */\n  ChinaNorth = \"chinanorth\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'chinaeast' region. */\n  ChinaEast = \"chinaeast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'chinanorth2' region. */\n  ChinaNorth2 = \"chinanorth2\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'chinaeast2' region. */\n  ChinaEast2 = \"chinaeast2\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'germanycentral' region. */\n  GermanyCentral = \"germanycentral\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'germanynortheast' region. */\n  GermanyNorthEast = \"germanynortheast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usgovvirginia' region. */\n  GovernmentUSVirginia = \"usgovvirginia\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usgoviowa' region. */\n  GovernmentUSIowa = \"usgoviowa\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usgovarizona' region. */\n  GovernmentUSArizona = \"usgovarizona\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usgovtexas' region. */\n  GovernmentUSTexas = \"usgovtexas\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usdodeast' region. */\n  GovernmentUSDodEast = \"usdodeast\",\n  /** Uses the {@link RegionalAuthority} for the Azure 'usdodcentral' region. */\n  GovernmentUSDodCentral = \"usdodcentral\",\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msalNode from \"@azure/msal-node\";\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { getLogLevel } from \"@azure/logger\";\nimport {\n  MsalBaseUtilities,\n  defaultLoggerCallback,\n  getAuthority,\n  getKnownAuthorities,\n  msalToPublic,\n  publicToMsal,\n  getMSALLogLevel,\n} from \"../utils\";\nimport { Msal<PERSON>low, MsalFlowOptions } from \"../flows\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n  resolveTenantId,\n} from \"../../util/tenantIdUtils\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { AppType, AuthenticationRecord } from \"../types\";\nimport { AuthenticationRequiredError } from \"../../errors\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { CACHE_CAE_SUFFIX, CACHE_NON_CAE_SUFFIX, DeveloperSignOnClientId } from \"../../constants\";\nimport { IdentityClient } from \"../../client/identityClient\";\nimport { LogPolicyOptions } from \"@azure/core-rest-pipeline\";\nimport { MultiTenantTokenCredentialOptions } from \"../../credentials/multiTenantTokenCredentialOptions\";\nimport { RegionalAuthority } from \"../../regionalAuthority\";\nimport { TokenCachePersistenceOptions } from \"./tokenCachePersistenceOptions\";\n\n/**\n * Union of the constructor parameters that all MSAL flow types for Node.\n * @internal\n */\nexport interface MsalNodeOptions extends MsalFlowOptions {\n  tokenCachePersistenceOptions?: TokenCachePersistenceOptions;\n  tokenCredentialOptions: MultiTenantTokenCredentialOptions;\n  /**\n   * Specifies a regional authority. Please refer to the {@link RegionalAuthority} type for the accepted values.\n   * If {@link RegionalAuthority.AutoDiscoverRegion} is specified, we will try to discover the regional authority endpoint.\n   * If the property is not specified, uses a non-regional authority endpoint.\n   */\n  regionalAuthority?: string;\n  /**\n   * Allows users to configure settings for logging policy options, allow logging account information and personally identifiable information for customer support.\n   */\n  loggingOptions?: LogPolicyOptions & {\n    /**\n     * Allows logging account information once the authentication flow succeeds.\n     */\n    allowLoggingAccountIdentifiers?: boolean;\n    /**\n     * Allows logging personally identifiable information for customer support.\n     */\n    enableUnsafeSupportLogging?: boolean;\n  };\n}\n\n/**\n * The current persistence provider, undefined by default.\n * @internal\n */\nlet persistenceProvider:\n  | ((options?: TokenCachePersistenceOptions) => Promise<msalNode.ICachePlugin>)\n  | undefined = undefined;\n\n/**\n * An object that allows setting the persistence provider.\n * @internal\n */\nexport const msalNodeFlowCacheControl = {\n  setPersistence(pluginProvider: Exclude<typeof persistenceProvider, undefined>): void {\n    persistenceProvider = pluginProvider;\n  },\n};\n\n/**\n * MSAL partial base client for Node.js.\n *\n * It completes the input configuration with some default values.\n * It also provides with utility protected methods that can be used from any of the clients,\n * which includes handlers for successful responses and errors.\n *\n * @internal\n */\nexport abstract class MsalNode extends MsalBaseUtilities implements MsalFlow {\n  // protected publicApp: msalNode.PublicClientApplication | undefined;\n  // protected publicAppCae: msalNode.PublicClientApplication | undefined;\n  // protected confidentialApp: msalNode.ConfidentialClientApplication | undefined;\n  // protected confidentialAppCae: msalNode.ConfidentialClientApplication | undefined;\n  private app: {\n    public?: msalNode.PublicClientApplication;\n    confidential?: msalNode.ConfidentialClientApplication;\n  } = {};\n  private caeApp: {\n    public?: msalNode.PublicClientApplication;\n    confidential?: msalNode.ConfidentialClientApplication;\n  } = {};\n  protected msalConfig: msalNode.Configuration;\n  protected clientId: string;\n  protected tenantId: string;\n  protected additionallyAllowedTenantIds: string[];\n  protected authorityHost?: string;\n  protected identityClient?: IdentityClient;\n  protected requiresConfidential: boolean = false;\n  protected azureRegion?: string;\n  protected createCachePlugin: (() => Promise<msalNode.ICachePlugin>) | undefined;\n  protected createCachePluginCae: (() => Promise<msalNode.ICachePlugin>) | undefined;\n\n  /**\n   * MSAL currently caches the tokens depending on the claims used to retrieve them.\n   * In cases like CAE, in which we use claims to update the tokens, trying to retrieve the token without the claims will yield the original token.\n   * To ensure we always get the latest token, we have to keep track of the claims.\n   */\n  private cachedClaims: string | undefined;\n\n  protected getAssertion: (() => Promise<string>) | undefined;\n  constructor(options: MsalNodeOptions) {\n    super(options);\n    this.msalConfig = this.defaultNodeMsalConfig(options);\n    this.tenantId = resolveTenantId(options.logger, options.tenantId, options.clientId);\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.tokenCredentialOptions?.additionallyAllowedTenants\n    );\n    this.clientId = this.msalConfig.auth.clientId;\n    if (options?.getAssertion) {\n      this.getAssertion = options.getAssertion;\n    }\n\n    // If persistence has been configured\n    if (persistenceProvider !== undefined && options.tokenCachePersistenceOptions?.enabled) {\n      const nonCaeOptions = {\n        name: `${options.tokenCachePersistenceOptions.name}.${CACHE_NON_CAE_SUFFIX}`,\n        ...options.tokenCachePersistenceOptions,\n      };\n      const caeOptions = {\n        name: `${options.tokenCachePersistenceOptions.name}.${CACHE_CAE_SUFFIX}`,\n        ...options.tokenCachePersistenceOptions,\n      };\n      this.createCachePlugin = () => persistenceProvider!(nonCaeOptions);\n      this.createCachePluginCae = () => persistenceProvider!(caeOptions);\n    } else if (options.tokenCachePersistenceOptions?.enabled) {\n      throw new Error(\n        [\n          \"Persistent token caching was requested, but no persistence provider was configured.\",\n          \"You must install the identity-cache-persistence plugin package (`npm install --save @azure/identity-cache-persistence`)\",\n          \"and enable it by importing `useIdentityPlugin` from `@azure/identity` and calling\",\n          \"`useIdentityPlugin(cachePersistencePlugin)` before using `tokenCachePersistenceOptions`.\",\n        ].join(\" \")\n      );\n    }\n\n    this.azureRegion = options.regionalAuthority ?? process.env.AZURE_REGIONAL_AUTHORITY_NAME;\n    if (this.azureRegion === RegionalAuthority.AutoDiscoverRegion) {\n      this.azureRegion = \"AUTO_DISCOVER\";\n    }\n  }\n\n  /**\n   * Generates a MSAL configuration that generally works for Node.js\n   */\n  protected defaultNodeMsalConfig(options: MsalNodeOptions): msalNode.Configuration {\n    const clientId = options.clientId || DeveloperSignOnClientId;\n    const tenantId = resolveTenantId(options.logger, options.tenantId, options.clientId);\n\n    this.authorityHost = options.authorityHost || process.env.AZURE_AUTHORITY_HOST;\n    const authority = getAuthority(tenantId, this.authorityHost);\n\n    this.identityClient = new IdentityClient({\n      ...options.tokenCredentialOptions,\n      authorityHost: authority,\n      loggingOptions: options.loggingOptions,\n    });\n\n    const clientCapabilities: string[] = [];\n\n    return {\n      auth: {\n        clientId,\n        authority,\n        knownAuthorities: getKnownAuthorities(\n          tenantId,\n          authority,\n          options.disableInstanceDiscovery\n        ),\n        clientCapabilities,\n      },\n      // Cache is defined in this.prepare();\n      system: {\n        networkClient: this.identityClient,\n        loggerOptions: {\n          loggerCallback: defaultLoggerCallback(options.logger),\n          logLevel: getMSALLogLevel(getLogLevel()),\n          piiLoggingEnabled: options.loggingOptions?.enableUnsafeSupportLogging,\n        },\n      },\n    };\n  }\n  protected getApp(\n    appType: \"publicFirst\" | \"confidentialFirst\",\n    enableCae?: boolean\n  ): msalNode.ConfidentialClientApplication | msalNode.PublicClientApplication;\n  protected getApp(appType: \"public\", enableCae?: boolean): msalNode.PublicClientApplication;\n\n  protected getApp(\n    appType: \"confidential\",\n    enableCae?: boolean\n  ): msalNode.ConfidentialClientApplication;\n\n  protected getApp(\n    appType: AppType,\n    enableCae?: boolean\n  ): msalNode.ConfidentialClientApplication | msalNode.PublicClientApplication {\n    const app = enableCae ? this.caeApp : this.app;\n    if (appType === \"publicFirst\") {\n      return (app.public || app.confidential)!;\n    } else if (appType === \"confidentialFirst\") {\n      return (app.confidential || app.public)!;\n    } else if (appType === \"confidential\") {\n      return app.confidential!;\n    } else {\n      return app.public!;\n    }\n  }\n\n  /**\n   * Prepares the MSAL applications.\n   */\n  async init(options?: CredentialFlowGetTokenOptions): Promise<void> {\n    if (options?.abortSignal) {\n      options.abortSignal.addEventListener(\"abort\", () => {\n        // This will abort any pending request in the IdentityClient,\n        // based on the received or generated correlationId\n        this.identityClient!.abortRequests(options.correlationId);\n      });\n    }\n\n    const app = options?.enableCae ? this.caeApp : this.app;\n    if (options?.enableCae) {\n      this.msalConfig.auth.clientCapabilities = [\"cp1\"];\n    }\n    if (app.public || app.confidential) {\n      return;\n    }\n    if (options?.enableCae && this.createCachePluginCae !== undefined) {\n      this.msalConfig.cache = {\n        cachePlugin: await this.createCachePluginCae(),\n      };\n    }\n    if (this.createCachePlugin !== undefined) {\n      this.msalConfig.cache = {\n        cachePlugin: await this.createCachePlugin(),\n      };\n    }\n\n    if (options?.enableCae) {\n      this.caeApp.public = new msalNode.PublicClientApplication(this.msalConfig);\n    } else {\n      this.app.public = new msalNode.PublicClientApplication(this.msalConfig);\n    }\n\n    if (this.getAssertion) {\n      this.msalConfig.auth.clientAssertion = await this.getAssertion();\n    }\n    // The confidential client requires either a secret, assertion or certificate.\n    if (\n      this.msalConfig.auth.clientSecret ||\n      this.msalConfig.auth.clientAssertion ||\n      this.msalConfig.auth.clientCertificate\n    ) {\n      if (options?.enableCae) {\n        this.caeApp.confidential = new msalNode.ConfidentialClientApplication(this.msalConfig);\n      } else {\n        this.app.confidential = new msalNode.ConfidentialClientApplication(this.msalConfig);\n      }\n    } else {\n      if (this.requiresConfidential) {\n        throw new Error(\n          \"Unable to generate the MSAL confidential client. Missing either the client's secret, certificate or assertion.\"\n        );\n      }\n    }\n  }\n\n  /**\n   * Allows the cancellation of a MSAL request.\n   */\n  protected withCancellation(\n    promise: Promise<msalNode.AuthenticationResult | null>,\n    abortSignal?: AbortSignalLike,\n    onCancel?: () => void\n  ): Promise<msalNode.AuthenticationResult | null> {\n    return new Promise((resolve, reject) => {\n      promise\n        .then((msalToken) => {\n          return resolve(msalToken!);\n        })\n        .catch(reject);\n      if (abortSignal) {\n        abortSignal.addEventListener(\"abort\", () => {\n          onCancel?.();\n        });\n      }\n    });\n  }\n\n  /**\n   * Returns the existing account, attempts to load the account from MSAL.\n   */\n  async getActiveAccount(enableCae = false): Promise<AuthenticationRecord | undefined> {\n    if (this.account) {\n      return this.account;\n    }\n    const cache = this.getApp(\"confidentialFirst\", enableCae).getTokenCache();\n    const accountsByTenant = await cache?.getAllAccounts();\n\n    if (!accountsByTenant) {\n      return;\n    }\n\n    if (accountsByTenant.length === 1) {\n      this.account = msalToPublic(this.clientId, accountsByTenant[0]);\n    } else {\n      this.logger\n        .info(`More than one account was found authenticated for this Client ID and Tenant ID.\nHowever, no \"authenticationRecord\" has been provided for this credential,\ntherefore we're unable to pick between these accounts.\nA new login attempt will be requested, to ensure the correct account is picked.\nTo work with multiple accounts for the same Client ID and Tenant ID, please provide an \"authenticationRecord\" when initializing a credential to prevent this from happening.`);\n      return;\n    }\n\n    return this.account;\n  }\n\n  /**\n   * Attempts to retrieve a token from cache.\n   */\n  async getTokenSilent(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    await this.getActiveAccount(options?.enableCae);\n    if (!this.account) {\n      throw new AuthenticationRequiredError({\n        scopes,\n        getTokenOptions: options,\n        message:\n          \"Silent authentication failed. We couldn't retrieve an active account from the cache.\",\n      });\n    }\n\n    const silentRequest: msalNode.SilentFlowRequest = {\n      // To be able to re-use the account, the Token Cache must also have been provided.\n      account: publicToMsal(this.account),\n      correlationId: options?.correlationId,\n      scopes,\n      authority: options?.authority,\n      claims: options?.claims,\n    };\n\n    try {\n      this.logger.info(\"Attempting to acquire token silently\");\n      /**\n       * The following code to retrieve all accounts is done as a workaround in an attempt to force the\n       * refresh of the token cache with the token and the account passed in through the\n       * `authenticationRecord` parameter. See issue - https://github.com/Azure/azure-sdk-for-js/issues/24349#issuecomment-**********\n       * This workaround serves as a workaround for silent authentication not happening when authenticationRecord is passed.\n       */\n      await this.getApp(\"publicFirst\", options?.enableCae)?.getTokenCache().getAllAccounts();\n      const response =\n        (await this.getApp(\"confidential\", options?.enableCae)?.acquireTokenSilent(\n          silentRequest\n        )) ?? (await this.getApp(\"public\", options?.enableCae).acquireTokenSilent(silentRequest));\n      return this.handleResult(scopes, this.clientId, response || undefined);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n\n  /**\n   * Attempts to retrieve an authenticated token from MSAL.\n   */\n  protected abstract doGetToken(scopes: string[], options?: GetTokenOptions): Promise<AccessToken>;\n\n  /**\n   * Wrapper around each MSAL flow get token operation: doGetToken.\n   * If disableAutomaticAuthentication is sent through the constructor, it will prevent MSAL from requesting the user input.\n   */\n  public async getToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    const tenantId =\n      processMultiTenantRequest(this.tenantId, options, this.additionallyAllowedTenantIds) ||\n      this.tenantId;\n\n    options.authority = getAuthority(tenantId, this.authorityHost);\n\n    options.correlationId = options?.correlationId || this.generateUuid();\n    await this.init(options);\n\n    try {\n      // MSAL now caches tokens based on their claims,\n      // so now one has to keep track fo claims in order to retrieve the newer tokens from acquireTokenSilent\n      // This update happened on PR: https://github.com/AzureAD/microsoft-authentication-library-for-js/pull/4533\n      const optionsClaims = (options as any).claims;\n      if (optionsClaims) {\n        this.cachedClaims = optionsClaims;\n      }\n      if (this.cachedClaims && !optionsClaims) {\n        (options as any).claims = this.cachedClaims;\n      }\n      // We don't return the promise since we want to catch errors right here.\n      return await this.getTokenSilent(scopes, options);\n    } catch (err: any) {\n      if (err.name !== \"AuthenticationRequiredError\") {\n        throw err;\n      }\n      if (options?.disableAutomaticAuthentication) {\n        throw new AuthenticationRequiredError({\n          scopes,\n          getTokenOptions: options,\n          message:\n            \"Automatic authentication has been disabled. You may call the authentication() method.\",\n        });\n      }\n      this.logger.info(`Silent authentication failed, falling back to interactive method.`);\n      return this.doGetToken(scopes, options);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError, formatSuccess } from \"../util/logging\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { AzureAuthorityHosts } from \"../constants\";\nimport { CredentialUnavailableError } from \"../errors\";\nimport { IdentityClient } from \"../client/identityClient\";\nimport { VisualStudioCodeCredentialOptions } from \"./visualStudioCodeCredentialOptions\";\nimport { VSCodeCredentialFinder } from \"./visualStudioCodeCredentialPlugin\";\nimport { checkTenantId } from \"../util/tenantIdUtils\";\nimport fs from \"fs\";\nimport os from \"os\";\nimport path from \"path\";\n\nconst CommonTenantId = \"common\";\nconst AzureAccountClientId = \"aebc6443-996d-45c2-90f0-388ff96faa56\"; // VSC: 'aebc6443-996d-45c2-90f0-388ff96faa56'\nconst logger = credentialLogger(\"VisualStudioCodeCredential\");\n\nlet findCredentials: VSCodeCredentialFinder | undefined = undefined;\n\nexport const vsCodeCredentialControl = {\n  setVsCodeCredentialFinder(finder: VSCodeCredentialFinder): void {\n    findCredentials = finder;\n  },\n};\n\n// Map of unsupported Tenant IDs and the errors we will be throwing.\nconst unsupportedTenantIds: Record<string, string> = {\n  adfs: \"The VisualStudioCodeCredential does not support authentication with ADFS tenants.\",\n};\n\nfunction checkUnsupportedTenant(tenantId: string): void {\n  // If the Tenant ID isn't supported, we throw.\n  const unsupportedTenantError = unsupportedTenantIds[tenantId];\n  if (unsupportedTenantError) {\n    throw new CredentialUnavailableError(unsupportedTenantError);\n  }\n}\n\ntype VSCodeCloudNames = \"AzureCloud\" | \"AzureChina\" | \"AzureGermanCloud\" | \"AzureUSGovernment\";\n\nconst mapVSCodeAuthorityHosts: Record<VSCodeCloudNames, string> = {\n  AzureCloud: AzureAuthorityHosts.AzurePublicCloud,\n  AzureChina: AzureAuthorityHosts.AzureChina,\n  AzureGermanCloud: AzureAuthorityHosts.AzureGermany,\n  AzureUSGovernment: AzureAuthorityHosts.AzureGovernment,\n};\n\n/**\n * Attempts to load a specific property from the VSCode configurations of the current OS.\n * If it fails at any point, returns undefined.\n */\nexport function getPropertyFromVSCode(property: string): string | undefined {\n  const settingsPath = [\"User\", \"settings.json\"];\n  // Eventually we can add more folders for more versions of VSCode.\n  const vsCodeFolder = \"Code\";\n  const homedir = os.homedir();\n\n  function loadProperty(...pathSegments: string[]): string | undefined {\n    const fullPath = path.join(...pathSegments, vsCodeFolder, ...settingsPath);\n    const settings = JSON.parse(fs.readFileSync(fullPath, { encoding: \"utf8\" }));\n    return settings[property];\n  }\n\n  try {\n    let appData: string;\n    switch (process.platform) {\n      case \"win32\":\n        appData = process.env.APPDATA!;\n        return appData ? loadProperty(appData) : undefined;\n      case \"darwin\":\n        return loadProperty(homedir, \"Library\", \"Application Support\");\n      case \"linux\":\n        return loadProperty(homedir, \".config\");\n      default:\n        return;\n    }\n  } catch (e: any) {\n    logger.info(`Failed to load the Visual Studio Code configuration file. Error: ${e.message}`);\n    return;\n  }\n}\n\n/**\n * Connects to Azure using the credential provided by the VSCode extension 'Azure Account'.\n * Once the user has logged in via the extension, this credential can share the same refresh token\n * that is cached by the extension.\n *\n * It's a [known issue](https://github.com/Azure/azure-sdk-for-js/issues/20500) that this credential doesn't\n * work with [Azure Account extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode.azure-account)\n * versions newer than **0.9.11**. A long-term fix to this problem is in progress. In the meantime, consider\n * authenticating with {@link AzureCliCredential}.\n */\nexport class VisualStudioCodeCredential implements TokenCredential {\n  private identityClient: IdentityClient;\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private cloudName: VSCodeCloudNames;\n\n  /**\n   * Creates an instance of VisualStudioCodeCredential to use for automatically authenticating via VSCode.\n   *\n   * **Note**: `VisualStudioCodeCredential` is provided by a plugin package:\n   * `@azure/identity-vscode`. If this package is not installed and registered\n   * using the plugin API (`useIdentityPlugin`), then authentication using\n   * `VisualStudioCodeCredential` will not be available.\n   *\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(options?: VisualStudioCodeCredentialOptions) {\n    // We want to make sure we use the one assigned by the user on the VSCode settings.\n    // Or just `AzureCloud` by default.\n    this.cloudName = (getPropertyFromVSCode(\"azure.cloud\") || \"AzureCloud\") as VSCodeCloudNames;\n\n    // Picking an authority host based on the cloud name.\n    const authorityHost = mapVSCodeAuthorityHosts[this.cloudName];\n\n    this.identityClient = new IdentityClient({\n      authorityHost,\n      ...options,\n    });\n\n    if (options && options.tenantId) {\n      checkTenantId(logger, options.tenantId);\n      this.tenantId = options.tenantId;\n    } else {\n      this.tenantId = CommonTenantId;\n    }\n\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n\n    checkUnsupportedTenant(this.tenantId);\n  }\n\n  /**\n   * Runs preparations for any further getToken request.\n   */\n  private async prepare(): Promise<void> {\n    // Attempts to load the tenant from the VSCode configuration file.\n    const settingsTenant = getPropertyFromVSCode(\"azure.tenant\");\n    if (settingsTenant) {\n      this.tenantId = settingsTenant;\n    }\n    checkUnsupportedTenant(this.tenantId);\n  }\n\n  /**\n   * The promise of the single preparation that will be executed at the first getToken request for an instance of this class.\n   */\n  private preparePromise: Promise<void> | undefined;\n\n  /**\n   * Runs preparations for any further getToken, but only once.\n   */\n  private prepareOnce(): Promise<void> | undefined {\n    if (!this.preparePromise) {\n      this.preparePromise = this.prepare();\n    }\n    return this.preparePromise;\n  }\n\n  /**\n   * Returns the token found by searching VSCode's authentication cache or\n   * returns null if no token could be found.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                `TokenCredential` implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options?: GetTokenOptions\n  ): Promise<AccessToken> {\n    await this.prepareOnce();\n\n    const tenantId =\n      processMultiTenantRequest(\n        this.tenantId,\n        options,\n        this.additionallyAllowedTenantIds,\n        logger\n      ) || this.tenantId;\n\n    if (findCredentials === undefined) {\n      throw new CredentialUnavailableError(\n        [\n          \"No implementation of `VisualStudioCodeCredential` is available.\",\n          \"You must install the identity-vscode plugin package (`npm install --save-dev @azure/identity-vscode`)\",\n          \"and enable it by importing `useIdentityPlugin` from `@azure/identity` and calling\",\n          \"`useIdentityPlugin(vsCodePlugin)` before creating a `VisualStudioCodeCredential`.\",\n          \"To troubleshoot, visit https://aka.ms/azsdk/js/identity/vscodecredential/troubleshoot.\",\n        ].join(\" \")\n      );\n    }\n\n    let scopeString = typeof scopes === \"string\" ? scopes : scopes.join(\" \");\n\n    // Check to make sure the scope we get back is a valid scope\n    if (!scopeString.match(/^[0-9a-zA-Z-.:/]+$/)) {\n      const error = new Error(\"Invalid scope was specified by the user or calling client\");\n      logger.getToken.info(formatError(scopes, error));\n      throw error;\n    }\n\n    if (scopeString.indexOf(\"offline_access\") < 0) {\n      scopeString += \" offline_access\";\n    }\n\n    // findCredentials returns an array similar to:\n    // [\n    //   {\n    //     account: \"\",\n    //     password: \"\",\n    //   },\n    //   /* ... */\n    // ]\n    const credentials = await findCredentials();\n\n    // If we can't find the credential based on the name, we'll pick the first one available.\n    const { password: refreshToken } =\n      credentials.find(({ account }) => account === this.cloudName) ?? credentials[0] ?? {};\n\n    if (refreshToken) {\n      const tokenResponse = await this.identityClient.refreshAccessToken(\n        tenantId,\n        AzureAccountClientId,\n        scopeString,\n        refreshToken,\n        undefined\n      );\n\n      if (tokenResponse) {\n        logger.getToken.info(formatSuccess(scopes));\n        return tokenResponse.accessToken;\n      } else {\n        const error = new CredentialUnavailableError(\n          \"Could not retrieve the token associated with Visual Studio Code. Have you connected using the 'Azure Account' extension recently? To troubleshoot, visit https://aka.ms/azsdk/js/identity/vscodecredential/troubleshoot.\"\n        );\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n    } else {\n      const error = new CredentialUnavailableError(\n        \"Could not retrieve the token associated with Visual Studio Code. Did you connect using the 'Azure Account' extension? To troubleshoot, visit https://aka.ms/azsdk/js/identity/vscodecredential/troubleshoot.\"\n      );\n      logger.getToken.info(formatError(scopes, error));\n      throw error;\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AzurePluginContext, IdentityPlugin } from \"./provider\";\nimport { msalNodeFlowCacheControl } from \"../msal/nodeFlows/msalNodeCommon\";\nimport { vsCodeCredentialControl } from \"../credentials/visualStudioCodeCredential\";\n\n/**\n * The context passed to an Identity plugin. This contains objects that\n * plugins can use to set backend implementations.\n * @internal\n */\nconst pluginContext: AzurePluginContext = {\n  cachePluginControl: msalNodeFlowCacheControl,\n  vsCodeCredentialControl: vsCodeCredentialControl,\n};\n\n/**\n * Extend Azure Identity with additional functionality. Pass a plugin from\n * a plugin package, such as:\n *\n * - `@azure/identity-cache-persistence`: provides persistent token caching\n * - `@azure/identity-vscode`: provides the dependencies of\n *   `VisualStudioCodeCredential` and enables it\n *\n * Example:\n *\n * ```javascript\n * import { cachePersistencePlugin } from \"@azure/identity-cache-persistence\";\n *\n * import { useIdentityPlugin, DefaultAzureCredential } from \"@azure/identity\";\n * useIdentityPlugin(cachePersistencePlugin);\n *\n * // The plugin has the capability to extend `DefaultAzureCredential` and to\n * // add middleware to the underlying credentials, such as persistence.\n * const credential = new DefaultAzureCredential({\n *   tokenCachePersistenceOptions: {\n *     enabled: true\n *   }\n * });\n * ```\n *\n * @param plugin - the plugin to register\n */\nexport function useIdentityPlugin(plugin: IdentityPlugin): void {\n  plugin(pluginContext);\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  PipelineRequestOptions,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { GetTokenOptions } from \"@azure/core-auth\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { MSI, MSIConfiguration, MSIToken } from \"./models\";\nimport { mapScopesToResource } from \"./utils\";\n\nconst msiName = \"ManagedIdentityCredential - AppServiceMSI 2017\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Generates the options used on the request for an access token.\n */\nfunction prepareRequestOptions(\n  scopes: string | string[],\n  clientId?: string\n): PipelineRequestOptions {\n  const resource = mapScopesToResource(scopes);\n  if (!resource) {\n    throw new Error(`${msiName}: Multiple scopes are not supported.`);\n  }\n\n  const queryParameters: Record<string, string> = {\n    resource,\n    \"api-version\": \"2017-09-01\",\n  };\n\n  if (clientId) {\n    queryParameters.clientid = clientId;\n  }\n\n  const query = new URLSearchParams(queryParameters);\n\n  // This error should not bubble up, since we verify that this environment variable is defined in the isAvailable() method defined below.\n  if (!process.env.MSI_ENDPOINT) {\n    throw new Error(`${msiName}: Missing environment variable: MSI_ENDPOINT`);\n  }\n  if (!process.env.MSI_SECRET) {\n    throw new Error(`${msiName}: Missing environment variable: MSI_SECRET`);\n  }\n\n  return {\n    url: `${process.env.MSI_ENDPOINT}?${query.toString()}`,\n    method: \"GET\",\n    headers: createHttpHeaders({\n      Accept: \"application/json\",\n      secret: process.env.MSI_SECRET,\n    }),\n  };\n}\n\n/**\n * Defines how to determine whether the Azure App Service MSI is available, and also how to retrieve a token from the Azure App Service MSI.\n */\nexport const appServiceMsi2017: MSI = {\n  name: \"appServiceMsi2017\",\n  async isAvailable({ scopes }): Promise<boolean> {\n    const resource = mapScopesToResource(scopes);\n    if (!resource) {\n      logger.info(`${msiName}: Unavailable. Multiple scopes are not supported.`);\n      return false;\n    }\n    const env = process.env;\n    const result = Boolean(env.MSI_ENDPOINT && env.MSI_SECRET);\n    if (!result) {\n      logger.info(\n        `${msiName}: Unavailable. The environment variables needed are: MSI_ENDPOINT and MSI_SECRET.`\n      );\n    }\n    return result;\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {}\n  ): Promise<MSIToken | null> {\n    const { identityClient, scopes, clientId, resourceId } = configuration;\n\n    if (resourceId) {\n      logger.warning(\n        `${msiName}: managed Identity by resource Id is not supported. Argument resourceId might be ignored by the service.`\n      );\n    }\n\n    logger.info(\n      `${msiName}: Using the endpoint and the secret coming form the environment variables: MSI_ENDPOINT=${process.env.MSI_ENDPOINT} and MSI_SECRET=[REDACTED].`\n    );\n\n    const request = createPipelineRequest({\n      abortSignal: getTokenOptions.abortSignal,\n      ...prepareRequestOptions(scopes, clientId),\n      // Generally, MSI endpoints use the HTTP protocol, without transport layer security (TLS).\n      allowInsecureConnection: true,\n    });\n    const tokenResponse = await identityClient.sendTokenRequest(request);\n    return (tokenResponse && tokenResponse.accessToken) || null;\n  },\n};\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  PipelineRequestOptions,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { GetTokenOptions } from \"@azure/core-auth\";\nimport { MSI, MSIConfiguration, MSIToken } from \"./models\";\nimport { mapScopesToResource } from \"./utils\";\n\nconst msiName = \"ManagedIdentityCredential - CloudShellMSI\";\nexport const logger = credentialLogger(msiName);\n\n/**\n * Generates the options used on the request for an access token.\n */\nfunction prepareRequestOptions(\n  scopes: string | string[],\n  clientId?: string,\n  resourceId?: string\n): PipelineRequestOptions {\n  const resource = mapScopesToResource(scopes);\n  if (!resource) {\n    throw new Error(`${msiName}: Multiple scopes are not supported.`);\n  }\n\n  const body: Record<string, string> = {\n    resource,\n  };\n\n  if (clientId) {\n    body.client_id = clientId;\n  }\n  if (resourceId) {\n    body.msi_res_id = resourceId;\n  }\n\n  // This error should not bubble up, since we verify that this environment variable is defined in the isAvailable() method defined below.\n  if (!process.env.MSI_ENDPOINT) {\n    throw new Error(`${msiName}: Missing environment variable: MSI_ENDPOINT`);\n  }\n  const params = new URLSearchParams(body);\n  return {\n    url: process.env.MSI_ENDPOINT,\n    method: \"POST\",\n    body: params.toString(),\n    headers: createHttpHeaders({\n      Accept: \"application/json\",\n      Metadata: \"true\",\n      \"Content-Type\": \"application/x-www-form-urlencoded\",\n    }),\n  };\n}\n\n/**\n * Defines how to determine whether the Azure Cloud Shell MSI is available, and also how to retrieve a token from the Azure Cloud Shell MSI.\n * Since Azure Managed Identities aren't available in the Azure Cloud Shell, we log a warning for users that try to access cloud shell using user assigned identity.\n */\nexport const cloudShellMsi: MSI = {\n  name: \"cloudShellMsi\",\n  async isAvailable({ scopes }): Promise<boolean> {\n    const resource = mapScopesToResource(scopes);\n    if (!resource) {\n      logger.info(`${msiName}: Unavailable. Multiple scopes are not supported.`);\n      return false;\n    }\n\n    const result = Boolean(process.env.MSI_ENDPOINT);\n    if (!result) {\n      logger.info(`${msiName}: Unavailable. The environment variable MSI_ENDPOINT is needed.`);\n    }\n    return result;\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {}\n  ): Promise<MSIToken | null> {\n    const { identityClient, scopes, clientId, resourceId } = configuration;\n\n    if (clientId) {\n      logger.warning(\n        `${msiName}: user-assigned identities not supported. The argument clientId might be ignored by the service.`\n      );\n    }\n\n    if (resourceId) {\n      logger.warning(\n        `${msiName}: user defined managed Identity by resource Id not supported. The argument resourceId might be ignored by the service.`\n      );\n    }\n\n    logger.info(\n      `${msiName}: Using the endpoint coming form the environment variable MSI_ENDPOINT = ${process.env.MSI_ENDPOINT}.`\n    );\n\n    const request = createPipelineRequest({\n      abortSignal: getTokenOptions.abortSignal,\n      ...prepareRequestOptions(scopes, clientId, resourceId),\n      // Generally, MSI endpoints use the HTTP protocol, without transport layer security (TLS).\n      allowInsecureConnection: true,\n    });\n    const tokenResponse = await identityClient.sendTokenRequest(request);\n    return (tokenResponse && tokenResponse.accessToken) || null;\n  },\n};\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { delay, isError } from \"@azure/core-util\";\nimport { GetTokenOptions } from \"@azure/core-auth\";\nimport {\n  PipelineRequestOptions,\n  PipelineResponse,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { AuthenticationError } from \"../../errors\";\nimport { tracingClient } from \"../../util/tracing\";\nimport { imdsApiVersion, imdsEndpointPath, imdsHost } from \"./constants\";\nimport { MSI, MSIConfiguration, MSIToken } from \"./models\";\nimport { mapScopesToResource } from \"./utils\";\n\nconst msiName = \"ManagedIdentityCredential - IMDS\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Generates the options used on the request for an access token.\n */\nfunction prepareRequestOptions(\n  scopes: string | string[],\n  clientId?: string,\n  resourceId?: string,\n  options?: {\n    skipQuery?: boolean;\n    skipMetadataHeader?: boolean;\n  }\n): PipelineRequestOptions {\n  const resource = mapScopesToResource(scopes);\n  if (!resource) {\n    throw new Error(`${msiName}: Multiple scopes are not supported.`);\n  }\n\n  const { skipQuery, skipMetadataHeader } = options || {};\n  let query = \"\";\n\n  // Pod Identity will try to process this request even if the Metadata header is missing.\n  // We can exclude the request query to ensure no IMDS endpoint tries to process the ping request.\n  if (!skipQuery) {\n    const queryParameters: Record<string, string> = {\n      resource,\n      \"api-version\": imdsApiVersion,\n    };\n    if (clientId) {\n      queryParameters.client_id = clientId;\n    }\n    if (resourceId) {\n      queryParameters.msi_res_id = resourceId;\n    }\n    const params = new URLSearchParams(queryParameters);\n    query = `?${params.toString()}`;\n  }\n\n  const url = new URL(imdsEndpointPath, process.env.AZURE_POD_IDENTITY_AUTHORITY_HOST ?? imdsHost);\n\n  const rawHeaders: Record<string, string> = {\n    Accept: \"application/json\",\n    Metadata: \"true\",\n  };\n\n  // Remove the Metadata header to invoke a request error from some IMDS endpoints.\n  if (skipMetadataHeader) {\n    delete rawHeaders.Metadata;\n  }\n\n  return {\n    // In this case, the `?` should be added in the \"query\" variable `skipQuery` is not set.\n    url: `${url}${query}`,\n    method: \"GET\",\n    headers: createHttpHeaders(rawHeaders),\n  };\n}\n\n// 800ms -> 1600ms -> 3200ms\nexport const imdsMsiRetryConfig = {\n  maxRetries: 3,\n  startDelayInMs: 800,\n  intervalIncrement: 2,\n};\n\n/**\n * Defines how to determine whether the Azure IMDS MSI is available, and also how to retrieve a token from the Azure IMDS MSI.\n */\nexport const imdsMsi: MSI = {\n  name: \"imdsMsi\",\n  async isAvailable({\n    scopes,\n    identityClient,\n    clientId,\n    resourceId,\n    getTokenOptions = {},\n  }): Promise<boolean> {\n    const resource = mapScopesToResource(scopes);\n    if (!resource) {\n      logger.info(`${msiName}: Unavailable. Multiple scopes are not supported.`);\n      return false;\n    }\n\n    // if the PodIdentityEndpoint environment variable was set no need to probe the endpoint, it can be assumed to exist\n    if (process.env.AZURE_POD_IDENTITY_AUTHORITY_HOST) {\n      return true;\n    }\n\n    if (!identityClient) {\n      throw new Error(\"Missing IdentityClient\");\n    }\n\n    const requestOptions = prepareRequestOptions(resource, clientId, resourceId, {\n      skipMetadataHeader: true,\n      skipQuery: true,\n    });\n\n    return tracingClient.withSpan(\n      \"ManagedIdentityCredential-pingImdsEndpoint\",\n      getTokenOptions,\n      async (options) => {\n        requestOptions.tracingOptions = options.tracingOptions;\n\n        // Create a request with a timeout since we expect that\n        // not having a \"Metadata\" header should cause an error to be\n        // returned quickly from the endpoint, proving its availability.\n        const request = createPipelineRequest(requestOptions);\n\n        // Default to 1000 if the default of 0 is used.\n        // Negative values can still be used to disable the timeout.\n        request.timeout = options.requestOptions?.timeout || 1000;\n\n        // This MSI uses the imdsEndpoint to get the token, which only uses http://\n        request.allowInsecureConnection = true;\n        let response: PipelineResponse;\n        try {\n          logger.info(`${msiName}: Pinging the Azure IMDS endpoint`);\n          response = await identityClient.sendRequest(request);\n        } catch (err: unknown) {\n          // If the request failed, or Node.js was unable to establish a connection,\n          // or the host was down, we'll assume the IMDS endpoint isn't available.\n          if (isError(err)) {\n            logger.verbose(`${msiName}: Caught error ${err.name}: ${err.message}`);\n          }\n          // This is a special case for Docker Desktop which responds with a 403 with a message that contains \"A socket operation was attempted to an unreachable network\"\n          // rather than just timing out, as expected.\n          logger.info(`${msiName}: The Azure IMDS endpoint is unavailable`);\n          return false;\n        }\n        if (response.status === 403) {\n          if (\n            response.bodyAsText?.includes(\n              \"A socket operation was attempted to an unreachable network\"\n            )\n          ) {\n            logger.info(`${msiName}: The Azure IMDS endpoint is unavailable`);\n            logger.info(`${msiName}: ${response.bodyAsText}`);\n            return false;\n          }\n        }\n        // If we received any response, the endpoint is available\n        logger.info(`${msiName}: The Azure IMDS endpoint is available`);\n        return true;\n      }\n    );\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {}\n  ): Promise<MSIToken | null> {\n    const { identityClient, scopes, clientId, resourceId } = configuration;\n\n    if (process.env.AZURE_POD_IDENTITY_AUTHORITY_HOST) {\n      logger.info(\n        `${msiName}: Using the Azure IMDS endpoint coming from the environment variable AZURE_POD_IDENTITY_AUTHORITY_HOST=${process.env.AZURE_POD_IDENTITY_AUTHORITY_HOST}.`\n      );\n    } else {\n      logger.info(`${msiName}: Using the default Azure IMDS endpoint ${imdsHost}.`);\n    }\n\n    let nextDelayInMs = imdsMsiRetryConfig.startDelayInMs;\n    for (let retries = 0; retries < imdsMsiRetryConfig.maxRetries; retries++) {\n      try {\n        const request = createPipelineRequest({\n          abortSignal: getTokenOptions.abortSignal,\n          ...prepareRequestOptions(scopes, clientId, resourceId),\n          allowInsecureConnection: true,\n        });\n        const tokenResponse = await identityClient.sendTokenRequest(request);\n\n        return (tokenResponse && tokenResponse.accessToken) || null;\n      } catch (error: any) {\n        if (error.statusCode === 404) {\n          await delay(nextDelayInMs);\n          nextDelayInMs *= imdsMsiRetryConfig.intervalIncrement;\n          continue;\n        }\n        throw error;\n      }\n    }\n\n    throw new AuthenticationError(\n      404,\n      `${msiName}: Failed to retrieve IMDS token after ${imdsMsiRetryConfig.maxRetries} retries.`\n    );\n  },\n};\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  PipelineRequestOptions,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { GetTokenOptions } from \"@azure/core-auth\";\nimport { readFile } from \"fs\";\nimport { AuthenticationError } from \"../../errors\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { IdentityClient } from \"../../client/identityClient\";\nimport { mapScopesToResource } from \"./utils\";\nimport { MSI, MSIConfiguration, MSIToken } from \"./models\";\nimport { azureArcAPIVersion } from \"./constants\";\n\nconst msiName = \"ManagedIdentityCredential - Azure Arc MSI\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Generates the options used on the request for an access token.\n */\nfunction prepareRequestOptions(\n  scopes: string | string[],\n  clientId?: string,\n  resourceId?: string\n): PipelineRequestOptions {\n  const resource = mapScopesToResource(scopes);\n  if (!resource) {\n    throw new Error(`${msiName}: Multiple scopes are not supported.`);\n  }\n  const queryParameters: Record<string, string> = {\n    resource,\n    \"api-version\": azureArcAPIVersion,\n  };\n\n  if (clientId) {\n    queryParameters.client_id = clientId;\n  }\n  if (resourceId) {\n    queryParameters.msi_res_id = resourceId;\n  }\n\n  // This error should not bubble up, since we verify that this environment variable is defined in the isAvailable() method defined below.\n  if (!process.env.IDENTITY_ENDPOINT) {\n    throw new Error(`${msiName}: Missing environment variable: IDENTITY_ENDPOINT`);\n  }\n\n  const query = new URLSearchParams(queryParameters);\n\n  return createPipelineRequest({\n    // Should be similar to: http://localhost:40342/metadata/identity/oauth2/token\n    url: `${process.env.IDENTITY_ENDPOINT}?${query.toString()}`,\n    method: \"GET\",\n    headers: createHttpHeaders({\n      Accept: \"application/json\",\n      Metadata: \"true\",\n    }),\n  });\n}\n\n/**\n * Retrieves the file contents at the given path using promises.\n * Useful since `fs`'s readFileSync locks the thread, and to avoid extra dependencies.\n */\nfunction readFileAsync(path: string, options: { encoding: BufferEncoding }): Promise<string> {\n  return new Promise((resolve, reject) =>\n    readFile(path, options, (err, data) => {\n      if (err) {\n        reject(err);\n      }\n      resolve(data);\n    })\n  );\n}\n\n/**\n * Does a request to the authentication provider that results in a file path.\n */\nasync function filePathRequest(\n  identityClient: IdentityClient,\n  requestPrepareOptions: PipelineRequestOptions\n): Promise<string | undefined> {\n  const response = await identityClient.sendRequest(createPipelineRequest(requestPrepareOptions));\n\n  if (response.status !== 401) {\n    let message = \"\";\n    if (response.bodyAsText) {\n      message = ` Response: ${response.bodyAsText}`;\n    }\n    throw new AuthenticationError(\n      response.status,\n      `${msiName}: To authenticate with Azure Arc MSI, status code 401 is expected on the first request. ${message}`\n    );\n  }\n\n  const authHeader = response.headers.get(\"www-authenticate\") || \"\";\n  try {\n    return authHeader.split(\"=\").slice(1)[0];\n  } catch (e: any) {\n    throw Error(`Invalid www-authenticate header format: ${authHeader}`);\n  }\n}\n\n/**\n * Defines how to determine whether the Azure Arc MSI is available, and also how to retrieve a token from the Azure Arc MSI.\n */\nexport const arcMsi: MSI = {\n  name: \"arc\",\n  async isAvailable({ scopes }): Promise<boolean> {\n    const resource = mapScopesToResource(scopes);\n    if (!resource) {\n      logger.info(`${msiName}: Unavailable. Multiple scopes are not supported.`);\n      return false;\n    }\n    const result = Boolean(process.env.IMDS_ENDPOINT && process.env.IDENTITY_ENDPOINT);\n    if (!result) {\n      logger.info(\n        `${msiName}: The environment variables needed are: IMDS_ENDPOINT and IDENTITY_ENDPOINT`\n      );\n    }\n    return result;\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {}\n  ): Promise<MSIToken | null> {\n    const { identityClient, scopes, clientId, resourceId } = configuration;\n\n    if (clientId) {\n      logger.warning(\n        `${msiName}: user-assigned identities not supported. The argument clientId might be ignored by the service.`\n      );\n    }\n    if (resourceId) {\n      logger.warning(\n        `${msiName}: user defined managed Identity by resource Id is not supported. Argument resourceId will be ignored.`\n      );\n    }\n\n    logger.info(`${msiName}: Authenticating.`);\n\n    const requestOptions = {\n      disableJsonStringifyOnBody: true,\n      deserializationMapper: undefined,\n      abortSignal: getTokenOptions.abortSignal,\n      ...prepareRequestOptions(scopes, clientId, resourceId),\n      allowInsecureConnection: true,\n    };\n\n    const filePath = await filePathRequest(identityClient, requestOptions);\n\n    if (!filePath) {\n      throw new Error(`${msiName}: Failed to find the token file.`);\n    }\n\n    const key = await readFileAsync(filePath, { encoding: \"utf-8\" });\n    requestOptions.headers?.set(\"Authorization\", `Basic ${key}`);\n\n    const request = createPipelineRequest({\n      ...requestOptions,\n      // Generally, MSI endpoints use the HTTP protocol, without transport layer security (TLS).\n      allowInsecureConnection: true,\n    });\n    const tokenResponse = await identityClient.sendTokenRequest(request);\n    return (tokenResponse && tokenResponse.accessToken) || null;\n  },\n};\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { <PERSON>al<PERSON><PERSON>, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { isError } from \"@azure/core-util\";\n\n/**\n * Options that can be passed to configure MSAL to handle client assertions.\n * @internal\n */\nexport interface MsalClientAssertionOptions extends MsalNodeOptions {\n  /**\n   * A function that retrieves the assertion for the credential to use.\n   */\n  getAssertion: () => Promise<string>;\n}\n\n/**\n * MSAL client assertion client. Calls to MSAL's confidential application's `acquireTokenByClientCredential` during `doGetToken`.\n * @internal\n */\nexport class MsalClientAssertion extends MsalNode {\n  getAssertion: () => Promise<string>;\n  constructor(options: MsalClientAssertionOptions) {\n    super(options);\n    this.requiresConfidential = true;\n    this.getAssertion = options.getAssertion;\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    try {\n      const assertion = await this.getAssertion();\n      const result = await this.getApp(\n        \"confidential\",\n        options.enableCae\n      ).acquireTokenByClientCredential({\n        scopes,\n        correlationId: options.correlationId,\n        azureRegion: this.azureRegion,\n        authority: options.authority,\n        claims: options.claims,\n        clientAssertion: assertion,\n      });\n      // The Client Credential flow does not return an account,\n      // so each time getToken gets called, we will have to acquire a new token through the service.\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (err: unknown) {\n      let err2 = err;\n      if (err === null || err === undefined) {\n        err2 = new Error(JSON.stringify(err));\n      } else {\n        err2 = isError(err) ? err : new Error(String(err));\n      }\n      throw this.handleError(scopes, err2 as Error, options);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { ClientAssertionCredentialOptions } from \"./clientAssertionCredentialOptions\";\nimport { MsalClientAssertion } from \"../msal/nodeFlows/msalClientAssertion\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { credentialLogger } from \"../util/logging\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"ClientAssertionCredential\");\n\n/**\n * Authenticates a service principal with a JWT assertion.\n */\nexport class ClientAssertionCredential implements TokenCredential {\n  private msalFlow: MsalFlow;\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private clientId: string;\n  private options: ClientAssertionCredentialOptions;\n\n  /**\n   * Creates an instance of the ClientAssertionCredential with the details\n   * needed to authenticate against Azure Active Directory with a client\n   * assertion provided by the developer through the `getAssertion` function parameter.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param getAssertion - A function that retrieves the assertion for the credential to use.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    getAssertion: () => Promise<string>,\n    options: ClientAssertionCredentialOptions = {}\n  ) {\n    if (!tenantId || !clientId || !getAssertion) {\n      throw new Error(\n        \"ClientAssertionCredential: tenantId, clientId, and clientAssertion are required parameters.\"\n      );\n    }\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n    this.clientId = clientId;\n    this.options = options;\n    this.msalFlow = new MsalClientAssertion({\n      ...options,\n      logger,\n      clientId: this.clientId,\n      tenantId: this.tenantId,\n      tokenCredentialOptions: this.options,\n      getAssertion,\n    });\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n          logger\n        );\n\n        const arrayScopes = Array.isArray(scopes) ? scopes : [scopes];\n        return this.msalFlow.getToken(arrayScopes, newOptions);\n      }\n    );\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { ClientAssertionCredential } from \"./clientAssertionCredential\";\nimport { WorkloadIdentityCredentialOptions } from \"./workloadIdentityCredentialOptions\";\nimport { readFile } from \"fs/promises\";\nimport { CredentialUnavailableError } from \"../errors\";\nimport { credentialLogger, processEnvVars } from \"../util/logging\";\nimport { checkTenantId } from \"../util/tenantIdUtils\";\n\nconst credentialName = \"WorkloadIdentityCredential\";\n/**\n * Contains the list of all supported environment variable names so that an\n * appropriate error message can be generated when no credentials can be\n * configured.\n *\n * @internal\n */\nexport const SupportedWorkloadEnvironmentVariables = [\n  \"AZURE_TENANT_ID\",\n  \"AZURE_CLIENT_ID\",\n  \"AZURE_FEDERATED_TOKEN_FILE\",\n];\nconst logger = credentialLogger(credentialName);\n/**\n * Workload Identity authentication is a feature in Azure that allows applications running on virtual machines (VMs)\n * to access other Azure resources without the need for a service principal or managed identity. With Workload Identity\n * authentication, applications authenticate themselves using their own identity, rather than using a shared service\n * principal or managed identity. Under the hood, Workload Identity authentication uses the concept of Service Account\n * Credentials (SACs), which are automatically created by Azure and stored securely in the VM. By using Workload\n * Identity authentication, you can avoid the need to manage and rotate service principals or managed identities for\n * each application on each VM. Additionally, because SACs are created automatically and managed by Azure, you don't\n * need to worry about storing and securing sensitive credentials themselves.\n * The WorkloadIdentityCredential supports Azure workload identity authentication on Azure Kubernetes and acquires\n * a token using the SACs available in the Azure Kubernetes environment.\n * Refer to <a href=\"https://learn.microsoft.com/azure/aks/workload-identity-overview\">Azure Active Directory\n * Workload Identity</a> for more information.\n */\nexport class WorkloadIdentityCredential implements TokenCredential {\n  private client: ClientAssertionCredential | undefined;\n  private azureFederatedTokenFileContent: string | undefined = undefined;\n  private cacheDate: number | undefined = undefined;\n  private federatedTokenFilePath: string | undefined;\n\n  /**\n   * WorkloadIdentityCredential supports Azure workload identity on Kubernetes.\n   *\n   * @param options - The identity client options to use for authentication.\n   */\n  constructor(options?: WorkloadIdentityCredentialOptions) {\n    // Logging environment variables for error details\n    const assignedEnv = processEnvVars(SupportedWorkloadEnvironmentVariables).assigned.join(\", \");\n    logger.info(`Found the following environment variables: ${assignedEnv}`);\n\n    const workloadIdentityCredentialOptions = options ?? {};\n    const tenantId = workloadIdentityCredentialOptions.tenantId || process.env.AZURE_TENANT_ID;\n    const clientId = workloadIdentityCredentialOptions.clientId || process.env.AZURE_CLIENT_ID;\n    this.federatedTokenFilePath =\n      workloadIdentityCredentialOptions.tokenFilePath || process.env.AZURE_FEDERATED_TOKEN_FILE;\n    if (tenantId) {\n      checkTenantId(logger, tenantId);\n    }\n    if (clientId && tenantId && this.federatedTokenFilePath) {\n      logger.info(\n        `Invoking ClientAssertionCredential with tenant ID: ${tenantId}, clientId: ${workloadIdentityCredentialOptions.clientId} and federated token path: [REDACTED]`\n      );\n      this.client = new ClientAssertionCredential(\n        tenantId,\n        clientId,\n        this.readFileContents.bind(this),\n        options\n      );\n    }\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options?: GetTokenOptions\n  ): Promise<AccessToken | null> {\n    if (!this.client) {\n      const errorMessage = `${credentialName}: is unavailable. tenantId, clientId, and federatedTokenFilePath are required parameters. \n      In DefaultAzureCredential and ManagedIdentityCredential, these can be provided as environment variables - \n      \"AZURE_TENANT_ID\",\n      \"AZURE_CLIENT_ID\",\n      \"AZURE_FEDERATED_TOKEN_FILE\". See the troubleshooting guide for more information: https://aka.ms/azsdk/js/identity/workloadidentitycredential/troubleshoot  `;\n      logger.info(errorMessage);\n      throw new CredentialUnavailableError(errorMessage);\n    }\n    logger.info(\"Invoking getToken() of Client Assertion Credential\");\n    return this.client.getToken(scopes, options);\n  }\n\n  private async readFileContents(): Promise<string> {\n    // Cached assertions expire after 5 minutes\n    if (this.cacheDate !== undefined && Date.now() - this.cacheDate >= 1000 * 60 * 5) {\n      this.azureFederatedTokenFileContent = undefined;\n    }\n    if (!this.federatedTokenFilePath) {\n      throw new CredentialUnavailableError(\n        `${credentialName}: is unavailable. Invalid file path provided ${this.federatedTokenFilePath}.`\n      );\n    }\n    if (!this.azureFederatedTokenFileContent) {\n      const file = await readFile(this.federatedTokenFilePath, \"utf8\");\n      const value = file.trim();\n      if (!value) {\n        throw new CredentialUnavailableError(\n          `${credentialName}: is unavailable. No content on the file ${this.federatedTokenFilePath}.`\n        );\n      } else {\n        this.azureFederatedTokenFileContent = value;\n        this.cacheDate = Date.now();\n      }\n    }\n    return this.azureFederatedTokenFileContent;\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions } from \"@azure/core-auth\";\nimport { MSI, MSIConfiguration } from \"./models\";\nimport { WorkloadIdentityCredential } from \"../workloadIdentityCredential\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { WorkloadIdentityCredentialOptions } from \"../workloadIdentityCredentialOptions\";\n\nconst msiName = \"ManagedIdentityCredential - Token Exchange\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Defines how to determine whether the token exchange MSI is available, and also how to retrieve a token from the token exchange MSI.\n */\nexport function tokenExchangeMsi(): MSI {\n  return {\n    name: \"tokenExchangeMsi\",\n    async isAvailable({ clientId }): Promise<boolean> {\n      const env = process.env;\n      const result = Boolean(\n        (clientId || env.AZURE_CLIENT_ID) &&\n          env.AZURE_TENANT_ID &&\n          process.env.AZURE_FEDERATED_TOKEN_FILE\n      );\n      if (!result) {\n        logger.info(\n          `${msiName}: Unavailable. The environment variables needed are: AZURE_CLIENT_ID (or the client ID sent through the parameters), AZURE_TENANT_ID and AZURE_FEDERATED_TOKEN_FILE`\n        );\n      }\n      return result;\n    },\n    async getToken(\n      configuration: MSIConfiguration,\n      getTokenOptions: GetTokenOptions = {}\n    ): Promise<AccessToken | null> {\n      const { scopes, clientId } = configuration;\n      const identityClientTokenCredentialOptions = {};\n      const workloadIdentityCredential = new WorkloadIdentityCredential({\n        clientId,\n        tenantId: process.env.AZURE_TENANT_ID,\n        tokenFilePath: process.env.AZURE_FEDERATED_TOKEN_FILE,\n        ...identityClientTokenCredentialOptions,\n        disableInstanceDiscovery: true,\n      } as WorkloadIdentityCredentialOptions);\n      const token = await workloadIdentityCredential.getToken(scopes, getTokenOptions);\n      return token;\n    },\n  };\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport https from \"https\";\nimport {\n  PipelineRequestOptions,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { GetTokenOptions } from \"@azure/core-auth\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { MSI, MSIConfiguration, MSIToken } from \"./models\";\nimport { mapScopesToResource } from \"./utils\";\nimport { azureFabricVersion } from \"./constants\";\n\n// This MSI can be easily tested by deploying a container to Azure Service Fabric with the Dockerfile:\n//\n//   FROM node:12\n//   RUN wget https://host.any/path/bash.sh\n//   CMD [\"bash\", \"bash.sh\"]\n//\n// Where the bash script contains:\n//\n//   curl --insecure $IDENTITY_ENDPOINT'?api-version=2019-07-01-preview&resource=https://vault.azure.net/' -H \"Secret: $IDENTITY_HEADER\"\n//\n\nconst msiName = \"ManagedIdentityCredential - Fabric MSI\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Generates the options used on the request for an access token.\n */\nfunction prepareRequestOptions(\n  scopes: string | string[],\n  clientId?: string,\n  resourceId?: string\n): PipelineRequestOptions {\n  const resource = mapScopesToResource(scopes);\n  if (!resource) {\n    throw new Error(`${msiName}: Multiple scopes are not supported.`);\n  }\n\n  const queryParameters: Record<string, string> = {\n    resource,\n    \"api-version\": azureFabricVersion,\n  };\n\n  if (clientId) {\n    queryParameters.client_id = clientId;\n  }\n  if (resourceId) {\n    queryParameters.msi_res_id = resourceId;\n  }\n  const query = new URLSearchParams(queryParameters);\n\n  // This error should not bubble up, since we verify that this environment variable is defined in the isAvailable() method defined below.\n  if (!process.env.IDENTITY_ENDPOINT) {\n    throw new Error(\"Missing environment variable: IDENTITY_ENDPOINT\");\n  }\n  if (!process.env.IDENTITY_HEADER) {\n    throw new Error(\"Missing environment variable: IDENTITY_HEADER\");\n  }\n\n  return {\n    url: `${process.env.IDENTITY_ENDPOINT}?${query.toString()}`,\n    method: \"GET\",\n    headers: createHttpHeaders({\n      Accept: \"application/json\",\n      secret: process.env.IDENTITY_HEADER,\n    }),\n  };\n}\n\n/**\n * Defines how to determine whether the Azure Service Fabric MSI is available, and also how to retrieve a token from the Azure Service Fabric MSI.\n */\nexport const fabricMsi: MSI = {\n  name: \"fabricMsi\",\n  async isAvailable({ scopes }): Promise<boolean> {\n    const resource = mapScopesToResource(scopes);\n    if (!resource) {\n      logger.info(`${msiName}: Unavailable. Multiple scopes are not supported.`);\n      return false;\n    }\n    const env = process.env;\n    const result = Boolean(\n      env.IDENTITY_ENDPOINT && env.IDENTITY_HEADER && env.IDENTITY_SERVER_THUMBPRINT\n    );\n    if (!result) {\n      logger.info(\n        `${msiName}: Unavailable. The environment variables needed are: IDENTITY_ENDPOINT, IDENTITY_HEADER and IDENTITY_SERVER_THUMBPRINT`\n      );\n    }\n    return result;\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {}\n  ): Promise<MSIToken | null> {\n    const { scopes, identityClient, clientId, resourceId } = configuration;\n\n    if (resourceId) {\n      logger.warning(\n        `${msiName}: user defined managed Identity by resource Id is not supported. Argument resourceId might be ignored by the service.`\n      );\n    }\n\n    logger.info(\n      [\n        `${msiName}:`,\n        \"Using the endpoint and the secret coming from the environment variables:\",\n        `IDENTITY_ENDPOINT=${process.env.IDENTITY_ENDPOINT},`,\n        \"IDENTITY_HEADER=[REDACTED] and\",\n        \"IDENTITY_SERVER_THUMBPRINT=[REDACTED].\",\n      ].join(\" \")\n    );\n\n    const request = createPipelineRequest({\n      abortSignal: getTokenOptions.abortSignal,\n      ...prepareRequestOptions(scopes, clientId, resourceId),\n      // The service fabric MSI endpoint will be HTTPS (however, the certificate will be self-signed).\n      // allowInsecureConnection: true\n    });\n\n    request.agent = new https.Agent({\n      // This is necessary because Service Fabric provides a self-signed certificate.\n      // The alternative path is to verify the certificate using the IDENTITY_SERVER_THUMBPRINT env variable.\n      rejectUnauthorized: false,\n    });\n\n    const tokenResponse = await identityClient.sendTokenRequest(request);\n    return (tokenResponse && tokenResponse.accessToken) || null;\n  },\n};\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  PipelineRequestOptions,\n  createHttpHeaders,\n  createPipelineRequest,\n} from \"@azure/core-rest-pipeline\";\nimport { GetTokenOptions } from \"@azure/core-auth\";\nimport { credentialLogger } from \"../../util/logging\";\nimport { MSI, MSIConfiguration, MSIToken } from \"./models\";\nimport { mapScopesToResource } from \"./utils\";\n\nconst msiName = \"ManagedIdentityCredential - AppServiceMSI 2019\";\nconst logger = credentialLogger(msiName);\n\n/**\n * Generates the options used on the request for an access token.\n */\nfunction prepareRequestOptions(\n  scopes: string | string[],\n  clientId?: string,\n  resourceId?: string\n): PipelineRequestOptions {\n  const resource = mapScopesToResource(scopes);\n  if (!resource) {\n    throw new Error(`${msiName}: Multiple scopes are not supported.`);\n  }\n\n  const queryParameters: Record<string, string> = {\n    resource,\n    \"api-version\": \"2019-08-01\",\n  };\n\n  if (clientId) {\n    queryParameters.client_id = clientId;\n  }\n\n  if (resourceId) {\n    queryParameters.mi_res_id = resourceId;\n  }\n  const query = new URLSearchParams(queryParameters);\n\n  // This error should not bubble up, since we verify that this environment variable is defined in the isAvailable() method defined below.\n  if (!process.env.IDENTITY_ENDPOINT) {\n    throw new Error(`${msiName}: Missing environment variable: IDENTITY_ENDPOINT`);\n  }\n  if (!process.env.IDENTITY_HEADER) {\n    throw new Error(`${msiName}: Missing environment variable: IDENTITY_HEADER`);\n  }\n\n  return {\n    url: `${process.env.IDENTITY_ENDPOINT}?${query.toString()}`,\n    method: \"GET\",\n    headers: createHttpHeaders({\n      Accept: \"application/json\",\n      \"X-IDENTITY-HEADER\": process.env.IDENTITY_HEADER,\n    }),\n  };\n}\n\n/**\n * Defines how to determine whether the Azure App Service MSI is available, and also how to retrieve a token from the Azure App Service MSI.\n */\nexport const appServiceMsi2019: MSI = {\n  name: \"appServiceMsi2019\",\n  async isAvailable({ scopes }): Promise<boolean> {\n    const resource = mapScopesToResource(scopes);\n    if (!resource) {\n      logger.info(`${msiName}: Unavailable. Multiple scopes are not supported.`);\n      return false;\n    }\n    const env = process.env;\n    const result = Boolean(env.IDENTITY_ENDPOINT && env.IDENTITY_HEADER);\n    if (!result) {\n      logger.info(\n        `${msiName}: Unavailable. The environment variables needed are: IDENTITY_ENDPOINT and IDENTITY_HEADER.`\n      );\n    }\n    return result;\n  },\n  async getToken(\n    configuration: MSIConfiguration,\n    getTokenOptions: GetTokenOptions = {}\n  ): Promise<MSIToken | null> {\n    const { identityClient, scopes, clientId, resourceId } = configuration;\n\n    logger.info(\n      `${msiName}: Using the endpoint and the secret coming form the environment variables: IDENTITY_ENDPOINT=${process.env.IDENTITY_ENDPOINT} and IDENTITY_HEADER=[REDACTED].`\n    );\n\n    const request = createPipelineRequest({\n      abortSignal: getTokenOptions.abortSignal,\n      ...prepareRequestOptions(scopes, clientId, resourceId),\n      // Generally, MSI endpoints use the HTTP protocol, without transport layer security (TLS).\n      allowInsecureConnection: true,\n    });\n    const tokenResponse = await identityClient.sendTokenRequest(request);\n    return (tokenResponse && tokenResponse.accessToken) || null;\n  },\n};\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\n\nimport { IdentityClient } from \"../../client/identityClient\";\nimport { TokenCredentialOptions } from \"../../tokenCredentialOptions\";\nimport {\n  AuthenticationError,\n  AuthenticationRequiredError,\n  CredentialUnavailableError,\n} from \"../../errors\";\nimport { credentialLogger, formatError, formatSuccess } from \"../../util/logging\";\nimport { appServiceMsi2017 } from \"./appServiceMsi2017\";\nimport { tracingClient } from \"../../util/tracing\";\nimport { cloudShellMsi } from \"./cloudShellMsi\";\nimport { imdsMsi } from \"./imdsMsi\";\nimport { MSI, MSIToken } from \"./models\";\nimport { arcMsi } from \"./arcMsi\";\nimport { tokenExchangeMsi } from \"./tokenExchangeMsi\";\nimport { fabricMsi } from \"./fabricMsi\";\nimport { appServiceMsi2019 } from \"./appServiceMsi2019\";\nimport { AppTokenProviderParameters, ConfidentialClientApplication } from \"@azure/msal-node\";\nimport { DeveloperSignOnClientId } from \"../../constants\";\nimport { MsalResult, MsalToken } from \"../../msal/types\";\nimport { getMSALLogLevel } from \"../../msal/utils\";\nimport { getLogLevel } from \"@azure/logger\";\n\nconst logger = credentialLogger(\"ManagedIdentityCredential\");\n\n/**\n * Options to send on the {@link ManagedIdentityCredential} constructor.\n * This variation supports `clientId` and not `resourceId`, since only one of both is supported.\n */\nexport interface ManagedIdentityCredentialClientIdOptions extends TokenCredentialOptions {\n  /**\n   * The client ID of the user - assigned identity, or app registration(when working with AKS pod - identity).\n   */\n  clientId?: string;\n}\n\n/**\n * Options to send on the {@link ManagedIdentityCredential} constructor.\n * This variation supports `resourceId` and not `clientId`, since only one of both is supported.\n */\nexport interface ManagedIdentityCredentialResourceIdOptions extends TokenCredentialOptions {\n  /**\n   * Allows specifying a custom resource Id.\n   * In scenarios such as when user assigned identities are created using an ARM template,\n   * where the resource Id of the identity is known but the client Id can't be known ahead of time,\n   * this parameter allows programs to use these user assigned identities\n   * without having to first determine the client Id of the created identity.\n   */\n  resourceId: string;\n}\n\n/**\n * Attempts authentication using a managed identity available at the deployment environment.\n * This authentication type works in Azure VMs, App Service instances, Azure Functions applications,\n * Azure Kubernetes Services, Azure Service Fabric instances and inside of the Azure Cloud Shell.\n *\n * More information about configuring managed identities can be found here:\n * https://learn.microsoft.com/en-us/azure/active-directory/managed-identities-azure-resources/overview\n */\nexport class ManagedIdentityCredential implements TokenCredential {\n  private identityClient: IdentityClient;\n  private clientId: string | undefined;\n  private resourceId: string | undefined;\n  private isEndpointUnavailable: boolean | null = null;\n  private isAvailableIdentityClient: IdentityClient;\n  private confidentialApp: ConfidentialClientApplication;\n  private isAppTokenProviderInitialized: boolean = false;\n\n  /**\n   * Creates an instance of ManagedIdentityCredential with the client ID of a\n   * user-assigned identity, or app registration (when working with AKS pod-identity).\n   *\n   * @param clientId - The client ID of the user-assigned identity, or app registration (when working with AKS pod-identity).\n   * @param options - Options for configuring the client which makes the access token request.\n   */\n  constructor(clientId: string, options?: TokenCredentialOptions);\n  /**\n   * Creates an instance of ManagedIdentityCredential with clientId\n   *\n   * @param options - Options for configuring the client which makes the access token request.\n   */\n  constructor(options?: ManagedIdentityCredentialClientIdOptions);\n  /**\n   * Creates an instance of ManagedIdentityCredential with Resource Id\n   *\n   * @param options - Options for configuring the resource which makes the access token request.\n   */\n  constructor(options?: ManagedIdentityCredentialResourceIdOptions);\n  /**\n   * @internal\n   * @hidden\n   */\n  constructor(\n    clientIdOrOptions?:\n      | string\n      | ManagedIdentityCredentialClientIdOptions\n      | ManagedIdentityCredentialResourceIdOptions,\n    options?: TokenCredentialOptions\n  ) {\n    let _options: TokenCredentialOptions | undefined;\n    if (typeof clientIdOrOptions === \"string\") {\n      this.clientId = clientIdOrOptions;\n      _options = options;\n    } else {\n      this.clientId = (clientIdOrOptions as ManagedIdentityCredentialClientIdOptions)?.clientId;\n      _options = clientIdOrOptions;\n    }\n    this.resourceId = (_options as ManagedIdentityCredentialResourceIdOptions)?.resourceId;\n    // For JavaScript users.\n    if (this.clientId && this.resourceId) {\n      throw new Error(\n        `${ManagedIdentityCredential.name} - Client Id and Resource Id can't be provided at the same time.`\n      );\n    }\n    this.identityClient = new IdentityClient(_options);\n    this.isAvailableIdentityClient = new IdentityClient({\n      ..._options,\n      retryOptions: {\n        maxRetries: 0,\n      },\n    });\n\n    /**  authority host validation and metadata discovery to be skipped in managed identity\n     * since this wasn't done previously before adding token cache support\n     */\n    this.confidentialApp = new ConfidentialClientApplication({\n      auth: {\n        authority: \"https://login.microsoftonline.com/managed_identity\",\n        clientId: this.clientId ?? DeveloperSignOnClientId,\n        clientSecret: \"dummy-secret\",\n        cloudDiscoveryMetadata:\n          '{\"tenant_discovery_endpoint\":\"https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration\",\"api-version\":\"1.1\",\"metadata\":[{\"preferred_network\":\"login.microsoftonline.com\",\"preferred_cache\":\"login.windows.net\",\"aliases\":[\"login.microsoftonline.com\",\"login.windows.net\",\"login.microsoft.com\",\"sts.windows.net\"]},{\"preferred_network\":\"login.partner.microsoftonline.cn\",\"preferred_cache\":\"login.partner.microsoftonline.cn\",\"aliases\":[\"login.partner.microsoftonline.cn\",\"login.chinacloudapi.cn\"]},{\"preferred_network\":\"login.microsoftonline.de\",\"preferred_cache\":\"login.microsoftonline.de\",\"aliases\":[\"login.microsoftonline.de\"]},{\"preferred_network\":\"login.microsoftonline.us\",\"preferred_cache\":\"login.microsoftonline.us\",\"aliases\":[\"login.microsoftonline.us\",\"login.usgovcloudapi.net\"]},{\"preferred_network\":\"login-us.microsoftonline.com\",\"preferred_cache\":\"login-us.microsoftonline.com\",\"aliases\":[\"login-us.microsoftonline.com\"]}]}',\n        authorityMetadata:\n          '{\"token_endpoint\":\"https://login.microsoftonline.com/common/oauth2/v2.0/token\",\"token_endpoint_auth_methods_supported\":[\"client_secret_post\",\"private_key_jwt\",\"client_secret_basic\"],\"jwks_uri\":\"https://login.microsoftonline.com/common/discovery/v2.0/keys\",\"response_modes_supported\":[\"query\",\"fragment\",\"form_post\"],\"subject_types_supported\":[\"pairwise\"],\"id_token_signing_alg_values_supported\":[\"RS256\"],\"response_types_supported\":[\"code\",\"id_token\",\"code id_token\",\"id_token token\"],\"scopes_supported\":[\"openid\",\"profile\",\"email\",\"offline_access\"],\"issuer\":\"https://login.microsoftonline.com/{tenantid}/v2.0\",\"request_uri_parameter_supported\":false,\"userinfo_endpoint\":\"https://graph.microsoft.com/oidc/userinfo\",\"authorization_endpoint\":\"https://login.microsoftonline.com/common/oauth2/v2.0/authorize\",\"device_authorization_endpoint\":\"https://login.microsoftonline.com/common/oauth2/v2.0/devicecode\",\"http_logout_supported\":true,\"frontchannel_logout_supported\":true,\"end_session_endpoint\":\"https://login.microsoftonline.com/common/oauth2/v2.0/logout\",\"claims_supported\":[\"sub\",\"iss\",\"cloud_instance_name\",\"cloud_instance_host_name\",\"cloud_graph_host_name\",\"msgraph_host\",\"aud\",\"exp\",\"iat\",\"auth_time\",\"acr\",\"nonce\",\"preferred_username\",\"name\",\"tid\",\"ver\",\"at_hash\",\"c_hash\",\"email\"],\"kerberos_endpoint\":\"https://login.microsoftonline.com/common/kerberos\",\"tenant_region_scope\":null,\"cloud_instance_name\":\"microsoftonline.com\",\"cloud_graph_host_name\":\"graph.windows.net\",\"msgraph_host\":\"graph.microsoft.com\",\"rbac_url\":\"https://pas.windows.net\"}',\n        clientCapabilities: [],\n      },\n      system: {\n        loggerOptions: {\n          logLevel: getMSALLogLevel(getLogLevel()),\n        },\n      },\n    });\n  }\n\n  private cachedMSI: MSI | undefined;\n\n  private async cachedAvailableMSI(\n    scopes: string | string[],\n    getTokenOptions?: GetTokenOptions\n  ): Promise<MSI> {\n    if (this.cachedMSI) {\n      return this.cachedMSI;\n    }\n\n    const MSIs = [\n      arcMsi,\n      fabricMsi,\n      appServiceMsi2019,\n      appServiceMsi2017,\n      cloudShellMsi,\n      tokenExchangeMsi(),\n      imdsMsi,\n    ];\n\n    for (const msi of MSIs) {\n      if (\n        await msi.isAvailable({\n          scopes,\n          identityClient: this.isAvailableIdentityClient,\n          clientId: this.clientId,\n          resourceId: this.resourceId,\n          getTokenOptions,\n        })\n      ) {\n        this.cachedMSI = msi;\n        return msi;\n      }\n    }\n\n    throw new CredentialUnavailableError(\n      `${ManagedIdentityCredential.name} - No MSI credential available`\n    );\n  }\n\n  private async authenticateManagedIdentity(\n    scopes: string | string[],\n    getTokenOptions?: GetTokenOptions\n  ): Promise<MSIToken | null> {\n    const { span, updatedOptions } = tracingClient.startSpan(\n      `${ManagedIdentityCredential.name}.authenticateManagedIdentity`,\n      getTokenOptions\n    );\n\n    try {\n      // Determining the available MSI, and avoiding checking for other MSIs while the program is running.\n      const availableMSI = await this.cachedAvailableMSI(scopes, updatedOptions);\n      return availableMSI.getToken(\n        {\n          identityClient: this.identityClient,\n          scopes,\n          clientId: this.clientId,\n          resourceId: this.resourceId,\n        },\n        updatedOptions\n      );\n    } catch (err: any) {\n      span.setStatus({\n        status: \"error\",\n        error: err,\n      });\n      throw err;\n    } finally {\n      span.end();\n    }\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   * If an unexpected error occurs, an {@link AuthenticationError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options?: GetTokenOptions\n  ): Promise<AccessToken> {\n    let result: AccessToken | null = null;\n    const { span, updatedOptions } = tracingClient.startSpan(\n      `${ManagedIdentityCredential.name}.getToken`,\n      options\n    );\n    try {\n      // isEndpointAvailable can be true, false, or null,\n      // If it's null, it means we don't yet know whether\n      // the endpoint is available and need to check for it.\n      if (this.isEndpointUnavailable !== true) {\n        const availableMSI = await this.cachedAvailableMSI(scopes, updatedOptions);\n        if (availableMSI.name === \"tokenExchangeMsi\") {\n          result = await this.authenticateManagedIdentity(scopes, updatedOptions);\n        } else {\n          const appTokenParameters: AppTokenProviderParameters = {\n            correlationId: this.identityClient.getCorrelationId(),\n            tenantId: options?.tenantId || \"managed_identity\",\n            scopes: Array.isArray(scopes) ? scopes : [scopes],\n            claims: options?.claims,\n          };\n\n          // Added a check to see if SetAppTokenProvider was already defined.\n          this.initializeSetAppTokenProvider();\n          const authenticationResult = await this.confidentialApp.acquireTokenByClientCredential({\n            ...appTokenParameters,\n          });\n          result = this.handleResult(scopes, authenticationResult || undefined);\n        }\n        if (result === null) {\n          // If authenticateManagedIdentity returns null,\n          // it means no MSI endpoints are available.\n          // If so, we avoid trying to reach to them in future requests.\n          this.isEndpointUnavailable = true;\n\n          // It also means that the endpoint answered with either 200 or 201 (see the sendTokenRequest method),\n          // yet we had no access token. For this reason, we'll throw once with a specific message:\n          const error = new CredentialUnavailableError(\n            \"The managed identity endpoint was reached, yet no tokens were received.\"\n          );\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n\n        // Since `authenticateManagedIdentity` didn't throw, and the result was not null,\n        // We will assume that this endpoint is reachable from this point forward,\n        // and avoid pinging again to it.\n        this.isEndpointUnavailable = false;\n      } else {\n        // We've previously determined that the endpoint was unavailable,\n        // either because it was unreachable or permanently unable to authenticate.\n        const error = new CredentialUnavailableError(\n          \"The managed identity endpoint is not currently available\"\n        );\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n\n      logger.getToken.info(formatSuccess(scopes));\n      return result;\n    } catch (err: any) {\n      // CredentialUnavailable errors are expected to reach here.\n      // We intend them to bubble up, so that DefaultAzureCredential can catch them.\n      if (err.name === \"AuthenticationRequiredError\") {\n        throw err;\n      }\n\n      // Expected errors to reach this point:\n      // - Errors coming from a method unexpectedly breaking.\n      // - When identityClient.sendTokenRequest throws, in which case\n      //   if the status code was 400, it means that the endpoint is working,\n      //   but no identity is available.\n\n      span.setStatus({\n        status: \"error\",\n        error: err,\n      });\n\n      // If either the network is unreachable,\n      // we can safely assume the credential is unavailable.\n      if (err.code === \"ENETUNREACH\") {\n        const error = new CredentialUnavailableError(\n          `${ManagedIdentityCredential.name}: Unavailable. Network unreachable. Message: ${err.message}`\n        );\n\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n\n      // If either the host was unreachable,\n      // we can safely assume the credential is unavailable.\n      if (err.code === \"EHOSTUNREACH\") {\n        const error = new CredentialUnavailableError(\n          `${ManagedIdentityCredential.name}: Unavailable. No managed identity endpoint found. Message: ${err.message}`\n        );\n\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n      // If err.statusCode has a value of 400, it comes from sendTokenRequest,\n      // and it means that the endpoint is working, but that no identity is available.\n      if (err.statusCode === 400) {\n        throw new CredentialUnavailableError(\n          `${ManagedIdentityCredential.name}: The managed identity endpoint is indicating there's no available identity. Message: ${err.message}`\n        );\n      }\n\n      // This is a special case for Docker Desktop which responds with a 403 with a message that contains \"A socket operation was attempted to an unreachable network\"\n      // rather than just timing out, as expected.\n      if (err.statusCode === 403 || err.code === 403) {\n        if (err.message.includes(\"A socket operation was attempted to an unreachable network\")) {\n          const error = new CredentialUnavailableError(\n            `${ManagedIdentityCredential.name}: Unavailable. Network unreachable. Message: ${err.message}`\n          );\n\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n      }\n\n      // If the error has no status code, we can assume there was no available identity.\n      // This will throw silently during any ChainedTokenCredential.\n      if (err.statusCode === undefined) {\n        throw new CredentialUnavailableError(\n          `${ManagedIdentityCredential.name}: Authentication failed. Message ${err.message}`\n        );\n      }\n\n      // Any other error should break the chain.\n      throw new AuthenticationError(err.statusCode, {\n        error: `${ManagedIdentityCredential.name} authentication failed.`,\n        error_description: err.message,\n      });\n    } finally {\n      // Finally is always called, both if we return and if we throw in the above try/catch.\n      span.end();\n    }\n  }\n\n  /**\n   * Handles the MSAL authentication result.\n   * If the result has an account, we update the local account reference.\n   * If the token received is invalid, an error will be thrown depending on what's missing.\n   */\n  private handleResult(\n    scopes: string | string[],\n    result?: MsalResult,\n    getTokenOptions?: GetTokenOptions\n  ): AccessToken {\n    this.ensureValidMsalToken(scopes, result, getTokenOptions);\n    logger.getToken.info(formatSuccess(scopes));\n    return {\n      token: result!.accessToken!,\n      expiresOnTimestamp: result!.expiresOn!.getTime(),\n    };\n  }\n\n  /**\n   * Ensures the validity of the MSAL token\n   * @internal\n   */\n  private ensureValidMsalToken(\n    scopes: string | string[],\n    msalToken?: MsalToken,\n    getTokenOptions?: GetTokenOptions\n  ): void {\n    const error = (message: string): Error => {\n      logger.getToken.info(message);\n      return new AuthenticationRequiredError({\n        scopes: Array.isArray(scopes) ? scopes : [scopes],\n        getTokenOptions,\n        message,\n      });\n    };\n    if (!msalToken) {\n      throw error(\"No response\");\n    }\n    if (!msalToken.expiresOn) {\n      throw error(`Response had no \"expiresOn\" property.`);\n    }\n    if (!msalToken.accessToken) {\n      throw error(`Response had no \"accessToken\" property.`);\n    }\n  }\n\n  private initializeSetAppTokenProvider(): void {\n    if (!this.isAppTokenProviderInitialized) {\n      this.confidentialApp.SetAppTokenProvider(async (appTokenProviderParameters) => {\n        logger.info(\n          `SetAppTokenProvider invoked with parameters- ${JSON.stringify(\n            appTokenProviderParameters\n          )}`\n        );\n        const getTokenOptions: GetTokenOptions = {\n          ...appTokenProviderParameters,\n        };\n        logger.info(\n          `authenticateManagedIdentity invoked with scopes- ${JSON.stringify(\n            appTokenProviderParameters.scopes\n          )} and getTokenOptions - ${JSON.stringify(getTokenOptions)}`\n        );\n        const resultToken = await this.authenticateManagedIdentity(\n          appTokenProviderParameters.scopes,\n          getTokenOptions\n        );\n\n        if (resultToken) {\n          logger.info(`SetAppTokenProvider will save the token in cache`);\n\n          const expiresInSeconds = resultToken?.expiresOnTimestamp\n            ? Math.floor((resultToken.expiresOnTimestamp - Date.now()) / 1000)\n            : 0;\n          return {\n            accessToken: resultToken?.token,\n            expiresInSeconds,\n          };\n        } else {\n          logger.info(\n            `SetAppTokenProvider token has \"no_access_token_returned\" as the saved token`\n          );\n          return {\n            accessToken: \"no_access_token_returned\",\n            expiresInSeconds: 0,\n          };\n        }\n      });\n      this.isAppTokenProviderInitialized = true;\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { CredentialLogger, formatError } from \"./logging\";\n\n/**\n * Ensures the scopes value is an array.\n * @internal\n */\nexport function ensureScopes(scopes: string | string[]): string[] {\n  return Array.isArray(scopes) ? scopes : [scopes];\n}\n\n/**\n * Throws if the received scope is not valid.\n * @internal\n */\nexport function ensureValidScopeForDevTimeCreds(scope: string, logger: CredentialLogger): void {\n  if (!scope.match(/^[0-9a-zA-Z-_.:/]+$/)) {\n    const error = new Error(\"Invalid scope was specified by the user or calling client\");\n    logger.getToken.info(formatError(scope, error));\n    throw error;\n  }\n}\n\n/**\n * Returns the resource out of a scope.\n * @internal\n */\nexport function getScopeResource(scope: string): string {\n  return scope.replace(/\\/.default$/, \"\");\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError, formatSuccess } from \"../util/logging\";\nimport { ensureValidScopeForDevTimeCreds, getScopeResource } from \"../util/scopeUtils\";\nimport { AzureCliCredentialOptions } from \"./azureCliCredentialOptions\";\nimport { CredentialUnavailableError } from \"../errors\";\nimport child_process from \"child_process\";\nimport {\n  checkTenantId,\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\n/**\n * Mockable reference to the CLI credential cliCredentialFunctions\n * @internal\n */\nexport const cliCredentialInternals = {\n  /**\n   * @internal\n   */\n  getSafeWorkingDir(): string {\n    if (process.platform === \"win32\") {\n      if (!process.env.SystemRoot) {\n        throw new Error(\"Azure CLI credential expects a 'SystemRoot' environment variable\");\n      }\n      return process.env.SystemRoot;\n    } else {\n      return \"/bin\";\n    }\n  },\n\n  /**\n   * Gets the access token from Azure CLI\n   * @param resource - The resource to use when getting the token\n   * @internal\n   */\n  async getAzureCliAccessToken(\n    resource: string,\n    tenantId?: string,\n    timeout?: number\n  ): Promise<{ stdout: string; stderr: string; error: Error | null }> {\n    let tenantSection: string[] = [];\n    if (tenantId) {\n      tenantSection = [\"--tenant\", tenantId];\n    }\n    return new Promise((resolve, reject) => {\n      try {\n        child_process.execFile(\n          \"az\",\n          [\n            \"account\",\n            \"get-access-token\",\n            \"--output\",\n            \"json\",\n            \"--resource\",\n            resource,\n            ...tenantSection,\n          ],\n          { cwd: cliCredentialInternals.getSafeWorkingDir(), shell: true, timeout },\n          (error, stdout, stderr) => {\n            resolve({ stdout: stdout, stderr: stderr, error });\n          }\n        );\n      } catch (err: any) {\n        reject(err);\n      }\n    });\n  },\n};\n\nconst logger = credentialLogger(\"AzureCliCredential\");\n\n/**\n * This credential will use the currently logged-in user login information\n * via the Azure CLI ('az') commandline tool.\n * To do so, it will read the user access token and expire time\n * with Azure CLI command \"az account get-access-token\".\n */\nexport class AzureCliCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private timeout?: number;\n\n  /**\n   * Creates an instance of the {@link AzureCliCredential}.\n   *\n   * To use this credential, ensure that you have already logged\n   * in via the 'az' tool using the command \"az login\" from the commandline.\n   *\n   * @param options - Options, to optionally allow multi-tenant requests.\n   */\n  constructor(options?: AzureCliCredentialOptions) {\n    if (options?.tenantId) {\n      checkTenantId(logger, options?.tenantId);\n      this.tenantId = options?.tenantId;\n    }\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n    this.timeout = options?.processTimeoutInMs;\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AccessToken> {\n    const tenantId = processMultiTenantRequest(\n      this.tenantId,\n      options,\n      this.additionallyAllowedTenantIds\n    );\n\n    if (tenantId) {\n      checkTenantId(logger, tenantId);\n    }\n    const scope = typeof scopes === \"string\" ? scopes : scopes[0];\n    logger.getToken.info(`Using the scope ${scope}`);\n\n    return tracingClient.withSpan(`${this.constructor.name}.getToken`, options, async () => {\n      try {\n        ensureValidScopeForDevTimeCreds(scope, logger);\n        const resource = getScopeResource(scope);\n        const obj = await cliCredentialInternals.getAzureCliAccessToken(\n          resource,\n          tenantId,\n          this.timeout\n        );\n        const specificScope = obj.stderr?.match(\"(.*)az login --scope(.*)\");\n        const isLoginError = obj.stderr?.match(\"(.*)az login(.*)\") && !specificScope;\n        const isNotInstallError =\n          obj.stderr?.match(\"az:(.*)not found\") || obj.stderr?.startsWith(\"'az' is not recognized\");\n\n        if (isNotInstallError) {\n          const error = new CredentialUnavailableError(\n            \"Azure CLI could not be found. Please visit https://aka.ms/azure-cli for installation instructions and then, once installed, authenticate to your Azure account using 'az login'.\"\n          );\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n        if (isLoginError) {\n          const error = new CredentialUnavailableError(\n            \"Please run 'az login' from a command prompt to authenticate before using this credential.\"\n          );\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n        try {\n          const responseData = obj.stdout;\n          const response: { accessToken: string; expiresOn: string } = JSON.parse(responseData);\n          logger.getToken.info(formatSuccess(scopes));\n          const returnValue = {\n            token: response.accessToken,\n            expiresOnTimestamp: new Date(response.expiresOn).getTime(),\n          };\n          return returnValue;\n        } catch (e: any) {\n          if (obj.stderr) {\n            throw new CredentialUnavailableError(obj.stderr);\n          }\n          throw e;\n        }\n      } catch (err: any) {\n        const error =\n          err.name === \"CredentialUnavailableError\"\n            ? err\n            : new CredentialUnavailableError(\n                (err as Error).message || \"Unknown error while trying to retrieve the access token\"\n              );\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n    });\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as childProcess from \"child_process\";\n\n/**\n * Easy to mock childProcess utils.\n * @internal\n */\nexport const processUtils = {\n  /**\n   * Promisifying childProcess.execFile\n   * @internal\n   */\n  execFile(\n    file: string,\n    params: string[],\n    options?: childProcess.ExecFileOptionsWithStringEncoding\n  ): Promise<string | Buffer> {\n    return new Promise((resolve, reject) => {\n      childProcess.execFile(file, params, options, (error, stdout, stderr) => {\n        if (Buffer.isBuffer(stdout)) {\n          stdout = stdout.toString(\"utf8\");\n        }\n        if (Buffer.isBuffer(stderr)) {\n          stderr = stderr.toString(\"utf8\");\n        }\n        if (stderr || error) {\n          reject(stderr ? new Error(stderr) : error);\n        } else {\n          resolve(stdout);\n        }\n      });\n    });\n  },\n};\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError, formatSuccess } from \"../util/logging\";\nimport { ensureValidScopeForDevTimeCreds, getScopeResource } from \"../util/scopeUtils\";\nimport { AzurePowerShellCredentialOptions } from \"./azurePowerShellCredentialOptions\";\nimport { CredentialUnavailableError } from \"../errors\";\nimport {\n  checkTenantId,\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { processUtils } from \"../util/processUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"AzurePowerShellCredential\");\n\nconst isWindows = process.platform === \"win32\";\n\n/**\n * Returns a platform-appropriate command name by appending \".exe\" on Windows.\n *\n * @internal\n */\nexport function formatCommand(commandName: string): string {\n  if (isWindows) {\n    return `${commandName}.exe`;\n  } else {\n    return commandName;\n  }\n}\n\n/**\n * Receives a list of commands to run, executes them, then returns the outputs.\n * If anything fails, an error is thrown.\n * @internal\n */\nasync function runCommands(commands: string[][], timeout?: number): Promise<string[]> {\n  const results: string[] = [];\n\n  for (const command of commands) {\n    const [file, ...parameters] = command;\n    const result = (await processUtils.execFile(file, parameters, {\n      encoding: \"utf8\",\n      timeout,\n    })) as string;\n    results.push(result);\n  }\n\n  return results;\n}\n\n/**\n * Known PowerShell errors\n * @internal\n */\nexport const powerShellErrors = {\n  login: \"Run Connect-AzAccount to login\",\n  installed:\n    \"The specified module 'Az.Accounts' with version '2.2.0' was not loaded because no valid module file was found in any module directory\",\n};\n\n/**\n * Messages to use when throwing in this credential.\n * @internal\n */\nexport const powerShellPublicErrorMessages = {\n  login:\n    \"Please run 'Connect-AzAccount' from PowerShell to authenticate before using this credential.\",\n  installed: `The 'Az.Account' module >= 2.2.0 is not installed. Install the Azure Az PowerShell module with: \"Install-Module -Name Az -Scope CurrentUser -Repository PSGallery -Force\".`,\n  troubleshoot: `To troubleshoot, visit https://aka.ms/azsdk/js/identity/powershellcredential/troubleshoot.`,\n};\n\n// PowerShell Azure User not logged in error check.\nconst isLoginError: (err: Error) => RegExpMatchArray | null = (err: Error) =>\n  err.message.match(`(.*)${powerShellErrors.login}(.*)`);\n\n// Az Module not Installed in Azure PowerShell check.\nconst isNotInstalledError: (err: Error) => RegExpMatchArray | null = (err: Error) =>\n  err.message.match(powerShellErrors.installed);\n\n/**\n * The PowerShell commands to be tried, in order.\n *\n * @internal\n */\nexport const commandStack = [formatCommand(\"pwsh\")];\n\nif (isWindows) {\n  commandStack.push(formatCommand(\"powershell\"));\n}\n\n/**\n * This credential will use the currently logged-in user information from the\n * Azure PowerShell module. To do so, it will read the user access token and\n * expire time with Azure PowerShell command `Get-AzAccessToken -ResourceUrl {ResourceScope}`\n */\nexport class AzurePowerShellCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private timeout?: number;\n\n  /**\n   * Creates an instance of the {@link AzurePowerShellCredential}.\n   *\n   * To use this credential:\n   * - Install the Azure Az PowerShell module with:\n   *   `Install-Module -Name Az -Scope CurrentUser -Repository PSGallery -Force`.\n   * - You have already logged in to Azure PowerShell using the command\n   * `Connect-AzAccount` from the command line.\n   *\n   * @param options - Options, to optionally allow multi-tenant requests.\n   */\n  constructor(options?: AzurePowerShellCredentialOptions) {\n    if (options?.tenantId) {\n      checkTenantId(logger, options?.tenantId);\n      this.tenantId = options?.tenantId;\n    }\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n    this.timeout = options?.processTimeoutInMs;\n  }\n\n  /**\n   * Gets the access token from Azure PowerShell\n   * @param resource - The resource to use when getting the token\n   */\n  private async getAzurePowerShellAccessToken(\n    resource: string,\n    tenantId?: string,\n    timeout?: number\n  ): Promise<{ Token: string; ExpiresOn: string }> {\n    // Clone the stack to avoid mutating it while iterating\n    for (const powerShellCommand of [...commandStack]) {\n      try {\n        await runCommands([[powerShellCommand, \"/?\"]], timeout);\n      } catch (e: any) {\n        // Remove this credential from the original stack so that we don't try it again.\n        commandStack.shift();\n        continue;\n      }\n\n      let tenantSection = \"\";\n      if (tenantId) {\n        tenantSection = `-TenantId \"${tenantId}\"`;\n      }\n\n      const results = await runCommands([\n        [\n          powerShellCommand,\n          \"-NoProfile\",\n          \"-NonInteractive\",\n          \"-Command\",\n          \"Import-Module Az.Accounts -MinimumVersion 2.2.0 -PassThru\",\n        ],\n        [\n          powerShellCommand,\n          \"-NoProfile\",\n          \"-NonInteractive\",\n          \"-Command\",\n          `Get-AzAccessToken ${tenantSection} -ResourceUrl \"${resource}\" | ConvertTo-Json`,\n        ],\n      ]);\n\n      const result = results[1];\n      try {\n        return JSON.parse(result);\n      } catch (e: any) {\n        throw new Error(`Unable to parse the output of PowerShell. Received output: ${result}`);\n      }\n    }\n\n    throw new Error(`Unable to execute PowerShell. Ensure that it is installed in your system`);\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If the authentication cannot be performed through PowerShell, a {@link CredentialUnavailableError} will be thrown.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AccessToken> {\n    return tracingClient.withSpan(`${this.constructor.name}.getToken`, options, async () => {\n      const tenantId = processMultiTenantRequest(\n        this.tenantId,\n        options,\n        this.additionallyAllowedTenantIds\n      );\n      const scope = typeof scopes === \"string\" ? scopes : scopes[0];\n      if (tenantId) {\n        checkTenantId(logger, tenantId);\n      }\n      try {\n        ensureValidScopeForDevTimeCreds(scope, logger);\n        logger.getToken.info(`Using the scope ${scope}`);\n        const resource = getScopeResource(scope);\n        const response = await this.getAzurePowerShellAccessToken(resource, tenantId, this.timeout);\n        logger.getToken.info(formatSuccess(scopes));\n        return {\n          token: response.Token,\n          expiresOnTimestamp: new Date(response.ExpiresOn).getTime(),\n        };\n      } catch (err: any) {\n        if (isNotInstalledError(err)) {\n          const error = new CredentialUnavailableError(powerShellPublicErrorMessages.installed);\n          logger.getToken.info(formatError(scope, error));\n          throw error;\n        } else if (isLoginError(err)) {\n          const error = new CredentialUnavailableError(powerShellPublicErrorMessages.login);\n          logger.getToken.info(formatError(scope, error));\n          throw error;\n        }\n        const error = new CredentialUnavailableError(\n          `${err}. ${powerShellPublicErrorMessages.troubleshoot}`\n        );\n        logger.getToken.info(formatError(scope, error));\n        throw error;\n      }\n    });\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { AggregateAuthenticationError, CredentialUnavailableError } from \"../errors\";\nimport { credentialLogger, formatError, formatSuccess } from \"../util/logging\";\nimport { tracingClient } from \"../util/tracing\";\n\n/**\n * @internal\n */\nexport const logger = credentialLogger(\"ChainedTokenCredential\");\n\n/**\n * Enables multiple `TokenCredential` implementations to be tried in order\n * until one of the getToken methods returns an access token.\n */\nexport class ChainedTokenCredential implements TokenCredential {\n  private _sources: TokenCredential[] = [];\n\n  /**\n   * Creates an instance of ChainedTokenCredential using the given credentials.\n   *\n   * @param sources - `TokenCredential` implementations to be tried in order.\n   *\n   * Example usage:\n   * ```javascript\n   * const firstCredential = new ClientSecretCredential(tenantId, clientId, clientSecret);\n   * const secondCredential = new ClientSecretCredential(tenantId, anotherClientId, anotherSecret);\n   * const credentialChain = new ChainedTokenCredential(firstCredential, secondCredential);\n   * ```\n   */\n  constructor(...sources: TokenCredential[]) {\n    this._sources = sources;\n  }\n\n  /**\n   * Returns the first access token returned by one of the chained\n   * `TokenCredential` implementations.  Throws an {@link AggregateAuthenticationError}\n   * when one or more credentials throws an {@link AuthenticationError} and\n   * no credentials have returned an access token.\n   *\n   * This method is called automatically by Azure SDK client libraries. You may call this method\n   * directly, but you must also handle token caching and token refreshing.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                `TokenCredential` implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    const { token } = await this.getTokenInternal(scopes, options);\n    return token;\n  }\n\n  private async getTokenInternal(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<{ token: AccessToken; successfulCredential: TokenCredential }> {\n    let token: AccessToken | null = null;\n    let successfulCredential: TokenCredential;\n    const errors: Error[] = [];\n\n    return tracingClient.withSpan(\n      \"ChainedTokenCredential.getToken\",\n      options,\n      async (updatedOptions) => {\n        for (let i = 0; i < this._sources.length && token === null; i++) {\n          try {\n            token = await this._sources[i].getToken(scopes, updatedOptions);\n            successfulCredential = this._sources[i];\n          } catch (err: any) {\n            if (\n              err.name === \"CredentialUnavailableError\" ||\n              err.name === \"AuthenticationRequiredError\"\n            ) {\n              errors.push(err);\n            } else {\n              logger.getToken.info(formatError(scopes, err));\n              throw err;\n            }\n          }\n        }\n\n        if (!token && errors.length > 0) {\n          const err = new AggregateAuthenticationError(\n            errors,\n            \"ChainedTokenCredential authentication failed.\"\n          );\n          logger.getToken.info(formatError(scopes, err));\n          throw err;\n        }\n\n        logger.getToken.info(\n          `Result for ${successfulCredential.constructor.name}: ${formatSuccess(scopes)}`\n        );\n\n        if (token === null) {\n          throw new CredentialUnavailableError(\"Failed to retrieve a valid token\");\n        }\n        return { token, successfulCredential };\n      }\n    );\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  ClientCertificateCredentialPEMConfiguration,\n  ClientCertificatePEMCertificate,\n  ClientCertificatePEMCertificatePath,\n} from \"../../credentials/clientCertificateCredential\";\nimport { MsalN<PERSON>, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { createHash, createPrivate<PERSON><PERSON> } from \"crypto\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { ClientCredentialRequest } from \"@azure/msal-node\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { formatError } from \"../../util/logging\";\nimport { promisify } from \"util\";\nimport { readFile } from \"fs\";\n\nconst readFileAsync = promisify(readFile);\n\n/**\n * Options that can be passed to configure MSAL to handle client certificates.\n * @internal\n */\nexport interface MsalClientCertificateOptions extends MsalNodeOptions {\n  /**\n   * Location of the PEM certificate.\n   */\n  configuration: ClientCertificateCredentialPEMConfiguration;\n  /**\n   * Option to include x5c header for SubjectName and Issuer name authorization.\n   * Set this option to send base64 encoded public certificate in the client assertion header as an x5c claim\n   */\n  sendCertificateChain?: boolean;\n}\n\n/**\n * Parts of a certificate, as understood by MSAL.\n * @internal\n */\ninterface CertificateParts {\n  /**\n   * Hex encoded X.509 SHA-1 thumbprint of the certificate\n   */\n  thumbprint: string;\n  /**\n   * The PEM encoded private key (string should contain -----BEGIN PRIVATE KEY----- ... -----END PRIVATE KEY-----\n   */\n  certificateContents: string;\n  /**\n   * x5c header.\n   */\n  x5c: string;\n}\n\n/**\n * Tries to asynchronously load a certificate from the given path.\n *\n * @param configuration - Either the PEM value or the path to the certificate.\n * @param sendCertificateChain - Option to include x5c header for SubjectName and Issuer name authorization.\n * @returns - The certificate parts, or `undefined` if the certificate could not be loaded.\n * @internal\n */\nexport async function parseCertificate(\n  configuration: ClientCertificateCredentialPEMConfiguration,\n  sendCertificateChain?: boolean\n): Promise<CertificateParts> {\n  const certificateParts: Partial<CertificateParts> = {};\n\n  const certificate: string | undefined = (configuration as ClientCertificatePEMCertificate)\n    .certificate;\n  const certificatePath: string | undefined = (configuration as ClientCertificatePEMCertificatePath)\n    .certificatePath;\n  certificateParts.certificateContents =\n    certificate || (await readFileAsync(certificatePath!, \"utf8\"));\n  if (sendCertificateChain) {\n    certificateParts.x5c = certificateParts.certificateContents;\n  }\n\n  const certificatePattern =\n    /(-+BEGIN CERTIFICATE-+)(\\n\\r?|\\r\\n?)([A-Za-z0-9+/\\n\\r]+=*)(\\n\\r?|\\r\\n?)(-+END CERTIFICATE-+)/g;\n  const publicKeys: string[] = [];\n\n  // Match all possible certificates, in the order they are in the file. These will form the chain that is used for x5c\n  let match;\n  do {\n    match = certificatePattern.exec(certificateParts.certificateContents);\n    if (match) {\n      publicKeys.push(match[3]);\n    }\n  } while (match);\n\n  if (publicKeys.length === 0) {\n    throw new Error(\"The file at the specified path does not contain a PEM-encoded certificate.\");\n  }\n\n  certificateParts.thumbprint = createHash(\"sha1\")\n    .update(Buffer.from(publicKeys[0], \"base64\"))\n    .digest(\"hex\")\n    .toUpperCase();\n\n  return certificateParts as CertificateParts;\n}\n\n/**\n * MSAL client certificate client. Calls to MSAL's confidential application's `acquireTokenByClientCredential` during `doGetToken`.\n * @internal\n */\nexport class MsalClientCertificate extends MsalNode {\n  private configuration: ClientCertificateCredentialPEMConfiguration;\n  private sendCertificateChain?: boolean;\n\n  constructor(options: MsalClientCertificateOptions) {\n    super(options);\n    this.requiresConfidential = true;\n    this.configuration = options.configuration;\n    this.sendCertificateChain = options.sendCertificateChain;\n  }\n\n  // Changing the MSAL configuration asynchronously\n  async init(options?: CredentialFlowGetTokenOptions): Promise<void> {\n    try {\n      const parts = await parseCertificate(this.configuration, this.sendCertificateChain);\n\n      let privateKey: string | undefined;\n      if (this.configuration.certificatePassword !== undefined) {\n        const privateKeyObject = createPrivateKey({\n          key: parts.certificateContents,\n          passphrase: this.configuration.certificatePassword,\n          format: \"pem\",\n        });\n\n        privateKey = privateKeyObject\n          .export({\n            format: \"pem\",\n            type: \"pkcs8\",\n          })\n          .toString();\n      } else {\n        privateKey = parts.certificateContents;\n      }\n\n      this.msalConfig.auth.clientCertificate = {\n        thumbprint: parts.thumbprint,\n        privateKey: privateKey,\n        x5c: parts.x5c,\n      };\n    } catch (error: any) {\n      this.logger.info(formatError(\"\", error));\n      throw error;\n    }\n    return super.init(options);\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    try {\n      const clientCredReq: ClientCredentialRequest = {\n        scopes,\n        correlationId: options.correlationId,\n        azureRegion: this.azureRegion,\n        authority: options.authority,\n        claims: options.claims,\n      };\n      const result = await this.getApp(\n        \"confidential\",\n        options.enableCae\n      ).acquireTokenByClientCredential(clientCredReq);\n      // Even though we're providing the same default in memory persistence cache that we use for DeviceCodeCredential,\n      // The Client Credential flow does not return the account information from the authentication service,\n      // so each time getToken gets called, we will have to acquire a new token through the service.\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { ClientCertificateCredentialOptions } from \"./clientCertificateCredentialOptions\";\nimport { MsalClientCertificate } from \"../msal/nodeFlows/msalClientCertificate\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { credentialLogger } from \"../util/logging\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst credentialName = \"ClientCertificateCredential\";\nconst logger = credentialLogger(credentialName);\n\n/**\n * Required configuration options for the {@link ClientCertificateCredential}, with the string contents of a PEM certificate\n */\nexport interface ClientCertificatePEMCertificate {\n  /**\n   * The PEM-encoded public/private key certificate on the filesystem.\n   */\n  certificate: string;\n\n  /**\n   * The password for the certificate file.\n   */\n  certificatePassword?: string;\n}\n/**\n * Required configuration options for the {@link ClientCertificateCredential}, with the path to a PEM certificate.\n */\nexport interface ClientCertificatePEMCertificatePath {\n  /**\n   * The path to the PEM-encoded public/private key certificate on the filesystem.\n   */\n  certificatePath: string;\n\n  /**\n   * The password for the certificate file.\n   */\n  certificatePassword?: string;\n}\n/**\n * Required configuration options for the {@link ClientCertificateCredential}, with either the string contents of a PEM certificate, or the path to a PEM certificate.\n */\nexport type ClientCertificateCredentialPEMConfiguration =\n  | ClientCertificatePEMCertificate\n  | ClientCertificatePEMCertificatePath;\n\n/**\n * Enables authentication to Azure Active Directory using a PEM-encoded\n * certificate that is assigned to an App Registration. More information\n * on how to configure certificate authentication can be found here:\n *\n * https://learn.microsoft.com/en-us/azure/active-directory/develop/active-directory-certificate-credentials#register-your-certificate-with-azure-ad\n *\n */\nexport class ClientCertificateCredential implements TokenCredential {\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n\n  /**\n   * Creates an instance of the ClientCertificateCredential with the details\n   * needed to authenticate against Azure Active Directory with a certificate.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param certificatePath - The path to a PEM-encoded public/private key certificate on the filesystem.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    certificatePath: string,\n    options?: ClientCertificateCredentialOptions\n  );\n  /**\n   * Creates an instance of the ClientCertificateCredential with the details\n   * needed to authenticate against Azure Active Directory with a certificate.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param configuration - Other parameters required, including the path of the certificate on the filesystem.\n   *                        If the type is ignored, we will throw the value of the path to a PEM certificate.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    configuration: ClientCertificatePEMCertificatePath,\n    options?: ClientCertificateCredentialOptions\n  );\n  /**\n   * Creates an instance of the ClientCertificateCredential with the details\n   * needed to authenticate against Azure Active Directory with a certificate.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param configuration - Other parameters required, including the PEM-encoded certificate as a string.\n   *                        If the type is ignored, we will throw the value of the PEM-encoded certificate.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    configuration: ClientCertificatePEMCertificate,\n    options?: ClientCertificateCredentialOptions\n  );\n  constructor(\n    tenantId: string,\n    clientId: string,\n    certificatePathOrConfiguration: string | ClientCertificateCredentialPEMConfiguration,\n    options: ClientCertificateCredentialOptions = {}\n  ) {\n    if (!tenantId || !clientId) {\n      throw new Error(`${credentialName}: tenantId and clientId are required parameters.`);\n    }\n\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n\n    const configuration: ClientCertificateCredentialPEMConfiguration = {\n      ...(typeof certificatePathOrConfiguration === \"string\"\n        ? {\n            certificatePath: certificatePathOrConfiguration,\n          }\n        : certificatePathOrConfiguration),\n    };\n    const certificate: string | undefined = (configuration as ClientCertificatePEMCertificate)\n      .certificate;\n    const certificatePath: string | undefined = (\n      configuration as ClientCertificatePEMCertificatePath\n    ).certificatePath;\n    if (!configuration || !(certificate || certificatePath)) {\n      throw new Error(\n        `${credentialName}: Provide either a PEM certificate in string form, or the path to that certificate in the filesystem. To troubleshoot, visit https://aka.ms/azsdk/js/identity/serviceprincipalauthentication/troubleshoot.`\n      );\n    }\n    if (certificate && certificatePath) {\n      throw new Error(\n        `${credentialName}: To avoid unexpected behaviors, providing both the contents of a PEM certificate and the path to a PEM certificate is forbidden. To troubleshoot, visit https://aka.ms/azsdk/js/identity/serviceprincipalauthentication/troubleshoot.`\n      );\n    }\n    this.msalFlow = new MsalClientCertificate({\n      ...options,\n      configuration,\n      logger,\n      clientId,\n      tenantId,\n      sendCertificateChain: options.sendCertificateChain,\n      tokenCredentialOptions: options,\n    });\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(`${credentialName}.getToken`, options, async (newOptions) => {\n      newOptions.tenantId = processMultiTenantRequest(\n        this.tenantId,\n        newOptions,\n        this.additionallyAllowedTenantIds,\n        logger\n      );\n\n      const arrayScopes = Array.isArray(scopes) ? scopes : [scopes];\n      return this.msalFlow.getToken(arrayScopes, newOptions);\n    });\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { Msal<PERSON><PERSON>, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\n\n/**\n * Options that can be passed to configure MS<PERSON> to handle client secrets.\n * @internal\n */\nexport interface MsalClientSecretOptions extends MsalNodeOptions {\n  /**\n   * A client secret that was generated for the App Registration.\n   */\n  clientSecret: string;\n}\n\n/**\n * MSAL client secret client. Calls to MSAL's confidential application's `acquireTokenByClientCredential` during `doGetToken`.\n * @internal\n */\nexport class MsalClientSecret extends MsalNode {\n  constructor(options: MsalClientSecretOptions) {\n    super(options);\n    this.requiresConfidential = true;\n    this.msalConfig.auth.clientSecret = options.clientSecret;\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    try {\n      const result = await this.getApp(\n        \"confidential\",\n        options.enableCae\n      ).acquireTokenByClientCredential({\n        scopes,\n        correlationId: options.correlationId,\n        azureRegion: this.azureRegion,\n        authority: options.authority,\n        claims: options.claims,\n      });\n      // The Client Credential flow does not return an account,\n      // so each time getToken gets called, we will have to acquire a new token through the service.\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { ClientSecretCredentialOptions } from \"./clientSecretCredentialOptions\";\nimport { MsalClientSecret } from \"../msal/nodeFlows/msalClientSecret\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"ClientSecretCredential\");\n\n/**\n * Enables authentication to Azure Active Directory using a client secret\n * that was generated for an App Registration. More information on how\n * to configure a client secret can be found here:\n *\n * https://docs.microsoft.com/en-us/azure/active-directory/develop/quickstart-configure-app-access-web-apis#add-credentials-to-your-web-application\n *\n */\nexport class ClientSecretCredential implements TokenCredential {\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n\n  /**\n   * Creates an instance of the ClientSecretCredential with the details\n   * needed to authenticate against Azure Active Directory with a client\n   * secret.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param clientSecret - A client secret that was generated for the App Registration.\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    clientSecret: string,\n    options: ClientSecretCredentialOptions = {}\n  ) {\n    if (!tenantId || !clientId || !clientSecret) {\n      throw new Error(\n        \"ClientSecretCredential: tenantId, clientId, and clientSecret are required parameters. To troubleshoot, visit https://aka.ms/azsdk/js/identity/serviceprincipalauthentication/troubleshoot.\"\n      );\n    }\n\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n\n    this.msalFlow = new MsalClientSecret({\n      ...options,\n      logger,\n      clientId,\n      tenantId,\n      clientSecret,\n      tokenCredentialOptions: options,\n    });\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n          logger\n        );\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalFlow.getToken(arrayScopes, newOptions);\n      }\n    );\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msalNode from \"@azure/msal-node\";\nimport { MsalNode, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\n\n/**\n * Options that can be passed to configure MSAL to handle authentication through username and password.\n * @internal\n */\nexport interface MsalUsernamePasswordOptions extends MsalNodeOptions {\n  username: string;\n  password: string;\n}\n\n/**\n * MSAL username and password client. Calls to the MSAL's public application's `acquireTokenByUsernamePassword` during `doGetToken`.\n * @internal\n */\nexport class MsalUsernamePassword extends MsalNode {\n  private username: string;\n  private password: string;\n\n  constructor(options: MsalUsernamePasswordOptions) {\n    super(options);\n    this.username = options.username;\n    this.password = options.password;\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    try {\n      const requestOptions: msalNode.UsernamePasswordRequest = {\n        scopes,\n        username: this.username,\n        password: this.password,\n        correlationId: options?.correlationId,\n        authority: options?.authority,\n        claims: options?.claims,\n      };\n      const result = await this.getApp(\"public\", options?.enableCae).acquireTokenByUsernamePassword(\n        requestOptions\n      );\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (error: any) {\n      throw this.handleError(scopes, error, options);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { MsalUsernamePassword } from \"../msal/nodeFlows/msalUsernamePassword\";\nimport { UsernamePasswordCredentialOptions } from \"./usernamePasswordCredentialOptions\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"UsernamePasswordCredential\");\n\n/**\n * Enables authentication to Azure Active Directory with a user's\n * username and password. This credential requires a high degree of\n * trust so you should only use it when other, more secure credential\n * types can't be used.\n */\nexport class UsernamePasswordCredential implements TokenCredential {\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n\n  /**\n   * Creates an instance of the UsernamePasswordCredential with the details\n   * needed to authenticate against Azure Active Directory with a username\n   * and password.\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory).\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param username - The user account's e-mail address (user name).\n   * @param password - The user account's account password\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    tenantId: string,\n    clientId: string,\n    username: string,\n    password: string,\n    options: UsernamePasswordCredentialOptions = {}\n  ) {\n    if (!tenantId || !clientId || !username || !password) {\n      throw new Error(\n        \"UsernamePasswordCredential: tenantId, clientId, username and password are required parameters. To troubleshoot, visit https://aka.ms/azsdk/js/identity/usernamepasswordcredential/troubleshoot.\"\n      );\n    }\n\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n\n    this.msalFlow = new MsalUsernamePassword({\n      ...options,\n      logger,\n      clientId,\n      tenantId,\n      username,\n      password,\n      tokenCredentialOptions: options || {},\n    });\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the user provided the option `disableAutomaticAuthentication`,\n   * once the token can't be retrieved silently,\n   * this method won't attempt to request user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n          logger\n        );\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalFlow.getToken(arrayScopes, newOptions);\n      }\n    );\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { AuthenticationError, CredentialUnavailableError } from \"../errors\";\nimport { credentialLogger, formatError, formatSuccess, processEnvVars } from \"../util/logging\";\nimport { ClientCertificateCredential } from \"./clientCertificateCredential\";\nimport { ClientSecretCredential } from \"./clientSecretCredential\";\nimport { EnvironmentCredentialOptions } from \"./environmentCredentialOptions\";\nimport { UsernamePasswordCredential } from \"./usernamePasswordCredential\";\nimport { checkTenantId } from \"../util/tenantIdUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\n/**\n * Contains the list of all supported environment variable names so that an\n * appropriate error message can be generated when no credentials can be\n * configured.\n *\n * @internal\n */\nexport const AllSupportedEnvironmentVariables = [\n  \"AZURE_TENANT_ID\",\n  \"AZURE_CLIENT_ID\",\n  \"AZURE_CLIENT_SECRET\",\n  \"AZURE_CLIENT_CERTIFICATE_PATH\",\n  \"AZURE_CLIENT_CERTIFICATE_PASSWORD\",\n  \"AZURE_USERNAME\",\n  \"AZURE_PASSWORD\",\n  \"AZURE_ADDITIONALLY_ALLOWED_TENANTS\",\n];\n\nfunction getAdditionallyAllowedTenants(): string[] {\n  const additionallyAllowedValues = process.env.AZURE_ADDITIONALLY_ALLOWED_TENANTS ?? \"\";\n  return additionallyAllowedValues.split(\";\");\n}\n\nconst credentialName = \"EnvironmentCredential\";\nconst logger = credentialLogger(credentialName);\n\n/**\n * Enables authentication to Azure Active Directory using a client secret or certificate, or as a user\n * with a username and password.\n */\nexport class EnvironmentCredential implements TokenCredential {\n  private _credential?:\n    | ClientSecretCredential\n    | ClientCertificateCredential\n    | UsernamePasswordCredential = undefined;\n  /**\n   * Creates an instance of the EnvironmentCredential class and decides what credential to use depending on the available environment variables.\n   *\n   * Required environment variables:\n   * - `AZURE_TENANT_ID`: The Azure Active Directory tenant (directory) ID.\n   * - `AZURE_CLIENT_ID`: The client (application) ID of an App Registration in the tenant.\n   *\n   * If setting the AZURE_TENANT_ID, then you can also set the additionally allowed tenants\n   * - `AZURE_ADDITIONALLY_ALLOWED_TENANTS`: For multi-tenant applications, specifies additional tenants for which the credential may acquire tokens with a single semicolon delimited string. Use * to allow all tenants.\n   *\n   * Environment variables used for client credential authentication:\n   * - `AZURE_CLIENT_SECRET`: A client secret that was generated for the App Registration.\n   * - `AZURE_CLIENT_CERTIFICATE_PATH`: The path to a PEM certificate to use during the authentication, instead of the client secret.\n   * - `AZURE_CLIENT_CERTIFICATE_PASSWORD`: (optional) password for the certificate file.\n   *\n   * Alternatively, users can provide environment variables for username and password authentication:\n   * - `AZURE_USERNAME`: Username to authenticate with.\n   * - `AZURE_PASSWORD`: Password to authenticate with.\n   *\n   * If the environment variables required to perform the authentication are missing, a {@link CredentialUnavailableError} will be thrown.\n   * If the authentication fails, or if there's an unknown error, an {@link AuthenticationError} will be thrown.\n   *\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(options?: EnvironmentCredentialOptions) {\n    // Keep track of any missing environment variables for error details\n\n    const assigned = processEnvVars(AllSupportedEnvironmentVariables).assigned.join(\", \");\n    logger.info(`Found the following environment variables: ${assigned}`);\n\n    const tenantId = process.env.AZURE_TENANT_ID,\n      clientId = process.env.AZURE_CLIENT_ID,\n      clientSecret = process.env.AZURE_CLIENT_SECRET;\n\n    const additionallyAllowedTenantIds = getAdditionallyAllowedTenants();\n    const newOptions = { ...options, additionallyAllowedTenantIds };\n\n    if (tenantId) {\n      checkTenantId(logger, tenantId);\n    }\n\n    if (tenantId && clientId && clientSecret) {\n      logger.info(\n        `Invoking ClientSecretCredential with tenant ID: ${tenantId}, clientId: ${clientId} and clientSecret: [REDACTED]`\n      );\n      this._credential = new ClientSecretCredential(tenantId, clientId, clientSecret, newOptions);\n      return;\n    }\n\n    const certificatePath = process.env.AZURE_CLIENT_CERTIFICATE_PATH;\n    const certificatePassword = process.env.AZURE_CLIENT_CERTIFICATE_PASSWORD;\n    if (tenantId && clientId && certificatePath) {\n      logger.info(\n        `Invoking ClientCertificateCredential with tenant ID: ${tenantId}, clientId: ${clientId} and certificatePath: ${certificatePath}`\n      );\n      this._credential = new ClientCertificateCredential(\n        tenantId,\n        clientId,\n        { certificatePath, certificatePassword },\n        newOptions\n      );\n      return;\n    }\n\n    const username = process.env.AZURE_USERNAME;\n    const password = process.env.AZURE_PASSWORD;\n    if (tenantId && clientId && username && password) {\n      logger.info(\n        `Invoking UsernamePasswordCredential with tenant ID: ${tenantId}, clientId: ${clientId} and username: ${username}`\n      );\n      this._credential = new UsernamePasswordCredential(\n        tenantId,\n        clientId,\n        username,\n        password,\n        newOptions\n      );\n    }\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - Optional parameters. See {@link GetTokenOptions}.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(`${credentialName}.getToken`, options, async (newOptions) => {\n      if (this._credential) {\n        try {\n          const result = await this._credential.getToken(scopes, newOptions);\n          logger.getToken.info(formatSuccess(scopes));\n          return result;\n        } catch (err: any) {\n          const authenticationError = new AuthenticationError(400, {\n            error: `${credentialName} authentication failed. To troubleshoot, visit https://aka.ms/azsdk/js/identity/environmentcredential/troubleshoot.`,\n            error_description: err.message.toString().split(\"More details:\").join(\"\"),\n          });\n          logger.getToken.info(formatError(scopes, authenticationError));\n          throw authenticationError;\n        }\n      }\n      throw new CredentialUnavailableError(\n        `${credentialName} is unavailable. No underlying credential could be used. To troubleshoot, visit https://aka.ms/azsdk/js/identity/environmentcredential/troubleshoot.`\n      );\n    });\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport { credentialLogger, formatError, formatSuccess } from \"../util/logging\";\nimport { AzureDeveloperCliCredentialOptions } from \"./azureDeveloperCliCredentialOptions\";\nimport { CredentialUnavailableError } from \"../errors\";\nimport child_process from \"child_process\";\nimport {\n  checkTenantId,\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { tracingClient } from \"../util/tracing\";\nimport { ensureValidScopeForDevTimeCreds } from \"../util/scopeUtils\";\n\n/**\n * Mockable reference to the Developer CLI credential cliCredentialFunctions\n * @internal\n */\nexport const developerCliCredentialInternals = {\n  /**\n   * @internal\n   */\n  getSafeWorkingDir(): string {\n    if (process.platform === \"win32\") {\n      if (!process.env.SystemRoot) {\n        throw new Error(\n          \"Azure Developer CLI credential expects a 'SystemRoot' environment variable\"\n        );\n      }\n      return process.env.SystemRoot;\n    } else {\n      return \"/bin\";\n    }\n  },\n\n  /**\n   * Gets the access token from Azure Developer CLI\n   * @param scopes - The scopes to use when getting the token\n   * @internal\n   */\n  async getAzdAccessToken(\n    scopes: string[],\n    tenantId?: string,\n    timeout?: number\n  ): Promise<{ stdout: string; stderr: string; error: Error | null }> {\n    let tenantSection: string[] = [];\n    if (tenantId) {\n      tenantSection = [\"--tenant-id\", tenantId];\n    }\n    return new Promise((resolve, reject) => {\n      try {\n        child_process.execFile(\n          \"azd\",\n          [\n            \"auth\",\n            \"token\",\n            \"--output\",\n            \"json\",\n            ...scopes.reduce<string[]>(\n              (previous, current) => previous.concat(\"--scope\", current),\n              []\n            ),\n            ...tenantSection,\n          ],\n          {\n            cwd: developerCliCredentialInternals.getSafeWorkingDir(),\n            timeout,\n          },\n          (error, stdout, stderr) => {\n            resolve({ stdout, stderr, error });\n          }\n        );\n      } catch (err: any) {\n        reject(err);\n      }\n    });\n  },\n};\n\nconst logger = credentialLogger(\"AzureDeveloperCliCredential\");\n\n/**\n * Azure Developer CLI is a command-line interface tool that allows developers to create, manage, and deploy\n * resources in Azure. It's built on top of the Azure CLI and provides additional functionality specific\n * to Azure developers. It allows users to authenticate as a user and/or a service principal against\n * <a href=\"https://learn.microsoft.com/azure/active-directory/fundamentals/\">Azure Active Directory (Azure AD)\n * </a>. The AzureDeveloperCliCredential authenticates in a development environment and acquires a token on behalf of\n * the logged-in user or service principal in the Azure Developer CLI. It acts as the Azure Developer CLI logged in user or\n * service principal and executes an Azure CLI command underneath to authenticate the application against\n * Azure Active Directory.\n *\n * <h2> Configure AzureDeveloperCliCredential </h2>\n *\n * To use this credential, the developer needs to authenticate locally in Azure Developer CLI using one of the\n * commands below:\n *\n * <ol>\n *     <li>Run \"azd auth login\" in Azure Developer CLI to authenticate interactively as a user.</li>\n *     <li>Run \"azd auth login --client-id clientID --client-secret clientSecret\n *     --tenant-id tenantID\" to authenticate as a service principal.</li>\n * </ol>\n *\n * You may need to repeat this process after a certain time period, depending on the refresh token validity in your\n * organization. Generally, the refresh token validity period is a few weeks to a few months.\n * AzureDeveloperCliCredential will prompt you to sign in again.\n */\nexport class AzureDeveloperCliCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private timeout?: number;\n\n  /**\n   * Creates an instance of the {@link AzureDeveloperCliCredential}.\n   *\n   * To use this credential, ensure that you have already logged\n   * in via the 'azd' tool using the command \"azd auth login\" from the commandline.\n   *\n   * @param options - Options, to optionally allow multi-tenant requests.\n   */\n  constructor(options?: AzureDeveloperCliCredentialOptions) {\n    if (options?.tenantId) {\n      checkTenantId(logger, options?.tenantId);\n      this.tenantId = options?.tenantId;\n    }\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n    this.timeout = options?.processTimeoutInMs;\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  public async getToken(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AccessToken> {\n    const tenantId = processMultiTenantRequest(\n      this.tenantId,\n      options,\n      this.additionallyAllowedTenantIds\n    );\n    if (tenantId) {\n      checkTenantId(logger, tenantId);\n    }\n    let scopeList: string[];\n    if (typeof scopes === \"string\") {\n      scopeList = [scopes];\n    } else {\n      scopeList = scopes;\n    }\n    logger.getToken.info(`Using the scopes ${scopes}`);\n\n    return tracingClient.withSpan(`${this.constructor.name}.getToken`, options, async () => {\n      try {\n        scopeList.forEach((scope) => {\n          ensureValidScopeForDevTimeCreds(scope, logger);\n        });\n        const obj = await developerCliCredentialInternals.getAzdAccessToken(\n          scopeList,\n          tenantId,\n          this.timeout\n        );\n        const isNotLoggedInError =\n          obj.stderr?.match(\"not logged in, run `azd login` to login\") ||\n          obj.stderr?.match(\"not logged in, run `azd auth login` to login\");\n        const isNotInstallError =\n          obj.stderr?.match(\"azd:(.*)not found\") ||\n          obj.stderr?.startsWith(\"'azd' is not recognized\");\n\n        if (isNotInstallError || (obj.error && (obj.error as any).code === \"ENOENT\")) {\n          const error = new CredentialUnavailableError(\n            \"Azure Developer CLI couldn't be found. To mitigate this issue, see the troubleshooting guidelines at https://aka.ms/azsdk/js/identity/azdevclicredential/troubleshoot.\"\n          );\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n\n        if (isNotLoggedInError) {\n          const error = new CredentialUnavailableError(\n            \"Please run 'azd auth login' from a command prompt to authenticate before using this credential. For more information, see the troubleshooting guidelines at https://aka.ms/azsdk/js/identity/azdevclicredential/troubleshoot.\"\n          );\n          logger.getToken.info(formatError(scopes, error));\n          throw error;\n        }\n\n        try {\n          const resp: { token: string; expiresOn: string } = JSON.parse(obj.stdout);\n          logger.getToken.info(formatSuccess(scopes));\n          return {\n            token: resp.token,\n            expiresOnTimestamp: new Date(resp.expiresOn).getTime(),\n          };\n        } catch (e: any) {\n          if (obj.stderr) {\n            throw new CredentialUnavailableError(obj.stderr);\n          }\n          throw e;\n        }\n      } catch (err: any) {\n        const error =\n          err.name === \"CredentialUnavailableError\"\n            ? err\n            : new CredentialUnavailableError(\n                (err as Error).message || \"Unknown error while trying to retrieve the access token\"\n              );\n        logger.getToken.info(formatError(scopes, error));\n        throw error;\n      }\n    });\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport {\n  DefaultAzureCredentialClientIdOptions,\n  DefaultAzureCredentialOptions,\n  DefaultAzureCredentialResourceIdOptions,\n} from \"./defaultAzureCredentialOptions\";\nimport {\n  ManagedIdentityCredential,\n  ManagedIdentityCredentialClientIdOptions,\n  ManagedIdentityCredentialResourceIdOptions,\n} from \"./managedIdentityCredential\";\nimport { AzureCliCredential } from \"./azureCliCredential\";\nimport { AzurePowerShellCredential } from \"./azurePowerShellCredential\";\nimport { ChainedTokenCredential } from \"./chainedTokenCredential\";\nimport { EnvironmentCredential } from \"./environmentCredential\";\nimport { TokenCredential } from \"@azure/core-auth\";\nimport { AzureDeveloperCliCredential } from \"./azureDeveloperCliCredential\";\nimport { WorkloadIdentityCredential } from \"./workloadIdentityCredential\";\nimport { WorkloadIdentityCredentialOptions } from \"./workloadIdentityCredentialOptions\";\n\n/**\n * The type of a class that implements TokenCredential and accepts either\n * {@link DefaultAzureCredentialClientIdOptions} or\n * {@link DefaultAzureCredentialResourceIdOptions} or\n * {@link DefaultAzureCredentialOptions}.\n */\ninterface DefaultCredentialConstructor {\n  new (options?: DefaultAzureCredentialOptions): TokenCredential;\n  new (options?: DefaultAzureCredentialResourceIdOptions): TokenCredential;\n  new (options?: DefaultAzureCredentialClientIdOptions): TokenCredential;\n}\n\n/**\n * A shim around ManagedIdentityCredential that adapts it to accept\n * `DefaultAzureCredentialOptions`.\n *\n * @internal\n */\nexport class DefaultManagedIdentityCredential extends ManagedIdentityCredential {\n  // Constructor overload with just client id options\n  constructor(options?: DefaultAzureCredentialClientIdOptions);\n  // Constructor overload with just resource id options\n  constructor(options?: DefaultAzureCredentialResourceIdOptions);\n  // Constructor overload with just the other default options\n  // Last constructor overload with Union of all options not required since the above two constructor overloads have optional properties\n  constructor(options?: DefaultAzureCredentialOptions) {\n    const managedIdentityClientId =\n      (options as DefaultAzureCredentialClientIdOptions)?.managedIdentityClientId ??\n      process.env.AZURE_CLIENT_ID;\n    const workloadIdentityClientId =\n      (options as DefaultAzureCredentialClientIdOptions)?.workloadIdentityClientId ??\n      managedIdentityClientId;\n    const managedResourceId = (options as DefaultAzureCredentialResourceIdOptions)\n      ?.managedIdentityResourceId;\n    const workloadFile = process.env.AZURE_FEDERATED_TOKEN_FILE;\n    const tenantId = options?.tenantId ?? process.env.AZURE_TENANT_ID;\n    // ManagedIdentityCredential throws if both the resourceId and the clientId are provided.\n    if (managedResourceId) {\n      const managedIdentityResourceIdOptions: ManagedIdentityCredentialResourceIdOptions = {\n        ...options,\n        resourceId: managedResourceId,\n      };\n      super(managedIdentityResourceIdOptions);\n    } else if (workloadFile && workloadIdentityClientId) {\n      const workloadIdentityCredentialOptions: DefaultAzureCredentialOptions = {\n        ...options,\n        tenantId: tenantId,\n      };\n      super(workloadIdentityClientId, workloadIdentityCredentialOptions);\n    } else if (managedIdentityClientId) {\n      const managedIdentityClientOptions: ManagedIdentityCredentialClientIdOptions = {\n        ...options,\n        clientId: managedIdentityClientId,\n      };\n      super(managedIdentityClientOptions);\n    } else {\n      super(options);\n    }\n  }\n}\n/**\n * A shim around WorkloadIdentityCredential that adapts it to accept\n * `DefaultAzureCredentialOptions`.\n *\n * @internal\n */\nexport class DefaultWorkloadIdentityCredential extends WorkloadIdentityCredential {\n  // Constructor overload with just client id options\n  constructor(options?: DefaultAzureCredentialClientIdOptions);\n  // Constructor overload with just the other default options\n  // Last constructor overload with Union of all options not required since the above two constructor overloads have optional properties\n  constructor(options?: DefaultAzureCredentialOptions) {\n    const managedIdentityClientId =\n      (options as DefaultAzureCredentialClientIdOptions)?.managedIdentityClientId ??\n      process.env.AZURE_CLIENT_ID;\n    const workloadIdentityClientId =\n      (options as DefaultAzureCredentialClientIdOptions)?.workloadIdentityClientId ??\n      managedIdentityClientId;\n    const workloadFile = process.env.AZURE_FEDERATED_TOKEN_FILE;\n    const tenantId = options?.tenantId ?? process.env.AZURE_TENANT_ID;\n    if (workloadFile && workloadIdentityClientId) {\n      const workloadIdentityCredentialOptions: WorkloadIdentityCredentialOptions = {\n        ...options,\n        tenantId,\n        clientId: workloadIdentityClientId,\n        tokenFilePath: workloadFile,\n      };\n      super(workloadIdentityCredentialOptions);\n    } else if (tenantId) {\n      const workloadIdentityClientTenantOptions: WorkloadIdentityCredentialOptions = {\n        ...options,\n        tenantId,\n      };\n      super(workloadIdentityClientTenantOptions);\n    } else {\n      super(options as WorkloadIdentityCredentialOptions);\n    }\n  }\n}\n\nexport class DefaultAzureDeveloperCliCredential extends AzureDeveloperCliCredential {\n  constructor(options?: DefaultAzureCredentialOptions) {\n    super({\n      processTimeoutInMs: options?.processTimeoutInMs,\n      ...options,\n    });\n  }\n}\nexport class DefaultAzureCliCredential extends AzureCliCredential {\n  constructor(options?: DefaultAzureCredentialOptions) {\n    super({\n      processTimeoutInMs: options?.processTimeoutInMs,\n      ...options,\n    });\n  }\n}\n\nexport class DefaultAzurePowershellCredential extends AzurePowerShellCredential {\n  constructor(options?: DefaultAzureCredentialOptions) {\n    super({\n      processTimeoutInMs: options?.processTimeoutInMs,\n      ...options,\n    });\n  }\n}\n\nexport const defaultCredentials: DefaultCredentialConstructor[] = [\n  EnvironmentCredential,\n  DefaultWorkloadIdentityCredential,\n  DefaultManagedIdentityCredential,\n  DefaultAzureCliCredential,\n  DefaultAzurePowershellCredential,\n  DefaultAzureDeveloperCliCredential,\n];\n\n/**\n * Provides a default {@link ChainedTokenCredential} configuration that should\n * work for most applications that use the Azure SDK.\n */\nexport class DefaultAzureCredential extends ChainedTokenCredential {\n  /**\n   * Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialClientIdOptions}\n   *\n   * This credential provides a default {@link ChainedTokenCredential} configuration that should\n   * work for most applications that use the Azure SDK.\n   *\n   * The following credential types will be tried, in order:\n   *\n   * - {@link EnvironmentCredential}\n   * - {@link WorkloadIdentityCredential}\n   * - {@link ManagedIdentityCredential}\n   * - {@link AzureCliCredential}\n   * - {@link AzurePowerShellCredential}\n   * - {@link AzureDeveloperCliCredential}\n   *\n   * Consult the documentation of these credential types for more information\n   * on how they attempt authentication.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialClientIdOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialClientIdOptions);\n\n  /**\n   *  Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialResourceIdOptions}\n   *\n   * This credential provides a default {@link ChainedTokenCredential} configuration that should\n   * work for most applications that use the Azure SDK.\n   *\n   * The following credential types will be tried, in order:\n   *\n   * - {@link EnvironmentCredential}\n   * - {@link WorkloadIdentityCredential}\n   * - {@link ManagedIdentityCredential}\n   * - {@link AzureCliCredential}\n   * - {@link AzurePowerShellCredential}\n   * - {@link AzureDeveloperCliCredential}\n   *\n   * Consult the documentation of these credential types for more information\n   * on how they attempt authentication.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialResourceIdOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialResourceIdOptions);\n\n  /**\n   * Creates an instance of the DefaultAzureCredential class with {@link DefaultAzureCredentialOptions}\n   *\n   * This credential provides a default {@link ChainedTokenCredential} configuration that should\n   * work for most applications that use the Azure SDK.\n   *\n   * The following credential types will be tried, in order:\n   *\n   * - {@link EnvironmentCredential}\n   * - {@link WorkloadIdentityCredential}\n   * - {@link ManagedIdentityCredential}\n   * - {@link AzureCliCredential}\n   * - {@link AzurePowerShellCredential}\n   * - {@link AzureDeveloperCliCredential}\n   *\n   * Consult the documentation of these credential types for more information\n   * on how they attempt authentication.\n   *\n   * @param options - Optional parameters. See {@link DefaultAzureCredentialOptions}.\n   */\n  constructor(options?: DefaultAzureCredentialOptions);\n\n  constructor(\n    options?:\n      | DefaultAzureCredentialOptions\n      | DefaultAzureCredentialResourceIdOptions\n      | DefaultAzureCredentialClientIdOptions\n  ) {\n    super(...defaultCredentials.map((ctor) => new ctor(options)));\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msalNode from \"@azure/msal-node\";\nimport { MsalNode, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { credentialLogger, formatError, formatSuccess } from \"../../util/logging\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { CredentialUnavailableError } from \"../../errors\";\nimport { Socket } from \"net\";\nimport http from \"http\";\nimport { msalToPublic } from \"../utils\";\nimport open from \"open\";\nimport stoppable from \"stoppable\";\n\n/**\n * Options that can be passed to configure MSAL to handle authentication through opening a browser window.\n * @internal\n */\nexport interface MsalOpenBrowserOptions extends MsalNodeOptions {\n  redirectUri: string;\n  loginHint?: string;\n}\n\n/**\n * A call to open(), but mockable\n * @internal\n */\nexport const interactiveBrowserMockable = {\n  open,\n};\n\n/**\n * This MSAL client sets up a web server to listen for redirect callbacks, then calls to the MSAL's public application's `acquireTokenByDeviceCode` during `doGetToken`\n * to trigger the authentication flow, and then respond based on the values obtained from the redirect callback\n * @internal\n */\nexport class MsalOpenBrowser extends MsalNode {\n  private redirectUri: string;\n  private port: number;\n  private hostname: string;\n  private loginHint?: string;\n\n  constructor(options: MsalOpenBrowserOptions) {\n    super(options);\n    this.logger = credentialLogger(\"Node.js MSAL Open Browser\");\n    this.redirectUri = options.redirectUri;\n    this.loginHint = options.loginHint;\n\n    const url = new URL(this.redirectUri);\n    this.port = parseInt(url.port);\n    if (isNaN(this.port)) {\n      this.port = 80;\n    }\n    this.hostname = url.hostname;\n  }\n\n  private async acquireTokenByCode(\n    request: msalNode.AuthorizationCodeRequest,\n    enableCae?: boolean\n  ): Promise<msalNode.AuthenticationResult | null> {\n    return this.getApp(\"public\", enableCae).acquireTokenByCode(request);\n  }\n\n  protected doGetToken(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    return new Promise<AccessToken>((resolve, reject) => {\n      const socketToDestroy: Socket[] = [];\n\n      const requestListener = (req: http.IncomingMessage, res: http.ServerResponse): void => {\n        if (!req.url) {\n          reject(\n            new Error(\n              `Interactive Browser Authentication Error \"Did not receive token with a valid expiration\"`\n            )\n          );\n          return;\n        }\n        let url: URL;\n        try {\n          url = new URL(req.url, this.redirectUri);\n        } catch (e: any) {\n          reject(\n            new Error(\n              `Interactive Browser Authentication Error \"Did not receive token with a valid expiration\"`\n            )\n          );\n          return;\n        }\n        const tokenRequest: msalNode.AuthorizationCodeRequest = {\n          code: url.searchParams.get(\"code\")!,\n          redirectUri: this.redirectUri,\n          scopes: scopes,\n          authority: options?.authority,\n          codeVerifier: this.pkceCodes?.verifier,\n        };\n\n        this.acquireTokenByCode(tokenRequest, options?.enableCae)\n          .then((authResponse) => {\n            if (authResponse?.account) {\n              this.account = msalToPublic(this.clientId, authResponse.account);\n            }\n            const successMessage = `Authentication Complete. You can close the browser and return to the application.`;\n            if (authResponse && authResponse.expiresOn) {\n              const expiresOnTimestamp = authResponse?.expiresOn.valueOf();\n              res.writeHead(200);\n              res.end(successMessage);\n              this.logger.getToken.info(formatSuccess(scopes));\n\n              resolve({\n                expiresOnTimestamp,\n                token: authResponse.accessToken,\n              });\n            } else {\n              const errorMessage = formatError(\n                scopes,\n                `${url.searchParams.get(\"error\")}. ${url.searchParams.get(\"error_description\")}`\n              );\n              res.writeHead(500);\n              res.end(errorMessage);\n              this.logger.getToken.info(errorMessage);\n\n              reject(\n                new Error(\n                  `Interactive Browser Authentication Error \"Did not receive token with a valid expiration\"`\n                )\n              );\n            }\n            cleanup();\n            return;\n          })\n          .catch(() => {\n            const errorMessage = formatError(\n              scopes,\n              `${url.searchParams.get(\"error\")}. ${url.searchParams.get(\"error_description\")}`\n            );\n            res.writeHead(500);\n            res.end(errorMessage);\n            this.logger.getToken.info(errorMessage);\n\n            reject(\n              new Error(\n                `Interactive Browser Authentication Error \"Did not receive token with a valid expiration\"`\n              )\n            );\n            cleanup();\n          });\n      };\n\n      const app = http.createServer(requestListener);\n      const server = stoppable(app);\n\n      const listen = app.listen(this.port, this.hostname, () =>\n        this.logger.info(`InteractiveBrowserCredential listening on port ${this.port}!`)\n      );\n\n      function cleanup(): void {\n        if (listen) {\n          listen.close();\n        }\n\n        for (const socket of socketToDestroy) {\n          socket.destroy();\n        }\n\n        if (server) {\n          server.close();\n          server.stop();\n        }\n      }\n\n      app.on(\"connection\", (socket) => socketToDestroy.push(socket));\n\n      app.on(\"error\", (err) => {\n        cleanup();\n        const code = (err as any).code;\n        if (code === \"EACCES\" || code === \"EADDRINUSE\") {\n          reject(\n            new CredentialUnavailableError(\n              [\n                `InteractiveBrowserCredential: Access denied to port ${this.port}.`,\n                `Try sending a redirect URI with a different port, as follows:`,\n                '`new InteractiveBrowserCredential({ redirectUri: \"http://localhost:1337\" })`',\n              ].join(\" \")\n            )\n          );\n        } else {\n          reject(\n            new CredentialUnavailableError(\n              `InteractiveBrowserCredential: Failed to start the necessary web server. Error: ${err.message}`\n            )\n          );\n        }\n      });\n\n      app.on(\"listening\", () => {\n        const openPromise = this.openAuthCodeUrl(scopes, options);\n\n        const abortSignal = options?.abortSignal;\n        if (abortSignal) {\n          abortSignal.addEventListener(\"abort\", () => {\n            cleanup();\n            reject(new Error(\"Aborted\"));\n          });\n        }\n\n        openPromise.catch((e) => {\n          cleanup();\n          reject(e);\n        });\n      });\n    });\n  }\n\n  private pkceCodes?: {\n    verifier: string;\n    challenge: string;\n  };\n\n  private async openAuthCodeUrl(\n    scopeArray: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<void> {\n    // Initialize CryptoProvider instance\n    const cryptoProvider = new msalNode.CryptoProvider();\n    // Generate PKCE Codes before starting the authorization flow\n    this.pkceCodes = await cryptoProvider.generatePkceCodes();\n\n    const authCodeUrlParameters: msalNode.AuthorizationUrlRequest = {\n      scopes: scopeArray,\n      correlationId: options?.correlationId,\n      redirectUri: this.redirectUri,\n      authority: options?.authority,\n      claims: options?.claims,\n      loginHint: this.loginHint,\n      codeChallenge: this.pkceCodes.challenge,\n      codeChallengeMethod: \"S256\", // Use SHA256 Algorithm\n    };\n    const response = await this.getApp(\"public\", options?.enableCae).getAuthCodeUrl(\n      authCodeUrlParameters\n    );\n    try {\n      // A new instance on macOS only which allows it to not hang, does not fix the issue on linux\n      await interactiveBrowserMockable.open(response, { wait: true, newInstance: true });\n    } catch (e: any) {\n      throw new CredentialUnavailableError(\n        `InteractiveBrowserCredential: Could not open a browser window. Error: ${e.message}`\n      );\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  InteractiveBrowserCredentialInBrowserOptions,\n  InteractiveBrowserCredentialNodeOptions,\n} from \"./interactiveBrowserCredentialOptions\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { AuthenticationRecord } from \"../msal/types\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { MsalOpenBrowser } from \"../msal/nodeFlows/msalOpenBrowser\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"InteractiveBrowserCredential\");\n\n/**\n * Enables authentication to Azure Active Directory inside of the web browser\n * using the interactive login flow.\n */\nexport class InteractiveBrowserCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n  private disableAutomaticAuthentication?: boolean;\n\n  /**\n   * Creates an instance of InteractiveBrowserCredential with the details needed.\n   *\n   * This credential uses the [Authorization Code Flow](https://docs.microsoft.com/azure/active-directory/develop/v2-oauth2-auth-code-flow).\n   * On Node.js, it will open a browser window while it listens for a redirect response from the authentication service.\n   * On browsers, it authenticates via popups. The `loginStyle` optional parameter can be set to `redirect` to authenticate by redirecting the user to an Azure secure login page, which then will redirect the user back to the web application where the authentication started.\n   *\n   * For Node.js, if a `clientId` is provided, the Azure Active Directory application will need to be configured to have a \"Mobile and desktop applications\" redirect endpoint.\n   * Follow our guide on [setting up Redirect URIs for Desktop apps that calls to web APIs](https://docs.microsoft.com/azure/active-directory/develop/scenario-desktop-app-registration#redirect-uris).\n   *\n   * @param options - Options for configuring the client which makes the authentication requests.\n   */\n  constructor(\n    options:\n      | InteractiveBrowserCredentialNodeOptions\n      | InteractiveBrowserCredentialInBrowserOptions = {}\n  ) {\n    const redirectUri =\n      typeof options.redirectUri === \"function\"\n        ? options.redirectUri()\n        : options.redirectUri || \"http://localhost\";\n\n    this.tenantId = options?.tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n\n    this.msalFlow = new MsalOpenBrowser({\n      ...options,\n      tokenCredentialOptions: options,\n      logger,\n      redirectUri,\n    });\n    this.disableAutomaticAuthentication = options?.disableAutomaticAuthentication;\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the user provided the option `disableAutomaticAuthentication`,\n   * once the token can't be retrieved silently,\n   * this method won't attempt to request user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n          logger\n        );\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalFlow.getToken(arrayScopes, {\n          ...newOptions,\n          disableAutomaticAuthentication: this.disableAutomaticAuthentication,\n        });\n      }\n    );\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the token can't be retrieved silently, this method will require user interaction to retrieve the token.\n   *\n   * On Node.js, this credential has [Proof Key for Code Exchange (PKCE)](https://datatracker.ietf.org/doc/html/rfc7636) enabled by default.\n   * PKCE is a security feature that mitigates authentication code interception attacks.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                  TokenCredential implementation might make.\n   */\n  async authenticate(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AuthenticationRecord | undefined> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.authenticate`,\n      options,\n      async (newOptions) => {\n        const arrayScopes = ensureScopes(scopes);\n        await this.msalFlow.getToken(arrayScopes, newOptions);\n        return this.msalFlow.getActiveAccount();\n      }\n    );\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport * as msalNode from \"@azure/msal-node\";\nimport { MsalNode, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { DeviceCodePromptCallback } from \"../../credentials/deviceCodeCredentialOptions\";\n\n/**\n * Options that can be passed to configure MSAL to handle authentication through device codes.\n * @internal\n */\nexport interface MsalDeviceCodeOptions extends MsalNodeOptions {\n  userPromptCallback: DeviceCodePromptCallback;\n}\n\n/**\n * MSAL device code client. Calls to the MSAL's public application's `acquireTokenByDeviceCode` during `doGetToken`.\n * @internal\n */\nexport class MsalDeviceCode extends MsalNode {\n  private userPromptCallback: DeviceCodePromptCallback;\n\n  constructor(options: MsalDeviceCodeOptions) {\n    super(options);\n    this.userPromptCallback = options.userPromptCallback;\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    try {\n      const requestOptions: msalNode.DeviceCodeRequest = {\n        deviceCodeCallback: this.userPromptCallback,\n        scopes,\n        cancel: false,\n        correlationId: options?.correlationId,\n        authority: options?.authority,\n        claims: options?.claims,\n      };\n      const promise = this.getApp(\"public\", options?.enableCae).acquireTokenByDeviceCode(\n        requestOptions\n      );\n      const deviceResponse = await this.withCancellation(promise, options?.abortSignal, () => {\n        requestOptions.cancel = true;\n      });\n      return this.handleResult(scopes, this.clientId, deviceResponse || undefined);\n    } catch (error: any) {\n      throw this.handleError(scopes, error, options);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { DeviceCodeCredentialOptions, DeviceCodeInfo } from \"./deviceCodeCredentialOptions\";\nimport { AuthenticationRecord } from \"../msal/types\";\nimport { MsalDeviceCode } from \"../msal/nodeFlows/msalDeviceCode\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"DeviceCodeCredential\");\n\n/**\n * Method that logs the user code from the DeviceCodeCredential.\n * @param deviceCodeInfo - The device code.\n */\nexport function defaultDeviceCodePromptCallback(deviceCodeInfo: DeviceCodeInfo): void {\n  console.log(deviceCodeInfo.message);\n}\n\n/**\n * Enables authentication to Azure Active Directory using a device code\n * that the user can enter into https://microsoft.com/devicelogin.\n */\nexport class DeviceCodeCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n  private disableAutomaticAuthentication?: boolean;\n\n  /**\n   * Creates an instance of DeviceCodeCredential with the details needed\n   * to initiate the device code authorization flow with Azure Active Directory.\n   *\n   * A message will be logged, giving users a code that they can use to authenticate once they go to https://microsoft.com/devicelogin\n   *\n   * Developers can configure how this message is shown by passing a custom `userPromptCallback`:\n   *\n   * ```js\n   * const credential = new DeviceCodeCredential({\n   *   tenantId: env.AZURE_TENANT_ID,\n   *   clientId: env.AZURE_CLIENT_ID,\n   *   userPromptCallback: (info) => {\n   *     console.log(\"CUSTOMIZED PROMPT CALLBACK\", info.message);\n   *   }\n   * });\n   * ```\n   *\n   * @param options - Options for configuring the client which makes the authentication requests.\n   */\n  constructor(options?: DeviceCodeCredentialOptions) {\n    this.tenantId = options?.tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n    this.msalFlow = new MsalDeviceCode({\n      ...options,\n      logger,\n      userPromptCallback: options?.userPromptCallback || defaultDeviceCodePromptCallback,\n      tokenCredentialOptions: options || {},\n    });\n    this.disableAutomaticAuthentication = options?.disableAutomaticAuthentication;\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the user provided the option `disableAutomaticAuthentication`,\n   * once the token can't be retrieved silently,\n   * this method won't attempt to request user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        newOptions.tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n          logger\n        );\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalFlow.getToken(arrayScopes, {\n          ...newOptions,\n          disableAutomaticAuthentication: this.disableAutomaticAuthentication,\n        });\n      }\n    );\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the token can't be retrieved silently, this method will require user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                  TokenCredential implementation might make.\n   */\n  async authenticate(\n    scopes: string | string[],\n    options: GetTokenOptions = {}\n  ): Promise<AuthenticationRecord | undefined> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.authenticate`,\n      options,\n      async (newOptions) => {\n        const arrayScopes = Array.isArray(scopes) ? scopes : [scopes];\n        await this.msalFlow.getToken(arrayScopes, newOptions);\n        return this.msalFlow.getActiveAccount();\n      }\n    );\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { MsalN<PERSON>, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { credentialLogger } from \"../../util/logging\";\n\n/**\n * Options that can be passed to configure MSAL to handle authentication through opening a browser window.\n * @internal\n */\nexport interface MsalAuthorizationCodeOptions extends MsalNodeOptions {\n  redirectUri: string;\n  authorizationCode: string;\n  clientSecret?: string;\n}\n\n/**\n * This MSAL client sets up a web server to listen for redirect callbacks, then calls to the MSAL's public application's `acquireTokenByDeviceCode` during `doGetToken`\n * to trigger the authentication flow, and then respond based on the values obtained from the redirect callback\n * @internal\n */\nexport class MsalAuthorizationCode extends MsalNode {\n  private redirectUri: string;\n  private authorizationCode: string;\n\n  constructor(options: MsalAuthorizationCodeOptions) {\n    super(options);\n    this.logger = credentialLogger(\"Node.js MSAL Authorization Code\");\n    this.redirectUri = options.redirectUri;\n    this.authorizationCode = options.authorizationCode;\n    if (options.clientSecret) {\n      this.msalConfig.auth.clientSecret = options.clientSecret;\n    }\n  }\n\n  async getAuthCodeUrl(options: {\n    scopes: string[];\n    redirectUri: string;\n    enableCae?: boolean;\n  }): Promise<string> {\n    await this.init();\n    return this.getApp(\"confidentialFirst\", options.enableCae).getAuthCodeUrl({\n      scopes: options.scopes,\n      redirectUri: options.redirectUri,\n    });\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options?: CredentialFlowGetTokenOptions\n  ): Promise<AccessToken> {\n    try {\n      const result = await this.getApp(\"confidentialFirst\", options?.enableCae).acquireTokenByCode({\n        scopes,\n        redirectUri: this.redirectUri,\n        code: this.authorizationCode,\n        correlationId: options?.correlationId,\n        authority: options?.authority,\n        claims: options?.claims,\n      });\n      // The Client Credential flow does not return an account,\n      // so each time getToken gets called, we will have to acquire a new token through the service.\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { AuthorizationCodeCredentialOptions } from \"./authorizationCodeCredentialOptions\";\nimport { MsalAuthorizationCode } from \"../msal/nodeFlows/msalAuthorizationCode\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { checkTenantId } from \"../util/tenantIdUtils\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst logger = credentialLogger(\"AuthorizationCodeCredential\");\n\n/**\n * Enables authentication to Azure Active Directory using an authorization code\n * that was obtained through the authorization code flow, described in more detail\n * in the Azure Active Directory documentation:\n *\n * https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-auth-code-flow\n */\nexport class AuthorizationCodeCredential implements TokenCredential {\n  private msalFlow: MsalFlow;\n  private disableAutomaticAuthentication?: boolean;\n  private authorizationCode: string;\n  private redirectUri: string;\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n\n  /**\n   * Creates an instance of AuthorizationCodeCredential with the details needed\n   * to request an access token using an authentication that was obtained\n   * from Azure Active Directory.\n   *\n   * It is currently necessary for the user of this credential to initiate\n   * the authorization code flow to obtain an authorization code to be used\n   * with this credential.  A full example of this flow is provided here:\n   *\n   * https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/identity/identity/samples/v2/manual/authorizationCodeSample.ts\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID or name.\n   *                 'common' may be used when dealing with multi-tenant scenarios.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param clientSecret - A client secret that was generated for the App Registration\n   * @param authorizationCode - An authorization code that was received from following the\n                              authorization code flow.  This authorization code must not\n                              have already been used to obtain an access token.\n   * @param redirectUri - The redirect URI that was used to request the authorization code.\n                        Must be the same URI that is configured for the App Registration.\n   * @param options - Options for configuring the client which makes the access token request.\n   */\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    clientSecret: string,\n    authorizationCode: string,\n    redirectUri: string,\n    options?: AuthorizationCodeCredentialOptions\n  );\n  /**\n   * Creates an instance of AuthorizationCodeCredential with the details needed\n   * to request an access token using an authentication that was obtained\n   * from Azure Active Directory.\n   *\n   * It is currently necessary for the user of this credential to initiate\n   * the authorization code flow to obtain an authorization code to be used\n   * with this credential.  A full example of this flow is provided here:\n   *\n   * https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/identity/identity/samples/v2/manual/authorizationCodeSample.ts\n   *\n   * @param tenantId - The Azure Active Directory tenant (directory) ID or name.\n   *                 'common' may be used when dealing with multi-tenant scenarios.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param authorizationCode - An authorization code that was received from following the\n                              authorization code flow.  This authorization code must not\n                              have already been used to obtain an access token.\n   * @param redirectUri - The redirect URI that was used to request the authorization code.\n                        Must be the same URI that is configured for the App Registration.\n   * @param options - Options for configuring the client which makes the access token request.\n   */\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    authorizationCode: string,\n    redirectUri: string,\n    options?: AuthorizationCodeCredentialOptions\n  );\n  /**\n   * @hidden\n   * @internal\n   */\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    clientSecretOrAuthorizationCode: string,\n    authorizationCodeOrRedirectUri: string,\n    redirectUriOrOptions: string | AuthorizationCodeCredentialOptions | undefined,\n    options?: AuthorizationCodeCredentialOptions\n  ) {\n    checkTenantId(logger, tenantId);\n    let clientSecret: string | undefined = clientSecretOrAuthorizationCode;\n\n    if (typeof redirectUriOrOptions === \"string\") {\n      // the clientId+clientSecret constructor\n      this.authorizationCode = authorizationCodeOrRedirectUri;\n      this.redirectUri = redirectUriOrOptions;\n      // in this case, options are good as they come\n    } else {\n      // clientId only\n      this.authorizationCode = clientSecretOrAuthorizationCode;\n      this.redirectUri = authorizationCodeOrRedirectUri as string;\n      clientSecret = undefined;\n      options = redirectUriOrOptions as AuthorizationCodeCredentialOptions;\n    }\n\n    // TODO: Validate tenant if provided\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants\n    );\n\n    this.msalFlow = new MsalAuthorizationCode({\n      ...options,\n      clientSecret,\n      clientId,\n      tenantId,\n      tokenCredentialOptions: options || {},\n      logger,\n      redirectUri: this.redirectUri,\n      authorizationCode: this.authorizationCode,\n    });\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        const tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds\n        );\n        newOptions.tenantId = tenantId;\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalFlow.getToken(arrayScopes, {\n          ...newOptions,\n          disableAutomaticAuthentication: this.disableAutomaticAuthentication,\n        });\n      }\n    );\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { MsalNode, MsalNodeOptions } from \"./msalNodeCommon\";\nimport { AccessToken } from \"@azure/core-auth\";\nimport { CredentialFlowGetTokenOptions } from \"../credentials\";\nimport { formatError } from \"../../util/logging\";\nimport { parseCertificate } from \"./msalClientCertificate\";\n\n/**\n * Options that can be passed to configure MSAL to handle On-Behalf-Of authentication requests.\n * @internal\n */\nexport interface MsalOnBehalfOfOptions extends MsalNodeOptions {\n  /**\n   * A client secret that was generated for the App Registration.\n   */\n  clientSecret?: string;\n  /**\n   * Location of the PEM certificate.\n   */\n  certificatePath?: string;\n  /**\n   * Option to include x5c header for SubjectName and Issuer name authorization.\n   * Set this option to send base64 encoded public certificate in the client assertion header as an x5c claim\n   */\n  sendCertificateChain?: boolean;\n  /**\n   * The user assertion for the On-Behalf-Of flow.\n   */\n  userAssertionToken: string;\n}\n\n/**\n * MSAL on behalf of flow. Calls to MSAL's confidential application's `acquireTokenOnBehalfOf` during `doGetToken`.\n * @internal\n */\nexport class MsalOnBehalfOf extends MsalNode {\n  private userAssertionToken: string;\n  private certificatePath?: string;\n  private sendCertificateChain?: boolean;\n  private clientSecret?: string;\n\n  constructor(options: MsalOnBehalfOfOptions) {\n    super(options);\n    this.logger.info(\"Initialized MSAL's On-Behalf-Of flow\");\n    this.requiresConfidential = true;\n    this.userAssertionToken = options.userAssertionToken;\n    this.certificatePath = options.certificatePath;\n    this.sendCertificateChain = options.sendCertificateChain;\n    this.clientSecret = options.clientSecret;\n  }\n\n  // Changing the MSAL configuration asynchronously\n  async init(options?: CredentialFlowGetTokenOptions): Promise<void> {\n    if (this.certificatePath) {\n      try {\n        const parts = await parseCertificate(\n          { certificatePath: this.certificatePath },\n          this.sendCertificateChain\n        );\n        this.msalConfig.auth.clientCertificate = {\n          thumbprint: parts.thumbprint,\n          privateKey: parts.certificateContents,\n          x5c: parts.x5c,\n        };\n      } catch (error: any) {\n        this.logger.info(formatError(\"\", error));\n        throw error;\n      }\n    } else {\n      this.msalConfig.auth.clientSecret = this.clientSecret;\n    }\n    return super.init(options);\n  }\n\n  protected async doGetToken(\n    scopes: string[],\n    options: CredentialFlowGetTokenOptions = {}\n  ): Promise<AccessToken> {\n    try {\n      const result = await this.getApp(\"confidential\", options.enableCae).acquireTokenOnBehalfOf({\n        scopes,\n        correlationId: options.correlationId,\n        authority: options.authority,\n        claims: options.claims,\n        oboAssertion: this.userAssertionToken,\n      });\n      return this.handleResult(scopes, this.clientId, result || undefined);\n    } catch (err: any) {\n      throw this.handleError(scopes, err, options);\n    }\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  OnBehalfOfCredentialCertificateOptions,\n  OnBehalfOfCredentialOptions,\n  OnBehalfOfCredentialSecretOptions,\n} from \"./onBehalfOfCredentialOptions\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils\";\nimport { CredentialPersistenceOptions } from \"./credentialPersistenceOptions\";\nimport { MsalFlow } from \"../msal/flows\";\nimport { MsalOnBehalfOf } from \"../msal/nodeFlows/msalOnBehalfOf\";\nimport { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions\";\nimport { credentialLogger } from \"../util/logging\";\nimport { ensureScopes } from \"../util/scopeUtils\";\nimport { tracingClient } from \"../util/tracing\";\n\nconst credentialName = \"OnBehalfOfCredential\";\nconst logger = credentialLogger(credentialName);\n\n/**\n * Enables authentication to Microsoft Entra ID using the [On Behalf Of flow](https://learn.microsoft.com/azure/active-directory/develop/v2-oauth2-on-behalf-of-flow).\n */\nexport class OnBehalfOfCredential implements TokenCredential {\n  private tenantId: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalFlow: MsalFlow;\n  /**\n   * Creates an instance of the {@link OnBehalfOfCredential} with the details\n   * needed to authenticate against Azure Active Directory with path to a PEM certificate,\n   * and an user assertion.\n   *\n   * Example using the `KeyClient` from [\\@azure/keyvault-keys](https://www.npmjs.com/package/\\@azure/keyvault-keys):\n   *\n   * ```ts\n   * const tokenCredential = new OnBehalfOfCredential({\n   *   tenantId,\n   *   clientId,\n   *   certificatePath: \"/path/to/certificate.pem\",\n   *   userAssertionToken: \"access-token\"\n   * });\n   * const client = new KeyClient(\"vault-url\", tokenCredential);\n   *\n   * await client.getKey(\"key-name\");\n   * ```\n   *\n   * @param options - Optional parameters, generally common across credentials.\n   */\n  constructor(\n    options: OnBehalfOfCredentialCertificateOptions &\n      MultiTenantTokenCredentialOptions &\n      CredentialPersistenceOptions\n  );\n  /**\n   * Creates an instance of the {@link OnBehalfOfCredential} with the details\n   * needed to authenticate against Azure Active Directory with a client\n   * secret and an user assertion.\n   *\n   * Example using the `KeyClient` from [\\@azure/keyvault-keys](https://www.npmjs.com/package/\\@azure/keyvault-keys):\n   *\n   * ```ts\n   * const tokenCredential = new OnBehalfOfCredential({\n   *   tenantId,\n   *   clientId,\n   *   clientSecret,\n   *   userAssertionToken: \"access-token\"\n   * });\n   * const client = new KeyClient(\"vault-url\", tokenCredential);\n   *\n   * await client.getKey(\"key-name\");\n   * ```\n   *\n   * @param options - Optional parameters, generally common across credentials.\n   */\n  constructor(\n    options: OnBehalfOfCredentialSecretOptions &\n      MultiTenantTokenCredentialOptions &\n      CredentialPersistenceOptions\n  );\n\n  constructor(private options: OnBehalfOfCredentialOptions) {\n    const { clientSecret } = options as OnBehalfOfCredentialSecretOptions;\n    const { certificatePath } = options as OnBehalfOfCredentialCertificateOptions;\n    const {\n      tenantId,\n      clientId,\n      userAssertionToken,\n      additionallyAllowedTenants: additionallyAllowedTenantIds,\n    } = options;\n    if (!tenantId || !clientId || !(clientSecret || certificatePath) || !userAssertionToken) {\n      throw new Error(\n        `${credentialName}: tenantId, clientId, clientSecret (or certificatePath) and userAssertionToken are required parameters.`\n      );\n    }\n\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      additionallyAllowedTenantIds\n    );\n\n    this.msalFlow = new MsalOnBehalfOf({\n      ...this.options,\n      logger,\n      tokenCredentialOptions: this.options,\n    });\n  }\n\n  /**\n   * Authenticates with Azure Active Directory and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure the underlying network requests.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(`${credentialName}.getToken`, options, async (newOptions) => {\n      newOptions.tenantId = processMultiTenantRequest(\n        this.tenantId,\n        newOptions,\n        this.additionallyAllowedTenantIds,\n        logger\n      );\n\n      const arrayScopes = ensureScopes(scopes);\n      return this.msalFlow!.getToken(arrayScopes, newOptions);\n    });\n  }\n}\n", "// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nexport * from \"./plugins/consumer\";\n\nexport { IdentityPlugin } from \"./plugins/provider\";\n\nimport { TokenCredential } from \"@azure/core-auth\";\nimport { DefaultAzureCredential } from \"./credentials/defaultAzureCredential\";\n\nexport {\n  AuthenticationError,\n  ErrorResponse,\n  AggregateAuthenticationError,\n  AuthenticationErrorName,\n  AggregateAuthenticationErrorName,\n  CredentialUnavailableError,\n  CredentialUnavailableErrorName,\n  AuthenticationRequiredError,\n  AuthenticationRequiredErrorOptions,\n} from \"./errors\";\n\nexport { AuthenticationRecord } from \"./msal/types\";\nexport { serializeAuthenticationRecord, deserializeAuthenticationRecord } from \"./msal/utils\";\nexport { TokenCredentialOptions } from \"./tokenCredentialOptions\";\nexport { MultiTenantTokenCredentialOptions } from \"./credentials/multiTenantTokenCredentialOptions\";\nexport { AuthorityValidationOptions } from \"./credentials/authorityValidationOptions\";\n// TODO: Export again once we're ready to release this feature.\n// export { RegionalAuthority } from \"./regionalAuthority\";\n\nexport { InteractiveCredentialOptions } from \"./credentials/interactiveCredentialOptions\";\n\nexport { ChainedTokenCredential } from \"./credentials/chainedTokenCredential\";\n\nexport { ClientSecretCredential } from \"./credentials/clientSecretCredential\";\nexport { ClientSecretCredentialOptions } from \"./credentials/clientSecretCredentialOptions\";\n\nexport { DefaultAzureCredential } from \"./credentials/defaultAzureCredential\";\nexport {\n  DefaultAzureCredentialOptions,\n  DefaultAzureCredentialClientIdOptions,\n  DefaultAzureCredentialResourceIdOptions,\n} from \"./credentials/defaultAzureCredentialOptions\";\n\nexport { EnvironmentCredential } from \"./credentials/environmentCredential\";\nexport { EnvironmentCredentialOptions } from \"./credentials/environmentCredentialOptions\";\n\nexport {\n  ClientCertificateCredential,\n  ClientCertificateCredentialPEMConfiguration,\n  ClientCertificatePEMCertificatePath,\n  ClientCertificatePEMCertificate,\n} from \"./credentials/clientCertificateCredential\";\nexport { ClientCertificateCredentialOptions } from \"./credentials/clientCertificateCredentialOptions\";\nexport { ClientAssertionCredential } from \"./credentials/clientAssertionCredential\";\nexport { ClientAssertionCredentialOptions } from \"./credentials/clientAssertionCredentialOptions\";\nexport { CredentialPersistenceOptions } from \"./credentials/credentialPersistenceOptions\";\nexport { AzureCliCredential } from \"./credentials/azureCliCredential\";\nexport { AzureCliCredentialOptions } from \"./credentials/azureCliCredentialOptions\";\nexport { AzureDeveloperCliCredential } from \"./credentials/azureDeveloperCliCredential\";\nexport { AzureDeveloperCliCredentialOptions } from \"./credentials/azureDeveloperCliCredentialOptions\";\nexport { InteractiveBrowserCredential } from \"./credentials/interactiveBrowserCredential\";\nexport {\n  InteractiveBrowserCredentialNodeOptions,\n  InteractiveBrowserCredentialInBrowserOptions,\n  BrowserLoginStyle,\n} from \"./credentials/interactiveBrowserCredentialOptions\";\nexport {\n  ManagedIdentityCredential,\n  ManagedIdentityCredentialClientIdOptions,\n  ManagedIdentityCredentialResourceIdOptions,\n} from \"./credentials/managedIdentityCredential\";\nexport { DeviceCodeCredential } from \"./credentials/deviceCodeCredential\";\nexport {\n  DeviceCodePromptCallback,\n  DeviceCodeInfo,\n} from \"./credentials/deviceCodeCredentialOptions\";\nexport { DeviceCodeCredentialOptions } from \"./credentials/deviceCodeCredentialOptions\";\n\nexport { AuthorizationCodeCredential } from \"./credentials/authorizationCodeCredential\";\nexport { AuthorizationCodeCredentialOptions } from \"./credentials/authorizationCodeCredentialOptions\";\nexport { AzurePowerShellCredential } from \"./credentials/azurePowerShellCredential\";\nexport { AzurePowerShellCredentialOptions } from \"./credentials/azurePowerShellCredentialOptions\";\nexport {\n  OnBehalfOfCredentialOptions,\n  OnBehalfOfCredentialSecretOptions,\n  OnBehalfOfCredentialCertificateOptions,\n} from \"./credentials/onBehalfOfCredentialOptions\";\nexport { UsernamePasswordCredential } from \"./credentials/usernamePasswordCredential\";\nexport { UsernamePasswordCredentialOptions } from \"./credentials/usernamePasswordCredentialOptions\";\nexport { VisualStudioCodeCredential } from \"./credentials/visualStudioCodeCredential\";\nexport { VisualStudioCodeCredentialOptions } from \"./credentials/visualStudioCodeCredentialOptions\";\nexport { OnBehalfOfCredential } from \"./credentials/onBehalfOfCredential\";\nexport { WorkloadIdentityCredential } from \"./credentials/workloadIdentityCredential\";\nexport { WorkloadIdentityCredentialOptions } from \"./credentials/workloadIdentityCredentialOptions\";\n\nexport { TokenCachePersistenceOptions } from \"./msal/nodeFlows/tokenCachePersistenceOptions\";\n\nexport { TokenCredential, GetTokenOptions, AccessToken } from \"@azure/core-auth\";\nexport { logger } from \"./util/logging\";\n\nexport { AzureAuthorityHosts } from \"./constants\";\n\n/**\n * Returns a new instance of the {@link DefaultAzureCredential}.\n */\nexport function getDefaultAzureCredential(): TokenCredential {\n  return new DefaultAzureCredential();\n}\n"], "names": ["logger", "createClientLogger", "AzureAuthorityHosts", "isNode", "msal<PERSON><PERSON>mon", "randomUUID", "AbortError", "createTracingClient", "ServiceClient", "createPipelineRequest", "createHttpHeaders", "AbortController", "getLogLevel", "msalNode", "os", "path", "fs", "msiName", "prepareRequestOptions", "isError", "delay", "readFileAsync", "readFile", "credentialName", "https", "ConfidentialClientApplication", "child_process", "childProcess", "promisify", "createHash", "createPrivateKey", "open", "http", "stoppable"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAyDA,SAAS,eAAe,CAAC,aAAkB,EAAA;AACzC,IAAA,QACE,aAAa;AACb,QAAA,OAAO,aAAa,CAAC,KAAK,KAAK,QAAQ;AACvC,QAAA,OAAO,aAAa,CAAC,iBAAiB,KAAK,QAAQ,EACnD;AACJ,CAAC;AAED;;AAEG;AACI,MAAM,8BAA8B,GAAG,6BAA6B;AAE3E;;;;AAIG;AACG,MAAO,0BAA2B,SAAQ,KAAK,CAAA;AACnD,IAAA,WAAA,CAAY,OAAgB,EAAA;QAC1B,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,IAAI,GAAG,8BAA8B,CAAC;KAC5C;AACF,CAAA;AAED;;AAEG;AACI,MAAM,uBAAuB,GAAG,sBAAsB;AAE7D;;;;AAIG;AACG,MAAO,mBAAoB,SAAQ,KAAK,CAAA;;IAY5C,WAAY,CAAA,UAAkB,EAAE,SAA6C,EAAA;AAC3E,QAAA,IAAI,aAAa,GAAkB;AACjC,YAAA,KAAK,EAAE,SAAS;AAChB,YAAA,gBAAgB,EAAE,oEAAoE;SACvF,CAAC;AAEF,QAAA,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE;AAC9B,YAAA,aAAa,GAAG,wCAAwC,CAAC,SAAS,CAAC,CAAC;AACrE,SAAA;AAAM,aAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACxC,IAAI;;;gBAGF,MAAM,kBAAkB,GAAuB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AACrE,gBAAA,aAAa,GAAG,wCAAwC,CAAC,kBAAkB,CAAC,CAAC;AAC9E,aAAA;AAAC,YAAA,OAAO,CAAM,EAAE;gBACf,IAAI,UAAU,KAAK,GAAG,EAAE;AACtB,oBAAA,aAAa,GAAG;AACd,wBAAA,KAAK,EAAE,qBAAqB;AAC5B,wBAAA,gBAAgB,EAAE,4CAA4C;qBAC/D,CAAC;AACH,iBAAA;AAAM,qBAAA;AACL,oBAAA,aAAa,GAAG;AACd,wBAAA,KAAK,EAAE,eAAe;wBACtB,gBAAgB,EAAE,CAAoD,iDAAA,EAAA,SAAS,CAAE,CAAA;qBAClF,CAAC;AACH,iBAAA;AACF,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,aAAa,GAAG;AACd,gBAAA,KAAK,EAAE,eAAe;AACtB,gBAAA,gBAAgB,EAAE,oEAAoE;aACvF,CAAC;AACH,SAAA;AAED,QAAA,KAAK,CACH,CAAA,EAAG,aAAa,CAAC,KAAK,CAAA,cAAA,EAAiB,UAAU,CAAA,iBAAA,EAAoB,aAAa,CAAC,gBAAgB,CAAA,CAAE,CACtG,CAAC;AACF,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;;AAGnC,QAAA,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;KACrC;AACF,CAAA;AAED;;AAEG;AACI,MAAM,gCAAgC,GAAG,+BAA+B;AAE/E;;;AAGG;AACG,MAAO,4BAA6B,SAAQ,KAAK,CAAA;IAOrD,WAAY,CAAA,MAAa,EAAE,YAAqB,EAAA;QAC9C,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtC,QAAA,KAAK,CAAC,CAAG,EAAA,YAAY,KAAK,WAAW,CAAA,CAAE,CAAC,CAAC;AACzC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;;AAGrB,QAAA,IAAI,CAAC,IAAI,GAAG,gCAAgC,CAAC;KAC9C;AACF,CAAA;AAED,SAAS,wCAAwC,CAAC,SAA6B,EAAA;IAC7E,OAAO;QACL,KAAK,EAAE,SAAS,CAAC,KAAK;QACtB,gBAAgB,EAAE,SAAS,CAAC,iBAAiB;QAC7C,aAAa,EAAE,SAAS,CAAC,cAAc;QACvC,UAAU,EAAE,SAAS,CAAC,WAAW;QACjC,SAAS,EAAE,SAAS,CAAC,SAAS;QAC9B,OAAO,EAAE,SAAS,CAAC,QAAQ;KAC5B,CAAC;AACJ,CAAC;AAoBD;;AAEG;AACG,MAAO,2BAA4B,SAAQ,KAAK,CAAA;AAUpD,IAAA,WAAA;AACE;;AAEG;IACH,OAA2C,EAAA;AAE3C,QAAA,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAC7B,QAAA,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AAC/C,QAAA,IAAI,CAAC,IAAI,GAAG,6BAA6B,CAAC;KAC3C;AACF;;ACrOD;AAKA;;AAEG;MACUA,QAAM,GAAGC,2BAAkB,CAAC,UAAU,EAAE;AAOrD;;;AAGG;AACG,SAAU,cAAc,CAAC,gBAA0B,EAAA;IACvD,OAAO,gBAAgB,CAAC,MAAM,CAC5B,CAAC,GAA2B,EAAE,WAAmB,KAAI;AACnD,QAAA,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;AAC5B,YAAA,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAChC,SAAA;AAAM,aAAA;AACL,YAAA,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/B,SAAA;AACD,QAAA,OAAO,GAAG,CAAC;KACZ,EACD,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAC9B,CAAC;AACJ,CAAC;AAeD;;AAEG;AACG,SAAU,aAAa,CAAC,KAAwB,EAAA;IACpD,OAAO,CAAA,iBAAA,EAAoB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA,CAAA,CAAG,CAAC;AAChF,CAAC;AAED;;AAEG;AACa,SAAA,WAAW,CAAC,KAAoC,EAAE,KAAqB,EAAA;IACrF,IAAI,OAAO,GAAG,QAAQ,CAAC;AACvB,IAAA,IAAI,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,MAAM,EAAE;QACjB,OAAO,IAAI,YAAY,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA,CAAA,CAAG,CAAC;AAC3E,KAAA;AACD,IAAA,OAAO,GAAG,OAAO,CAAA,gBAAA,EAAmB,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC;AAC3F,CAAC;AAoBD;;;;;;;AAOG;AACG,SAAU,wBAAwB,CACtC,KAAa,EACb,MAAiC,EACjC,MAAmBD,QAAM,EAAA;AAEzB,IAAA,MAAM,SAAS,GAAG,MAAM,GAAG,CAAG,EAAA,MAAM,CAAC,SAAS,IAAI,KAAK,CAAA,CAAE,GAAG,KAAK,CAAC;IAElE,SAAS,IAAI,CAAC,OAAe,EAAA;QAC3B,GAAG,CAAC,IAAI,CAAC,CAAA,EAAG,SAAS,CAAK,GAAA,CAAA,EAAE,OAAO,CAAC,CAAC;KACtC;IAED,SAAS,OAAO,CAAC,OAAe,EAAA;QAC9B,GAAG,CAAC,OAAO,CAAC,CAAA,EAAG,SAAS,CAAK,GAAA,CAAA,EAAE,OAAO,CAAC,CAAC;KACzC;IAED,SAAS,OAAO,CAAC,OAAe,EAAA;QAC9B,GAAG,CAAC,OAAO,CAAC,CAAA,EAAG,SAAS,CAAK,GAAA,CAAA,EAAE,OAAO,CAAC,CAAC;KACzC;IACD,OAAO;QACL,KAAK;QACL,SAAS;QACT,IAAI;QACJ,OAAO;QACP,OAAO;KACR,CAAC;AACJ,CAAC;AAWD;;;;;;;;;AASG;SACa,gBAAgB,CAAC,KAAa,EAAE,MAAmBA,QAAM,EAAA;IACvE,MAAM,UAAU,GAAG,wBAAwB,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;AACnE,IAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,UAAU,CACb,EAAA,EAAA,MAAM,EAAE,GAAG,EACX,QAAQ,EAAE,wBAAwB,CAAC,eAAe,EAAE,UAAU,EAAE,GAAG,CAAC,EACpE,CAAA,CAAA;AACJ;;AC/IA;AACA;AAEA;;AAEG;AAEI,MAAM,WAAW,GAAG,OAAO,CAAC;AAEnC;;;AAGG;AACH;AACA;AACA;AACO,MAAM,uBAAuB,GAAG,sCAAsC,CAAC;AAE9E;;;AAGG;AACI,MAAM,eAAe,GAAG,QAAQ,CAAC;AAExC;;AAEG;AACSE,qCAiBX;AAjBD,CAAA,UAAY,mBAAmB,EAAA;AAC7B;;AAEG;AACH,IAAA,mBAAA,CAAA,YAAA,CAAA,GAAA,gCAA6C,CAAA;AAC7C;;AAEG;AACH,IAAA,mBAAA,CAAA,cAAA,CAAA,GAAA,kCAAiD,CAAA;AACjD;;AAEG;AACH,IAAA,mBAAA,CAAA,iBAAA,CAAA,GAAA,kCAAoD,CAAA;AACpD;;AAEG;AACH,IAAA,mBAAA,CAAA,kBAAA,CAAA,GAAA,mCAAsD,CAAA;AACxD,CAAC,EAjBWA,2BAAmB,KAAnBA,2BAAmB,GAiB9B,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;AACI,MAAM,oBAAoB,GAAGA,2BAAmB,CAAC,gBAAgB,CAAC;AAEzE;;AAEG;AACI,MAAM,WAAW,GAAa,CAAC,GAAG,CAAC,CAAC;AAEpC,MAAM,gBAAgB,GAAG,MAAM,CAAC;AAChC,MAAM,oBAAoB,GAAG,QAAQ;;ACzD5C;AAmBA;;;AAGG;AACH,MAAM,iCAAiC,GAAG,KAAK,CAAC;AAEhD;;;AAGG;AACG,SAAU,oBAAoB,CAClC,MAAyB,EACzB,MAAwB,EACxB,SAAqB,EACrB,eAAiC,EAAA;AAEjC,IAAA,MAAM,KAAK,GAAG,CAAC,OAAe,KAAW;AACvC,QAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,OAAO,IAAI,2BAA2B,CAAC;AACrC,YAAA,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC;YACjD,eAAe;YACf,OAAO;AACR,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;IACF,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;AAC5B,KAAA;AACD,IAAA,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AACxB,QAAA,MAAM,KAAK,CAAC,CAAuC,qCAAA,CAAA,CAAC,CAAC;AACtD,KAAA;AACD,IAAA,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AAC1B,QAAA,MAAM,KAAK,CAAC,CAAyC,uCAAA,CAAA,CAAC,CAAC;AACxD,KAAA;AACH,CAAC;AAED;;;AAGG;AACa,SAAA,YAAY,CAAC,QAAgB,EAAE,IAAa,EAAA;IAC1D,IAAI,CAAC,IAAI,EAAE;QACT,IAAI,GAAG,oBAAoB,CAAC;AAC7B,KAAA;AACD,IAAA,IAAI,IAAI,MAAM,CAAC,CAAA,EAAG,QAAQ,CAAA,GAAA,CAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC3C,QAAA,OAAO,IAAI,CAAC;AACb,KAAA;AACD,IAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtB,OAAO,IAAI,GAAG,QAAQ,CAAC;AACxB,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,CAAG,EAAA,IAAI,CAAI,CAAA,EAAA,QAAQ,EAAE,CAAC;AAC9B,KAAA;AACH,CAAC;AAED;;;;;;AAMG;SACa,mBAAmB,CACjC,QAAgB,EAChB,aAAqB,EACrB,wBAAkC,EAAA;IAElC,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,aAAa,KAAK,wBAAwB,EAAE;QACtE,OAAO,CAAC,aAAa,CAAC,CAAC;AACxB,KAAA;AACD,IAAA,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;AAIG;AACI,MAAM,qBAAqB,GAIhC,CAAC,MAAwB,EAAE,QAA+B,GAAAC,eAAM,GAAG,MAAM,GAAG,SAAS,KACrF,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,KAAU;AACpC,IAAA,IAAI,WAAW,EAAE;QACf,OAAO;AACR,KAAA;AACD,IAAA,QAAQ,KAAK;AACX,QAAA,KAAKC,qBAAU,CAAC,QAAQ,CAAC,KAAK;YAC5B,MAAM,CAAC,IAAI,CAAC,CAAA,KAAA,EAAQ,QAAQ,CAAc,WAAA,EAAA,OAAO,CAAE,CAAA,CAAC,CAAC;YACrD,OAAO;AACT,QAAA,KAAKA,qBAAU,CAAC,QAAQ,CAAC,IAAI;YAC3B,MAAM,CAAC,IAAI,CAAC,CAAA,KAAA,EAAQ,QAAQ,CAAqB,kBAAA,EAAA,OAAO,CAAE,CAAA,CAAC,CAAC;YAC5D,OAAO;AACT,QAAA,KAAKA,qBAAU,CAAC,QAAQ,CAAC,OAAO;YAC9B,MAAM,CAAC,IAAI,CAAC,CAAA,KAAA,EAAQ,QAAQ,CAAwB,qBAAA,EAAA,OAAO,CAAE,CAAA,CAAC,CAAC;YAC/D,OAAO;AACT,QAAA,KAAKA,qBAAU,CAAC,QAAQ,CAAC,OAAO;YAC9B,MAAM,CAAC,IAAI,CAAC,CAAA,KAAA,EAAQ,QAAQ,CAAgB,aAAA,EAAA,OAAO,CAAE,CAAA,CAAC,CAAC;YACvD,OAAO;AACV,KAAA;AACH,CAAC,CAAC;AAEJ;;AAEG;AACG,SAAU,eAAe,CAAC,QAAmC,EAAA;AACjE,IAAA,QAAQ,QAAQ;AACd,QAAA,KAAK,OAAO;AACV,YAAA,OAAOA,qBAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;AACnC,QAAA,KAAK,MAAM;AACT,YAAA,OAAOA,qBAAU,CAAC,QAAQ,CAAC,IAAI,CAAC;AAClC,QAAA,KAAK,SAAS;AACZ,YAAA,OAAOA,qBAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;AACrC,QAAA,KAAK,SAAS;AACZ,YAAA,OAAOA,qBAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;AACrC,QAAA;;AAEE,YAAA,OAAOA,qBAAU,CAAC,QAAQ,CAAC,IAAI,CAAC;AACnC,KAAA;AACH,CAAC;AAED;;;;;;;AAOG;MACU,iBAAiB,CAAA;AAI5B,IAAA,WAAA,CAAY,OAAwB,EAAA;AAClC,QAAA,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAC7B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC;KAC7C;AAED;;AAEG;IACH,YAAY,GAAA;QACV,OAAOC,mBAAU,EAAE,CAAC;KACrB;AAED;;;;AAIG;AACO,IAAA,YAAY,CACpB,MAAyB,EACzB,QAAgB,EAChB,MAAmB,EACnB,eAAiC,EAAA;AAEjC,QAAA,IAAI,MAAM,KAAN,IAAA,IAAA,MAAM,uBAAN,MAAM,CAAE,OAAO,EAAE;YACnB,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AACvD,SAAA;QACD,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;AACnE,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QACjD,OAAO;YACL,KAAK,EAAE,MAAO,CAAC,WAAY;AAC3B,YAAA,kBAAkB,EAAE,MAAO,CAAC,SAAU,CAAC,OAAO,EAAE;SACjD,CAAC;KACH;AAED;;AAEG;AACO,IAAA,WAAW,CAAC,MAAgB,EAAE,KAAY,EAAE,eAAiC,EAAA;AACrF,QAAA,IACE,KAAK,CAAC,IAAI,KAAK,WAAW;YAC1B,KAAK,CAAC,IAAI,KAAK,iBAAiB;AAChC,YAAA,KAAK,CAAC,IAAI,KAAK,kBAAkB,EACjC;YACA,MAAM,SAAS,GAAG,KAA6B,CAAC;YAChD,QAAQ,SAAS,CAAC,SAAS;AACzB,gBAAA,KAAK,4BAA4B;AAC/B,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACrD,oBAAA,OAAO,IAAI,0BAA0B,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACvD,gBAAA,KAAK,+BAA+B;AAClC,oBAAA,OAAO,IAAIC,0BAAU,CAAC,oDAAoD,CAAC,CAAC;AAC9E,gBAAA,KAAK,kBAAkB,CAAC;AACxB,gBAAA,KAAK,sBAAsB,CAAC;AAC5B,gBAAA,KAAK,gBAAgB;AACnB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,WAAW,CAAC,MAAM,EAAE,CAAA,kCAAA,EAAqC,SAAS,CAAC,SAAS,CAAE,CAAA,CAAC,CAChF,CAAC;oBACF,MAAM;AACR,gBAAA;AACE,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAA,yBAAA,EAA4B,KAAK,CAAC,OAAO,CAAE,CAAA,CAAC,CAAC,CAAC;oBACnF,MAAM;AACT,aAAA;AACF,SAAA;AACD,QAAA,IACE,KAAK,CAAC,IAAI,KAAK,0BAA0B;YACzC,KAAK,CAAC,IAAI,KAAK,+BAA+B;AAC9C,YAAA,KAAK,CAAC,IAAI,KAAK,YAAY,EAC3B;AACA,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACD,QAAA,OAAO,IAAI,2BAA2B,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KAC7F;AACF,CAAA;AAED;AAEM,SAAU,YAAY,CAAC,OAA6B,EAAA;AACxD,IAAA,MAAM,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClF,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,KACV,cAAc,EAAE,OAAO,CAAC,aAAa,EACrC,WAAW,EACX,CAAA,CAAA;AACJ,CAAC;AAEe,SAAA,YAAY,CAAC,QAAgB,EAAE,OAAwB,EAAA;AACrE,IAAA,MAAM,MAAM,GAAG;QACb,SAAS,EAAE,YAAY,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC;QAC9D,aAAa,EAAE,OAAO,CAAC,aAAa;AACpC,QAAA,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,eAAe;QAC7C,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,QAAQ;AACR,QAAA,OAAO,EAAE,iCAAiC;KAC3C,CAAC;AACF,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;;;;;;AAaG;AACG,SAAU,6BAA6B,CAAC,MAA4B,EAAA;AACxE,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;;;;;;;;;;;;;AAkBG;AACG,SAAU,+BAA+B,CAAC,gBAAwB,EAAA;IACtE,MAAM,MAAM,GAAgD,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAEzF,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,KAAK,iCAAiC,EAAE;AAC1E,QAAA,MAAM,KAAK,CAAC,0CAA0C,CAAC,CAAC;AACzD,KAAA;AAED,IAAA,OAAO,MAAM,CAAC;AAChB;;ACpSA;AAOA,SAAS,+BAA+B,CAAC,QAAgB,EAAA;IACvD,OAAO,CAAA,sEAAA,EAAyE,QAAQ,CAAA,mMAAA,CAAqM,CAAC;AAChS,CAAC;AAED;;;;;AAKG;AACG,SAAU,yBAAyB,CACvC,QAAiB,EACjB,eAAiC,EACjC,4BAAA,GAAyC,EAAE,EAC3C,MAAyB,EAAA;;AAEzB,IAAA,IAAI,gBAAoC,CAAC;AACzC,IAAA,IAAI,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE;QACtD,gBAAgB,GAAG,QAAQ,CAAC;AAC7B,KAAA;SAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;QAC9B,gBAAgB,GAAG,QAAQ,CAAC;AAC7B,KAAA;AAAM,SAAA;QACL,gBAAgB,GAAG,CAAA,EAAA,GAAA,eAAe,KAAf,IAAA,IAAA,eAAe,KAAf,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,eAAe,CAAE,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,QAAQ,CAAC;AAC1D,KAAA;AACD,IAAA,IACE,QAAQ;AACR,QAAA,gBAAgB,KAAK,QAAQ;AAC7B,QAAA,CAAC,4BAA4B,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC3C,QAAA,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,gBAAiB,CAAC,KAAK,CAAC,CAAC,EACnF;AACA,QAAA,MAAM,OAAO,GAAG,+BAA+B,CAAC,QAAQ,CAAC,CAAC;QAC1D,MAAM,KAAA,IAAA,IAAN,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AACtB,QAAA,MAAM,IAAI,0BAA0B,CAAC,OAAO,CAAC,CAAC;AAC/C,KAAA;AAED,IAAA,OAAO,gBAAgB,CAAC;AAC1B;;AC3CA;AAOA;;AAEG;AACa,SAAA,aAAa,CAAC,MAAwB,EAAE,QAAgB,EAAA;AACtE,IAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE;AACvC,QAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,4KAA4K,CAC7K,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;AACpC,QAAA,MAAM,KAAK,CAAC;AACb,KAAA;AACH,CAAC;AAED;;AAEG;SACa,eAAe,CAC7B,MAAwB,EACxB,QAAiB,EACjB,QAAiB,EAAA;AAEjB,IAAA,IAAI,QAAQ,EAAE;AACZ,QAAA,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAChC,QAAA,OAAO,QAAQ,CAAC;AACjB,KAAA;IACD,IAAI,CAAC,QAAQ,EAAE;QACb,QAAQ,GAAG,uBAAuB,CAAC;AACpC,KAAA;IACD,IAAI,QAAQ,KAAK,uBAAuB,EAAE;AACxC,QAAA,OAAO,QAAQ,CAAC;AACjB,KAAA;AACD,IAAA,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;AAEG;AACG,SAAU,mCAAmC,CACjD,0BAAqC,EAAA;IAErC,IAAI,CAAC,0BAA0B,IAAI,0BAA0B,CAAC,MAAM,KAAK,CAAC,EAAE;AAC1E,QAAA,OAAO,EAAE,CAAC;AACX,KAAA;AAED,IAAA,IAAI,0BAA0B,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC5C,QAAA,OAAO,WAAW,CAAC;AACpB,KAAA;AAED,IAAA,OAAO,0BAA0B,CAAC;AACpC;;ACxDA;AACA;AAEM,SAAU,8BAA8B,CAAC,QAAgB,EAAA;IAC7D,IAAI,QAAQ,KAAK,MAAM,EAAE;AACvB,QAAA,OAAO,cAAc,CAAC;AACvB,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,mBAAmB,CAAC;AAC5B,KAAA;AACH;;ACTA;AAMA;;;AAGG;AACI,MAAM,aAAa,GAAGC,+BAAmB,CAAC;AAC/C,IAAA,SAAS,EAAE,eAAe;AAC1B,IAAA,WAAW,EAAE,iBAAiB;AAC9B,IAAA,cAAc,EAAE,WAAW;AAC5B,CAAA,CAAC;;ACdF;AACA;AAEO,MAAM,kBAAkB,GAAG,WAAW,CAAC;AACvC,MAAM,QAAQ,GAAG,wBAAwB,CAAC;AAC1C,MAAM,gBAAgB,GAAG,iCAAiC,CAAC;AAC3D,MAAM,cAAc,GAAG,YAAY,CAAC;AACpC,MAAM,kBAAkB,GAAG,YAAY,CAAC;AACxC,MAAM,kBAAkB,GAAG,oBAAoB;;ACRtD;AAKA;;;;;;;;AAQG;AACG,SAAU,mBAAmB,CAAC,MAAyB,EAAA;IAC3D,IAAI,KAAK,GAAG,EAAE,CAAC;AACf,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACzB,QAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,OAAO;AACR,SAAA;AAED,QAAA,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACnB,KAAA;AAAM,SAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACrC,KAAK,GAAG,MAAM,CAAC;AAChB,KAAA;AAED,IAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;AACvC,QAAA,OAAO,KAAK,CAAC;AACd,KAAA;AAED,IAAA,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC;AAChE,CAAC;AAeD;;;AAGG;AACG,SAAU,wBAAwB,CAAC,IAA6B,EAAA;AACpE,IAAA,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;AACvC,QAAA,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC/B,KAAA;AAED,IAAA,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;AACvC,QAAA,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;AAClC,QAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YACpB,OAAO,QAAQ,GAAG,IAAI,CAAC;AACxB,SAAA;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AAClB,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AACF,KAAA;AAED,IAAA,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE;QACvC,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC5C,KAAA;AAED,IAAA,MAAM,IAAI,KAAK,CACb,CAAA,wDAAA,EAA2D,IAAI,CAAC,UAAU,CAAA,eAAA,EAAkB,IAAI,CAAC,UAAU,CAAA,CAAA,CAAG,CAC/G,CAAC;AACJ;;AC1EA;AAyBA,MAAM,eAAe,GAAG,iBAAiB,CAAC;AAiB1C;;AAEG;AACG,SAAU,8BAA8B,CAAC,OAAgC,EAAA;;IAE7E,IAAI,aAAa,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,aAAa,CAAC;;AAG3C,IAAA,IAAIJ,eAAM,EAAE;AACV,QAAA,aAAa,GAAG,aAAa,KAAb,IAAA,IAAA,aAAa,KAAb,KAAA,CAAA,GAAA,aAAa,GAAI,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;AACnE,KAAA;;AAGD,IAAA,OAAO,aAAa,KAAb,IAAA,IAAA,aAAa,cAAb,aAAa,GAAI,oBAAoB,CAAC;AAC/C,CAAC;AAED;;;;;;AAMG;AACG,MAAO,cAAe,SAAQK,wBAAa,CAAA;AAO/C,IAAA,WAAA,CAAY,OAAgC,EAAA;;AAC1C,QAAA,MAAM,cAAc,GAAG,CAAqB,kBAAA,EAAA,WAAW,EAAE,CAAC;AAC1D,QAAA,MAAM,eAAe,GAAG,CAAA,CAAA,EAAA,GAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,gBAAgB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe;cAC9D,GAAG,OAAO,CAAC,gBAAgB,CAAC,eAAe,CAAI,CAAA,EAAA,cAAc,CAAE,CAAA;AACjE,cAAE,CAAA,EAAG,cAAc,CAAA,CAAE,CAAC;AAExB,QAAA,MAAM,OAAO,GAAG,8BAA8B,CAAC,OAAO,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AACjC,YAAA,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;AAC7E,SAAA;AAED,QAAA,KAAK,+BACH,kBAAkB,EAAE,iCAAiC,EACrD,YAAY,EAAE;AACZ,gBAAA,UAAU,EAAE,CAAC;aACd,EACE,EAAA,OAAO,CACV,EAAA,EAAA,gBAAgB,EAAE;gBAChB,eAAe;aAChB,EACD,OAAO,IACP,CAAC;AAEH,QAAA,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;AAC7B,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,8BAA8B,GAAG,CAAA,EAAA,GAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,8BAA8B,CAAC;;AAE9F,QAAA,IAAI,CAAC,sBAAsB,GAAQ,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CAAE,CAAC;KAC9C;IAED,MAAM,gBAAgB,CAAC,OAAwB,EAAA;QAC7CR,QAAM,CAAC,IAAI,CAAC,CAAA,0CAAA,EAA6C,OAAO,CAAC,GAAG,CAAG,CAAA,CAAA,CAAC,CAAC;QACzE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAEjD,QAAA,IAAI,QAAQ,CAAC,UAAU,KAAK,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC,EAAE;YAC/E,MAAM,UAAU,GAA4B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAE5E,YAAA,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;AAC5B,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AAED,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AAE9B,YAAA,MAAM,KAAK,GAAG;AACZ,gBAAA,WAAW,EAAE;oBACX,KAAK,EAAE,UAAU,CAAC,YAAY;AAC9B,oBAAA,kBAAkB,EAAE,wBAAwB,CAAC,UAAU,CAAC;AACzD,iBAAA;gBACD,YAAY,EAAE,UAAU,CAAC,aAAa;aACvC,CAAC;AAEF,YAAAA,QAAM,CAAC,IAAI,CACT,CAAA,iBAAA,EAAoB,OAAO,CAAC,GAAG,CAAgC,6BAAA,EAAA,KAAK,CAAC,WAAW,CAAC,kBAAkB,CAAA,CAAE,CACtG,CAAC;AACF,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,KAAK,GAAG,IAAI,mBAAmB,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC5E,YAAAA,QAAM,CAAC,OAAO,CACZ,CAAA,mDAAA,EAAsD,QAAQ,CAAC,MAAM,CAAK,EAAA,EAAA,KAAK,CAAC,aAAa,CAAC,gBAAgB,CAAA,CAAE,CACjH,CAAC;AACF,YAAA,MAAM,KAAK,CAAC;AACb,SAAA;KACF;AAED,IAAA,MAAM,kBAAkB,CACtB,QAAgB,EAChB,QAAgB,EAChB,MAAc,EACd,YAAgC,EAChC,YAAgC,EAChC,UAA2B,EAAE,EAAA;QAE7B,IAAI,YAAY,KAAK,SAAS,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QACDA,QAAM,CAAC,IAAI,CACT,CAAA,wDAAA,EAA2D,QAAQ,CAAa,UAAA,EAAA,MAAM,CAAU,QAAA,CAAA,CACjG,CAAC;AAEF,QAAA,MAAM,aAAa,GAAG;AACpB,YAAA,UAAU,EAAE,eAAe;AAC3B,YAAA,SAAS,EAAE,QAAQ;AACnB,YAAA,aAAa,EAAE,YAAY;AAC3B,YAAA,KAAK,EAAE,MAAM;SACd,CAAC;QAEF,IAAI,YAAY,KAAK,SAAS,EAAE;AAC7B,YAAA,aAAqB,CAAC,aAAa,GAAG,YAAY,CAAC;AACrD,SAAA;AAED,QAAA,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,aAAa,CAAC,CAAC;AAEjD,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,OAAO,cAAc,KAAI;YACvB,IAAI;AACF,gBAAA,MAAM,SAAS,GAAG,8BAA8B,CAAC,QAAQ,CAAC,CAAC;gBAC3D,MAAM,OAAO,GAAGS,sCAAqB,CAAC;oBACpC,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa,CAAI,CAAA,EAAA,QAAQ,CAAI,CAAA,EAAA,SAAS,CAAE,CAAA;AACrD,oBAAA,MAAM,EAAE,MAAM;AACd,oBAAA,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE;oBACtB,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,OAAO,EAAEC,kCAAiB,CAAC;AACzB,wBAAA,MAAM,EAAE,kBAAkB;AAC1B,wBAAA,cAAc,EAAE,mCAAmC;qBACpD,CAAC;oBACF,cAAc,EAAE,cAAc,CAAC,cAAc;AAC9C,iBAAA,CAAC,CAAC;gBAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACtD,gBAAAV,QAAM,CAAC,IAAI,CAAC,kDAAkD,QAAQ,CAAA,CAAE,CAAC,CAAC;AAC1E,gBAAA,OAAO,QAAQ,CAAC;AACjB,aAAA;AAAC,YAAA,OAAO,GAAQ,EAAE;AACjB,gBAAA,IACE,GAAG,CAAC,IAAI,KAAK,uBAAuB;AACpC,oBAAA,GAAG,CAAC,aAAa,CAAC,KAAK,KAAK,sBAAsB,EAClD;;;;AAIA,oBAAAA,QAAM,CAAC,IAAI,CAAC,uDAAuD,QAAQ,CAAA,CAAE,CAAC,CAAC;AAC/E,oBAAA,OAAO,IAAI,CAAC;AACb,iBAAA;AAAM,qBAAA;oBACLA,QAAM,CAAC,OAAO,CACZ,CAAA,uDAAA,EAA0D,QAAQ,CAAK,EAAA,EAAA,GAAG,CAAE,CAAA,CAC7E,CAAC;AACF,oBAAA,MAAM,GAAG,CAAC;AACX,iBAAA;AACF,aAAA;AACH,SAAC,CACF,CAAC;KACH;;;AAKD,IAAA,mBAAmB,CAAC,aAAqB,EAAA;AACvC,QAAA,MAAM,UAAU,GAAG,IAAIW,+BAAe,EAAE,CAAC;AACzC,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;AACnE,QAAA,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AACtD,QAAA,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC;QAClD,UAAU,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,GAAG,MAAM,KAAI;YACxC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;AACpD,YAAA,IAAI,eAAe,EAAE;AACnB,gBAAA,eAAe,CAAC,GAAG,MAAM,CAAC,CAAC;AAC5B,aAAA;AACH,SAAC,CAAC;QACF,OAAO,UAAU,CAAC,MAAM,CAAC;KAC1B;AAED,IAAA,aAAa,CAAC,aAAsB,EAAA;AAClC,QAAA,MAAM,GAAG,GAAG,aAAa,IAAI,eAAe,CAAC;AAC7C,QAAA,MAAM,WAAW,GAAG;YAClB,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;;YAEzC,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;SACtD,CAAC;AACF,QAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACvB,OAAO;AACR,SAAA;AACD,QAAA,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;YACpC,UAAU,CAAC,KAAK,EAAE,CAAC;AACpB,SAAA;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;KAC3C;AAED,IAAA,gBAAgB,CAAC,OAA+B,EAAA;;AAC9C,QAAA,MAAM,SAAS,GAAG,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,IAAI,MAC3B,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAC,GAAG,CAAA,CACV,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,CAC7B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,mBAAmB,CAAC,CAAC;AAChD,QAAA,OAAO,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,eAAe,GAAG,eAAe,CAAC;KAC1F;;AAID,IAAA,MAAM,mBAAmB,CACvB,GAAW,EACX,OAA+B,EAAA;QAE/B,MAAM,OAAO,GAAGF,sCAAqB,CAAC;YACpC,GAAG;AACH,YAAA,MAAM,EAAE,KAAK;AACb,YAAA,IAAI,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,IAAI;YACnB,OAAO,EAAEC,kCAAiB,CAAC,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,OAAO,CAAC;AAC5C,YAAA,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC;AACvD,SAAA,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAEjD,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAE9B,OAAO;AACL,YAAA,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,SAAS;AACvE,YAAA,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE;YAClC,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC;KACH;AAED,IAAA,MAAM,oBAAoB,CACxB,GAAW,EACX,OAA+B,EAAA;QAE/B,MAAM,OAAO,GAAGD,sCAAqB,CAAC;YACpC,GAAG;AACH,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,IAAI,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,IAAI;YACnB,OAAO,EAAEC,kCAAiB,CAAC,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,OAAO,CAAC;;YAE5C,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACtE,SAAA,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAEjD,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAE9B,OAAO;AACL,YAAA,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,SAAS;AACvE,YAAA,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE;YAClC,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC;KACH;AAED;;;AAGG;IACH,yBAAyB,GAAA;QACvB,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACpC;AACD;;;;;;;;;;;AAWG;AACK,IAAA,cAAc,CAAC,QAA0B,EAAA;QAC/C,IAAI,CAAC,IAAI,CAAC,8BAA8B,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;YAChE,OAAO;AACR,SAAA;QACD,MAAM,cAAc,GAAG,kCAAkC,CAAC;QAC1D,IAAI;AACF,YAAA,MAAM,MAAM,GAAI,QAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC/E,YAAA,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;YACxC,IAAI,CAAC,WAAW,EAAE;;gBAEhB,OAAO;AACR,aAAA;YACD,MAAM,cAAc,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,YAAA,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CACzC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CACvD,CAAC;AAEF,YAAAV,QAAM,CAAC,IAAI,CACT,CAAA,mCAAA,EAAsC,KAAK,CAAgB,aAAA,EAAA,GAAG,CAC5D,uBAAA,EAAA,GAAG,IAAI,cACT,CAAA,oBAAA,EAAuB,GAAG,CAAA,CAAE,CAC7B,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,CAAM,EAAE;YACfA,QAAM,CAAC,OAAO,CACZ,6FAA6F,EAC7F,CAAC,CAAC,OAAO,CACV,CAAC;AACH,SAAA;KACF;AACF;;ACzVD;AACA;AAEA;;AAEG;AACH,IAAY,iBA2GX,CAAA;AA3GD,CAAA,UAAY,iBAAiB,EAAA;;AAE3B,IAAA,iBAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;;AAEzC,IAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;;AAEjB,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;;AAEnB,IAAA,iBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;;AAEvB,IAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;;AAEjB,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;;AAEnB,IAAA,iBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;;AAEjC,IAAA,iBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;;AAEjC,IAAA,iBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;;AAE/B,IAAA,iBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;;AAE/B,IAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,iBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;;AAE3B,IAAA,iBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;;AAE3B,IAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;;AAEnB,IAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;;AAEjB,IAAA,iBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;;AAE/B,IAAA,iBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;;AAE3B,IAAA,iBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;;AAErC,IAAA,iBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;;AAEnC,IAAA,iBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;;AAE7B,IAAA,iBAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;;AAEzC,IAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;;AAErB,IAAA,iBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;;AAE/B,IAAA,iBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;;AAEvB,IAAA,iBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;;AAEvB,IAAA,iBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B,CAAA;;AAE/B,IAAA,iBAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;;AAEzC,IAAA,iBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;;AAErC,IAAA,iBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;;AAEvC,IAAA,iBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;;AAE7B,IAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,iBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;;AAEvB,IAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,iBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B,CAAA;;AAE7B,IAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;;AAErB,IAAA,iBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;;AAErC,IAAA,iBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;;AAEnC,IAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,iBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;;AAEvB,IAAA,iBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;;AAE3B,IAAA,iBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;;AAEzB,IAAA,iBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC,CAAA;;AAEjC,IAAA,iBAAA,CAAA,kBAAA,CAAA,GAAA,kBAAqC,CAAA;;AAErC,IAAA,iBAAA,CAAA,sBAAA,CAAA,GAAA,eAAsC,CAAA;;AAEtC,IAAA,iBAAA,CAAA,kBAAA,CAAA,GAAA,WAA8B,CAAA;;AAE9B,IAAA,iBAAA,CAAA,qBAAA,CAAA,GAAA,cAAoC,CAAA;;AAEpC,IAAA,iBAAA,CAAA,mBAAA,CAAA,GAAA,YAAgC,CAAA;;AAEhC,IAAA,iBAAA,CAAA,qBAAA,CAAA,GAAA,WAAiC,CAAA;;AAEjC,IAAA,iBAAA,CAAA,wBAAA,CAAA,GAAA,cAAuC,CAAA;AACzC,CAAC,EA3GW,iBAAiB,KAAjB,iBAAiB,GA2G5B,EAAA,CAAA,CAAA;;ACjHD;AA4DA;;;AAGG;AACH,IAAI,mBAAmB,GAEP,SAAS,CAAC;AAE1B;;;AAGG;AACI,MAAM,wBAAwB,GAAG;AACtC,IAAA,cAAc,CAAC,cAA8D,EAAA;QAC3E,mBAAmB,GAAG,cAAc,CAAC;KACtC;CACF,CAAC;AAEF;;;;;;;;AAQG;AACG,MAAgB,QAAS,SAAQ,iBAAiB,CAAA;AAgCtD,IAAA,WAAA,CAAY,OAAwB,EAAA;;QAClC,KAAK,CAAC,OAAO,CAAC,CAAC;;;;;QA5BT,IAAG,CAAA,GAAA,GAGP,EAAE,CAAC;QACC,IAAM,CAAA,MAAA,GAGV,EAAE,CAAC;QAOG,IAAoB,CAAA,oBAAA,GAAY,KAAK,CAAC;QAe9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AACpF,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,MAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,sBAAsB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,0BAA0B,CAC5D,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC9C,QAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,YAAY,EAAE;AACzB,YAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;AAC1C,SAAA;;QAGD,IAAI,mBAAmB,KAAK,SAAS,KAAI,CAAA,EAAA,GAAA,OAAO,CAAC,4BAA4B,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAA,EAAE;AACtF,YAAA,MAAM,aAAa,GACjB,MAAA,CAAA,MAAA,CAAA,EAAA,IAAI,EAAE,CAAG,EAAA,OAAO,CAAC,4BAA4B,CAAC,IAAI,CAAA,CAAA,EAAI,oBAAoB,CAAE,CAAA,EAAA,EACzE,OAAO,CAAC,4BAA4B,CACxC,CAAC;AACF,YAAA,MAAM,UAAU,GACd,MAAA,CAAA,MAAA,CAAA,EAAA,IAAI,EAAE,CAAG,EAAA,OAAO,CAAC,4BAA4B,CAAC,IAAI,CAAA,CAAA,EAAI,gBAAgB,CAAE,CAAA,EAAA,EACrE,OAAO,CAAC,4BAA4B,CACxC,CAAC;YACF,IAAI,CAAC,iBAAiB,GAAG,MAAM,mBAAoB,CAAC,aAAa,CAAC,CAAC;YACnE,IAAI,CAAC,oBAAoB,GAAG,MAAM,mBAAoB,CAAC,UAAU,CAAC,CAAC;AACpE,SAAA;AAAM,aAAA,IAAI,MAAA,OAAO,CAAC,4BAA4B,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,EAAE;YACxD,MAAM,IAAI,KAAK,CACb;gBACE,qFAAqF;gBACrF,yHAAyH;gBACzH,mFAAmF;gBACnF,0FAA0F;AAC3F,aAAA,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;AACH,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;AAC1F,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,iBAAiB,CAAC,kBAAkB,EAAE;AAC7D,YAAA,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC;AACpC,SAAA;KACF;AAED;;AAEG;AACO,IAAA,qBAAqB,CAAC,OAAwB,EAAA;;AACtD,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,uBAAuB,CAAC;AAC7D,QAAA,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AAErF,QAAA,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;QAC/E,MAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7D,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CACnC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CAAC,sBAAsB,CAAA,EAAA,EACjC,aAAa,EAAE,SAAS,EACxB,cAAc,EAAE,OAAO,CAAC,cAAc,IACtC,CAAC;QAEH,MAAM,kBAAkB,GAAa,EAAE,CAAC;QAExC,OAAO;AACL,YAAA,IAAI,EAAE;gBACJ,QAAQ;gBACR,SAAS;gBACT,gBAAgB,EAAE,mBAAmB,CACnC,QAAQ,EACR,SAAS,EACT,OAAO,CAAC,wBAAwB,CACjC;gBACD,kBAAkB;AACnB,aAAA;;AAED,YAAA,MAAM,EAAE;gBACN,aAAa,EAAE,IAAI,CAAC,cAAc;AAClC,gBAAA,aAAa,EAAE;AACb,oBAAA,cAAc,EAAE,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC;AACrD,oBAAA,QAAQ,EAAE,eAAe,CAACY,oBAAW,EAAE,CAAC;AACxC,oBAAA,iBAAiB,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,cAAc,0CAAE,0BAA0B;AACtE,iBAAA;AACF,aAAA;SACF,CAAC;KACH;IAYS,MAAM,CACd,OAAgB,EAChB,SAAmB,EAAA;AAEnB,QAAA,MAAM,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;QAC/C,IAAI,OAAO,KAAK,aAAa,EAAE;YAC7B,QAAQ,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,YAAY,EAAG;AAC1C,SAAA;aAAM,IAAI,OAAO,KAAK,mBAAmB,EAAE;YAC1C,QAAQ,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,MAAM,EAAG;AAC1C,SAAA;aAAM,IAAI,OAAO,KAAK,cAAc,EAAE;YACrC,OAAO,GAAG,CAAC,YAAa,CAAC;AAC1B,SAAA;AAAM,aAAA;YACL,OAAO,GAAG,CAAC,MAAO,CAAC;AACpB,SAAA;KACF;AAED;;AAEG;IACH,MAAM,IAAI,CAAC,OAAuC,EAAA;AAChD,QAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE;YACxB,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAK;;;gBAGjD,IAAI,CAAC,cAAe,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC5D,aAAC,CAAC,CAAC;AACJ,SAAA;QAED,MAAM,GAAG,GAAG,CAAA,OAAO,aAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,SAAS,IAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;AACxD,QAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAE;YACtB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,GAAG,CAAC,KAAK,CAAC,CAAC;AACnD,SAAA;AACD,QAAA,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,YAAY,EAAE;YAClC,OAAO;AACR,SAAA;AACD,QAAA,IAAI,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,SAAS,KAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;AACjE,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG;AACtB,gBAAA,WAAW,EAAE,MAAM,IAAI,CAAC,oBAAoB,EAAE;aAC/C,CAAC;AACH,SAAA;AACD,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE;AACxC,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG;AACtB,gBAAA,WAAW,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;aAC5C,CAAC;AACH,SAAA;AAED,QAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAE;AACtB,YAAA,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAIC,qBAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5E,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,IAAIA,qBAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACzE,SAAA;QAED,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AAClE,SAAA;;AAED,QAAA,IACE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY;AACjC,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe;AACpC,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,EACtC;AACA,YAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,SAAS,EAAE;AACtB,gBAAA,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAIA,qBAAQ,CAAC,6BAA6B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACxF,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,IAAIA,qBAAQ,CAAC,6BAA6B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACrF,aAAA;AACF,SAAA;AAAM,aAAA;YACL,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC7B,gBAAA,MAAM,IAAI,KAAK,CACb,gHAAgH,CACjH,CAAC;AACH,aAAA;AACF,SAAA;KACF;AAED;;AAEG;AACO,IAAA,gBAAgB,CACxB,OAAsD,EACtD,WAA6B,EAC7B,QAAqB,EAAA;QAErB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;YACrC,OAAO;AACJ,iBAAA,IAAI,CAAC,CAAC,SAAS,KAAI;AAClB,gBAAA,OAAO,OAAO,CAAC,SAAU,CAAC,CAAC;AAC7B,aAAC,CAAC;iBACD,KAAK,CAAC,MAAM,CAAC,CAAC;AACjB,YAAA,IAAI,WAAW,EAAE;AACf,gBAAA,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAK;AACzC,oBAAA,QAAQ,KAAR,IAAA,IAAA,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,EAAI,CAAC;AACf,iBAAC,CAAC,CAAC;AACJ,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;AAEG;AACH,IAAA,MAAM,gBAAgB,CAAC,SAAS,GAAG,KAAK,EAAA;QACtC,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO,IAAI,CAAC,OAAO,CAAC;AACrB,SAAA;AACD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC,aAAa,EAAE,CAAC;AAC1E,QAAA,MAAM,gBAAgB,GAAG,OAAM,KAAK,KAAA,IAAA,IAAL,KAAK,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAL,KAAK,CAAE,cAAc,EAAE,CAAA,CAAC;QAEvD,IAAI,CAAC,gBAAgB,EAAE;YACrB,OAAO;AACR,SAAA;AAED,QAAA,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,MAAM;AACR,iBAAA,IAAI,CAAC,CAAA;;;;AAI+J,4KAAA,CAAA,CAAC,CAAC;YACzK,OAAO;AACR,SAAA;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AAED;;AAEG;AACH,IAAA,MAAM,cAAc,CAClB,MAAgB,EAChB,OAAuC,EAAA;;AAEvC,QAAA,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,SAAS,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,MAAM,IAAI,2BAA2B,CAAC;gBACpC,MAAM;AACN,gBAAA,eAAe,EAAE,OAAO;AACxB,gBAAA,OAAO,EACL,sFAAsF;AACzF,aAAA,CAAC,CAAC;AACJ,SAAA;AAED,QAAA,MAAM,aAAa,GAA+B;;AAEhD,YAAA,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;AACnC,YAAA,aAAa,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,aAAa;YACrC,MAAM;AACN,YAAA,SAAS,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS;AAC7B,YAAA,MAAM,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,MAAM;SACxB,CAAC;QAEF,IAAI;AACF,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;AACzD;;;;;AAKG;YACH,OAAM,MAAA,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,SAAS,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,aAAa,EAAG,CAAA,cAAc,EAAE,CAAA,CAAC;YACvF,MAAM,QAAQ,GACZ,CAAA,EAAA,IAAC,OAAM,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,kBAAkB,CACxE,aAAa,CACd,CAAA,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,IAAC,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS,CAAC,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC;AAC5F,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,QAAQ,IAAI,SAAS,CAAC,CAAC;AACxE,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAC9C,SAAA;KACF;AAOD;;;AAGG;AACI,IAAA,MAAM,QAAQ,CACnB,MAAgB,EAChB,UAAyC,EAAE,EAAA;AAE3C,QAAA,MAAM,QAAQ,GACZ,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,4BAA4B,CAAC;YACpF,IAAI,CAAC,QAAQ,CAAC;QAEhB,OAAO,CAAC,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAE/D,QAAA,OAAO,CAAC,aAAa,GAAG,CAAA,OAAO,aAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,aAAa,KAAI,IAAI,CAAC,YAAY,EAAE,CAAC;AACtE,QAAA,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEzB,IAAI;;;;AAIF,YAAA,MAAM,aAAa,GAAI,OAAe,CAAC,MAAM,CAAC;AAC9C,YAAA,IAAI,aAAa,EAAE;AACjB,gBAAA,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC;AACnC,aAAA;AACD,YAAA,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,aAAa,EAAE;AACtC,gBAAA,OAAe,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;AAC7C,aAAA;;YAED,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACnD,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;AACjB,YAAA,IAAI,GAAG,CAAC,IAAI,KAAK,6BAA6B,EAAE;AAC9C,gBAAA,MAAM,GAAG,CAAC;AACX,aAAA;AACD,YAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,8BAA8B,EAAE;gBAC3C,MAAM,IAAI,2BAA2B,CAAC;oBACpC,MAAM;AACN,oBAAA,eAAe,EAAE,OAAO;AACxB,oBAAA,OAAO,EACL,uFAAuF;AAC1F,iBAAA,CAAC,CAAC;AACJ,aAAA;AACD,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,iEAAA,CAAmE,CAAC,CAAC;YACtF,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACzC,SAAA;KACF;AACF;;ACjbD;AAmBA,MAAM,cAAc,GAAG,QAAQ,CAAC;AAChC,MAAM,oBAAoB,GAAG,sCAAsC,CAAC;AACpE,MAAMb,QAAM,GAAG,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;AAE9D,IAAI,eAAe,GAAuC,SAAS,CAAC;AAE7D,MAAM,uBAAuB,GAAG;AACrC,IAAA,yBAAyB,CAAC,MAA8B,EAAA;QACtD,eAAe,GAAG,MAAM,CAAC;KAC1B;CACF,CAAC;AAEF;AACA,MAAM,oBAAoB,GAA2B;AACnD,IAAA,IAAI,EAAE,mFAAmF;CAC1F,CAAC;AAEF,SAAS,sBAAsB,CAAC,QAAgB,EAAA;;AAE9C,IAAA,MAAM,sBAAsB,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAC9D,IAAA,IAAI,sBAAsB,EAAE;AAC1B,QAAA,MAAM,IAAI,0BAA0B,CAAC,sBAAsB,CAAC,CAAC;AAC9D,KAAA;AACH,CAAC;AAID,MAAM,uBAAuB,GAAqC;IAChE,UAAU,EAAEE,2BAAmB,CAAC,gBAAgB;IAChD,UAAU,EAAEA,2BAAmB,CAAC,UAAU;IAC1C,gBAAgB,EAAEA,2BAAmB,CAAC,YAAY;IAClD,iBAAiB,EAAEA,2BAAmB,CAAC,eAAe;CACvD,CAAC;AAEF;;;AAGG;AACG,SAAU,qBAAqB,CAAC,QAAgB,EAAA;AACpD,IAAA,MAAM,YAAY,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;;IAE/C,MAAM,YAAY,GAAG,MAAM,CAAC;AAC5B,IAAA,MAAM,OAAO,GAAGY,sBAAE,CAAC,OAAO,EAAE,CAAC;IAE7B,SAAS,YAAY,CAAC,GAAG,YAAsB,EAAA;AAC7C,QAAA,MAAM,QAAQ,GAAGC,wBAAI,CAAC,IAAI,CAAC,GAAG,YAAY,EAAE,YAAY,EAAE,GAAG,YAAY,CAAC,CAAC;AAC3E,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAACC,sBAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AAC7E,QAAA,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC;KAC3B;IAED,IAAI;AACF,QAAA,IAAI,OAAe,CAAC;QACpB,QAAQ,OAAO,CAAC,QAAQ;AACtB,YAAA,KAAK,OAAO;AACV,gBAAA,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,OAAQ,CAAC;AAC/B,gBAAA,OAAO,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;AACrD,YAAA,KAAK,QAAQ;gBACX,OAAO,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,qBAAqB,CAAC,CAAC;AACjE,YAAA,KAAK,OAAO;AACV,gBAAA,OAAO,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC1C,YAAA;gBACE,OAAO;AACV,SAAA;AACF,KAAA;AAAC,IAAA,OAAO,CAAM,EAAE;QACfhB,QAAM,CAAC,IAAI,CAAC,CAAA,iEAAA,EAAoE,CAAC,CAAC,OAAO,CAAE,CAAA,CAAC,CAAC;QAC7F,OAAO;AACR,KAAA;AACH,CAAC;AAED;;;;;;;;;AASG;MACU,0BAA0B,CAAA;AAMrC;;;;;;;;;AASG;AACH,IAAA,WAAA,CAAY,OAA2C,EAAA;;;QAGrD,IAAI,CAAC,SAAS,IAAI,qBAAqB,CAAC,aAAa,CAAC,IAAI,YAAY,CAAqB,CAAC;;QAG5F,MAAM,aAAa,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE9D,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,iBACtC,aAAa,EAAA,EACV,OAAO,CAAA,CACV,CAAC;AAEH,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC/B,YAAA,aAAa,CAACA,QAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;AACxC,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AAClC,SAAA;AAAM,aAAA;AACL,YAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;AAChC,SAAA;AAED,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;AAEF,QAAA,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACvC;AAED;;AAEG;AACK,IAAA,MAAM,OAAO,GAAA;;AAEnB,QAAA,MAAM,cAAc,GAAG,qBAAqB,CAAC,cAAc,CAAC,CAAC;AAC7D,QAAA,IAAI,cAAc,EAAE;AAClB,YAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;AAChC,SAAA;AACD,QAAA,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KACvC;AAOD;;AAEG;IACK,WAAW,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AACtC,SAAA;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;KAC5B;AAED;;;;;;;AAOG;AACI,IAAA,MAAM,QAAQ,CACnB,MAAyB,EACzB,OAAyB,EAAA;;AAEzB,QAAA,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAEzB,MAAM,QAAQ,GACZ,yBAAyB,CACvB,IAAI,CAAC,QAAQ,EACb,OAAO,EACP,IAAI,CAAC,4BAA4B,EACjCA,QAAM,CACP,IAAI,IAAI,CAAC,QAAQ,CAAC;QAErB,IAAI,eAAe,KAAK,SAAS,EAAE;YACjC,MAAM,IAAI,0BAA0B,CAClC;gBACE,iEAAiE;gBACjE,uGAAuG;gBACvG,mFAAmF;gBACnF,mFAAmF;gBACnF,wFAAwF;AACzF,aAAA,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;AACH,SAAA;AAED,QAAA,IAAI,WAAW,GAAG,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;AAGzE,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE;AAC5C,YAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;AACrF,YAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,YAAA,MAAM,KAAK,CAAC;AACb,SAAA;QAED,IAAI,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE;YAC7C,WAAW,IAAI,iBAAiB,CAAC;AAClC,SAAA;;;;;;;;;AAUD,QAAA,MAAM,WAAW,GAAG,MAAM,eAAe,EAAE,CAAC;;AAG5C,QAAA,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAC9B,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,OAAO,KAAK,IAAI,CAAC,SAAS,CAAC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,WAAW,CAAC,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,EAAE,CAAC;AAExF,QAAA,IAAI,YAAY,EAAE;AAChB,YAAA,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAChE,QAAQ,EACR,oBAAoB,EACpB,WAAW,EACX,YAAY,EACZ,SAAS,CACV,CAAC;AAEF,YAAA,IAAI,aAAa,EAAE;gBACjBA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC5C,OAAO,aAAa,CAAC,WAAW,CAAC;AAClC,aAAA;AAAM,iBAAA;AACL,gBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,0NAA0N,CAC3N,CAAC;AACF,gBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;AACF,SAAA;AAAM,aAAA;AACL,YAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,8MAA8M,CAC/M,CAAC;AACF,YAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,YAAA,MAAM,KAAK,CAAC;AACb,SAAA;KACF;AACF;;AChQD;AAOA;;;;AAIG;AACH,MAAM,aAAa,GAAuB;AACxC,IAAA,kBAAkB,EAAE,wBAAwB;AAC5C,IAAA,uBAAuB,EAAE,uBAAuB;CACjD,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;AACG,SAAU,iBAAiB,CAAC,MAAsB,EAAA;IACtD,MAAM,CAAC,aAAa,CAAC,CAAC;AACxB;;AC9CA;AAaA,MAAMiB,SAAO,GAAG,gDAAgD,CAAC;AACjE,MAAMjB,QAAM,GAAG,gBAAgB,CAACiB,SAAO,CAAC,CAAC;AAEzC;;AAEG;AACH,SAASC,uBAAqB,CAC5B,MAAyB,EACzB,QAAiB,EAAA;AAEjB,IAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,MAAM,IAAI,KAAK,CAAC,GAAGD,SAAO,CAAA,oCAAA,CAAsC,CAAC,CAAC;AACnE,KAAA;AAED,IAAA,MAAM,eAAe,GAA2B;QAC9C,QAAQ;AACR,QAAA,aAAa,EAAE,YAAY;KAC5B,CAAC;AAEF,IAAA,IAAI,QAAQ,EAAE;AACZ,QAAA,eAAe,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACrC,KAAA;AAED,IAAA,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,eAAe,CAAC,CAAC;;AAGnD,IAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;AAC7B,QAAA,MAAM,IAAI,KAAK,CAAC,GAAGA,SAAO,CAAA,4CAAA,CAA8C,CAAC,CAAC;AAC3E,KAAA;AACD,IAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;AAC3B,QAAA,MAAM,IAAI,KAAK,CAAC,GAAGA,SAAO,CAAA,0CAAA,CAA4C,CAAC,CAAC;AACzE,KAAA;IAED,OAAO;AACL,QAAA,GAAG,EAAE,CAAA,EAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA,CAAA,EAAI,KAAK,CAAC,QAAQ,EAAE,CAAE,CAAA;AACtD,QAAA,MAAM,EAAE,KAAK;QACb,OAAO,EAAEP,kCAAiB,CAAC;AACzB,YAAA,MAAM,EAAE,kBAAkB;AAC1B,YAAA,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;SAC/B,CAAC;KACH,CAAC;AACJ,CAAC;AAED;;AAEG;AACI,MAAM,iBAAiB,GAAQ;AACpC,IAAA,IAAI,EAAE,mBAAmB;AACzB,IAAA,MAAM,WAAW,CAAC,EAAE,MAAM,EAAE,EAAA;AAC1B,QAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,YAAAV,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,iDAAA,CAAmD,CAAC,CAAC;AAC3E,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACxB,QAAA,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,CAAC,MAAM,EAAE;AACX,YAAAjB,QAAM,CAAC,IAAI,CACT,GAAGiB,SAAO,CAAA,iFAAA,CAAmF,CAC9F,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AACD,IAAA,MAAM,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE,EAAA;QAErC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;AAEvE,QAAA,IAAI,UAAU,EAAE;AACd,YAAAjB,QAAM,CAAC,OAAO,CACZ,GAAGiB,SAAO,CAAA,wGAAA,CAA0G,CACrH,CAAC;AACH,SAAA;AAED,QAAAjB,QAAM,CAAC,IAAI,CACT,CAAA,EAAGiB,SAAO,CAAA,wFAAA,EAA2F,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA,2BAAA,CAA6B,CAC3J,CAAC;AAEF,QAAA,MAAM,OAAO,GAAGR,sCAAqB,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EACnC,WAAW,EAAE,eAAe,CAAC,WAAW,IACrCS,uBAAqB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA,EAAA;;YAE1C,uBAAuB,EAAE,IAAI,EAAA,CAAA,CAC7B,CAAC;QACH,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACrE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,KAAK,IAAI,CAAC;KAC7D;CACF;;ACtGD;AAaA,MAAMD,SAAO,GAAG,2CAA2C,CAAC;AACrD,MAAMjB,QAAM,GAAG,gBAAgB,CAACiB,SAAO,CAAC,CAAC;AAEhD;;AAEG;AACH,SAASC,uBAAqB,CAC5B,MAAyB,EACzB,QAAiB,EACjB,UAAmB,EAAA;AAEnB,IAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,MAAM,IAAI,KAAK,CAAC,GAAGD,SAAO,CAAA,oCAAA,CAAsC,CAAC,CAAC;AACnE,KAAA;AAED,IAAA,MAAM,IAAI,GAA2B;QACnC,QAAQ;KACT,CAAC;AAEF,IAAA,IAAI,QAAQ,EAAE;AACZ,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC3B,KAAA;AACD,IAAA,IAAI,UAAU,EAAE;AACd,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC9B,KAAA;;AAGD,IAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;AAC7B,QAAA,MAAM,IAAI,KAAK,CAAC,GAAGA,SAAO,CAAA,4CAAA,CAA8C,CAAC,CAAC;AAC3E,KAAA;AACD,IAAA,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;IACzC,OAAO;AACL,QAAA,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY;AAC7B,QAAA,MAAM,EAAE,MAAM;AACd,QAAA,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE;QACvB,OAAO,EAAEP,kCAAiB,CAAC;AACzB,YAAA,MAAM,EAAE,kBAAkB;AAC1B,YAAA,QAAQ,EAAE,MAAM;AAChB,YAAA,cAAc,EAAE,mCAAmC;SACpD,CAAC;KACH,CAAC;AACJ,CAAC;AAED;;;AAGG;AACI,MAAM,aAAa,GAAQ;AAChC,IAAA,IAAI,EAAE,eAAe;AACrB,IAAA,MAAM,WAAW,CAAC,EAAE,MAAM,EAAE,EAAA;AAC1B,QAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,YAAAV,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,iDAAA,CAAmD,CAAC,CAAC;AAC3E,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,EAAE;AACX,YAAAjB,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,+DAAA,CAAiE,CAAC,CAAC;AAC1F,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AACD,IAAA,MAAM,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE,EAAA;QAErC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;AAEvE,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAAjB,QAAM,CAAC,OAAO,CACZ,GAAGiB,SAAO,CAAA,gGAAA,CAAkG,CAC7G,CAAC;AACH,SAAA;AAED,QAAA,IAAI,UAAU,EAAE;AACd,YAAAjB,QAAM,CAAC,OAAO,CACZ,GAAGiB,SAAO,CAAA,sHAAA,CAAwH,CACnI,CAAC;AACH,SAAA;AAED,QAAAjB,QAAM,CAAC,IAAI,CACT,CAAA,EAAGiB,SAAO,CAAA,yEAAA,EAA4E,OAAO,CAAC,GAAG,CAAC,YAAY,CAAA,CAAA,CAAG,CAClH,CAAC;AAEF,QAAA,MAAM,OAAO,GAAGR,sCAAqB,CACnC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,WAAW,EAAE,eAAe,CAAC,WAAW,EAAA,EACrCS,uBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA,EAAA;;YAEtD,uBAAuB,EAAE,IAAI,EAAA,CAAA,CAC7B,CAAC;QACH,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACrE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,KAAK,IAAI,CAAC;KAC7D;CACF;;AC3GD;AAkBA,MAAMD,SAAO,GAAG,kCAAkC,CAAC;AACnD,MAAMjB,QAAM,GAAG,gBAAgB,CAACiB,SAAO,CAAC,CAAC;AAEzC;;AAEG;AACH,SAASC,uBAAqB,CAC5B,MAAyB,EACzB,QAAiB,EACjB,UAAmB,EACnB,OAGC,EAAA;;AAED,IAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,MAAM,IAAI,KAAK,CAAC,GAAGD,SAAO,CAAA,oCAAA,CAAsC,CAAC,CAAC;AACnE,KAAA;IAED,MAAM,EAAE,SAAS,EAAE,kBAAkB,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;IACxD,IAAI,KAAK,GAAG,EAAE,CAAC;;;IAIf,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,eAAe,GAA2B;YAC9C,QAAQ;AACR,YAAA,aAAa,EAAE,cAAc;SAC9B,CAAC;AACF,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,eAAe,CAAC,SAAS,GAAG,QAAQ,CAAC;AACtC,SAAA;AACD,QAAA,IAAI,UAAU,EAAE;AACd,YAAA,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;AACzC,SAAA;AACD,QAAA,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,eAAe,CAAC,CAAC;AACpD,QAAA,KAAK,GAAG,CAAI,CAAA,EAAA,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;AACjC,KAAA;AAED,IAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,gBAAgB,EAAE,CAAA,EAAA,GAAA,OAAO,CAAC,GAAG,CAAC,iCAAiC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,QAAQ,CAAC,CAAC;AAEjG,IAAA,MAAM,UAAU,GAA2B;AACzC,QAAA,MAAM,EAAE,kBAAkB;AAC1B,QAAA,QAAQ,EAAE,MAAM;KACjB,CAAC;;AAGF,IAAA,IAAI,kBAAkB,EAAE;QACtB,OAAO,UAAU,CAAC,QAAQ,CAAC;AAC5B,KAAA;IAED,OAAO;;AAEL,QAAA,GAAG,EAAE,CAAA,EAAG,GAAG,CAAA,EAAG,KAAK,CAAE,CAAA;AACrB,QAAA,MAAM,EAAE,KAAK;AACb,QAAA,OAAO,EAAEP,kCAAiB,CAAC,UAAU,CAAC;KACvC,CAAC;AACJ,CAAC;AAED;AACO,MAAM,kBAAkB,GAAG;AAChC,IAAA,UAAU,EAAE,CAAC;AACb,IAAA,cAAc,EAAE,GAAG;AACnB,IAAA,iBAAiB,EAAE,CAAC;CACrB,CAAC;AAEF;;AAEG;AACI,MAAM,OAAO,GAAQ;AAC1B,IAAA,IAAI,EAAE,SAAS;AACf,IAAA,MAAM,WAAW,CAAC,EAChB,MAAM,EACN,cAAc,EACd,QAAQ,EACR,UAAU,EACV,eAAe,GAAG,EAAE,GACrB,EAAA;AACC,QAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,YAAAV,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,iDAAA,CAAmD,CAAC,CAAC;AAC3E,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;;AAGD,QAAA,IAAI,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE;AACjD,YAAA,OAAO,IAAI,CAAC;AACb,SAAA;QAED,IAAI,CAAC,cAAc,EAAE;AACnB,YAAA,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC3C,SAAA;QAED,MAAM,cAAc,GAAGC,uBAAqB,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE;AAC3E,YAAA,kBAAkB,EAAE,IAAI;AACxB,YAAA,SAAS,EAAE,IAAI;AAChB,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,4CAA4C,EAC5C,eAAe,EACf,OAAO,OAAO,KAAI;;AAChB,YAAA,cAAc,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;;;;AAKvD,YAAA,MAAM,OAAO,GAAGT,sCAAqB,CAAC,cAAc,CAAC,CAAC;;;AAItD,YAAA,OAAO,CAAC,OAAO,GAAG,CAAA,CAAA,EAAA,GAAA,OAAO,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,KAAI,IAAI,CAAC;;AAG1D,YAAA,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;AACvC,YAAA,IAAI,QAA0B,CAAC;YAC/B,IAAI;AACF,gBAAAT,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,iCAAA,CAAmC,CAAC,CAAC;gBAC3D,QAAQ,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACtD,aAAA;AAAC,YAAA,OAAO,GAAY,EAAE;;;AAGrB,gBAAA,IAAIE,gBAAO,CAAC,GAAG,CAAC,EAAE;AAChB,oBAAAnB,QAAM,CAAC,OAAO,CAAC,CAAA,EAAGiB,SAAO,CAAkB,eAAA,EAAA,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,CAAA,CAAE,CAAC,CAAC;AACxE,iBAAA;;;AAGD,gBAAAjB,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,wCAAA,CAA0C,CAAC,CAAC;AAClE,gBAAA,OAAO,KAAK,CAAC;AACd,aAAA;AACD,YAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;gBAC3B,IACE,CAAA,EAAA,GAAA,QAAQ,CAAC,UAAU,0CAAE,QAAQ,CAC3B,4DAA4D,CAC7D,EACD;AACA,oBAAAjB,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,wCAAA,CAA0C,CAAC,CAAC;oBAClEjB,QAAM,CAAC,IAAI,CAAC,CAAG,EAAAiB,SAAO,CAAK,EAAA,EAAA,QAAQ,CAAC,UAAU,CAAE,CAAA,CAAC,CAAC;AAClD,oBAAA,OAAO,KAAK,CAAC;AACd,iBAAA;AACF,aAAA;;AAED,YAAAjB,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,sCAAA,CAAwC,CAAC,CAAC;AAChE,YAAA,OAAO,IAAI,CAAC;AACd,SAAC,CACF,CAAC;KACH;AACD,IAAA,MAAM,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE,EAAA;QAErC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;AAEvE,QAAA,IAAI,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE;AACjD,YAAAjB,QAAM,CAAC,IAAI,CACT,CAAA,EAAGiB,SAAO,CAAA,uGAAA,EAA0G,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAA,CAAA,CAAG,CACrK,CAAC;AACH,SAAA;AAAM,aAAA;YACLjB,QAAM,CAAC,IAAI,CAAC,CAAA,EAAGiB,SAAO,CAA2C,wCAAA,EAAA,QAAQ,CAAG,CAAA,CAAA,CAAC,CAAC;AAC/E,SAAA;AAED,QAAA,IAAI,aAAa,GAAG,kBAAkB,CAAC,cAAc,CAAC;AACtD,QAAA,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE;YACxE,IAAI;gBACF,MAAM,OAAO,GAAGR,sCAAqB,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EACnC,WAAW,EAAE,eAAe,CAAC,WAAW,EACrC,EAAAS,uBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,KACtD,uBAAuB,EAAE,IAAI,EAAA,CAAA,CAC7B,CAAC;gBACH,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBAErE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,KAAK,IAAI,CAAC;AAC7D,aAAA;AAAC,YAAA,OAAO,KAAU,EAAE;AACnB,gBAAA,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE;AAC5B,oBAAA,MAAME,cAAK,CAAC,aAAa,CAAC,CAAC;AAC3B,oBAAA,aAAa,IAAI,kBAAkB,CAAC,iBAAiB,CAAC;oBACtD,SAAS;AACV,iBAAA;AACD,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;AACF,SAAA;AAED,QAAA,MAAM,IAAI,mBAAmB,CAC3B,GAAG,EACH,CAAA,EAAGH,SAAO,CAAA,sCAAA,EAAyC,kBAAkB,CAAC,UAAU,CAAA,SAAA,CAAW,CAC5F,CAAC;KACH;CACF;;AC9MD;AAiBA,MAAMA,SAAO,GAAG,2CAA2C,CAAC;AAC5D,MAAMjB,QAAM,GAAG,gBAAgB,CAACiB,SAAO,CAAC,CAAC;AAEzC;;AAEG;AACH,SAASC,uBAAqB,CAC5B,MAAyB,EACzB,QAAiB,EACjB,UAAmB,EAAA;AAEnB,IAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,MAAM,IAAI,KAAK,CAAC,GAAGD,SAAO,CAAA,oCAAA,CAAsC,CAAC,CAAC;AACnE,KAAA;AACD,IAAA,MAAM,eAAe,GAA2B;QAC9C,QAAQ;AACR,QAAA,aAAa,EAAE,kBAAkB;KAClC,CAAC;AAEF,IAAA,IAAI,QAAQ,EAAE;AACZ,QAAA,eAAe,CAAC,SAAS,GAAG,QAAQ,CAAC;AACtC,KAAA;AACD,IAAA,IAAI,UAAU,EAAE;AACd,QAAA,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;AACzC,KAAA;;AAGD,IAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;AAClC,QAAA,MAAM,IAAI,KAAK,CAAC,GAAGA,SAAO,CAAA,iDAAA,CAAmD,CAAC,CAAC;AAChF,KAAA;AAED,IAAA,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,eAAe,CAAC,CAAC;AAEnD,IAAA,OAAOR,sCAAqB,CAAC;;AAE3B,QAAA,GAAG,EAAE,CAAA,EAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAA,CAAA,EAAI,KAAK,CAAC,QAAQ,EAAE,CAAE,CAAA;AAC3D,QAAA,MAAM,EAAE,KAAK;QACb,OAAO,EAAEC,kCAAiB,CAAC;AACzB,YAAA,MAAM,EAAE,kBAAkB;AAC1B,YAAA,QAAQ,EAAE,MAAM;SACjB,CAAC;AACH,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;AAGG;AACH,SAASW,eAAa,CAAC,IAAY,EAAE,OAAqC,EAAA;IACxE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KACjCC,WAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,IAAI,KAAI;AACpC,QAAA,IAAI,GAAG,EAAE;YACP,MAAM,CAAC,GAAG,CAAC,CAAC;AACb,SAAA;QACD,OAAO,CAAC,IAAI,CAAC,CAAC;KACf,CAAC,CACH,CAAC;AACJ,CAAC;AAED;;AAEG;AACH,eAAe,eAAe,CAC5B,cAA8B,EAC9B,qBAA6C,EAAA;AAE7C,IAAA,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,WAAW,CAACb,sCAAqB,CAAC,qBAAqB,CAAC,CAAC,CAAC;AAEhG,IAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;QAC3B,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,QAAQ,CAAC,UAAU,EAAE;AACvB,YAAA,OAAO,GAAG,CAAc,WAAA,EAAA,QAAQ,CAAC,UAAU,EAAE,CAAC;AAC/C,SAAA;AACD,QAAA,MAAM,IAAI,mBAAmB,CAC3B,QAAQ,CAAC,MAAM,EACf,CAAA,EAAGQ,SAAO,CAAA,wFAAA,EAA2F,OAAO,CAAA,CAAE,CAC/G,CAAC;AACH,KAAA;AAED,IAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;IAClE,IAAI;AACF,QAAA,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,KAAA;AAAC,IAAA,OAAO,CAAM,EAAE;AACf,QAAA,MAAM,KAAK,CAAC,CAAA,wCAAA,EAA2C,UAAU,CAAA,CAAE,CAAC,CAAC;AACtE,KAAA;AACH,CAAC;AAED;;AAEG;AACI,MAAM,MAAM,GAAQ;AACzB,IAAA,IAAI,EAAE,KAAK;AACX,IAAA,MAAM,WAAW,CAAC,EAAE,MAAM,EAAE,EAAA;AAC1B,QAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,YAAAjB,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,iDAAA,CAAmD,CAAC,CAAC;AAC3E,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACD,QAAA,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACnF,IAAI,CAAC,MAAM,EAAE;AACX,YAAAjB,QAAM,CAAC,IAAI,CACT,GAAGiB,SAAO,CAAA,2EAAA,CAA6E,CACxF,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AACD,IAAA,MAAM,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE,EAAA;;QAErC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;AAEvE,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAAjB,QAAM,CAAC,OAAO,CACZ,GAAGiB,SAAO,CAAA,gGAAA,CAAkG,CAC7G,CAAC;AACH,SAAA;AACD,QAAA,IAAI,UAAU,EAAE;AACd,YAAAjB,QAAM,CAAC,OAAO,CACZ,GAAGiB,SAAO,CAAA,qGAAA,CAAuG,CAClH,CAAC;AACH,SAAA;AAED,QAAAjB,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,iBAAA,CAAmB,CAAC,CAAC;AAE3C,QAAA,MAAM,cAAc,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAClB,0BAA0B,EAAE,IAAI,EAChC,qBAAqB,EAAE,SAAS,EAChC,WAAW,EAAE,eAAe,CAAC,WAAW,EAAA,EACrCC,uBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CACtD,EAAA,EAAA,uBAAuB,EAAE,IAAI,GAC9B,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;QAEvE,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,MAAM,IAAI,KAAK,CAAC,GAAGD,SAAO,CAAA,gCAAA,CAAkC,CAAC,CAAC;AAC/D,SAAA;AAED,QAAA,MAAM,GAAG,GAAG,MAAMI,eAAa,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;AACjE,QAAA,CAAA,EAAA,GAAA,cAAc,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,GAAG,CAAC,eAAe,EAAE,CAAS,MAAA,EAAA,GAAG,CAAE,CAAA,CAAC,CAAC;AAE7D,QAAA,MAAM,OAAO,GAAGZ,sCAAqB,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAChC,cAAc,CAAA,EAAA;;YAEjB,uBAAuB,EAAE,IAAI,EAAA,CAAA,CAC7B,CAAC;QACH,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACrE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,KAAK,IAAI,CAAC;KAC7D;CACF;;ACxKD;AAmBA;;;AAGG;AACG,MAAO,mBAAoB,SAAQ,QAAQ,CAAA;AAE/C,IAAA,WAAA,CAAY,OAAmC,EAAA;QAC7C,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACjC,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;KAC1C;AAES,IAAA,MAAM,UAAU,CACxB,MAAgB,EAChB,UAAyC,EAAE,EAAA;QAE3C,IAAI;AACF,YAAA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5C,YAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAC9B,cAAc,EACd,OAAO,CAAC,SAAS,CAClB,CAAC,8BAA8B,CAAC;gBAC/B,MAAM;gBACN,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;AACtB,gBAAA,eAAe,EAAE,SAAS;AAC3B,aAAA,CAAC,CAAC;;;AAGH,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;AACtE,SAAA;AAAC,QAAA,OAAO,GAAY,EAAE;YACrB,IAAI,IAAI,GAAG,GAAG,CAAC;AACf,YAAA,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;gBACrC,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,aAAA;AAAM,iBAAA;gBACL,IAAI,GAAGU,gBAAO,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACpD,aAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAa,EAAE,OAAO,CAAC,CAAC;AACxD,SAAA;KACF;AACF;;AC7DD;AAcA,MAAMnB,QAAM,GAAG,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;AAE7D;;AAEG;MACU,yBAAyB,CAAA;AAOpC;;;;;;;;;AASG;AACH,IAAA,WAAA,CACE,QAAgB,EAChB,QAAgB,EAChB,YAAmC,EACnC,UAA4C,EAAE,EAAA;QAE9C,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE;AAC3C,YAAA,MAAM,IAAI,KAAK,CACb,6FAA6F,CAC9F,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;AACF,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,mBAAmB,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAClC,OAAO,CAAA,EAAA,UACVA,QAAM,EACN,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,sBAAsB,EAAE,IAAI,CAAC,OAAO,EACpC,YAAY,IACZ,CAAC;KACJ;AAED;;;;;;;AAOG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE,EAAA;AACrE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,CAAG,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA,SAAA,CAAW,EACnC,OAAO,EACP,OAAO,UAAU,KAAI;AACnB,YAAA,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjCA,QAAM,CACP,CAAC;AAEF,YAAA,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AACzD,SAAC,CACF,CAAC;KACH;AACF;;ACxFD;AAWA,MAAMuB,gBAAc,GAAG,4BAA4B,CAAC;AACpD;;;;;;AAMG;AACI,MAAM,qCAAqC,GAAG;IACnD,iBAAiB;IACjB,iBAAiB;IACjB,4BAA4B;CAC7B,CAAC;AACF,MAAMvB,QAAM,GAAG,gBAAgB,CAACuB,gBAAc,CAAC,CAAC;AAChD;;;;;;;;;;;;;AAaG;MACU,0BAA0B,CAAA;AAMrC;;;;AAIG;AACH,IAAA,WAAA,CAAY,OAA2C,EAAA;QAT/C,IAA8B,CAAA,8BAAA,GAAuB,SAAS,CAAC;QAC/D,IAAS,CAAA,SAAA,GAAuB,SAAS,CAAC;;AAUhD,QAAA,MAAM,WAAW,GAAG,cAAc,CAAC,qCAAqC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9F,QAAAvB,QAAM,CAAC,IAAI,CAAC,8CAA8C,WAAW,CAAA,CAAE,CAAC,CAAC;QAEzE,MAAM,iCAAiC,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,OAAO,GAAI,EAAE,CAAC;QACxD,MAAM,QAAQ,GAAG,iCAAiC,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;QAC3F,MAAM,QAAQ,GAAG,iCAAiC,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;AAC3F,QAAA,IAAI,CAAC,sBAAsB;YACzB,iCAAiC,CAAC,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC5F,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,aAAa,CAACA,QAAM,EAAE,QAAQ,CAAC,CAAC;AACjC,SAAA;AACD,QAAA,IAAI,QAAQ,IAAI,QAAQ,IAAI,IAAI,CAAC,sBAAsB,EAAE;YACvDA,QAAM,CAAC,IAAI,CACT,CAAsD,mDAAA,EAAA,QAAQ,CAAe,YAAA,EAAA,iCAAiC,CAAC,QAAQ,CAAuC,qCAAA,CAAA,CAC/J,CAAC;YACF,IAAI,CAAC,MAAM,GAAG,IAAI,yBAAyB,CACzC,QAAQ,EACR,QAAQ,EACR,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAChC,OAAO,CACR,CAAC;AACH,SAAA;KACF;AAED;;;;;;;AAOG;AACI,IAAA,MAAM,QAAQ,CACnB,MAAyB,EACzB,OAAyB,EAAA;AAEzB,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,YAAY,GAAG,CAAA,EAAGuB,gBAAc,CAAA;;;;mKAIuH,CAAC;AAC9J,YAAAvB,QAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC1B,YAAA,MAAM,IAAI,0BAA0B,CAAC,YAAY,CAAC,CAAC;AACpD,SAAA;AACD,QAAAA,QAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAClE,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KAC9C;AAEO,IAAA,MAAM,gBAAgB,GAAA;;QAE5B,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE;AAChF,YAAA,IAAI,CAAC,8BAA8B,GAAG,SAAS,CAAC;AACjD,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAChC,MAAM,IAAI,0BAA0B,CAClC,CAAG,EAAAuB,gBAAc,CAAgD,6CAAA,EAAA,IAAI,CAAC,sBAAsB,CAAG,CAAA,CAAA,CAChG,CAAC;AACH,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE;YACxC,MAAM,IAAI,GAAG,MAAMD,iBAAQ,CAAC,IAAI,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;AACjE,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,EAAE;gBACV,MAAM,IAAI,0BAA0B,CAClC,CAAG,EAAAC,gBAAc,CAA4C,yCAAA,EAAA,IAAI,CAAC,sBAAsB,CAAG,CAAA,CAAA,CAC5F,CAAC;AACH,aAAA;AAAM,iBAAA;AACL,gBAAA,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;AAC5C,gBAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAC7B,aAAA;AACF,SAAA;QACD,OAAO,IAAI,CAAC,8BAA8B,CAAC;KAC5C;AACF;;AC7HD;AASA,MAAMN,SAAO,GAAG,4CAA4C,CAAC;AAC7D,MAAMjB,QAAM,GAAG,gBAAgB,CAACiB,SAAO,CAAC,CAAC;AAEzC;;AAEG;SACa,gBAAgB,GAAA;IAC9B,OAAO;AACL,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,MAAM,WAAW,CAAC,EAAE,QAAQ,EAAE,EAAA;AAC5B,YAAA,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;YACxB,MAAM,MAAM,GAAG,OAAO,CACpB,CAAC,QAAQ,IAAI,GAAG,CAAC,eAAe;AAC9B,gBAAA,GAAG,CAAC,eAAe;AACnB,gBAAA,OAAO,CAAC,GAAG,CAAC,0BAA0B,CACzC,CAAC;YACF,IAAI,CAAC,MAAM,EAAE;AACX,gBAAAjB,QAAM,CAAC,IAAI,CACT,GAAGiB,SAAO,CAAA,mKAAA,CAAqK,CAChL,CAAC;AACH,aAAA;AACD,YAAA,OAAO,MAAM,CAAC;SACf;AACD,QAAA,MAAM,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE,EAAA;AAErC,YAAA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,aAAa,CAAC;YAC3C,MAAM,oCAAoC,GAAG,EAAE,CAAC;AAChD,YAAA,MAAM,0BAA0B,GAAG,IAAI,0BAA0B,CAAC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAChE,QAAQ,EACR,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EACrC,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAA,EAClD,oCAAoC,CAAA,EAAA,EACvC,wBAAwB,EAAE,IAAI,EAAA,CACM,CAAC,CAAC;YACxC,MAAM,KAAK,GAAG,MAAM,0BAA0B,CAAC,QAAQ,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;AACjF,YAAA,OAAO,KAAK,CAAC;SACd;KACF,CAAC;AACJ;;ACjDA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAMA,SAAO,GAAG,wCAAwC,CAAC;AACzD,MAAMjB,QAAM,GAAG,gBAAgB,CAACiB,SAAO,CAAC,CAAC;AAEzC;;AAEG;AACH,SAASC,uBAAqB,CAC5B,MAAyB,EACzB,QAAiB,EACjB,UAAmB,EAAA;AAEnB,IAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,MAAM,IAAI,KAAK,CAAC,GAAGD,SAAO,CAAA,oCAAA,CAAsC,CAAC,CAAC;AACnE,KAAA;AAED,IAAA,MAAM,eAAe,GAA2B;QAC9C,QAAQ;AACR,QAAA,aAAa,EAAE,kBAAkB;KAClC,CAAC;AAEF,IAAA,IAAI,QAAQ,EAAE;AACZ,QAAA,eAAe,CAAC,SAAS,GAAG,QAAQ,CAAC;AACtC,KAAA;AACD,IAAA,IAAI,UAAU,EAAE;AACd,QAAA,eAAe,CAAC,UAAU,GAAG,UAAU,CAAC;AACzC,KAAA;AACD,IAAA,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,eAAe,CAAC,CAAC;;AAGnD,IAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;AAClC,QAAA,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;AACpE,KAAA;AACD,IAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE;AAChC,QAAA,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;AAClE,KAAA;IAED,OAAO;AACL,QAAA,GAAG,EAAE,CAAA,EAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAA,CAAA,EAAI,KAAK,CAAC,QAAQ,EAAE,CAAE,CAAA;AAC3D,QAAA,MAAM,EAAE,KAAK;QACb,OAAO,EAAEP,kCAAiB,CAAC;AACzB,YAAA,MAAM,EAAE,kBAAkB;AAC1B,YAAA,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;SACpC,CAAC;KACH,CAAC;AACJ,CAAC;AAED;;AAEG;AACI,MAAM,SAAS,GAAQ;AAC5B,IAAA,IAAI,EAAE,WAAW;AACjB,IAAA,MAAM,WAAW,CAAC,EAAE,MAAM,EAAE,EAAA;AAC1B,QAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,YAAAV,QAAM,CAAC,IAAI,CAAC,GAAGiB,SAAO,CAAA,iDAAA,CAAmD,CAAC,CAAC;AAC3E,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACxB,QAAA,MAAM,MAAM,GAAG,OAAO,CACpB,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,eAAe,IAAI,GAAG,CAAC,0BAA0B,CAC/E,CAAC;QACF,IAAI,CAAC,MAAM,EAAE;AACX,YAAAjB,QAAM,CAAC,IAAI,CACT,GAAGiB,SAAO,CAAA,sHAAA,CAAwH,CACnI,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AACD,IAAA,MAAM,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE,EAAA;QAErC,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;AAEvE,QAAA,IAAI,UAAU,EAAE;AACd,YAAAjB,QAAM,CAAC,OAAO,CACZ,GAAGiB,SAAO,CAAA,qHAAA,CAAuH,CAClI,CAAC;AACH,SAAA;QAEDjB,QAAM,CAAC,IAAI,CACT;AACE,YAAA,CAAA,EAAGiB,SAAO,CAAG,CAAA,CAAA;YACb,0EAA0E;AAC1E,YAAA,CAAA,kBAAA,EAAqB,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAG,CAAA,CAAA;YACrD,gCAAgC;YAChC,wCAAwC;AACzC,SAAA,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;AAEF,QAAA,MAAM,OAAO,GAAGR,sCAAqB,iBACnC,WAAW,EAAE,eAAe,CAAC,WAAW,IACrCS,uBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,EAGtD,CAAC;AAEH,QAAA,OAAO,CAAC,KAAK,GAAG,IAAIM,yBAAK,CAAC,KAAK,CAAC;;;AAG9B,YAAA,kBAAkB,EAAE,KAAK;AAC1B,SAAA,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACrE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,KAAK,IAAI,CAAC;KAC7D;CACF;;ACrID;AAaA,MAAM,OAAO,GAAG,gDAAgD,CAAC;AACjE,MAAMxB,QAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAEzC;;AAEG;AACH,SAAS,qBAAqB,CAC5B,MAAyB,EACzB,QAAiB,EACjB,UAAmB,EAAA;AAEnB,IAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,CAAA,oCAAA,CAAsC,CAAC,CAAC;AACnE,KAAA;AAED,IAAA,MAAM,eAAe,GAA2B;QAC9C,QAAQ;AACR,QAAA,aAAa,EAAE,YAAY;KAC5B,CAAC;AAEF,IAAA,IAAI,QAAQ,EAAE;AACZ,QAAA,eAAe,CAAC,SAAS,GAAG,QAAQ,CAAC;AACtC,KAAA;AAED,IAAA,IAAI,UAAU,EAAE;AACd,QAAA,eAAe,CAAC,SAAS,GAAG,UAAU,CAAC;AACxC,KAAA;AACD,IAAA,MAAM,KAAK,GAAG,IAAI,eAAe,CAAC,eAAe,CAAC,CAAC;;AAGnD,IAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;AAClC,QAAA,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,CAAA,iDAAA,CAAmD,CAAC,CAAC;AAChF,KAAA;AACD,IAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE;AAChC,QAAA,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,CAAA,+CAAA,CAAiD,CAAC,CAAC;AAC9E,KAAA;IAED,OAAO;AACL,QAAA,GAAG,EAAE,CAAA,EAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAA,CAAA,EAAI,KAAK,CAAC,QAAQ,EAAE,CAAE,CAAA;AAC3D,QAAA,MAAM,EAAE,KAAK;QACb,OAAO,EAAEU,kCAAiB,CAAC;AACzB,YAAA,MAAM,EAAE,kBAAkB;AAC1B,YAAA,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;SACjD,CAAC;KACH,CAAC;AACJ,CAAC;AAED;;AAEG;AACI,MAAM,iBAAiB,GAAQ;AACpC,IAAA,IAAI,EAAE,mBAAmB;AACzB,IAAA,MAAM,WAAW,CAAC,EAAE,MAAM,EAAE,EAAA;AAC1B,QAAA,MAAM,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;AACb,YAAAV,QAAM,CAAC,IAAI,CAAC,GAAG,OAAO,CAAA,iDAAA,CAAmD,CAAC,CAAC;AAC3E,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACD,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACxB,QAAA,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,eAAe,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,EAAE;AACX,YAAAA,QAAM,CAAC,IAAI,CACT,GAAG,OAAO,CAAA,2FAAA,CAA6F,CACxG,CAAC;AACH,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACf;AACD,IAAA,MAAM,QAAQ,CACZ,aAA+B,EAC/B,kBAAmC,EAAE,EAAA;QAErC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,aAAa,CAAC;AAEvE,QAAAA,QAAM,CAAC,IAAI,CACT,CAAA,EAAG,OAAO,CAAA,6FAAA,EAAgG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAA,gCAAA,CAAkC,CAC1K,CAAC;AAEF,QAAA,MAAM,OAAO,GAAGS,sCAAqB,CACnC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,WAAW,EAAE,eAAe,CAAC,WAAW,EAAA,EACrC,qBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA,EAAA;;YAEtD,uBAAuB,EAAE,IAAI,EAAA,CAAA,CAC7B,CAAC;QACH,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACrE,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC,WAAW,KAAK,IAAI,CAAC;KAC7D;CACF;;ACpGD;AA4BA,MAAMT,QAAM,GAAG,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;AA4B7D;;;;;;;AAOG;MACU,yBAAyB,CAAA;AA6BpC;;;AAGG;IACH,WACE,CAAA,iBAG8C,EAC9C,OAAgC,EAAA;;QAlC1B,IAAqB,CAAA,qBAAA,GAAmB,IAAI,CAAC;QAG7C,IAA6B,CAAA,6BAAA,GAAY,KAAK,CAAC;AAiCrD,QAAA,IAAI,QAA4C,CAAC;AACjD,QAAA,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;AACzC,YAAA,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC;YAClC,QAAQ,GAAG,OAAO,CAAC;AACpB,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,QAAQ,GAAI,iBAA8D,KAAA,IAAA,IAA9D,iBAAiB,KAAjB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,iBAAiB,CAA+C,QAAQ,CAAC;YAC1F,QAAQ,GAAG,iBAAiB,CAAC;AAC9B,SAAA;QACD,IAAI,CAAC,UAAU,GAAI,QAAuD,KAAA,IAAA,IAAvD,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAiD,UAAU,CAAC;;AAEvF,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE;YACpC,MAAM,IAAI,KAAK,CACb,CAAA,EAAG,yBAAyB,CAAC,IAAI,CAAkE,gEAAA,CAAA,CACpG,CAAC;AACH,SAAA;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAI,CAAC,yBAAyB,GAAG,IAAI,cAAc,CAC9C,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,QAAQ,CACX,EAAA,EAAA,YAAY,EAAE;AACZ,gBAAA,UAAU,EAAE,CAAC;AACd,aAAA,EAAA,CAAA,CACD,CAAC;AAEH;;AAEG;AACH,QAAA,IAAI,CAAC,eAAe,GAAG,IAAIyB,wCAA6B,CAAC;AACvD,YAAA,IAAI,EAAE;AACJ,gBAAA,SAAS,EAAE,oDAAoD;AAC/D,gBAAA,QAAQ,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,mCAAI,uBAAuB;AAClD,gBAAA,YAAY,EAAE,cAAc;AAC5B,gBAAA,sBAAsB,EACpB,w7BAAw7B;AAC17B,gBAAA,iBAAiB,EACf,6gDAA6gD;AAC/gD,gBAAA,kBAAkB,EAAE,EAAE;AACvB,aAAA;AACD,YAAA,MAAM,EAAE;AACN,gBAAA,aAAa,EAAE;AACb,oBAAA,QAAQ,EAAE,eAAe,CAACb,oBAAW,EAAE,CAAC;AACzC,iBAAA;AACF,aAAA;AACF,SAAA,CAAC,CAAC;KACJ;AAIO,IAAA,MAAM,kBAAkB,CAC9B,MAAyB,EACzB,eAAiC,EAAA;QAEjC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;AACvB,SAAA;AAED,QAAA,MAAM,IAAI,GAAG;YACX,MAAM;YACN,SAAS;YACT,iBAAiB;YACjB,iBAAiB;YACjB,aAAa;AACb,YAAA,gBAAgB,EAAE;YAClB,OAAO;SACR,CAAC;AAEF,QAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;AACtB,YAAA,IACE,MAAM,GAAG,CAAC,WAAW,CAAC;gBACpB,MAAM;gBACN,cAAc,EAAE,IAAI,CAAC,yBAAyB;gBAC9C,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,eAAe;AAChB,aAAA,CAAC,EACF;AACA,gBAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AACrB,gBAAA,OAAO,GAAG,CAAC;AACZ,aAAA;AACF,SAAA;QAED,MAAM,IAAI,0BAA0B,CAClC,CAAA,EAAG,yBAAyB,CAAC,IAAI,CAAgC,8BAAA,CAAA,CAClE,CAAC;KACH;AAEO,IAAA,MAAM,2BAA2B,CACvC,MAAyB,EACzB,eAAiC,EAAA;AAEjC,QAAA,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,aAAa,CAAC,SAAS,CACtD,CAAA,EAAG,yBAAyB,CAAC,IAAI,8BAA8B,EAC/D,eAAe,CAChB,CAAC;QAEF,IAAI;;YAEF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAC3E,OAAO,YAAY,CAAC,QAAQ,CAC1B;gBACE,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,MAAM;gBACN,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,EACD,cAAc,CACf,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;YACjB,IAAI,CAAC,SAAS,CAAC;AACb,gBAAA,MAAM,EAAE,OAAO;AACf,gBAAA,KAAK,EAAE,GAAG;AACX,aAAA,CAAC,CAAC;AACH,YAAA,MAAM,GAAG,CAAC;AACX,SAAA;AAAS,gBAAA;YACR,IAAI,CAAC,GAAG,EAAE,CAAC;AACZ,SAAA;KACF;AAED;;;;;;;;AAQG;AACI,IAAA,MAAM,QAAQ,CACnB,MAAyB,EACzB,OAAyB,EAAA;QAEzB,IAAI,MAAM,GAAuB,IAAI,CAAC;AACtC,QAAA,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,aAAa,CAAC,SAAS,CACtD,CAAA,EAAG,yBAAyB,CAAC,IAAI,WAAW,EAC5C,OAAO,CACR,CAAC;QACF,IAAI;;;;AAIF,YAAA,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE;gBACvC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAC3E,gBAAA,IAAI,YAAY,CAAC,IAAI,KAAK,kBAAkB,EAAE;oBAC5C,MAAM,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AACzE,iBAAA;AAAM,qBAAA;AACL,oBAAA,MAAM,kBAAkB,GAA+B;AACrD,wBAAA,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE;wBACrD,QAAQ,EAAE,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,QAAQ,KAAI,kBAAkB;AACjD,wBAAA,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC;AACjD,wBAAA,MAAM,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,MAAM;qBACxB,CAAC;;oBAGF,IAAI,CAAC,6BAA6B,EAAE,CAAC;oBACrC,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,8BAA8B,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACjF,kBAAkB,CAAA,CACrB,CAAC;oBACH,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,oBAAoB,IAAI,SAAS,CAAC,CAAC;AACvE,iBAAA;gBACD,IAAI,MAAM,KAAK,IAAI,EAAE;;;;AAInB,oBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;;;AAIlC,oBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,yEAAyE,CAC1E,CAAC;AACF,oBAAAZ,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,oBAAA,MAAM,KAAK,CAAC;AACb,iBAAA;;;;AAKD,gBAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AACpC,aAAA;AAAM,iBAAA;;;AAGL,gBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,0DAA0D,CAC3D,CAAC;AACF,gBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;YAEDA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5C,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;;;AAGjB,YAAA,IAAI,GAAG,CAAC,IAAI,KAAK,6BAA6B,EAAE;AAC9C,gBAAA,MAAM,GAAG,CAAC;AACX,aAAA;;;;;;YAQD,IAAI,CAAC,SAAS,CAAC;AACb,gBAAA,MAAM,EAAE,OAAO;AACf,gBAAA,KAAK,EAAE,GAAG;AACX,aAAA,CAAC,CAAC;;;AAIH,YAAA,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,EAAE;AAC9B,gBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,CAAG,EAAA,yBAAyB,CAAC,IAAI,gDAAgD,GAAG,CAAC,OAAO,CAAA,CAAE,CAC/F,CAAC;AAEF,gBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;;;AAID,YAAA,IAAI,GAAG,CAAC,IAAI,KAAK,cAAc,EAAE;AAC/B,gBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,CAAG,EAAA,yBAAyB,CAAC,IAAI,+DAA+D,GAAG,CAAC,OAAO,CAAA,CAAE,CAC9G,CAAC;AAEF,gBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;;;AAGD,YAAA,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE;AAC1B,gBAAA,MAAM,IAAI,0BAA0B,CAClC,CAAA,EAAG,yBAAyB,CAAC,IAAI,CAAA,sFAAA,EAAyF,GAAG,CAAC,OAAO,CAAA,CAAE,CACxI,CAAC;AACH,aAAA;;;YAID,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE;gBAC9C,IAAI,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,4DAA4D,CAAC,EAAE;AACtF,oBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,CAAG,EAAA,yBAAyB,CAAC,IAAI,gDAAgD,GAAG,CAAC,OAAO,CAAA,CAAE,CAC/F,CAAC;AAEF,oBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,oBAAA,MAAM,KAAK,CAAC;AACb,iBAAA;AACF,aAAA;;;AAID,YAAA,IAAI,GAAG,CAAC,UAAU,KAAK,SAAS,EAAE;AAChC,gBAAA,MAAM,IAAI,0BAA0B,CAClC,CAAA,EAAG,yBAAyB,CAAC,IAAI,CAAA,iCAAA,EAAoC,GAAG,CAAC,OAAO,CAAA,CAAE,CACnF,CAAC;AACH,aAAA;;AAGD,YAAA,MAAM,IAAI,mBAAmB,CAAC,GAAG,CAAC,UAAU,EAAE;AAC5C,gBAAA,KAAK,EAAE,CAAA,EAAG,yBAAyB,CAAC,IAAI,CAAyB,uBAAA,CAAA;gBACjE,iBAAiB,EAAE,GAAG,CAAC,OAAO;AAC/B,aAAA,CAAC,CAAC;AACJ,SAAA;AAAS,gBAAA;;YAER,IAAI,CAAC,GAAG,EAAE,CAAC;AACZ,SAAA;KACF;AAED;;;;AAIG;AACK,IAAA,YAAY,CAClB,MAAyB,EACzB,MAAmB,EACnB,eAAiC,EAAA;QAEjC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;QAC3DA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5C,OAAO;YACL,KAAK,EAAE,MAAO,CAAC,WAAY;AAC3B,YAAA,kBAAkB,EAAE,MAAO,CAAC,SAAU,CAAC,OAAO,EAAE;SACjD,CAAC;KACH;AAED;;;AAGG;AACK,IAAA,oBAAoB,CAC1B,MAAyB,EACzB,SAAqB,EACrB,eAAiC,EAAA;AAEjC,QAAA,MAAM,KAAK,GAAG,CAAC,OAAe,KAAW;AACvC,YAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,OAAO,IAAI,2BAA2B,CAAC;AACrC,gBAAA,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC;gBACjD,eAAe;gBACf,OAAO;AACR,aAAA,CAAC,CAAC;AACL,SAAC,CAAC;QACF,IAAI,CAAC,SAAS,EAAE;AACd,YAAA,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;AAC5B,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AACxB,YAAA,MAAM,KAAK,CAAC,CAAuC,qCAAA,CAAA,CAAC,CAAC;AACtD,SAAA;AACD,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AAC1B,YAAA,MAAM,KAAK,CAAC,CAAyC,uCAAA,CAAA,CAAC,CAAC;AACxD,SAAA;KACF;IAEO,6BAA6B,GAAA;AACnC,QAAA,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACvC,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,OAAO,0BAA0B,KAAI;AAC5E,gBAAAA,QAAM,CAAC,IAAI,CACT,CAAA,6CAAA,EAAgD,IAAI,CAAC,SAAS,CAC5D,0BAA0B,CAC3B,CAAE,CAAA,CACJ,CAAC;AACF,gBAAA,MAAM,eAAe,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAChB,0BAA0B,CAC9B,CAAC;gBACFA,QAAM,CAAC,IAAI,CACT,CAAA,iDAAA,EAAoD,IAAI,CAAC,SAAS,CAChE,0BAA0B,CAAC,MAAM,CAClC,CAAA,uBAAA,EAA0B,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAE,CAAA,CAC7D,CAAC;AACF,gBAAA,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,2BAA2B,CACxD,0BAA0B,CAAC,MAAM,EACjC,eAAe,CAChB,CAAC;AAEF,gBAAA,IAAI,WAAW,EAAE;AACf,oBAAAA,QAAM,CAAC,IAAI,CAAC,CAAA,gDAAA,CAAkD,CAAC,CAAC;oBAEhE,MAAM,gBAAgB,GAAG,CAAA,WAAW,aAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,kBAAkB;AACtD,0BAAE,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC;0BAChE,CAAC,CAAC;oBACN,OAAO;AACL,wBAAA,WAAW,EAAE,WAAW,KAAA,IAAA,IAAX,WAAW,KAAX,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,WAAW,CAAE,KAAK;wBAC/B,gBAAgB;qBACjB,CAAC;AACH,iBAAA;AAAM,qBAAA;AACL,oBAAAA,QAAM,CAAC,IAAI,CACT,CAAA,2EAAA,CAA6E,CAC9E,CAAC;oBACF,OAAO;AACL,wBAAA,WAAW,EAAE,0BAA0B;AACvC,wBAAA,gBAAgB,EAAE,CAAC;qBACpB,CAAC;AACH,iBAAA;AACH,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;AAC3C,SAAA;KACF;AACF;;AC9cD;AAKA;;;AAGG;AACG,SAAU,YAAY,CAAC,MAAyB,EAAA;AACpD,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;AACnD,CAAC;AAED;;;AAGG;AACa,SAAA,+BAA+B,CAAC,KAAa,EAAE,MAAwB,EAAA;AACrF,IAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE;AACvC,QAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;AACrF,QAAA,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAChD,QAAA,MAAM,KAAK,CAAC;AACb,KAAA;AACH,CAAC;AAED;;;AAGG;AACG,SAAU,gBAAgB,CAAC,KAAa,EAAA;IAC5C,OAAO,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;AAC1C;;AC/BA;AAgBA;;;AAGG;AACI,MAAM,sBAAsB,GAAG;AACpC;;AAEG;IACH,iBAAiB,GAAA;AACf,QAAA,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;AAChC,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;AAC3B,gBAAA,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;AACrF,aAAA;AACD,YAAA,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;AAC/B,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;KACF;AAED;;;;AAIG;AACH,IAAA,MAAM,sBAAsB,CAC1B,QAAgB,EAChB,QAAiB,EACjB,OAAgB,EAAA;QAEhB,IAAI,aAAa,GAAa,EAAE,CAAC;AACjC,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,aAAa,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AACxC,SAAA;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;YACrC,IAAI;AACF,gBAAA0B,iCAAa,CAAC,QAAQ,CACpB,IAAI,EACJ;oBACE,SAAS;oBACT,kBAAkB;oBAClB,UAAU;oBACV,MAAM;oBACN,YAAY;oBACZ,QAAQ;AACR,oBAAA,GAAG,aAAa;iBACjB,EACD,EAAE,GAAG,EAAE,sBAAsB,CAAC,iBAAiB,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,EACzE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,KAAI;AACxB,oBAAA,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AACrD,iBAAC,CACF,CAAC;AACH,aAAA;AAAC,YAAA,OAAO,GAAQ,EAAE;gBACjB,MAAM,CAAC,GAAG,CAAC,CAAC;AACb,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;CACF,CAAC;AAEF,MAAM1B,QAAM,GAAG,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;AAEtD;;;;;AAKG;MACU,kBAAkB,CAAA;AAK7B;;;;;;;AAOG;AACH,IAAA,WAAA,CAAY,OAAmC,EAAA;AAC7C,QAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,QAAQ,EAAE;YACrB,aAAa,CAACA,QAAM,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,CAAC;AACnC,SAAA;AACD,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;QACF,IAAI,CAAC,OAAO,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,kBAAkB,CAAC;KAC5C;AAED;;;;;;;AAOG;AACI,IAAA,MAAM,QAAQ,CACnB,MAAyB,EACzB,UAA2B,EAAE,EAAA;AAE7B,QAAA,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,OAAO,EACP,IAAI,CAAC,4BAA4B,CAClC,CAAC;AAEF,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,aAAa,CAACA,QAAM,EAAE,QAAQ,CAAC,CAAC;AACjC,SAAA;AACD,QAAA,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC9DA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAmB,gBAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;AAEjD,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAA,EAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EAAE,OAAO,EAAE,YAAW;;YACrF,IAAI;AACF,gBAAA,+BAA+B,CAAC,KAAK,EAAEA,QAAM,CAAC,CAAC;AAC/C,gBAAA,MAAM,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACzC,gBAAA,MAAM,GAAG,GAAG,MAAM,sBAAsB,CAAC,sBAAsB,CAC7D,QAAQ,EACR,QAAQ,EACR,IAAI,CAAC,OAAO,CACb,CAAC;gBACF,MAAM,aAAa,GAAG,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC,0BAA0B,CAAC,CAAC;AACpE,gBAAA,MAAM,YAAY,GAAG,CAAA,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAC,kBAAkB,CAAC,KAAI,CAAC,aAAa,CAAC;gBAC7E,MAAM,iBAAiB,GACrB,CAAA,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAC,kBAAkB,CAAC,MAAI,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,UAAU,CAAC,wBAAwB,CAAC,CAAA,CAAC;AAE5F,gBAAA,IAAI,iBAAiB,EAAE;AACrB,oBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,kLAAkL,CACnL,CAAC;AACF,oBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,oBAAA,MAAM,KAAK,CAAC;AACb,iBAAA;AACD,gBAAA,IAAI,YAAY,EAAE;AAChB,oBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,2FAA2F,CAC5F,CAAC;AACF,oBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,oBAAA,MAAM,KAAK,CAAC;AACb,iBAAA;gBACD,IAAI;AACF,oBAAA,MAAM,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC;oBAChC,MAAM,QAAQ,GAA+C,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBACtFA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5C,oBAAA,MAAM,WAAW,GAAG;wBAClB,KAAK,EAAE,QAAQ,CAAC,WAAW;wBAC3B,kBAAkB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;qBAC3D,CAAC;AACF,oBAAA,OAAO,WAAW,CAAC;AACpB,iBAAA;AAAC,gBAAA,OAAO,CAAM,EAAE;oBACf,IAAI,GAAG,CAAC,MAAM,EAAE;AACd,wBAAA,MAAM,IAAI,0BAA0B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClD,qBAAA;AACD,oBAAA,MAAM,CAAC,CAAC;AACT,iBAAA;AACF,aAAA;AAAC,YAAA,OAAO,GAAQ,EAAE;AACjB,gBAAA,MAAM,KAAK,GACT,GAAG,CAAC,IAAI,KAAK,4BAA4B;AACvC,sBAAE,GAAG;sBACH,IAAI,0BAA0B,CAC3B,GAAa,CAAC,OAAO,IAAI,yDAAyD,CACpF,CAAC;AACR,gBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;AACF;;ACzLD;AAKA;;;AAGG;AACI,MAAM,YAAY,GAAG;AAC1B;;;AAGG;AACH,IAAA,QAAQ,CACN,IAAY,EACZ,MAAgB,EAChB,OAAwD,EAAA;QAExD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,YAAA2B,wBAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,KAAI;AACrE,gBAAA,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC3B,oBAAA,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAClC,iBAAA;AACD,gBAAA,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC3B,oBAAA,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAClC,iBAAA;gBACD,IAAI,MAAM,IAAI,KAAK,EAAE;AACnB,oBAAA,MAAM,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;AAC5C,iBAAA;AAAM,qBAAA;oBACL,OAAO,CAAC,MAAM,CAAC,CAAC;AACjB,iBAAA;AACH,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;CACF;;ACnCD;AAgBA,MAAM3B,QAAM,GAAG,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;AAE7D,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;AAE/C;;;;AAIG;AACG,SAAU,aAAa,CAAC,WAAmB,EAAA;AAC/C,IAAA,IAAI,SAAS,EAAE;QACb,OAAO,CAAA,EAAG,WAAW,CAAA,IAAA,CAAM,CAAC;AAC7B,KAAA;AAAM,SAAA;AACL,QAAA,OAAO,WAAW,CAAC;AACpB,KAAA;AACH,CAAC;AAED;;;;AAIG;AACH,eAAe,WAAW,CAAC,QAAoB,EAAE,OAAgB,EAAA;IAC/D,MAAM,OAAO,GAAa,EAAE,CAAC;AAE7B,IAAA,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;QAC9B,MAAM,CAAC,IAAI,EAAE,GAAG,UAAU,CAAC,GAAG,OAAO,CAAC;QACtC,MAAM,MAAM,IAAI,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE;AAC5D,YAAA,QAAQ,EAAE,MAAM;YAChB,OAAO;AACR,SAAA,CAAC,CAAW,CAAC;AACd,QAAA,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtB,KAAA;AAED,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;AAGG;AACI,MAAM,gBAAgB,GAAG;AAC9B,IAAA,KAAK,EAAE,gCAAgC;AACvC,IAAA,SAAS,EACP,uIAAuI;CAC1I,CAAC;AAEF;;;AAGG;AACI,MAAM,6BAA6B,GAAG;AAC3C,IAAA,KAAK,EACH,8FAA8F;AAChG,IAAA,SAAS,EAAE,CAA4K,0KAAA,CAAA;AACvL,IAAA,YAAY,EAAE,CAA4F,0FAAA,CAAA;CAC3G,CAAC;AAEF;AACA,MAAM,YAAY,GAA4C,CAAC,GAAU,KACvE,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAO,IAAA,EAAA,gBAAgB,CAAC,KAAK,CAAA,IAAA,CAAM,CAAC,CAAC;AAEzD;AACA,MAAM,mBAAmB,GAA4C,CAAC,GAAU,KAC9E,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;AAEhD;;;;AAIG;AACI,MAAM,YAAY,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;AAEpD,IAAI,SAAS,EAAE;IACb,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC;AAChD,CAAA;AAED;;;;AAIG;MACU,yBAAyB,CAAA;AAKpC;;;;;;;;;;AAUG;AACH,IAAA,WAAA,CAAY,OAA0C,EAAA;AACpD,QAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,QAAQ,EAAE;YACrB,aAAa,CAACA,QAAM,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,CAAC;AACnC,SAAA;AACD,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;QACF,IAAI,CAAC,OAAO,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,kBAAkB,CAAC;KAC5C;AAED;;;AAGG;AACK,IAAA,MAAM,6BAA6B,CACzC,QAAgB,EAChB,QAAiB,EACjB,OAAgB,EAAA;;AAGhB,QAAA,KAAK,MAAM,iBAAiB,IAAI,CAAC,GAAG,YAAY,CAAC,EAAE;YACjD,IAAI;AACF,gBAAA,MAAM,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACzD,aAAA;AAAC,YAAA,OAAO,CAAM,EAAE;;gBAEf,YAAY,CAAC,KAAK,EAAE,CAAC;gBACrB,SAAS;AACV,aAAA;YAED,IAAI,aAAa,GAAG,EAAE,CAAC;AACvB,YAAA,IAAI,QAAQ,EAAE;AACZ,gBAAA,aAAa,GAAG,CAAA,WAAA,EAAc,QAAQ,CAAA,CAAA,CAAG,CAAC;AAC3C,aAAA;AAED,YAAA,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC;AAChC,gBAAA;oBACE,iBAAiB;oBACjB,YAAY;oBACZ,iBAAiB;oBACjB,UAAU;oBACV,2DAA2D;AAC5D,iBAAA;AACD,gBAAA;oBACE,iBAAiB;oBACjB,YAAY;oBACZ,iBAAiB;oBACjB,UAAU;oBACV,CAAqB,kBAAA,EAAA,aAAa,CAAkB,eAAA,EAAA,QAAQ,CAAoB,kBAAA,CAAA;AACjF,iBAAA;AACF,aAAA,CAAC,CAAC;AAEH,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI;AACF,gBAAA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC3B,aAAA;AAAC,YAAA,OAAO,CAAM,EAAE;AACf,gBAAA,MAAM,IAAI,KAAK,CAAC,8DAA8D,MAAM,CAAA,CAAE,CAAC,CAAC;AACzF,aAAA;AACF,SAAA;AAED,QAAA,MAAM,IAAI,KAAK,CAAC,CAAA,wEAAA,CAA0E,CAAC,CAAC;KAC7F;AAED;;;;;;AAMG;AACI,IAAA,MAAM,QAAQ,CACnB,MAAyB,EACzB,UAA2B,EAAE,EAAA;AAE7B,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAA,EAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EAAE,OAAO,EAAE,YAAW;AACrF,YAAA,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,OAAO,EACP,IAAI,CAAC,4BAA4B,CAClC,CAAC;AACF,YAAA,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9D,YAAA,IAAI,QAAQ,EAAE;AACZ,gBAAA,aAAa,CAACA,QAAM,EAAE,QAAQ,CAAC,CAAC;AACjC,aAAA;YACD,IAAI;AACF,gBAAA,+BAA+B,CAAC,KAAK,EAAEA,QAAM,CAAC,CAAC;gBAC/CA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAmB,gBAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;AACjD,gBAAA,MAAM,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACzC,gBAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5FA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC5C,OAAO;oBACL,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,kBAAkB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;iBAC3D,CAAC;AACH,aAAA;AAAC,YAAA,OAAO,GAAQ,EAAE;AACjB,gBAAA,IAAI,mBAAmB,CAAC,GAAG,CAAC,EAAE;oBAC5B,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAAC,6BAA6B,CAAC,SAAS,CAAC,CAAC;AACtF,oBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAChD,oBAAA,MAAM,KAAK,CAAC;AACb,iBAAA;AAAM,qBAAA,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;oBAC5B,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;AAClF,oBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAChD,oBAAA,MAAM,KAAK,CAAC;AACb,iBAAA;AACD,gBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,CAAA,EAAG,GAAG,CAAA,EAAA,EAAK,6BAA6B,CAAC,YAAY,CAAA,CAAE,CACxD,CAAC;AACF,gBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAChD,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;AACF;;AClOD;AAQA;;AAEG;AACI,MAAMA,QAAM,GAAG,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;AAEjE;;;AAGG;MACU,sBAAsB,CAAA;AAGjC;;;;;;;;;;;AAWG;AACH,IAAA,WAAA,CAAY,GAAG,OAA0B,EAAA;QAdjC,IAAQ,CAAA,QAAA,GAAsB,EAAE,CAAC;AAevC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;KACzB;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE,EAAA;AACrE,QAAA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC/D,QAAA,OAAO,KAAK,CAAC;KACd;AAEO,IAAA,MAAM,gBAAgB,CAC5B,MAAyB,EACzB,UAA2B,EAAE,EAAA;QAE7B,IAAI,KAAK,GAAuB,IAAI,CAAC;AACrC,QAAA,IAAI,oBAAqC,CAAC;QAC1C,MAAM,MAAM,GAAY,EAAE,CAAC;AAE3B,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,OAAO,cAAc,KAAI;AACvB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,EAAE,EAAE;gBAC/D,IAAI;AACF,oBAAA,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAChE,oBAAA,oBAAoB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzC,iBAAA;AAAC,gBAAA,OAAO,GAAQ,EAAE;AACjB,oBAAA,IACE,GAAG,CAAC,IAAI,KAAK,4BAA4B;AACzC,wBAAA,GAAG,CAAC,IAAI,KAAK,6BAA6B,EAC1C;AACA,wBAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB,qBAAA;AAAM,yBAAA;AACL,wBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AAC/C,wBAAA,MAAM,GAAG,CAAC;AACX,qBAAA;AACF,iBAAA;AACF,aAAA;YAED,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/B,MAAM,GAAG,GAAG,IAAI,4BAA4B,CAC1C,MAAM,EACN,+CAA+C,CAChD,CAAC;AACF,gBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;AAC/C,gBAAA,MAAM,GAAG,CAAC;AACX,aAAA;AAED,YAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAClB,CAAA,WAAA,EAAc,oBAAoB,CAAC,WAAW,CAAC,IAAI,KAAK,aAAa,CAAC,MAAM,CAAC,CAAA,CAAE,CAChF,CAAC;YAEF,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,gBAAA,MAAM,IAAI,0BAA0B,CAAC,kCAAkC,CAAC,CAAC;AAC1E,aAAA;AACD,YAAA,OAAO,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC;AACzC,SAAC,CACF,CAAC;KACH;AACF;;ACvGD;AAiBA,MAAM,aAAa,GAAG4B,cAAS,CAACN,WAAQ,CAAC,CAAC;AAqC1C;;;;;;;AAOG;AACI,eAAe,gBAAgB,CACpC,aAA0D,EAC1D,oBAA8B,EAAA;IAE9B,MAAM,gBAAgB,GAA8B,EAAE,CAAC;IAEvD,MAAM,WAAW,GAAwB,aAAiD;AACvF,SAAA,WAAW,CAAC;IACf,MAAM,eAAe,GAAwB,aAAqD;AAC/F,SAAA,eAAe,CAAC;AACnB,IAAA,gBAAgB,CAAC,mBAAmB;QAClC,WAAW,KAAK,MAAM,aAAa,CAAC,eAAgB,EAAE,MAAM,CAAC,CAAC,CAAC;AACjE,IAAA,IAAI,oBAAoB,EAAE;AACxB,QAAA,gBAAgB,CAAC,GAAG,GAAG,gBAAgB,CAAC,mBAAmB,CAAC;AAC7D,KAAA;IAED,MAAM,kBAAkB,GACtB,+FAA+F,CAAC;IAClG,MAAM,UAAU,GAAa,EAAE,CAAC;;AAGhC,IAAA,IAAI,KAAK,CAAC;IACV,GAAG;QACD,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;AACtE,QAAA,IAAI,KAAK,EAAE;YACT,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,SAAA;AACF,KAAA,QAAQ,KAAK,EAAE;AAEhB,IAAA,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3B,QAAA,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;AAC/F,KAAA;AAED,IAAA,gBAAgB,CAAC,UAAU,GAAGO,iBAAU,CAAC,MAAM,CAAC;AAC7C,SAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SAC5C,MAAM,CAAC,KAAK,CAAC;AACb,SAAA,WAAW,EAAE,CAAC;AAEjB,IAAA,OAAO,gBAAoC,CAAC;AAC9C,CAAC;AAED;;;AAGG;AACG,MAAO,qBAAsB,SAAQ,QAAQ,CAAA;AAIjD,IAAA,WAAA,CAAY,OAAqC,EAAA;QAC/C,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACjC,QAAA,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AAC3C,QAAA,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;KAC1D;;IAGD,MAAM,IAAI,CAAC,OAAuC,EAAA;QAChD,IAAI;AACF,YAAA,MAAM,KAAK,GAAG,MAAM,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;AAEpF,YAAA,IAAI,UAA8B,CAAC;AACnC,YAAA,IAAI,IAAI,CAAC,aAAa,CAAC,mBAAmB,KAAK,SAAS,EAAE;gBACxD,MAAM,gBAAgB,GAAGC,uBAAgB,CAAC;oBACxC,GAAG,EAAE,KAAK,CAAC,mBAAmB;AAC9B,oBAAA,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,mBAAmB;AAClD,oBAAA,MAAM,EAAE,KAAK;AACd,iBAAA,CAAC,CAAC;AAEH,gBAAA,UAAU,GAAG,gBAAgB;AAC1B,qBAAA,MAAM,CAAC;AACN,oBAAA,MAAM,EAAE,KAAK;AACb,oBAAA,IAAI,EAAE,OAAO;iBACd,CAAC;AACD,qBAAA,QAAQ,EAAE,CAAC;AACf,aAAA;AAAM,iBAAA;AACL,gBAAA,UAAU,GAAG,KAAK,CAAC,mBAAmB,CAAC;AACxC,aAAA;AAED,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG;gBACvC,UAAU,EAAE,KAAK,CAAC,UAAU;AAC5B,gBAAA,UAAU,EAAE,UAAU;gBACtB,GAAG,EAAE,KAAK,CAAC,GAAG;aACf,CAAC;AACH,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;AACnB,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;AACzC,YAAA,MAAM,KAAK,CAAC;AACb,SAAA;AACD,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC5B;AAES,IAAA,MAAM,UAAU,CACxB,MAAgB,EAChB,UAAyC,EAAE,EAAA;QAE3C,IAAI;AACF,YAAA,MAAM,aAAa,GAA4B;gBAC7C,MAAM;gBACN,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC;AACF,YAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAC9B,cAAc,EACd,OAAO,CAAC,SAAS,CAClB,CAAC,8BAA8B,CAAC,aAAa,CAAC,CAAC;;;;AAIhD,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;AACtE,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAC9C,SAAA;KACF;AACF;;ACjLD;AAcA,MAAMP,gBAAc,GAAG,6BAA6B,CAAC;AACrD,MAAMvB,QAAM,GAAG,gBAAgB,CAACuB,gBAAc,CAAC,CAAC;AAqChD;;;;;;;AAOG;MACU,2BAA2B,CAAA;AAoDtC,IAAA,WAAA,CACE,QAAgB,EAChB,QAAgB,EAChB,8BAAoF,EACpF,UAA8C,EAAE,EAAA;AAEhD,QAAA,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;AAC1B,YAAA,MAAM,IAAI,KAAK,CAAC,GAAGA,gBAAc,CAAA,gDAAA,CAAkD,CAAC,CAAC;AACtF,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;AAEF,QAAA,MAAM,aAAa,GACd,MAAA,CAAA,MAAA,CAAA,EAAA,GAAC,OAAO,8BAA8B,KAAK,QAAQ;AACpD,cAAE;AACE,gBAAA,eAAe,EAAE,8BAA8B;AAChD,aAAA;AACH,cAAE,8BAA8B,EACnC,CAAC;QACF,MAAM,WAAW,GAAwB,aAAiD;AACvF,aAAA,WAAW,CAAC;AACf,QAAA,MAAM,eAAe,GACnB,aACD,CAAC,eAAe,CAAC;QAClB,IAAI,CAAC,aAAa,IAAI,EAAE,WAAW,IAAI,eAAe,CAAC,EAAE;AACvD,YAAA,MAAM,IAAI,KAAK,CACb,GAAGA,gBAAc,CAAA,0MAAA,CAA4M,CAC9N,CAAC;AACH,SAAA;QACD,IAAI,WAAW,IAAI,eAAe,EAAE;AAClC,YAAA,MAAM,IAAI,KAAK,CACb,GAAGA,gBAAc,CAAA,sOAAA,CAAwO,CAC1P,CAAC;AACH,SAAA;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,qBAAqB,CACpC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,KACV,aAAa;oBACbvB,QAAM;YACN,QAAQ;YACR,QAAQ,EACR,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,EAClD,sBAAsB,EAAE,OAAO,EAAA,CAAA,CAC/B,CAAC;KACJ;AAED;;;;;;;AAOG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE,EAAA;AACrE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,GAAGuB,gBAAc,CAAA,SAAA,CAAW,EAAE,OAAO,EAAE,OAAO,UAAU,KAAI;AACxF,YAAA,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjCvB,QAAM,CACP,CAAC;AAEF,YAAA,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AACzD,SAAC,CAAC,CAAC;KACJ;AACF;;ACrLD;AAkBA;;;AAGG;AACG,MAAO,gBAAiB,SAAQ,QAAQ,CAAA;AAC5C,IAAA,WAAA,CAAY,OAAgC,EAAA;QAC1C,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;KAC1D;AAES,IAAA,MAAM,UAAU,CACxB,MAAgB,EAChB,UAAyC,EAAE,EAAA;QAE3C,IAAI;AACF,YAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAC9B,cAAc,EACd,OAAO,CAAC,SAAS,CAClB,CAAC,8BAA8B,CAAC;gBAC/B,MAAM;gBACN,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;AACvB,aAAA,CAAC,CAAC;;;AAGH,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;AACtE,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAC9C,SAAA;KACF;AACF;;ACnDD;AAeA,MAAMA,QAAM,GAAG,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;AAE1D;;;;;;;AAOG;MACU,sBAAsB,CAAA;AAKjC;;;;;;;;;AASG;AACH,IAAA,WAAA,CACE,QAAgB,EAChB,QAAgB,EAChB,YAAoB,EACpB,UAAyC,EAAE,EAAA;QAE3C,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE;AAC3C,YAAA,MAAM,IAAI,KAAK,CACb,4LAA4L,CAC7L,CAAC;AACH,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,gBAAgB,CAC/B,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,aACVA,QAAM;YACN,QAAQ;YACR,QAAQ;AACR,YAAA,YAAY,EACZ,sBAAsB,EAAE,OAAO,IAC/B,CAAC;KACJ;AAED;;;;;;;AAOG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE,EAAA;AACrE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,CAAG,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA,SAAA,CAAW,EACnC,OAAO,EACP,OAAO,UAAU,KAAI;AACnB,YAAA,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjCA,QAAM,CACP,CAAC;AAEF,YAAA,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AACzD,SAAC,CACF,CAAC;KACH;AACF;;AC5FD;AAiBA;;;AAGG;AACG,MAAO,oBAAqB,SAAQ,QAAQ,CAAA;AAIhD,IAAA,WAAA,CAAY,OAAoC,EAAA;QAC9C,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACjC,QAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;KAClC;AAES,IAAA,MAAM,UAAU,CACxB,MAAgB,EAChB,OAAuC,EAAA;QAEvC,IAAI;AACF,YAAA,MAAM,cAAc,GAAqC;gBACvD,MAAM;gBACN,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvB,gBAAA,aAAa,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,aAAa;AACrC,gBAAA,SAAS,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS;AAC7B,gBAAA,MAAM,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,MAAM;aACxB,CAAC;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS,CAAC,CAAC,8BAA8B,CAC3F,cAAc,CACf,CAAC;AACF,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;AACtE,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;YACnB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAChD,SAAA;KACF;AACF;;ACpDD;AAeA,MAAMA,QAAM,GAAG,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;AAE9D;;;;;AAKG;MACU,0BAA0B,CAAA;AAKrC;;;;;;;;;;AAUG;IACH,WACE,CAAA,QAAgB,EAChB,QAAgB,EAChB,QAAgB,EAChB,QAAgB,EAChB,OAAA,GAA6C,EAAE,EAAA;QAE/C,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACpD,YAAA,MAAM,IAAI,KAAK,CACb,iMAAiM,CAClM,CAAC;AACH,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAoB,CACnC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,aACVA,QAAM;YACN,QAAQ;YACR,QAAQ;YACR,QAAQ;AACR,YAAA,QAAQ,EACR,sBAAsB,EAAE,OAAO,IAAI,EAAE,IACrC,CAAC;KACJ;AAED;;;;;;;;;;;AAWG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE,EAAA;AACrE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,CAAG,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA,SAAA,CAAW,EACnC,OAAO,EACP,OAAO,UAAU,KAAI;AACnB,YAAA,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjCA,QAAM,CACP,CAAC;AAEF,YAAA,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AACzD,SAAC,CACF,CAAC;KACH;AACF;;ACjGD;AAaA;;;;;;AAMG;AACI,MAAM,gCAAgC,GAAG;IAC9C,iBAAiB;IACjB,iBAAiB;IACjB,qBAAqB;IACrB,+BAA+B;IAC/B,mCAAmC;IACnC,gBAAgB;IAChB,gBAAgB;IAChB,oCAAoC;CACrC,CAAC;AAEF,SAAS,6BAA6B,GAAA;;IACpC,MAAM,yBAAyB,GAAG,CAAA,EAAA,GAAA,OAAO,CAAC,GAAG,CAAC,kCAAkC,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,EAAE,CAAC;AACvF,IAAA,OAAO,yBAAyB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC9C,CAAC;AAED,MAAMuB,gBAAc,GAAG,uBAAuB,CAAC;AAC/C,MAAMvB,QAAM,GAAG,gBAAgB,CAACuB,gBAAc,CAAC,CAAC;AAEhD;;;AAGG;MACU,qBAAqB,CAAA;AAKhC;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACH,IAAA,WAAA,CAAY,OAAsC,EAAA;;QA5B1C,IAAW,CAAA,WAAA,GAGc,SAAS,CAAC;AA4BzC,QAAA,MAAM,QAAQ,GAAG,cAAc,CAAC,gCAAgC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtF,QAAAvB,QAAM,CAAC,IAAI,CAAC,8CAA8C,QAAQ,CAAA,CAAE,CAAC,CAAC;QAEtE,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,EAC1C,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,EACtC,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;AAEjD,QAAA,MAAM,4BAA4B,GAAG,6BAA6B,EAAE,CAAC;AACrE,QAAA,MAAM,UAAU,GAAQ,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CAAE,EAAA,EAAA,4BAA4B,GAAE,CAAC;AAEhE,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,aAAa,CAACA,QAAM,EAAE,QAAQ,CAAC,CAAC;AACjC,SAAA;AAED,QAAA,IAAI,QAAQ,IAAI,QAAQ,IAAI,YAAY,EAAE;YACxCA,QAAM,CAAC,IAAI,CACT,CAAA,gDAAA,EAAmD,QAAQ,CAAe,YAAA,EAAA,QAAQ,CAA+B,6BAAA,CAAA,CAClH,CAAC;AACF,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;YAC5F,OAAO;AACR,SAAA;AAED,QAAA,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;AAClE,QAAA,MAAM,mBAAmB,GAAG,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC;AAC1E,QAAA,IAAI,QAAQ,IAAI,QAAQ,IAAI,eAAe,EAAE;YAC3CA,QAAM,CAAC,IAAI,CACT,CAAwD,qDAAA,EAAA,QAAQ,CAAe,YAAA,EAAA,QAAQ,CAAyB,sBAAA,EAAA,eAAe,CAAE,CAAA,CAClI,CAAC;AACF,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,2BAA2B,CAChD,QAAQ,EACR,QAAQ,EACR,EAAE,eAAe,EAAE,mBAAmB,EAAE,EACxC,UAAU,CACX,CAAC;YACF,OAAO;AACR,SAAA;AAED,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;AAC5C,QAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;AAC5C,QAAA,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,EAAE;YAChDA,QAAM,CAAC,IAAI,CACT,CAAuD,oDAAA,EAAA,QAAQ,CAAe,YAAA,EAAA,QAAQ,CAAkB,eAAA,EAAA,QAAQ,CAAE,CAAA,CACnH,CAAC;AACF,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,0BAA0B,CAC/C,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,UAAU,CACX,CAAC;AACH,SAAA;KACF;AAED;;;;;AAKG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE,EAAA;AACrE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,GAAGuB,gBAAc,CAAA,SAAA,CAAW,EAAE,OAAO,EAAE,OAAO,UAAU,KAAI;YACxF,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI;AACF,oBAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;oBACnEvB,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5C,oBAAA,OAAO,MAAM,CAAC;AACf,iBAAA;AAAC,gBAAA,OAAO,GAAQ,EAAE;AACjB,oBAAA,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,CAAC,GAAG,EAAE;wBACvD,KAAK,EAAE,CAAG,EAAAuB,gBAAc,CAAqH,mHAAA,CAAA;AAC7I,wBAAA,iBAAiB,EAAE,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1E,qBAAA,CAAC,CAAC;AACH,oBAAAvB,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC,CAAC;AAC/D,oBAAA,MAAM,mBAAmB,CAAC;AAC3B,iBAAA;AACF,aAAA;AACD,YAAA,MAAM,IAAI,0BAA0B,CAClC,GAAGuB,gBAAc,CAAA,oJAAA,CAAsJ,CACxK,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AACF;;AC3JD;AAgBA;;;AAGG;AACI,MAAM,+BAA+B,GAAG;AAC7C;;AAEG;IACH,iBAAiB,GAAA;AACf,QAAA,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;AAChC,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;AAC3B,gBAAA,MAAM,IAAI,KAAK,CACb,4EAA4E,CAC7E,CAAC;AACH,aAAA;AACD,YAAA,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;AAC/B,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,MAAM,CAAC;AACf,SAAA;KACF;AAED;;;;AAIG;AACH,IAAA,MAAM,iBAAiB,CACrB,MAAgB,EAChB,QAAiB,EACjB,OAAgB,EAAA;QAEhB,IAAI,aAAa,GAAa,EAAE,CAAC;AACjC,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,aAAa,GAAG,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;AAC3C,SAAA;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;YACrC,IAAI;AACF,gBAAAG,iCAAa,CAAC,QAAQ,CACpB,KAAK,EACL;oBACE,MAAM;oBACN,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,GAAG,MAAM,CAAC,MAAM,CACd,CAAC,QAAQ,EAAE,OAAO,KAAK,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,EAC1D,EAAE,CACH;AACD,oBAAA,GAAG,aAAa;iBACjB,EACD;AACE,oBAAA,GAAG,EAAE,+BAA+B,CAAC,iBAAiB,EAAE;oBACxD,OAAO;AACR,iBAAA,EACD,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,KAAI;oBACxB,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;AACrC,iBAAC,CACF,CAAC;AACH,aAAA;AAAC,YAAA,OAAO,GAAQ,EAAE;gBACjB,MAAM,CAAC,GAAG,CAAC,CAAC;AACb,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;CACF,CAAC;AAEF,MAAM1B,QAAM,GAAG,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;AAE/D;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;MACU,2BAA2B,CAAA;AAKtC;;;;;;;AAOG;AACH,IAAA,WAAA,CAAY,OAA4C,EAAA;AACtD,QAAA,IAAI,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,QAAQ,EAAE;YACrB,aAAa,CAACA,QAAM,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAP,OAAO,CAAE,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,CAAC;AACnC,SAAA;AACD,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;QACF,IAAI,CAAC,OAAO,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,kBAAkB,CAAC;KAC5C;AAED;;;;;;;AAOG;AACI,IAAA,MAAM,QAAQ,CACnB,MAAyB,EACzB,UAA2B,EAAE,EAAA;AAE7B,QAAA,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,OAAO,EACP,IAAI,CAAC,4BAA4B,CAClC,CAAC;AACF,QAAA,IAAI,QAAQ,EAAE;AACZ,YAAA,aAAa,CAACA,QAAM,EAAE,QAAQ,CAAC,CAAC;AACjC,SAAA;AACD,QAAA,IAAI,SAAmB,CAAC;AACxB,QAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,YAAA,SAAS,GAAG,CAAC,MAAM,CAAC,CAAC;AACtB,SAAA;AAAM,aAAA;YACL,SAAS,GAAG,MAAM,CAAC;AACpB,SAAA;QACDA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAoB,iBAAA,EAAA,MAAM,CAAE,CAAA,CAAC,CAAC;AAEnD,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAA,EAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EAAE,OAAO,EAAE,YAAW;;YACrF,IAAI;AACF,gBAAA,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AAC1B,oBAAA,+BAA+B,CAAC,KAAK,EAAEA,QAAM,CAAC,CAAC;AACjD,iBAAC,CAAC,CAAC;AACH,gBAAA,MAAM,GAAG,GAAG,MAAM,+BAA+B,CAAC,iBAAiB,CACjE,SAAS,EACT,QAAQ,EACR,IAAI,CAAC,OAAO,CACb,CAAC;gBACF,MAAM,kBAAkB,GACtB,CAAA,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAC,yCAAyC,CAAC;qBAC5D,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC,8CAA8C,CAAC,CAAA,CAAC;gBACpE,MAAM,iBAAiB,GACrB,CAAA,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,CAAC,mBAAmB,CAAC;qBACtC,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,UAAU,CAAC,yBAAyB,CAAC,CAAA,CAAC;AAEpD,gBAAA,IAAI,iBAAiB,KAAK,GAAG,CAAC,KAAK,IAAK,GAAG,CAAC,KAAa,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE;AAC5E,oBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,wKAAwK,CACzK,CAAC;AACF,oBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,oBAAA,MAAM,KAAK,CAAC;AACb,iBAAA;AAED,gBAAA,IAAI,kBAAkB,EAAE;AACtB,oBAAA,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAC1C,+NAA+N,CAChO,CAAC;AACF,oBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,oBAAA,MAAM,KAAK,CAAC;AACb,iBAAA;gBAED,IAAI;oBACF,MAAM,IAAI,GAAyC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBAC1EA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC5C,OAAO;wBACL,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,kBAAkB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;qBACvD,CAAC;AACH,iBAAA;AAAC,gBAAA,OAAO,CAAM,EAAE;oBACf,IAAI,GAAG,CAAC,MAAM,EAAE;AACd,wBAAA,MAAM,IAAI,0BAA0B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClD,qBAAA;AACD,oBAAA,MAAM,CAAC,CAAC;AACT,iBAAA;AACF,aAAA;AAAC,YAAA,OAAO,GAAQ,EAAE;AACjB,gBAAA,MAAM,KAAK,GACT,GAAG,CAAC,IAAI,KAAK,4BAA4B;AACvC,sBAAE,GAAG;sBACH,IAAI,0BAA0B,CAC3B,GAAa,CAAC,OAAO,IAAI,yDAAyD,CACpF,CAAC;AACR,gBAAAA,QAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AACjD,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;AACH,SAAC,CAAC,CAAC;KACJ;AACF;;AC1ND;AAkCA;;;;;AAKG;AACG,MAAO,gCAAiC,SAAQ,yBAAyB,CAAA;;;AAO7E,IAAA,WAAA,CAAY,OAAuC,EAAA;;AACjD,QAAA,MAAM,uBAAuB,GAC3B,CAAA,EAAA,GAAC,OAAiD,KAAA,IAAA,IAAjD,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAA4C,uBAAuB,mCAC3E,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;AAC9B,QAAA,MAAM,wBAAwB,GAC5B,CAAC,EAAA,GAAA,OAAiD,KAAjD,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAA4C,wBAAwB,MAC5E,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,uBAAuB,CAAC;QAC1B,MAAM,iBAAiB,GAAI,OAAmD,KAAA,IAAA,IAAnD,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAC9B,yBAAyB,CAAC;AAC9B,QAAA,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC5D,QAAA,MAAM,QAAQ,GAAG,CAAA,EAAA,GAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,mCAAI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;;AAElE,QAAA,IAAI,iBAAiB,EAAE;YACrB,MAAM,gCAAgC,mCACjC,OAAO,CAAA,EAAA,EACV,UAAU,EAAE,iBAAiB,GAC9B,CAAC;YACF,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACzC,SAAA;aAAM,IAAI,YAAY,IAAI,wBAAwB,EAAE;YACnD,MAAM,iCAAiC,mCAClC,OAAO,CAAA,EAAA,EACV,QAAQ,EAAE,QAAQ,GACnB,CAAC;AACF,YAAA,KAAK,CAAC,wBAAwB,EAAE,iCAAiC,CAAC,CAAC;AACpE,SAAA;AAAM,aAAA,IAAI,uBAAuB,EAAE;YAClC,MAAM,4BAA4B,mCAC7B,OAAO,CAAA,EAAA,EACV,QAAQ,EAAE,uBAAuB,GAClC,CAAC;YACF,KAAK,CAAC,4BAA4B,CAAC,CAAC;AACrC,SAAA;AAAM,aAAA;YACL,KAAK,CAAC,OAAO,CAAC,CAAC;AAChB,SAAA;KACF;AACF,CAAA;AACD;;;;;AAKG;AACG,MAAO,iCAAkC,SAAQ,0BAA0B,CAAA;;;AAK/E,IAAA,WAAA,CAAY,OAAuC,EAAA;;AACjD,QAAA,MAAM,uBAAuB,GAC3B,CAAA,EAAA,GAAC,OAAiD,KAAA,IAAA,IAAjD,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAA4C,uBAAuB,mCAC3E,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;AAC9B,QAAA,MAAM,wBAAwB,GAC5B,CAAC,EAAA,GAAA,OAAiD,KAAjD,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAA4C,wBAAwB,MAC5E,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,uBAAuB,CAAC;AAC1B,QAAA,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC5D,QAAA,MAAM,QAAQ,GAAG,CAAA,EAAA,GAAA,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,mCAAI,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;QAClE,IAAI,YAAY,IAAI,wBAAwB,EAAE;AAC5C,YAAA,MAAM,iCAAiC,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAClC,OAAO,CAAA,EAAA,EACV,QAAQ,EACR,QAAQ,EAAE,wBAAwB,EAClC,aAAa,EAAE,YAAY,GAC5B,CAAC;YACF,KAAK,CAAC,iCAAiC,CAAC,CAAC;AAC1C,SAAA;AAAM,aAAA,IAAI,QAAQ,EAAE;AACnB,YAAA,MAAM,mCAAmC,GACpC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CACV,EAAA,EAAA,QAAQ,GACT,CAAC;YACF,KAAK,CAAC,mCAAmC,CAAC,CAAC;AAC5C,SAAA;AAAM,aAAA;YACL,KAAK,CAAC,OAA4C,CAAC,CAAC;AACrD,SAAA;KACF;AACF,CAAA;AAEK,MAAO,kCAAmC,SAAQ,2BAA2B,CAAA;AACjF,IAAA,WAAA,CAAY,OAAuC,EAAA;AACjD,QAAA,KAAK,CACH,MAAA,CAAA,MAAA,CAAA,EAAA,kBAAkB,EAAE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,kBAAkB,EAC5C,EAAA,OAAO,EACV,CAAC;KACJ;AACF,CAAA;AACK,MAAO,yBAA0B,SAAQ,kBAAkB,CAAA;AAC/D,IAAA,WAAA,CAAY,OAAuC,EAAA;AACjD,QAAA,KAAK,CACH,MAAA,CAAA,MAAA,CAAA,EAAA,kBAAkB,EAAE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,kBAAkB,EAC5C,EAAA,OAAO,EACV,CAAC;KACJ;AACF,CAAA;AAEK,MAAO,gCAAiC,SAAQ,yBAAyB,CAAA;AAC7E,IAAA,WAAA,CAAY,OAAuC,EAAA;AACjD,QAAA,KAAK,CACH,MAAA,CAAA,MAAA,CAAA,EAAA,kBAAkB,EAAE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,kBAAkB,EAC5C,EAAA,OAAO,EACV,CAAC;KACJ;AACF,CAAA;AAEM,MAAM,kBAAkB,GAAmC;IAChE,qBAAqB;IACrB,iCAAiC;IACjC,gCAAgC;IAChC,yBAAyB;IACzB,gCAAgC;IAChC,kCAAkC;CACnC,CAAC;AAEF;;;AAGG;AACG,MAAO,sBAAuB,SAAQ,sBAAsB,CAAA;AAmEhE,IAAA,WAAA,CACE,OAGyC,EAAA;AAEzC,QAAA,KAAK,CAAC,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;KAC/D;AACF;;AC5OD;AAwBA;;;AAGG;AACI,MAAM,0BAA0B,GAAG;UACxC+B,wBAAI;CACL,CAAC;AAEF;;;;AAIG;AACG,MAAO,eAAgB,SAAQ,QAAQ,CAAA;AAM3C,IAAA,WAAA,CAAY,OAA+B,EAAA;QACzC,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AACvC,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAEnC,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/B,QAAA,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACpB,YAAA,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AAChB,SAAA;AACD,QAAA,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;KAC9B;AAEO,IAAA,MAAM,kBAAkB,CAC9B,OAA0C,EAC1C,SAAmB,EAAA;AAEnB,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;KACrE;IAES,UAAU,CAClB,MAAgB,EAChB,OAAuC,EAAA;QAEvC,OAAO,IAAI,OAAO,CAAc,CAAC,OAAO,EAAE,MAAM,KAAI;YAClD,MAAM,eAAe,GAAa,EAAE,CAAC;AAErC,YAAA,MAAM,eAAe,GAAG,CAAC,GAAyB,EAAE,GAAwB,KAAU;;AACpF,gBAAA,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;AACZ,oBAAA,MAAM,CACJ,IAAI,KAAK,CACP,CAA0F,wFAAA,CAAA,CAC3F,CACF,CAAC;oBACF,OAAO;AACR,iBAAA;AACD,gBAAA,IAAI,GAAQ,CAAC;gBACb,IAAI;AACF,oBAAA,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC1C,iBAAA;AAAC,gBAAA,OAAO,CAAM,EAAE;AACf,oBAAA,MAAM,CACJ,IAAI,KAAK,CACP,CAA0F,wFAAA,CAAA,CAC3F,CACF,CAAC;oBACF,OAAO;AACR,iBAAA;AACD,gBAAA,MAAM,YAAY,GAAsC;oBACtD,IAAI,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAE;oBACnC,WAAW,EAAE,IAAI,CAAC,WAAW;AAC7B,oBAAA,MAAM,EAAE,MAAM;AACd,oBAAA,SAAS,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS;AAC7B,oBAAA,YAAY,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,SAAS,0CAAE,QAAQ;iBACvC,CAAC;AAEF,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS,CAAC;AACtD,qBAAA,IAAI,CAAC,CAAC,YAAY,KAAI;AACrB,oBAAA,IAAI,YAAY,KAAZ,IAAA,IAAA,YAAY,uBAAZ,YAAY,CAAE,OAAO,EAAE;AACzB,wBAAA,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;AAClE,qBAAA;oBACD,MAAM,cAAc,GAAG,CAAA,iFAAA,CAAmF,CAAC;AAC3G,oBAAA,IAAI,YAAY,IAAI,YAAY,CAAC,SAAS,EAAE;AAC1C,wBAAA,MAAM,kBAAkB,GAAG,YAAY,KAAA,IAAA,IAAZ,YAAY,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAZ,YAAY,CAAE,SAAS,CAAC,OAAO,EAAE,CAAC;AAC7D,wBAAA,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACnB,wBAAA,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACxB,wBAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;AAEjD,wBAAA,OAAO,CAAC;4BACN,kBAAkB;4BAClB,KAAK,EAAE,YAAY,CAAC,WAAW;AAChC,yBAAA,CAAC,CAAC;AACJ,qBAAA;AAAM,yBAAA;wBACL,MAAM,YAAY,GAAG,WAAW,CAC9B,MAAM,EACN,CAAA,EAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA,EAAA,EAAK,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAE,CAAA,CACjF,CAAC;AACF,wBAAA,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACnB,wBAAA,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;wBACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAExC,wBAAA,MAAM,CACJ,IAAI,KAAK,CACP,CAA0F,wFAAA,CAAA,CAC3F,CACF,CAAC;AACH,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC;oBACV,OAAO;AACT,iBAAC,CAAC;qBACD,KAAK,CAAC,MAAK;oBACV,MAAM,YAAY,GAAG,WAAW,CAC9B,MAAM,EACN,CAAA,EAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA,EAAA,EAAK,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAE,CAAA,CACjF,CAAC;AACF,oBAAA,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACnB,oBAAA,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;oBACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAExC,oBAAA,MAAM,CACJ,IAAI,KAAK,CACP,CAA0F,wFAAA,CAAA,CAC3F,CACF,CAAC;AACF,oBAAA,OAAO,EAAE,CAAC;AACZ,iBAAC,CAAC,CAAC;AACP,aAAC,CAAC;YAEF,MAAM,GAAG,GAAGC,wBAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;AAC/C,YAAA,MAAM,MAAM,GAAGC,6BAAS,CAAC,GAAG,CAAC,CAAC;AAE9B,YAAA,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,MAClD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAkD,+CAAA,EAAA,IAAI,CAAC,IAAI,CAAG,CAAA,CAAA,CAAC,CACjF,CAAC;AAEF,YAAA,SAAS,OAAO,GAAA;AACd,gBAAA,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,KAAK,EAAE,CAAC;AAChB,iBAAA;AAED,gBAAA,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE;oBACpC,MAAM,CAAC,OAAO,EAAE,CAAC;AAClB,iBAAA;AAED,gBAAA,IAAI,MAAM,EAAE;oBACV,MAAM,CAAC,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,EAAE,CAAC;AACf,iBAAA;aACF;AAED,YAAA,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,KAAK,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAE/D,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,KAAI;AACtB,gBAAA,OAAO,EAAE,CAAC;AACV,gBAAA,MAAM,IAAI,GAAI,GAAW,CAAC,IAAI,CAAC;AAC/B,gBAAA,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,YAAY,EAAE;oBAC9C,MAAM,CACJ,IAAI,0BAA0B,CAC5B;wBACE,CAAuD,oDAAA,EAAA,IAAI,CAAC,IAAI,CAAG,CAAA,CAAA;wBACnE,CAA+D,6DAAA,CAAA;wBAC/D,8EAA8E;AAC/E,qBAAA,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CACF,CAAC;AACH,iBAAA;AAAM,qBAAA;oBACL,MAAM,CACJ,IAAI,0BAA0B,CAC5B,CAAA,+EAAA,EAAkF,GAAG,CAAC,OAAO,CAAA,CAAE,CAChG,CACF,CAAC;AACH,iBAAA;AACH,aAAC,CAAC,CAAC;AAEH,YAAA,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,MAAK;gBACvB,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAE1D,MAAM,WAAW,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,WAAW,CAAC;AACzC,gBAAA,IAAI,WAAW,EAAE;AACf,oBAAA,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAK;AACzC,wBAAA,OAAO,EAAE,CAAC;AACV,wBAAA,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AAC/B,qBAAC,CAAC,CAAC;AACJ,iBAAA;AAED,gBAAA,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,KAAI;AACtB,oBAAA,OAAO,EAAE,CAAC;oBACV,MAAM,CAAC,CAAC,CAAC,CAAC;AACZ,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;AAOO,IAAA,MAAM,eAAe,CAC3B,UAAoB,EACpB,OAAuC,EAAA;;AAGvC,QAAA,MAAM,cAAc,GAAG,IAAIpB,qBAAQ,CAAC,cAAc,EAAE,CAAC;;QAErD,IAAI,CAAC,SAAS,GAAG,MAAM,cAAc,CAAC,iBAAiB,EAAE,CAAC;AAE1D,QAAA,MAAM,qBAAqB,GAAqC;AAC9D,YAAA,MAAM,EAAE,UAAU;AAClB,YAAA,aAAa,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,aAAa;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW;AAC7B,YAAA,SAAS,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS;AAC7B,YAAA,MAAM,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,MAAM;YACvB,SAAS,EAAE,IAAI,CAAC,SAAS;AACzB,YAAA,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;YACvC,mBAAmB,EAAE,MAAM;SAC5B,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS,CAAC,CAAC,cAAc,CAC7E,qBAAqB,CACtB,CAAC;QACF,IAAI;;AAEF,YAAA,MAAM,0BAA0B,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;AACpF,SAAA;AAAC,QAAA,OAAO,CAAM,EAAE;YACf,MAAM,IAAI,0BAA0B,CAClC,CAAA,sEAAA,EAAyE,CAAC,CAAC,OAAO,CAAE,CAAA,CACrF,CAAC;AACH,SAAA;KACF;AACF;;AC5PD;AAqBA,MAAMb,QAAM,GAAG,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;AAEhE;;;AAGG;MACU,4BAA4B,CAAA;AAMvC;;;;;;;;;;;AAWG;AACH,IAAA,WAAA,CACE,UAEmD,EAAE,EAAA;AAErD,QAAA,MAAM,WAAW,GACf,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU;AACvC,cAAE,OAAO,CAAC,WAAW,EAAE;AACvB,cAAE,OAAO,CAAC,WAAW,IAAI,kBAAkB,CAAC;QAEhD,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,CAAC;AAClC,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;AAEF,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAe,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAC9B,OAAO,CAAA,EAAA,EACV,sBAAsB,EAAE,OAAO,UAC/BA,QAAM;AACN,YAAA,WAAW,IACX,CAAC;QACH,IAAI,CAAC,8BAA8B,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,8BAA8B,CAAC;KAC/E;AAED;;;;;;;;;;;AAWG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE,EAAA;AACrE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,CAAG,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA,SAAA,CAAW,EACnC,OAAO,EACP,OAAO,UAAU,KAAI;AACnB,YAAA,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjCA,QAAM,CACP,CAAC;AAEF,YAAA,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AACzC,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACpC,UAAU,CAAA,EAAA,EACb,8BAA8B,EAAE,IAAI,CAAC,8BAA8B,IACnE,CAAC;AACL,SAAC,CACF,CAAC;KACH;AAED;;;;;;;;;;;;AAYG;AACH,IAAA,MAAM,YAAY,CAChB,MAAyB,EACzB,UAA2B,EAAE,EAAA;AAE7B,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,CAAG,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA,aAAA,CAAe,EACvC,OAAO,EACP,OAAO,UAAU,KAAI;AACnB,YAAA,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AACtD,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;AAC1C,SAAC,CACF,CAAC;KACH;AACF;;ACjID;AAiBA;;;AAGG;AACG,MAAO,cAAe,SAAQ,QAAQ,CAAA;AAG1C,IAAA,WAAA,CAAY,OAA8B,EAAA;QACxC,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;KACtD;AAES,IAAA,MAAM,UAAU,CACxB,MAAgB,EAChB,OAAuC,EAAA;QAEvC,IAAI;AACF,YAAA,MAAM,cAAc,GAA+B;gBACjD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;gBAC3C,MAAM;AACN,gBAAA,MAAM,EAAE,KAAK;AACb,gBAAA,aAAa,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,aAAa;AACrC,gBAAA,SAAS,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS;AAC7B,gBAAA,MAAM,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,MAAM;aACxB,CAAC;YACF,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS,CAAC,CAAC,wBAAwB,CAChF,cAAc,CACf,CAAC;AACF,YAAA,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,WAAW,EAAE,MAAK;AACrF,gBAAA,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC;AAC/B,aAAC,CAAC,CAAC;AACH,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,cAAc,IAAI,SAAS,CAAC,CAAC;AAC9E,SAAA;AAAC,QAAA,OAAO,KAAU,EAAE;YACnB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAChD,SAAA;KACF;AACF;;ACrDD;AAgBA,MAAMA,QAAM,GAAG,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;AAExD;;;AAGG;AACG,SAAU,+BAA+B,CAAC,cAA8B,EAAA;AAC5E,IAAA,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AAED;;;AAGG;MACU,oBAAoB,CAAA;AAM/B;;;;;;;;;;;;;;;;;;;AAmBG;AACH,IAAA,WAAA,CAAY,OAAqC,EAAA;QAC/C,IAAI,CAAC,QAAQ,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,CAAC;AAClC,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;AACF,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,cAAc,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAC7B,OAAO,CAAA,EAAA,UACVA,QAAM,EACN,kBAAkB,EAAE,CAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,kBAAkB,KAAI,+BAA+B,EAClF,sBAAsB,EAAE,OAAO,IAAI,EAAE,IACrC,CAAC;QACH,IAAI,CAAC,8BAA8B,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,8BAA8B,CAAC;KAC/E;AAED;;;;;;;;;;;AAWG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE,EAAA;AACrE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,CAAG,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA,SAAA,CAAW,EACnC,OAAO,EACP,OAAO,UAAU,KAAI;AACnB,YAAA,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjCA,QAAM,CACP,CAAC;AAEF,YAAA,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AACzC,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACpC,UAAU,CAAA,EAAA,EACb,8BAA8B,EAAE,IAAI,CAAC,8BAA8B,IACnE,CAAC;AACL,SAAC,CACF,CAAC;KACH;AAED;;;;;;;;;AASG;AACH,IAAA,MAAM,YAAY,CAChB,MAAyB,EACzB,UAA2B,EAAE,EAAA;AAE7B,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,CAAG,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA,aAAA,CAAe,EACvC,OAAO,EACP,OAAO,UAAU,KAAI;AACnB,YAAA,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AACtD,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;AAC1C,SAAC,CACF,CAAC;KACH;AACF;;AC/HD;AAkBA;;;;AAIG;AACG,MAAO,qBAAsB,SAAQ,QAAQ,CAAA;AAIjD,IAAA,WAAA,CAAY,OAAqC,EAAA;QAC/C,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,iCAAiC,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AACvC,QAAA,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACnD,IAAI,OAAO,CAAC,YAAY,EAAE;YACxB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;AAC1D,SAAA;KACF;IAED,MAAM,cAAc,CAAC,OAIpB,EAAA;AACC,QAAA,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAClB,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC;YACxE,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,WAAW,EAAE,OAAO,CAAC,WAAW;AACjC,SAAA,CAAC,CAAC;KACJ;AAES,IAAA,MAAM,UAAU,CACxB,MAAgB,EAChB,OAAuC,EAAA;QAEvC,IAAI;AACF,YAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,OAAO,KAAP,IAAA,IAAA,OAAO,uBAAP,OAAO,CAAE,SAAS,CAAC,CAAC,kBAAkB,CAAC;gBAC3F,MAAM;gBACN,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,IAAI,EAAE,IAAI,CAAC,iBAAiB;AAC5B,gBAAA,aAAa,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,aAAa;AACrC,gBAAA,SAAS,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,SAAS;AAC7B,gBAAA,MAAM,EAAE,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,MAAM;AACxB,aAAA,CAAC,CAAC;;;AAGH,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;AACtE,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAC9C,SAAA;KACF;AACF;;ACrED;AAgBA,MAAMA,QAAM,GAAG,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;AAE/D;;;;;;AAMG;MACU,2BAA2B,CAAA;AAkEtC;;;AAGG;IACH,WACE,CAAA,QAA2B,EAC3B,QAAgB,EAChB,+BAAuC,EACvC,8BAAsC,EACtC,oBAA6E,EAC7E,OAA4C,EAAA;AAE5C,QAAA,aAAa,CAACA,QAAM,EAAE,QAAQ,CAAC,CAAC;QAChC,IAAI,YAAY,GAAuB,+BAA+B,CAAC;AAEvE,QAAA,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;;AAE5C,YAAA,IAAI,CAAC,iBAAiB,GAAG,8BAA8B,CAAC;AACxD,YAAA,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC;;AAEzC,SAAA;AAAM,aAAA;;AAEL,YAAA,IAAI,CAAC,iBAAiB,GAAG,+BAA+B,CAAC;AACzD,YAAA,IAAI,CAAC,WAAW,GAAG,8BAAwC,CAAC;YAC5D,YAAY,GAAG,SAAS,CAAC;YACzB,OAAO,GAAG,oBAA0D,CAAC;AACtE,SAAA;;AAGD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,qBAAqB,CACpC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,KACV,YAAY;YACZ,QAAQ;YACR,QAAQ,EACR,sBAAsB,EAAE,OAAO,IAAI,EAAE,UACrCA,QAAM,EACN,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAA,CAAA,CACzC,CAAC;KACJ;AAED;;;;;;;AAOG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE,EAAA;AACrE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAC3B,CAAG,EAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA,SAAA,CAAW,EACnC,OAAO,EACP,OAAO,UAAU,KAAI;AACnB,YAAA,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,CAClC,CAAC;AACF,YAAA,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAE/B,YAAA,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;AACzC,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACpC,UAAU,CAAA,EAAA,EACb,8BAA8B,EAAE,IAAI,CAAC,8BAA8B,IACnE,CAAC;AACL,SAAC,CACF,CAAC;KACH;AACF;;ACrKD;AAiCA;;;AAGG;AACG,MAAO,cAAe,SAAQ,QAAQ,CAAA;AAM1C,IAAA,WAAA,CAAY,OAA8B,EAAA;QACxC,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;AACzD,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACjC,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;AACrD,QAAA,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AAC/C,QAAA,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;AACzD,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;KAC1C;;IAGD,MAAM,IAAI,CAAC,OAAuC,EAAA;QAChD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI;AACF,gBAAA,MAAM,KAAK,GAAG,MAAM,gBAAgB,CAClC,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,EACzC,IAAI,CAAC,oBAAoB,CAC1B,CAAC;AACF,gBAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG;oBACvC,UAAU,EAAE,KAAK,CAAC,UAAU;oBAC5B,UAAU,EAAE,KAAK,CAAC,mBAAmB;oBACrC,GAAG,EAAE,KAAK,CAAC,GAAG;iBACf,CAAC;AACH,aAAA;AAAC,YAAA,OAAO,KAAU,EAAE;AACnB,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;AACzC,gBAAA,MAAM,KAAK,CAAC;AACb,aAAA;AACF,SAAA;AAAM,aAAA;YACL,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AACvD,SAAA;AACD,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC5B;AAES,IAAA,MAAM,UAAU,CACxB,MAAgB,EAChB,UAAyC,EAAE,EAAA;QAE3C,IAAI;AACF,YAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC;gBACzF,MAAM;gBACN,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,YAAY,EAAE,IAAI,CAAC,kBAAkB;AACtC,aAAA,CAAC,CAAC;AACH,YAAA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,CAAC;AACtE,SAAA;AAAC,QAAA,OAAO,GAAQ,EAAE;YACjB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AAC9C,SAAA;KACF;AACF;;AC7FD;AAqBA,MAAM,cAAc,GAAG,sBAAsB,CAAC;AAC9C,MAAM,MAAM,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AAEhD;;AAEG;MACU,oBAAoB,CAAA;AAyD/B,IAAA,WAAA,CAAoB,OAAoC,EAAA;QAApC,IAAO,CAAA,OAAA,GAAP,OAAO,CAA6B;AACtD,QAAA,MAAM,EAAE,YAAY,EAAE,GAAG,OAA4C,CAAC;AACtE,QAAA,MAAM,EAAE,eAAe,EAAE,GAAG,OAAiD,CAAC;AAC9E,QAAA,MAAM,EACJ,QAAQ,EACR,QAAQ,EACR,kBAAkB,EAClB,0BAA0B,EAAE,4BAA4B,GACzD,GAAG,OAAO,CAAC;AACZ,QAAA,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,EAAE,YAAY,IAAI,eAAe,CAAC,IAAI,CAAC,kBAAkB,EAAE;AACvF,YAAA,MAAM,IAAI,KAAK,CACb,GAAG,cAAc,CAAA,uGAAA,CAAyG,CAC3H,CAAC;AACH,SAAA;AAED,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,4BAA4B,CAC7B,CAAC;AAEF,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,cAAc,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAC7B,IAAI,CAAC,OAAO,CACf,EAAA,EAAA,MAAM,EACN,sBAAsB,EAAE,IAAI,CAAC,OAAO,IACpC,CAAC;KACJ;AAED;;;;;;AAMG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE,EAAA;AACrE,QAAA,OAAO,aAAa,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAA,SAAA,CAAW,EAAE,OAAO,EAAE,OAAO,UAAU,KAAI;AACxF,YAAA,UAAU,CAAC,QAAQ,GAAG,yBAAyB,CAC7C,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,EACjC,MAAM,CACP,CAAC;AAEF,YAAA,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,QAAS,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AAC1D,SAAC,CAAC,CAAC;KACJ;AACF;;ACnID;AAuGA;;AAEG;SACa,yBAAyB,GAAA;IACvC,OAAO,IAAI,sBAAsB,EAAE,CAAC;AACtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}