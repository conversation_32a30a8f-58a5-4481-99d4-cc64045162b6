{"name": "@azure/identity", "sdk-type": "client", "version": "3.4.2", "description": "Provides credential implementations for Azure SDK libraries that can authenticate with Azure Active Directory", "main": "dist/index.js", "module": "dist-esm/src/index.js", "types": "./types/identity.d.ts", "browser": {"os": false, "process": false, "./dist-esm/src/credentials/azureCliCredential.js": "./dist-esm/src/credentials/azureCliCredential.browser.js", "./dist-esm/src/credentials/azureDeveloperCliCredential.js": "./dist-esm/src/credentials/azureDeveloperCliCredential.browser.js", "./dist-esm/src/credentials/environmentCredential.js": "./dist-esm/src/credentials/environmentCredential.browser.js", "./dist-esm/src/credentials/managedIdentityCredential/index.js": "./dist-esm/src/credentials/managedIdentityCredential/index.browser.js", "./dist-esm/src/credentials/clientCertificateCredential.js": "./dist-esm/src/credentials/clientCertificateCredential.browser.js", "./dist-esm/src/credentials/clientSecretCredential.js": "./dist-esm/src/credentials/clientSecretCredential.browser.js", "./dist-esm/src/credentials/clientAssertionCredential.js": "./dist-esm/src/credentials/clientAssertionCredential.browser.js", "./dist-esm/src/credentials/deviceCodeCredential.js": "./dist-esm/src/credentials/deviceCodeCredential.browser.js", "./dist-esm/src/credentials/defaultAzureCredential.js": "./dist-esm/src/credentials/defaultAzureCredential.browser.js", "./dist-esm/src/credentials/authorizationCodeCredential.js": "./dist-esm/src/credentials/authorizationCodeCredential.browser.js", "./dist-esm/src/credentials/interactiveBrowserCredential.js": "./dist-esm/src/credentials/interactiveBrowserCredential.browser.js", "./dist-esm/src/credentials/visualStudioCodeCredential.js": "./dist-esm/src/credentials/visualStudioCodeCredential.browser.js", "./dist-esm/src/credentials/usernamePasswordCredential.js": "./dist-esm/src/credentials/usernamePasswordCredential.browser.js", "./dist-esm/src/credentials/azurePowerShellCredential.js": "./dist-esm/src/credentials/azurePowerShellCredential.browser.js", "./dist-esm/src/credentials/azureApplicationCredential.js": "./dist-esm/src/credentials/azureApplicationCredential.browser.js", "./dist-esm/src/credentials/onBehalfOfCredential.js": "./dist-esm/src/credentials/onBehalfOfCredential.browser.js", "./dist-esm/src/credentials/workloadIdentityCredential.js": "./dist-esm/src/credentials/workloadIdentityCredential.browser.js", "./dist-esm/src/msal/utils.js": "./dist-esm/src/msal/utils.browser.js", "./dist-esm/src/util/authHostEnv.js": "./dist-esm/src/util/authHostEnv.browser.js", "./dist-esm/src/util/processMultiTenantRequest.js": "./dist-esm/src/util/processMultiTenantRequest.browser.js", "./dist-esm/src/tokenCache/TokenCachePersistence.js": "./dist-esm/src/tokenCache/TokenCachePersistence.browser.js", "./dist-esm/src/plugins/consumer.js": "./dist-esm/src/plugins/consumer.browser.js", "./dist-esm/test/httpRequests.js": "./dist-esm/test/httpRequests.browser.js"}, "//sampleConfiguration": {"productName": "Azure Identity", "productSlugs": ["azure", "azure-active-directory"], "requiredResources": {"Azure Active Directory App Registration": "https://docs.microsoft.com/azure/active-directory/develop/quickstart-register-app", "Azure Key Vault": "https://docs.microsoft.com/azure/key-vault/quick-create-portal"}}, "scripts": {"audit": "node ../../../common/scripts/rush-audit.js && rimraf node_modules package-lock.json && npm i --package-lock-only 2>&1 && npm audit", "build:samples": "echo Obsolete.", "build:test": "tsc -p . && dev-tool run bundle", "build": "npm run clean && npm run extract-api && tsc -p . && dev-tool run bundle", "clean": "rimraf dist dist-* types *.tgz *.log", "execute:samples": "dev-tool samples run samples-dev", "extract-api": "tsc -p . && api-extractor run --local", "format": "prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "check-format": "prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.ts\" \"test/**/*.ts\" \"samples-dev/**/*.ts\" \"*.{js,json}\"", "integration-test:browser": "echo skipped", "integration-test:node": "dev-tool run test:node-js-input -- --timeout 180000 'dist-esm/test/public/node/*.spec.js' 'dist-esm/test/internal/node/*.spec.js'", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "lint:fix": "eslint package.json api-extractor.json src test --ext .ts --fix --fix-type [problem,suggestion]", "lint": "eslint package.json api-extractor.json src test --ext .ts", "pack": "npm pack 2>&1", "test:browser": "npm run clean && npm run build:test && npm run unit-test:browser && npm run integration-test:browser", "test:node": "npm run clean && npm run build:test && npm run unit-test:node && npm run integration-test:node", "test": "npm run clean && npm run build:test && npm run unit-test && npm run integration-test", "unit-test:browser": "dev-tool run test:browser", "unit-test:node": "dev-tool run test:node-ts-input -- --timeout 300000 --exclude 'test/**/browser/**/*.spec.ts' 'test/**/**/*.spec.ts'", "unit-test:node:no-timeouts": "dev-tool run test:node-ts-input -- --timeout Infinite --exclude 'test/**/browser/**/*.spec.ts' 'test/**/**/*.spec.ts'", "unit-test": "npm run unit-test:node && npm run unit-test:browser"}, "files": ["dist/", "dist-esm/src/", "types/identity.d.ts", "README.md", "LICENSE"], "//metadata": {"constantPaths": [{"path": "src/constants.ts", "prefix": "SDK_VERSION"}]}, "engines": {"node": ">=14.0.0"}, "repository": "github:Azure/azure-sdk-for-js", "keywords": ["azure", "cloud", "active directory", "authentication", "credential", "certificate", "managed identity", "client secret", "access token"], "author": "Microsoft Corporation", "license": "MIT", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "homepage": "https://github.com/Azure/azure-sdk-for-js/tree/main/sdk/identity/identity/README.md", "sideEffects": false, "dependencies": {"@azure/abort-controller": "^1.0.0", "@azure/core-auth": "^1.5.0", "@azure/core-client": "^1.4.0", "@azure/core-rest-pipeline": "^1.1.0", "@azure/core-tracing": "^1.0.0", "@azure/core-util": "^1.6.1", "@azure/logger": "^1.0.0", "@azure/msal-browser": "^3.5.0", "@azure/msal-node": "^2.5.1", "events": "^3.0.0", "jws": "^4.0.0", "open": "^8.0.0", "stoppable": "^1.1.0", "tslib": "^2.2.0"}, "devDependencies": {"@azure-tools/test-recorder": "^3.0.0", "@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@azure/keyvault-keys": "^4.2.0", "@azure/test-utils": "^1.0.0", "@microsoft/api-extractor": "^7.31.1", "@types/chai": "^4.1.6", "@types/jsonwebtoken": "^9.0.0", "@types/jws": "^3.2.2", "@types/mocha": "^7.0.2", "@types/ms": "^0.7.31", "@types/node": "^14.0.0", "@types/sinon": "^10.0.0", "@types/stoppable": "^1.1.0", "chai": "^4.2.0", "cross-env": "^7.0.2", "dotenv": "^16.0.0", "eslint": "^8.0.0", "inherits": "^2.0.3", "jsonwebtoken": "^9.0.0", "karma": "^6.2.0", "karma-chrome-launcher": "^3.0.0", "karma-coverage": "^2.0.0", "karma-env-preprocessor": "^0.1.1", "karma-junit-reporter": "^2.0.1", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-sourcemap-loader": "^0.3.8", "mocha": "^7.1.1", "mocha-junit-reporter": "^2.0.0", "ms": "^2.1.3", "nyc": "^15.0.0", "prettier": "^2.5.1", "puppeteer": "^19.2.2", "rimraf": "^3.0.0", "sinon": "^15.0.0", "ts-node": "^10.0.0", "typescript": "~5.0.0"}}