/*! @azure/msal-common v13.3.1 2023-10-27 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
var CcsCredentialType;
(function (CcsCredentialType) {
    CcsCredentialType["HOME_ACCOUNT_ID"] = "home_account_id";
    CcsCredentialType["UPN"] = "UPN";
})(CcsCredentialType || (CcsCredentialType = {}));

export { CcsCredentialType };
//# sourceMappingURL=CcsCredential.js.map
