{"version": 3, "file": "Authority.js", "sources": ["../../src/authority/Authority.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthorityType } from \"./AuthorityType\";\r\nimport { isOpenIdConfigResponse, OpenIdConfigResponse } from \"./OpenIdConfigResponse\";\r\nimport { UrlString } from \"../url/UrlString\";\r\nimport { IUri } from \"../url/IUri\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { INetworkModule } from \"../network/INetworkModule\";\r\nimport { AADAuthorityConstants, AuthorityMetadataSource, Constants, RegionDiscoveryOutcomes } from \"../utils/Constants\";\r\nimport { EndpointMetadata, InstanceDiscoveryMetadata } from \"./AuthorityMetadata\";\r\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\r\nimport { ProtocolMode } from \"./ProtocolMode\";\r\nimport { ICacheManager } from \"../cache/interface/ICacheManager\";\r\nimport { AuthorityMetadataEntity } from \"../cache/entities/AuthorityMetadataEntity\";\r\nimport { AuthorityOptions, AzureCloudInstance } from \"./AuthorityOptions\";\r\nimport { CloudInstanceDiscoveryResponse, isCloudInstanceDiscoveryResponse } from \"./CloudInstanceDiscoveryResponse\";\r\nimport {\r\n    CloudInstanceDiscoveryErrorResponse,\r\n    isCloudInstanceDiscoveryErrorResponse\r\n} from \"./CloudInstanceDiscoveryErrorResponse\";\r\nimport { CloudDiscoveryMetadata } from \"./CloudDiscoveryMetadata\";\r\nimport { RegionDiscovery } from \"./RegionDiscovery\";\r\nimport { RegionDiscoveryMetadata } from \"./RegionDiscoveryMetadata\";\r\nimport { ImdsOptions } from \"./ImdsOptions\";\r\nimport { AzureCloudOptions } from \"../config/ClientConfiguration\";\r\nimport { Logger } from \"../logger/Logger\";\r\nimport { AuthError } from \"../error/AuthError\";\r\nimport { IPerformanceClient } from \"../telemetry/performance/IPerformanceClient\";\r\nimport { PerformanceEvents } from \"../telemetry/performance/PerformanceEvent\";\r\n\r\n/**\r\n * The authority class validates the authority URIs used by the user, and retrieves the OpenID Configuration Data from the\r\n * endpoint. It will store the pertinent config data in this object for use during token calls.\r\n */\r\nexport class Authority {\r\n    // Canonical authority url string\r\n    private _canonicalAuthority: UrlString;\r\n    // Canonicaly authority url components\r\n    private _canonicalAuthorityUrlComponents: IUri | null;\r\n    // Network interface to make requests with.\r\n    protected networkInterface: INetworkModule;\r\n    // Cache Manager to cache network responses\r\n    protected cacheManager: ICacheManager;\r\n    // Protocol mode to construct endpoints\r\n    private authorityOptions: AuthorityOptions;\r\n    // Authority metadata\r\n    private metadata: AuthorityMetadataEntity;\r\n    // Region discovery service\r\n    private regionDiscovery: RegionDiscovery;\r\n    // Region discovery metadata\r\n    public regionDiscoveryMetadata: RegionDiscoveryMetadata;\r\n    // Logger object\r\n    private logger: Logger;\r\n    // Performance client\r\n    protected performanceClient: IPerformanceClient | undefined;\r\n    // Correlation Id\r\n    protected correlationId: string | undefined;\r\n    // Reserved tenant domain names that will not be replaced with tenant id\r\n    private static reservedTenantDomains: Set<string> = (new Set([\r\n        \"{tenant}\",\r\n        \"{tenantid}\",\r\n        AADAuthorityConstants.COMMON,\r\n        AADAuthorityConstants.CONSUMERS,\r\n        AADAuthorityConstants.ORGANIZATIONS\r\n    ]));\r\n\r\n    constructor(\r\n        authority: string,\r\n        networkInterface: INetworkModule,\r\n        cacheManager: ICacheManager,\r\n        authorityOptions: AuthorityOptions,\r\n        logger: Logger,\r\n        performanceClient?: IPerformanceClient,\r\n        correlationId?: string\r\n    ) {\r\n        this.canonicalAuthority = authority;\r\n        this._canonicalAuthority.validateAsUri();\r\n        this.networkInterface = networkInterface;\r\n        this.cacheManager = cacheManager;\r\n        this.authorityOptions = authorityOptions;\r\n        this.regionDiscoveryMetadata = { region_used: undefined, region_source: undefined, region_outcome: undefined };\r\n        this.logger = logger;\r\n        this.performanceClient = performanceClient;\r\n        this.correlationId = correlationId;\r\n        this.regionDiscovery = new RegionDiscovery(networkInterface, this.performanceClient, this.correlationId);\r\n    }\r\n\r\n    /**\r\n     * Get {@link AuthorityType}\r\n     * @param authorityUri {@link IUri}\r\n     * @private\r\n     */\r\n    private getAuthorityType(authorityUri: IUri): AuthorityType {\r\n        // CIAM auth url pattern is being standardized as: <tenant>.ciamlogin.com\r\n        if (authorityUri.HostNameAndPort.endsWith(Constants.CIAM_AUTH_URL)) {\r\n            return AuthorityType.Ciam;\r\n        }\r\n\r\n        const pathSegments = authorityUri.PathSegments;\r\n        if (pathSegments.length) {\r\n            switch(pathSegments[0].toLowerCase()) {\r\n                case Constants.ADFS:\r\n                    return AuthorityType.Adfs;\r\n                case Constants.DSTS:\r\n                    return AuthorityType.Dsts;\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n        return AuthorityType.Default;\r\n    }\r\n\r\n    // See above for AuthorityType\r\n    public get authorityType(): AuthorityType {\r\n        return this.getAuthorityType(this.canonicalAuthorityUrlComponents);\r\n    }\r\n\r\n    /**\r\n     * ProtocolMode enum representing the way endpoints are constructed.\r\n     */\r\n    public get protocolMode(): ProtocolMode {\r\n        return this.authorityOptions.protocolMode;\r\n    }\r\n\r\n    /**\r\n     * Returns authorityOptions which can be used to reinstantiate a new authority instance\r\n     */\r\n    public get options(): AuthorityOptions {\r\n        return this.authorityOptions;\r\n    }\r\n\r\n    /**\r\n     * A URL that is the authority set by the developer\r\n     */\r\n    public get canonicalAuthority(): string {\r\n        return this._canonicalAuthority.urlString;\r\n    }\r\n\r\n    /**\r\n     * Sets canonical authority.\r\n     */\r\n    public set canonicalAuthority(url: string) {\r\n        this._canonicalAuthority = new UrlString(url);\r\n        this._canonicalAuthority.validateAsUri();\r\n        this._canonicalAuthorityUrlComponents = null;\r\n    }\r\n\r\n    /**\r\n     * Get authority components.\r\n     */\r\n    public get canonicalAuthorityUrlComponents(): IUri {\r\n        if (!this._canonicalAuthorityUrlComponents) {\r\n            this._canonicalAuthorityUrlComponents = this._canonicalAuthority.getUrlComponents();\r\n        }\r\n\r\n        return this._canonicalAuthorityUrlComponents;\r\n    }\r\n\r\n    /**\r\n     * Get hostname and port i.e. login.microsoftonline.com\r\n     */\r\n    public get hostnameAndPort(): string {\r\n        return this.canonicalAuthorityUrlComponents.HostNameAndPort.toLowerCase();\r\n    }\r\n\r\n    /**\r\n     * Get tenant for authority.\r\n     */\r\n    public get tenant(): string {\r\n        return this.canonicalAuthorityUrlComponents.PathSegments[0];\r\n    }\r\n\r\n    /**\r\n     * OAuth /authorize endpoint for requests\r\n     */\r\n    public get authorizationEndpoint(): string {\r\n        if(this.discoveryComplete()) {\r\n            return this.replacePath(this.metadata.authorization_endpoint);\r\n        } else {\r\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * OAuth /token endpoint for requests\r\n     */\r\n    public get tokenEndpoint(): string {\r\n        if(this.discoveryComplete()) {\r\n            return this.replacePath(this.metadata.token_endpoint);\r\n        } else {\r\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\r\n        }\r\n    }\r\n\r\n    public get deviceCodeEndpoint(): string {\r\n        if(this.discoveryComplete()) {\r\n            return this.replacePath(this.metadata.token_endpoint.replace(\"/token\", \"/devicecode\"));\r\n        } else {\r\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * OAuth logout endpoint for requests\r\n     */\r\n    public get endSessionEndpoint(): string {\r\n        if(this.discoveryComplete()) {\r\n            // ROPC policies may not have end_session_endpoint set\r\n            if (!this.metadata.end_session_endpoint) {\r\n                throw ClientAuthError.createLogoutNotSupportedError();\r\n            }\r\n            return this.replacePath(this.metadata.end_session_endpoint);\r\n        } else {\r\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * OAuth issuer for requests\r\n     */\r\n    public get selfSignedJwtAudience(): string {\r\n        if(this.discoveryComplete()) {\r\n            return this.replacePath(this.metadata.issuer);\r\n        } else {\r\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Jwks_uri for token signing keys\r\n     */\r\n    public get jwksUri(): string {\r\n        if(this.discoveryComplete()) {\r\n            return this.replacePath(this.metadata.jwks_uri);\r\n        } else {\r\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns a flag indicating that tenant name can be replaced in authority {@link IUri}\r\n     * @param authorityUri {@link IUri}\r\n     * @private\r\n     */\r\n    private canReplaceTenant(authorityUri: IUri): boolean {\r\n        return authorityUri.PathSegments.length === 1\r\n            && !Authority.reservedTenantDomains.has(authorityUri.PathSegments[0])\r\n            && this.getAuthorityType(authorityUri) === AuthorityType.Default\r\n            && this.protocolMode === ProtocolMode.AAD;\r\n    }\r\n\r\n    /**\r\n     * Replaces tenant in url path with current tenant. Defaults to common.\r\n     * @param urlString\r\n     */\r\n    private replaceTenant(urlString: string): string {\r\n        return urlString.replace(/{tenant}|{tenantid}/g, this.tenant);\r\n    }\r\n\r\n    /**\r\n     * Replaces path such as tenant or policy with the current tenant or policy.\r\n     * @param urlString\r\n     */\r\n    private replacePath(urlString: string): string {\r\n        let endpoint = urlString;\r\n        const cachedAuthorityUrl = new UrlString(this.metadata.canonical_authority);\r\n        const cachedAuthorityUrlComponents = cachedAuthorityUrl.getUrlComponents();\r\n        const cachedAuthorityParts = cachedAuthorityUrlComponents.PathSegments;\r\n        const currentAuthorityParts = this.canonicalAuthorityUrlComponents.PathSegments;\r\n\r\n        currentAuthorityParts.forEach((currentPart, index) => {\r\n            let cachedPart = cachedAuthorityParts[index];\r\n            if (index === 0 && this.canReplaceTenant(cachedAuthorityUrlComponents))\r\n            {\r\n                const tenantId = (new UrlString(this.metadata.authorization_endpoint)).getUrlComponents().PathSegments[0];\r\n                /**\r\n                 * Check if AAD canonical authority contains tenant domain name, for example \"testdomain.onmicrosoft.com\",\r\n                 * by comparing its first path segment to the corresponding authorization endpoint path segment, which is\r\n                 * always resolved with tenant id by OIDC.\r\n                 */\r\n                if (cachedPart !== tenantId) {\r\n                    this.logger.verbose(`Replacing tenant domain name ${cachedPart} with id ${tenantId}`);\r\n                    cachedPart = tenantId;\r\n                }\r\n            }\r\n            if (currentPart !== cachedPart) {\r\n                endpoint = endpoint.replace(`/${cachedPart}/`, `/${currentPart}/`);\r\n            }\r\n        });\r\n\r\n        return this.replaceTenant(endpoint);\r\n    }\r\n\r\n    /**\r\n     * The default open id configuration endpoint for any canonical authority.\r\n     */\r\n    protected get defaultOpenIdConfigurationEndpoint(): string {\r\n        if (\r\n            this.authorityType === AuthorityType.Adfs ||\r\n            this.authorityType === AuthorityType.Dsts ||\r\n            this.protocolMode === ProtocolMode.OIDC\r\n        ) {\r\n            return `${this.canonicalAuthority}.well-known/openid-configuration`;\r\n        }\r\n        return `${this.canonicalAuthority}v2.0/.well-known/openid-configuration`;\r\n    }\r\n\r\n    /**\r\n     * Boolean that returns whethr or not tenant discovery has been completed.\r\n     */\r\n    discoveryComplete(): boolean {\r\n        return !!this.metadata;\r\n    }\r\n\r\n    /**\r\n     * Perform endpoint discovery to discover aliases, preferred_cache, preferred_network\r\n     * and the /authorize, /token and logout endpoints.\r\n     */\r\n    public async resolveEndpointsAsync(): Promise<void> {\r\n\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityResolveEndpointsAsync, this.correlationId);\r\n\r\n        let metadataEntity = this.cacheManager.getAuthorityMetadataByAlias(this.hostnameAndPort);\r\n        if (!metadataEntity) {\r\n            metadataEntity = new AuthorityMetadataEntity();\r\n            metadataEntity.updateCanonicalAuthority(this.canonicalAuthority);\r\n        }\r\n\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityUpdateCloudDiscoveryMetadata, this.correlationId);\r\n        const cloudDiscoverySource = await this.updateCloudDiscoveryMetadata(metadataEntity);\r\n        this.canonicalAuthority = this.canonicalAuthority.replace(this.hostnameAndPort, metadataEntity.preferred_network);\r\n\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityUpdateEndpointMetadata, this.correlationId);\r\n        const endpointSource = await this.updateEndpointMetadata(metadataEntity);\r\n\r\n        if (cloudDiscoverySource !== AuthorityMetadataSource.CACHE && endpointSource !== AuthorityMetadataSource.CACHE) {\r\n            // Reset the expiration time unless both values came from a successful cache lookup\r\n            metadataEntity.resetExpiresAt();\r\n            metadataEntity.updateCanonicalAuthority(this.canonicalAuthority);\r\n        }\r\n\r\n        const cacheKey = this.cacheManager.generateAuthorityMetadataCacheKey(metadataEntity.preferred_cache);\r\n        this.cacheManager.setAuthorityMetadata(cacheKey, metadataEntity);\r\n        this.metadata = metadataEntity;\r\n    }\r\n\r\n    /**\r\n     * Update AuthorityMetadataEntity with new endpoints and return where the information came from\r\n     * @param metadataEntity\r\n     */\r\n    private async updateEndpointMetadata(metadataEntity: AuthorityMetadataEntity): Promise<AuthorityMetadataSource> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityUpdateEndpointMetadata, this.correlationId);\r\n\r\n        let metadata = this.getEndpointMetadataFromConfig();\r\n        if (metadata) {\r\n            metadataEntity.updateEndpointMetadata(metadata, false);\r\n            return AuthorityMetadataSource.CONFIG;\r\n        }\r\n\r\n        if (this.isAuthoritySameType(metadataEntity) && metadataEntity.endpointsFromNetwork && !metadataEntity.isExpired()) {\r\n            // No need to update\r\n            return AuthorityMetadataSource.CACHE;\r\n        }\r\n\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityGetEndpointMetadataFromNetwork, this.correlationId);\r\n        metadata = await this.getEndpointMetadataFromNetwork();\r\n        if (metadata) {\r\n            // If the user prefers to use an azure region replace the global endpoints with regional information.\r\n            if (this.authorityOptions.azureRegionConfiguration?.azureRegion) {\r\n                this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityUpdateMetadataWithRegionalInformation, this.correlationId);\r\n                metadata = await this.updateMetadataWithRegionalInformation(metadata);\r\n            }\r\n\r\n            metadataEntity.updateEndpointMetadata(metadata, true);\r\n            return AuthorityMetadataSource.NETWORK;\r\n        }\r\n\r\n        let harcodedMetadata = this.getEndpointMetadataFromHardcodedValues();\r\n        if (harcodedMetadata && !this.authorityOptions.skipAuthorityMetadataCache) {\r\n            // If the user prefers to use an azure region replace the global endpoints with regional information.\r\n            if (this.authorityOptions.azureRegionConfiguration?.azureRegion) {\r\n                this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityUpdateMetadataWithRegionalInformation, this.correlationId);\r\n                harcodedMetadata = await this.updateMetadataWithRegionalInformation(\r\n                    harcodedMetadata\r\n                );\r\n            }\r\n\r\n            metadataEntity.updateEndpointMetadata(harcodedMetadata, false);\r\n            return AuthorityMetadataSource.HARDCODED_VALUES;\r\n        } else {\r\n            throw ClientAuthError.createUnableToGetOpenidConfigError(\r\n                this.defaultOpenIdConfigurationEndpoint\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Compares the number of url components after the domain to determine if the cached\r\n     * authority metadata can be used for the requested authority. Protects against same domain different\r\n     * authority such as login.microsoftonline.com/tenant and login.microsoftonline.com/tfp/tenant/policy\r\n     * @param metadataEntity\r\n     */\r\n    private isAuthoritySameType(metadataEntity: AuthorityMetadataEntity): boolean {\r\n        const cachedAuthorityUrl = new UrlString(metadataEntity.canonical_authority);\r\n        const cachedParts = cachedAuthorityUrl.getUrlComponents().PathSegments;\r\n\r\n        return cachedParts.length === this.canonicalAuthorityUrlComponents.PathSegments.length;\r\n    }\r\n\r\n    /**\r\n     * Parse authorityMetadata config option\r\n     */\r\n    private getEndpointMetadataFromConfig(): OpenIdConfigResponse | null {\r\n        if (this.authorityOptions.authorityMetadata) {\r\n            try {\r\n                return JSON.parse(this.authorityOptions.authorityMetadata) as OpenIdConfigResponse;\r\n            } catch (e) {\r\n                throw ClientConfigurationError.createInvalidAuthorityMetadataError();\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets OAuth endpoints from the given OpenID configuration endpoint.\r\n     *\r\n     * @param hasHardcodedMetadata boolean\r\n     */\r\n    private async getEndpointMetadataFromNetwork(): Promise<OpenIdConfigResponse | null> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityGetEndpointMetadataFromNetwork, this.correlationId);\r\n\r\n        const options: ImdsOptions = {};\r\n\r\n        /*\r\n         * TODO: Add a timeout if the authority exists in our library's\r\n         * hardcoded list of metadata\r\n         */\r\n\r\n        try {\r\n            const response = await this.networkInterface.\r\n                sendGetRequestAsync<OpenIdConfigResponse>(this.defaultOpenIdConfigurationEndpoint, options);\r\n            return isOpenIdConfigResponse(response.body) ? response.body : null;\r\n        } catch (e) {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get OAuth endpoints for common authorities.\r\n     */\r\n    private getEndpointMetadataFromHardcodedValues(): OpenIdConfigResponse | null {\r\n        if (this.canonicalAuthority in EndpointMetadata) {\r\n            return EndpointMetadata[this.canonicalAuthority];\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Update the retrieved metadata with regional information.\r\n     * User selected Azure region will be used if configured.\r\n     */\r\n    private async updateMetadataWithRegionalInformation(\r\n        metadata: OpenIdConfigResponse\r\n    ): Promise<OpenIdConfigResponse> {\r\n        this.performanceClient?.addQueueMeasurement(\r\n            PerformanceEvents.AuthorityUpdateMetadataWithRegionalInformation,\r\n            this.correlationId\r\n        );\r\n\r\n        const userConfiguredAzureRegion = this.authorityOptions.azureRegionConfiguration?.azureRegion;\r\n\r\n        if (userConfiguredAzureRegion) {\r\n            if (userConfiguredAzureRegion !== Constants.AZURE_REGION_AUTO_DISCOVER_FLAG) {\r\n                this.regionDiscoveryMetadata.region_outcome = RegionDiscoveryOutcomes.CONFIGURED_NO_AUTO_DETECTION;\r\n                this.regionDiscoveryMetadata.region_used = userConfiguredAzureRegion;\r\n                return Authority.replaceWithRegionalInformation(\r\n                    metadata, \r\n                    userConfiguredAzureRegion\r\n                );\r\n            }\r\n    \r\n            this.performanceClient?.setPreQueueTime(\r\n                PerformanceEvents.RegionDiscoveryDetectRegion,\r\n                this.correlationId\r\n            );\r\n    \r\n            const autodetectedRegionName = await this.regionDiscovery.detectRegion(\r\n                this.authorityOptions.azureRegionConfiguration?.environmentRegion,\r\n                this.regionDiscoveryMetadata\r\n            );\r\n    \r\n            if (autodetectedRegionName) {\r\n                this.regionDiscoveryMetadata.region_outcome = RegionDiscoveryOutcomes.AUTO_DETECTION_REQUESTED_SUCCESSFUL;\r\n                this.regionDiscoveryMetadata.region_used = autodetectedRegionName;\r\n                return Authority.replaceWithRegionalInformation(\r\n                    metadata, \r\n                    autodetectedRegionName\r\n                );\r\n            }\r\n    \r\n            this.regionDiscoveryMetadata.region_outcome = RegionDiscoveryOutcomes.AUTO_DETECTION_REQUESTED_FAILED;\r\n        }\r\n\r\n        return metadata;\r\n    }\r\n\r\n    /**\r\n     * Updates the AuthorityMetadataEntity with new aliases, preferred_network and preferred_cache\r\n     * and returns where the information was retrieved from\r\n     * @param metadataEntity\r\n     * @returns AuthorityMetadataSource\r\n     */\r\n    private async updateCloudDiscoveryMetadata(metadataEntity: AuthorityMetadataEntity): Promise<AuthorityMetadataSource> {\r\n\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityUpdateCloudDiscoveryMetadata, this.correlationId);\r\n\r\n        // attempt to read metadata from the config\r\n        this.logger.verbose(\"Attempting to get cloud discovery metadata in the config\");\r\n        this.logger.verbosePii(`Known Authorities: ${this.authorityOptions.knownAuthorities || Constants.NOT_APPLICABLE}`);\r\n        this.logger.verbosePii(`Authority Metadata: ${this.authorityOptions.authorityMetadata || Constants.NOT_APPLICABLE}`);\r\n        this.logger.verbosePii(`Canonical Authority: ${metadataEntity.canonical_authority || Constants.NOT_APPLICABLE}`);\r\n        let metadata = this.getCloudDiscoveryMetadataFromConfig();\r\n        if (metadata) {\r\n            this.logger.verbose(\"Found cloud discovery metadata in the config.\");\r\n            metadataEntity.updateCloudDiscoveryMetadata(metadata, false);\r\n            return AuthorityMetadataSource.CONFIG;\r\n        }\r\n\r\n        // If the cached metadata came from config but that config was not passed to this instance, we must go to the network\r\n        this.logger.verbose(\"Did not find cloud discovery metadata in the config... Attempting to get cloud discovery metadata from the cache.\");\r\n        const metadataEntityExpired = metadataEntity.isExpired();\r\n        if (this.isAuthoritySameType(metadataEntity) && metadataEntity.aliasesFromNetwork && !metadataEntityExpired) {\r\n            this.logger.verbose(\"Found metadata in the cache.\");\r\n            // No need to update\r\n            return AuthorityMetadataSource.CACHE;\r\n        } else if (metadataEntityExpired) {\r\n            this.logger.verbose(\"The metadata entity is expired.\");\r\n        }\r\n\r\n        this.logger.verbose(\"Did not find cloud discovery metadata in the cache... Attempting to get cloud discovery metadata from the network.\");\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthorityGetCloudDiscoveryMetadataFromNetwork, this.correlationId);\r\n        metadata = await this.getCloudDiscoveryMetadataFromNetwork();\r\n        if (metadata) {\r\n            this.logger.verbose(\"cloud discovery metadata was successfully returned from getCloudDiscoveryMetadataFromNetwork()\");\r\n            metadataEntity.updateCloudDiscoveryMetadata(metadata, true);\r\n            return AuthorityMetadataSource.NETWORK;\r\n        }\r\n\r\n        this.logger.verbose(\"Did not find cloud discovery metadata from the network... Attempting to get cloud discovery metadata from hardcoded values.\");\r\n        const harcodedMetadata = this.getCloudDiscoveryMetadataFromHarcodedValues();\r\n        if (harcodedMetadata && !this.options.skipAuthorityMetadataCache) {\r\n            this.logger.verbose(\"Found cloud discovery metadata from hardcoded values.\");\r\n            metadataEntity.updateCloudDiscoveryMetadata(harcodedMetadata, false);\r\n            return AuthorityMetadataSource.HARDCODED_VALUES;\r\n        }\r\n\r\n        // Metadata could not be obtained from the config, cache, network or hardcoded values\r\n        this.logger.error(\"Did not find cloud discovery metadata from hardcoded values... Metadata could not be obtained from config, cache, network or hardcoded values. Throwing Untrusted Authority Error.\");\r\n        throw ClientConfigurationError.createUntrustedAuthorityError();\r\n    }\r\n\r\n    /**\r\n     * Parse cloudDiscoveryMetadata config or check knownAuthorities\r\n     */\r\n    private getCloudDiscoveryMetadataFromConfig(): CloudDiscoveryMetadata | null {\r\n\r\n        // CIAM does not support cloud discovery metadata\r\n        if (this.authorityType === AuthorityType.Ciam) {\r\n            this.logger.verbose(\"CIAM authorities do not support cloud discovery metadata, generate the aliases from authority host.\");\r\n            return Authority.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort);\r\n        }\r\n\r\n        // Check if network response was provided in config\r\n        if (this.authorityOptions.cloudDiscoveryMetadata) {\r\n            this.logger.verbose(\"The cloud discovery metadata has been provided as a network response, in the config.\");\r\n            try {\r\n                this.logger.verbose(\"Attempting to parse the cloud discovery metadata.\");\r\n                const parsedResponse = JSON.parse(this.authorityOptions.cloudDiscoveryMetadata) as CloudInstanceDiscoveryResponse;\r\n                const metadata = Authority.getCloudDiscoveryMetadataFromNetworkResponse(\r\n                    parsedResponse.metadata,\r\n                    this.hostnameAndPort\r\n                );\r\n                this.logger.verbose(\"Parsed the cloud discovery metadata.\");\r\n                if (metadata) {\r\n                    this.logger.verbose(\"There is returnable metadata attached to the parsed cloud discovery metadata.\");\r\n                    return metadata;\r\n                } else {\r\n                    this.logger.verbose(\"There is no metadata attached to the parsed cloud discovery metadata.\");\r\n                }\r\n            } catch (e) {\r\n                this.logger.verbose(\"Unable to parse the cloud discovery metadata. Throwing Invalid Cloud Discovery Metadata Error.\");\r\n                throw ClientConfigurationError.createInvalidCloudDiscoveryMetadataError();\r\n            }\r\n        }\r\n\r\n        // If cloudDiscoveryMetadata is empty or does not contain the host, check knownAuthorities\r\n        if (this.isInKnownAuthorities()) {\r\n            this.logger.verbose(\"The host is included in knownAuthorities. Creating new cloud discovery metadata from the host.\");\r\n            return Authority.createCloudDiscoveryMetadataFromHost(this.hostnameAndPort);\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Called to get metadata from network if CloudDiscoveryMetadata was not populated by config\r\n     *\r\n     * @param hasHardcodedMetadata boolean\r\n     */\r\n    private async getCloudDiscoveryMetadataFromNetwork(): Promise<CloudDiscoveryMetadata | null> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthorityGetCloudDiscoveryMetadataFromNetwork, this.correlationId);\r\n        const instanceDiscoveryEndpoint =\r\n            `${Constants.AAD_INSTANCE_DISCOVERY_ENDPT}${this.canonicalAuthority}oauth2/v2.0/authorize`;\r\n        const options: ImdsOptions = {};\r\n\r\n        /*\r\n         * TODO: Add a timeout if the authority exists in our library's\r\n         * hardcoded list of metadata\r\n         */\r\n\r\n        let match = null;\r\n        try {\r\n            const response =\r\n                await this.networkInterface.sendGetRequestAsync<CloudInstanceDiscoveryResponse | CloudInstanceDiscoveryErrorResponse>(\r\n                    instanceDiscoveryEndpoint,\r\n                    options\r\n                );\r\n\r\n            let typedResponseBody: CloudInstanceDiscoveryResponse | CloudInstanceDiscoveryErrorResponse;\r\n            let metadata: Array<CloudDiscoveryMetadata>;\r\n            if (isCloudInstanceDiscoveryResponse(response.body)) {\r\n                typedResponseBody = response.body as CloudInstanceDiscoveryResponse;\r\n                metadata = typedResponseBody.metadata;\r\n\r\n                this.logger.verbosePii(`tenant_discovery_endpoint is: ${typedResponseBody.tenant_discovery_endpoint}`);\r\n            } else if (isCloudInstanceDiscoveryErrorResponse(response.body)) {\r\n                this.logger.warning(`A CloudInstanceDiscoveryErrorResponse was returned. The cloud instance discovery network request's status code is: ${response.status}`);\r\n\r\n                typedResponseBody = response.body as CloudInstanceDiscoveryErrorResponse;\r\n                if (typedResponseBody.error === Constants.INVALID_INSTANCE) {\r\n                    this.logger.error(\"The CloudInstanceDiscoveryErrorResponse error is invalid_instance.\");\r\n                    return null;\r\n                }\r\n\r\n                this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error is ${typedResponseBody.error}`);\r\n                this.logger.warning(`The CloudInstanceDiscoveryErrorResponse error description is ${typedResponseBody.error_description}`);\r\n\r\n                this.logger.warning(\"Setting the value of the CloudInstanceDiscoveryMetadata (returned from the network) to []\");\r\n                metadata = [];\r\n            } else {\r\n                this.logger.error(\"AAD did not return a CloudInstanceDiscoveryResponse or CloudInstanceDiscoveryErrorResponse\");\r\n                return null;\r\n            }\r\n\r\n            this.logger.verbose(\"Attempting to find a match between the developer's authority and the CloudInstanceDiscoveryMetadata returned from the network request.\");\r\n            match = Authority.getCloudDiscoveryMetadataFromNetworkResponse(\r\n                metadata,\r\n                this.hostnameAndPort\r\n            );\r\n        } catch (error) {\r\n            if (error instanceof AuthError) {\r\n                this.logger.error(`There was a network error while attempting to get the cloud discovery instance metadata.\\nError: ${error.errorCode}\\nError Description: ${error.errorMessage}`);\r\n            } else {\r\n                const typedError = error as Error;\r\n                this.logger.error(`A non-MSALJS error was thrown while attempting to get the cloud instance discovery metadata.\\nError: ${typedError.name}\\nError Description: ${typedError.message}`);\r\n            }\r\n\r\n            return null;\r\n        }\r\n\r\n        // Custom Domain scenario, host is trusted because Instance Discovery call succeeded\r\n        if (!match) {\r\n            this.logger.warning(\"The developer's authority was not found within the CloudInstanceDiscoveryMetadata returned from the network request.\");\r\n            this.logger.verbose(\"Creating custom Authority for custom domain scenario.\");\r\n\r\n            match = Authority.createCloudDiscoveryMetadataFromHost(\r\n                this.hostnameAndPort\r\n            );\r\n        }\r\n        return match;\r\n    }\r\n\r\n    /**\r\n     * Get cloud discovery metadata for common authorities\r\n     */\r\n    private getCloudDiscoveryMetadataFromHarcodedValues(): CloudDiscoveryMetadata | null {\r\n        if (this.canonicalAuthority in InstanceDiscoveryMetadata) {\r\n            return InstanceDiscoveryMetadata[this.canonicalAuthority];\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Helper function to determine if this host is included in the knownAuthorities config option\r\n     */\r\n    private isInKnownAuthorities(): boolean {\r\n        const matches = this.authorityOptions.knownAuthorities.filter((authority) => {\r\n            return UrlString.getDomainFromUrl(authority).toLowerCase() === this.hostnameAndPort;\r\n        });\r\n\r\n        return matches.length > 0;\r\n    }\r\n\r\n    /**\r\n     * helper function to populate the authority based on azureCloudOptions\r\n     * @param authorityString\r\n     * @param azureCloudOptions\r\n     */\r\n    static generateAuthority(authorityString: string, azureCloudOptions?: AzureCloudOptions): string {\r\n        let authorityAzureCloudInstance;\r\n\r\n        if (azureCloudOptions && azureCloudOptions.azureCloudInstance !== AzureCloudInstance.None) {\r\n            const tenant = azureCloudOptions.tenant ? azureCloudOptions.tenant : Constants.DEFAULT_COMMON_TENANT;\r\n            authorityAzureCloudInstance = `${azureCloudOptions.azureCloudInstance}/${tenant}/`;\r\n        }\r\n\r\n        return authorityAzureCloudInstance ? authorityAzureCloudInstance : authorityString;\r\n    }\r\n\r\n    /**\r\n     * Creates cloud discovery metadata object from a given host\r\n     * @param host\r\n     */\r\n    static createCloudDiscoveryMetadataFromHost(host: string): CloudDiscoveryMetadata {\r\n        return {\r\n            preferred_network: host,\r\n            preferred_cache: host,\r\n            aliases: [host]\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Searches instance discovery network response for the entry that contains the host in the aliases list\r\n     * @param response\r\n     * @param authority\r\n     */\r\n    static getCloudDiscoveryMetadataFromNetworkResponse(\r\n        response: CloudDiscoveryMetadata[],\r\n        authority: string\r\n    ): CloudDiscoveryMetadata | null {\r\n        for (let i = 0; i < response.length; i++) {\r\n            const metadata = response[i];\r\n            if (metadata.aliases.indexOf(authority) > -1) {\r\n                return metadata;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * helper function to generate environment from authority object\r\n     */\r\n    getPreferredCache(): string {\r\n        if(this.discoveryComplete()) {\r\n            return this.metadata.preferred_cache;\r\n        } else {\r\n            throw ClientAuthError.createEndpointDiscoveryIncompleteError(\"Discovery incomplete.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns whether or not the provided host is an alias of this authority instance\r\n     * @param host\r\n     */\r\n    isAlias(host: string): boolean {\r\n        return this.metadata.aliases.indexOf(host) > -1;\r\n    }\r\n\r\n    /**\r\n     * Checks whether the provided host is that of a public cloud authority\r\n     *\r\n     * @param authority string\r\n     * @returns bool\r\n     */\r\n    static isPublicCloudAuthority(host: string): boolean {\r\n        return Constants.KNOWN_PUBLIC_CLOUDS.indexOf(host) >= 0;\r\n    }\r\n\r\n    /**\r\n     * Rebuild the authority string with the region\r\n     *\r\n     * @param host string\r\n     * @param region string\r\n     */\r\n    static buildRegionalAuthorityString(host: string, region: string, queryString?: string): string {\r\n        // Create and validate a Url string object with the initial authority string\r\n        const authorityUrlInstance = new UrlString(host);\r\n        authorityUrlInstance.validateAsUri();\r\n\r\n        const authorityUrlParts = authorityUrlInstance.getUrlComponents();\r\n\r\n        let hostNameAndPort= `${region}.${authorityUrlParts.HostNameAndPort}`;\r\n\r\n        if (this.isPublicCloudAuthority(authorityUrlParts.HostNameAndPort)) {\r\n            hostNameAndPort = `${region}.${Constants.REGIONAL_AUTH_PUBLIC_CLOUD_SUFFIX}`;\r\n        }\r\n\r\n        // Include the query string portion of the url\r\n        const url = UrlString.constructAuthorityUriFromObject({\r\n            ...authorityUrlInstance.getUrlComponents(),\r\n            HostNameAndPort: hostNameAndPort\r\n        }).urlString;\r\n\r\n        // Add the query string if a query string was provided\r\n        if (queryString) return `${url}?${queryString}`;\r\n\r\n        return url;\r\n    }\r\n\r\n    /**\r\n     * Replace the endpoints in the metadata object with their regional equivalents.\r\n     *\r\n     * @param metadata OpenIdConfigResponse\r\n     * @param azureRegion string\r\n     */\r\n    static replaceWithRegionalInformation(metadata: OpenIdConfigResponse, azureRegion: string): OpenIdConfigResponse {\r\n        metadata.authorization_endpoint = Authority.buildRegionalAuthorityString(metadata.authorization_endpoint, azureRegion);\r\n        // TODO: Enquire on whether we should leave the query string or remove it before releasing the feature\r\n        metadata.token_endpoint = Authority.buildRegionalAuthorityString(\r\n            metadata.token_endpoint, azureRegion, Constants.REGIONAL_AUTH_NON_MSI_QUERY_STRING\r\n        );\r\n\r\n        if (metadata.end_session_endpoint) {\r\n            metadata.end_session_endpoint = Authority.buildRegionalAuthorityString(metadata.end_session_endpoint, azureRegion);\r\n        }\r\n\r\n        return metadata;\r\n    }\r\n\r\n    /**\r\n     * Transform CIAM_AUTHORIY as per the below rules:\r\n     * If no path segments found and it is a CIAM authority (hostname ends with .ciamlogin.com), then transform it\r\n     *\r\n     * NOTE: The transformation path should go away once STS supports CIAM with the format: `tenantIdorDomain.ciamlogin.com`\r\n     * `ciamlogin.com` can also change in the future and we should accommodate the same\r\n     *\r\n     * @param authority\r\n     */\r\n    static transformCIAMAuthority(authority: string): string {\r\n        let ciamAuthority = authority.endsWith(Constants.FORWARD_SLASH) ? authority : `${authority}${Constants.FORWARD_SLASH}`;\r\n        const authorityUrl = new UrlString(authority);\r\n        const authorityUrlComponents = authorityUrl.getUrlComponents();\r\n\r\n        // check if transformation is needed\r\n        if (authorityUrlComponents.PathSegments.length === 0 && (authorityUrlComponents.HostNameAndPort.endsWith(Constants.CIAM_AUTH_URL))){\r\n            const tenantIdOrDomain = authorityUrlComponents.HostNameAndPort.split(\".\")[0];\r\n            ciamAuthority = `${ciamAuthority}${tenantIdOrDomain}${Constants.AAD_TENANT_DOMAIN_SUFFIX}`;\r\n        }\r\n\r\n        return ciamAuthority;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AA8BH;;;AAGG;AACH,IAAA,SAAA,kBAAA,YAAA;AAgCI,IAAA,SAAA,SAAA,CACI,SAAiB,EACjB,gBAAgC,EAChC,YAA2B,EAC3B,gBAAkC,EAClC,MAAc,EACd,iBAAsC,EACtC,aAAsB,EAAA;AAEtB,QAAA,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;AACpC,QAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;AACzC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,uBAAuB,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,CAAC;AAC/G,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;KAC5G;AAED;;;;AAIG;IACK,SAAgB,CAAA,SAAA,CAAA,gBAAA,GAAxB,UAAyB,YAAkB,EAAA;;QAEvC,IAAI,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE;YAChE,OAAO,aAAa,CAAC,IAAI,CAAC;AAC7B,SAAA;AAED,QAAA,IAAM,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;QAC/C,IAAI,YAAY,CAAC,MAAM,EAAE;AACrB,YAAA,QAAO,YAAY,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;gBAChC,KAAK,SAAS,CAAC,IAAI;oBACf,OAAO,aAAa,CAAC,IAAI,CAAC;gBAC9B,KAAK,SAAS,CAAC,IAAI;oBACf,OAAO,aAAa,CAAC,IAAI,CAAC;AAGjC,aAAA;AACJ,SAAA;QACD,OAAO,aAAa,CAAC,OAAO,CAAC;KAChC,CAAA;AAGD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAa,CAAA,SAAA,EAAA,eAAA,EAAA;;AAAxB,QAAA,GAAA,EAAA,YAAA;YACI,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;SACtE;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAY,CAAA,SAAA,EAAA,cAAA,EAAA;AAHvB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;SAC7C;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAO,CAAA,SAAA,EAAA,SAAA,EAAA;AAHlB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;YACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;SAChC;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAkB,CAAA,SAAA,EAAA,oBAAA,EAAA;AAH7B;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC;SAC7C;AAED;;AAEG;AACH,QAAA,GAAA,EAAA,UAA8B,GAAW,EAAA;YACrC,IAAI,CAAC,mBAAmB,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;AAC9C,YAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;AACzC,YAAA,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;SAChD;;;AATA,KAAA,CAAA,CAAA;AAcD,IAAA,MAAA,CAAA,cAAA,CAAW,SAA+B,CAAA,SAAA,EAAA,iCAAA,EAAA;AAH1C;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBACxC,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;AACvF,aAAA;YAED,OAAO,IAAI,CAAC,gCAAgC,CAAC;SAChD;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;AAH1B;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;YACI,OAAO,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;SAC7E;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAHjB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;YACI,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;SAC/D;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAqB,CAAA,SAAA,EAAA,uBAAA,EAAA;AAHhC;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBACzB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;AACjE,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAa,CAAA,SAAA,EAAA,eAAA,EAAA;AAHxB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBACzB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AACzD,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAED,IAAA,MAAA,CAAA,cAAA,CAAW,SAAkB,CAAA,SAAA,EAAA,oBAAA,EAAA;AAA7B,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACzB,gBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC;AAC1F,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAkB,CAAA,SAAA,EAAA,oBAAA,EAAA;AAH7B;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;;AAEzB,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE;AACrC,oBAAA,MAAM,eAAe,CAAC,6BAA6B,EAAE,CAAC;AACzD,iBAAA;gBACD,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;AAC/D,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAqB,CAAA,SAAA,EAAA,uBAAA,EAAA;AAHhC;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBACzB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACjD,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAO,CAAA,SAAA,EAAA,SAAA,EAAA;AAHlB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBACzB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACnD,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,aAAA;SACJ;;;AAAA,KAAA,CAAA,CAAA;AAED;;;;AAIG;IACK,SAAgB,CAAA,SAAA,CAAA,gBAAA,GAAxB,UAAyB,YAAkB,EAAA;AACvC,QAAA,OAAO,YAAY,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC;AACtC,eAAA,CAAC,SAAS,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;eAClE,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,aAAa,CAAC,OAAO;AAC7D,eAAA,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC,GAAG,CAAC;KACjD,CAAA;AAED;;;AAGG;IACK,SAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,SAAiB,EAAA;QACnC,OAAO,SAAS,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;KACjE,CAAA;AAED;;;AAGG;IACK,SAAW,CAAA,SAAA,CAAA,WAAA,GAAnB,UAAoB,SAAiB,EAAA;QAArC,IA4BC,KAAA,GAAA,IAAA,CAAA;QA3BG,IAAI,QAAQ,GAAG,SAAS,CAAC;QACzB,IAAM,kBAAkB,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;AAC5E,QAAA,IAAM,4BAA4B,GAAG,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;AAC3E,QAAA,IAAM,oBAAoB,GAAG,4BAA4B,CAAC,YAAY,CAAC;AACvE,QAAA,IAAM,qBAAqB,GAAG,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC;AAEhF,QAAA,qBAAqB,CAAC,OAAO,CAAC,UAAC,WAAW,EAAE,KAAK,EAAA;AAC7C,YAAA,IAAI,UAAU,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,KAAK,KAAK,CAAC,IAAI,KAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,EACtE;gBACI,IAAM,QAAQ,GAAG,CAAC,IAAI,SAAS,CAAC,KAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE,gBAAgB,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AAC1G;;;;AAIG;gBACH,IAAI,UAAU,KAAK,QAAQ,EAAE;oBACzB,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kCAAgC,UAAU,GAAA,WAAA,GAAY,QAAU,CAAC,CAAC;oBACtF,UAAU,GAAG,QAAQ,CAAC;AACzB,iBAAA;AACJ,aAAA;YACD,IAAI,WAAW,KAAK,UAAU,EAAE;AAC5B,gBAAA,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAA,GAAI,UAAU,GAAA,GAAG,EAAE,GAAA,GAAI,WAAW,GAAA,GAAG,CAAC,CAAC;AACtE,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;KACvC,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAc,SAAkC,CAAA,SAAA,EAAA,oCAAA,EAAA;AAHhD;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACI,YAAA,IACI,IAAI,CAAC,aAAa,KAAK,aAAa,CAAC,IAAI;AACzC,gBAAA,IAAI,CAAC,aAAa,KAAK,aAAa,CAAC,IAAI;AACzC,gBAAA,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,EACzC;AACE,gBAAA,OAAU,IAAI,CAAC,kBAAkB,GAAA,kCAAkC,CAAC;AACvE,aAAA;AACD,YAAA,OAAU,IAAI,CAAC,kBAAkB,GAAA,uCAAuC,CAAC;SAC5E;;;AAAA,KAAA,CAAA,CAAA;AAED;;AAEG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;AACI,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;KAC1B,CAAA;AAED;;;AAGG;AACU,IAAA,SAAA,CAAA,SAAA,CAAA,qBAAqB,GAAlC,YAAA;;;;;;;AAEI,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;wBAE9G,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,2BAA2B,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBACzF,IAAI,CAAC,cAAc,EAAE;AACjB,4BAAA,cAAc,GAAG,IAAI,uBAAuB,EAAE,CAAC;AAC/C,4BAAA,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACpE,yBAAA;AAED,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,qCAAqC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AACxF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAC,CAAA,CAAA;;AAA9E,wBAAA,oBAAoB,GAAG,EAAuD,CAAA,IAAA,EAAA,CAAA;AACpF,wBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,iBAAiB,CAAC,CAAC;AAElH,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,+BAA+B,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AACxF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAA,CAAA;;AAAlE,wBAAA,cAAc,GAAG,EAAiD,CAAA,IAAA,EAAA,CAAA;wBAExE,IAAI,oBAAoB,KAAK,uBAAuB,CAAC,KAAK,IAAI,cAAc,KAAK,uBAAuB,CAAC,KAAK,EAAE;;4BAE5G,cAAc,CAAC,cAAc,EAAE,CAAC;AAChC,4BAAA,cAAc,CAAC,wBAAwB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACpE,yBAAA;wBAEK,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,iCAAiC,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;wBACrG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AACjE,wBAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;;;;;AAClC,KAAA,CAAA;AAED;;;AAGG;IACW,SAAsB,CAAA,SAAA,CAAA,sBAAA,GAApC,UAAqC,cAAuC,EAAA;;;;;;;AACxE,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,+BAA+B,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AAE/G,wBAAA,QAAQ,GAAG,IAAI,CAAC,6BAA6B,EAAE,CAAC;AACpD,wBAAA,IAAI,QAAQ,EAAE;AACV,4BAAA,cAAc,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;4BACvD,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,MAAM,CAAC,CAAA;AACzC,yBAAA;AAED,wBAAA,IAAI,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC,oBAAoB,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE;;4BAEhH,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,KAAK,CAAC,CAAA;AACxC,yBAAA;AAED,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,uCAAuC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AAC5G,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,8BAA8B,EAAE,CAAA,CAAA;;wBAAtD,QAAQ,GAAG,SAA2C,CAAC;AACnD,wBAAA,IAAA,CAAA,QAAQ,EAAR,OAAQ,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAEJ,wBAAA,IAAA,EAAA,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,0CAAE,WAAW,CAAA,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC3D,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,8CAA8C,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AACnH,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,qCAAqC,CAAC,QAAQ,CAAC,CAAA,CAAA;;wBAArE,QAAQ,GAAG,SAA0D,CAAC;;;AAG1E,wBAAA,cAAc,CAAC,sBAAsB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;wBACtD,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,OAAO,CAAC,CAAA;;AAGvC,wBAAA,gBAAgB,GAAG,IAAI,CAAC,sCAAsC,EAAE,CAAC;8BACjE,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAA,EAArE,OAAqE,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAEjE,wBAAA,IAAA,EAAA,CAAA,EAAA,GAAA,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,0CAAE,WAAW,CAAA,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAC3D,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,8CAA8C,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AAC3G,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,qCAAqC,CAC/D,gBAAgB,CACnB,CAAA,CAAA;;wBAFD,gBAAgB,GAAG,SAElB,CAAC;;;AAGN,wBAAA,cAAc,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;wBAC/D,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,gBAAgB,CAAC,CAAA;4BAEhD,MAAM,eAAe,CAAC,kCAAkC,CACpD,IAAI,CAAC,kCAAkC,CAC1C,CAAC;;;;AAET,KAAA,CAAA;AAED;;;;;AAKG;IACK,SAAmB,CAAA,SAAA,CAAA,mBAAA,GAA3B,UAA4B,cAAuC,EAAA;QAC/D,IAAM,kBAAkB,GAAG,IAAI,SAAS,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;QAC7E,IAAM,WAAW,GAAG,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,YAAY,CAAC;QAEvE,OAAO,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,+BAA+B,CAAC,YAAY,CAAC,MAAM,CAAC;KAC1F,CAAA;AAED;;AAEG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,6BAA6B,GAArC,YAAA;AACI,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE;YACzC,IAAI;gBACA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAyB,CAAC;AACtF,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,MAAM,wBAAwB,CAAC,mCAAmC,EAAE,CAAC;AACxE,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;AACW,IAAA,SAAA,CAAA,SAAA,CAAA,8BAA8B,GAA5C,YAAA;;;;;;;AACI,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,uCAAuC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;wBAErH,OAAO,GAAgB,EAAE,CAAC;;;;wBAQX,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,gBAAgB;AACxC,gCAAA,mBAAmB,CAAuB,IAAI,CAAC,kCAAkC,EAAE,OAAO,CAAC,CAAA,CAAA;;AADzF,wBAAA,QAAQ,GAAG,EAC8E,CAAA,IAAA,EAAA,CAAA;AAC/F,wBAAA,OAAA,CAAA,CAAA,aAAO,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,CAAA;;;AAEpE,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;;AAEnB,KAAA,CAAA;AAED;;AAEG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,sCAAsC,GAA9C,YAAA;AACI,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,EAAE;AAC7C,YAAA,OAAO,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACpD,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACW,SAAqC,CAAA,SAAA,CAAA,qCAAA,GAAnD,UACI,QAA8B,EAAA;;;;;;;AAE9B,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CACvC,iBAAiB,CAAC,8CAA8C,EAChE,IAAI,CAAC,aAAa,CACpB,CAAA;wBAEI,yBAAyB,GAAA,CAAA,EAAA,GAAG,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC;AAE1F,wBAAA,IAAA,CAAA,yBAAyB,EAAzB,OAAyB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACzB,wBAAA,IAAI,yBAAyB,KAAK,SAAS,CAAC,+BAA+B,EAAE;4BACzE,IAAI,CAAC,uBAAuB,CAAC,cAAc,GAAG,uBAAuB,CAAC,4BAA4B,CAAC;AACnG,4BAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,GAAG,yBAAyB,CAAC;4BACrE,OAAO,CAAA,CAAA,aAAA,SAAS,CAAC,8BAA8B,CAC3C,QAAQ,EACR,yBAAyB,CAC5B,CAAC,CAAA;AACL,yBAAA;AAED,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CACnC,iBAAiB,CAAC,2BAA2B,EAC7C,IAAI,CAAC,aAAa,CACpB,CAAA;AAE6B,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAA,CAAA,EAAA,GAClE,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,0CAAE,iBAAiB,EACjE,IAAI,CAAC,uBAAuB,CAC/B,CAAA,CAAA;;AAHK,wBAAA,sBAAsB,GAAG,EAG9B,CAAA,IAAA,EAAA,CAAA;AAED,wBAAA,IAAI,sBAAsB,EAAE;4BACxB,IAAI,CAAC,uBAAuB,CAAC,cAAc,GAAG,uBAAuB,CAAC,mCAAmC,CAAC;AAC1G,4BAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,GAAG,sBAAsB,CAAC;4BAClE,OAAO,CAAA,CAAA,aAAA,SAAS,CAAC,8BAA8B,CAC3C,QAAQ,EACR,sBAAsB,CACzB,CAAC,CAAA;AACL,yBAAA;wBAED,IAAI,CAAC,uBAAuB,CAAC,cAAc,GAAG,uBAAuB,CAAC,+BAA+B,CAAC;;AAG1G,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,QAAQ,CAAC,CAAA;;;;AACnB,KAAA,CAAA;AAED;;;;;AAKG;IACW,SAA4B,CAAA,SAAA,CAAA,4BAAA,GAA1C,UAA2C,cAAuC,EAAA;;;;;;;AAE9E,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,qCAAqC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;;AAGzH,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0DAA0D,CAAC,CAAC;AAChF,wBAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,yBAAsB,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,IAAI,SAAS,CAAC,cAAc,CAAE,CAAC,CAAC;AACnH,wBAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,0BAAuB,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,IAAI,SAAS,CAAC,cAAc,CAAE,CAAC,CAAC;AACrH,wBAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,uBAAwB,IAAA,cAAc,CAAC,mBAAmB,IAAI,SAAS,CAAC,cAAc,CAAE,CAAC,CAAC;AAC7G,wBAAA,QAAQ,GAAG,IAAI,CAAC,mCAAmC,EAAE,CAAC;AAC1D,wBAAA,IAAI,QAAQ,EAAE;AACV,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;AACrE,4BAAA,cAAc,CAAC,4BAA4B,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;4BAC7D,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,MAAM,CAAC,CAAA;AACzC,yBAAA;;AAGD,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mHAAmH,CAAC,CAAC;AACnI,wBAAA,qBAAqB,GAAG,cAAc,CAAC,SAAS,EAAE,CAAC;AACzD,wBAAA,IAAI,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,cAAc,CAAC,kBAAkB,IAAI,CAAC,qBAAqB,EAAE;AACzG,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;;4BAEpD,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,KAAK,CAAC,CAAA;AACxC,yBAAA;AAAM,6BAAA,IAAI,qBAAqB,EAAE;AAC9B,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AAC1D,yBAAA;AAED,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oHAAoH,CAAC,CAAC;AAC1I,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AAClH,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,oCAAoC,EAAE,CAAA,CAAA;;wBAA5D,QAAQ,GAAG,SAAiD,CAAC;AAC7D,wBAAA,IAAI,QAAQ,EAAE;AACV,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gGAAgG,CAAC,CAAC;AACtH,4BAAA,cAAc,CAAC,4BAA4B,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;4BAC5D,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,OAAO,CAAC,CAAA;AAC1C,yBAAA;AAED,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6HAA6H,CAAC,CAAC;AAC7I,wBAAA,gBAAgB,GAAG,IAAI,CAAC,2CAA2C,EAAE,CAAC;wBAC5E,IAAI,gBAAgB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;AAC9D,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uDAAuD,CAAC,CAAC;AAC7E,4BAAA,cAAc,CAAC,4BAA4B,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;4BACrE,OAAO,CAAA,CAAA,aAAA,uBAAuB,CAAC,gBAAgB,CAAC,CAAA;AACnD,yBAAA;;AAGD,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oLAAoL,CAAC,CAAC;AACxM,wBAAA,MAAM,wBAAwB,CAAC,6BAA6B,EAAE,CAAC;;;;AAClE,KAAA,CAAA;AAED;;AAEG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,mCAAmC,GAA3C,YAAA;;AAGI,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,aAAa,CAAC,IAAI,EAAE;AAC3C,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qGAAqG,CAAC,CAAC;YAC3H,OAAO,SAAS,CAAC,oCAAoC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAC/E,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,EAAE;AAC9C,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sFAAsF,CAAC,CAAC;YAC5G,IAAI;AACA,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mDAAmD,CAAC,CAAC;AACzE,gBAAA,IAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAmC,CAAC;AAClH,gBAAA,IAAM,QAAQ,GAAG,SAAS,CAAC,4CAA4C,CACnE,cAAc,CAAC,QAAQ,EACvB,IAAI,CAAC,eAAe,CACvB,CAAC;AACF,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5D,gBAAA,IAAI,QAAQ,EAAE;AACV,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+EAA+E,CAAC,CAAC;AACrG,oBAAA,OAAO,QAAQ,CAAC;AACnB,iBAAA;AAAM,qBAAA;AACH,oBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uEAAuE,CAAC,CAAC;AAChG,iBAAA;AACJ,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gGAAgG,CAAC,CAAC;AACtH,gBAAA,MAAM,wBAAwB,CAAC,wCAAwC,EAAE,CAAC;AAC7E,aAAA;AACJ,SAAA;;AAGD,QAAA,IAAI,IAAI,CAAC,oBAAoB,EAAE,EAAE;AAC7B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gGAAgG,CAAC,CAAC;YACtH,OAAO,SAAS,CAAC,oCAAoC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAC/E,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;AACW,IAAA,SAAA,CAAA,SAAA,CAAA,oCAAoC,GAAlD,YAAA;;;;;;;AACI,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,6CAA6C,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;wBAC3H,yBAAyB,GAC3B,EAAG,GAAA,SAAS,CAAC,4BAA4B,GAAG,IAAI,CAAC,kBAAkB,GAAA,uBAAuB,CAAC;wBACzF,OAAO,GAAgB,EAAE,CAAC;wBAO5B,KAAK,GAAG,IAAI,CAAC;;;;wBAGT,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAC3C,yBAAyB,EACzB,OAAO,CACV,CAAA,CAAA;;AAJC,wBAAA,QAAQ,GACV,EAGC,CAAA,IAAA,EAAA,CAAA;AAED,wBAAA,iBAAiB,SAAsE,CAAC;AACxF,wBAAA,QAAQ,SAA+B,CAAC;AAC5C,wBAAA,IAAI,gCAAgC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACjD,4BAAA,iBAAiB,GAAG,QAAQ,CAAC,IAAsC,CAAC;AACpE,4BAAA,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC;4BAEtC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,mCAAiC,iBAAiB,CAAC,yBAA2B,CAAC,CAAC;AAC1G,yBAAA;AAAM,6BAAA,IAAI,qCAAqC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;4BAC7D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wHAAsH,QAAQ,CAAC,MAAQ,CAAC,CAAC;AAE7J,4BAAA,iBAAiB,GAAG,QAAQ,CAAC,IAA2C,CAAC;AACzE,4BAAA,IAAI,iBAAiB,CAAC,KAAK,KAAK,SAAS,CAAC,gBAAgB,EAAE;AACxD,gCAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC;AACxF,gCAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,6BAAA;4BAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sDAAoD,iBAAiB,CAAC,KAAO,CAAC,CAAC;4BACnG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kEAAgE,iBAAiB,CAAC,iBAAmB,CAAC,CAAC;AAE3H,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,2FAA2F,CAAC,CAAC;4BACjH,QAAQ,GAAG,EAAE,CAAC;AACjB,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4FAA4F,CAAC,CAAC;AAChH,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,yBAAA;AAED,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wIAAwI,CAAC,CAAC;wBAC9J,KAAK,GAAG,SAAS,CAAC,4CAA4C,CAC1D,QAAQ,EACR,IAAI,CAAC,eAAe,CACvB,CAAC;;;;wBAEF,IAAI,OAAK,YAAY,SAAS,EAAE;AAC5B,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mGAAoG,GAAA,OAAK,CAAC,SAAS,GAAwB,uBAAA,GAAA,OAAK,CAAC,YAAc,CAAC,CAAC;AACtL,yBAAA;AAAM,6BAAA;4BACG,UAAU,GAAG,OAAc,CAAC;AAClC,4BAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uGAAwG,GAAA,UAAU,CAAC,IAAI,GAAwB,uBAAA,GAAA,UAAU,CAAC,OAAS,CAAC,CAAC;AAC1L,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;wBAIhB,IAAI,CAAC,KAAK,EAAE;AACR,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sHAAsH,CAAC,CAAC;AAC5I,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uDAAuD,CAAC,CAAC;4BAE7E,KAAK,GAAG,SAAS,CAAC,oCAAoC,CAClD,IAAI,CAAC,eAAe,CACvB,CAAC;AACL,yBAAA;AACD,wBAAA,OAAA,CAAA,CAAA,aAAO,KAAK,CAAC,CAAA;;;;AAChB,KAAA,CAAA;AAED;;AAEG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,2CAA2C,GAAnD,YAAA;AACI,QAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,yBAAyB,EAAE;AACtD,YAAA,OAAO,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC7D,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;AAEG;AACK,IAAA,SAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,YAAA;QAAA,IAMC,KAAA,GAAA,IAAA,CAAA;QALG,IAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAC,SAAS,EAAA;AACpE,YAAA,OAAO,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,KAAK,KAAI,CAAC,eAAe,CAAC;AACxF,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;KAC7B,CAAA;AAED;;;;AAIG;AACI,IAAA,SAAA,CAAA,iBAAiB,GAAxB,UAAyB,eAAuB,EAAE,iBAAqC,EAAA;AACnF,QAAA,IAAI,2BAA2B,CAAC;QAEhC,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,IAAI,EAAE;AACvF,YAAA,IAAM,MAAM,GAAG,iBAAiB,CAAC,MAAM,GAAG,iBAAiB,CAAC,MAAM,GAAG,SAAS,CAAC,qBAAqB,CAAC;AACrG,YAAA,2BAA2B,GAAM,iBAAiB,CAAC,kBAAkB,GAAI,GAAA,GAAA,MAAM,MAAG,CAAC;AACtF,SAAA;QAED,OAAO,2BAA2B,GAAG,2BAA2B,GAAG,eAAe,CAAC;KACtF,CAAA;AAED;;;AAGG;IACI,SAAoC,CAAA,oCAAA,GAA3C,UAA4C,IAAY,EAAA;QACpD,OAAO;AACH,YAAA,iBAAiB,EAAE,IAAI;AACvB,YAAA,eAAe,EAAE,IAAI;YACrB,OAAO,EAAE,CAAC,IAAI,CAAC;SAClB,CAAC;KACL,CAAA;AAED;;;;AAIG;AACI,IAAA,SAAA,CAAA,4CAA4C,GAAnD,UACI,QAAkC,EAClC,SAAiB,EAAA;AAEjB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,IAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;AAC1C,gBAAA,OAAO,QAAQ,CAAC;AACnB,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;AAEG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;AACI,QAAA,IAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;AACxC,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,eAAe,CAAC,sCAAsC,CAAC,uBAAuB,CAAC,CAAC;AACzF,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,SAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,IAAY,EAAA;AAChB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;KACnD,CAAA;AAED;;;;;AAKG;IACI,SAAsB,CAAA,sBAAA,GAA7B,UAA8B,IAAY,EAAA;QACtC,OAAO,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC3D,CAAA;AAED;;;;;AAKG;AACI,IAAA,SAAA,CAAA,4BAA4B,GAAnC,UAAoC,IAAY,EAAE,MAAc,EAAE,WAAoB,EAAA;;AAElF,QAAA,IAAM,oBAAoB,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;QACjD,oBAAoB,CAAC,aAAa,EAAE,CAAC;AAErC,QAAA,IAAM,iBAAiB,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,CAAC;AAElE,QAAA,IAAI,eAAe,GAAK,MAAM,SAAI,iBAAiB,CAAC,eAAiB,CAAC;QAEtE,IAAI,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE;AAChE,YAAA,eAAe,GAAM,MAAM,GAAA,GAAA,GAAI,SAAS,CAAC,iCAAmC,CAAC;AAChF,SAAA;;AAGD,QAAA,IAAM,GAAG,GAAG,SAAS,CAAC,+BAA+B,uBAC9C,oBAAoB,CAAC,gBAAgB,EAAE,KAC1C,eAAe,EAAE,eAAe,EAClC,CAAA,CAAA,CAAC,SAAS,CAAC;;AAGb,QAAA,IAAI,WAAW;YAAE,OAAU,GAAG,GAAI,GAAA,GAAA,WAAa,CAAC;AAEhD,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;AAED;;;;;AAKG;AACI,IAAA,SAAA,CAAA,8BAA8B,GAArC,UAAsC,QAA8B,EAAE,WAAmB,EAAA;AACrF,QAAA,QAAQ,CAAC,sBAAsB,GAAG,SAAS,CAAC,4BAA4B,CAAC,QAAQ,CAAC,sBAAsB,EAAE,WAAW,CAAC,CAAC;;AAEvH,QAAA,QAAQ,CAAC,cAAc,GAAG,SAAS,CAAC,4BAA4B,CAC5D,QAAQ,CAAC,cAAc,EAAE,WAAW,EAAE,SAAS,CAAC,kCAAkC,CACrF,CAAC;QAEF,IAAI,QAAQ,CAAC,oBAAoB,EAAE;AAC/B,YAAA,QAAQ,CAAC,oBAAoB,GAAG,SAAS,CAAC,4BAA4B,CAAC,QAAQ,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;AACtH,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB,CAAA;AAED;;;;;;;;AAQG;IACI,SAAsB,CAAA,sBAAA,GAA7B,UAA8B,SAAiB,EAAA;QAC3C,IAAI,aAAa,GAAG,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,SAAS,GAAG,EAAA,GAAG,SAAS,GAAG,SAAS,CAAC,aAAe,CAAC;AACvH,QAAA,IAAM,YAAY,GAAG,IAAI,SAAS,CAAC,SAAS,CAAC,CAAC;AAC9C,QAAA,IAAM,sBAAsB,GAAG,YAAY,CAAC,gBAAgB,EAAE,CAAC;;QAG/D,IAAI,sBAAsB,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,KAAK,sBAAsB,CAAC,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,EAAC;AAC/H,YAAA,IAAM,gBAAgB,GAAG,sBAAsB,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9E,aAAa,GAAG,KAAG,aAAa,GAAG,gBAAgB,GAAG,SAAS,CAAC,wBAA0B,CAAC;AAC9F,SAAA;AAED,QAAA,OAAO,aAAa,CAAC;KACxB,CAAA;;AA5xBc,IAAA,SAAA,CAAA,qBAAqB,IAAiB,IAAI,GAAG,CAAC;QACzD,UAAU;QACV,YAAY;AACZ,QAAA,qBAAqB,CAAC,MAAM;AAC5B,QAAA,qBAAqB,CAAC,SAAS;AAC/B,QAAA,qBAAqB,CAAC,aAAa;AACtC,KAAA,CAAC,CAAC,CAAC;IAuxBR,OAAC,SAAA,CAAA;AAAA,CArzBD,EAqzBC;;;;"}