{"version": 3, "file": "AuthorityOptions.js", "sources": ["../../src/authority/AuthorityOptions.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ProtocolMode } from \"./ProtocolMode\";\r\nimport { AzureRegionConfiguration } from \"./AzureRegionConfiguration\";\r\n\r\nexport type AuthorityOptions = {\r\n    protocolMode: ProtocolMode;\r\n    knownAuthorities: Array<string>;\r\n    cloudDiscoveryMetadata: string;\r\n    authorityMetadata: string;\r\n    skipAuthorityMetadataCache?: boolean;\r\n    azureRegionConfiguration?: AzureRegionConfiguration;\r\n};\r\n\r\nexport enum AzureCloudInstance {\r\n    // AzureCloudInstance is not specified.\r\n    None,\r\n\r\n    // Microsoft Azure public cloud\r\n    AzurePublic = \"https://login.microsoftonline.com\",\r\n\r\n    // Microsoft PPE\r\n    AzurePpe = \"https://login.windows-ppe.net\",\r\n\r\n    // Microsoft Chinese national cloud\r\n    AzureChina = \"https://login.chinacloudapi.cn\",\r\n\r\n    // Microsoft German national cloud (\"Black Forest\")\r\n    AzureGermany = \"https://login.microsoftonline.de\",\r\n\r\n    // US Government cloud\r\n    AzureUsGovernment = \"https://login.microsoftonline.us\",\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;IAcS,mBAkBX;AAlBD,CAAA,UAAY,kBAAkB,EAAA;;AAE1B,IAAA,kBAAA,CAAA,kBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;;AAGJ,IAAA,kBAAA,CAAA,aAAA,CAAA,GAAA,mCAAiD,CAAA;;AAGjD,IAAA,kBAAA,CAAA,UAAA,CAAA,GAAA,+BAA0C,CAAA;;AAG1C,IAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,gCAA6C,CAAA;;AAG7C,IAAA,kBAAA,CAAA,cAAA,CAAA,GAAA,kCAAiD,CAAA;;AAGjD,IAAA,kBAAA,CAAA,mBAAA,CAAA,GAAA,kCAAsD,CAAA;AAC1D,CAAC,EAlBW,kBAAkB,KAAlB,kBAAkB,GAkB7B,EAAA,CAAA,CAAA;;;;"}