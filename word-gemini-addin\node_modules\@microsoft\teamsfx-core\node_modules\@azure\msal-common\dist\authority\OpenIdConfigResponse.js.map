{"version": 3, "file": "OpenIdConfigResponse.js", "sources": ["../../src/authority/OpenIdConfigResponse.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\n/**\r\n * Tenant Discovery Response which contains the relevant OAuth endpoints and data needed for authentication and authorization.\r\n */\r\nexport type OpenIdConfigResponse = {\r\n    authorization_endpoint: string;\r\n    token_endpoint: string;\r\n    end_session_endpoint?: string;\r\n    issuer: string;\r\n    jwks_uri: string;\r\n};\r\n\r\nexport function isOpenIdConfigResponse(response: object): boolean {\r\n    return (\r\n        response.hasOwnProperty(\"authorization_endpoint\") &&\r\n        response.hasOwnProperty(\"token_endpoint\") && \r\n        response.hasOwnProperty(\"issuer\") &&\r\n        response.hasOwnProperty(\"jwks_uri\")\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAaG,SAAU,sBAAsB,CAAC,QAAgB,EAAA;AACnD,IAAA,QACI,QAAQ,CAAC,cAAc,CAAC,wBAAwB,CAAC;AACjD,QAAA,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACzC,QAAA,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC;AACjC,QAAA,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,EACrC;AACN;;;;"}