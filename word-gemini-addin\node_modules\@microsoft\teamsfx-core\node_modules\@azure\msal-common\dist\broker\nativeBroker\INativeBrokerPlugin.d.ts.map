{"version": 3, "file": "INativeBrokerPlugin.d.ts", "sourceRoot": "", "sources": ["../../../src/broker/nativeBroker/INativeBrokerPlugin.ts"], "names": [], "mappings": ";AAKA,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,aAAa,EAAE,MAAM,kCAAkC,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,oBAAoB,EAAE,MAAM,oCAAoC,CAAC;AAC1E,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAE3E,MAAM,WAAW,mBAAmB;IAChC,iBAAiB,EAAE,OAAO,CAAC;IAC3B,SAAS,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI,CAAC;IAC9C,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;IAC/E,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IAChF,kBAAkB,CAAC,OAAO,EAAE,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAC1E,uBAAuB,CAAC,OAAO,EAAE,aAAa,EAAE,YAAY,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACtG,OAAO,CAAC,OAAO,EAAE,oBAAoB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CACzD"}