{"version": 3, "file": "CacheManager.js", "sources": ["../../src/cache/CacheManager.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { A<PERSON>unt<PERSON><PERSON>er, CredentialFilter, ValidCredentialType, AppMetadataFilter, AppMetadataCache, TokenKeys } from \"./utils/CacheTypes\";\r\nimport { CacheRecord } from \"./entities/CacheRecord\";\r\nimport { CredentialType, APP_METADATA, THE_FAMILY_ID, AUTHORITY_METADATA_CONSTANTS, AuthenticationScheme, Separators } from \"../utils/Constants\";\r\nimport { CredentialEntity } from \"./entities/CredentialEntity\";\r\nimport { ScopeSet } from \"../request/ScopeSet\";\r\nimport { AccountEntity } from \"./entities/AccountEntity\";\r\nimport { AccessTokenEntity } from \"./entities/AccessTokenEntity\";\r\nimport { IdTokenEntity } from \"./entities/IdTokenEntity\";\r\nimport { RefreshTokenEntity } from \"./entities/RefreshTokenEntity\";\r\nimport { AuthError } from \"../error/AuthError\";\r\nimport { ICacheManager } from \"./interface/ICacheManager\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { AccountInfo } from \"../account/AccountInfo\";\r\nimport { AppMetadataEntity } from \"./entities/AppMetadataEntity\";\r\nimport { ServerTelemetryEntity } from \"./entities/ServerTelemetryEntity\";\r\nimport { ThrottlingEntity } from \"./entities/ThrottlingEntity\";\r\nimport { AuthToken } from \"../account/AuthToken\";\r\nimport { ICrypto } from \"../crypto/ICrypto\";\r\nimport { AuthorityMetadataEntity } from \"./entities/AuthorityMetadataEntity\";\r\nimport { BaseAuthRequest } from \"../request/BaseAuthRequest\";\r\nimport { Logger } from \"../logger/Logger\";\r\nimport { name, version } from \"../packageMetadata\";\r\n\r\n/**\r\n * Interface class which implement cache storage functions used by MSAL to perform validity checks, and store tokens.\r\n */\r\nexport abstract class CacheManager implements ICacheManager {\r\n    protected clientId: string;\r\n    protected cryptoImpl: ICrypto;\r\n    // Instance of logger for functions defined in the msal-common layer\r\n    private commonLogger: Logger;\r\n\r\n    constructor(clientId: string, cryptoImpl: ICrypto, logger: Logger) {\r\n        this.clientId = clientId;\r\n        this.cryptoImpl = cryptoImpl;\r\n        this.commonLogger = logger.clone(name, version);\r\n    }\r\n\r\n    /**\r\n     * fetch the account entity from the platform cache\r\n     *  @param accountKey\r\n     */\r\n    abstract getAccount(accountKey: string): AccountEntity | null;\r\n\r\n    /**\r\n     * set account entity in the platform cache\r\n     * @param account\r\n     */\r\n    abstract setAccount(account: AccountEntity): void;\r\n\r\n    /**\r\n     * fetch the idToken entity from the platform cache\r\n     * @param idTokenKey\r\n     */\r\n    abstract getIdTokenCredential(idTokenKey: string): IdTokenEntity | null;\r\n\r\n    /**\r\n     * set idToken entity to the platform cache\r\n     * @param idToken\r\n     */\r\n    abstract setIdTokenCredential(idToken: IdTokenEntity): void;\r\n\r\n    /**\r\n     * fetch the idToken entity from the platform cache\r\n     * @param accessTokenKey\r\n     */\r\n    abstract getAccessTokenCredential(accessTokenKey: string): AccessTokenEntity | null;\r\n\r\n    /**\r\n     * set idToken entity to the platform cache\r\n     * @param accessToken\r\n     */\r\n    abstract setAccessTokenCredential(accessToken: AccessTokenEntity): void;\r\n\r\n    /**\r\n     * fetch the idToken entity from the platform cache\r\n     * @param refreshTokenKey\r\n     */\r\n    abstract getRefreshTokenCredential(refreshTokenKey: string): RefreshTokenEntity | null;\r\n\r\n    /**\r\n     * set idToken entity to the platform cache\r\n     * @param refreshToken\r\n     */\r\n    abstract setRefreshTokenCredential(refreshToken: RefreshTokenEntity): void;\r\n\r\n    /**\r\n     * fetch appMetadata entity from the platform cache\r\n     * @param appMetadataKey\r\n     */\r\n    abstract getAppMetadata(appMetadataKey: string): AppMetadataEntity | null;\r\n\r\n    /**\r\n     * set appMetadata entity to the platform cache\r\n     * @param appMetadata\r\n     */\r\n    abstract setAppMetadata(appMetadata: AppMetadataEntity): void;\r\n\r\n    /**\r\n     * fetch server telemetry entity from the platform cache\r\n     * @param serverTelemetryKey\r\n     */\r\n    abstract getServerTelemetry(serverTelemetryKey: string): ServerTelemetryEntity | null;\r\n\r\n    /**\r\n     * set server telemetry entity to the platform cache\r\n     * @param serverTelemetryKey\r\n     * @param serverTelemetry\r\n     */\r\n    abstract setServerTelemetry(serverTelemetryKey: string, serverTelemetry: ServerTelemetryEntity): void;\r\n\r\n    /**\r\n     * fetch cloud discovery metadata entity from the platform cache\r\n     * @param key\r\n     */\r\n    abstract getAuthorityMetadata(key: string): AuthorityMetadataEntity | null;\r\n\r\n    /**\r\n     *\r\n     */\r\n    abstract getAuthorityMetadataKeys(): Array<string>;\r\n\r\n    /**\r\n     * set cloud discovery metadata entity to the platform cache\r\n     * @param key\r\n     * @param value\r\n     */\r\n    abstract setAuthorityMetadata(key: string, value: AuthorityMetadataEntity): void;\r\n\r\n    /**\r\n     * fetch throttling entity from the platform cache\r\n     * @param throttlingCacheKey\r\n     */\r\n    abstract getThrottlingCache(throttlingCacheKey: string): ThrottlingEntity | null;\r\n\r\n    /**\r\n     * set throttling entity to the platform cache\r\n     * @param throttlingCacheKey\r\n     * @param throttlingCache\r\n     */\r\n    abstract setThrottlingCache(throttlingCacheKey: string, throttlingCache: ThrottlingEntity): void;\r\n\r\n    /**\r\n     * Function to remove an item from cache given its key.\r\n     * @param key\r\n     */\r\n    abstract removeItem(key: string): void;\r\n\r\n    /**\r\n     * Function which returns boolean whether cache contains a specific key.\r\n     * @param key\r\n     */\r\n    abstract containsKey(key: string, type?: string): boolean;\r\n\r\n    /**\r\n     * Function which retrieves all current keys from the cache.\r\n     */\r\n    abstract getKeys(): string[];\r\n\r\n    /**\r\n     * Function which retrieves all account keys from the cache\r\n     */\r\n    abstract getAccountKeys(): string[];\r\n\r\n    /**\r\n     * Function which retrieves all token keys from the cache\r\n     */\r\n    abstract getTokenKeys(): TokenKeys;\r\n\r\n    /**\r\n     * Function which clears cache.\r\n     */\r\n    abstract clear(): Promise<void>;\r\n\r\n    /**\r\n     * Function which updates an outdated credential cache key\r\n     */\r\n    abstract updateCredentialCacheKey(currentCacheKey: string, credential: ValidCredentialType): string;\r\n\r\n    /**\r\n     * Returns all accounts in cache\r\n     */\r\n    getAllAccounts(): AccountInfo[] {\r\n        const allAccountKeys = this.getAccountKeys();\r\n        if (allAccountKeys.length < 1) {\r\n            return [];\r\n        }\r\n\r\n        const accountEntities: AccountEntity[] = allAccountKeys.reduce((accounts: AccountEntity[], key: string) => {\r\n            const entity: AccountEntity | null = this.getAccount(key);\r\n\r\n            if (!entity) {\r\n                return accounts;\r\n            }\r\n            accounts.push(entity);\r\n            return accounts;\r\n        }, []);\r\n\r\n        if (accountEntities.length < 1) {\r\n            return [];\r\n        } else {\r\n            const allAccounts = accountEntities.map<AccountInfo>((accountEntity) => {\r\n                return this.getAccountInfoFromEntity(accountEntity);\r\n            });\r\n            return allAccounts;\r\n        }\r\n    }\r\n\r\n    /** \r\n     * Gets accountInfo object based on provided filters\r\n     */\r\n    getAccountInfoFilteredBy(accountFilter: AccountFilter): AccountInfo | null{\r\n        const allAccounts = this.getAccountsFilteredBy(accountFilter);\r\n        if (allAccounts.length > 0) {\r\n            return this.getAccountInfoFromEntity(allAccounts[0]);\r\n        } else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    private getAccountInfoFromEntity(accountEntity: AccountEntity): AccountInfo {\r\n        const accountInfo = accountEntity.getAccountInfo();\r\n        const idToken = this.getIdToken(accountInfo);\r\n        if (idToken) {\r\n            accountInfo.idToken = idToken.secret;\r\n            accountInfo.idTokenClaims = new AuthToken(idToken.secret, this.cryptoImpl).claims;\r\n        }\r\n        return accountInfo;\r\n    }\r\n\r\n    /**\r\n     * saves a cache record\r\n     * @param cacheRecord\r\n     */\r\n    async saveCacheRecord(cacheRecord: CacheRecord): Promise<void> {\r\n        if (!cacheRecord) {\r\n            throw ClientAuthError.createNullOrUndefinedCacheRecord();\r\n        }\r\n\r\n        if (!!cacheRecord.account) {\r\n            this.setAccount(cacheRecord.account);\r\n        }\r\n\r\n        if (!!cacheRecord.idToken) {\r\n            this.setIdTokenCredential(cacheRecord.idToken);\r\n        }\r\n\r\n        if (!!cacheRecord.accessToken) {\r\n            await this.saveAccessToken(cacheRecord.accessToken);\r\n        }\r\n\r\n        if (!!cacheRecord.refreshToken) {\r\n            this.setRefreshTokenCredential(cacheRecord.refreshToken);\r\n        }\r\n\r\n        if (!!cacheRecord.appMetadata) {\r\n            this.setAppMetadata(cacheRecord.appMetadata);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * saves access token credential\r\n     * @param credential\r\n     */\r\n    private async saveAccessToken(credential: AccessTokenEntity): Promise<void> {\r\n        const accessTokenFilter: CredentialFilter = {\r\n            clientId: credential.clientId,\r\n            credentialType: credential.credentialType,\r\n            environment: credential.environment,\r\n            homeAccountId: credential.homeAccountId,\r\n            realm: credential.realm,\r\n            tokenType: credential.tokenType,\r\n            requestedClaimsHash: credential.requestedClaimsHash\r\n        };\r\n\r\n        const tokenKeys = this.getTokenKeys();\r\n        const currentScopes = ScopeSet.fromString(credential.target);\r\n\r\n        const removedAccessTokens: Array<Promise<void>> = [];\r\n        tokenKeys.accessToken.forEach((key) => {\r\n            if(!this.accessTokenKeyMatchesFilter(key, accessTokenFilter, false)) {\r\n                return;\r\n            }\r\n            \r\n            const tokenEntity = this.getAccessTokenCredential(key);\r\n\r\n            if (tokenEntity && this.credentialMatchesFilter(tokenEntity, accessTokenFilter)) {\r\n                const tokenScopeSet = ScopeSet.fromString(tokenEntity.target);\r\n                if (tokenScopeSet.intersectingScopeSets(currentScopes)) {\r\n                    removedAccessTokens.push(this.removeAccessToken(key));\r\n                }\r\n            }\r\n        });\r\n        await Promise.all(removedAccessTokens);\r\n        this.setAccessTokenCredential(credential);\r\n    }\r\n\r\n    /**\r\n     * retrieve accounts matching all provided filters; if no filter is set, get all accounts\r\n     * not checking for casing as keys are all generated in lower case, remember to convert to lower case if object properties are compared\r\n     * @param homeAccountId\r\n     * @param environment\r\n     * @param realm\r\n     */\r\n    getAccountsFilteredBy(accountFilter: AccountFilter): AccountEntity[] {\r\n        const allAccountKeys = this.getAccountKeys();\r\n        const matchingAccounts: AccountEntity[] = [];\r\n\r\n        allAccountKeys.forEach((cacheKey) => {\r\n            if (!this.isAccountKey(cacheKey, accountFilter.homeAccountId, accountFilter.realm)) {\r\n                // Don't parse value if the key doesn't match the account filters\r\n                return;\r\n            }\r\n\r\n            const entity: AccountEntity | null = this.getAccount(cacheKey);\r\n\r\n            if (!entity) {\r\n                return;\r\n            }\r\n\r\n            if (!!accountFilter.homeAccountId && !this.matchHomeAccountId(entity, accountFilter.homeAccountId)) {\r\n                return;\r\n            }\r\n\r\n            if (!!accountFilter.localAccountId && !this.matchLocalAccountId(entity, accountFilter.localAccountId)) {\r\n                return;\r\n            }\r\n\r\n            if (!!accountFilter.username && !this.matchUsername(entity, accountFilter.username)) {\r\n                return;\r\n            }\r\n\r\n            if (!!accountFilter.environment && !this.matchEnvironment(entity, accountFilter.environment)) {\r\n                return;\r\n            }\r\n\r\n            if (!!accountFilter.realm && !this.matchRealm(entity, accountFilter.realm)) {\r\n                return;\r\n            }\r\n\r\n            if (!!accountFilter.nativeAccountId && !this.matchNativeAccountId(entity, accountFilter.nativeAccountId)) {\r\n                return;\r\n            }\r\n\r\n            matchingAccounts.push(entity);\r\n        });\r\n\r\n        return matchingAccounts;\r\n    }\r\n\r\n    /**\r\n     * Returns true if the given key matches our account key schema. Also matches homeAccountId and/or tenantId if provided\r\n     * @param key \r\n     * @param homeAccountId \r\n     * @param tenantId \r\n     * @returns \r\n     */\r\n    isAccountKey(key: string, homeAccountId?: string, tenantId?: string): boolean {\r\n        if (key.split(Separators.CACHE_KEY_SEPARATOR).length < 3) {\r\n            // Account cache keys contain 3 items separated by '-' (each item may also contain '-')\r\n            return false;\r\n        }\r\n\r\n        if (homeAccountId && !key.toLowerCase().includes(homeAccountId.toLowerCase())) {\r\n            return false;\r\n        }\r\n\r\n        if (tenantId && !key.toLowerCase().includes(tenantId.toLowerCase())) {\r\n            return false;\r\n        }\r\n\r\n        // Do not check environment as aliasing can cause false negatives\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Returns true if the given key matches our credential key schema.\r\n     * @param key \r\n     */\r\n    isCredentialKey(key: string): boolean {\r\n        if (key.split(Separators.CACHE_KEY_SEPARATOR).length < 6) {\r\n            // Credential cache keys contain 6 items separated by '-' (each item may also contain '-')\r\n            return false;\r\n        }\r\n\r\n        const lowerCaseKey = key.toLowerCase();\r\n        // Credential keys must indicate what credential type they represent\r\n        if (lowerCaseKey.indexOf(CredentialType.ID_TOKEN.toLowerCase()) === -1 &&\r\n            lowerCaseKey.indexOf(CredentialType.ACCESS_TOKEN.toLowerCase()) === -1 &&\r\n            lowerCaseKey.indexOf(CredentialType.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase()) === -1 &&\r\n            lowerCaseKey.indexOf(CredentialType.REFRESH_TOKEN.toLowerCase()) === -1\r\n        ) {\r\n            return false;\r\n        }\r\n\r\n        if (lowerCaseKey.indexOf(CredentialType.REFRESH_TOKEN.toLowerCase()) > -1) {\r\n            // Refresh tokens must contain the client id or family id\r\n            const clientIdValidation = `${CredentialType.REFRESH_TOKEN}${Separators.CACHE_KEY_SEPARATOR}${this.clientId}${Separators.CACHE_KEY_SEPARATOR}`;\r\n            const familyIdValidation = `${CredentialType.REFRESH_TOKEN}${Separators.CACHE_KEY_SEPARATOR}${THE_FAMILY_ID}${Separators.CACHE_KEY_SEPARATOR}`;\r\n            if (lowerCaseKey.indexOf(clientIdValidation.toLowerCase()) === -1 && lowerCaseKey.indexOf(familyIdValidation.toLowerCase()) === -1) {\r\n                return false;\r\n            }\r\n        } else if (lowerCaseKey.indexOf(this.clientId.toLowerCase()) === -1) {\r\n            // Tokens must contain the clientId\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Returns whether or not the given credential entity matches the filter\r\n     * @param entity \r\n     * @param filter \r\n     * @returns \r\n     */\r\n    credentialMatchesFilter(entity: ValidCredentialType, filter: CredentialFilter): boolean {\r\n        if (!!filter.clientId && !this.matchClientId(entity, filter.clientId)) {\r\n            return false;\r\n        }\r\n\r\n        if (!!filter.userAssertionHash && !this.matchUserAssertionHash(entity, filter.userAssertionHash)) {\r\n            return false;\r\n        }\r\n\r\n        /*\r\n         * homeAccountId can be undefined, and we want to filter out cached items that have a homeAccountId of \"\"\r\n         * because we don't want a client_credential request to return a cached token that has a homeAccountId\r\n         */\r\n        if ((typeof filter.homeAccountId === \"string\") && !this.matchHomeAccountId(entity, filter.homeAccountId)) {\r\n            return false;\r\n        }\r\n\r\n        if (!!filter.environment && !this.matchEnvironment(entity, filter.environment)) {\r\n            return false;\r\n        }\r\n\r\n        if (!!filter.realm && !this.matchRealm(entity, filter.realm)) {\r\n            return false;\r\n        }\r\n\r\n        if (!!filter.credentialType && !this.matchCredentialType(entity, filter.credentialType)) {\r\n            return false;\r\n        }\r\n\r\n        if (!!filter.familyId && !this.matchFamilyId(entity, filter.familyId)) {\r\n            return false;\r\n        }\r\n\r\n        /*\r\n         * idTokens do not have \"target\", target specific refreshTokens do exist for some types of authentication\r\n         * Resource specific refresh tokens case will be added when the support is deemed necessary\r\n         */\r\n        if (!!filter.target && !this.matchTarget(entity, filter.target)) {\r\n            return false;\r\n        }\r\n\r\n        // If request OR cached entity has requested Claims Hash, check if they match\r\n        if (filter.requestedClaimsHash || entity.requestedClaimsHash) {\r\n            // Don't match if either is undefined or they are different\r\n            if (entity.requestedClaimsHash !== filter.requestedClaimsHash) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        // Access Token with Auth Scheme specific matching\r\n        if (entity.credentialType === CredentialType.ACCESS_TOKEN_WITH_AUTH_SCHEME) {\r\n            if(!!filter.tokenType && !this.matchTokenType(entity, filter.tokenType)) {\r\n                return false;\r\n            }\r\n\r\n            // KeyId (sshKid) in request must match cached SSH certificate keyId because SSH cert is bound to a specific key\r\n            if (filter.tokenType === AuthenticationScheme.SSH) {\r\n                if(filter.keyId && !this.matchKeyId(entity, filter.keyId)) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * retrieve appMetadata matching all provided filters; if no filter is set, get all appMetadata\r\n     * @param filter\r\n     */\r\n    getAppMetadataFilteredBy(filter: AppMetadataFilter): AppMetadataCache {\r\n        return this.getAppMetadataFilteredByInternal(\r\n            filter.environment,\r\n            filter.clientId,\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Support function to help match appMetadata\r\n     * @param environment\r\n     * @param clientId\r\n     */\r\n    private getAppMetadataFilteredByInternal(\r\n        environment?: string,\r\n        clientId?: string\r\n    ): AppMetadataCache {\r\n\r\n        const allCacheKeys = this.getKeys();\r\n        const matchingAppMetadata: AppMetadataCache = {};\r\n\r\n        allCacheKeys.forEach((cacheKey) => {\r\n            // don't parse any non-appMetadata type cache entities\r\n            if (!this.isAppMetadata(cacheKey)) {\r\n                return;\r\n            }\r\n\r\n            // Attempt retrieval\r\n            const entity = this.getAppMetadata(cacheKey);\r\n\r\n            if (!entity) {\r\n                return;\r\n            }\r\n\r\n            if (!!environment && !this.matchEnvironment(entity, environment)) {\r\n                return;\r\n            }\r\n\r\n            if (!!clientId && !this.matchClientId(entity, clientId)) {\r\n                return;\r\n            }\r\n\r\n            matchingAppMetadata[cacheKey] = entity;\r\n\r\n        });\r\n\r\n        return matchingAppMetadata;\r\n    }\r\n\r\n    /**\r\n     * retrieve authorityMetadata that contains a matching alias\r\n     * @param filter\r\n     */\r\n    getAuthorityMetadataByAlias(host: string): AuthorityMetadataEntity | null {\r\n        const allCacheKeys = this.getAuthorityMetadataKeys();\r\n        let matchedEntity = null;\r\n\r\n        allCacheKeys.forEach((cacheKey) => {\r\n            // don't parse any non-authorityMetadata type cache entities\r\n            if (!this.isAuthorityMetadata(cacheKey) || cacheKey.indexOf(this.clientId) === -1) {\r\n                return;\r\n            }\r\n\r\n            // Attempt retrieval\r\n            const entity = this.getAuthorityMetadata(cacheKey);\r\n\r\n            if (!entity) {\r\n                return;\r\n            }\r\n\r\n            if (entity.aliases.indexOf(host) === -1) {\r\n                return;\r\n            }\r\n\r\n            matchedEntity = entity;\r\n\r\n        });\r\n\r\n        return matchedEntity;\r\n    }\r\n\r\n    /**\r\n     * Removes all accounts and related tokens from cache.\r\n     */\r\n    async removeAllAccounts(): Promise<void> {\r\n        const allAccountKeys = this.getAccountKeys();\r\n        const removedAccounts: Array<Promise<void>> = [];\r\n\r\n        allAccountKeys.forEach((cacheKey) => {\r\n            removedAccounts.push(this.removeAccount(cacheKey));\r\n        });\r\n\r\n        await Promise.all(removedAccounts);\r\n    }\r\n\r\n    /**\r\n     * Removes the account and related tokens for a given account key\r\n     * @param account\r\n     */\r\n    async removeAccount(accountKey: string): Promise<void> {\r\n        const account = this.getAccount(accountKey);\r\n        if (!account) {\r\n            throw ClientAuthError.createNoAccountFoundError();\r\n        }\r\n        await this.removeAccountContext(account);\r\n        this.removeItem(accountKey);\r\n    }\r\n\r\n    /**\r\n     * Removes credentials associated with the provided account\r\n     * @param account\r\n     */\r\n    async removeAccountContext(account: AccountEntity): Promise<void> {\r\n        const allTokenKeys = this.getTokenKeys();\r\n        const accountId = account.generateAccountId();\r\n        const removedCredentials: Array<Promise<void>> = [];\r\n\r\n        allTokenKeys.idToken.forEach((key) => {\r\n            if (key.indexOf(accountId) === 0) {\r\n                this.removeIdToken(key);\r\n            }\r\n        });\r\n\r\n        allTokenKeys.accessToken.forEach((key) => {\r\n            if (key.indexOf(accountId) === 0) {\r\n                removedCredentials.push(this.removeAccessToken(key));\r\n            }\r\n        });\r\n\r\n        allTokenKeys.refreshToken.forEach((key) => {\r\n            if (key.indexOf(accountId) === 0) {\r\n                this.removeRefreshToken(key);\r\n            }\r\n        });\r\n\r\n        await Promise.all(removedCredentials);\r\n    }\r\n\r\n    /**\r\n     * returns a boolean if the given credential is removed\r\n     * @param credential\r\n     */\r\n    async removeAccessToken(key: string): Promise<void> {\r\n        const credential = this.getAccessTokenCredential(key);\r\n        if (!credential) {\r\n            return;\r\n        }\r\n\r\n        // Remove Token Binding Key from key store for PoP Tokens Credentials\r\n        if (credential.credentialType.toLowerCase() === CredentialType.ACCESS_TOKEN_WITH_AUTH_SCHEME.toLowerCase()) {\r\n            if(credential.tokenType === AuthenticationScheme.POP) {\r\n                const accessTokenWithAuthSchemeEntity = credential as AccessTokenEntity;\r\n                const kid = accessTokenWithAuthSchemeEntity.keyId;\r\n\r\n                if (kid) {\r\n                    try {\r\n                        await this.cryptoImpl.removeTokenBindingKey(kid);\r\n                    } catch (error) {\r\n                        throw ClientAuthError.createBindingKeyNotRemovedError();\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return this.removeItem(key);\r\n    }\r\n\r\n    /**\r\n     * Removes all app metadata objects from cache.\r\n     */\r\n    removeAppMetadata(): boolean {\r\n        const allCacheKeys = this.getKeys();\r\n        allCacheKeys.forEach((cacheKey) => {\r\n            if (this.isAppMetadata(cacheKey)) {\r\n                this.removeItem(cacheKey);\r\n            }\r\n        });\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Retrieve the cached credentials into a cacherecord\r\n     * @param account\r\n     * @param clientId\r\n     * @param scopes\r\n     * @param environment\r\n     * @param authScheme\r\n     */\r\n    readCacheRecord(account: AccountInfo, request: BaseAuthRequest, environment: string): CacheRecord {\r\n        const tokenKeys = this.getTokenKeys();\r\n        const cachedAccount = this.readAccountFromCache(account);\r\n        const cachedIdToken = this.getIdToken(account, tokenKeys);\r\n        const cachedAccessToken = this.getAccessToken(account, request, tokenKeys);\r\n        const cachedRefreshToken = this.getRefreshToken(account, false, tokenKeys);\r\n        const cachedAppMetadata = this.readAppMetadataFromCache(environment);\r\n\r\n        if (cachedAccount && cachedIdToken) {\r\n            cachedAccount.idTokenClaims = new AuthToken(cachedIdToken.secret, this.cryptoImpl).claims;\r\n        }\r\n\r\n        return {\r\n            account: cachedAccount,\r\n            idToken: cachedIdToken,\r\n            accessToken: cachedAccessToken,\r\n            refreshToken: cachedRefreshToken,\r\n            appMetadata: cachedAppMetadata,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Retrieve AccountEntity from cache\r\n     * @param account\r\n     */\r\n    readAccountFromCache(account: AccountInfo): AccountEntity | null {\r\n        const accountKey: string = AccountEntity.generateAccountCacheKey(account);\r\n        return this.getAccount(accountKey);\r\n    }\r\n\r\n    /**\r\n     * Retrieve IdTokenEntity from cache\r\n     * @param clientId\r\n     * @param account\r\n     * @param inputRealm\r\n     */\r\n    getIdToken(account: AccountInfo, tokenKeys?: TokenKeys): IdTokenEntity | null {\r\n        this.commonLogger.trace(\"CacheManager - getIdToken called\");\r\n        const idTokenFilter: CredentialFilter = {\r\n            homeAccountId: account.homeAccountId,\r\n            environment: account.environment,\r\n            credentialType: CredentialType.ID_TOKEN,\r\n            clientId: this.clientId,\r\n            realm: account.tenantId,\r\n        };\r\n\r\n        const idTokens: IdTokenEntity[] = this.getIdTokensByFilter(idTokenFilter, tokenKeys);\r\n        const numIdTokens = idTokens.length;\r\n\r\n        if (numIdTokens < 1) {\r\n            this.commonLogger.info(\"CacheManager:getIdToken - No token found\");\r\n            return null;\r\n        } else if (numIdTokens > 1) {\r\n            this.commonLogger.info(\r\n                \"CacheManager:getIdToken - Multiple id tokens found, clearing them\"\r\n            );\r\n            idTokens.forEach((idToken) => {\r\n                this.removeIdToken(idToken.generateCredentialKey());\r\n            });\r\n            return null;\r\n        }\r\n\r\n        this.commonLogger.info(\"CacheManager:getIdToken - Returning id token\");\r\n        return idTokens[0];\r\n    }\r\n\r\n    /**\r\n     * Gets all idTokens matching the given filter\r\n     * @param filter \r\n     * @returns \r\n     */\r\n    getIdTokensByFilter(filter: CredentialFilter, tokenKeys?: TokenKeys): IdTokenEntity[] {\r\n        const idTokenKeys = tokenKeys && tokenKeys.idToken || this.getTokenKeys().idToken;\r\n\r\n        const idTokens: IdTokenEntity[] = [];\r\n        idTokenKeys.forEach((key) => {\r\n            if (!this.idTokenKeyMatchesFilter(key, {clientId: this.clientId, ...filter})) {\r\n                return;\r\n            }\r\n\r\n            const idToken = this.getIdTokenCredential(key);\r\n            if (idToken && this.credentialMatchesFilter(idToken, filter)) {\r\n                idTokens.push(idToken);\r\n            }\r\n        });\r\n\r\n        return idTokens;\r\n    }\r\n\r\n    /**\r\n     * Validate the cache key against filter before retrieving and parsing cache value\r\n     * @param key \r\n     * @param filter\r\n     * @returns \r\n     */\r\n    idTokenKeyMatchesFilter(inputKey: string, filter: CredentialFilter): boolean {\r\n        const key = inputKey.toLowerCase();\r\n        if (filter.clientId && key.indexOf(filter.clientId.toLowerCase()) === -1) {\r\n            return false;\r\n        }\r\n\r\n        if (filter.homeAccountId && key.indexOf(filter.homeAccountId.toLowerCase()) === -1) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Removes idToken from the cache\r\n     * @param key \r\n     */\r\n    removeIdToken(key: string): void {\r\n        this.removeItem(key);\r\n    }\r\n\r\n    /**\r\n     * Removes refresh token from the cache\r\n     * @param key \r\n     */\r\n    removeRefreshToken(key: string): void {\r\n        this.removeItem(key);\r\n    }\r\n\r\n    /**\r\n     * Retrieve AccessTokenEntity from cache\r\n     * @param clientId\r\n     * @param account\r\n     * @param scopes\r\n     * @param authScheme\r\n     */\r\n    getAccessToken(account: AccountInfo, request: BaseAuthRequest, tokenKeys?: TokenKeys): AccessTokenEntity | null {\r\n        this.commonLogger.trace(\"CacheManager - getAccessToken called\");\r\n        const scopes =  ScopeSet.createSearchScopes(request.scopes);\r\n        const authScheme = request.authenticationScheme || AuthenticationScheme.BEARER;\r\n        /*\r\n         * Distinguish between Bearer and PoP/SSH token cache types\r\n         * Cast to lowercase to handle \"bearer\" from ADFS\r\n         */\r\n        const credentialType = (authScheme && authScheme.toLowerCase() !== AuthenticationScheme.BEARER.toLowerCase()) ? CredentialType.ACCESS_TOKEN_WITH_AUTH_SCHEME : CredentialType.ACCESS_TOKEN;\r\n\r\n        const accessTokenFilter: CredentialFilter = {\r\n            homeAccountId: account.homeAccountId,\r\n            environment: account.environment,\r\n            credentialType: credentialType,\r\n            clientId: this.clientId,\r\n            realm: account.tenantId,\r\n            target: scopes,\r\n            tokenType: authScheme,\r\n            keyId: request.sshKid,\r\n            requestedClaimsHash: request.requestedClaimsHash,\r\n        };\r\n\r\n        const accessTokenKeys = tokenKeys && tokenKeys.accessToken || this.getTokenKeys().accessToken;\r\n        const accessTokens: AccessTokenEntity[] = [];\r\n\r\n        accessTokenKeys.forEach((key) => {\r\n            // Validate key\r\n            if (this.accessTokenKeyMatchesFilter(key, accessTokenFilter, true)) {\r\n                const accessToken = this.getAccessTokenCredential(key);\r\n\r\n                // Validate value\r\n                if (accessToken && this.credentialMatchesFilter(accessToken, accessTokenFilter)) {\r\n                    accessTokens.push(accessToken);\r\n                }\r\n            }\r\n        });\r\n\r\n        const numAccessTokens = accessTokens.length;\r\n        if (numAccessTokens < 1) {\r\n            this.commonLogger.info(\"CacheManager:getAccessToken - No token found\");\r\n            return null;\r\n        } else if (numAccessTokens > 1) {\r\n            this.commonLogger.info(\r\n                \"CacheManager:getAccessToken - Multiple access tokens found, clearing them\"\r\n            );\r\n            accessTokens.forEach((accessToken) => {\r\n                this.removeAccessToken(accessToken.generateCredentialKey());\r\n            });\r\n            return null;\r\n        }\r\n\r\n        this.commonLogger.info(\"CacheManager:getAccessToken - Returning access token\");\r\n        return accessTokens[0];\r\n    }\r\n\r\n    /**\r\n     * Validate the cache key against filter before retrieving and parsing cache value\r\n     * @param key \r\n     * @param filter \r\n     * @param keyMustContainAllScopes \r\n     * @returns \r\n     */\r\n    accessTokenKeyMatchesFilter(inputKey: string, filter: CredentialFilter, keyMustContainAllScopes: boolean): boolean {\r\n        const key = inputKey.toLowerCase();\r\n        if (filter.clientId && key.indexOf(filter.clientId.toLowerCase()) === -1) {\r\n            return false;\r\n        }\r\n\r\n        if (filter.homeAccountId && key.indexOf(filter.homeAccountId.toLowerCase()) === -1) {\r\n            return false;\r\n        }\r\n\r\n        if (filter.realm && key.indexOf(filter.realm.toLowerCase()) === -1) {\r\n            return false;\r\n        }\r\n\r\n        if (filter.requestedClaimsHash && key.indexOf(filter.requestedClaimsHash.toLowerCase()) === -1) {\r\n            return false;\r\n        }\r\n\r\n        if (filter.target) {\r\n            const scopes = filter.target.asArray();\r\n            for (let i = 0; i < scopes.length; i++) {\r\n                if (keyMustContainAllScopes && !key.includes(scopes[i].toLowerCase())) {\r\n                    // When performing a cache lookup a missing scope would be a cache miss\r\n                    return false;\r\n                } else if (!keyMustContainAllScopes && key.includes(scopes[i].toLowerCase())) {\r\n                    // When performing a cache write, any token with a subset of requested scopes should be replaced\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Gets all access tokens matching the filter\r\n     * @param filter \r\n     * @returns \r\n     */\r\n    getAccessTokensByFilter(filter: CredentialFilter): AccessTokenEntity[] {\r\n        const tokenKeys = this.getTokenKeys();\r\n\r\n        const accessTokens: AccessTokenEntity[] = [];\r\n        tokenKeys.accessToken.forEach((key) => {\r\n            if (!this.accessTokenKeyMatchesFilter(key, filter, true)) {\r\n                return;\r\n            }\r\n\r\n            const accessToken = this.getAccessTokenCredential(key);\r\n            if (accessToken && this.credentialMatchesFilter(accessToken, filter)) {\r\n                accessTokens.push(accessToken);\r\n            }\r\n        });\r\n\r\n        return accessTokens;\r\n    }\r\n\r\n    /**\r\n     * Helper to retrieve the appropriate refresh token from cache\r\n     * @param clientId\r\n     * @param account\r\n     * @param familyRT\r\n     */\r\n    getRefreshToken(account: AccountInfo, familyRT: boolean, tokenKeys?: TokenKeys): RefreshTokenEntity | null {\r\n        this.commonLogger.trace(\"CacheManager - getRefreshToken called\");\r\n        const id = familyRT ? THE_FAMILY_ID : undefined;\r\n        const refreshTokenFilter: CredentialFilter = {\r\n            homeAccountId: account.homeAccountId,\r\n            environment: account.environment,\r\n            credentialType: CredentialType.REFRESH_TOKEN,\r\n            clientId: this.clientId,\r\n            familyId: id,\r\n        };\r\n\r\n        const refreshTokenKeys = tokenKeys && tokenKeys.refreshToken || this.getTokenKeys().refreshToken;\r\n        const refreshTokens: RefreshTokenEntity[] = [];\r\n\r\n        refreshTokenKeys.forEach((key) => {\r\n            // Validate key\r\n            if (this.refreshTokenKeyMatchesFilter(key, refreshTokenFilter)) {\r\n                const refreshToken = this.getRefreshTokenCredential(key);\r\n                // Validate value\r\n                if (refreshToken && this.credentialMatchesFilter(refreshToken, refreshTokenFilter)) {\r\n                    refreshTokens.push(refreshToken);\r\n                }\r\n            }\r\n        });\r\n\r\n        const numRefreshTokens = refreshTokens.length;\r\n        if (numRefreshTokens < 1) {\r\n            this.commonLogger.info(\"CacheManager:getRefreshToken - No refresh token found.\");\r\n            return null;\r\n        }\r\n        // address the else case after remove functions address environment aliases\r\n\r\n        this.commonLogger.info(\"CacheManager:getRefreshToken - returning refresh token\");\r\n        return refreshTokens[0] as RefreshTokenEntity;\r\n    }\r\n\r\n    /**\r\n     * Validate the cache key against filter before retrieving and parsing cache value\r\n     * @param key\r\n     * @param filter\r\n     */\r\n    refreshTokenKeyMatchesFilter(inputKey: string, filter: CredentialFilter): boolean {\r\n        const key = inputKey.toLowerCase();\r\n        if (filter.familyId && key.indexOf(filter.familyId.toLowerCase()) === -1) {\r\n            return false;\r\n        }\r\n\r\n        // If familyId is used, clientId is not in the key\r\n        if (!filter.familyId && filter.clientId && key.indexOf(filter.clientId.toLowerCase()) === -1) {\r\n            return false;\r\n        }\r\n\r\n        if (filter.homeAccountId && key.indexOf(filter.homeAccountId.toLowerCase()) === -1) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Retrieve AppMetadataEntity from cache\r\n     */\r\n    readAppMetadataFromCache(environment: string): AppMetadataEntity | null {\r\n        const appMetadataFilter: AppMetadataFilter = {\r\n            environment,\r\n            clientId: this.clientId,\r\n        };\r\n\r\n        const appMetadata: AppMetadataCache = this.getAppMetadataFilteredBy(appMetadataFilter);\r\n        const appMetadataEntries: AppMetadataEntity[] = Object.keys(appMetadata).map((key) => appMetadata[key]);\r\n\r\n        const numAppMetadata = appMetadataEntries.length;\r\n        if (numAppMetadata < 1) {\r\n            return null;\r\n        } else if (numAppMetadata > 1) {\r\n            throw ClientAuthError.createMultipleMatchingAppMetadataInCacheError();\r\n        }\r\n\r\n        return appMetadataEntries[0] as AppMetadataEntity;\r\n    }\r\n\r\n    /**\r\n     * Return the family_id value associated  with FOCI\r\n     * @param environment\r\n     * @param clientId\r\n     */\r\n    isAppMetadataFOCI(environment: string): boolean {\r\n        const appMetadata = this.readAppMetadataFromCache(environment);\r\n        return !!(appMetadata && appMetadata.familyId === THE_FAMILY_ID);\r\n    }\r\n\r\n    /**\r\n     * helper to match account ids\r\n     * @param value\r\n     * @param homeAccountId\r\n     */\r\n    private matchHomeAccountId(entity: AccountEntity | CredentialEntity, homeAccountId: string): boolean {\r\n        return !!((typeof entity.homeAccountId === \"string\") && (homeAccountId === entity.homeAccountId));\r\n    }\r\n\r\n    /**\r\n     * helper to match account ids\r\n     * @param entity \r\n     * @param localAccountId \r\n     * @returns \r\n     */\r\n    private matchLocalAccountId(entity: AccountEntity, localAccountId: string): boolean {\r\n        return !!((typeof entity.localAccountId === \"string\") && (localAccountId === entity.localAccountId));\r\n    }\r\n\r\n    /**\r\n     * helper to match usernames\r\n     * @param entity \r\n     * @param username \r\n     * @returns \r\n     */\r\n    private matchUsername(entity: AccountEntity, username: string): boolean {\r\n        return !!((typeof entity.username === \"string\") && (username.toLowerCase() === entity.username.toLowerCase()));\r\n    }\r\n\r\n    /**\r\n     * helper to match assertion\r\n     * @param value\r\n     * @param oboAssertion\r\n     */\r\n    private matchUserAssertionHash(entity: CredentialEntity, userAssertionHash: string): boolean {\r\n        return !!(entity.userAssertionHash && userAssertionHash === entity.userAssertionHash);\r\n    }\r\n\r\n    /**\r\n     * helper to match environment\r\n     * @param value\r\n     * @param environment\r\n     */\r\n    private matchEnvironment(entity: AccountEntity | CredentialEntity | AppMetadataEntity, environment: string): boolean {\r\n        const cloudMetadata = this.getAuthorityMetadataByAlias(environment);\r\n        if (cloudMetadata && cloudMetadata.aliases.indexOf(entity.environment) > -1) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * helper to match credential type\r\n     * @param entity\r\n     * @param credentialType\r\n     */\r\n    private matchCredentialType(entity: CredentialEntity, credentialType: string): boolean {\r\n        return (entity.credentialType && credentialType.toLowerCase() === entity.credentialType.toLowerCase());\r\n    }\r\n\r\n    /**\r\n     * helper to match client ids\r\n     * @param entity\r\n     * @param clientId\r\n     */\r\n    private matchClientId(entity: CredentialEntity | AppMetadataEntity, clientId: string): boolean {\r\n        return !!(entity.clientId && clientId === entity.clientId);\r\n    }\r\n\r\n    /**\r\n     * helper to match family ids\r\n     * @param entity\r\n     * @param familyId\r\n     */\r\n    private matchFamilyId(entity: CredentialEntity | AppMetadataEntity, familyId: string): boolean {\r\n        return !!(entity.familyId && familyId === entity.familyId);\r\n    }\r\n\r\n    /**\r\n     * helper to match realm\r\n     * @param entity\r\n     * @param realm\r\n     */\r\n    private matchRealm(entity: AccountEntity | CredentialEntity, realm: string): boolean {\r\n        return !!(entity.realm && realm === entity.realm);\r\n    }\r\n\r\n    /**\r\n     * helper to match nativeAccountId\r\n     * @param entity\r\n     * @param nativeAccountId\r\n     * @returns boolean indicating the match result\r\n     */\r\n    private matchNativeAccountId(entity: AccountEntity, nativeAccountId: string): boolean {\r\n        return !!(entity.nativeAccountId && nativeAccountId === entity.nativeAccountId);\r\n    }\r\n\r\n    /**\r\n     * Returns true if the target scopes are a subset of the current entity's scopes, false otherwise.\r\n     * @param entity\r\n     * @param target\r\n     */\r\n    private matchTarget(entity: CredentialEntity, target: ScopeSet): boolean {\r\n        const isNotAccessTokenCredential = (entity.credentialType !== CredentialType.ACCESS_TOKEN && entity.credentialType !== CredentialType.ACCESS_TOKEN_WITH_AUTH_SCHEME);\r\n\r\n        if ( isNotAccessTokenCredential || !entity.target) {\r\n            return false;\r\n        }\r\n\r\n        const entityScopeSet: ScopeSet = ScopeSet.fromString(entity.target);\r\n\r\n        return entityScopeSet.containsScopeSet(target);\r\n    }\r\n\r\n    /**\r\n     * Returns true if the credential's tokenType or Authentication Scheme matches the one in the request, false otherwise\r\n     * @param entity\r\n     * @param tokenType\r\n     */\r\n    private matchTokenType(entity: CredentialEntity, tokenType: AuthenticationScheme): boolean {\r\n        return !!(entity.tokenType && entity.tokenType === tokenType);\r\n    }\r\n\r\n    /**\r\n     * Returns true if the credential's keyId matches the one in the request, false otherwise\r\n     * @param entity\r\n     * @param tokenType\r\n     */\r\n    private matchKeyId(entity: CredentialEntity, keyId: string): boolean {\r\n        return !!(entity.keyId && entity.keyId === keyId);\r\n    }\r\n\r\n    /**\r\n     * returns if a given cache entity is of the type appmetadata\r\n     * @param key\r\n     */\r\n    private isAppMetadata(key: string): boolean {\r\n        return key.indexOf(APP_METADATA) !== -1;\r\n    }\r\n\r\n    /**\r\n     * returns if a given cache entity is of the type authoritymetadata\r\n     * @param key\r\n     */\r\n    protected isAuthorityMetadata(key: string): boolean {\r\n        return key.indexOf(AUTHORITY_METADATA_CONSTANTS.CACHE_KEY) !== -1;\r\n    }\r\n\r\n    /**\r\n     * returns cache key used for cloud instance metadata\r\n     */\r\n    generateAuthorityMetadataCacheKey(authority: string): string {\r\n        return `${AUTHORITY_METADATA_CONSTANTS.CACHE_KEY}-${this.clientId}-${authority}`;\r\n    }\r\n\r\n    /**\r\n     * Helper to convert serialized data to object\r\n     * @param obj\r\n     * @param json\r\n     */\r\n    static toObject<T>(obj: T, json: object): T {\r\n        for (const propertyName in json) {\r\n            obj[propertyName] = json[propertyName];\r\n        }\r\n        return obj;\r\n    }\r\n}\r\n\r\nexport class DefaultStorageClass extends CacheManager {\r\n    setAccount(): void {\r\n        const notImplErr = \"Storage interface - setAccount() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getAccount(): AccountEntity {\r\n        const notImplErr = \"Storage interface - getAccount() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    setIdTokenCredential(): void {\r\n        const notImplErr = \"Storage interface - setIdTokenCredential() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getIdTokenCredential(): IdTokenEntity {\r\n        const notImplErr = \"Storage interface - getIdTokenCredential() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    setAccessTokenCredential(): void {\r\n        const notImplErr = \"Storage interface - setAccessTokenCredential() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getAccessTokenCredential(): AccessTokenEntity {\r\n        const notImplErr = \"Storage interface - getAccessTokenCredential() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    setRefreshTokenCredential(): void {\r\n        const notImplErr = \"Storage interface - setRefreshTokenCredential() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getRefreshTokenCredential(): RefreshTokenEntity {\r\n        const notImplErr = \"Storage interface - getRefreshTokenCredential() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    setAppMetadata(): void {\r\n        const notImplErr = \"Storage interface - setAppMetadata() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getAppMetadata(): AppMetadataEntity {\r\n        const notImplErr = \"Storage interface - getAppMetadata() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    setServerTelemetry(): void {\r\n        const notImplErr = \"Storage interface - setServerTelemetry() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getServerTelemetry(): ServerTelemetryEntity {\r\n        const notImplErr = \"Storage interface - getServerTelemetry() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    setAuthorityMetadata(): void {\r\n        const notImplErr = \"Storage interface - setAuthorityMetadata() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getAuthorityMetadata(): AuthorityMetadataEntity | null {\r\n        const notImplErr = \"Storage interface - getAuthorityMetadata() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getAuthorityMetadataKeys(): Array<string> {\r\n        const notImplErr = \"Storage interface - getAuthorityMetadataKeys() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    setThrottlingCache(): void {\r\n        const notImplErr = \"Storage interface - setThrottlingCache() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getThrottlingCache(): ThrottlingEntity {\r\n        const notImplErr = \"Storage interface - getThrottlingCache() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    removeItem(): boolean {\r\n        const notImplErr = \"Storage interface - removeItem() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    containsKey(): boolean {\r\n        const notImplErr = \"Storage interface - containsKey() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getKeys(): string[] {\r\n        const notImplErr = \"Storage interface - getKeys() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getAccountKeys(): string[] {\r\n        const notImplErr = \"Storage interface - getAccountKeys() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    getTokenKeys(): TokenKeys {\r\n        const notImplErr = \"Storage interface - getTokenKeys() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    async clear(): Promise<void> {\r\n        const notImplErr = \"Storage interface - clear() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n    updateCredentialCacheKey(): string {\r\n        const notImplErr = \"Storage interface - updateCredentialCacheKey() has not been implemented for the cacheStorage interface.\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;AAGG;AAyBH;;AAEG;AACH,IAAA,YAAA,kBAAA,YAAA;AAMI,IAAA,SAAA,YAAA,CAAY,QAAgB,EAAE,UAAmB,EAAE,MAAc,EAAA;AAC7D,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KACnD;AA+ID;;AAEG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QAAA,IAwBC,KAAA,GAAA,IAAA,CAAA;AAvBG,QAAA,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AAC7C,QAAA,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3B,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;QAED,IAAM,eAAe,GAAoB,cAAc,CAAC,MAAM,CAAC,UAAC,QAAyB,EAAE,GAAW,EAAA;YAClG,IAAM,MAAM,GAAyB,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAE1D,IAAI,CAAC,MAAM,EAAE;AACT,gBAAA,OAAO,QAAQ,CAAC;AACnB,aAAA;AACD,YAAA,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtB,YAAA,OAAO,QAAQ,CAAC;SACnB,EAAE,EAAE,CAAC,CAAC;AAEP,QAAA,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;AAAM,aAAA;AACH,YAAA,IAAM,WAAW,GAAG,eAAe,CAAC,GAAG,CAAc,UAAC,aAAa,EAAA;AAC/D,gBAAA,OAAO,KAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;AACxD,aAAC,CAAC,CAAC;AACH,YAAA,OAAO,WAAW,CAAC;AACtB,SAAA;KACJ,CAAA;AAED;;AAEG;IACH,YAAwB,CAAA,SAAA,CAAA,wBAAA,GAAxB,UAAyB,aAA4B,EAAA;QACjD,IAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAC9D,QAAA,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,SAAA;AAAM,aAAA;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;KACJ,CAAA;IAEO,YAAwB,CAAA,SAAA,CAAA,wBAAA,GAAhC,UAAiC,aAA4B,EAAA;AACzD,QAAA,IAAM,WAAW,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;QACnD,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AAC7C,QAAA,IAAI,OAAO,EAAE;AACT,YAAA,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;AACrC,YAAA,WAAW,CAAC,aAAa,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;AACrF,SAAA;AACD,QAAA,OAAO,WAAW,CAAC;KACtB,CAAA;AAED;;;AAGG;IACG,YAAe,CAAA,SAAA,CAAA,eAAA,GAArB,UAAsB,WAAwB,EAAA;;;;;wBAC1C,IAAI,CAAC,WAAW,EAAE;AACd,4BAAA,MAAM,eAAe,CAAC,gCAAgC,EAAE,CAAC;AAC5D,yBAAA;AAED,wBAAA,IAAI,CAAC,CAAC,WAAW,CAAC,OAAO,EAAE;AACvB,4BAAA,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACxC,yBAAA;AAED,wBAAA,IAAI,CAAC,CAAC,WAAW,CAAC,OAAO,EAAE;AACvB,4BAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAClD,yBAAA;AAEG,wBAAA,IAAA,CAAA,CAAC,CAAC,WAAW,CAAC,WAAW,EAAzB,OAAyB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBACzB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA,CAAA;;AAAnD,wBAAA,EAAA,CAAA,IAAA,EAAmD,CAAC;;;AAGxD,wBAAA,IAAI,CAAC,CAAC,WAAW,CAAC,YAAY,EAAE;AAC5B,4BAAA,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AAC5D,yBAAA;AAED,wBAAA,IAAI,CAAC,CAAC,WAAW,CAAC,WAAW,EAAE;AAC3B,4BAAA,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAChD,yBAAA;;;;;AACJ,KAAA,CAAA;AAED;;;AAGG;IACW,YAAe,CAAA,SAAA,CAAA,eAAA,GAA7B,UAA8B,UAA6B,EAAA;;;;;;;AACjD,wBAAA,iBAAiB,GAAqB;4BACxC,QAAQ,EAAE,UAAU,CAAC,QAAQ;4BAC7B,cAAc,EAAE,UAAU,CAAC,cAAc;4BACzC,WAAW,EAAE,UAAU,CAAC,WAAW;4BACnC,aAAa,EAAE,UAAU,CAAC,aAAa;4BACvC,KAAK,EAAE,UAAU,CAAC,KAAK;4BACvB,SAAS,EAAE,UAAU,CAAC,SAAS;4BAC/B,mBAAmB,EAAE,UAAU,CAAC,mBAAmB;yBACtD,CAAC;AAEI,wBAAA,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;wBAChC,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;wBAEvD,mBAAmB,GAAyB,EAAE,CAAC;AACrD,wBAAA,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;4BAC9B,IAAG,CAAC,KAAI,CAAC,2BAA2B,CAAC,GAAG,EAAE,iBAAiB,EAAE,KAAK,CAAC,EAAE;gCACjE,OAAO;AACV,6BAAA;4BAED,IAAM,WAAW,GAAG,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;4BAEvD,IAAI,WAAW,IAAI,KAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,iBAAiB,CAAC,EAAE;gCAC7E,IAAM,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC9D,gCAAA,IAAI,aAAa,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAE;oCACpD,mBAAmB,CAAC,IAAI,CAAC,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,iCAAA;AACJ,6BAAA;AACL,yBAAC,CAAC,CAAC;AACH,wBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAA,CAAA;;AAAtC,wBAAA,EAAA,CAAA,IAAA,EAAsC,CAAC;AACvC,wBAAA,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;;;;;AAC7C,KAAA,CAAA;AAED;;;;;;AAMG;IACH,YAAqB,CAAA,SAAA,CAAA,qBAAA,GAArB,UAAsB,aAA4B,EAAA;QAAlD,IA4CC,KAAA,GAAA,IAAA,CAAA;AA3CG,QAAA,IAAM,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC7C,IAAM,gBAAgB,GAAoB,EAAE,CAAC;AAE7C,QAAA,cAAc,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAA;AAC5B,YAAA,IAAI,CAAC,KAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,aAAa,CAAC,aAAa,EAAE,aAAa,CAAC,KAAK,CAAC,EAAE;;gBAEhF,OAAO;AACV,aAAA;YAED,IAAM,MAAM,GAAyB,KAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAE/D,IAAI,CAAC,MAAM,EAAE;gBACT,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,CAAC,aAAa,CAAC,aAAa,IAAI,CAAC,KAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,aAAa,CAAC,aAAa,CAAC,EAAE;gBAChG,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,CAAC,aAAa,CAAC,cAAc,IAAI,CAAC,KAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,aAAa,CAAC,cAAc,CAAC,EAAE;gBACnG,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,CAAC,aAAa,CAAC,QAAQ,IAAI,CAAC,KAAI,CAAC,aAAa,CAAC,MAAM,EAAE,aAAa,CAAC,QAAQ,CAAC,EAAE;gBACjF,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,KAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,aAAa,CAAC,WAAW,CAAC,EAAE;gBAC1F,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,KAAI,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,CAAC,KAAK,CAAC,EAAE;gBACxE,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,CAAC,aAAa,CAAC,eAAe,IAAI,CAAC,KAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,aAAa,CAAC,eAAe,CAAC,EAAE;gBACtG,OAAO;AACV,aAAA;AAED,YAAA,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAClC,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,gBAAgB,CAAC;KAC3B,CAAA;AAED;;;;;;AAMG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,UAAa,GAAW,EAAE,aAAsB,EAAE,QAAiB,EAAA;AAC/D,QAAA,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;;AAEtD,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,aAAa,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,EAAE;AAC3E,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,QAAQ,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE;AACjE,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;;AAID,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACH,YAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,GAAW,EAAA;AACvB,QAAA,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;;AAEtD,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAM,YAAY,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;;AAEvC,QAAA,IAAI,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC;AAClE,YAAA,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC;AACtE,YAAA,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,6BAA6B,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC;AACvF,YAAA,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EACzE;AACE,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE;;AAEvE,YAAA,IAAM,kBAAkB,GAAG,EAAA,GAAG,cAAc,CAAC,aAAa,GAAG,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,mBAAqB,CAAC;AAC/I,YAAA,IAAM,kBAAkB,GAAG,EAAA,GAAG,cAAc,CAAC,aAAa,GAAG,UAAU,CAAC,mBAAmB,GAAG,aAAa,GAAG,UAAU,CAAC,mBAAqB,CAAC;YAC/I,IAAI,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AAChI,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;AACJ,SAAA;AAAM,aAAA,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;;AAEjE,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;AAKG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,uBAAuB,GAAvB,UAAwB,MAA2B,EAAE,MAAwB,EAAA;AACzE,QAAA,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;AACnE,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,CAAC,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,MAAM,CAAC,iBAAiB,CAAC,EAAE;AAC9F,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED;;;AAGG;QACH,IAAI,CAAC,OAAO,MAAM,CAAC,aAAa,KAAK,QAAQ,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,aAAa,CAAC,EAAE;AACtG,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,CAAC,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE;AAC5E,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;AAC1D,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,cAAc,CAAC,EAAE;AACrF,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;AACnE,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED;;;AAGG;AACH,QAAA,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE;AAC7D,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;;AAGD,QAAA,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,mBAAmB,EAAE;;AAE1D,YAAA,IAAI,MAAM,CAAC,mBAAmB,KAAK,MAAM,CAAC,mBAAmB,EAAE;AAC3D,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;AACJ,SAAA;;AAGD,QAAA,IAAI,MAAM,CAAC,cAAc,KAAK,cAAc,CAAC,6BAA6B,EAAE;AACxE,YAAA,IAAG,CAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE;AACrE,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;;AAGD,YAAA,IAAI,MAAM,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG,EAAE;AAC/C,gBAAA,IAAG,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;AACvD,oBAAA,OAAO,KAAK,CAAC;AAChB,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACH,YAAwB,CAAA,SAAA,CAAA,wBAAA,GAAxB,UAAyB,MAAyB,EAAA;AAC9C,QAAA,OAAO,IAAI,CAAC,gCAAgC,CACxC,MAAM,CAAC,WAAW,EAClB,MAAM,CAAC,QAAQ,CAClB,CAAC;KACL,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,gCAAgC,GAAxC,UACI,WAAoB,EACpB,QAAiB,EAAA;QAFrB,IAkCC,KAAA,GAAA,IAAA,CAAA;AA7BG,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACpC,IAAM,mBAAmB,GAAqB,EAAE,CAAC;AAEjD,QAAA,YAAY,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAA;;AAE1B,YAAA,IAAI,CAAC,KAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;gBAC/B,OAAO;AACV,aAAA;;YAGD,IAAM,MAAM,GAAG,KAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YAE7C,IAAI,CAAC,MAAM,EAAE;gBACT,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,KAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;gBAC9D,OAAO;AACV,aAAA;AAED,YAAA,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,KAAI,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;gBACrD,OAAO;AACV,aAAA;AAED,YAAA,mBAAmB,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAE3C,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,mBAAmB,CAAC;KAC9B,CAAA;AAED;;;AAGG;IACH,YAA2B,CAAA,SAAA,CAAA,2BAAA,GAA3B,UAA4B,IAAY,EAAA;QAAxC,IA0BC,KAAA,GAAA,IAAA,CAAA;AAzBG,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACrD,IAAI,aAAa,GAAG,IAAI,CAAC;AAEzB,QAAA,YAAY,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAA;;AAE1B,YAAA,IAAI,CAAC,KAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC/E,OAAO;AACV,aAAA;;YAGD,IAAM,MAAM,GAAG,KAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAEnD,IAAI,CAAC,MAAM,EAAE;gBACT,OAAO;AACV,aAAA;YAED,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACrC,OAAO;AACV,aAAA;YAED,aAAa,GAAG,MAAM,CAAC;AAE3B,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,aAAa,CAAC;KACxB,CAAA;AAED;;AAEG;AACG,IAAA,YAAA,CAAA,SAAA,CAAA,iBAAiB,GAAvB,YAAA;;;;;;;AACU,wBAAA,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;wBACvC,eAAe,GAAyB,EAAE,CAAC;AAEjD,wBAAA,cAAc,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAA;4BAC5B,eAAe,CAAC,IAAI,CAAC,KAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvD,yBAAC,CAAC,CAAC;AAEH,wBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA,CAAA;;AAAlC,wBAAA,EAAA,CAAA,IAAA,EAAkC,CAAC;;;;;AACtC,KAAA,CAAA;AAED;;;AAGG;IACG,YAAa,CAAA,SAAA,CAAA,aAAA,GAAnB,UAAoB,UAAkB,EAAA;;;;;;AAC5B,wBAAA,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;wBAC5C,IAAI,CAAC,OAAO,EAAE;AACV,4BAAA,MAAM,eAAe,CAAC,yBAAyB,EAAE,CAAC;AACrD,yBAAA;AACD,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAA,CAAA;;AAAxC,wBAAA,EAAA,CAAA,IAAA,EAAwC,CAAC;AACzC,wBAAA,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;;;;;AAC/B,KAAA,CAAA;AAED;;;AAGG;IACG,YAAoB,CAAA,SAAA,CAAA,oBAAA,GAA1B,UAA2B,OAAsB,EAAA;;;;;;;AACvC,wBAAA,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AACnC,wBAAA,SAAS,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;wBACxC,kBAAkB,GAAyB,EAAE,CAAC;AAEpD,wBAAA,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;4BAC7B,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AAC9B,gCAAA,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC3B,6BAAA;AACL,yBAAC,CAAC,CAAC;AAEH,wBAAA,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;4BACjC,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;gCAC9B,kBAAkB,CAAC,IAAI,CAAC,KAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC;AACxD,6BAAA;AACL,yBAAC,CAAC,CAAC;AAEH,wBAAA,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;4BAClC,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;AAC9B,gCAAA,KAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAChC,6BAAA;AACL,yBAAC,CAAC,CAAC;AAEH,wBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAA,CAAA;;AAArC,wBAAA,EAAA,CAAA,IAAA,EAAqC,CAAC;;;;;AACzC,KAAA,CAAA;AAED;;;AAGG;IACG,YAAiB,CAAA,SAAA,CAAA,iBAAA,GAAvB,UAAwB,GAAW,EAAA;;;;;;AACzB,wBAAA,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;wBACtD,IAAI,CAAC,UAAU,EAAE;4BACb,OAAO,CAAA,CAAA,YAAA,CAAA;AACV,yBAAA;AAGG,wBAAA,IAAA,EAAA,UAAU,CAAC,cAAc,CAAC,WAAW,EAAE,KAAK,cAAc,CAAC,6BAA6B,CAAC,WAAW,EAAE,CAAA,EAAtG,OAAsG,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;8BACnG,UAAU,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG,CAAA,EAAjD,OAAiD,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBAC1C,+BAA+B,GAAG,UAA+B,CAAC;AAClE,wBAAA,GAAG,GAAG,+BAA+B,CAAC,KAAK,CAAC;AAE9C,wBAAA,IAAA,CAAA,GAAG,EAAH,OAAG,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;;;wBAEC,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAA,CAAA;;AAAhD,wBAAA,EAAA,CAAA,IAAA,EAAgD,CAAC;;;;AAEjD,wBAAA,MAAM,eAAe,CAAC,+BAA+B,EAAE,CAAC;AAMxE,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAA;;;;AAC/B,KAAA,CAAA;AAED;;AAEG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;QAAA,IASC,KAAA,GAAA,IAAA,CAAA;AARG,QAAA,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AACpC,QAAA,YAAY,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAA;AAC1B,YAAA,IAAI,KAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;AAC9B,gBAAA,KAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC7B,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;;;AAOG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,OAAoB,EAAE,OAAwB,EAAE,WAAmB,EAAA;AAC/E,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACzD,IAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC1D,QAAA,IAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;AAC3E,QAAA,IAAM,kBAAkB,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QAC3E,IAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QAErE,IAAI,aAAa,IAAI,aAAa,EAAE;AAChC,YAAA,aAAa,CAAC,aAAa,GAAG,IAAI,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;AAC7F,SAAA;QAED,OAAO;AACH,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,OAAO,EAAE,aAAa;AACtB,YAAA,WAAW,EAAE,iBAAiB;AAC9B,YAAA,YAAY,EAAE,kBAAkB;AAChC,YAAA,WAAW,EAAE,iBAAiB;SACjC,CAAC;KACL,CAAA;AAED;;;AAGG;IACH,YAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,OAAoB,EAAA;QACrC,IAAM,UAAU,GAAW,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AAC1E,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;KACtC,CAAA;AAED;;;;;AAKG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,UAAU,GAAV,UAAW,OAAoB,EAAE,SAAqB,EAAA;QAAtD,IA4BC,KAAA,GAAA,IAAA,CAAA;AA3BG,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;AAC5D,QAAA,IAAM,aAAa,GAAqB;YACpC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,cAAc,EAAE,cAAc,CAAC,QAAQ;YACvC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,OAAO,CAAC,QAAQ;SAC1B,CAAC;QAEF,IAAM,QAAQ,GAAoB,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;AACrF,QAAA,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC;QAEpC,IAAI,WAAW,GAAG,CAAC,EAAE;AACjB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;AACnE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;aAAM,IAAI,WAAW,GAAG,CAAC,EAAE;AACxB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAClB,mEAAmE,CACtE,CAAC;AACF,YAAA,QAAQ,CAAC,OAAO,CAAC,UAAC,OAAO,EAAA;gBACrB,KAAI,CAAC,aAAa,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC;AACxD,aAAC,CAAC,CAAC;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;AACvE,QAAA,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;KACtB,CAAA;AAED;;;;AAIG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,mBAAmB,GAAnB,UAAoB,MAAwB,EAAE,SAAqB,EAAA;QAAnE,IAgBC,KAAA,GAAA,IAAA,CAAA;AAfG,QAAA,IAAM,WAAW,GAAG,SAAS,IAAI,SAAS,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC;QAElF,IAAM,QAAQ,GAAoB,EAAE,CAAC;AACrC,QAAA,WAAW,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;AACpB,YAAA,IAAI,CAAC,KAAI,CAAC,uBAAuB,CAAC,GAAG,EAAA,QAAA,CAAA,EAAG,QAAQ,EAAE,KAAI,CAAC,QAAQ,EAAK,EAAA,MAAM,EAAE,EAAE;gBAC1E,OAAO;AACV,aAAA;YAED,IAAM,OAAO,GAAG,KAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YAC/C,IAAI,OAAO,IAAI,KAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;AAC1D,gBAAA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC1B,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,QAAQ,CAAC;KACnB,CAAA;AAED;;;;;AAKG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,uBAAuB,GAAvB,UAAwB,QAAgB,EAAE,MAAwB,EAAA;AAC9D,QAAA,IAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;AACnC,QAAA,IAAI,MAAM,CAAC,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACtE,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,aAAa,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AAChF,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;AAGG;IACH,YAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,GAAW,EAAA;AACrB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KACxB,CAAA;AAED;;;AAGG;IACH,YAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,GAAW,EAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KACxB,CAAA;AAED;;;;;;AAMG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,cAAc,GAAd,UAAe,OAAoB,EAAE,OAAwB,EAAE,SAAqB,EAAA;QAApF,IAqDC,KAAA,GAAA,IAAA,CAAA;AApDG,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAChE,IAAM,MAAM,GAAI,QAAQ,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5D,IAAM,UAAU,GAAG,OAAO,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,CAAC;AAC/E;;;AAGG;AACH,QAAA,IAAM,cAAc,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,WAAW,EAAE,KAAK,oBAAoB,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,cAAc,CAAC,6BAA6B,GAAG,cAAc,CAAC,YAAY,CAAC;AAE3L,QAAA,IAAM,iBAAiB,GAAqB;YACxC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,WAAW,EAAE,OAAO,CAAC,WAAW;AAChC,YAAA,cAAc,EAAE,cAAc;YAC9B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,KAAK,EAAE,OAAO,CAAC,QAAQ;AACvB,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,SAAS,EAAE,UAAU;YACrB,KAAK,EAAE,OAAO,CAAC,MAAM;YACrB,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;SACnD,CAAC;AAEF,QAAA,IAAM,eAAe,GAAG,SAAS,IAAI,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,WAAW,CAAC;QAC9F,IAAM,YAAY,GAAwB,EAAE,CAAC;AAE7C,QAAA,eAAe,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;;YAExB,IAAI,KAAI,CAAC,2BAA2B,CAAC,GAAG,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE;gBAChE,IAAM,WAAW,GAAG,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;;gBAGvD,IAAI,WAAW,IAAI,KAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,iBAAiB,CAAC,EAAE;AAC7E,oBAAA,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAClC,iBAAA;AACJ,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,IAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC;QAC5C,IAAI,eAAe,GAAG,CAAC,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;AACvE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;aAAM,IAAI,eAAe,GAAG,CAAC,EAAE;AAC5B,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAClB,2EAA2E,CAC9E,CAAC;AACF,YAAA,YAAY,CAAC,OAAO,CAAC,UAAC,WAAW,EAAA;gBAC7B,KAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC,CAAC;AAChE,aAAC,CAAC,CAAC;AACH,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;AAC/E,QAAA,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;KAC1B,CAAA;AAED;;;;;;AAMG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,2BAA2B,GAA3B,UAA4B,QAAgB,EAAE,MAAwB,EAAE,uBAAgC,EAAA;AACpG,QAAA,IAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;AACnC,QAAA,IAAI,MAAM,CAAC,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACtE,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,aAAa,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AAChF,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AAChE,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,mBAAmB,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AAC5F,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;AACvC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,gBAAA,IAAI,uBAAuB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE;;AAEnE,oBAAA,OAAO,KAAK,CAAC;AAChB,iBAAA;AAAM,qBAAA,IAAI,CAAC,uBAAuB,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE;;AAE1E,oBAAA,OAAO,IAAI,CAAC;AACf,iBAAA;AACJ,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;AAIG;IACH,YAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvB,UAAwB,MAAwB,EAAA;QAAhD,IAgBC,KAAA,GAAA,IAAA,CAAA;AAfG,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEtC,IAAM,YAAY,GAAwB,EAAE,CAAC;AAC7C,QAAA,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;YAC9B,IAAI,CAAC,KAAI,CAAC,2BAA2B,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;gBACtD,OAAO;AACV,aAAA;YAED,IAAM,WAAW,GAAG,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;YACvD,IAAI,WAAW,IAAI,KAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE;AAClE,gBAAA,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAClC,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,YAAY,CAAC;KACvB,CAAA;AAED;;;;;AAKG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,OAAoB,EAAE,QAAiB,EAAE,SAAqB,EAAA;QAA9E,IAkCC,KAAA,GAAA,IAAA,CAAA;AAjCG,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACjE,IAAM,EAAE,GAAG,QAAQ,GAAG,aAAa,GAAG,SAAS,CAAC;AAChD,QAAA,IAAM,kBAAkB,GAAqB;YACzC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,cAAc,EAAE,cAAc,CAAC,aAAa;YAC5C,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvB,YAAA,QAAQ,EAAE,EAAE;SACf,CAAC;AAEF,QAAA,IAAM,gBAAgB,GAAG,SAAS,IAAI,SAAS,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,YAAY,CAAC;QACjG,IAAM,aAAa,GAAyB,EAAE,CAAC;AAE/C,QAAA,gBAAgB,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;;YAEzB,IAAI,KAAI,CAAC,4BAA4B,CAAC,GAAG,EAAE,kBAAkB,CAAC,EAAE;gBAC5D,IAAM,YAAY,GAAG,KAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC;;gBAEzD,IAAI,YAAY,IAAI,KAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,kBAAkB,CAAC,EAAE;AAChF,oBAAA,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACpC,iBAAA;AACJ,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,IAAM,gBAAgB,GAAG,aAAa,CAAC,MAAM,CAAC;QAC9C,IAAI,gBAAgB,GAAG,CAAC,EAAE;AACtB,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;AACjF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;;AAGD,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;AACjF,QAAA,OAAO,aAAa,CAAC,CAAC,CAAuB,CAAC;KACjD,CAAA;AAED;;;;AAIG;AACH,IAAA,YAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5B,UAA6B,QAAgB,EAAE,MAAwB,EAAA;AACnE,QAAA,IAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;AACnC,QAAA,IAAI,MAAM,CAAC,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AACtE,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;;QAGD,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AAC1F,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,IAAI,MAAM,CAAC,aAAa,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;AAChF,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;AAEG;IACH,YAAwB,CAAA,SAAA,CAAA,wBAAA,GAAxB,UAAyB,WAAmB,EAAA;AACxC,QAAA,IAAM,iBAAiB,GAAsB;AACzC,YAAA,WAAW,EAAA,WAAA;YACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;SAC1B,CAAC;QAEF,IAAM,WAAW,GAAqB,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QACvF,IAAM,kBAAkB,GAAwB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,UAAC,GAAG,EAAA,EAAK,OAAA,WAAW,CAAC,GAAG,CAAC,CAAA,EAAA,CAAC,CAAC;AAExG,QAAA,IAAM,cAAc,GAAG,kBAAkB,CAAC,MAAM,CAAC;QACjD,IAAI,cAAc,GAAG,CAAC,EAAE;AACpB,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;aAAM,IAAI,cAAc,GAAG,CAAC,EAAE;AAC3B,YAAA,MAAM,eAAe,CAAC,6CAA6C,EAAE,CAAC;AACzE,SAAA;AAED,QAAA,OAAO,kBAAkB,CAAC,CAAC,CAAsB,CAAC;KACrD,CAAA;AAED;;;;AAIG;IACH,YAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,WAAmB,EAAA;QACjC,IAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QAC/D,OAAO,CAAC,EAAE,WAAW,IAAI,WAAW,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC;KACpE,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,kBAAkB,GAA1B,UAA2B,MAAwC,EAAE,aAAqB,EAAA;QACtF,OAAO,CAAC,EAAE,CAAC,OAAO,MAAM,CAAC,aAAa,KAAK,QAAQ,MAAM,aAAa,KAAK,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;KACrG,CAAA;AAED;;;;;AAKG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,mBAAmB,GAA3B,UAA4B,MAAqB,EAAE,cAAsB,EAAA;QACrE,OAAO,CAAC,EAAE,CAAC,OAAO,MAAM,CAAC,cAAc,KAAK,QAAQ,MAAM,cAAc,KAAK,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC;KACxG,CAAA;AAED;;;;;AAKG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,aAAa,GAArB,UAAsB,MAAqB,EAAE,QAAgB,EAAA;QACzD,OAAO,CAAC,EAAE,CAAC,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,MAAM,QAAQ,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KAClH,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,sBAAsB,GAA9B,UAA+B,MAAwB,EAAE,iBAAyB,EAAA;AAC9E,QAAA,OAAO,CAAC,EAAE,MAAM,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,MAAM,CAAC,iBAAiB,CAAC,CAAC;KACzF,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,gBAAgB,GAAxB,UAAyB,MAA4D,EAAE,WAAmB,EAAA;QACtG,IAAM,aAAa,GAAG,IAAI,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;AACpE,QAAA,IAAI,aAAa,IAAI,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;AACzE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,mBAAmB,GAA3B,UAA4B,MAAwB,EAAE,cAAsB,EAAA;AACxE,QAAA,QAAQ,MAAM,CAAC,cAAc,IAAI,cAAc,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,cAAc,CAAC,WAAW,EAAE,EAAE;KAC1G,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,aAAa,GAArB,UAAsB,MAA4C,EAAE,QAAgB,EAAA;AAChF,QAAA,OAAO,CAAC,EAAE,MAAM,CAAC,QAAQ,IAAI,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,CAAC;KAC9D,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,aAAa,GAArB,UAAsB,MAA4C,EAAE,QAAgB,EAAA;AAChF,QAAA,OAAO,CAAC,EAAE,MAAM,CAAC,QAAQ,IAAI,QAAQ,KAAK,MAAM,CAAC,QAAQ,CAAC,CAAC;KAC9D,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,UAAU,GAAlB,UAAmB,MAAwC,EAAE,KAAa,EAAA;AACtE,QAAA,OAAO,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC;KACrD,CAAA;AAED;;;;;AAKG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,UAA6B,MAAqB,EAAE,eAAuB,EAAA;AACvE,QAAA,OAAO,CAAC,EAAE,MAAM,CAAC,eAAe,IAAI,eAAe,KAAK,MAAM,CAAC,eAAe,CAAC,CAAC;KACnF,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,WAAW,GAAnB,UAAoB,MAAwB,EAAE,MAAgB,EAAA;AAC1D,QAAA,IAAM,0BAA0B,IAAI,MAAM,CAAC,cAAc,KAAK,cAAc,CAAC,YAAY,IAAI,MAAM,CAAC,cAAc,KAAK,cAAc,CAAC,6BAA6B,CAAC,CAAC;AAErK,QAAA,IAAK,0BAA0B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAC/C,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,IAAM,cAAc,GAAa,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAEpE,QAAA,OAAO,cAAc,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;KAClD,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,cAAc,GAAtB,UAAuB,MAAwB,EAAE,SAA+B,EAAA;AAC5E,QAAA,OAAO,CAAC,EAAE,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC;KACjE,CAAA;AAED;;;;AAIG;AACK,IAAA,YAAA,CAAA,SAAA,CAAA,UAAU,GAAlB,UAAmB,MAAwB,EAAE,KAAa,EAAA;AACtD,QAAA,OAAO,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC;KACrD,CAAA;AAED;;;AAGG;IACK,YAAa,CAAA,SAAA,CAAA,aAAA,GAArB,UAAsB,GAAW,EAAA;QAC7B,OAAO,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;KAC3C,CAAA;AAED;;;AAGG;IACO,YAAmB,CAAA,SAAA,CAAA,mBAAA,GAA7B,UAA8B,GAAW,EAAA;QACrC,OAAO,GAAG,CAAC,OAAO,CAAC,4BAA4B,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;KACrE,CAAA;AAED;;AAEG;IACH,YAAiC,CAAA,SAAA,CAAA,iCAAA,GAAjC,UAAkC,SAAiB,EAAA;QAC/C,OAAU,4BAA4B,CAAC,SAAS,GAAA,GAAA,GAAI,IAAI,CAAC,QAAQ,GAAI,GAAA,GAAA,SAAW,CAAC;KACpF,CAAA;AAED;;;;AAIG;AACI,IAAA,YAAA,CAAA,QAAQ,GAAf,UAAmB,GAAM,EAAE,IAAY,EAAA;AACnC,QAAA,KAAK,IAAM,YAAY,IAAI,IAAI,EAAE;YAC7B,GAAG,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;AAC1C,SAAA;AACD,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;IACL,OAAC,YAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,IAAA,mBAAA,kBAAA,UAAA,MAAA,EAAA;IAAyC,SAAY,CAAA,mBAAA,EAAA,MAAA,CAAA,CAAA;AAArD,IAAA,SAAA,mBAAA,GAAA;;KAiGC;AAhGG,IAAA,mBAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;QACI,IAAM,UAAU,GAAG,2FAA2F,CAAC;AAC/G,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;QACI,IAAM,UAAU,GAAG,2FAA2F,CAAC;AAC/G,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;QACI,IAAM,UAAU,GAAG,qGAAqG,CAAC;AACzH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;QACI,IAAM,UAAU,GAAG,qGAAqG,CAAC;AACzH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAxB,YAAA;QACI,IAAM,UAAU,GAAG,yGAAyG,CAAC;AAC7H,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAxB,YAAA;QACI,IAAM,UAAU,GAAG,yGAAyG,CAAC;AAC7H,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,yBAAyB,GAAzB,YAAA;QACI,IAAM,UAAU,GAAG,0GAA0G,CAAC;AAC9H,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,yBAAyB,GAAzB,YAAA;QACI,IAAM,UAAU,GAAG,0GAA0G,CAAC;AAC9H,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QACI,IAAM,UAAU,GAAG,+FAA+F,CAAC;AACnH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QACI,IAAM,UAAU,GAAG,+FAA+F,CAAC;AACnH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,YAAA;QACI,IAAM,UAAU,GAAG,mGAAmG,CAAC;AACvH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,YAAA;QACI,IAAM,UAAU,GAAG,mGAAmG,CAAC;AACvH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;QACI,IAAM,UAAU,GAAG,qGAAqG,CAAC;AACzH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;QACI,IAAM,UAAU,GAAG,qGAAqG,CAAC;AACzH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAxB,YAAA;QACI,IAAM,UAAU,GAAG,yGAAyG,CAAC;AAC7H,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,YAAA;QACI,IAAM,UAAU,GAAG,mGAAmG,CAAC;AACvH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,kBAAkB,GAAlB,YAAA;QACI,IAAM,UAAU,GAAG,mGAAmG,CAAC;AACvH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;QACI,IAAM,UAAU,GAAG,2FAA2F,CAAC;AAC/G,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;QACI,IAAM,UAAU,GAAG,4FAA4F,CAAC;AAChH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QACI,IAAM,UAAU,GAAG,wFAAwF,CAAC;AAC5G,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QACI,IAAM,UAAU,GAAG,+FAA+F,CAAC;AACnH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;QACI,IAAM,UAAU,GAAG,6FAA6F,CAAC;AACjH,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;AACK,IAAA,mBAAA,CAAA,SAAA,CAAA,KAAK,GAAX,YAAA;;;;gBACU,UAAU,GAAG,sFAAsF,CAAC;AAC1G,gBAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;;;AACrD,KAAA,CAAA;AACD,IAAA,mBAAA,CAAA,SAAA,CAAA,wBAAwB,GAAxB,YAAA;QACI,IAAM,UAAU,GAAG,yGAAyG,CAAC;AAC7H,QAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;KACrD,CAAA;IACL,OAAC,mBAAA,CAAA;AAAD,CAjGA,CAAyC,YAAY,CAiGpD;;;;"}