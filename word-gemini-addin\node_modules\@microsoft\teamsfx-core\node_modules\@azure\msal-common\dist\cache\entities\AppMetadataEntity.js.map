{"version": 3, "file": "AppMetadataEntity.js", "sources": ["../../../src/cache/entities/AppMetadataEntity.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { APP_METADATA, Separators } from \"../../utils/Constants\";\r\n\r\n/**\r\n * APP_METADATA Cache\r\n *\r\n * Key:Value Schema:\r\n *\r\n * Key: appmetadata-<environment>-<client_id>\r\n *\r\n * Value:\r\n * {\r\n *      clientId: client ID of the application\r\n *      environment: entity that issued the token, represented as a full host\r\n *      familyId: Family ID identifier, '1' represents Microsoft Family\r\n * }\r\n */\r\nexport class AppMetadataEntity {\r\n    clientId: string;\r\n    environment: string;\r\n    familyId?: string;\r\n\r\n    /**\r\n     * Generate AppMetadata Cache Key as per the schema: appmetadata-<environment>-<client_id>\r\n     */\r\n    generateAppMetadataKey(): string {\r\n        return AppMetadataEntity.generateAppMetadataCacheKey(this.environment, this.clientId);\r\n    }\r\n\r\n    /**\r\n     * Generate AppMetadata Cache Key\r\n     */\r\n    static generateAppMetadataCacheKey(environment: string, clientId: string): string {\r\n        const appMetaDataKeyArray: Array<string> = [\r\n            APP_METADATA,\r\n            environment,\r\n            clientId,\r\n        ];\r\n        return appMetaDataKeyArray.join(Separators.CACHE_KEY_SEPARATOR).toLowerCase();\r\n    }\r\n\r\n    /**\r\n     * Creates AppMetadataEntity\r\n     * @param clientId\r\n     * @param environment\r\n     * @param familyId\r\n     */\r\n    static createAppMetadataEntity(clientId: string, environment: string, familyId?: string): AppMetadataEntity {\r\n        const appMetadata = new AppMetadataEntity();\r\n\r\n        appMetadata.clientId = clientId;\r\n        appMetadata.environment = environment;\r\n        if (familyId) {\r\n            appMetadata.familyId = familyId;\r\n        }\r\n\r\n        return appMetadata;\r\n    }\r\n\r\n    /**\r\n     * Validates an entity: checks for all expected params\r\n     * @param entity\r\n     */\r\n    static isAppMetadataEntity(key: string, entity: object): boolean {\r\n\r\n        if (!entity) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            key.indexOf(APP_METADATA) === 0 &&\r\n            entity.hasOwnProperty(\"clientId\") &&\r\n            entity.hasOwnProperty(\"environment\")\r\n        );\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAIH;;;;;;;;;;;;;AAaG;AACH,IAAA,iBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,iBAAA,GAAA;KA0DC;AArDG;;AAEG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtB,YAAA;AACI,QAAA,OAAO,iBAAiB,CAAC,2BAA2B,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACzF,CAAA;AAED;;AAEG;AACI,IAAA,iBAAA,CAAA,2BAA2B,GAAlC,UAAmC,WAAmB,EAAE,QAAgB,EAAA;AACpE,QAAA,IAAM,mBAAmB,GAAkB;YACvC,YAAY;YACZ,WAAW;YACX,QAAQ;SACX,CAAC;QACF,OAAO,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;KACjF,CAAA;AAED;;;;;AAKG;AACI,IAAA,iBAAA,CAAA,uBAAuB,GAA9B,UAA+B,QAAgB,EAAE,WAAmB,EAAE,QAAiB,EAAA;AACnF,QAAA,IAAM,WAAW,GAAG,IAAI,iBAAiB,EAAE,CAAC;AAE5C,QAAA,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAChC,QAAA,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC;AACtC,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACnC,SAAA;AAED,QAAA,OAAO,WAAW,CAAC;KACtB,CAAA;AAED;;;AAGG;AACI,IAAA,iBAAA,CAAA,mBAAmB,GAA1B,UAA2B,GAAW,EAAE,MAAc,EAAA;QAElD,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,QACI,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC;AAC/B,YAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AACjC,YAAA,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,EACtC;KACL,CAAA;IACL,OAAC,iBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}