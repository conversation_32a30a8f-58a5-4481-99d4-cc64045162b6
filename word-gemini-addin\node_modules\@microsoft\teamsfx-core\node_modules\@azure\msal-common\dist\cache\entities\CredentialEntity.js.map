{"version": 3, "file": "CredentialEntity.js", "sources": ["../../../src/cache/entities/CredentialEntity.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Separators, CredentialType, CacheType, Constants, AuthenticationScheme } from \"../../utils/Constants\";\r\nimport { ClientAuthError } from \"../../error/ClientAuthError\";\r\n\r\n/**\r\n * Base type for credentials to be stored in the cache: eg: ACCESS_TOKEN, ID_TOKEN etc\r\n *\r\n * Key:Value Schema:\r\n *\r\n * Key: <home_account_id*>-<environment>-<credential_type>-<client_id>-<realm*>-<target*>-<requestedClaims*>-<scheme*>\r\n *\r\n * Value Schema:\r\n * {\r\n *      homeAccountId: home account identifier for the auth scheme,\r\n *      environment: entity that issued the token, represented as a full host\r\n *      credentialType: Type of credential as a string, can be one of the following: RefreshToken, AccessToken, IdToken, Password, Cookie, Certificate, Other\r\n *      clientId: client ID of the application\r\n *      secret: Actual credential as a string\r\n *      familyId: Family ID identifier, usually only used for refresh tokens\r\n *      realm: Full tenant or organizational identifier that the account belongs to\r\n *      target: Permissions that are included in the token, or for refresh tokens, the resource identifier.\r\n *      tokenType: Matches the authentication scheme for which the token was issued (i.e. Bearer or pop)\r\n *      requestedClaimsHash: Matches the SHA 256 hash of the claims object included in the token request\r\n *      userAssertionHash: Matches the SHA 256 hash of the obo_assertion for the OBO flow\r\n * }\r\n */\r\nexport class CredentialEntity {\r\n    homeAccountId: string;\r\n    environment: string;\r\n    credentialType: CredentialType;\r\n    clientId: string;\r\n    secret: string;\r\n    familyId?: string;\r\n    realm?: string;\r\n    target?: string;\r\n    userAssertionHash?: string;\r\n    tokenType?: AuthenticationScheme;\r\n    keyId?: string;\r\n    requestedClaimsHash?: string;\r\n\r\n    /**\r\n     * Generate Account Id key component as per the schema: <home_account_id>-<environment>\r\n     */\r\n    generateAccountId(): string {\r\n        return CredentialEntity.generateAccountIdForCacheKey(this.homeAccountId, this.environment);\r\n    }\r\n\r\n    /**\r\n     * Generate Credential Id key component as per the schema: <credential_type>-<client_id>-<realm>\r\n     */\r\n    generateCredentialId(): string {\r\n        return CredentialEntity.generateCredentialIdForCacheKey(\r\n            this.credentialType,\r\n            this.clientId,\r\n            this.realm,\r\n            this.familyId\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Generate target key component as per schema: <target>\r\n     */\r\n    generateTarget(): string {\r\n        return CredentialEntity.generateTargetForCacheKey(this.target);\r\n    }\r\n\r\n    /**\r\n     * generates credential key\r\n     */\r\n    generateCredentialKey(): string {\r\n        return CredentialEntity.generateCredentialCacheKey(\r\n            this.homeAccountId,\r\n            this.environment,\r\n            this.credentialType,\r\n            this.clientId,\r\n            this.realm,\r\n            this.target,\r\n            this.familyId,\r\n            this.tokenType,\r\n            this.requestedClaimsHash,\r\n        );\r\n    }\r\n\r\n    /**\r\n     * returns the type of the cache (in this case credential)\r\n     */\r\n    generateType(): number {\r\n        switch (this.credentialType) {\r\n            case CredentialType.ID_TOKEN:\r\n                return CacheType.ID_TOKEN;\r\n            case CredentialType.ACCESS_TOKEN:\r\n            case CredentialType.ACCESS_TOKEN_WITH_AUTH_SCHEME:\r\n                return CacheType.ACCESS_TOKEN;\r\n            case CredentialType.REFRESH_TOKEN:\r\n                return CacheType.REFRESH_TOKEN;\r\n            default: {\r\n                throw ClientAuthError.createUnexpectedCredentialTypeError();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * generates credential key\r\n     * <home_account_id*>-\\<environment>-<credential_type>-<client_id>-<realm\\*>-<target\\*>-<scheme\\*>\r\n     */\r\n    static generateCredentialCacheKey(\r\n        homeAccountId: string,\r\n        environment: string,\r\n        credentialType: CredentialType,\r\n        clientId: string,\r\n        realm?: string,\r\n        target?: string,\r\n        familyId?: string,\r\n        tokenType?: AuthenticationScheme,\r\n        requestedClaimsHash?: string\r\n    ): string {\r\n        const credentialKey = [\r\n            this.generateAccountIdForCacheKey(homeAccountId, environment),\r\n            this.generateCredentialIdForCacheKey(credentialType, clientId, realm, familyId),\r\n            this.generateTargetForCacheKey(target),\r\n            this.generateClaimsHashForCacheKey(requestedClaimsHash),\r\n            this.generateSchemeForCacheKey(tokenType)\r\n        ];\r\n\r\n        return credentialKey.join(Separators.CACHE_KEY_SEPARATOR).toLowerCase();\r\n    }\r\n\r\n    /**\r\n     * generates Account Id for keys\r\n     * @param homeAccountId\r\n     * @param environment\r\n     */\r\n    private static generateAccountIdForCacheKey(\r\n        homeAccountId: string,\r\n        environment: string\r\n    ): string {\r\n        const accountId: Array<string> = [homeAccountId, environment];\r\n        return accountId.join(Separators.CACHE_KEY_SEPARATOR).toLowerCase();\r\n    }\r\n\r\n    /**\r\n     * Generates Credential Id for keys\r\n     * @param credentialType\r\n     * @param realm\r\n     * @param clientId\r\n     * @param familyId\r\n     */\r\n    private static generateCredentialIdForCacheKey(\r\n        credentialType: CredentialType,\r\n        clientId: string,\r\n        realm?: string,\r\n        familyId?: string\r\n    ): string {\r\n        const clientOrFamilyId =\r\n            credentialType === CredentialType.REFRESH_TOKEN\r\n                ? familyId || clientId\r\n                : clientId;\r\n        const credentialId: Array<string> = [\r\n            credentialType,\r\n            clientOrFamilyId,\r\n            realm || Constants.EMPTY_STRING,\r\n        ];\r\n\r\n        return credentialId.join(Separators.CACHE_KEY_SEPARATOR).toLowerCase();\r\n    }\r\n\r\n    /**\r\n     * Generate target key component as per schema: <target>\r\n     */\r\n    private static generateTargetForCacheKey(scopes?: string): string {\r\n        return (scopes || Constants.EMPTY_STRING).toLowerCase();\r\n    }\r\n\r\n    /**\r\n     * Generate requested claims key component as per schema: <requestedClaims>\r\n     */\r\n    private static generateClaimsHashForCacheKey(requestedClaimsHash?: string): string {\r\n        return(requestedClaimsHash || Constants.EMPTY_STRING).toLowerCase();\r\n    }\r\n\r\n    /**\r\n     * Generate scheme key componenet as per schema: <scheme>\r\n     */\r\n    private static generateSchemeForCacheKey(tokenType?: string): string {\r\n        /*\r\n         * PoP Tokens and SSH certs include scheme in cache key\r\n         * Cast to lowercase to handle \"bearer\" from ADFS\r\n         */\r\n        return (tokenType && tokenType.toLowerCase() !== AuthenticationScheme.BEARER.toLowerCase()) ? tokenType.toLowerCase() : Constants.EMPTY_STRING;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAKH;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,IAAA,gBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,gBAAA,GAAA;KAoKC;AAtJG;;AAEG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;AACI,QAAA,OAAO,gBAAgB,CAAC,4BAA4B,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;KAC9F,CAAA;AAED;;AAEG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;QACI,OAAO,gBAAgB,CAAC,+BAA+B,CACnD,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,CAChB,CAAC;KACL,CAAA;AAED;;AAEG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;QACI,OAAO,gBAAgB,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAClE,CAAA;AAED;;AAEG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,qBAAqB,GAArB,YAAA;AACI,QAAA,OAAO,gBAAgB,CAAC,0BAA0B,CAC9C,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,mBAAmB,CAC3B,CAAC;KACL,CAAA;AAED;;AAEG;AACH,IAAA,gBAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;QACI,QAAQ,IAAI,CAAC,cAAc;YACvB,KAAK,cAAc,CAAC,QAAQ;gBACxB,OAAO,SAAS,CAAC,QAAQ,CAAC;YAC9B,KAAK,cAAc,CAAC,YAAY,CAAC;YACjC,KAAK,cAAc,CAAC,6BAA6B;gBAC7C,OAAO,SAAS,CAAC,YAAY,CAAC;YAClC,KAAK,cAAc,CAAC,aAAa;gBAC7B,OAAO,SAAS,CAAC,aAAa,CAAC;AACnC,YAAA,SAAS;AACL,gBAAA,MAAM,eAAe,CAAC,mCAAmC,EAAE,CAAC;AAC/D,aAAA;AACJ,SAAA;KACJ,CAAA;AAED;;;AAGG;AACI,IAAA,gBAAA,CAAA,0BAA0B,GAAjC,UACI,aAAqB,EACrB,WAAmB,EACnB,cAA8B,EAC9B,QAAgB,EAChB,KAAc,EACd,MAAe,EACf,QAAiB,EACjB,SAAgC,EAChC,mBAA4B,EAAA;AAE5B,QAAA,IAAM,aAAa,GAAG;AAClB,YAAA,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,WAAW,CAAC;YAC7D,IAAI,CAAC,+BAA+B,CAAC,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC;AAC/E,YAAA,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;AACtC,YAAA,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,CAAC;AACvD,YAAA,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC;SAC5C,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;KAC3E,CAAA;AAED;;;;AAIG;AACY,IAAA,gBAAA,CAAA,4BAA4B,GAA3C,UACI,aAAqB,EACrB,WAAmB,EAAA;AAEnB,QAAA,IAAM,SAAS,GAAkB,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAC9D,OAAO,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;KACvE,CAAA;AAED;;;;;;AAMG;IACY,gBAA+B,CAAA,+BAAA,GAA9C,UACI,cAA8B,EAC9B,QAAgB,EAChB,KAAc,EACd,QAAiB,EAAA;AAEjB,QAAA,IAAM,gBAAgB,GAClB,cAAc,KAAK,cAAc,CAAC,aAAa;cACzC,QAAQ,IAAI,QAAQ;cACpB,QAAQ,CAAC;AACnB,QAAA,IAAM,YAAY,GAAkB;YAChC,cAAc;YACd,gBAAgB;YAChB,KAAK,IAAI,SAAS,CAAC,YAAY;SAClC,CAAC;QAEF,OAAO,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;KAC1E,CAAA;AAED;;AAEG;IACY,gBAAyB,CAAA,yBAAA,GAAxC,UAAyC,MAAe,EAAA;QACpD,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;KAC3D,CAAA;AAED;;AAEG;IACY,gBAA6B,CAAA,6BAAA,GAA5C,UAA6C,mBAA4B,EAAA;QACrE,OAAM,CAAC,mBAAmB,IAAI,SAAS,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC;KACvE,CAAA;AAED;;AAEG;IACY,gBAAyB,CAAA,yBAAA,GAAxC,UAAyC,SAAkB,EAAA;AACvD;;;AAGG;AACH,QAAA,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,WAAW,EAAE,KAAK,oBAAoB,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,SAAS,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,YAAY,CAAC;KAClJ,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}