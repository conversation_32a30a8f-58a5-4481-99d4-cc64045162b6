{"version": 3, "file": "RefreshTokenEntity.js", "sources": ["../../../src/cache/entities/RefreshTokenEntity.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { CredentialEntity } from \"./CredentialEntity\";\r\nimport { CredentialType } from \"../../utils/Constants\";\r\n\r\n/**\r\n * REFRESH_TOKEN Cache\r\n *\r\n * Key:Value Schema:\r\n *\r\n * Key Example: uid.utid-login.microsoftonline.com-refreshtoken-clientId--\r\n *\r\n * Value:\r\n * {\r\n *      homeAccountId: home account identifier for the auth scheme,\r\n *      environment: entity that issued the token, represented as a full host\r\n *      credentialType: Type of credential as a string, can be one of the following: RefreshToken, AccessToken, IdToken, Password, Cookie, Certificate, Other\r\n *      clientId: client ID of the application\r\n *      secret: Actual credential as a string\r\n *      familyId: Family ID identifier, '1' represents Microsoft Family\r\n *      realm: Full tenant or organizational identifier that the account belongs to\r\n *      target: Permissions that are included in the token, or for refresh tokens, the resource identifier.\r\n * }\r\n */\r\nexport class RefreshTokenEntity extends CredentialEntity {\r\n    familyId?: string;\r\n\r\n    /**\r\n     * Create RefreshTokenEntity\r\n     * @param homeAccountId\r\n     * @param authenticationResult\r\n     * @param clientId\r\n     * @param authority\r\n     */\r\n    static createRefreshTokenEntity(\r\n        homeAccountId: string,\r\n        environment: string,\r\n        refreshToken: string,\r\n        clientId: string,\r\n        familyId?: string,\r\n        userAssertionHash?: string\r\n    ): RefreshTokenEntity {\r\n        const rtEntity = new RefreshTokenEntity();\r\n\r\n        rtEntity.clientId = clientId;\r\n        rtEntity.credentialType = CredentialType.REFRESH_TOKEN;\r\n        rtEntity.environment = environment;\r\n        rtEntity.homeAccountId = homeAccountId;\r\n        rtEntity.secret = refreshToken;\r\n        rtEntity.userAssertionHash = userAssertionHash;\r\n\r\n        if (familyId)\r\n            rtEntity.familyId = familyId;\r\n\r\n        return rtEntity;\r\n    }\r\n\r\n    /**\r\n     * Validates an entity: checks for all expected params\r\n     * @param entity\r\n     */\r\n    static isRefreshTokenEntity(entity: object): boolean {\r\n\r\n        if (!entity) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            entity.hasOwnProperty(\"homeAccountId\") &&\r\n            entity.hasOwnProperty(\"environment\") &&\r\n            entity.hasOwnProperty(\"credentialType\") &&\r\n            entity.hasOwnProperty(\"clientId\") &&\r\n            entity.hasOwnProperty(\"secret\") &&\r\n            entity[\"credentialType\"] === CredentialType.REFRESH_TOKEN\r\n        );\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAKH;;;;;;;;;;;;;;;;;;AAkBG;AACH,IAAA,kBAAA,kBAAA,UAAA,MAAA,EAAA;IAAwC,SAAgB,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;AAAxD,IAAA,SAAA,kBAAA,GAAA;;KAoDC;AAjDG;;;;;;AAMG;AACI,IAAA,kBAAA,CAAA,wBAAwB,GAA/B,UACI,aAAqB,EACrB,WAAmB,EACnB,YAAoB,EACpB,QAAgB,EAChB,QAAiB,EACjB,iBAA0B,EAAA;AAE1B,QAAA,IAAM,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;AAE1C,QAAA,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,QAAA,QAAQ,CAAC,cAAc,GAAG,cAAc,CAAC,aAAa,CAAC;AACvD,QAAA,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,QAAA,QAAQ,CAAC,aAAa,GAAG,aAAa,CAAC;AACvC,QAAA,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC;AAC/B,QAAA,QAAQ,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAE/C,QAAA,IAAI,QAAQ;AACR,YAAA,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAEjC,QAAA,OAAO,QAAQ,CAAC;KACnB,CAAA;AAED;;;AAGG;IACI,kBAAoB,CAAA,oBAAA,GAA3B,UAA4B,MAAc,EAAA;QAEtC,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,QACI,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC;AACtC,YAAA,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC;AACpC,YAAA,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACvC,YAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AACjC,YAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC/B,MAAM,CAAC,gBAAgB,CAAC,KAAK,cAAc,CAAC,aAAa,EAC3D;KACL,CAAA;IACL,OAAC,kBAAA,CAAA;AAAD,CApDA,CAAwC,gBAAgB,CAoDvD;;;;"}