{"version": 3, "file": "TokenCacheContext.js", "sources": ["../../../src/cache/persistence/TokenCacheContext.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ISerializableTokenCache } from \"../interface/ISerializableTokenCache\";\r\n\r\n/**\r\n * This class instance helps track the memory changes facilitating\r\n * decisions to read from and write to the persistent cache\r\n */export class TokenCacheContext {\r\n    /**\r\n     * boolean indicating cache change\r\n     */\r\n    hasChanged: boolean;\r\n    /**\r\n     * serializable token cache interface\r\n     */\r\n    cache: ISerializableTokenCache;\r\n\r\n    constructor(tokenCache: ISerializableTokenCache, hasChanged: boolean) {\r\n        this.cache = tokenCache;\r\n        this.hasChanged = hasChanged;\r\n    }\r\n\r\n    /**\r\n     * boolean which indicates the changes in cache\r\n     */\r\n    get cacheHasChanged(): boolean {\r\n        return this.hasChanged;\r\n    }\r\n\r\n    /**\r\n     * function to retrieve the token cache\r\n     */\r\n    get tokenCache(): ISerializableTokenCache {\r\n        return this.cache;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAIH;;;IAGG,IAAA,iBAAA,kBAAA,YAAA;IAUC,SAAY,iBAAA,CAAA,UAAmC,EAAE,UAAmB,EAAA;AAChE,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;AACxB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;KAChC;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,iBAAe,CAAA,SAAA,EAAA,iBAAA,EAAA;AAHnB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;YACI,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,iBAAU,CAAA,SAAA,EAAA,YAAA,EAAA;AAHd;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;YACI,OAAO,IAAI,CAAC,KAAK,CAAC;SACrB;;;AAAA,KAAA,CAAA,CAAA;IACL,OAAC,iBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}