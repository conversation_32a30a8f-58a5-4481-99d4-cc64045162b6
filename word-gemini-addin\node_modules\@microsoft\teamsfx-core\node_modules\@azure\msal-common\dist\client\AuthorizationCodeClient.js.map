{"version": 3, "file": "AuthorizationCodeClient.js", "sources": ["../../src/client/AuthorizationCodeClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { BaseClient } from \"./BaseClient\";\r\nimport { CommonAuthorizationUrlRequest } from \"../request/CommonAuthorizationUrlRequest\";\r\nimport { CommonAuthorizationCodeRequest } from \"../request/CommonAuthorizationCodeRequest\";\r\nimport { Authority } from \"../authority/Authority\";\r\nimport { RequestParameterBuilder } from \"../request/RequestParameterBuilder\";\r\nimport { GrantType, AuthenticationScheme, PromptValue, Separators, AADServerParamKeys, HeaderNames } from \"../utils/Constants\";\r\nimport { ClientConfiguration } from \"../config/ClientConfiguration\";\r\nimport { ServerAuthorizationTokenResponse } from \"../response/ServerAuthorizationTokenResponse\";\r\nimport { NetworkResponse } from \"../network/NetworkManager\";\r\nimport { ResponseHandler } from \"../response/ResponseHandler\";\r\nimport { AuthenticationResult } from \"../response/AuthenticationResult\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { UrlString } from \"../url/UrlString\";\r\nimport { ServerAuthorizationCodeResponse } from \"../response/ServerAuthorizationCodeResponse\";\r\nimport { CommonEndSessionRequest } from \"../request/CommonEndSessionRequest\";\r\nimport { PopTokenGenerator } from \"../crypto/PopTokenGenerator\";\r\nimport { RequestThumbprint } from \"../network/RequestThumbprint\";\r\nimport { AuthorizationCodePayload } from \"../response/AuthorizationCodePayload\";\r\nimport { TimeUtils } from \"../utils/TimeUtils\";\r\nimport { AccountInfo } from \"../account/AccountInfo\";\r\nimport { buildClientInfoFromHomeAccountId, buildClientInfo } from \"../account/ClientInfo\";\r\nimport { CcsCredentialType, CcsCredential } from \"../account/CcsCredential\";\r\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\r\nimport { RequestValidator } from \"../request/RequestValidator\";\r\nimport { IPerformanceClient } from \"../telemetry/performance/IPerformanceClient\";\r\nimport { PerformanceEvents } from \"../telemetry/performance/PerformanceEvent\";\r\n\r\n/**\r\n * Oauth2.0 Authorization Code client\r\n */\r\nexport class AuthorizationCodeClient extends BaseClient {\r\n    // Flag to indicate if client is for hybrid spa auth code redemption\r\n    protected includeRedirectUri: boolean = true;\r\n\r\n    constructor(configuration: ClientConfiguration, performanceClient?: IPerformanceClient) {\r\n        super(configuration, performanceClient);\r\n    }\r\n\r\n    /**\r\n     * Creates the URL of the authorization request letting the user input credentials and consent to the\r\n     * application. The URL target the /authorize endpoint of the authority configured in the\r\n     * application object.\r\n     *\r\n     * Once the user inputs their credentials and consents, the authority will send a response to the redirect URI\r\n     * sent in the request and should contain an authorization code, which can then be used to acquire tokens via\r\n     * acquireToken(AuthorizationCodeRequest)\r\n     * @param request\r\n     */\r\n    async getAuthCodeUrl(request: CommonAuthorizationUrlRequest): Promise<string> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.GetAuthCodeUrl, request.correlationId);\r\n\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthClientCreateQueryString, request.correlationId);\r\n        const queryString = await this.createAuthCodeUrlQueryString(request);\r\n\r\n        return UrlString.appendQueryString(this.authority.authorizationEndpoint, queryString);\r\n    }\r\n\r\n    /**\r\n     * API to acquire a token in exchange of 'authorization_code` acquired by the user in the first leg of the\r\n     * authorization_code_grant\r\n     * @param request\r\n     */\r\n    async acquireToken(request: CommonAuthorizationCodeRequest, authCodePayload?: AuthorizationCodePayload): Promise<AuthenticationResult> {\r\n        if (!request || !request.code) {\r\n            throw ClientAuthError.createTokenRequestCannotBeMadeError();\r\n        }\r\n\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthClientAcquireToken, request.correlationId);\r\n        \r\n        // @ts-ignore\r\n        const atsMeasurement = this.performanceClient?.startMeasurement(\"AuthCodeClientAcquireToken\", request.correlationId);\r\n        this.logger.info(\"in acquireToken call in auth-code client\");\r\n\r\n        const reqTimestamp = TimeUtils.nowSeconds();\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthClientExecuteTokenRequest, request.correlationId);\r\n        const response = await this.executeTokenRequest(this.authority, request);\r\n\r\n        // Retrieve requestId from response headers\r\n        const requestId = response.headers?.[HeaderNames.X_MS_REQUEST_ID];\r\n        const httpVerAuthority = response.headers?.[HeaderNames.X_MS_HTTP_VERSION];\r\n        if(httpVerAuthority)\r\n        {\r\n            atsMeasurement?.addStaticFields({\r\n                httpVerAuthority\r\n            });\r\n        }\r\n        const responseHandler = new ResponseHandler(\r\n            this.config.authOptions.clientId,\r\n            this.cacheManager,\r\n            this.cryptoUtils,\r\n            this.logger,\r\n            this.config.serializableCache,\r\n            this.config.persistencePlugin,\r\n            this.performanceClient\r\n        );\r\n\r\n        // Validate response. This function throws a server error if an error is returned by the server.\r\n        responseHandler.validateTokenResponse(response.body);\r\n\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.HandleServerTokenResponse, request.correlationId);\r\n        return responseHandler.handleServerTokenResponse(\r\n            response.body,\r\n            this.authority,\r\n            reqTimestamp,\r\n            request,\r\n            authCodePayload,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            requestId\r\n        ).then((result: AuthenticationResult) => {\r\n            atsMeasurement?.endMeasurement({\r\n                success: true\r\n            });\r\n            return result;\r\n        })\r\n            .catch((error) => {\r\n                this.logger.verbose(\"Error in fetching token in ACC\", request.correlationId);\r\n                atsMeasurement?.endMeasurement({\r\n                    errorCode: error.errorCode,\r\n                    subErrorCode: error.subError,\r\n                    success: false\r\n                });\r\n                throw error;\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Handles the hash fragment response from public client code request. Returns a code response used by\r\n     * the client to exchange for a token in acquireToken.\r\n     * @param hashFragment\r\n     */\r\n    handleFragmentResponse(hashFragment: string, cachedState: string): AuthorizationCodePayload {\r\n        // Handle responses.\r\n        const responseHandler = new ResponseHandler(this.config.authOptions.clientId, this.cacheManager, this.cryptoUtils, this.logger, null, null);\r\n\r\n        // Deserialize hash fragment response parameters.\r\n        const hashUrlString = new UrlString(hashFragment);\r\n        // Deserialize hash fragment response parameters.\r\n        const serverParams: ServerAuthorizationCodeResponse = UrlString.getDeserializedHash(hashUrlString.getHash());\r\n\r\n        // Get code response\r\n        responseHandler.validateServerAuthorizationCodeResponse(serverParams, cachedState, this.cryptoUtils);\r\n\r\n        // throw when there is no auth code in the response\r\n        if (!serverParams.code) {\r\n            throw ClientAuthError.createNoAuthCodeInServerResponseError();\r\n        }\r\n        return {\r\n            ...serverParams,\r\n            // Code param is optional in ServerAuthorizationCodeResponse but required in AuthorizationCodePaylod\r\n            code: serverParams.code\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Used to log out the current user, and redirect the user to the postLogoutRedirectUri.\r\n     * Default behaviour is to redirect the user to `window.location.href`.\r\n     * @param authorityUri\r\n     */\r\n    getLogoutUri(logoutRequest: CommonEndSessionRequest): string {\r\n        // Throw error if logoutRequest is null/undefined\r\n        if (!logoutRequest) {\r\n            throw ClientConfigurationError.createEmptyLogoutRequestError();\r\n        }\r\n        const queryString = this.createLogoutUrlQueryString(logoutRequest);\r\n\r\n        // Construct logout URI\r\n        return UrlString.appendQueryString(this.authority.endSessionEndpoint, queryString);\r\n    }\r\n\r\n    /**\r\n     * Executes POST request to token endpoint\r\n     * @param authority\r\n     * @param request\r\n     */\r\n    private async executeTokenRequest(authority: Authority, request: CommonAuthorizationCodeRequest): Promise<NetworkResponse<ServerAuthorizationTokenResponse>> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthClientExecuteTokenRequest, request.correlationId);\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.AuthClientCreateTokenRequestBody, request.correlationId);\r\n        \r\n        const queryParametersString = this.createTokenQueryParameters(request);\r\n        const endpoint = UrlString.appendQueryString(authority.tokenEndpoint, queryParametersString);\r\n        \r\n        const requestBody = await this.createTokenRequestBody(request);\r\n        \r\n        let ccsCredential: CcsCredential | undefined = undefined;\r\n        if (request.clientInfo) {\r\n            try {\r\n                const clientInfo = buildClientInfo(request.clientInfo, this.cryptoUtils);\r\n                ccsCredential = {\r\n                    credential: `${clientInfo.uid}${Separators.CLIENT_INFO_SEPARATOR}${clientInfo.utid}`,\r\n                    type: CcsCredentialType.HOME_ACCOUNT_ID\r\n                };\r\n            } catch (e) {\r\n                this.logger.verbose(\"Could not parse client info for CCS Header: \" + e);\r\n            }\r\n        }\r\n        const headers: Record<string, string> = this.createTokenRequestHeaders(ccsCredential || request.ccsCredential);\r\n\r\n        const thumbprint: RequestThumbprint = {\r\n            clientId: this.config.authOptions.clientId,\r\n            authority: authority.canonicalAuthority,\r\n            scopes: request.scopes,\r\n            claims: request.claims,\r\n            authenticationScheme: request.authenticationScheme,\r\n            resourceRequestMethod: request.resourceRequestMethod,\r\n            resourceRequestUri: request.resourceRequestUri,\r\n            shrClaims: request.shrClaims,\r\n            sshKid: request.sshKid\r\n        };\r\n\r\n        return this.executePostToTokenEndpoint(endpoint, requestBody, headers, thumbprint);\r\n    }\r\n\r\n    /**\r\n     * Generates a map for all the params to be sent to the service\r\n     * @param request\r\n     */\r\n    private async createTokenRequestBody(request: CommonAuthorizationCodeRequest): Promise<string> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthClientCreateTokenRequestBody, request.correlationId);\r\n\r\n        const parameterBuilder = new RequestParameterBuilder();\r\n\r\n        parameterBuilder.addClientId(this.config.authOptions.clientId);\r\n\r\n        /*\r\n         * For hybrid spa flow, there will be a code but no verifier\r\n         * In this scenario, don't include redirect uri as auth code will not be bound to redirect URI\r\n         */\r\n        if (!this.includeRedirectUri) {\r\n            // Just validate\r\n            RequestValidator.validateRedirectUri(request.redirectUri);\r\n        } else {\r\n            // Validate and include redirect uri\r\n            parameterBuilder.addRedirectUri(request.redirectUri);\r\n        }\r\n\r\n        // Add scope array, parameter builder will add default scopes and dedupe\r\n        parameterBuilder.addScopes(request.scopes);\r\n\r\n        // add code: user set, not validated\r\n        parameterBuilder.addAuthorizationCode(request.code);\r\n\r\n        // Add library metadata\r\n        parameterBuilder.addLibraryInfo(this.config.libraryInfo);\r\n        parameterBuilder.addApplicationTelemetry(this.config.telemetry.application);\r\n        parameterBuilder.addThrottling();\r\n\r\n        if (this.serverTelemetryManager) {\r\n            parameterBuilder.addServerTelemetry(this.serverTelemetryManager);\r\n        }\r\n\r\n        // add code_verifier if passed\r\n        if (request.codeVerifier) {\r\n            parameterBuilder.addCodeVerifier(request.codeVerifier);\r\n        }\r\n\r\n        if (this.config.clientCredentials.clientSecret) {\r\n            parameterBuilder.addClientSecret(this.config.clientCredentials.clientSecret);\r\n        }\r\n\r\n        if (this.config.clientCredentials.clientAssertion) {\r\n            const clientAssertion = this.config.clientCredentials.clientAssertion;\r\n            parameterBuilder.addClientAssertion(clientAssertion.assertion);\r\n            parameterBuilder.addClientAssertionType(clientAssertion.assertionType);\r\n        }\r\n\r\n        parameterBuilder.addGrantType(GrantType.AUTHORIZATION_CODE_GRANT);\r\n        parameterBuilder.addClientInfo();\r\n\r\n        if (request.authenticationScheme === AuthenticationScheme.POP) {\r\n            const popTokenGenerator = new PopTokenGenerator(this.cryptoUtils, this.performanceClient);\r\n\r\n            this.performanceClient?.setPreQueueTime(PerformanceEvents.PopTokenGenerateCnf, request.correlationId);\r\n            const reqCnfData = await popTokenGenerator.generateCnf(request);\r\n            // SPA PoP requires full Base64Url encoded req_cnf string (unhashed)\r\n            parameterBuilder.addPopToken(reqCnfData.reqCnfString);\r\n        } else if (request.authenticationScheme === AuthenticationScheme.SSH) {\r\n            if (request.sshJwk) {\r\n                parameterBuilder.addSshJwk(request.sshJwk);\r\n            } else {\r\n                throw ClientConfigurationError.createMissingSshJwkError();\r\n            }\r\n        }\r\n\r\n        const correlationId = request.correlationId || this.config.cryptoInterface.createNewGuid();\r\n        parameterBuilder.addCorrelationId(correlationId);\r\n\r\n        if (!StringUtils.isEmptyObj(request.claims) || this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0) {\r\n            parameterBuilder.addClaims(request.claims, this.config.authOptions.clientCapabilities);\r\n        }\r\n\r\n        let ccsCred: CcsCredential | undefined = undefined;\r\n        if (request.clientInfo) {\r\n            try {\r\n                const clientInfo = buildClientInfo(request.clientInfo, this.cryptoUtils);\r\n                ccsCred = {\r\n                    credential: `${clientInfo.uid}${Separators.CLIENT_INFO_SEPARATOR}${clientInfo.utid}`,\r\n                    type: CcsCredentialType.HOME_ACCOUNT_ID\r\n                };\r\n            } catch (e) {\r\n                this.logger.verbose(\"Could not parse client info for CCS Header: \" + e);\r\n            }\r\n        } else {\r\n            ccsCred = request.ccsCredential;\r\n        }\r\n\r\n        // Adds these as parameters in the request instead of headers to prevent CORS preflight request\r\n        if (this.config.systemOptions.preventCorsPreflight && ccsCred) {\r\n            switch (ccsCred.type) {\r\n                case CcsCredentialType.HOME_ACCOUNT_ID:\r\n                    try {\r\n                        const clientInfo = buildClientInfoFromHomeAccountId(ccsCred.credential);\r\n                        parameterBuilder.addCcsOid(clientInfo);\r\n                    } catch (e) {\r\n                        this.logger.verbose(\"Could not parse home account ID for CCS Header: \" + e);\r\n                    }\r\n                    break;\r\n                case CcsCredentialType.UPN:\r\n                    parameterBuilder.addCcsUpn(ccsCred.credential);\r\n                    break;\r\n            }\r\n        }\r\n\r\n        if (request.tokenBodyParameters) {\r\n            parameterBuilder.addExtraQueryParameters(request.tokenBodyParameters);\r\n        }\r\n\r\n        // Add hybrid spa parameters if not already provided\r\n        if (request.enableSpaAuthorizationCode && (!request.tokenBodyParameters || !request.tokenBodyParameters[AADServerParamKeys.RETURN_SPA_CODE])) {\r\n            parameterBuilder.addExtraQueryParameters({\r\n                [AADServerParamKeys.RETURN_SPA_CODE]: \"1\"\r\n            });\r\n        }\r\n\r\n        return parameterBuilder.createQueryString();\r\n    }\r\n\r\n    /**\r\n     * This API validates the `AuthorizationCodeUrlRequest` and creates a URL\r\n     * @param request\r\n     */\r\n    private async createAuthCodeUrlQueryString(request: CommonAuthorizationUrlRequest): Promise<string> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.AuthClientCreateQueryString, request.correlationId);\r\n\r\n        const parameterBuilder = new RequestParameterBuilder();\r\n\r\n        parameterBuilder.addClientId(this.config.authOptions.clientId);\r\n\r\n        const requestScopes = [...request.scopes || [], ...request.extraScopesToConsent || []];\r\n        parameterBuilder.addScopes(requestScopes);\r\n\r\n        // validate the redirectUri (to be a non null value)\r\n        parameterBuilder.addRedirectUri(request.redirectUri);\r\n\r\n        // generate the correlationId if not set by the user and add\r\n        const correlationId = request.correlationId || this.config.cryptoInterface.createNewGuid();\r\n        parameterBuilder.addCorrelationId(correlationId);\r\n\r\n        // add response_mode. If not passed in it defaults to query.\r\n        parameterBuilder.addResponseMode(request.responseMode);\r\n\r\n        // add response_type = code\r\n        parameterBuilder.addResponseTypeCode();\r\n\r\n        // add library info parameters\r\n        parameterBuilder.addLibraryInfo(this.config.libraryInfo);\r\n        parameterBuilder.addApplicationTelemetry(this.config.telemetry.application);\r\n\r\n        // add client_info=1\r\n        parameterBuilder.addClientInfo();\r\n\r\n        if (request.codeChallenge && request.codeChallengeMethod) {\r\n            parameterBuilder.addCodeChallengeParams(request.codeChallenge, request.codeChallengeMethod);\r\n        }\r\n\r\n        if (request.prompt) {\r\n            parameterBuilder.addPrompt(request.prompt);\r\n        }\r\n\r\n        if (request.domainHint) {\r\n            parameterBuilder.addDomainHint(request.domainHint);\r\n        }\r\n\r\n        // Add sid or loginHint with preference for login_hint claim (in request) -> sid -> loginHint (upn/email) -> username of AccountInfo object\r\n        if (request.prompt !== PromptValue.SELECT_ACCOUNT) {\r\n            // AAD will throw if prompt=select_account is passed with an account hint\r\n            if (request.sid && request.prompt === PromptValue.NONE) {\r\n                // SessionID is only used in silent calls\r\n                this.logger.verbose(\"createAuthCodeUrlQueryString: Prompt is none, adding sid from request\");\r\n                parameterBuilder.addSid(request.sid);\r\n            } else if (request.account) {\r\n                const accountSid = this.extractAccountSid(request.account);\r\n                const accountLoginHintClaim = this.extractLoginHint(request.account);\r\n                // If login_hint claim is present, use it over sid/username\r\n                if (accountLoginHintClaim) {\r\n                    this.logger.verbose(\"createAuthCodeUrlQueryString: login_hint claim present on account\");\r\n                    parameterBuilder.addLoginHint(accountLoginHintClaim);\r\n                    try {\r\n                        const clientInfo = buildClientInfoFromHomeAccountId(request.account.homeAccountId);\r\n                        parameterBuilder.addCcsOid(clientInfo);\r\n                    } catch (e) {\r\n                        this.logger.verbose(\"createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header\");\r\n                    }\r\n                } else if (accountSid && request.prompt === PromptValue.NONE) {\r\n                    /*\r\n                     * If account and loginHint are provided, we will check account first for sid before adding loginHint\r\n                     * SessionId is only used in silent calls\r\n                     */\r\n                    this.logger.verbose(\"createAuthCodeUrlQueryString: Prompt is none, adding sid from account\");\r\n                    parameterBuilder.addSid(accountSid);\r\n                    try {\r\n                        const clientInfo = buildClientInfoFromHomeAccountId(request.account.homeAccountId);\r\n                        parameterBuilder.addCcsOid(clientInfo);\r\n                    } catch (e) {\r\n                        this.logger.verbose(\"createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header\");\r\n                    }\r\n                } else if (request.loginHint) {\r\n                    this.logger.verbose(\"createAuthCodeUrlQueryString: Adding login_hint from request\");\r\n                    parameterBuilder.addLoginHint(request.loginHint);\r\n                    parameterBuilder.addCcsUpn(request.loginHint);\r\n                } else if (request.account.username) {\r\n                    // Fallback to account username if provided\r\n                    this.logger.verbose(\"createAuthCodeUrlQueryString: Adding login_hint from account\");\r\n                    parameterBuilder.addLoginHint(request.account.username);\r\n                    try {\r\n                        const clientInfo = buildClientInfoFromHomeAccountId(request.account.homeAccountId);\r\n                        parameterBuilder.addCcsOid(clientInfo);\r\n                    } catch (e) {\r\n                        this.logger.verbose(\"createAuthCodeUrlQueryString: Could not parse home account ID for CCS Header\");\r\n                    }\r\n                }\r\n            } else if (request.loginHint) {\r\n                this.logger.verbose(\"createAuthCodeUrlQueryString: No account, adding login_hint from request\");\r\n                parameterBuilder.addLoginHint(request.loginHint);\r\n                parameterBuilder.addCcsUpn(request.loginHint);\r\n            }\r\n        } else {\r\n            this.logger.verbose(\"createAuthCodeUrlQueryString: Prompt is select_account, ignoring account hints\");\r\n        }\r\n\r\n        if (request.nonce) {\r\n            parameterBuilder.addNonce(request.nonce);\r\n        }\r\n\r\n        if (request.state) {\r\n            parameterBuilder.addState(request.state);\r\n        }\r\n\r\n        if (!StringUtils.isEmpty(request.claims) || this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0) {\r\n            parameterBuilder.addClaims(request.claims, this.config.authOptions.clientCapabilities);\r\n        }\r\n\r\n        if (request.extraQueryParameters) {\r\n            parameterBuilder.addExtraQueryParameters(request.extraQueryParameters);\r\n        }\r\n\r\n        if (request.nativeBroker) {\r\n            // signal ests that this is a WAM call\r\n            parameterBuilder.addNativeBroker();\r\n\r\n            // pass the req_cnf for POP\r\n            if (request.authenticationScheme === AuthenticationScheme.POP) {\r\n                const popTokenGenerator = new PopTokenGenerator(this.cryptoUtils);\r\n                // to reduce the URL length, it is recommended to send the short form of the req_cnf\r\n                const reqCnfData = await popTokenGenerator.generateCnf(request);\r\n                parameterBuilder.addPopToken(reqCnfData.reqCnfString);\r\n            }\r\n        }\r\n\r\n        return parameterBuilder.createQueryString();\r\n    }\r\n\r\n    /**\r\n     * This API validates the `EndSessionRequest` and creates a URL\r\n     * @param request\r\n     */\r\n    private createLogoutUrlQueryString(request: CommonEndSessionRequest): string {\r\n        const parameterBuilder = new RequestParameterBuilder();\r\n\r\n        if (request.postLogoutRedirectUri) {\r\n            parameterBuilder.addPostLogoutRedirectUri(request.postLogoutRedirectUri);\r\n        }\r\n\r\n        if (request.correlationId) {\r\n            parameterBuilder.addCorrelationId(request.correlationId);\r\n        }\r\n\r\n        if (request.idTokenHint) {\r\n            parameterBuilder.addIdTokenHint(request.idTokenHint);\r\n        }\r\n\r\n        if (request.state) {\r\n            parameterBuilder.addState(request.state);\r\n        }\r\n\r\n        if (request.logoutHint) {\r\n            parameterBuilder.addLogoutHint(request.logoutHint);\r\n        }\r\n\r\n        if (request.extraQueryParameters) {\r\n            parameterBuilder.addExtraQueryParameters(request.extraQueryParameters);\r\n        }\r\n\r\n        return parameterBuilder.createQueryString();\r\n    }\r\n\r\n    /**\r\n     * Helper to get sid from account. Returns null if idTokenClaims are not present or sid is not present.\r\n     * @param account\r\n     */\r\n    private extractAccountSid(account: AccountInfo): string | null {\r\n        return account.idTokenClaims?.sid || null;\r\n    }\r\n\r\n    private extractLoginHint(account: AccountInfo): string | null {\r\n        return account.idTokenClaims?.login_hint || null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AA8BH;;AAEG;AACH,IAAA,uBAAA,kBAAA,UAAA,MAAA,EAAA;IAA6C,SAAU,CAAA,uBAAA,EAAA,MAAA,CAAA,CAAA;IAInD,SAAY,uBAAA,CAAA,aAAkC,EAAE,iBAAsC,EAAA;AAAtF,QAAA,IAAA,KAAA,GACI,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,aAAa,EAAE,iBAAiB,CAAC,IAC1C,IAAA,CAAA;;QAJS,KAAkB,CAAA,kBAAA,GAAY,IAAI,CAAC;;KAI5C;AAED;;;;;;;;;AASG;IACG,uBAAc,CAAA,SAAA,CAAA,cAAA,GAApB,UAAqB,OAAsC,EAAA;;;;;;;AACvD,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,cAAc,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAErG,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,2BAA2B,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAC1F,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAA,CAAA;;AAA9D,wBAAA,WAAW,GAAG,EAAgD,CAAA,IAAA,EAAA,CAAA;AAEpE,wBAAA,OAAA,CAAA,CAAA,aAAO,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC,CAAA;;;;AACzF,KAAA,CAAA;AAED;;;;AAIG;AACG,IAAA,uBAAA,CAAA,SAAA,CAAA,YAAY,GAAlB,UAAmB,OAAuC,EAAE,eAA0C,EAAA;;;;;;;;AAClG,wBAAA,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AAC3B,4BAAA,MAAM,eAAe,CAAC,mCAAmC,EAAE,CAAC;AAC/D,yBAAA;AAED,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAGvG,wBAAA,cAAc,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAgB,CAAC,4BAA4B,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACrH,wBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;AAEvD,wBAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AAC5C,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,6BAA6B,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;wBAC/F,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA,CAAA;;AAAlE,wBAAA,QAAQ,GAAG,EAAuD,CAAA,IAAA,EAAA,CAAA;wBAGlE,SAAS,GAAA,CAAA,EAAA,GAAG,QAAQ,CAAC,OAAO,0CAAG,WAAW,CAAC,eAAe,CAAC,CAAC;wBAC5D,gBAAgB,GAAA,CAAA,EAAA,GAAG,QAAQ,CAAC,OAAO,0CAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC;AAC3E,wBAAA,IAAG,gBAAgB,EACnB;AACI,4BAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,eAAe,CAAC;AAC5B,gCAAA,gBAAgB,EAAA,gBAAA;6BACnB,CAAE,CAAA;AACN,yBAAA;AACK,wBAAA,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,iBAAiB,CACzB,CAAC;;AAGF,wBAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAErD,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,yBAAyB,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAC5G,wBAAA,OAAA,CAAA,CAAA,aAAO,eAAe,CAAC,yBAAyB,CAC5C,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,EACP,eAAe,EACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACZ,CAAC,IAAI,CAAC,UAAC,MAA4B,EAAA;AAChC,gCAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,cAAc,CAAC;AAC3B,oCAAA,OAAO,EAAE,IAAI;iCAChB,CAAE,CAAA;AACH,gCAAA,OAAO,MAAM,CAAC;AAClB,6BAAC,CAAC;iCACG,KAAK,CAAC,UAAC,KAAK,EAAA;gCACT,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gCAAgC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC7E,gCAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,cAAc,CAAC;oCAC3B,SAAS,EAAE,KAAK,CAAC,SAAS;oCAC1B,YAAY,EAAE,KAAK,CAAC,QAAQ;AAC5B,oCAAA,OAAO,EAAE,KAAK;iCACjB,CAAE,CAAA;AACH,gCAAA,MAAM,KAAK,CAAC;AAChB,6BAAC,CAAC,CAAC,CAAA;;;;AACV,KAAA,CAAA;AAED;;;;AAIG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtB,UAAuB,YAAoB,EAAE,WAAmB,EAAA;;AAE5D,QAAA,IAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;;AAG5I,QAAA,IAAM,aAAa,GAAG,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;;QAElD,IAAM,YAAY,GAAoC,SAAS,CAAC,mBAAmB,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;;QAG7G,eAAe,CAAC,uCAAuC,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;;AAGrG,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AACpB,YAAA,MAAM,eAAe,CAAC,qCAAqC,EAAE,CAAC;AACjE,SAAA;AACD,QAAA,OAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EACO,YAAY,CAAA,EAAA;;AAEf,YAAA,IAAI,EAAE,YAAY,CAAC,IAAI,EACzB,CAAA,CAAA;KACL,CAAA;AAED;;;;AAIG;IACH,uBAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,aAAsC,EAAA;;QAE/C,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,MAAM,wBAAwB,CAAC,6BAA6B,EAAE,CAAC;AAClE,SAAA;QACD,IAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC;;AAGnE,QAAA,OAAO,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;KACtF,CAAA;AAED;;;;AAIG;AACW,IAAA,uBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAjC,UAAkC,SAAoB,EAAE,OAAuC,EAAA;;;;;;;AAC3F,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,6BAA6B,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AACpH,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAE7G,wBAAA,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;wBACjE,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;AAEzE,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAA,CAAA;;AAAxD,wBAAA,WAAW,GAAG,EAA0C,CAAA,IAAA,EAAA,CAAA;wBAE1D,aAAa,GAA8B,SAAS,CAAC;wBACzD,IAAI,OAAO,CAAC,UAAU,EAAE;4BACpB,IAAI;gCACM,UAAU,GAAG,eAAe,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACzE,gCAAA,aAAa,GAAG;AACZ,oCAAA,UAAU,EAAE,EAAA,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,qBAAqB,GAAG,UAAU,CAAC,IAAM;oCACpF,IAAI,EAAE,iBAAiB,CAAC,eAAe;iCAC1C,CAAC;AACL,6BAAA;AAAC,4BAAA,OAAO,CAAC,EAAE;gCACR,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8CAA8C,GAAG,CAAC,CAAC,CAAC;AAC3E,6BAAA;AACJ,yBAAA;wBACK,OAAO,GAA2B,IAAI,CAAC,yBAAyB,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,CAAC;AAEzG,wBAAA,UAAU,GAAsB;AAClC,4BAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;4BAC1C,SAAS,EAAE,SAAS,CAAC,kBAAkB;4BACvC,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;4BAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;4BACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;4BAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;yBACzB,CAAC;AAEF,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAA;;;;AACtF,KAAA,CAAA;AAED;;;AAGG;IACW,uBAAsB,CAAA,SAAA,CAAA,sBAAA,GAApC,UAAqC,OAAuC,EAAA;;;;;;;;AACxE,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAEjH,wBAAA,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;wBAEvD,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAE/D;;;AAGG;AACH,wBAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;;AAE1B,4BAAA,gBAAgB,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC7D,yBAAA;AAAM,6BAAA;;AAEH,4BAAA,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACxD,yBAAA;;AAGD,wBAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;AAG3C,wBAAA,gBAAgB,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;wBAGpD,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;wBACzD,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;wBAC5E,gBAAgB,CAAC,aAAa,EAAE,CAAC;wBAEjC,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC7B,4BAAA,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACpE,yBAAA;;wBAGD,IAAI,OAAO,CAAC,YAAY,EAAE;AACtB,4BAAA,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC1D,yBAAA;AAED,wBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;4BAC5C,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAChF,yBAAA;AAED,wBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,EAAE;4BACzC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;AACtE,4BAAA,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AAC/D,4BAAA,gBAAgB,CAAC,sBAAsB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC1E,yBAAA;AAED,wBAAA,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;wBAClE,gBAAgB,CAAC,aAAa,EAAE,CAAC;8BAE7B,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,CAAA,EAAzD,OAAyD,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACnD,wBAAA,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAE1F,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AACnF,wBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA,CAAA;;AAAzD,wBAAA,UAAU,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;;AAE/D,wBAAA,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;;;AACnD,wBAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;4BAClE,IAAI,OAAO,CAAC,MAAM,EAAE;AAChB,gCAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9C,6BAAA;AAAM,iCAAA;AACH,gCAAA,MAAM,wBAAwB,CAAC,wBAAwB,EAAE,CAAC;AAC7D,6BAAA;AACJ,yBAAA;;;AAEK,wBAAA,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;AAC3F,wBAAA,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAEjD,wBAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;AAChJ,4BAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAC1F,yBAAA;wBAEG,OAAO,GAA8B,SAAS,CAAC;wBACnD,IAAI,OAAO,CAAC,UAAU,EAAE;4BACpB,IAAI;gCACM,UAAU,GAAG,eAAe,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACzE,gCAAA,OAAO,GAAG;AACN,oCAAA,UAAU,EAAE,EAAA,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,qBAAqB,GAAG,UAAU,CAAC,IAAM;oCACpF,IAAI,EAAE,iBAAiB,CAAC,eAAe;iCAC1C,CAAC;AACL,6BAAA;AAAC,4BAAA,OAAO,CAAC,EAAE;gCACR,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8CAA8C,GAAG,CAAC,CAAC,CAAC;AAC3E,6BAAA;AACJ,yBAAA;AAAM,6BAAA;AACH,4BAAA,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC;AACnC,yBAAA;;wBAGD,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB,IAAI,OAAO,EAAE;4BAC3D,QAAQ,OAAO,CAAC,IAAI;gCAChB,KAAK,iBAAiB,CAAC,eAAe;oCAClC,IAAI;AACM,wCAAA,UAAU,GAAG,gCAAgC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACxE,wCAAA,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,qCAAA;AAAC,oCAAA,OAAO,CAAC,EAAE;wCACR,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kDAAkD,GAAG,CAAC,CAAC,CAAC;AAC/E,qCAAA;oCACD,MAAM;gCACV,KAAK,iBAAiB,CAAC,GAAG;AACtB,oCAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oCAC/C,MAAM;AACb,6BAAA;AACJ,yBAAA;wBAED,IAAI,OAAO,CAAC,mBAAmB,EAAE;AAC7B,4BAAA,gBAAgB,CAAC,uBAAuB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AACzE,yBAAA;;wBAGD,IAAI,OAAO,CAAC,0BAA0B,KAAK,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,EAAE;AAC1I,4BAAA,gBAAgB,CAAC,uBAAuB,EAAA,EAAA,GAAA,EAAA;AACpC,gCAAA,EAAA,CAAC,kBAAkB,CAAC,eAAe,CAAA,GAAG,GAAG;oCAC3C,CAAC;AACN,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAA;;;;AAC/C,KAAA,CAAA;AAED;;;AAGG;IACW,uBAA4B,CAAA,SAAA,CAAA,4BAAA,GAA1C,UAA2C,OAAsC,EAAA;;;;;;;AAC7E,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,2BAA2B,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAE5G,wBAAA,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;wBAEvD,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAEzD,wBAAA,aAAa,GAAO,cAAA,CAAA,OAAO,CAAC,MAAM,IAAI,EAAE,EAAK,OAAO,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC;AACvF,wBAAA,gBAAgB,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;;AAG1C,wBAAA,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAG/C,wBAAA,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;AAC3F,wBAAA,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;;AAGjD,wBAAA,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;;wBAGvD,gBAAgB,CAAC,mBAAmB,EAAE,CAAC;;wBAGvC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;wBACzD,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;;wBAG5E,gBAAgB,CAAC,aAAa,EAAE,CAAC;AAEjC,wBAAA,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,mBAAmB,EAAE;4BACtD,gBAAgB,CAAC,sBAAsB,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAC/F,yBAAA;wBAED,IAAI,OAAO,CAAC,MAAM,EAAE;AAChB,4BAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9C,yBAAA;wBAED,IAAI,OAAO,CAAC,UAAU,EAAE;AACpB,4BAAA,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACtD,yBAAA;;AAGD,wBAAA,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,cAAc,EAAE;;4BAE/C,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE;;AAEpD,gCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uEAAuE,CAAC,CAAC;AAC7F,gCAAA,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACxC,6BAAA;iCAAM,IAAI,OAAO,CAAC,OAAO,EAAE;gCAClB,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gCACrD,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;AAErE,gCAAA,IAAI,qBAAqB,EAAE;AACvB,oCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mEAAmE,CAAC,CAAC;AACzF,oCAAA,gBAAgB,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;oCACrD,IAAI;wCACM,UAAU,GAAG,gCAAgC,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACnF,wCAAA,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,qCAAA;AAAC,oCAAA,OAAO,CAAC,EAAE;AACR,wCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8EAA8E,CAAC,CAAC;AACvG,qCAAA;AACJ,iCAAA;qCAAM,IAAI,UAAU,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE;AAC1D;;;AAGG;AACH,oCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uEAAuE,CAAC,CAAC;AAC7F,oCAAA,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oCACpC,IAAI;wCACM,UAAU,GAAG,gCAAgC,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACnF,wCAAA,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,qCAAA;AAAC,oCAAA,OAAO,CAAC,EAAE;AACR,wCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8EAA8E,CAAC,CAAC;AACvG,qCAAA;AACJ,iCAAA;qCAAM,IAAI,OAAO,CAAC,SAAS,EAAE;AAC1B,oCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8DAA8D,CAAC,CAAC;AACpF,oCAAA,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACjD,oCAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACjD,iCAAA;AAAM,qCAAA,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE;;AAEjC,oCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8DAA8D,CAAC,CAAC;oCACpF,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oCACxD,IAAI;wCACM,UAAU,GAAG,gCAAgC,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACnF,wCAAA,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,qCAAA;AAAC,oCAAA,OAAO,CAAC,EAAE;AACR,wCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8EAA8E,CAAC,CAAC;AACvG,qCAAA;AACJ,iCAAA;AACJ,6BAAA;iCAAM,IAAI,OAAO,CAAC,SAAS,EAAE;AAC1B,gCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0EAA0E,CAAC,CAAC;AAChG,gCAAA,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACjD,gCAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACjD,6BAAA;AACJ,yBAAA;AAAM,6BAAA;AACH,4BAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gFAAgF,CAAC,CAAC;AACzG,yBAAA;wBAED,IAAI,OAAO,CAAC,KAAK,EAAE;AACf,4BAAA,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC5C,yBAAA;wBAED,IAAI,OAAO,CAAC,KAAK,EAAE;AACf,4BAAA,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC5C,yBAAA;AAED,wBAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7I,4BAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAC1F,yBAAA;wBAED,IAAI,OAAO,CAAC,oBAAoB,EAAE;AAC9B,4BAAA,gBAAgB,CAAC,uBAAuB,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC1E,yBAAA;6BAEG,OAAO,CAAC,YAAY,EAApB,OAAoB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;;wBAEpB,gBAAgB,CAAC,eAAe,EAAE,CAAC;8BAG/B,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,CAAA,EAAzD,OAAyD,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;wBACnD,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAE/C,wBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA,CAAA;;AAAzD,wBAAA,UAAU,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;AAC/D,wBAAA,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;;AAI9D,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAA;;;;AAC/C,KAAA,CAAA;AAED;;;AAGG;IACK,uBAA0B,CAAA,SAAA,CAAA,0BAAA,GAAlC,UAAmC,OAAgC,EAAA;AAC/D,QAAA,IAAM,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAEvD,IAAI,OAAO,CAAC,qBAAqB,EAAE;AAC/B,YAAA,gBAAgB,CAAC,wBAAwB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAC5E,SAAA;QAED,IAAI,OAAO,CAAC,aAAa,EAAE;AACvB,YAAA,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AAC5D,SAAA;QAED,IAAI,OAAO,CAAC,WAAW,EAAE;AACrB,YAAA,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACxD,SAAA;QAED,IAAI,OAAO,CAAC,KAAK,EAAE;AACf,YAAA,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC5C,SAAA;QAED,IAAI,OAAO,CAAC,UAAU,EAAE;AACpB,YAAA,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACtD,SAAA;QAED,IAAI,OAAO,CAAC,oBAAoB,EAAE;AAC9B,YAAA,gBAAgB,CAAC,uBAAuB,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC1E,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C,CAAA;AAED;;;AAGG;IACK,uBAAiB,CAAA,SAAA,CAAA,iBAAA,GAAzB,UAA0B,OAAoB,EAAA;;QAC1C,OAAO,CAAA,CAAA,EAAA,GAAA,OAAO,CAAC,aAAa,0CAAE,GAAG,KAAI,IAAI,CAAC;KAC7C,CAAA;IAEO,uBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAxB,UAAyB,OAAoB,EAAA;;QACzC,OAAO,CAAA,CAAA,EAAA,GAAA,OAAO,CAAC,aAAa,0CAAE,UAAU,KAAI,IAAI,CAAC;KACpD,CAAA;IACL,OAAC,uBAAA,CAAA;AAAD,CAxeA,CAA6C,UAAU,CAwetD;;;;"}