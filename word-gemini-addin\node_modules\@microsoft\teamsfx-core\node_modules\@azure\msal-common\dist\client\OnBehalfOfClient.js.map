{"version": 3, "file": "OnBehalfOfClient.js", "sources": ["../../src/client/OnBehalfOfClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ClientConfiguration } from \"../config/ClientConfiguration\";\r\nimport { BaseClient } from \"./BaseClient\";\r\nimport { Authority } from \"../authority/Authority\";\r\nimport { RequestParameterBuilder } from \"../request/RequestParameterBuilder\";\r\nimport { ScopeSet } from \"../request/ScopeSet\";\r\nimport { GrantType, AADServerParamKeys , CredentialType, Constants, CacheOutcome, AuthenticationScheme } from \"../utils/Constants\";\r\nimport { ResponseHandler } from \"../response/ResponseHandler\";\r\nimport { AuthenticationResult } from \"../response/AuthenticationResult\";\r\nimport { CommonOnBehalfOfRequest } from \"../request/CommonOnBehalfOfRequest\";\r\nimport { TimeUtils } from \"../utils/TimeUtils\";\r\nimport { CredentialFilter } from \"../cache/utils/CacheTypes\";\r\nimport { AccessTokenEntity } from \"../cache/entities/AccessTokenEntity\";\r\nimport { IdTokenEntity } from \"../cache/entities/IdTokenEntity\";\r\nimport { AccountEntity } from \"../cache/entities/AccountEntity\";\r\nimport { AuthToken } from \"../account/AuthToken\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { RequestThumbprint } from \"../network/RequestThumbprint\";\r\nimport { AccountInfo } from \"../account/AccountInfo\";\r\nimport { UrlString } from \"../url/UrlString\";\r\n\r\n/**\r\n * On-Behalf-Of client\r\n */\r\nexport class OnBehalfOfClient extends BaseClient {\r\n\r\n    private scopeSet: ScopeSet;\r\n    private userAssertionHash: string;\r\n\r\n    constructor(configuration: ClientConfiguration) {\r\n        super(configuration);\r\n    }\r\n\r\n    /**\r\n     * Public API to acquire tokens with on behalf of flow\r\n     * @param request\r\n     */\r\n    public async acquireToken(request: CommonOnBehalfOfRequest): Promise<AuthenticationResult | null> {\r\n        this.scopeSet = new ScopeSet(request.scopes || []);\r\n\r\n        // generate the user_assertion_hash for OBOAssertion\r\n        this.userAssertionHash = await this.cryptoUtils.hashString(request.oboAssertion);\r\n\r\n        if (request.skipCache) {\r\n            return await this.executeTokenRequest(request, this.authority, this.userAssertionHash);\r\n        }\r\n\r\n        try {\r\n            return await this.getCachedAuthenticationResult(request);\r\n        } catch (e) {\r\n            // Any failure falls back to interactive request, once we implement distributed cache, we plan to handle `createRefreshRequiredError` to refresh using the RT\r\n            return await this.executeTokenRequest(request, this.authority, this.userAssertionHash);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * look up cache for tokens\r\n     * Find idtoken in the cache\r\n     * Find accessToken based on user assertion and account info in the cache\r\n     * Please note we are not yet supported OBO tokens refreshed with long lived RT. User will have to send a new assertion if the current access token expires\r\n     * This is to prevent security issues when the assertion changes over time, however, longlived RT helps retaining the session\r\n     * @param request\r\n     */\r\n    private async getCachedAuthenticationResult(request: CommonOnBehalfOfRequest): Promise<AuthenticationResult | null> {\r\n\r\n        // look in the cache for the access_token which matches the incoming_assertion\r\n        const cachedAccessToken = this.readAccessTokenFromCacheForOBO(this.config.authOptions.clientId, request);\r\n        if (!cachedAccessToken) {\r\n            // Must refresh due to non-existent access_token.\r\n            this.serverTelemetryManager?.setCacheOutcome(CacheOutcome.NO_CACHED_ACCESS_TOKEN);\r\n            this.logger.info(\"SilentFlowClient:acquireCachedToken - No access token found in cache for the given properties.\");\r\n            throw ClientAuthError.createRefreshRequiredError();\r\n        } else if (TimeUtils.isTokenExpired(cachedAccessToken.expiresOn, this.config.systemOptions.tokenRenewalOffsetSeconds)) {\r\n            // Access token expired, will need to renewed\r\n            this.serverTelemetryManager?.setCacheOutcome(CacheOutcome.CACHED_ACCESS_TOKEN_EXPIRED);\r\n            this.logger.info(`OnbehalfofFlow:getCachedAuthenticationResult - Cached access token is expired or will expire within ${this.config.systemOptions.tokenRenewalOffsetSeconds} seconds.`);\r\n            throw ClientAuthError.createRefreshRequiredError();\r\n        }\r\n\r\n        // fetch the idToken from cache\r\n        const cachedIdToken = this.readIdTokenFromCacheForOBO(cachedAccessToken.homeAccountId);\r\n        let idTokenObject: AuthToken | undefined;\r\n        let cachedAccount: AccountEntity | null = null;\r\n        if (cachedIdToken) {\r\n            idTokenObject = new AuthToken(cachedIdToken.secret, this.config.cryptoInterface);\r\n            const localAccountId = idTokenObject.claims.oid ? idTokenObject.claims.oid : idTokenObject.claims.sub;\r\n            const accountInfo: AccountInfo = {\r\n                homeAccountId: cachedIdToken.homeAccountId,\r\n                environment: cachedIdToken.environment,\r\n                tenantId: cachedIdToken.realm,\r\n                username: Constants.EMPTY_STRING,\r\n                localAccountId: localAccountId || Constants.EMPTY_STRING\r\n            };\r\n\r\n            cachedAccount = this.cacheManager.readAccountFromCache(accountInfo);\r\n        }\r\n\r\n        // increment telemetry cache hit counter\r\n        if (this.config.serverTelemetryManager) {\r\n            this.config.serverTelemetryManager.incrementCacheHits();\r\n        }\r\n\r\n        return await ResponseHandler.generateAuthenticationResult(\r\n            this.cryptoUtils,\r\n            this.authority,\r\n            {\r\n                account: cachedAccount,\r\n                accessToken: cachedAccessToken,\r\n                idToken: cachedIdToken,\r\n                refreshToken: null,\r\n                appMetadata: null\r\n            },\r\n            true,\r\n            request,\r\n            idTokenObject);\r\n    }\r\n\r\n    /**\r\n     * read idtoken from cache, this is a specific implementation for OBO as the requirements differ from a generic lookup in the cacheManager\r\n     * Certain use cases of OBO flow do not expect an idToken in the cache/or from the service\r\n     * @param request\r\n     */\r\n    private readIdTokenFromCacheForOBO(atHomeAccountId: string): IdTokenEntity | null {\r\n\r\n        const idTokenFilter: CredentialFilter = {\r\n            homeAccountId: atHomeAccountId,\r\n            environment: this.authority.canonicalAuthorityUrlComponents.HostNameAndPort,\r\n            credentialType: CredentialType.ID_TOKEN,\r\n            clientId: this.config.authOptions.clientId,\r\n            realm: this.authority.tenant\r\n        };\r\n\r\n        const idTokens: IdTokenEntity[] = this.cacheManager.getIdTokensByFilter(idTokenFilter);\r\n\r\n        // When acquiring a token on behalf of an application, there might not be an id token in the cache\r\n        if (idTokens.length < 1) {\r\n            return null;\r\n        }\r\n        return idTokens[0] as IdTokenEntity;\r\n    }\r\n\r\n    /**\r\n     * Fetches the cached access token based on incoming assertion\r\n     * @param clientId\r\n     * @param request\r\n     * @param userAssertionHash\r\n     */\r\n    private readAccessTokenFromCacheForOBO(clientId: string, request: CommonOnBehalfOfRequest) {\r\n        const authScheme = request.authenticationScheme || AuthenticationScheme.BEARER;\r\n        /*\r\n         * Distinguish between Bearer and PoP/SSH token cache types\r\n         * Cast to lowercase to handle \"bearer\" from ADFS\r\n         */\r\n        const credentialType = (authScheme && authScheme.toLowerCase() !== AuthenticationScheme.BEARER.toLowerCase()) ? CredentialType.ACCESS_TOKEN_WITH_AUTH_SCHEME : CredentialType.ACCESS_TOKEN;\r\n\r\n        const accessTokenFilter: CredentialFilter = {\r\n            credentialType: credentialType,\r\n            clientId,\r\n            target: ScopeSet.createSearchScopes(this.scopeSet.asArray()),\r\n            tokenType: authScheme,\r\n            keyId: request.sshKid,\r\n            requestedClaimsHash: request.requestedClaimsHash,\r\n            userAssertionHash: this.userAssertionHash\r\n        };\r\n\r\n        const accessTokens = this.cacheManager.getAccessTokensByFilter(accessTokenFilter);\r\n\r\n        const numAccessTokens = accessTokens.length;\r\n        if (numAccessTokens < 1) {\r\n            return null;\r\n        } else if (numAccessTokens > 1) {\r\n            throw ClientAuthError.createMultipleMatchingTokensInCacheError();\r\n        }\r\n\r\n        return accessTokens[0] as AccessTokenEntity;\r\n    }\r\n\r\n    /**\r\n     * Make a network call to the server requesting credentials\r\n     * @param request\r\n     * @param authority\r\n     */\r\n    private async executeTokenRequest(request: CommonOnBehalfOfRequest, authority: Authority, userAssertionHash: string)\r\n        : Promise<AuthenticationResult | null> {\r\n        const queryParametersString = this.createTokenQueryParameters(request);\r\n        const endpoint = UrlString.appendQueryString(authority.tokenEndpoint, queryParametersString);\r\n        const requestBody = this.createTokenRequestBody(request);\r\n        const headers: Record<string, string> = this.createTokenRequestHeaders();\r\n        const thumbprint: RequestThumbprint = {\r\n            clientId: this.config.authOptions.clientId,\r\n            authority: request.authority,\r\n            scopes: request.scopes,\r\n            claims: request.claims,\r\n            authenticationScheme: request.authenticationScheme,\r\n            resourceRequestMethod: request.resourceRequestMethod,\r\n            resourceRequestUri: request.resourceRequestUri,\r\n            shrClaims: request.shrClaims,\r\n            sshKid: request.sshKid\r\n        };\r\n\r\n        const reqTimestamp = TimeUtils.nowSeconds();\r\n        const response = await this.executePostToTokenEndpoint(endpoint, requestBody, headers, thumbprint);\r\n\r\n        const responseHandler = new ResponseHandler(\r\n            this.config.authOptions.clientId,\r\n            this.cacheManager,\r\n            this.cryptoUtils,\r\n            this.logger,\r\n            this.config.serializableCache,\r\n            this.config.persistencePlugin\r\n        );\r\n\r\n        responseHandler.validateTokenResponse(response.body);\r\n        const tokenResponse = await responseHandler.handleServerTokenResponse(\r\n            response.body,\r\n            this.authority,\r\n            reqTimestamp,\r\n            request,\r\n            undefined,\r\n            userAssertionHash\r\n        );\r\n\r\n        return tokenResponse;\r\n    }\r\n\r\n    /**\r\n     * generate a server request in accepable format\r\n     * @param request\r\n     */\r\n    private createTokenRequestBody(request: CommonOnBehalfOfRequest): string {\r\n        const parameterBuilder = new RequestParameterBuilder();\r\n\r\n        parameterBuilder.addClientId(this.config.authOptions.clientId);\r\n\r\n        parameterBuilder.addScopes(request.scopes);\r\n\r\n        parameterBuilder.addGrantType(GrantType.JWT_BEARER);\r\n\r\n        parameterBuilder.addClientInfo();\r\n\r\n        parameterBuilder.addLibraryInfo(this.config.libraryInfo);\r\n        parameterBuilder.addApplicationTelemetry(this.config.telemetry.application);\r\n        parameterBuilder.addThrottling();\r\n\r\n        if (this.serverTelemetryManager) {\r\n            parameterBuilder.addServerTelemetry(this.serverTelemetryManager);\r\n        }\r\n\r\n        const correlationId = request.correlationId || this.config.cryptoInterface.createNewGuid();\r\n        parameterBuilder.addCorrelationId(correlationId);\r\n\r\n        parameterBuilder.addRequestTokenUse(AADServerParamKeys.ON_BEHALF_OF);\r\n\r\n        parameterBuilder.addOboAssertion(request.oboAssertion);\r\n\r\n        if (this.config.clientCredentials.clientSecret) {\r\n            parameterBuilder.addClientSecret(this.config.clientCredentials.clientSecret);\r\n        }\r\n\r\n        if (this.config.clientCredentials.clientAssertion) {\r\n            const clientAssertion = this.config.clientCredentials.clientAssertion;\r\n            parameterBuilder.addClientAssertion(clientAssertion.assertion);\r\n            parameterBuilder.addClientAssertionType(clientAssertion.assertionType);\r\n        }\r\n\r\n        if (request.claims || (this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0)) {\r\n            parameterBuilder.addClaims(request.claims, this.config.authOptions.clientCapabilities);\r\n        }\r\n       \r\n        return parameterBuilder.createQueryString();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AAsBH;;AAEG;AACH,IAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;IAAsC,SAAU,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;AAK5C,IAAA,SAAA,gBAAA,CAAY,aAAkC,EAAA;AAC1C,QAAA,OAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,aAAa,CAAC,IAAA,IAAA,CAAA;KACvB;AAED;;;AAGG;IACU,gBAAY,CAAA,SAAA,CAAA,YAAA,GAAzB,UAA0B,OAAgC,EAAA;;;;;;AACtD,wBAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;;AAGnD,wBAAA,EAAA,GAAA,IAAI,CAAA;wBAAqB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA,CAAA;;;wBAAhF,EAAK,CAAA,iBAAiB,GAAG,EAAA,CAAA,IAAA,EAAuD,CAAC;6BAE7E,OAAO,CAAC,SAAS,EAAjB,OAAiB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACV,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAA,CAAA;AAAtF,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAA+E,CAAC,CAAA;;;AAIhF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAA,CAAA;AAAxD,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAAiD,CAAC,CAAA;;;AAGlD,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAA,CAAA;;;AAAtF,oBAAA,OAAA,CAAA,CAAA,aAAO,SAA+E,CAAC,CAAA;;;;;AAE9F,KAAA,CAAA;AAED;;;;;;;AAOG;IACW,gBAA6B,CAAA,SAAA,CAAA,6BAAA,GAA3C,UAA4C,OAAgC,EAAA;;;;;;;AAGlE,wBAAA,iBAAiB,GAAG,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;wBACzG,IAAI,CAAC,iBAAiB,EAAE;;4BAEpB,CAAA,EAAA,GAAA,IAAI,CAAC,sBAAsB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,YAAY,CAAC,sBAAsB,CAAE,CAAA;AAClF,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gGAAgG,CAAC,CAAC;AACnH,4BAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AACtD,yBAAA;AAAM,6BAAA,IAAI,SAAS,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,yBAAyB,CAAC,EAAE;;4BAEnH,CAAA,EAAA,GAAA,IAAI,CAAC,sBAAsB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,YAAY,CAAC,2BAA2B,CAAE,CAAA;AACvF,4BAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sGAAuG,GAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,yBAAyB,GAAA,WAAW,CAAC,CAAC;AACxL,4BAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AACtD,yBAAA;wBAGK,aAAa,GAAG,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;wBAEnF,aAAa,GAAyB,IAAI,CAAC;AAC/C,wBAAA,IAAI,aAAa,EAAE;AACf,4BAAA,aAAa,GAAG,IAAI,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;4BAC3E,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC;AAChG,4BAAA,WAAW,GAAgB;gCAC7B,aAAa,EAAE,aAAa,CAAC,aAAa;gCAC1C,WAAW,EAAE,aAAa,CAAC,WAAW;gCACtC,QAAQ,EAAE,aAAa,CAAC,KAAK;gCAC7B,QAAQ,EAAE,SAAS,CAAC,YAAY;AAChC,gCAAA,cAAc,EAAE,cAAc,IAAI,SAAS,CAAC,YAAY;6BAC3D,CAAC;4BAEF,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;AACvE,yBAAA;;AAGD,wBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE;AACpC,4BAAA,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,CAAC;AAC3D,yBAAA;wBAEM,OAAM,CAAA,CAAA,YAAA,eAAe,CAAC,4BAA4B,CACrD,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,EACd;AACI,gCAAA,OAAO,EAAE,aAAa;AACtB,gCAAA,WAAW,EAAE,iBAAiB;AAC9B,gCAAA,OAAO,EAAE,aAAa;AACtB,gCAAA,YAAY,EAAE,IAAI;AAClB,gCAAA,WAAW,EAAE,IAAI;AACpB,6BAAA,EACD,IAAI,EACJ,OAAO,EACP,aAAa,CAAC,CAAA,CAAA;AAZlB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAYW,CAAC,CAAA;;;;AACtB,KAAA,CAAA;AAED;;;;AAIG;IACK,gBAA0B,CAAA,SAAA,CAAA,0BAAA,GAAlC,UAAmC,eAAuB,EAAA;AAEtD,QAAA,IAAM,aAAa,GAAqB;AACpC,YAAA,aAAa,EAAE,eAAe;AAC9B,YAAA,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,+BAA+B,CAAC,eAAe;YAC3E,cAAc,EAAE,cAAc,CAAC,QAAQ;AACvC,YAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;AAC1C,YAAA,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM;SAC/B,CAAC;QAEF,IAAM,QAAQ,GAAoB,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;;AAGvF,QAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AACD,QAAA,OAAO,QAAQ,CAAC,CAAC,CAAkB,CAAC;KACvC,CAAA;AAED;;;;;AAKG;AACK,IAAA,gBAAA,CAAA,SAAA,CAAA,8BAA8B,GAAtC,UAAuC,QAAgB,EAAE,OAAgC,EAAA;QACrF,IAAM,UAAU,GAAG,OAAO,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,CAAC;AAC/E;;;AAGG;AACH,QAAA,IAAM,cAAc,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,WAAW,EAAE,KAAK,oBAAoB,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,cAAc,CAAC,6BAA6B,GAAG,cAAc,CAAC,YAAY,CAAC;AAE3L,QAAA,IAAM,iBAAiB,GAAqB;AACxC,YAAA,cAAc,EAAE,cAAc;AAC9B,YAAA,QAAQ,EAAA,QAAA;YACR,MAAM,EAAE,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC5D,YAAA,SAAS,EAAE,UAAU;YACrB,KAAK,EAAE,OAAO,CAAC,MAAM;YACrB,mBAAmB,EAAE,OAAO,CAAC,mBAAmB;YAChD,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;SAC5C,CAAC;QAEF,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;AAElF,QAAA,IAAM,eAAe,GAAG,YAAY,CAAC,MAAM,CAAC;QAC5C,IAAI,eAAe,GAAG,CAAC,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;aAAM,IAAI,eAAe,GAAG,CAAC,EAAE;AAC5B,YAAA,MAAM,eAAe,CAAC,wCAAwC,EAAE,CAAC;AACpE,SAAA;AAED,QAAA,OAAO,YAAY,CAAC,CAAC,CAAsB,CAAC;KAC/C,CAAA;AAED;;;;AAIG;AACW,IAAA,gBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAjC,UAAkC,OAAgC,EAAE,SAAoB,EAAE,iBAAyB,EAAA;;;;;;AAEzG,wBAAA,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;wBACjE,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;AACvF,wBAAA,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;AACnD,wBAAA,OAAO,GAA2B,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACnE,wBAAA,UAAU,GAAsB;AAClC,4BAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;4BAC1C,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;4BAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;4BACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;4BAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;yBACzB,CAAC;AAEI,wBAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AAC3B,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,CAAA,CAAA;;AAA5F,wBAAA,QAAQ,GAAG,EAAiF,CAAA,IAAA,EAAA,CAAA;AAE5F,wBAAA,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;AAEF,wBAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;wBAC/B,OAAM,CAAA,CAAA,YAAA,eAAe,CAAC,yBAAyB,CACjE,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,EACP,SAAS,EACT,iBAAiB,CACpB,CAAA,CAAA;;AAPK,wBAAA,aAAa,GAAG,EAOrB,CAAA,IAAA,EAAA,CAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,aAAa,CAAC,CAAA;;;;AACxB,KAAA,CAAA;AAED;;;AAGG;IACK,gBAAsB,CAAA,SAAA,CAAA,sBAAA,GAA9B,UAA+B,OAAgC,EAAA;AAC3D,QAAA,IAAM,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAEvD,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAE/D,QAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAE3C,QAAA,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAEpD,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAEjC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QACzD,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5E,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC7B,YAAA,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACpE,SAAA;AAED,QAAA,IAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;AAC3F,QAAA,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAEjD,QAAA,gBAAgB,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAErE,QAAA,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAEvD,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;YAC5C,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAChF,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,EAAE;YAC/C,IAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;AACtE,YAAA,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AAC/D,YAAA,gBAAgB,CAAC,sBAAsB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC1E,SAAA;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;AACzH,YAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAC1F,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CAvPA,CAAsC,UAAU,CAuP/C;;;;"}