{"version": 3, "file": "RefreshTokenClient.js", "sources": ["../../src/client/RefreshTokenClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ClientConfiguration } from \"../config/ClientConfiguration\";\r\nimport { BaseClient } from \"./BaseClient\";\r\nimport { CommonRefreshTokenRequest } from \"../request/CommonRefreshTokenRequest\";\r\nimport { Authority } from \"../authority/Authority\";\r\nimport { ServerAuthorizationTokenResponse } from \"../response/ServerAuthorizationTokenResponse\";\r\nimport { RequestParameterBuilder } from \"../request/RequestParameterBuilder\";\r\nimport { GrantType, AuthenticationScheme, Errors, HeaderNames } from \"../utils/Constants\";\r\nimport { ResponseHandler } from \"../response/ResponseHandler\";\r\nimport { AuthenticationResult } from \"../response/AuthenticationResult\";\r\nimport { PopTokenGenerator } from \"../crypto/PopTokenGenerator\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { RequestThumbprint } from \"../network/RequestThumbprint\";\r\nimport { NetworkResponse } from \"../network/NetworkManager\";\r\nimport { CommonSilentFlowRequest } from \"../request/CommonSilentFlowRequest\";\r\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { ServerError } from \"../error/ServerError\";\r\nimport { TimeUtils } from \"../utils/TimeUtils\";\r\nimport { UrlString } from \"../url/UrlString\";\r\nimport { CcsCredentialType } from \"../account/CcsCredential\";\r\nimport { buildClientInfoFromHomeAccountId } from \"../account/ClientInfo\";\r\nimport { InteractionRequiredAuthError, InteractionRequiredAuthErrorMessage } from \"../error/InteractionRequiredAuthError\";\r\nimport { PerformanceEvents } from \"../telemetry/performance/PerformanceEvent\";\r\nimport { IPerformanceClient } from \"../telemetry/performance/IPerformanceClient\";\r\n/**\r\n * OAuth2.0 refresh token client\r\n */\r\nexport class RefreshTokenClient extends BaseClient {\r\n    constructor(configuration: ClientConfiguration, performanceClient?: IPerformanceClient) {\r\n        super(configuration, performanceClient);\r\n\r\n    }\r\n    public async acquireToken(request: CommonRefreshTokenRequest): Promise<AuthenticationResult> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.RefreshTokenClientAcquireToken, request.correlationId);\r\n\r\n        const atsMeasurement = this.performanceClient?.startMeasurement(PerformanceEvents.RefreshTokenClientAcquireToken, request.correlationId);\r\n        this.logger.verbose(\"RefreshTokenClientAcquireToken called\", request.correlationId);\r\n        const reqTimestamp = TimeUtils.nowSeconds();\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.RefreshTokenClientExecuteTokenRequest, request.correlationId);\r\n        const response = await this.executeTokenRequest(request, this.authority);\r\n        const httpVerToken = response.headers?.[HeaderNames.X_MS_HTTP_VERSION];\r\n        atsMeasurement?.addStaticFields({\r\n            refreshTokenSize: response.body.refresh_token?.length || 0,\r\n        });\r\n        if(httpVerToken)\r\n        {\r\n            atsMeasurement?.addStaticFields({\r\n                httpVerToken,\r\n            });\r\n        }\r\n\r\n        // Retrieve requestId from response headers\r\n        const requestId = response.headers?.[HeaderNames.X_MS_REQUEST_ID];\r\n        const responseHandler = new ResponseHandler(\r\n            this.config.authOptions.clientId,\r\n            this.cacheManager,\r\n            this.cryptoUtils,\r\n            this.logger,\r\n            this.config.serializableCache,\r\n            this.config.persistencePlugin\r\n        );\r\n        responseHandler.validateTokenResponse(response.body);\r\n\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.HandleServerTokenResponse, request.correlationId);\r\n        return responseHandler.handleServerTokenResponse(\r\n            response.body,\r\n            this.authority,\r\n            reqTimestamp,\r\n            request,\r\n            undefined,\r\n            undefined,\r\n            true,\r\n            request.forceCache,\r\n            requestId\r\n        ).then((result: AuthenticationResult) => {\r\n            atsMeasurement?.endMeasurement({\r\n                success: true\r\n            });\r\n            return result;\r\n        })\r\n            .catch((error) => {\r\n                this.logger.verbose(\"Error in fetching refresh token\", request.correlationId);\r\n                atsMeasurement?.endMeasurement({\r\n                    errorCode: error.errorCode,\r\n                    subErrorCode: error.subError,\r\n                    success: false\r\n                });\r\n                throw error;\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Gets cached refresh token and attaches to request, then calls acquireToken API\r\n     * @param request\r\n     */\r\n    public async acquireTokenByRefreshToken(request: CommonSilentFlowRequest): Promise<AuthenticationResult> {\r\n        // Cannot renew token if no request object is given.\r\n        if (!request) {\r\n            throw ClientConfigurationError.createEmptyTokenRequestError();\r\n        }\r\n\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.RefreshTokenClientAcquireTokenByRefreshToken, request.correlationId);\r\n\r\n        // We currently do not support silent flow for account === null use cases; This will be revisited for confidential flow usecases\r\n        if (!request.account) {\r\n            throw ClientAuthError.createNoAccountInSilentRequestError();\r\n        }\r\n\r\n        // try checking if FOCI is enabled for the given application\r\n        const isFOCI = this.cacheManager.isAppMetadataFOCI(request.account.environment);\r\n\r\n        // if the app is part of the family, retrive a Family refresh token if present and make a refreshTokenRequest\r\n        if (isFOCI) {\r\n            try {\r\n                this.performanceClient?.setPreQueueTime(PerformanceEvents.RefreshTokenClientAcquireTokenWithCachedRefreshToken, request.correlationId);\r\n                return this.acquireTokenWithCachedRefreshToken(request, true);\r\n            } catch (e) {\r\n                const noFamilyRTInCache = e instanceof InteractionRequiredAuthError && e.errorCode === InteractionRequiredAuthErrorMessage.noTokensFoundError.code;\r\n                const clientMismatchErrorWithFamilyRT = e instanceof ServerError && e.errorCode === Errors.INVALID_GRANT_ERROR && e.subError === Errors.CLIENT_MISMATCH_ERROR;\r\n\r\n                // if family Refresh Token (FRT) cache acquisition fails or if client_mismatch error is seen with FRT, reattempt with application Refresh Token (ART)\r\n                if (noFamilyRTInCache || clientMismatchErrorWithFamilyRT) {\r\n                    this.performanceClient?.setPreQueueTime(PerformanceEvents.RefreshTokenClientAcquireTokenWithCachedRefreshToken, request.correlationId);\r\n                    return this.acquireTokenWithCachedRefreshToken(request, false);\r\n                    // throw in all other cases\r\n                } else {\r\n                    throw e;\r\n                }\r\n            }\r\n        }\r\n        // fall back to application refresh token acquisition\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.RefreshTokenClientAcquireTokenWithCachedRefreshToken, request.correlationId);\r\n        return this.acquireTokenWithCachedRefreshToken(request, false);\r\n\r\n    }\r\n\r\n    /**\r\n     * makes a network call to acquire tokens by exchanging RefreshToken available in userCache; throws if refresh token is not cached\r\n     * @param request\r\n     */\r\n    private async acquireTokenWithCachedRefreshToken(request: CommonSilentFlowRequest, foci: boolean) {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.RefreshTokenClientAcquireTokenWithCachedRefreshToken, request.correlationId);\r\n\r\n        // fetches family RT or application RT based on FOCI value\r\n\r\n        const atsMeasurement = this.performanceClient?.startMeasurement(PerformanceEvents.RefreshTokenClientAcquireTokenWithCachedRefreshToken, request.correlationId);\r\n        this.logger.verbose(\"RefreshTokenClientAcquireTokenWithCachedRefreshToken called\", request.correlationId);\r\n        const refreshToken = this.cacheManager.getRefreshToken(request.account, foci);\r\n\r\n        if (!refreshToken) {\r\n            atsMeasurement?.discardMeasurement();\r\n            throw InteractionRequiredAuthError.createNoTokensFoundError();\r\n        }\r\n        // attach cached RT size to the current measurement\r\n        atsMeasurement?.endMeasurement({\r\n            success: true\r\n        });\r\n\r\n        const refreshTokenRequest: CommonRefreshTokenRequest = {\r\n            ...request,\r\n            refreshToken: refreshToken.secret,\r\n            authenticationScheme: request.authenticationScheme || AuthenticationScheme.BEARER,\r\n            ccsCredential: {\r\n                credential: request.account.homeAccountId,\r\n                type: CcsCredentialType.HOME_ACCOUNT_ID\r\n            }\r\n        };\r\n\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.RefreshTokenClientAcquireToken, request.correlationId);\r\n        return this.acquireToken(refreshTokenRequest);\r\n    }\r\n\r\n    /**\r\n     * Constructs the network message and makes a NW call to the underlying secure token service\r\n     * @param request\r\n     * @param authority\r\n     */\r\n    private async executeTokenRequest(request: CommonRefreshTokenRequest, authority: Authority)\r\n        : Promise<NetworkResponse<ServerAuthorizationTokenResponse>> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.RefreshTokenClientExecuteTokenRequest, request.correlationId);\r\n        const acquireTokenMeasurement = this.performanceClient?.startMeasurement(PerformanceEvents.RefreshTokenClientExecuteTokenRequest, request.correlationId);\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.RefreshTokenClientCreateTokenRequestBody, request.correlationId);\r\n        \r\n        const queryParametersString = this.createTokenQueryParameters(request);\r\n        const endpoint = UrlString.appendQueryString(authority.tokenEndpoint, queryParametersString);\r\n        \r\n        const requestBody = await this.createTokenRequestBody(request);\r\n        const headers: Record<string, string> = this.createTokenRequestHeaders(request.ccsCredential);\r\n        const thumbprint: RequestThumbprint = {\r\n            clientId: this.config.authOptions.clientId,\r\n            authority: authority.canonicalAuthority,\r\n            scopes: request.scopes,\r\n            claims: request.claims,\r\n            authenticationScheme: request.authenticationScheme,\r\n            resourceRequestMethod: request.resourceRequestMethod,\r\n            resourceRequestUri: request.resourceRequestUri,\r\n            shrClaims: request.shrClaims,\r\n            sshKid: request.sshKid\r\n        };\r\n\r\n        return this.executePostToTokenEndpoint(endpoint, requestBody, headers, thumbprint)\r\n            .then((result) => {\r\n                acquireTokenMeasurement?.endMeasurement({\r\n                    success: true\r\n                });\r\n                return result;\r\n            })\r\n            .catch((error) => {\r\n                acquireTokenMeasurement?.endMeasurement({\r\n                    success: false\r\n                });\r\n                throw error;\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Helper function to create the token request body\r\n     * @param request\r\n     */\r\n    private async createTokenRequestBody(request: CommonRefreshTokenRequest): Promise<string> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.RefreshTokenClientCreateTokenRequestBody, request.correlationId);\r\n\r\n        const correlationId = request.correlationId;\r\n        const acquireTokenMeasurement = this.performanceClient?.startMeasurement(PerformanceEvents.BaseClientCreateTokenRequestHeaders, correlationId);\r\n        const parameterBuilder = new RequestParameterBuilder();\r\n\r\n        parameterBuilder.addClientId(this.config.authOptions.clientId);\r\n\r\n        parameterBuilder.addScopes(request.scopes);\r\n\r\n        parameterBuilder.addGrantType(GrantType.REFRESH_TOKEN_GRANT);\r\n\r\n        parameterBuilder.addClientInfo();\r\n\r\n        parameterBuilder.addLibraryInfo(this.config.libraryInfo);\r\n        parameterBuilder.addApplicationTelemetry(this.config.telemetry.application);\r\n        parameterBuilder.addThrottling();\r\n\r\n        if (this.serverTelemetryManager) {\r\n            parameterBuilder.addServerTelemetry(this.serverTelemetryManager);\r\n        }\r\n\r\n        parameterBuilder.addCorrelationId(correlationId);\r\n\r\n        parameterBuilder.addRefreshToken(request.refreshToken);\r\n\r\n        if (this.config.clientCredentials.clientSecret) {\r\n            parameterBuilder.addClientSecret(this.config.clientCredentials.clientSecret);\r\n        }\r\n\r\n        if (this.config.clientCredentials.clientAssertion) {\r\n            const clientAssertion = this.config.clientCredentials.clientAssertion;\r\n            parameterBuilder.addClientAssertion(clientAssertion.assertion);\r\n            parameterBuilder.addClientAssertionType(clientAssertion.assertionType);\r\n        }\r\n\r\n        if (request.authenticationScheme === AuthenticationScheme.POP) {\r\n            const popTokenGenerator = new PopTokenGenerator(this.cryptoUtils, this.performanceClient);\r\n            this.performanceClient?.setPreQueueTime(PerformanceEvents.PopTokenGenerateCnf, request.correlationId);\r\n            const reqCnfData = await popTokenGenerator.generateCnf(request);\r\n            // SPA PoP requires full Base64Url encoded req_cnf string (unhashed)\r\n            parameterBuilder.addPopToken(reqCnfData.reqCnfString);\r\n        } else if (request.authenticationScheme === AuthenticationScheme.SSH) {\r\n            if (request.sshJwk) {\r\n                parameterBuilder.addSshJwk(request.sshJwk);\r\n            } else {\r\n                acquireTokenMeasurement?.endMeasurement({\r\n                    success: false\r\n                });\r\n                throw ClientConfigurationError.createMissingSshJwkError();\r\n            }\r\n        }\r\n\r\n        if (!StringUtils.isEmptyObj(request.claims) || this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0) {\r\n            parameterBuilder.addClaims(request.claims, this.config.authOptions.clientCapabilities);\r\n        }\r\n\r\n        if (this.config.systemOptions.preventCorsPreflight && request.ccsCredential) {\r\n            switch (request.ccsCredential.type) {\r\n                case CcsCredentialType.HOME_ACCOUNT_ID:\r\n                    try {\r\n                        const clientInfo = buildClientInfoFromHomeAccountId(request.ccsCredential.credential);\r\n                        parameterBuilder.addCcsOid(clientInfo);\r\n                    } catch (e) {\r\n                        this.logger.verbose(\"Could not parse home account ID for CCS Header: \" + e);\r\n                    }\r\n                    break;\r\n                case CcsCredentialType.UPN:\r\n                    parameterBuilder.addCcsUpn(request.ccsCredential.credential);\r\n                    break;\r\n            }\r\n        }\r\n        acquireTokenMeasurement?.endMeasurement({\r\n            success: true\r\n        });\r\n        return parameterBuilder.createQueryString();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AA0BH;;AAEG;AACH,IAAA,kBAAA,kBAAA,UAAA,MAAA,EAAA;IAAwC,SAAU,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;IAC9C,SAAY,kBAAA,CAAA,aAAkC,EAAE,iBAAsC,EAAA;eAClF,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,aAAa,EAAE,iBAAiB,CAAC,IAAA,IAAA,CAAA;KAE1C;IACY,kBAAY,CAAA,SAAA,CAAA,YAAA,GAAzB,UAA0B,OAAkC,EAAA;;;;;;;;AACxD,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAE/G,wBAAA,cAAc,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,0CAAE,gBAAgB,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;wBACzI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uCAAuC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC9E,wBAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AAC5C,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,qCAAqC,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;wBACvG,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA,CAAA;;AAAlE,wBAAA,QAAQ,GAAG,EAAuD,CAAA,IAAA,EAAA,CAAA;wBAClE,YAAY,GAAA,CAAA,EAAA,GAAG,QAAQ,CAAC,OAAO,0CAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC;AACvE,wBAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,eAAe,CAAC;4BAC5B,gBAAgB,EAAE,CAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,IAAI,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,KAAI,CAAC;yBAC7D,CAAE,CAAA;AACH,wBAAA,IAAG,YAAY,EACf;AACI,4BAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,eAAe,CAAC;AAC5B,gCAAA,YAAY,EAAA,YAAA;6BACf,CAAE,CAAA;AACN,yBAAA;wBAGK,SAAS,GAAA,CAAA,EAAA,GAAG,QAAQ,CAAC,OAAO,0CAAG,WAAW,CAAC,eAAe,CAAC,CAAC;AAC5D,wBAAA,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;AACF,wBAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAErD,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,yBAAyB,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAC5G,wBAAA,OAAA,CAAA,CAAA,aAAO,eAAe,CAAC,yBAAyB,CAC5C,QAAQ,CAAC,IAAI,EACb,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,EACP,SAAS,EACT,SAAS,EACT,IAAI,EACJ,OAAO,CAAC,UAAU,EAClB,SAAS,CACZ,CAAC,IAAI,CAAC,UAAC,MAA4B,EAAA;AAChC,gCAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,cAAc,CAAC;AAC3B,oCAAA,OAAO,EAAE,IAAI;iCAChB,CAAE,CAAA;AACH,gCAAA,OAAO,MAAM,CAAC;AAClB,6BAAC,CAAC;iCACG,KAAK,CAAC,UAAC,KAAK,EAAA;gCACT,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAAiC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC9E,gCAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,cAAc,CAAC;oCAC3B,SAAS,EAAE,KAAK,CAAC,SAAS;oCAC1B,YAAY,EAAE,KAAK,CAAC,QAAQ;AAC5B,oCAAA,OAAO,EAAE,KAAK;iCACjB,CAAE,CAAA;AACH,gCAAA,MAAM,KAAK,CAAC;AAChB,6BAAC,CAAC,CAAC,CAAA;;;;AACV,KAAA,CAAA;AAED;;;AAGG;IACU,kBAA0B,CAAA,SAAA,CAAA,0BAAA,GAAvC,UAAwC,OAAgC,EAAA;;;;;;gBAEpE,IAAI,CAAC,OAAO,EAAE;AACV,oBAAA,MAAM,wBAAwB,CAAC,4BAA4B,EAAE,CAAC;AACjE,iBAAA;AAED,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,4CAA4C,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;;AAGnI,gBAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAClB,oBAAA,MAAM,eAAe,CAAC,mCAAmC,EAAE,CAAC;AAC/D,iBAAA;AAGK,gBAAA,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;;AAGhF,gBAAA,IAAI,MAAM,EAAE;oBACR,IAAI;AACA,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,oDAAoD,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;wBACvI,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,kCAAkC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAA;AACjE,qBAAA;AAAC,oBAAA,OAAO,CAAC,EAAE;AACF,wBAAA,iBAAiB,GAAG,CAAC,YAAY,4BAA4B,IAAI,CAAC,CAAC,SAAS,KAAK,mCAAmC,CAAC,kBAAkB,CAAC,IAAI,CAAC;wBAC7I,+BAA+B,GAAG,CAAC,YAAY,WAAW,IAAI,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,mBAAmB,IAAI,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,qBAAqB,CAAC;;wBAG9J,IAAI,iBAAiB,IAAI,+BAA+B,EAAE;AACtD,4BAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,oDAAoD,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;4BACvI,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAA;;AAElE,yBAAA;AAAM,6BAAA;AACH,4BAAA,MAAM,CAAC,CAAC;AACX,yBAAA;AACJ,qBAAA;AACJ,iBAAA;;AAED,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,oDAAoD,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;gBACvI,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAA;;;AAElE,KAAA,CAAA;AAED;;;AAGG;AACW,IAAA,kBAAA,CAAA,SAAA,CAAA,kCAAkC,GAAhD,UAAiD,OAAgC,EAAE,IAAa,EAAA;;;;;AAC5F,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,oDAAoD,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAIrI,gBAAA,cAAc,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,0CAAE,gBAAgB,CAAC,iBAAiB,CAAC,oDAAoD,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;gBAC/J,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6DAA6D,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACpG,gBAAA,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAE9E,IAAI,CAAC,YAAY,EAAE;AACf,oBAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,kBAAkB,EAAG,CAAA;AACrC,oBAAA,MAAM,4BAA4B,CAAC,wBAAwB,EAAE,CAAC;AACjE,iBAAA;;AAED,gBAAA,cAAc,aAAd,cAAc,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAd,cAAc,CAAE,cAAc,CAAC;AAC3B,oBAAA,OAAO,EAAE,IAAI;iBAChB,CAAE,CAAA;gBAEG,mBAAmB,GAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EAClB,OAAO,CACV,EAAA,EAAA,YAAY,EAAE,YAAY,CAAC,MAAM,EACjC,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,EACjF,aAAa,EAAE;AACX,wBAAA,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,aAAa;wBACzC,IAAI,EAAE,iBAAiB,CAAC,eAAe;AAC1C,qBAAA,EAAA,CACJ,CAAC;AAEF,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,8BAA8B,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AACjH,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,CAAA;;;AACjD,KAAA,CAAA;AAED;;;;AAIG;AACW,IAAA,kBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAjC,UAAkC,OAAkC,EAAE,SAAoB,EAAA;;;;;;;AAEtF,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,qCAAqC,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AACtH,wBAAA,uBAAuB,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,0CAAE,gBAAgB,CAAC,iBAAiB,CAAC,qCAAqC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACzJ,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,wCAAwC,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAErH,wBAAA,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;wBACjE,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;AAEzE,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAA,CAAA;;AAAxD,wBAAA,WAAW,GAAG,EAA0C,CAAA,IAAA,EAAA,CAAA;wBACxD,OAAO,GAA2B,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACxF,wBAAA,UAAU,GAAsB;AAClC,4BAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;4BAC1C,SAAS,EAAE,SAAS,CAAC,kBAAkB;4BACvC,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;4BAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;4BACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;4BAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;yBACzB,CAAC;wBAEF,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC;iCAC7E,IAAI,CAAC,UAAC,MAAM,EAAA;AACT,gCAAA,uBAAuB,aAAvB,uBAAuB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAvB,uBAAuB,CAAE,cAAc,CAAC;AACpC,oCAAA,OAAO,EAAE,IAAI;iCAChB,CAAE,CAAA;AACH,gCAAA,OAAO,MAAM,CAAC;AAClB,6BAAC,CAAC;iCACD,KAAK,CAAC,UAAC,KAAK,EAAA;AACT,gCAAA,uBAAuB,aAAvB,uBAAuB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAvB,uBAAuB,CAAE,cAAc,CAAC;AACpC,oCAAA,OAAO,EAAE,KAAK;iCACjB,CAAE,CAAA;AACH,gCAAA,MAAM,KAAK,CAAC;AAChB,6BAAC,CAAC,CAAC,CAAA;;;;AACV,KAAA,CAAA;AAED;;;AAGG;IACW,kBAAsB,CAAA,SAAA,CAAA,sBAAA,GAApC,UAAqC,OAAkC,EAAA;;;;;;;AACnE,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,wCAAwC,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAEzH,wBAAA,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AACtC,wBAAA,uBAAuB,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAgB,CAAC,iBAAiB,CAAC,mCAAmC,EAAE,aAAa,CAAC,CAAC;AACzI,wBAAA,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;wBAEvD,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAE/D,wBAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAE3C,wBAAA,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;wBAE7D,gBAAgB,CAAC,aAAa,EAAE,CAAC;wBAEjC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;wBACzD,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;wBAC5E,gBAAgB,CAAC,aAAa,EAAE,CAAC;wBAEjC,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC7B,4BAAA,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACpE,yBAAA;AAED,wBAAA,gBAAgB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAEjD,wBAAA,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAEvD,wBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,EAAE;4BAC5C,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAChF,yBAAA;AAED,wBAAA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,EAAE;4BACzC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC;AACtE,4BAAA,gBAAgB,CAAC,kBAAkB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AAC/D,4BAAA,gBAAgB,CAAC,sBAAsB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC1E,yBAAA;8BAEG,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,CAAA,EAAzD,OAAyD,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACnD,wBAAA,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC1F,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AACnF,wBAAA,OAAA,CAAA,CAAA,YAAM,iBAAiB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA,CAAA;;AAAzD,wBAAA,UAAU,GAAG,EAA4C,CAAA,IAAA,EAAA,CAAA;;AAE/D,wBAAA,gBAAgB,CAAC,WAAW,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;;;AACnD,wBAAA,IAAI,OAAO,CAAC,oBAAoB,KAAK,oBAAoB,CAAC,GAAG,EAAE;4BAClE,IAAI,OAAO,CAAC,MAAM,EAAE;AAChB,gCAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9C,6BAAA;AAAM,iCAAA;AACH,gCAAA,uBAAuB,aAAvB,uBAAuB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAvB,uBAAuB,CAAE,cAAc,CAAC;AACpC,oCAAA,OAAO,EAAE,KAAK;iCACjB,CAAE,CAAA;AACH,gCAAA,MAAM,wBAAwB,CAAC,wBAAwB,EAAE,CAAC;AAC7D,6BAAA;AACJ,yBAAA;;;AAED,wBAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;AAChJ,4BAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAC1F,yBAAA;wBAED,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,oBAAoB,IAAI,OAAO,CAAC,aAAa,EAAE;AACzE,4BAAA,QAAQ,OAAO,CAAC,aAAa,CAAC,IAAI;gCAC9B,KAAK,iBAAiB,CAAC,eAAe;oCAClC,IAAI;wCACM,UAAU,GAAG,gCAAgC,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACtF,wCAAA,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,qCAAA;AAAC,oCAAA,OAAO,CAAC,EAAE;wCACR,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kDAAkD,GAAG,CAAC,CAAC,CAAC;AAC/E,qCAAA;oCACD,MAAM;gCACV,KAAK,iBAAiB,CAAC,GAAG;oCACtB,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;oCAC7D,MAAM;AACb,6BAAA;AACJ,yBAAA;AACD,wBAAA,uBAAuB,aAAvB,uBAAuB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAvB,uBAAuB,CAAE,cAAc,CAAC;AACpC,4BAAA,OAAO,EAAE,IAAI;yBAChB,CAAE,CAAA;AACH,wBAAA,OAAA,CAAA,CAAA,aAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,CAAA;;;;AAC/C,KAAA,CAAA;IACL,OAAC,kBAAA,CAAA;AAAD,CA9QA,CAAwC,UAAU,CA8QjD;;;;"}