{"version": 3, "file": "ClientConfiguration.js", "sources": ["../../src/config/ClientConfiguration.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { INetworkModule } from \"../network/INetworkModule\";\r\nimport { DEFAULT_CRYPTO_IMPLEMENTATION, ICrypto } from \"../crypto/ICrypto\";\r\nimport { AuthError } from \"../error/AuthError\";\r\nimport { ILoggerCallback, Logger, LogLevel } from \"../logger/Logger\";\r\nimport { Constants } from \"../utils/Constants\";\r\nimport { version } from \"../packageMetadata\";\r\nimport { Authority } from \"../authority/Authority\";\r\nimport { AzureCloudInstance } from \"../authority/AuthorityOptions\";\r\nimport { CacheManager, DefaultStorageClass } from \"../cache/CacheManager\";\r\nimport { ServerTelemetryManager } from \"../telemetry/server/ServerTelemetryManager\";\r\nimport { ICachePlugin } from \"../cache/interface/ICachePlugin\";\r\nimport { ISerializableTokenCache } from \"../cache/interface/ISerializableTokenCache\";\r\nimport { ClientCredentials } from \"../account/ClientCredentials\";\r\n\r\n// Token renewal offset default in seconds\r\nconst DEFAULT_TOKEN_RENEWAL_OFFSET_SEC = 300;\r\n\r\n/**\r\n * Use the configuration object to configure MSAL Modules and initialize the base interfaces for MSAL.\r\n *\r\n * This object allows you to configure important elements of MSAL functionality:\r\n * - authOptions                - Authentication for application\r\n * - cryptoInterface            - Implementation of crypto functions\r\n * - libraryInfo                - Library metadata\r\n * - telemetry                  - Telemetry options and data\r\n * - loggerOptions              - Logging for application\r\n * - cacheOptions               - Cache options for application\r\n * - networkInterface           - Network implementation\r\n * - storageInterface           - Storage implementation\r\n * - systemOptions              - Additional library options\r\n * - clientCredentials          - Credentials options for confidential clients\r\n */\r\nexport type ClientConfiguration = {\r\n    authOptions: AuthOptions,\r\n    systemOptions?: SystemOptions,\r\n    loggerOptions?: LoggerOptions,\r\n    cacheOptions?: CacheOptions,\r\n    storageInterface?: CacheManager,\r\n    networkInterface?: INetworkModule,\r\n    cryptoInterface?: ICrypto,\r\n    clientCredentials?: ClientCredentials,\r\n    libraryInfo?: LibraryInfo\r\n    telemetry?: TelemetryOptions,\r\n    serverTelemetryManager?: ServerTelemetryManager | null,\r\n    persistencePlugin?: ICachePlugin | null,\r\n    serializableCache?: ISerializableTokenCache | null,   \r\n};\r\n\r\nexport type CommonClientConfiguration = {\r\n    authOptions: Required<AuthOptions>,\r\n    systemOptions: Required<SystemOptions>,\r\n    loggerOptions : Required<LoggerOptions>,\r\n    cacheOptions: Required<CacheOptions>,\r\n    storageInterface: CacheManager,\r\n    networkInterface : INetworkModule,\r\n    cryptoInterface : Required<ICrypto>,\r\n    libraryInfo : LibraryInfo,\r\n    telemetry: Required<TelemetryOptions>,\r\n    serverTelemetryManager: ServerTelemetryManager | null,\r\n    clientCredentials: ClientCredentials,\r\n    persistencePlugin: ICachePlugin | null,\r\n    serializableCache: ISerializableTokenCache | null,    \r\n};\r\n\r\n/**\r\n * Use this to configure the auth options in the ClientConfiguration object\r\n *\r\n * - clientId                    - Client ID of your app registered with our Application registration portal : https://portal.azure.com/#blade/Microsoft_AAD_IAM/ActiveDirectoryMenuBlade/RegisteredAppsPreview in Microsoft Identity Platform\r\n * - authority                   - You can configure a specific authority, defaults to \" \" or \"https://login.microsoftonline.com/common\"\r\n * - knownAuthorities            - An array of URIs that are known to be valid. Used in B2C scenarios.\r\n * - cloudDiscoveryMetadata      - A string containing the cloud discovery response. Used in AAD scenarios.\r\n * - clientCapabilities          - Array of capabilities which will be added to the claims.access_token.xms_cc request property on every network request.\r\n * - protocolMode                - Enum that represents the protocol that msal follows. Used for configuring proper endpoints.\r\n * - skipAuthorityMetadataCache      - A flag to choose whether to use or not use the local metadata cache during authority initialization. Defaults to false.\r\n */\r\nexport type AuthOptions = {\r\n    clientId: string;\r\n    authority: Authority;\r\n    clientCapabilities?: Array<string>;\r\n    azureCloudOptions?: AzureCloudOptions;\r\n    skipAuthorityMetadataCache?: boolean;\r\n};\r\n\r\n/**\r\n * Use this to configure token renewal info in the Configuration object\r\n *\r\n * - tokenRenewalOffsetSeconds    - Sets the window of offset needed to renew the token before expiry\r\n */\r\nexport type SystemOptions = {\r\n    tokenRenewalOffsetSeconds?: number;\r\n    preventCorsPreflight?: boolean;\r\n};\r\n\r\n/**\r\n *  Use this to configure the logging that MSAL does, by configuring logger options in the Configuration object\r\n *\r\n * - loggerCallback                - Callback for logger\r\n * - piiLoggingEnabled             - Sets whether pii logging is enabled\r\n * - logLevel                      - Sets the level at which logging happens\r\n * - correlationId                 - Sets the correlationId printed by the logger\r\n */\r\nexport type LoggerOptions = {\r\n    loggerCallback?: ILoggerCallback,\r\n    piiLoggingEnabled?: boolean,\r\n    logLevel?: LogLevel,\r\n    correlationId?: string\r\n};\r\n\r\n/**\r\n *  Use this to configure credential cache preferences in the ClientConfiguration object\r\n *\r\n * - claimsBasedCachingEnabled   - Sets whether tokens should be cached based on the claims hash. Default is true.\r\n */\r\nexport type CacheOptions = {\r\n    claimsBasedCachingEnabled?: boolean;\r\n};\r\n\r\n/**\r\n * Library-specific options\r\n */\r\nexport type LibraryInfo = {\r\n    sku: string,\r\n    version: string,\r\n    cpu: string,\r\n    os: string\r\n};\r\n\r\n/**\r\n * AzureCloudInstance specific options\r\n *\r\n * - azureCloudInstance             - string enum providing short notation for soverign and public cloud authorities\r\n * - tenant                         - provision to provide the tenant info\r\n */\r\nexport type AzureCloudOptions = {\r\n    azureCloudInstance: AzureCloudInstance;\r\n    tenant?: string,\r\n};\r\n\r\nexport type TelemetryOptions = {\r\n    application: ApplicationTelemetry;\r\n};\r\n\r\n/**\r\n * Telemetry information sent on request\r\n * - appName: Unique string name of an application\r\n * - appVersion: Version of the application using MSAL\r\n */\r\nexport type ApplicationTelemetry = {\r\n    appName: string;\r\n    appVersion: string;\r\n};\r\n\r\nexport const DEFAULT_SYSTEM_OPTIONS: Required<SystemOptions> = {\r\n    tokenRenewalOffsetSeconds: DEFAULT_TOKEN_RENEWAL_OFFSET_SEC,\r\n    preventCorsPreflight: false\r\n};\r\n\r\nconst DEFAULT_LOGGER_IMPLEMENTATION: Required<LoggerOptions> = {\r\n    loggerCallback: () => {\r\n        // allow users to not set loggerCallback\r\n    },\r\n    piiLoggingEnabled: false,\r\n    logLevel: LogLevel.Info,\r\n    correlationId: Constants.EMPTY_STRING\r\n};\r\n\r\nconst DEFAULT_CACHE_OPTIONS: Required<CacheOptions> = {\r\n    claimsBasedCachingEnabled: true\r\n};\r\n\r\nconst DEFAULT_NETWORK_IMPLEMENTATION: INetworkModule = {\r\n    async sendGetRequestAsync<T>(): Promise<T> {\r\n        const notImplErr = \"Network interface - sendGetRequestAsync() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    },\r\n    async sendPostRequestAsync<T>(): Promise<T> {\r\n        const notImplErr = \"Network interface - sendPostRequestAsync() has not been implemented\";\r\n        throw AuthError.createUnexpectedError(notImplErr);\r\n    }\r\n};\r\n\r\nconst DEFAULT_LIBRARY_INFO: LibraryInfo = {\r\n    sku: Constants.SKU,\r\n    version: version,\r\n    cpu: Constants.EMPTY_STRING,\r\n    os: Constants.EMPTY_STRING\r\n};\r\n\r\nconst DEFAULT_CLIENT_CREDENTIALS: ClientCredentials = {\r\n    clientSecret: Constants.EMPTY_STRING,\r\n    clientAssertion: undefined\r\n};\r\n\r\nconst DEFAULT_AZURE_CLOUD_OPTIONS: AzureCloudOptions = {\r\n    azureCloudInstance: AzureCloudInstance.None,\r\n    tenant: `${Constants.DEFAULT_COMMON_TENANT}`\r\n};\r\n\r\nconst DEFAULT_TELEMETRY_OPTIONS: Required<TelemetryOptions> = {\r\n    application: {\r\n        appName: \"\",\r\n        appVersion: \"\"\r\n    }\r\n};\r\n\r\n/**\r\n * Function that sets the default options when not explicitly configured from app developer\r\n *\r\n * @param Configuration\r\n *\r\n * @returns Configuration\r\n */\r\nexport function buildClientConfiguration(\r\n    {\r\n        authOptions: userAuthOptions,\r\n        systemOptions: userSystemOptions,\r\n        loggerOptions: userLoggerOption,\r\n        cacheOptions: userCacheOptions,\r\n        storageInterface: storageImplementation,\r\n        networkInterface: networkImplementation,\r\n        cryptoInterface: cryptoImplementation,\r\n        clientCredentials: clientCredentials,\r\n        libraryInfo: libraryInfo,\r\n        telemetry: telemetry,\r\n        serverTelemetryManager: serverTelemetryManager,\r\n        persistencePlugin: persistencePlugin,\r\n        serializableCache: serializableCache,             \r\n    }: ClientConfiguration): CommonClientConfiguration {\r\n\r\n    const loggerOptions = { ...DEFAULT_LOGGER_IMPLEMENTATION, ...userLoggerOption };\r\n\r\n    return {\r\n        authOptions: buildAuthOptions(userAuthOptions),\r\n        systemOptions: { ...DEFAULT_SYSTEM_OPTIONS, ...userSystemOptions },\r\n        loggerOptions: loggerOptions,\r\n        cacheOptions: {...DEFAULT_CACHE_OPTIONS, ...userCacheOptions },\r\n        storageInterface: storageImplementation || new DefaultStorageClass(userAuthOptions.clientId, DEFAULT_CRYPTO_IMPLEMENTATION, new Logger(loggerOptions)),\r\n        networkInterface: networkImplementation || DEFAULT_NETWORK_IMPLEMENTATION,\r\n        cryptoInterface: cryptoImplementation || DEFAULT_CRYPTO_IMPLEMENTATION,\r\n        clientCredentials: clientCredentials || DEFAULT_CLIENT_CREDENTIALS,\r\n        libraryInfo: { ...DEFAULT_LIBRARY_INFO, ...libraryInfo },\r\n        telemetry: { ...DEFAULT_TELEMETRY_OPTIONS, ...telemetry },\r\n        serverTelemetryManager: serverTelemetryManager || null,\r\n        persistencePlugin: persistencePlugin || null,\r\n        serializableCache: serializableCache || null,             \r\n    };\r\n}\r\n\r\n/**\r\n * Construct authoptions from the client and platform passed values\r\n * @param authOptions\r\n */\r\nfunction buildAuthOptions(authOptions: AuthOptions): Required<AuthOptions> {\r\n    return {\r\n        clientCapabilities: [],\r\n        azureCloudOptions: DEFAULT_AZURE_CLOUD_OPTIONS,\r\n        skipAuthorityMetadataCache: false,\r\n        ...authOptions\r\n    };\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;AAGG;AAgBH;AACA,IAAM,gCAAgC,GAAG,GAAG,CAAC;AAyIhC,IAAA,sBAAsB,GAA4B;AAC3D,IAAA,yBAAyB,EAAE,gCAAgC;AAC3D,IAAA,oBAAoB,EAAE,KAAK;EAC7B;AAEF,IAAM,6BAA6B,GAA4B;AAC3D,IAAA,cAAc,EAAE,YAAA;;KAEf;AACD,IAAA,iBAAiB,EAAE,KAAK;IACxB,QAAQ,EAAE,QAAQ,CAAC,IAAI;IACvB,aAAa,EAAE,SAAS,CAAC,YAAY;CACxC,CAAC;AAEF,IAAM,qBAAqB,GAA2B;AAClD,IAAA,yBAAyB,EAAE,IAAI;CAClC,CAAC;AAEF,IAAM,8BAA8B,GAAmB;AAC7C,IAAA,mBAAmB,EAAzB,YAAA;;;;gBACU,UAAU,GAAG,oEAAoE,CAAC;AACxF,gBAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;;;AACrD,KAAA;AACK,IAAA,oBAAoB,EAA1B,YAAA;;;;gBACU,UAAU,GAAG,qEAAqE,CAAC;AACzF,gBAAA,MAAM,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;;;AACrD,KAAA;CACJ,CAAC;AAEF,IAAM,oBAAoB,GAAgB;IACtC,GAAG,EAAE,SAAS,CAAC,GAAG;AAClB,IAAA,OAAO,EAAE,OAAO;IAChB,GAAG,EAAE,SAAS,CAAC,YAAY;IAC3B,EAAE,EAAE,SAAS,CAAC,YAAY;CAC7B,CAAC;AAEF,IAAM,0BAA0B,GAAsB;IAClD,YAAY,EAAE,SAAS,CAAC,YAAY;AACpC,IAAA,eAAe,EAAE,SAAS;CAC7B,CAAC;AAEF,IAAM,2BAA2B,GAAsB;IACnD,kBAAkB,EAAE,kBAAkB,CAAC,IAAI;AAC3C,IAAA,MAAM,EAAE,EAAA,GAAG,SAAS,CAAC,qBAAuB;CAC/C,CAAC;AAEF,IAAM,yBAAyB,GAA+B;AAC1D,IAAA,WAAW,EAAE;AACT,QAAA,OAAO,EAAE,EAAE;AACX,QAAA,UAAU,EAAE,EAAE;AACjB,KAAA;CACJ,CAAC;AAEF;;;;;;AAMG;AACG,SAAU,wBAAwB,CACpC,EAcsB,EAAA;AAbL,IAAA,IAAA,eAAe,iBAAA,EACb,iBAAiB,GAAA,EAAA,CAAA,aAAA,EACjB,gBAAgB,GAAA,EAAA,CAAA,aAAA,EACjB,gBAAgB,GAAA,EAAA,CAAA,YAAA,EACZ,qBAAqB,GAAA,EAAA,CAAA,gBAAA,EACrB,qBAAqB,sBAAA,EACtB,oBAAoB,GAAA,EAAA,CAAA,eAAA,EAClB,iBAAiB,GAAA,EAAA,CAAA,iBAAA,EACvB,WAAW,GAAA,EAAA,CAAA,WAAA,EACb,SAAS,GAAA,EAAA,CAAA,SAAA,EACI,sBAAsB,4BAAA,EAC3B,iBAAiB,GAAA,EAAA,CAAA,iBAAA,EACjB,iBAAiB,GAAA,EAAA,CAAA,iBAAA,CAAA;AAGxC,IAAA,IAAM,aAAa,GAAQ,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,6BAA6B,CAAK,EAAA,gBAAgB,CAAE,CAAC;IAEhF,OAAO;AACH,QAAA,WAAW,EAAE,gBAAgB,CAAC,eAAe,CAAC;AAC9C,QAAA,aAAa,EAAO,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,sBAAsB,CAAK,EAAA,iBAAiB,CAAE;AAClE,QAAA,aAAa,EAAE,aAAa;AAC5B,QAAA,YAAY,EAAM,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,qBAAqB,CAAK,EAAA,gBAAgB,CAAE;AAC9D,QAAA,gBAAgB,EAAE,qBAAqB,IAAI,IAAI,mBAAmB,CAAC,eAAe,CAAC,QAAQ,EAAE,6BAA6B,EAAE,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC;QACtJ,gBAAgB,EAAE,qBAAqB,IAAI,8BAA8B;QACzE,eAAe,EAAE,oBAAoB,IAAI,6BAA6B;QACtE,iBAAiB,EAAE,iBAAiB,IAAI,0BAA0B;AAClE,QAAA,WAAW,EAAO,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,oBAAoB,CAAK,EAAA,WAAW,CAAE;AACxD,QAAA,SAAS,EAAO,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,yBAAyB,CAAK,EAAA,SAAS,CAAE;QACzD,sBAAsB,EAAE,sBAAsB,IAAI,IAAI;QACtD,iBAAiB,EAAE,iBAAiB,IAAI,IAAI;QAC5C,iBAAiB,EAAE,iBAAiB,IAAI,IAAI;KAC/C,CAAC;AACN,CAAC;AAED;;;AAGG;AACH,SAAS,gBAAgB,CAAC,WAAwB,EAAA;AAC9C,IAAA,OAAA,QAAA,CAAA,EACI,kBAAkB,EAAE,EAAE,EACtB,iBAAiB,EAAE,2BAA2B,EAC9C,0BAA0B,EAAE,KAAK,EAAA,EAC9B,WAAW,CAChB,CAAA;AACN;;;;"}