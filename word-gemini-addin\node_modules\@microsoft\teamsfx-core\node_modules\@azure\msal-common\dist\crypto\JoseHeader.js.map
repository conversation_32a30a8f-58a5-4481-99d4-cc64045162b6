{"version": 3, "file": "JoseHeader.js", "sources": ["../../src/crypto/JoseHeader.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { <PERSON><PERSON>eaderError } from \"../error/JoseHeaderError\";\r\nimport { JsonTypes } from \"../utils/Constants\";\r\n\r\nexport type JoseHeaderOptions = {\r\n    typ?: JsonTypes,\r\n    alg?: string,\r\n    kid?: string\r\n};\r\n\r\nexport class JoseHeader {\r\n    public typ?: JsonTypes;\r\n    public alg?: string;\r\n    public kid?: string;\r\n\r\n    constructor (options: JoseHeaderOptions) {\r\n        this.typ = options.typ;\r\n        this.alg = options.alg;\r\n        this.kid = options.kid;\r\n    }\r\n\r\n    /**\r\n     * Builds SignedHttpRequest formatted JOSE Header from the\r\n     * JOSE Header options provided or previously set on the object and returns\r\n     * the stringified header object.\r\n     * Throws if keyId or algorithm aren't provided since they are required for Access Token Binding.\r\n     * @param shrHeaderOptions \r\n     * @returns \r\n     */\r\n    static getShrHeaderString(shrHeaderOptions: JoseHeaderOptions): string {\r\n        // KeyID is required on the SHR header\r\n        if (!shrHeaderOptions.kid) {\r\n            throw JoseHeaderError.createMissingKidError();\r\n        }\r\n\r\n        // Alg is required on the SHR header\r\n        if (!shrHeaderOptions.alg) {\r\n            throw JoseHeaderError.createMissingAlgError();\r\n        }\r\n\r\n        const shrHeader = new JoseHeader({\r\n            // Access Token PoP headers must have type pop, but the type header can be overriden for special cases\r\n            typ: shrHeaderOptions.typ || JsonTypes.Pop,\r\n            kid: shrHeaderOptions.kid,\r\n            alg: shrHeaderOptions.alg\r\n        });\r\n\r\n        return JSON.stringify(shrHeader);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAWH,IAAA,UAAA,kBAAA,YAAA;AAKI,IAAA,SAAA,UAAA,CAAa,OAA0B,EAAA;AACnC,QAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACvB,QAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACvB,QAAA,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;KAC1B;AAED;;;;;;;AAOG;IACI,UAAkB,CAAA,kBAAA,GAAzB,UAA0B,gBAAmC,EAAA;;AAEzD,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;AACvB,YAAA,MAAM,eAAe,CAAC,qBAAqB,EAAE,CAAC;AACjD,SAAA;;AAGD,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;AACvB,YAAA,MAAM,eAAe,CAAC,qBAAqB,EAAE,CAAC;AACjD,SAAA;AAED,QAAA,IAAM,SAAS,GAAG,IAAI,UAAU,CAAC;;AAE7B,YAAA,GAAG,EAAE,gBAAgB,CAAC,GAAG,IAAI,SAAS,CAAC,GAAG;YAC1C,GAAG,EAAE,gBAAgB,CAAC,GAAG;YACzB,GAAG,EAAE,gBAAgB,CAAC,GAAG;AAC5B,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;KACpC,CAAA;IACL,OAAC,UAAA,CAAA;AAAD,CAAC,EAAA;;;;"}