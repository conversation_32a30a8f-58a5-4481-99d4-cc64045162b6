{"version": 3, "file": "PopTokenGenerator.js", "sources": ["../../src/crypto/PopTokenGenerator.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ICrypto, SignedHttpRequestParameters } from \"./ICrypto\";\r\nimport { TimeUtils } from \"../utils/TimeUtils\";\r\nimport { UrlString } from \"../url/UrlString\";\r\nimport { IPerformanceClient } from \"../telemetry/performance/IPerformanceClient\";\r\nimport { PerformanceEvents } from \"../telemetry/performance/PerformanceEvent\";\r\n\r\n/**\r\n * See eSTS docs for more info.\r\n * - A kid element, with the value containing an RFC 7638-compliant JWK thumbprint that is base64 encoded.\r\n * -  xms_ksl element, representing the storage location of the key's secret component on the client device. One of two values:\r\n *      - sw: software storage\r\n *      - uhw: hardware storage\r\n */\r\ntype ReqCnf = {\r\n    kid: string;\r\n    xms_ksl: KeyLocation;\r\n};\r\n\r\nexport type ReqCnfData = {\r\n    kid: string;\r\n    reqCnfString: string;\r\n    reqCnfHash: string;\r\n};\r\n\r\nenum KeyLocation {\r\n    SW = \"sw\",\r\n    UHW = \"uhw\"\r\n}\r\n\r\nexport class PopTokenGenerator {\r\n\r\n    private cryptoUtils: ICrypto;\r\n    private performanceClient?: IPerformanceClient;\r\n\r\n    constructor(cryptoUtils: ICrypto, performanceClient?: IPerformanceClient) {\r\n        this.cryptoUtils = cryptoUtils;\r\n        this.performanceClient = performanceClient;\r\n    }\r\n\r\n    /**\r\n     * Generates the req_cnf validated at the RP in the POP protocol for SHR parameters\r\n     * and returns an object containing the keyid, the full req_cnf string and the req_cnf string hash\r\n     * @param request\r\n     * @returns\r\n     */\r\n    async generateCnf(request: SignedHttpRequestParameters): Promise<ReqCnfData> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.PopTokenGenerateCnf, request.correlationId);\r\n\r\n        this.performanceClient?.setPreQueueTime(PerformanceEvents.PopTokenGenerateKid, request.correlationId);\r\n        const reqCnf = await this.generateKid(request);\r\n        const reqCnfString: string = this.cryptoUtils.base64Encode(JSON.stringify(reqCnf));\r\n\r\n        return {\r\n            kid: reqCnf.kid,\r\n            reqCnfString, \r\n            reqCnfHash: await this.cryptoUtils.hashString(reqCnfString) \r\n        };\r\n    }\r\n\r\n    /**\r\n     * Generates key_id for a SHR token request\r\n     * @param request\r\n     * @returns\r\n     */\r\n    async generateKid(request: SignedHttpRequestParameters): Promise<ReqCnf> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.PopTokenGenerateKid, request.correlationId);\r\n\r\n        const kidThumbprint = await this.cryptoUtils.getPublicKeyThumbprint(request);\r\n\r\n        return {\r\n            kid: kidThumbprint,\r\n            xms_ksl: KeyLocation.SW\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Signs the POP access_token with the local generated key-pair\r\n     * @param accessToken\r\n     * @param request\r\n     * @returns\r\n     */\r\n    async signPopToken(accessToken: string, keyId: string, request: SignedHttpRequestParameters): Promise<string> {\r\n        return this.signPayload(accessToken, keyId, request);\r\n    }\r\n\r\n    /**\r\n     * Utility function to generate the signed JWT for an access_token\r\n     * @param payload\r\n     * @param kid\r\n     * @param request\r\n     * @param claims\r\n     * @returns\r\n     */\r\n    async signPayload(payload: string, keyId: string, request: SignedHttpRequestParameters, claims?: object): Promise<string> {\r\n\r\n        // Deconstruct request to extract SHR parameters\r\n        const { resourceRequestMethod, resourceRequestUri, shrClaims, shrNonce } = request;\r\n\r\n        const resourceUrlString = (resourceRequestUri) ? new UrlString(resourceRequestUri) : undefined;\r\n        const resourceUrlComponents = resourceUrlString?.getUrlComponents();\r\n        return await this.cryptoUtils.signJwt({\r\n            at: payload,\r\n            ts: TimeUtils.nowSeconds(),\r\n            m: resourceRequestMethod?.toUpperCase(),\r\n            u: resourceUrlComponents?.HostNameAndPort,\r\n            nonce: shrNonce || this.cryptoUtils.createNewGuid(),\r\n            p: resourceUrlComponents?.AbsolutePath,\r\n            q: (resourceUrlComponents?.QueryString) ? [[], resourceUrlComponents.QueryString] : undefined,\r\n            client_claims: shrClaims || undefined,\r\n            ...claims\r\n        }, keyId, request.correlationId);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AA0BH,IAAK,WAGJ,CAAA;AAHD,CAAA,UAAK,WAAW,EAAA;AACZ,IAAA,WAAA,CAAA,IAAA,CAAA,GAAA,IAAS,CAAA;AACT,IAAA,WAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACf,CAAC,EAHI,WAAW,KAAX,WAAW,GAGf,EAAA,CAAA,CAAA,CAAA;AAED,IAAA,iBAAA,kBAAA,YAAA;IAKI,SAAY,iBAAA,CAAA,WAAoB,EAAE,iBAAsC,EAAA;AACpE,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAED;;;;;AAKG;IACG,iBAAW,CAAA,SAAA,CAAA,WAAA,GAAjB,UAAkB,OAAoC,EAAA;;;;;;;AAClD,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AAE1G,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;AACvF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA,CAAA;;AAAxC,wBAAA,MAAM,GAAG,EAA+B,CAAA,IAAA,EAAA,CAAA;AACxC,wBAAA,YAAY,GAAW,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;;4BAG/E,GAAG,EAAE,MAAM,CAAC,GAAG;AACf,4BAAA,YAAY,EAAA,YAAA;;wBACA,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,YAAY,CAAC,CAAA,CAAA;4BAH/D,OAGI,CAAA,CAAA,cAAA,EAAA,CAAA,UAAU,GAAE,EAA+C,CAAA,IAAA,EAAA;AAC7D,4BAAA,EAAA,EAAA,CAAA;;;;AACL,KAAA,CAAA;AAED;;;;AAIG;IACG,iBAAW,CAAA,SAAA,CAAA,WAAA,GAAjB,UAAkB,OAAoC,EAAA;;;;;;;AAClD,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,OAAO,CAAC,aAAa,CAAE,CAAA;wBAEpF,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAA,CAAA;;AAAtE,wBAAA,aAAa,GAAG,EAAsD,CAAA,IAAA,EAAA,CAAA;wBAE5E,OAAO,CAAA,CAAA,aAAA;AACH,gCAAA,GAAG,EAAE,aAAa;gCAClB,OAAO,EAAE,WAAW,CAAC,EAAE;6BAC1B,CAAC,CAAA;;;;AACL,KAAA,CAAA;AAED;;;;;AAKG;AACG,IAAA,iBAAA,CAAA,SAAA,CAAA,YAAY,GAAlB,UAAmB,WAAmB,EAAE,KAAa,EAAE,OAAoC,EAAA;;;gBACvF,OAAO,CAAA,CAAA,aAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAA;;;AACxD,KAAA,CAAA;AAED;;;;;;;AAOG;IACG,iBAAW,CAAA,SAAA,CAAA,WAAA,GAAjB,UAAkB,OAAe,EAAE,KAAa,EAAE,OAAoC,EAAE,MAAe,EAAA;;;;;;AAG3F,wBAAA,qBAAqB,GAA8C,OAAO,CAAA,qBAArD,EAAE,kBAAkB,GAA0B,OAAO,CAAjC,kBAAA,EAAE,SAAS,GAAe,OAAO,CAAtB,SAAA,EAAE,QAAQ,GAAK,OAAO,SAAZ,CAAa;AAE7E,wBAAA,iBAAiB,GAAG,CAAC,kBAAkB,IAAI,IAAI,SAAS,CAAC,kBAAkB,CAAC,GAAG,SAAS,CAAC;wBACzF,qBAAqB,GAAG,iBAAiB,KAAjB,IAAA,IAAA,iBAAiB,uBAAjB,iBAAiB,CAAE,gBAAgB,EAAE,CAAC;wBAC7D,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CACjC,QAAA,CAAA,EAAA,EAAE,EAAE,OAAO,EACX,EAAE,EAAE,SAAS,CAAC,UAAU,EAAE,EAC1B,CAAC,EAAE,qBAAqB,KAAA,IAAA,IAArB,qBAAqB,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAArB,qBAAqB,CAAE,WAAW,IACrC,CAAC,EAAE,qBAAqB,KAArB,IAAA,IAAA,qBAAqB,KAArB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,qBAAqB,CAAE,eAAe,EACzC,KAAK,EAAE,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EACnD,CAAC,EAAE,qBAAqB,KAAA,IAAA,IAArB,qBAAqB,KAArB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,qBAAqB,CAAE,YAAY,EACtC,CAAC,EAAE,CAAC,qBAAqB,KAArB,IAAA,IAAA,qBAAqB,uBAArB,qBAAqB,CAAE,WAAW,IAAI,CAAC,EAAE,EAAE,qBAAqB,CAAC,WAAW,CAAC,GAAG,SAAS,EAC7F,aAAa,EAAE,SAAS,IAAI,SAAS,EAAA,EAClC,MAAM,CACV,EAAA,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,CAAA,CAAA;AAVhC,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAUyB,CAAC,CAAA;;;;AACpC,KAAA,CAAA;IACL,OAAC,iBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}