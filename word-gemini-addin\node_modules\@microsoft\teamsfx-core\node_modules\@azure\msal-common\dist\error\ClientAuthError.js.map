{"version": 3, "file": "ClientAuthError.js", "sources": ["../../src/error/ClientAuthError.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthError } from \"./AuthError\";\r\n\r\n/**\r\n * ClientAuthErrorMessage class containing string constants used by error codes and messages.\r\n */\r\nexport const ClientAuthErrorMessage = {\r\n    clientInfoDecodingError: {\r\n        code: \"client_info_decoding_error\",\r\n        desc: \"The client info could not be parsed/decoded correctly. Please review the trace to determine the root cause.\"\r\n    },\r\n    clientInfoEmptyError: {\r\n        code: \"client_info_empty_error\",\r\n        desc: \"The client info was empty. Please review the trace to determine the root cause.\"\r\n    },\r\n    tokenParsingError: {\r\n        code: \"token_parsing_error\",\r\n        desc: \"Token cannot be parsed. Please review stack trace to determine root cause.\"\r\n    },\r\n    nullOrEmptyToken: {\r\n        code: \"null_or_empty_token\",\r\n        desc: \"The token is null or empty. Please review the trace to determine the root cause.\"\r\n    },\r\n    endpointResolutionError: {\r\n        code: \"endpoints_resolution_error\",\r\n        desc: \"Error: could not resolve endpoints. Please check network and try again.\"\r\n    },\r\n    networkError: {\r\n        code: \"network_error\",\r\n        desc: \"Network request failed. Please check network trace to determine root cause.\"\r\n    },\r\n    unableToGetOpenidConfigError: {\r\n        code: \"openid_config_error\",\r\n        desc: \"Could not retrieve endpoints. Check your authority and verify the .well-known/openid-configuration endpoint returns the required endpoints.\"\r\n    },\r\n    hashNotDeserialized: {\r\n        code: \"hash_not_deserialized\",\r\n        desc: \"The hash parameters could not be deserialized. Please review the trace to determine the root cause.\"\r\n    },\r\n    blankGuidGenerated: {\r\n        code: \"blank_guid_generated\",\r\n        desc: \"The guid generated was blank. Please review the trace to determine the root cause.\"\r\n    },\r\n    invalidStateError: {\r\n        code: \"invalid_state\",\r\n        desc: \"State was not the expected format. Please check the logs to determine whether the request was sent using ProtocolUtils.setRequestState().\"\r\n    },\r\n    stateMismatchError: {\r\n        code: \"state_mismatch\",\r\n        desc: \"State mismatch error. Please check your network. Continued requests may cause cache overflow.\"\r\n    },\r\n    stateNotFoundError: {\r\n        code: \"state_not_found\",\r\n        desc: \"State not found\"\r\n    },\r\n    nonceMismatchError: {\r\n        code: \"nonce_mismatch\",\r\n        desc: \"Nonce mismatch error. This may be caused by a race condition in concurrent requests.\"\r\n    },\r\n    nonceNotFoundError: {\r\n        code: \"nonce_not_found\",\r\n        desc: \"nonce not found\"\r\n    },\r\n    authTimeNotFoundError: {\r\n        code: \"auth_time_not_found\",\r\n        desc: \"Max Age was requested and the ID token is missing the auth_time variable.\" +\r\n            \" auth_time is an optional claim and is not enabled by default - it must be enabled.\" +\r\n            \" See https://aka.ms/msaljs/optional-claims for more information.\"\r\n    },\r\n    maxAgeTranspiredError: {\r\n        code: \"max_age_transpired\",\r\n        desc: \"Max Age is set to 0, or too much time has elapsed since the last end-user authentication.\"\r\n    },\r\n    noTokensFoundError: {\r\n        code: \"no_tokens_found\",\r\n        desc: \"No tokens were found for the given scopes, and no authorization code was passed to acquireToken. You must retrieve an authorization code before making a call to acquireToken().\"\r\n    },\r\n    multipleMatchingTokens: {\r\n        code: \"multiple_matching_tokens\",\r\n        desc: \"The cache contains multiple tokens satisfying the requirements. \" +\r\n            \"Call AcquireToken again providing more requirements such as authority or account.\"\r\n    },\r\n    multipleMatchingAccounts: {\r\n        code: \"multiple_matching_accounts\",\r\n        desc: \"The cache contains multiple accounts satisfying the given parameters. Please pass more info to obtain the correct account\"\r\n    },\r\n    multipleMatchingAppMetadata: {\r\n        code: \"multiple_matching_appMetadata\",\r\n        desc: \"The cache contains multiple appMetadata satisfying the given parameters. Please pass more info to obtain the correct appMetadata\"\r\n    },\r\n    tokenRequestCannotBeMade: {\r\n        code: \"request_cannot_be_made\",\r\n        desc: \"Token request cannot be made without authorization code or refresh token.\"\r\n    },\r\n    appendEmptyScopeError: {\r\n        code: \"cannot_append_empty_scope\",\r\n        desc: \"Cannot append null or empty scope to ScopeSet. Please check the stack trace for more info.\"\r\n    },\r\n    removeEmptyScopeError: {\r\n        code: \"cannot_remove_empty_scope\",\r\n        desc: \"Cannot remove null or empty scope from ScopeSet. Please check the stack trace for more info.\"\r\n    },\r\n    appendScopeSetError: {\r\n        code: \"cannot_append_scopeset\",\r\n        desc: \"Cannot append ScopeSet due to error.\"\r\n    },\r\n    emptyInputScopeSetError: {\r\n        code: \"empty_input_scopeset\",\r\n        desc: \"Empty input ScopeSet cannot be processed.\"\r\n    },\r\n    DeviceCodePollingCancelled: {\r\n        code: \"device_code_polling_cancelled\",\r\n        desc: \"Caller has cancelled token endpoint polling during device code flow by setting DeviceCodeRequest.cancel = true.\"\r\n    },\r\n    DeviceCodeExpired: {\r\n        code: \"device_code_expired\",\r\n        desc: \"Device code is expired.\"\r\n    },\r\n    DeviceCodeUnknownError: {\r\n        code: \"device_code_unknown_error\",\r\n        desc: \"Device code stopped polling for unknown reasons.\"\r\n    },\r\n    NoAccountInSilentRequest: {\r\n        code: \"no_account_in_silent_request\",\r\n        desc: \"Please pass an account object, silent flow is not supported without account information\"\r\n    },\r\n    invalidCacheRecord: {\r\n        code: \"invalid_cache_record\",\r\n        desc: \"Cache record object was null or undefined.\"\r\n    },\r\n    invalidCacheEnvironment: {\r\n        code: \"invalid_cache_environment\",\r\n        desc: \"Invalid environment when attempting to create cache entry\"\r\n    },\r\n    noAccountFound: {\r\n        code: \"no_account_found\",\r\n        desc: \"No account found in cache for given key.\"\r\n    },\r\n    CachePluginError: {\r\n        code: \"no cache plugin set on CacheManager\",\r\n        desc: \"ICachePlugin needs to be set before using readFromStorage or writeFromStorage\"\r\n    },\r\n    noCryptoObj: {\r\n        code: \"no_crypto_object\",\r\n        desc: \"No crypto object detected. This is required for the following operation: \"\r\n    },\r\n    invalidCacheType: {\r\n        code: \"invalid_cache_type\",\r\n        desc: \"Invalid cache type\"\r\n    },\r\n    unexpectedAccountType: {\r\n        code: \"unexpected_account_type\",\r\n        desc: \"Unexpected account type.\"\r\n    },\r\n    unexpectedCredentialType: {\r\n        code: \"unexpected_credential_type\",\r\n        desc: \"Unexpected credential type.\"\r\n    },\r\n    invalidAssertion: {\r\n        code: \"invalid_assertion\",\r\n        desc: \"Client assertion must meet requirements described in https://tools.ietf.org/html/rfc7515\"\r\n    },\r\n    invalidClientCredential: {\r\n        code: \"invalid_client_credential\",\r\n        desc: \"Client credential (secret, certificate, or assertion) must not be empty when creating a confidential client. An application should at most have one credential\"\r\n    },\r\n    tokenRefreshRequired: {\r\n        code: \"token_refresh_required\",\r\n        desc: \"Cannot return token from cache because it must be refreshed. This may be due to one of the following reasons: forceRefresh parameter is set to true, claims have been requested, there is no cached access token or it is expired.\"\r\n    },\r\n    userTimeoutReached: {\r\n        code: \"user_timeout_reached\",\r\n        desc: \"User defined timeout for device code polling reached\",\r\n    },\r\n    tokenClaimsRequired: {\r\n        code: \"token_claims_cnf_required_for_signedjwt\",\r\n        desc: \"Cannot generate a POP jwt if the token_claims are not populated\"\r\n    },\r\n    noAuthorizationCodeFromServer: {\r\n        code: \"authorization_code_missing_from_server_response\",\r\n        desc: \"Server response does not contain an authorization code to proceed\"\r\n    },\r\n    noAzureRegionDetected: {\r\n        code: \"no_azure_region_detected\",\r\n        desc: \"No azure region was detected and no fallback was made available\"\r\n    },\r\n    accessTokenEntityNullError: {\r\n        code: \"access_token_entity_null\",\r\n        desc: \"Access token entity is null, please check logs and cache to ensure a valid access token is present.\"\r\n    },\r\n    bindingKeyNotRemovedError: {\r\n        code: \"binding_key_not_removed\",\r\n        desc: \"Could not remove the credential's binding key from storage.\"\r\n    },\r\n    logoutNotSupported: {\r\n        code: \"end_session_endpoint_not_supported\",\r\n        desc: \"Provided authority does not support logout.\"\r\n    },\r\n    keyIdMissing: {\r\n        code: \"key_id_missing\",\r\n        desc: \"A keyId value is missing from the requested bound token's cache record and is required to match the token to it's stored binding key.\"\r\n    },\r\n    noNetworkConnectivity: {\r\n        code: \"no_network_connectivity\",\r\n        desc: \"No network connectivity. Check your internet connection.\"\r\n    },\r\n    userCanceledError: {\r\n        code: \"user_canceled\",\r\n        desc: \"User canceled the flow.\"\r\n    }\r\n};\r\n\r\n/**\r\n * Error thrown when there is an error in the client code running on the browser.\r\n */\r\nexport class ClientAuthError extends AuthError {\r\n\r\n    constructor(errorCode: string, errorMessage?: string) {\r\n        super(errorCode, errorMessage);\r\n        this.name = \"ClientAuthError\";\r\n\r\n        Object.setPrototypeOf(this, ClientAuthError.prototype);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when client info object doesn't decode correctly.\r\n     * @param caughtError\r\n     */\r\n    static createClientInfoDecodingError(caughtError: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.clientInfoDecodingError.code,\r\n            `${ClientAuthErrorMessage.clientInfoDecodingError.desc} Failed with error: ${caughtError}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if the client info is empty.\r\n     * @param rawClientInfo\r\n     */\r\n    static createClientInfoEmptyError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.clientInfoEmptyError.code,\r\n            `${ClientAuthErrorMessage.clientInfoEmptyError.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the id token extraction errors out.\r\n     * @param err\r\n     */\r\n    static createTokenParsingError(caughtExtractionError: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.tokenParsingError.code,\r\n            `${ClientAuthErrorMessage.tokenParsingError.desc} Failed with error: ${caughtExtractionError}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the id token string is null or empty.\r\n     * @param invalidRawTokenString\r\n     */\r\n    static createTokenNullOrEmptyError(invalidRawTokenString: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.nullOrEmptyToken.code,\r\n            `${ClientAuthErrorMessage.nullOrEmptyToken.desc} Raw Token Value: ${invalidRawTokenString}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the endpoint discovery doesn't complete correctly.\r\n     */\r\n    static createEndpointDiscoveryIncompleteError(errDetail: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.endpointResolutionError.code,\r\n            `${ClientAuthErrorMessage.endpointResolutionError.desc} Detail: ${errDetail}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the fetch client throws\r\n     */\r\n    static createNetworkError(endpoint: string, errDetail: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.networkError.code,\r\n            `${ClientAuthErrorMessage.networkError.desc} | Fetch client threw: ${errDetail} | Attempted to reach: ${endpoint.split(\"?\")[0]}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the openid-configuration endpoint cannot be reached or does not contain the required data\r\n     */\r\n    static createUnableToGetOpenidConfigError(errDetail: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.unableToGetOpenidConfigError.code,\r\n            `${ClientAuthErrorMessage.unableToGetOpenidConfigError.desc} Attempted to retrieve endpoints from: ${errDetail}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the hash cannot be deserialized.\r\n     * @param hashParamObj\r\n     */\r\n    static createHashNotDeserializedError(hashParamObj: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.hashNotDeserialized.code,\r\n            `${ClientAuthErrorMessage.hashNotDeserialized.desc} Given Object: ${hashParamObj}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the state cannot be parsed.\r\n     * @param invalidState\r\n     */\r\n    static createInvalidStateError(invalidState: string, errorString?: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.invalidStateError.code,\r\n            `${ClientAuthErrorMessage.invalidStateError.desc} Invalid State: ${invalidState}, Root Err: ${errorString}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when two states do not match.\r\n     */\r\n    static createStateMismatchError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.stateMismatchError.code,\r\n            ClientAuthErrorMessage.stateMismatchError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the state is not present\r\n     * @param missingState\r\n     */\r\n    static createStateNotFoundError(missingState: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.stateNotFoundError.code,\r\n            `${ClientAuthErrorMessage.stateNotFoundError.desc}:  ${missingState}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the nonce does not match.\r\n     */\r\n    static createNonceMismatchError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.nonceMismatchError.code,\r\n            ClientAuthErrorMessage.nonceMismatchError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when max_age was provided in the request, but auth_time is not in the token claims\r\n     * @param missingNonce\r\n     */\r\n    static createAuthTimeNotFoundError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.authTimeNotFoundError.code,\r\n            ClientAuthErrorMessage.authTimeNotFoundError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when too much time has elapsed since the last end-user authentication\r\n     */\r\n    static createMaxAgeTranspiredError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.maxAgeTranspiredError.code,\r\n            ClientAuthErrorMessage.maxAgeTranspiredError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the mnonce is not present\r\n     * @param missingNonce\r\n     */\r\n    static createNonceNotFoundError(missingNonce: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.nonceNotFoundError.code,\r\n            `${ClientAuthErrorMessage.nonceNotFoundError.desc}:  ${missingNonce}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error when multiple tokens are in cache.\r\n     */\r\n    static createMultipleMatchingTokensInCacheError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.multipleMatchingTokens.code,\r\n            `${ClientAuthErrorMessage.multipleMatchingTokens.desc}.`);\r\n    }\r\n\r\n    /**\r\n     * Throws error when multiple accounts are in cache for the given params\r\n     */\r\n    static createMultipleMatchingAccountsInCacheError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.multipleMatchingAccounts.code,\r\n            ClientAuthErrorMessage.multipleMatchingAccounts.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when multiple appMetada are in cache for the given clientId.\r\n     */\r\n    static createMultipleMatchingAppMetadataInCacheError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.multipleMatchingAppMetadata.code,\r\n            ClientAuthErrorMessage.multipleMatchingAppMetadata.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when no auth code or refresh token is given to ServerTokenRequestParameters.\r\n     */\r\n    static createTokenRequestCannotBeMadeError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.tokenRequestCannotBeMade.code, ClientAuthErrorMessage.tokenRequestCannotBeMade.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when attempting to append a null, undefined or empty scope to a set\r\n     * @param givenScope\r\n     */\r\n    static createAppendEmptyScopeToSetError(givenScope: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.appendEmptyScopeError.code, `${ClientAuthErrorMessage.appendEmptyScopeError.desc} Given Scope: ${givenScope}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error when attempting to append a null, undefined or empty scope to a set\r\n     * @param givenScope\r\n     */\r\n    static createRemoveEmptyScopeFromSetError(givenScope: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.removeEmptyScopeError.code, `${ClientAuthErrorMessage.removeEmptyScopeError.desc} Given Scope: ${givenScope}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error when attempting to append null or empty ScopeSet.\r\n     * @param appendError\r\n     */\r\n    static createAppendScopeSetError(appendError: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.appendScopeSetError.code, `${ClientAuthErrorMessage.appendScopeSetError.desc} Detail Error: ${appendError}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if ScopeSet is null or undefined.\r\n     * @param givenScopeSet\r\n     */\r\n    static createEmptyInputScopeSetError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.emptyInputScopeSetError.code, `${ClientAuthErrorMessage.emptyInputScopeSetError.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if user sets CancellationToken.cancel = true during polling of token endpoint during device code flow\r\n     */\r\n    static createDeviceCodeCancelledError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.DeviceCodePollingCancelled.code, `${ClientAuthErrorMessage.DeviceCodePollingCancelled.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if device code is expired\r\n     */\r\n    static createDeviceCodeExpiredError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.DeviceCodeExpired.code, `${ClientAuthErrorMessage.DeviceCodeExpired.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if device code is expired\r\n     */\r\n    static createDeviceCodeUnknownError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.DeviceCodeUnknownError.code, `${ClientAuthErrorMessage.DeviceCodeUnknownError.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error when silent requests are made without an account object\r\n     */\r\n    static createNoAccountInSilentRequestError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.NoAccountInSilentRequest.code, `${ClientAuthErrorMessage.NoAccountInSilentRequest.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error when cache record is null or undefined.\r\n     */\r\n    static createNullOrUndefinedCacheRecord(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.invalidCacheRecord.code, ClientAuthErrorMessage.invalidCacheRecord.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when provided environment is not part of the CloudDiscoveryMetadata object\r\n     */\r\n    static createInvalidCacheEnvironmentError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.invalidCacheEnvironment.code, ClientAuthErrorMessage.invalidCacheEnvironment.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when account is not found in cache.\r\n     */\r\n    static createNoAccountFoundError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.noAccountFound.code, ClientAuthErrorMessage.noAccountFound.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error if ICachePlugin not set on CacheManager.\r\n     */\r\n    static createCachePluginError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.CachePluginError.code, `${ClientAuthErrorMessage.CachePluginError.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if crypto object not found.\r\n     * @param operationName\r\n     */\r\n    static createNoCryptoObjectError(operationName: string): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.noCryptoObj.code, `${ClientAuthErrorMessage.noCryptoObj.desc}${operationName}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if cache type is invalid.\r\n     */\r\n    static createInvalidCacheTypeError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.invalidCacheType.code, `${ClientAuthErrorMessage.invalidCacheType.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if unexpected account type.\r\n     */\r\n    static createUnexpectedAccountTypeError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.unexpectedAccountType.code, `${ClientAuthErrorMessage.unexpectedAccountType.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if unexpected credential type.\r\n     */\r\n    static createUnexpectedCredentialTypeError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.unexpectedCredentialType.code, `${ClientAuthErrorMessage.unexpectedCredentialType.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if client assertion is not valid.\r\n     */\r\n    static createInvalidAssertionError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.invalidAssertion.code, `${ClientAuthErrorMessage.invalidAssertion.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if client assertion is not valid.\r\n     */\r\n    static createInvalidCredentialError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.invalidClientCredential.code, `${ClientAuthErrorMessage.invalidClientCredential.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Throws error if token cannot be retrieved from cache due to refresh being required.\r\n     */\r\n    static createRefreshRequiredError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.tokenRefreshRequired.code, ClientAuthErrorMessage.tokenRefreshRequired.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error if the user defined timeout is reached.\r\n     */\r\n    static createUserTimeoutReachedError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.userTimeoutReached.code, ClientAuthErrorMessage.userTimeoutReached.desc);\r\n    }\r\n\r\n    /*\r\n     * Throws error if token claims are not populated for a signed jwt generation\r\n     */\r\n    static createTokenClaimsRequiredError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.tokenClaimsRequired.code, ClientAuthErrorMessage.tokenClaimsRequired.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when the authorization code is missing from the server response\r\n     */\r\n    static createNoAuthCodeInServerResponseError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.noAuthorizationCodeFromServer.code, ClientAuthErrorMessage.noAuthorizationCodeFromServer.desc);\r\n    }\r\n\r\n    static createBindingKeyNotRemovedError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.bindingKeyNotRemovedError.code, ClientAuthErrorMessage.bindingKeyNotRemovedError.desc);\r\n    }\r\n\r\n    /**\r\n     * Thrown when logout is attempted for an authority that doesnt have an end_session_endpoint\r\n     */\r\n    static createLogoutNotSupportedError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.logoutNotSupported.code, ClientAuthErrorMessage.logoutNotSupported.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error when kid attribute is missing from a PoP token's cache record\r\n     */\r\n    static createKeyIdMissingError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.keyIdMissing.code, ClientAuthErrorMessage.keyIdMissing.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error when the client does not have network connectivity\r\n     */\r\n    static createNoNetworkConnectivityError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.noNetworkConnectivity.code, ClientAuthErrorMessage.noNetworkConnectivity.desc);\r\n    }\r\n\r\n    /**\r\n     * Create an error when the user cancels the flow\r\n     */\r\n    static createUserCanceledError(): ClientAuthError {\r\n        return new ClientAuthError(ClientAuthErrorMessage.userCanceledError.code, ClientAuthErrorMessage.userCanceledError.desc);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAIH;;AAEG;AACU,IAAA,sBAAsB,GAAG;AAClC,IAAA,uBAAuB,EAAE;AACrB,QAAA,IAAI,EAAE,4BAA4B;AAClC,QAAA,IAAI,EAAE,6GAA6G;AACtH,KAAA;AACD,IAAA,oBAAoB,EAAE;AAClB,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,IAAI,EAAE,iFAAiF;AAC1F,KAAA;AACD,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,4EAA4E;AACrF,KAAA;AACD,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,kFAAkF;AAC3F,KAAA;AACD,IAAA,uBAAuB,EAAE;AACrB,QAAA,IAAI,EAAE,4BAA4B;AAClC,QAAA,IAAI,EAAE,yEAAyE;AAClF,KAAA;AACD,IAAA,YAAY,EAAE;AACV,QAAA,IAAI,EAAE,eAAe;AACrB,QAAA,IAAI,EAAE,6EAA6E;AACtF,KAAA;AACD,IAAA,4BAA4B,EAAE;AAC1B,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,6IAA6I;AACtJ,KAAA;AACD,IAAA,mBAAmB,EAAE;AACjB,QAAA,IAAI,EAAE,uBAAuB;AAC7B,QAAA,IAAI,EAAE,qGAAqG;AAC9G,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,IAAI,EAAE,oFAAoF;AAC7F,KAAA;AACD,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,eAAe;AACrB,QAAA,IAAI,EAAE,2IAA2I;AACpJ,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,gBAAgB;AACtB,QAAA,IAAI,EAAE,+FAA+F;AACxG,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,IAAI,EAAE,iBAAiB;AAC1B,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,gBAAgB;AACtB,QAAA,IAAI,EAAE,sFAAsF;AAC/F,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,IAAI,EAAE,iBAAiB;AAC1B,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,2EAA2E;YAC7E,qFAAqF;YACrF,kEAAkE;AACzE,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,2FAA2F;AACpG,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,IAAI,EAAE,kLAAkL;AAC3L,KAAA;AACD,IAAA,sBAAsB,EAAE;AACpB,QAAA,IAAI,EAAE,0BAA0B;AAChC,QAAA,IAAI,EAAE,kEAAkE;YACpE,mFAAmF;AAC1F,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,4BAA4B;AAClC,QAAA,IAAI,EAAE,2HAA2H;AACpI,KAAA;AACD,IAAA,2BAA2B,EAAE;AACzB,QAAA,IAAI,EAAE,+BAA+B;AACrC,QAAA,IAAI,EAAE,kIAAkI;AAC3I,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,wBAAwB;AAC9B,QAAA,IAAI,EAAE,2EAA2E;AACpF,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,2BAA2B;AACjC,QAAA,IAAI,EAAE,4FAA4F;AACrG,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,2BAA2B;AACjC,QAAA,IAAI,EAAE,8FAA8F;AACvG,KAAA;AACD,IAAA,mBAAmB,EAAE;AACjB,QAAA,IAAI,EAAE,wBAAwB;AAC9B,QAAA,IAAI,EAAE,sCAAsC;AAC/C,KAAA;AACD,IAAA,uBAAuB,EAAE;AACrB,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,IAAI,EAAE,2CAA2C;AACpD,KAAA;AACD,IAAA,0BAA0B,EAAE;AACxB,QAAA,IAAI,EAAE,+BAA+B;AACrC,QAAA,IAAI,EAAE,iHAAiH;AAC1H,KAAA;AACD,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,yBAAyB;AAClC,KAAA;AACD,IAAA,sBAAsB,EAAE;AACpB,QAAA,IAAI,EAAE,2BAA2B;AACjC,QAAA,IAAI,EAAE,kDAAkD;AAC3D,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,8BAA8B;AACpC,QAAA,IAAI,EAAE,yFAAyF;AAClG,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,IAAI,EAAE,4CAA4C;AACrD,KAAA;AACD,IAAA,uBAAuB,EAAE;AACrB,QAAA,IAAI,EAAE,2BAA2B;AACjC,QAAA,IAAI,EAAE,2DAA2D;AACpE,KAAA;AACD,IAAA,cAAc,EAAE;AACZ,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,IAAI,EAAE,0CAA0C;AACnD,KAAA;AACD,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,qCAAqC;AAC3C,QAAA,IAAI,EAAE,+EAA+E;AACxF,KAAA;AACD,IAAA,WAAW,EAAE;AACT,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,IAAI,EAAE,2EAA2E;AACpF,KAAA;AACD,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,oBAAoB;AAC7B,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,IAAI,EAAE,0BAA0B;AACnC,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,4BAA4B;AAClC,QAAA,IAAI,EAAE,6BAA6B;AACtC,KAAA;AACD,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,mBAAmB;AACzB,QAAA,IAAI,EAAE,0FAA0F;AACnG,KAAA;AACD,IAAA,uBAAuB,EAAE;AACrB,QAAA,IAAI,EAAE,2BAA2B;AACjC,QAAA,IAAI,EAAE,gKAAgK;AACzK,KAAA;AACD,IAAA,oBAAoB,EAAE;AAClB,QAAA,IAAI,EAAE,wBAAwB;AAC9B,QAAA,IAAI,EAAE,oOAAoO;AAC7O,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,IAAI,EAAE,sDAAsD;AAC/D,KAAA;AACD,IAAA,mBAAmB,EAAE;AACjB,QAAA,IAAI,EAAE,yCAAyC;AAC/C,QAAA,IAAI,EAAE,iEAAiE;AAC1E,KAAA;AACD,IAAA,6BAA6B,EAAE;AAC3B,QAAA,IAAI,EAAE,iDAAiD;AACvD,QAAA,IAAI,EAAE,mEAAmE;AAC5E,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,0BAA0B;AAChC,QAAA,IAAI,EAAE,iEAAiE;AAC1E,KAAA;AACD,IAAA,0BAA0B,EAAE;AACxB,QAAA,IAAI,EAAE,0BAA0B;AAChC,QAAA,IAAI,EAAE,qGAAqG;AAC9G,KAAA;AACD,IAAA,yBAAyB,EAAE;AACvB,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,IAAI,EAAE,6DAA6D;AACtE,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,oCAAoC;AAC1C,QAAA,IAAI,EAAE,6CAA6C;AACtD,KAAA;AACD,IAAA,YAAY,EAAE;AACV,QAAA,IAAI,EAAE,gBAAgB;AACtB,QAAA,IAAI,EAAE,uIAAuI;AAChJ,KAAA;AACD,IAAA,qBAAqB,EAAE;AACnB,QAAA,IAAI,EAAE,yBAAyB;AAC/B,QAAA,IAAI,EAAE,0DAA0D;AACnE,KAAA;AACD,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,eAAe;AACrB,QAAA,IAAI,EAAE,yBAAyB;AAClC,KAAA;EACH;AAEF;;AAEG;AACH,IAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;IAAqC,SAAS,CAAA,eAAA,EAAA,MAAA,CAAA,CAAA;IAE1C,SAAY,eAAA,CAAA,SAAiB,EAAE,YAAqB,EAAA;AAApD,QAAA,IAAA,KAAA,GACI,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,SAAS,EAAE,YAAY,CAAC,IAIjC,IAAA,CAAA;AAHG,QAAA,KAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAE9B,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;;KAC1D;AAED;;;AAGG;IACI,eAA6B,CAAA,6BAAA,GAApC,UAAqC,WAAmB,EAAA;AACpD,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,EACvE,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,GAAuB,sBAAA,GAAA,WAAa,CAAC,CAAC;KACnG,CAAA;AAED;;;AAGG;AACI,IAAA,eAAA,CAAA,0BAA0B,GAAjC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,IAAI,EACvE,EAAA,GAAG,sBAAsB,CAAC,oBAAoB,CAAC,IAAM,CAAC,CAAC;KAC9D,CAAA;AAED;;;AAGG;IACI,eAAuB,CAAA,uBAAA,GAA9B,UAA+B,qBAA6B,EAAA;AACxD,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,EACjE,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,GAAuB,sBAAA,GAAA,qBAAuB,CAAC,CAAC;KACvG,CAAA;AAED;;;AAGG;IACI,eAA2B,CAAA,2BAAA,GAAlC,UAAmC,qBAA6B,EAAA;AAC5D,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,EAChE,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,GAAqB,oBAAA,GAAA,qBAAuB,CAAC,CAAC;KACpG,CAAA;AAED;;AAEG;IACI,eAAsC,CAAA,sCAAA,GAA7C,UAA8C,SAAiB,EAAA;AAC3D,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,EACvE,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,GAAY,WAAA,GAAA,SAAW,CAAC,CAAC;KACtF,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,kBAAkB,GAAzB,UAA0B,QAAgB,EAAE,SAAiB,EAAA;QACzD,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,YAAY,CAAC,IAAI,EAC5D,sBAAsB,CAAC,YAAY,CAAC,IAAI,GAAA,yBAAA,GAA0B,SAAS,GAAA,yBAAA,GAA0B,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAG,CAAC,CAAC;KACzI,CAAA;AAED;;AAEG;IACI,eAAkC,CAAA,kCAAA,GAAzC,UAA0C,SAAiB,EAAA;AACvD,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,IAAI,EAC5E,sBAAsB,CAAC,4BAA4B,CAAC,IAAI,GAA0C,yCAAA,GAAA,SAAW,CAAC,CAAC;KACzH,CAAA;AAED;;;AAGG;IACI,eAA8B,CAAA,8BAAA,GAArC,UAAsC,YAAoB,EAAA;AACtD,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,IAAI,EACnE,sBAAsB,CAAC,mBAAmB,CAAC,IAAI,GAAkB,iBAAA,GAAA,YAAc,CAAC,CAAC;KAC3F,CAAA;AAED;;;AAGG;AACI,IAAA,eAAA,CAAA,uBAAuB,GAA9B,UAA+B,YAAoB,EAAE,WAAoB,EAAA;AACrE,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,EACjE,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,GAAA,kBAAA,GAAmB,YAAY,GAAe,cAAA,GAAA,WAAa,CAAC,CAAC;KACpH,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,wBAAwB,GAA/B,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,EACrE,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;KACvD,CAAA;AAED;;;AAGG;IACI,eAAwB,CAAA,wBAAA,GAA/B,UAAgC,YAAoB,EAAA;AAChD,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,EAClE,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,GAAM,KAAA,GAAA,YAAc,CAAC,CAAC;KAC9E,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,wBAAwB,GAA/B,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,EACrE,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;KACvD,CAAA;AAED;;;AAGG;AACI,IAAA,eAAA,CAAA,2BAA2B,GAAlC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,EACxE,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;KAC1D,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,2BAA2B,GAAlC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,EACxE,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;KAC1D,CAAA;AAED;;;AAGG;IACI,eAAwB,CAAA,wBAAA,GAA/B,UAAgC,YAAoB,EAAA;AAChD,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,EAClE,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,GAAM,KAAA,GAAA,YAAc,CAAC,CAAC;KAC9E,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,wCAAwC,GAA/C,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,IAAI,EACtE,sBAAsB,CAAC,sBAAsB,CAAC,IAAI,GAAA,GAAG,CAAC,CAAC;KACjE,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,0CAA0C,GAAjD,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,IAAI,EAC3E,sBAAsB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;KAC7D,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,6CAA6C,GAApD,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,IAAI,EAC9E,sBAAsB,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;KAChE,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,mCAAmC,GAA1C,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,IAAI,EAAE,sBAAsB,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;KAC1I,CAAA;AAED;;;AAGG;IACI,eAAgC,CAAA,gCAAA,GAAvC,UAAwC,UAAkB,EAAA;AACtD,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,EAAK,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,GAAiB,gBAAA,GAAA,UAAY,CAAC,CAAC;KACpK,CAAA;AAED;;;AAGG;IACI,eAAkC,CAAA,kCAAA,GAAzC,UAA0C,UAAkB,EAAA;AACxD,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,EAAK,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,GAAiB,gBAAA,GAAA,UAAY,CAAC,CAAC;KACpK,CAAA;AAED;;;AAGG;IACI,eAAyB,CAAA,yBAAA,GAAhC,UAAiC,WAAmB,EAAA;AAChD,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,IAAI,EAAK,sBAAsB,CAAC,mBAAmB,CAAC,IAAI,GAAkB,iBAAA,GAAA,WAAa,CAAC,CAAC;KAClK,CAAA;AAED;;;AAGG;AACI,IAAA,eAAA,CAAA,6BAA6B,GAApC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,uBAAuB,CAAC,IAAM,CAAC,CAAC;KAC7I,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,8BAA8B,GAArC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,0BAA0B,CAAC,IAAM,CAAC,CAAC;KACnJ,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,4BAA4B,GAAnC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,iBAAiB,CAAC,IAAM,CAAC,CAAC;KACjI,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,4BAA4B,GAAnC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,sBAAsB,CAAC,IAAM,CAAC,CAAC;KAC3I,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,mCAAmC,GAA1C,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,wBAAwB,CAAC,IAAM,CAAC,CAAC;KAC/I,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,gCAAgC,GAAvC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,EAAE,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;KAC9H,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,kCAAkC,GAAzC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,EAAE,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;KACxI,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,yBAAyB,GAAhC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,EAAE,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;KACtH,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,sBAAsB,GAA7B,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,IAAM,CAAC,CAAC;KAC/H,CAAA;AAED;;;AAGG;IACI,eAAyB,CAAA,yBAAA,GAAhC,UAAiC,aAAqB,EAAA;AAClD,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,WAAW,CAAC,IAAI,EAAE,EAAG,GAAA,sBAAsB,CAAC,WAAW,CAAC,IAAI,GAAG,aAAe,CAAC,CAAC;KACrI,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,2BAA2B,GAAlC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,IAAM,CAAC,CAAC;KAC/H,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,gCAAgC,GAAvC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,qBAAqB,CAAC,IAAM,CAAC,CAAC;KACzI,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,mCAAmC,GAA1C,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,wBAAwB,CAAC,IAAM,CAAC,CAAC;KAC/I,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,2BAA2B,GAAlC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,gBAAgB,CAAC,IAAM,CAAC,CAAC;KAC/H,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,4BAA4B,GAAnC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,IAAI,EAAE,EAAA,GAAG,sBAAsB,CAAC,uBAAuB,CAAC,IAAM,CAAC,CAAC;KAC7I,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,0BAA0B,GAAjC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,IAAI,EAAE,sBAAsB,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;KAClI,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,6BAA6B,GAApC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,EAAE,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;KAC9H,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,8BAA8B,GAArC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,IAAI,EAAE,sBAAsB,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;KAChI,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,qCAAqC,GAA5C,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,IAAI,EAAE,sBAAsB,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;KACpJ,CAAA;AAEM,IAAA,eAAA,CAAA,+BAA+B,GAAtC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,IAAI,EAAE,sBAAsB,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;KAC5I,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,6BAA6B,GAApC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,EAAE,sBAAsB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;KAC9H,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,uBAAuB,GAA9B,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,YAAY,CAAC,IAAI,EAAE,sBAAsB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;KAClH,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,gCAAgC,GAAvC,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,EAAE,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;KACpI,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,uBAAuB,GAA9B,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,EAAE,sBAAsB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KAC5H,CAAA;IACL,OAAC,eAAA,CAAA;AAAD,CAvWA,CAAqC,SAAS,CAuW7C;;;;"}