{"version": 3, "file": "ClientConfigurationError.js", "sources": ["../../src/error/ClientConfigurationError.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ClientAuthError } from \"./ClientAuthError\";\r\n\r\n/**\r\n * ClientConfigurationErrorMessage class containing string constants used by error codes and messages.\r\n */\r\nexport const ClientConfigurationErrorMessage = {\r\n    redirectUriNotSet: {\r\n        code: \"redirect_uri_empty\",\r\n        desc: \"A redirect URI is required for all calls, and none has been set.\"\r\n    },\r\n    postLogoutUriNotSet: {\r\n        code: \"post_logout_uri_empty\",\r\n        desc: \"A post logout redirect has not been set.\"\r\n    },\r\n    claimsRequestParsingError: {\r\n        code: \"claims_request_parsing_error\",\r\n        desc: \"Could not parse the given claims request object.\"\r\n    },\r\n    authorityUriInsecure: {\r\n        code: \"authority_uri_insecure\",\r\n        desc: \"Authority URIs must use https.  Please see here for valid authority configuration options: https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-initializing-client-applications#configuration-options\"\r\n    },\r\n    urlParseError: {\r\n        code: \"url_parse_error\",\r\n        desc: \"URL could not be parsed into appropriate segments.\"\r\n    },\r\n    urlEmptyError: {\r\n        code: \"empty_url_error\",\r\n        desc: \"URL was empty or null.\"\r\n    },\r\n    emptyScopesError: {\r\n        code: \"empty_input_scopes_error\",\r\n        desc: \"Scopes cannot be passed as null, undefined or empty array because they are required to obtain an access token.\"\r\n    },\r\n    nonArrayScopesError: {\r\n        code: \"nonarray_input_scopes_error\",\r\n        desc: \"Scopes cannot be passed as non-array.\"\r\n    },\r\n    clientIdSingleScopeError: {\r\n        code: \"clientid_input_scopes_error\",\r\n        desc: \"Client ID can only be provided as a single scope.\"\r\n    },\r\n    invalidPrompt: {\r\n        code: \"invalid_prompt_value\",\r\n        desc: \"Supported prompt values are 'login', 'select_account', 'consent', 'create', 'none' and 'no_session'.  Please see here for valid configuration options: https://azuread.github.io/microsoft-authentication-library-for-js/ref/modules/_azure_msal_common.html#commonauthorizationurlrequest\",\r\n    },\r\n    invalidClaimsRequest: {\r\n        code: \"invalid_claims\",\r\n        desc: \"Given claims parameter must be a stringified JSON object.\"\r\n    },\r\n    tokenRequestEmptyError: {\r\n        code: \"token_request_empty\",\r\n        desc: \"Token request was empty and not found in cache.\"\r\n    },\r\n    logoutRequestEmptyError: {\r\n        code: \"logout_request_empty\",\r\n        desc: \"The logout request was null or undefined.\"\r\n    },\r\n    invalidCodeChallengeMethod: {\r\n        code: \"invalid_code_challenge_method\",\r\n        desc: \"code_challenge_method passed is invalid. Valid values are \\\"plain\\\" and \\\"S256\\\".\"\r\n    },\r\n    invalidCodeChallengeParams: {\r\n        code: \"pkce_params_missing\",\r\n        desc: \"Both params: code_challenge and code_challenge_method are to be passed if to be sent in the request\"\r\n    },\r\n    invalidCloudDiscoveryMetadata: {\r\n        code: \"invalid_cloud_discovery_metadata\",\r\n        desc: \"Invalid cloudDiscoveryMetadata provided. Must be a stringified JSON object containing tenant_discovery_endpoint and metadata fields\"\r\n    },\r\n    invalidAuthorityMetadata: {\r\n        code: \"invalid_authority_metadata\",\r\n        desc: \"Invalid authorityMetadata provided. Must by a stringified JSON object containing authorization_endpoint, token_endpoint, issuer fields.\"\r\n    },\r\n    untrustedAuthority: {\r\n        code: \"untrusted_authority\",\r\n        desc: \"The provided authority is not a trusted authority. Please include this authority in the knownAuthorities config parameter.\"\r\n    },\r\n    invalidAzureCloudInstance: {\r\n        code: \"invalid_azure_cloud_instance\",\r\n        desc: \"Invalid AzureCloudInstance provided. Please refer MSAL JS docs: aks.ms/msaljs/azure_cloud_instance for valid values\"\r\n    },\r\n    missingSshJwk: {\r\n        code: \"missing_ssh_jwk\",\r\n        desc: \"Missing sshJwk in SSH certificate request. A stringified JSON Web Key is required when using the SSH authentication scheme.\"\r\n    },\r\n    missingSshKid: {\r\n        code: \"missing_ssh_kid\",\r\n        desc: \"Missing sshKid in SSH certificate request. A string that uniquely identifies the public SSH key is required when using the SSH authentication scheme.\"\r\n    },\r\n    missingNonceAuthenticationHeader: {\r\n        code: \"missing_nonce_authentication_header\",\r\n        desc: \"Unable to find an authentication header containing server nonce. Either the Authentication-Info or WWW-Authenticate headers must be present in order to obtain a server nonce.\"\r\n    },\r\n    invalidAuthenticationHeader: {\r\n        code: \"invalid_authentication_header\",\r\n        desc: \"Invalid authentication header provided\"\r\n    },\r\n    authorityMismatch: {\r\n        code: \"authority_mismatch\",\r\n        desc: \"Authority mismatch error. Authority provided in login request or PublicClientApplication config does not match the environment of the provided account. Please use a matching account or make an interactive request to login to this authority.\"\r\n    }\r\n};\r\n\r\n/**\r\n * Error thrown when there is an error in configuration of the MSAL.js library.\r\n */\r\nexport class ClientConfigurationError extends ClientAuthError {\r\n\r\n    constructor(errorCode: string, errorMessage?: string) {\r\n        super(errorCode, errorMessage);\r\n        this.name = \"ClientConfigurationError\";\r\n        Object.setPrototypeOf(this, ClientConfigurationError.prototype);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the redirect uri is empty (not set by caller)\r\n     */\r\n    static createRedirectUriEmptyError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.redirectUriNotSet.code,\r\n            ClientConfigurationErrorMessage.redirectUriNotSet.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the post-logout redirect uri is empty (not set by caller)\r\n     */\r\n    static createPostLogoutRedirectUriEmptyError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.postLogoutUriNotSet.code,\r\n            ClientConfigurationErrorMessage.postLogoutUriNotSet.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the claims request could not be successfully parsed\r\n     */\r\n    static createClaimsRequestParsingError(claimsRequestParseError: string): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.claimsRequestParsingError.code,\r\n            `${ClientConfigurationErrorMessage.claimsRequestParsingError.desc} Given value: ${claimsRequestParseError}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if authority uri is given an insecure protocol.\r\n     * @param urlString\r\n     */\r\n    static createInsecureAuthorityUriError(urlString: string): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.authorityUriInsecure.code,\r\n            `${ClientConfigurationErrorMessage.authorityUriInsecure.desc} Given URI: ${urlString}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if URL string does not parse into separate segments.\r\n     * @param urlString\r\n     */\r\n    static createUrlParseError(urlParseError: string): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.urlParseError.code,\r\n            `${ClientConfigurationErrorMessage.urlParseError.desc} Given Error: ${urlParseError}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if URL string is empty or null.\r\n     * @param urlString\r\n     */\r\n    static createUrlEmptyError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.urlEmptyError.code,\r\n            ClientConfigurationErrorMessage.urlEmptyError.desc);\r\n    }\r\n\r\n    /**\r\n     * Error thrown when scopes are empty.\r\n     * @param scopesValue\r\n     */\r\n    static createEmptyScopesArrayError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.emptyScopesError.code,\r\n            `${ClientConfigurationErrorMessage.emptyScopesError.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Error thrown when client id scope is not provided as single scope.\r\n     * @param inputScopes\r\n     */\r\n    static createClientIdSingleScopeError(inputScopes: Array<string>): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.clientIdSingleScopeError.code,\r\n            `${ClientConfigurationErrorMessage.clientIdSingleScopeError.desc} Given Scopes: ${inputScopes}`);\r\n    }\r\n\r\n    /**\r\n     * Error thrown when prompt is not an allowed type.\r\n     * @param promptValue\r\n     */\r\n    static createInvalidPromptError(promptValue: string): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.invalidPrompt.code,\r\n            `${ClientConfigurationErrorMessage.invalidPrompt.desc} Given value: ${promptValue}`);\r\n    }\r\n\r\n    /**\r\n     * Creates error thrown when claims parameter is not a stringified JSON object\r\n     */\r\n    static createInvalidClaimsRequestError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.invalidClaimsRequest.code,\r\n            ClientConfigurationErrorMessage.invalidClaimsRequest.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when token request is empty and nothing cached in storage.\r\n     */\r\n    static createEmptyLogoutRequestError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(\r\n            ClientConfigurationErrorMessage.logoutRequestEmptyError.code,\r\n            ClientConfigurationErrorMessage.logoutRequestEmptyError.desc\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Throws error when token request is empty and nothing cached in storage.\r\n     */\r\n    static createEmptyTokenRequestError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(\r\n            ClientConfigurationErrorMessage.tokenRequestEmptyError.code,\r\n            ClientConfigurationErrorMessage.tokenRequestEmptyError.desc\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Throws error when an invalid code_challenge_method is passed by the user\r\n     */\r\n    static createInvalidCodeChallengeMethodError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(\r\n            ClientConfigurationErrorMessage.invalidCodeChallengeMethod.code,\r\n            ClientConfigurationErrorMessage.invalidCodeChallengeMethod.desc\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Throws error when both params: code_challenge and code_challenge_method are not passed together\r\n     */\r\n    static createInvalidCodeChallengeParamsError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(\r\n            ClientConfigurationErrorMessage.invalidCodeChallengeParams.code,\r\n            ClientConfigurationErrorMessage.invalidCodeChallengeParams.desc\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Throws an error when the user passes invalid cloudDiscoveryMetadata\r\n     */\r\n    static createInvalidCloudDiscoveryMetadataError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.invalidCloudDiscoveryMetadata.code,\r\n            ClientConfigurationErrorMessage.invalidCloudDiscoveryMetadata.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws an error when the user passes invalid cloudDiscoveryMetadata\r\n     */\r\n    static createInvalidAuthorityMetadataError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.invalidAuthorityMetadata.code,\r\n            ClientConfigurationErrorMessage.invalidAuthorityMetadata.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when provided authority is not a member of the trusted host list\r\n     */\r\n    static createUntrustedAuthorityError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.untrustedAuthority.code,\r\n            ClientConfigurationErrorMessage.untrustedAuthority.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when the AzureCloudInstance is set to an invalid value\r\n     */\r\n    static createInvalidAzureCloudInstanceError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.invalidAzureCloudInstance.code,\r\n            ClientConfigurationErrorMessage.invalidAzureCloudInstance.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws an error when the authentication scheme is set to SSH but the SSH public key is omitted from the request\r\n     */\r\n    static createMissingSshJwkError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.missingSshJwk.code,\r\n            ClientConfigurationErrorMessage.missingSshJwk.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws an error when the authentication scheme is set to SSH but the SSH public key ID is omitted from the request\r\n     */\r\n    static createMissingSshKidError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.missingSshKid.code,\r\n            ClientConfigurationErrorMessage.missingSshKid.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when provided headers don't contain a header that a server nonce can be extracted from\r\n     */\r\n    static createMissingNonceAuthenticationHeadersError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.missingNonceAuthenticationHeader.code,\r\n            ClientConfigurationErrorMessage.missingNonceAuthenticationHeader.desc);\r\n    }\r\n\r\n    /**\r\n     * Throws error when a provided header is invalid in any way\r\n     */\r\n    static createInvalidAuthenticationHeaderError(invalidHeaderName: string, details: string): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.invalidAuthenticationHeader.code,\r\n            `${ClientConfigurationErrorMessage.invalidAuthenticationHeader.desc}. Invalid header: ${invalidHeaderName}. Details: ${details}`);\r\n    }\r\n    \r\n    /**\r\n     * Create an error when the authority provided in request does not match authority provided in account or MSAL.js configuration.\r\n     */\r\n    static createAuthorityMismatchError(): ClientConfigurationError {\r\n        return new ClientConfigurationError(ClientConfigurationErrorMessage.authorityMismatch.code, ClientConfigurationErrorMessage.authorityMismatch.desc);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAIH;;AAEG;AACU,IAAA,+BAA+B,GAAG;AAC3C,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,kEAAkE;AAC3E,KAAA;AACD,IAAA,mBAAmB,EAAE;AACjB,QAAA,IAAI,EAAE,uBAAuB;AAC7B,QAAA,IAAI,EAAE,0CAA0C;AACnD,KAAA;AACD,IAAA,yBAAyB,EAAE;AACvB,QAAA,IAAI,EAAE,8BAA8B;AACpC,QAAA,IAAI,EAAE,kDAAkD;AAC3D,KAAA;AACD,IAAA,oBAAoB,EAAE;AAClB,QAAA,IAAI,EAAE,wBAAwB;AAC9B,QAAA,IAAI,EAAE,2NAA2N;AACpO,KAAA;AACD,IAAA,aAAa,EAAE;AACX,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,IAAI,EAAE,oDAAoD;AAC7D,KAAA;AACD,IAAA,aAAa,EAAE;AACX,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,IAAI,EAAE,wBAAwB;AACjC,KAAA;AACD,IAAA,gBAAgB,EAAE;AACd,QAAA,IAAI,EAAE,0BAA0B;AAChC,QAAA,IAAI,EAAE,gHAAgH;AACzH,KAAA;AACD,IAAA,mBAAmB,EAAE;AACjB,QAAA,IAAI,EAAE,6BAA6B;AACnC,QAAA,IAAI,EAAE,uCAAuC;AAChD,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,6BAA6B;AACnC,QAAA,IAAI,EAAE,mDAAmD;AAC5D,KAAA;AACD,IAAA,aAAa,EAAE;AACX,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,IAAI,EAAE,4RAA4R;AACrS,KAAA;AACD,IAAA,oBAAoB,EAAE;AAClB,QAAA,IAAI,EAAE,gBAAgB;AACtB,QAAA,IAAI,EAAE,2DAA2D;AACpE,KAAA;AACD,IAAA,sBAAsB,EAAE;AACpB,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,iDAAiD;AAC1D,KAAA;AACD,IAAA,uBAAuB,EAAE;AACrB,QAAA,IAAI,EAAE,sBAAsB;AAC5B,QAAA,IAAI,EAAE,2CAA2C;AACpD,KAAA;AACD,IAAA,0BAA0B,EAAE;AACxB,QAAA,IAAI,EAAE,+BAA+B;AACrC,QAAA,IAAI,EAAE,mFAAmF;AAC5F,KAAA;AACD,IAAA,0BAA0B,EAAE;AACxB,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,qGAAqG;AAC9G,KAAA;AACD,IAAA,6BAA6B,EAAE;AAC3B,QAAA,IAAI,EAAE,kCAAkC;AACxC,QAAA,IAAI,EAAE,qIAAqI;AAC9I,KAAA;AACD,IAAA,wBAAwB,EAAE;AACtB,QAAA,IAAI,EAAE,4BAA4B;AAClC,QAAA,IAAI,EAAE,yIAAyI;AAClJ,KAAA;AACD,IAAA,kBAAkB,EAAE;AAChB,QAAA,IAAI,EAAE,qBAAqB;AAC3B,QAAA,IAAI,EAAE,4HAA4H;AACrI,KAAA;AACD,IAAA,yBAAyB,EAAE;AACvB,QAAA,IAAI,EAAE,8BAA8B;AACpC,QAAA,IAAI,EAAE,qHAAqH;AAC9H,KAAA;AACD,IAAA,aAAa,EAAE;AACX,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,IAAI,EAAE,6HAA6H;AACtI,KAAA;AACD,IAAA,aAAa,EAAE;AACX,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,IAAI,EAAE,uJAAuJ;AAChK,KAAA;AACD,IAAA,gCAAgC,EAAE;AAC9B,QAAA,IAAI,EAAE,qCAAqC;AAC3C,QAAA,IAAI,EAAE,gLAAgL;AACzL,KAAA;AACD,IAAA,2BAA2B,EAAE;AACzB,QAAA,IAAI,EAAE,+BAA+B;AACrC,QAAA,IAAI,EAAE,wCAAwC;AACjD,KAAA;AACD,IAAA,iBAAiB,EAAE;AACf,QAAA,IAAI,EAAE,oBAAoB;AAC1B,QAAA,IAAI,EAAE,kPAAkP;AAC3P,KAAA;EACH;AAEF;;AAEG;AACH,IAAA,wBAAA,kBAAA,UAAA,MAAA,EAAA;IAA8C,SAAe,CAAA,wBAAA,EAAA,MAAA,CAAA,CAAA;IAEzD,SAAY,wBAAA,CAAA,SAAiB,EAAE,YAAqB,EAAA;AAApD,QAAA,IAAA,KAAA,GACI,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,SAAS,EAAE,YAAY,CAAC,IAGjC,IAAA,CAAA;AAFG,QAAA,KAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;QACvC,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,wBAAwB,CAAC,SAAS,CAAC,CAAC;;KACnE;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,2BAA2B,GAAlC,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,iBAAiB,CAAC,IAAI,EACtF,+BAA+B,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KAC/D,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,qCAAqC,GAA5C,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,mBAAmB,CAAC,IAAI,EACxF,+BAA+B,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;KACjE,CAAA;AAED;;AAEG;IACI,wBAA+B,CAAA,+BAAA,GAAtC,UAAuC,uBAA+B,EAAA;AAClE,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,yBAAyB,CAAC,IAAI,EAC3F,+BAA+B,CAAC,yBAAyB,CAAC,IAAI,GAAiB,gBAAA,GAAA,uBAAyB,CAAC,CAAC;KACpH,CAAA;AAED;;;AAGG;IACI,wBAA+B,CAAA,+BAAA,GAAtC,UAAuC,SAAiB,EAAA;AACpD,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,oBAAoB,CAAC,IAAI,EACtF,+BAA+B,CAAC,oBAAoB,CAAC,IAAI,GAAe,cAAA,GAAA,SAAW,CAAC,CAAC;KAC/F,CAAA;AAED;;;AAGG;IACI,wBAAmB,CAAA,mBAAA,GAA1B,UAA2B,aAAqB,EAAA;AAC5C,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,EAC/E,+BAA+B,CAAC,aAAa,CAAC,IAAI,GAAiB,gBAAA,GAAA,aAAe,CAAC,CAAC;KAC9F,CAAA;AAED;;;AAGG;AACI,IAAA,wBAAA,CAAA,mBAAmB,GAA1B,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,EAClF,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KAC3D,CAAA;AAED;;;AAGG;AACI,IAAA,wBAAA,CAAA,2BAA2B,GAAlC,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,gBAAgB,CAAC,IAAI,EACrF,EAAA,GAAG,+BAA+B,CAAC,gBAAgB,CAAC,IAAM,CAAC,CAAC;KACnE,CAAA;AAED;;;AAGG;IACI,wBAA8B,CAAA,8BAAA,GAArC,UAAsC,WAA0B,EAAA;AAC5D,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,wBAAwB,CAAC,IAAI,EAC1F,+BAA+B,CAAC,wBAAwB,CAAC,IAAI,GAAkB,iBAAA,GAAA,WAAa,CAAC,CAAC;KACxG,CAAA;AAED;;;AAGG;IACI,wBAAwB,CAAA,wBAAA,GAA/B,UAAgC,WAAmB,EAAA;AAC/C,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,EAC/E,+BAA+B,CAAC,aAAa,CAAC,IAAI,GAAiB,gBAAA,GAAA,WAAa,CAAC,CAAC;KAC5F,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,+BAA+B,GAAtC,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,oBAAoB,CAAC,IAAI,EACzF,+BAA+B,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;KAClE,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,6BAA6B,GAApC,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAC/B,+BAA+B,CAAC,uBAAuB,CAAC,IAAI,EAC5D,+BAA+B,CAAC,uBAAuB,CAAC,IAAI,CAC/D,CAAC;KACL,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,4BAA4B,GAAnC,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAC/B,+BAA+B,CAAC,sBAAsB,CAAC,IAAI,EAC3D,+BAA+B,CAAC,sBAAsB,CAAC,IAAI,CAC9D,CAAC;KACL,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,qCAAqC,GAA5C,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAC/B,+BAA+B,CAAC,0BAA0B,CAAC,IAAI,EAC/D,+BAA+B,CAAC,0BAA0B,CAAC,IAAI,CAClE,CAAC;KACL,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,qCAAqC,GAA5C,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAC/B,+BAA+B,CAAC,0BAA0B,CAAC,IAAI,EAC/D,+BAA+B,CAAC,0BAA0B,CAAC,IAAI,CAClE,CAAC;KACL,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,wCAAwC,GAA/C,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,6BAA6B,CAAC,IAAI,EAClG,+BAA+B,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;KAC3E,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,mCAAmC,GAA1C,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,wBAAwB,CAAC,IAAI,EAC7F,+BAA+B,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;KACtE,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,6BAA6B,GAApC,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,kBAAkB,CAAC,IAAI,EACvF,+BAA+B,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;KAChE,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,oCAAoC,GAA3C,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,yBAAyB,CAAC,IAAI,EAC9F,+BAA+B,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;KACvE,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,wBAAwB,GAA/B,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,EAClF,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KAC3D,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,wBAAwB,GAA/B,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,aAAa,CAAC,IAAI,EAClF,+BAA+B,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KAC3D,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,4CAA4C,GAAnD,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,gCAAgC,CAAC,IAAI,EACrG,+BAA+B,CAAC,gCAAgC,CAAC,IAAI,CAAC,CAAC;KAC9E,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,sCAAsC,GAA7C,UAA8C,iBAAyB,EAAE,OAAe,EAAA;AACpF,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,2BAA2B,CAAC,IAAI,EAC7F,+BAA+B,CAAC,2BAA2B,CAAC,IAAI,GAAA,oBAAA,GAAqB,iBAAiB,GAAc,aAAA,GAAA,OAAS,CAAC,CAAC;KACzI,CAAA;AAED;;AAEG;AACI,IAAA,wBAAA,CAAA,4BAA4B,GAAnC,YAAA;AACI,QAAA,OAAO,IAAI,wBAAwB,CAAC,+BAA+B,CAAC,iBAAiB,CAAC,IAAI,EAAE,+BAA+B,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;KACvJ,CAAA;IACL,OAAC,wBAAA,CAAA;AAAD,CA5MA,CAA8C,eAAe,CA4M5D;;;;"}