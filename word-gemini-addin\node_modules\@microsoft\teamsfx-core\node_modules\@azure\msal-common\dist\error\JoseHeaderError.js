/*! @azure/msal-common v13.3.1 2023-10-27 */
'use strict';
import { __extends } from '../_virtual/_tslib.js';
import { AuthError } from './AuthError.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * ClientAuthErrorMessage class containing string constants used by error codes and messages.
 */
var JoseHeaderErrorMessage = {
    missingKidError: {
        code: "missing_kid_error",
        desc: "The JOSE Header for the requested JWT, JWS or JWK object requires a keyId to be configured as the 'kid' header claim. No 'kid' value was provided."
    },
    missingAlgError: {
        code: "missing_alg_error",
        desc: "The JOSE Header for the requested JWT, JWS or JWK object requires an algorithm to be specified as the 'alg' header claim. No 'alg' value was provided."
    },
};
/**
 * Error thrown when there is an error in the client code running on the browser.
 */
var JoseHeaderError = /** @class */ (function (_super) {
    __extends(<PERSON><PERSON><PERSON>erError, _super);
    function JoseHeaderError(errorCode, errorMessage) {
        var _this = _super.call(this, errorCode, errorMessage) || this;
        _this.name = "JoseHeaderError";
        Object.setPrototypeOf(_this, JoseHeaderError.prototype);
        return _this;
    }
    /**
     * Creates an error thrown when keyId isn't set on JOSE header.
     */
    JoseHeaderError.createMissingKidError = function () {
        return new JoseHeaderError(JoseHeaderErrorMessage.missingKidError.code, JoseHeaderErrorMessage.missingKidError.desc);
    };
    /**
     * Creates an error thrown when algorithm isn't set on JOSE header.
     */
    JoseHeaderError.createMissingAlgError = function () {
        return new JoseHeaderError(JoseHeaderErrorMessage.missingAlgError.code, JoseHeaderErrorMessage.missingAlgError.desc);
    };
    return JoseHeaderError;
}(AuthError));

export { JoseHeaderError, JoseHeaderErrorMessage };
//# sourceMappingURL=JoseHeaderError.js.map
