{"version": 3, "file": "Logger.d.ts", "sourceRoot": "", "sources": ["../../src/logger/Logger.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAG9D;;GAEG;AACH,oBAAY,oBAAoB,GAAG;IAC/B,QAAQ,EAAE,QAAQ,CAAC;IACnB,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,aAAa,CAAC,EAAE,MAAM,CAAA;CACzB,CAAC;AAEF;;GAEG;AACH,oBAAY,QAAQ;IAChB,KAAK,IAAA;IACL,OAAO,IAAA;IACP,IAAI,IAAA;IACJ,OAAO,IAAA;IACP,KAAK,IAAA;CACR;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC5B,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,GAAG,IAAI,CAAC;CAClE;AAED;;GAEG;AACH,qBAAa,MAAM;IAGf,OAAO,CAAC,aAAa,CAAS;IAG9B,OAAO,CAAC,KAAK,CAA2B;IAGxC,OAAO,CAAC,iBAAiB,CAAU;IAGnC,OAAO,CAAC,aAAa,CAAkB;IAGvC,OAAO,CAAC,WAAW,CAAS;IAG5B,OAAO,CAAC,cAAc,CAAS;gBAEnB,aAAa,EAAE,aAAa,EAAE,WAAW,CAAC,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,MAAM;IAavF,OAAO,CAAC,MAAM,CAAC,0BAA0B;IAUzC;;OAEG;IACI,KAAK,CAAC,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,MAAM;IAIzF;;OAEG;IACH,OAAO,CAAC,UAAU;IAqBlB;;OAEG;IACH,eAAe,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,GAAG,IAAI;IAM7E;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAQpD;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAQvD;;OAEG;IACH,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAQtD;;OAEG;IACH,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAQzD;;OAEG;IACH,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAQnD;;OAEG;IACH,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAQtD;;OAEG;IACH,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAQtD;;OAEG;IACH,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAQzD;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAQpD;;OAEG;IACH,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAQvD;;OAEG;IACH,mBAAmB,IAAI,OAAO;CAGjC"}