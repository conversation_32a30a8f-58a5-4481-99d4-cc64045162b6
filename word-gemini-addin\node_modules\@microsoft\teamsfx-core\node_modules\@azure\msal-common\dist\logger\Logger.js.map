{"version": 3, "file": "Logger.js", "sources": ["../../src/logger/Logger.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { LoggerOptions } from \"../config/ClientConfiguration\";\r\nimport { Constants } from \"../utils/Constants\";\r\n\r\n/**\r\n * Options for logger messages.\r\n */\r\nexport type LoggerMessageOptions = {\r\n    logLevel: LogLevel,\r\n    containsPii?: boolean,\r\n    context?: string,\r\n    correlationId?: string\r\n};\r\n\r\n/**\r\n * Log message level.\r\n */\r\nexport enum LogLevel {\r\n    Error,\r\n    Warning,\r\n    Info,\r\n    Verbose,\r\n    Trace\r\n}\r\n\r\n/**\r\n * Callback to send the messages to.\r\n */\r\nexport interface ILoggerCallback {\r\n    (level: LogLevel, message: string, containsPii: boolean): void;\r\n}\r\n\r\n/**\r\n * Class which facilitates logging of messages to a specific place.\r\n */\r\nexport class Logger {\r\n\r\n    // Correlation ID for request, usually set by user.\r\n    private correlationId: string;\r\n\r\n    // Current log level, defaults to info.\r\n    private level: LogLevel = LogLevel.Info;\r\n\r\n    // Boolean describing whether PII logging is allowed.\r\n    private piiLoggingEnabled: boolean;\r\n\r\n    // Callback to send messages to.\r\n    private localCallback: ILoggerCallback;\r\n\r\n    // Package name implementing this logger\r\n    private packageName: string;\r\n\r\n    // Package version implementing this logger\r\n    private packageVersion: string;\r\n\r\n    constructor(loggerOptions: LoggerOptions, packageName?: string, packageVersion?: string) {\r\n        const defaultLoggerCallback = () => {\r\n            return;\r\n        };\r\n        const setLoggerOptions = loggerOptions || Logger.createDefaultLoggerOptions();\r\n        this.localCallback = setLoggerOptions.loggerCallback || defaultLoggerCallback;\r\n        this.piiLoggingEnabled = setLoggerOptions.piiLoggingEnabled || false;\r\n        this.level = typeof(setLoggerOptions.logLevel) === \"number\" ? setLoggerOptions.logLevel : LogLevel.Info;\r\n        this.correlationId = setLoggerOptions.correlationId || Constants.EMPTY_STRING;\r\n        this.packageName = packageName || Constants.EMPTY_STRING;\r\n        this.packageVersion = packageVersion || Constants.EMPTY_STRING;\r\n    }\r\n    \r\n    private static createDefaultLoggerOptions(): LoggerOptions {\r\n        return {\r\n            loggerCallback: () => {\r\n                // allow users to not set loggerCallback\r\n            },\r\n            piiLoggingEnabled: false,\r\n            logLevel: LogLevel.Info\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Create new Logger with existing configurations.\r\n     */\r\n    public clone(packageName: string, packageVersion: string, correlationId?: string): Logger {\r\n        return new Logger({loggerCallback: this.localCallback, piiLoggingEnabled: this.piiLoggingEnabled, logLevel: this.level, correlationId: correlationId || this.correlationId}, packageName, packageVersion);\r\n    }\r\n\r\n    /**\r\n     * Log message with required options.\r\n     */\r\n    private logMessage(logMessage: string, options: LoggerMessageOptions): void {\r\n        if ((options.logLevel > this.level) || (!this.piiLoggingEnabled && options.containsPii)) {\r\n            return;\r\n        }\r\n        const timestamp = new Date().toUTCString();\r\n\r\n        // Add correlationId to logs if set, correlationId provided on log messages take precedence\r\n        let logHeader: string;\r\n        if (!StringUtils.isEmpty(options.correlationId)) {\r\n            logHeader = `[${timestamp}] : [${options.correlationId}]`;\r\n        } else if (!StringUtils.isEmpty(this.correlationId)) {\r\n            logHeader = `[${timestamp}] : [${this.correlationId}]`;\r\n        } else {\r\n            logHeader = `[${timestamp}]`;\r\n        }\r\n\r\n        const log = `${logHeader} : ${this.packageName}@${this.packageVersion} : ${LogLevel[options.logLevel]} - ${logMessage}`;\r\n        // debug(`msal:${LogLevel[options.logLevel]}${options.containsPii ? \"-Pii\": Constants.EMPTY_STRING}${options.context ? `:${options.context}` : Constants.EMPTY_STRING}`)(logMessage);\r\n        this.executeCallback(options.logLevel, log, options.containsPii || false);\r\n    }\r\n\r\n    /**\r\n     * Execute callback with message.\r\n     */\r\n    executeCallback(level: LogLevel, message: string, containsPii: boolean): void {\r\n        if (this.localCallback) {\r\n            this.localCallback(level, message, containsPii);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Logs error messages.\r\n     */\r\n    error(message: string, correlationId?: string): void {\r\n        this.logMessage(message, {\r\n            logLevel: LogLevel.Error,\r\n            containsPii: false,\r\n            correlationId: correlationId || Constants.EMPTY_STRING\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Logs error messages with PII.\r\n     */\r\n    errorPii(message: string, correlationId?: string): void {\r\n        this.logMessage(message, {\r\n            logLevel: LogLevel.Error,\r\n            containsPii: true,\r\n            correlationId: correlationId || Constants.EMPTY_STRING\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Logs warning messages.\r\n     */\r\n    warning(message: string, correlationId?: string): void {\r\n        this.logMessage(message, {\r\n            logLevel: LogLevel.Warning,\r\n            containsPii: false,\r\n            correlationId: correlationId || Constants.EMPTY_STRING\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Logs warning messages with PII.\r\n     */\r\n    warningPii(message: string, correlationId?: string): void {\r\n        this.logMessage(message, {\r\n            logLevel: LogLevel.Warning,\r\n            containsPii: true,\r\n            correlationId: correlationId || Constants.EMPTY_STRING\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Logs info messages.\r\n     */\r\n    info(message: string, correlationId?: string): void {\r\n        this.logMessage(message, {\r\n            logLevel: LogLevel.Info,\r\n            containsPii: false,\r\n            correlationId: correlationId || Constants.EMPTY_STRING\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Logs info messages with PII.\r\n     */\r\n    infoPii(message: string, correlationId?: string): void {\r\n        this.logMessage(message, {\r\n            logLevel: LogLevel.Info,\r\n            containsPii: true,\r\n            correlationId: correlationId || Constants.EMPTY_STRING\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Logs verbose messages.\r\n     */\r\n    verbose(message: string, correlationId?: string): void {\r\n        this.logMessage(message, {\r\n            logLevel: LogLevel.Verbose,\r\n            containsPii: false,\r\n            correlationId: correlationId || Constants.EMPTY_STRING\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Logs verbose messages with PII.\r\n     */\r\n    verbosePii(message: string, correlationId?: string): void {\r\n        this.logMessage(message, {\r\n            logLevel: LogLevel.Verbose,\r\n            containsPii: true,\r\n            correlationId: correlationId || Constants.EMPTY_STRING\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Logs trace messages.\r\n     */\r\n    trace(message: string, correlationId?: string): void {\r\n        this.logMessage(message, {\r\n            logLevel: LogLevel.Trace,\r\n            containsPii: false,\r\n            correlationId: correlationId || Constants.EMPTY_STRING\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Logs trace messages with PII.\r\n     */\r\n    tracePii(message: string, correlationId?: string): void {\r\n        this.logMessage(message, {\r\n            logLevel: LogLevel.Trace,\r\n            containsPii: true,\r\n            correlationId: correlationId || Constants.EMPTY_STRING\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Returns whether PII Logging is enabled or not.\r\n     */\r\n    isPiiLoggingEnabled(): boolean {\r\n        return this.piiLoggingEnabled || false;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAgBH;;AAEG;IACS,SAMX;AAND,CAAA,UAAY,QAAQ,EAAA;AAChB,IAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,QAAA,CAAA,QAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,QAAA,CAAA,QAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACT,CAAC,EANW,QAAQ,KAAR,QAAQ,GAMnB,EAAA,CAAA,CAAA,CAAA;AASD;;AAEG;AACH,IAAA,MAAA,kBAAA,YAAA;AAoBI,IAAA,SAAA,MAAA,CAAY,aAA4B,EAAE,WAAoB,EAAE,cAAuB,EAAA;;AAd/E,QAAA,IAAA,CAAA,KAAK,GAAa,QAAQ,CAAC,IAAI,CAAC;AAepC,QAAA,IAAM,qBAAqB,GAAG,YAAA;YAC1B,OAAO;AACX,SAAC,CAAC;QACF,IAAM,gBAAgB,GAAG,aAAa,IAAI,MAAM,CAAC,0BAA0B,EAAE,CAAC;QAC9E,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC,cAAc,IAAI,qBAAqB,CAAC;QAC9E,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC,iBAAiB,IAAI,KAAK,CAAC;QACrE,IAAI,CAAC,KAAK,GAAG,QAAO,gBAAgB,CAAC,QAAQ,CAAC,KAAK,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;QACxG,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC,aAAa,IAAI,SAAS,CAAC,YAAY,CAAC;QAC9E,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,SAAS,CAAC,YAAY,CAAC;QACzD,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,SAAS,CAAC,YAAY,CAAC;KAClE;AAEc,IAAA,MAAA,CAAA,0BAA0B,GAAzC,YAAA;QACI,OAAO;AACH,YAAA,cAAc,EAAE,YAAA;;aAEf;AACD,YAAA,iBAAiB,EAAE,KAAK;YACxB,QAAQ,EAAE,QAAQ,CAAC,IAAI;SAC1B,CAAC;KACL,CAAA;AAED;;AAEG;AACI,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAZ,UAAa,WAAmB,EAAE,cAAsB,EAAE,aAAsB,EAAA;AAC5E,QAAA,OAAO,IAAI,MAAM,CAAC,EAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,aAAa,EAAE,aAAa,IAAI,IAAI,CAAC,aAAa,EAAC,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;KAC7M,CAAA;AAED;;AAEG;AACK,IAAA,MAAA,CAAA,SAAA,CAAA,UAAU,GAAlB,UAAmB,UAAkB,EAAE,OAA6B,EAAA;QAChE,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,iBAAiB,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE;YACrF,OAAO;AACV,SAAA;QACD,IAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;;AAG3C,QAAA,IAAI,SAAiB,CAAC;QACtB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;YAC7C,SAAS,GAAG,MAAI,SAAS,GAAA,OAAA,GAAQ,OAAO,CAAC,aAAa,MAAG,CAAC;AAC7D,SAAA;aAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACjD,SAAS,GAAG,MAAI,SAAS,GAAA,OAAA,GAAQ,IAAI,CAAC,aAAa,MAAG,CAAC;AAC1D,SAAA;AAAM,aAAA;AACH,YAAA,SAAS,GAAG,GAAA,GAAI,SAAS,GAAA,GAAG,CAAC;AAChC,SAAA;QAED,IAAM,GAAG,GAAM,SAAS,GAAA,KAAA,GAAM,IAAI,CAAC,WAAW,SAAI,IAAI,CAAC,cAAc,GAAM,KAAA,GAAA,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAA,KAAA,GAAM,UAAY,CAAC;;AAExH,QAAA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC,CAAC;KAC7E,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,KAAe,EAAE,OAAe,EAAE,WAAoB,EAAA;QAClE,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;AACnD,SAAA;KACJ,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UAAM,OAAe,EAAE,aAAsB,EAAA;AACzC,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,QAAQ,EAAE,QAAQ,CAAC,KAAK;AACxB,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,aAAa,EAAE,aAAa,IAAI,SAAS,CAAC,YAAY;AACzD,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,OAAe,EAAE,aAAsB,EAAA;AAC5C,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,QAAQ,EAAE,QAAQ,CAAC,KAAK;AACxB,YAAA,WAAW,EAAE,IAAI;AACjB,YAAA,aAAa,EAAE,aAAa,IAAI,SAAS,CAAC,YAAY;AACzD,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,OAAe,EAAE,aAAsB,EAAA;AAC3C,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,QAAQ,EAAE,QAAQ,CAAC,OAAO;AAC1B,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,aAAa,EAAE,aAAa,IAAI,SAAS,CAAC,YAAY;AACzD,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,UAAU,GAAV,UAAW,OAAe,EAAE,aAAsB,EAAA;AAC9C,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,QAAQ,EAAE,QAAQ,CAAC,OAAO;AAC1B,YAAA,WAAW,EAAE,IAAI;AACjB,YAAA,aAAa,EAAE,aAAa,IAAI,SAAS,CAAC,YAAY;AACzD,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UAAK,OAAe,EAAE,aAAsB,EAAA;AACxC,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,QAAQ,EAAE,QAAQ,CAAC,IAAI;AACvB,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,aAAa,EAAE,aAAa,IAAI,SAAS,CAAC,YAAY;AACzD,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,OAAe,EAAE,aAAsB,EAAA;AAC3C,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,QAAQ,EAAE,QAAQ,CAAC,IAAI;AACvB,YAAA,WAAW,EAAE,IAAI;AACjB,YAAA,aAAa,EAAE,aAAa,IAAI,SAAS,CAAC,YAAY;AACzD,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,OAAO,GAAP,UAAQ,OAAe,EAAE,aAAsB,EAAA;AAC3C,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,QAAQ,EAAE,QAAQ,CAAC,OAAO;AAC1B,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,aAAa,EAAE,aAAa,IAAI,SAAS,CAAC,YAAY;AACzD,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,UAAU,GAAV,UAAW,OAAe,EAAE,aAAsB,EAAA;AAC9C,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,QAAQ,EAAE,QAAQ,CAAC,OAAO;AAC1B,YAAA,WAAW,EAAE,IAAI;AACjB,YAAA,aAAa,EAAE,aAAa,IAAI,SAAS,CAAC,YAAY;AACzD,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,KAAK,GAAL,UAAM,OAAe,EAAE,aAAsB,EAAA;AACzC,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,QAAQ,EAAE,QAAQ,CAAC,KAAK;AACxB,YAAA,WAAW,EAAE,KAAK;AAClB,YAAA,aAAa,EAAE,aAAa,IAAI,SAAS,CAAC,YAAY;AACzD,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,QAAQ,GAAR,UAAS,OAAe,EAAE,aAAsB,EAAA;AAC5C,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACrB,QAAQ,EAAE,QAAQ,CAAC,KAAK;AACxB,YAAA,WAAW,EAAE,IAAI;AACjB,YAAA,aAAa,EAAE,aAAa,IAAI,SAAS,CAAC,YAAY;AACzD,SAAA,CAAC,CAAC;KACN,CAAA;AAED;;AAEG;AACH,IAAA,MAAA,CAAA,SAAA,CAAA,mBAAmB,GAAnB,YAAA;AACI,QAAA,OAAO,IAAI,CAAC,iBAAiB,IAAI,KAAK,CAAC;KAC1C,CAAA;IACL,OAAC,MAAA,CAAA;AAAD,CAAC,EAAA;;;;"}