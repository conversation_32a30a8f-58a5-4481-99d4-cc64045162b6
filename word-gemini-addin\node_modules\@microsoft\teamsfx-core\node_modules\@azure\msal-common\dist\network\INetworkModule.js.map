{"version": 3, "file": "INetworkModule.js", "sources": ["../../src/network/INetworkModule.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthError } from \"../error/AuthError\";\r\nimport { NetworkResponse } from \"./NetworkManager\";\r\n\r\n/**\r\n * Options allowed by network request APIs.\r\n */\r\nexport type NetworkRequestOptions = {\r\n    headers?: Record<string, string>,\r\n    body?: string;\r\n};\r\n\r\n/**\r\n * Client network interface to send backend requests.\r\n * @interface\r\n */\r\nexport interface INetworkModule {\r\n\r\n    /**\r\n     * Interface function for async network \"GET\" requests. Based on the Fetch standard: https://fetch.spec.whatwg.org/\r\n     * @param url\r\n     * @param requestParams\r\n     * @param enableCaching\r\n     */\r\n    sendGetRequestAsync<T>(url: string, options?: NetworkRequestOptions, cancellationToken?: number): Promise<NetworkResponse<T>>;\r\n\r\n    /**\r\n     * Interface function for async network \"POST\" requests. Based on the Fetch standard: https://fetch.spec.whatwg.org/\r\n     * @param url\r\n     * @param requestParams\r\n     * @param enableCaching\r\n     */\r\n    sendPostRequestAsync<T>(url: string, options?: NetworkRequestOptions): Promise<NetworkResponse<T>>;\r\n}\r\n\r\nexport const StubbedNetworkModule: INetworkModule = {\r\n    sendGetRequestAsync: () => {\r\n        const notImplErr = \"Network interface - sendGetRequestAsync() has not been implemented for the Network interface.\";\r\n        return Promise.reject(AuthError.createUnexpectedError(notImplErr));\r\n    },\r\n    sendPostRequestAsync: () => {\r\n        const notImplErr = \"Network interface - sendPostRequestAsync() has not been implemented for the Network interface.\";\r\n        return Promise.reject(AuthError.createUnexpectedError(notImplErr));\r\n    }\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAoCU,IAAA,oBAAoB,GAAmB;AAChD,IAAA,mBAAmB,EAAE,YAAA;QACjB,IAAM,UAAU,GAAG,+FAA+F,CAAC;QACnH,OAAO,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC;KACtE;AACD,IAAA,oBAAoB,EAAE,YAAA;QAClB,IAAM,UAAU,GAAG,gGAAgG,CAAC;QACpH,OAAO,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC;KACtE;;;;;"}