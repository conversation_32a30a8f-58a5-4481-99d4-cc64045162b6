{"version": 3, "file": "RequestParameterBuilder.js", "sources": ["../../src/request/RequestParameterBuilder.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AADServerParamKeys, Constants, ResponseMode, SSOTypes, CLIENT_INFO, AuthenticationScheme, ClaimsRequestKeys, PasswordGrantConstants, OIDC_DEFAULT_SCOPES, ThrottlingConstants, HeaderNames} from \"../utils/Constants\";\r\nimport { ScopeSet } from \"./ScopeSet\";\r\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\r\nimport { StringDict } from \"../utils/MsalTypes\";\r\nimport { RequestValidator } from \"./RequestValidator\";\r\nimport { ApplicationTelemetry, LibraryInfo } from \"../config/ClientConfiguration\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { ServerTelemetryManager } from \"../telemetry/server/ServerTelemetryManager\";\r\nimport { ClientInfo } from \"../account/ClientInfo\";\r\n\r\nexport class RequestParameterBuilder {\r\n\r\n    private parameters: Map<string, string>;\r\n\r\n    constructor() {\r\n        this.parameters = new Map<string, string>();\r\n    }\r\n\r\n    /**\r\n     * add response_type = code\r\n     */\r\n    addResponseTypeCode(): void {\r\n        this.parameters.set(\r\n            AADServerParamKeys.RESPONSE_TYPE, encodeURIComponent(Constants.CODE_RESPONSE_TYPE)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * add response_type = token id_token\r\n     */\r\n    addResponseTypeForTokenAndIdToken(): void {\r\n        this.parameters.set(\r\n            AADServerParamKeys.RESPONSE_TYPE, encodeURIComponent(`${Constants.TOKEN_RESPONSE_TYPE} ${Constants.ID_TOKEN_RESPONSE_TYPE}`)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * add response_mode. defaults to query.\r\n     * @param responseMode\r\n     */\r\n    addResponseMode(responseMode?: ResponseMode): void {\r\n        this.parameters.set(\r\n            AADServerParamKeys.RESPONSE_MODE,\r\n            encodeURIComponent((responseMode) ? responseMode : ResponseMode.QUERY)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Add flag to indicate STS should attempt to use WAM if available\r\n     */\r\n    addNativeBroker(): void {\r\n        this.parameters.set(\r\n            AADServerParamKeys.NATIVE_BROKER,\r\n            encodeURIComponent(\"1\")\r\n        );\r\n    }\r\n\r\n    /**\r\n     * add scopes. set addOidcScopes to false to prevent default scopes in non-user scenarios\r\n     * @param scopeSet\r\n     * @param addOidcScopes\r\n     */\r\n    addScopes(scopes: string[], addOidcScopes: boolean = true): void {\r\n        const requestScopes = addOidcScopes ? [...scopes || [], ...OIDC_DEFAULT_SCOPES] : scopes || [];\r\n        const scopeSet = new ScopeSet(requestScopes);\r\n        this.parameters.set(AADServerParamKeys.SCOPE, encodeURIComponent(scopeSet.printScopes()));\r\n    }\r\n\r\n    /**\r\n     * add clientId\r\n     * @param clientId\r\n     */\r\n    addClientId(clientId: string): void {\r\n        this.parameters.set(AADServerParamKeys.CLIENT_ID, encodeURIComponent(clientId));\r\n    }\r\n\r\n    /**\r\n     * add redirect_uri\r\n     * @param redirectUri\r\n     */\r\n    addRedirectUri(redirectUri: string): void {\r\n        RequestValidator.validateRedirectUri(redirectUri);\r\n        this.parameters.set(AADServerParamKeys.REDIRECT_URI, encodeURIComponent(redirectUri));\r\n    }\r\n\r\n    /**\r\n     * add post logout redirectUri\r\n     * @param redirectUri\r\n     */\r\n    addPostLogoutRedirectUri(redirectUri: string): void {\r\n        RequestValidator.validateRedirectUri(redirectUri);\r\n        this.parameters.set(AADServerParamKeys.POST_LOGOUT_URI, encodeURIComponent(redirectUri));\r\n    }\r\n\r\n    /**\r\n     * add id_token_hint to logout request\r\n     * @param idTokenHint\r\n     */\r\n    addIdTokenHint(idTokenHint: string): void {\r\n        this.parameters.set(AADServerParamKeys.ID_TOKEN_HINT, encodeURIComponent(idTokenHint));\r\n    }\r\n\r\n    /**\r\n     * add domain_hint\r\n     * @param domainHint\r\n     */\r\n    addDomainHint(domainHint: string): void {\r\n        this.parameters.set(SSOTypes.DOMAIN_HINT, encodeURIComponent(domainHint));\r\n    }\r\n\r\n    /**\r\n     * add login_hint\r\n     * @param loginHint\r\n     */\r\n    addLoginHint(loginHint: string): void {\r\n        this.parameters.set(SSOTypes.LOGIN_HINT, encodeURIComponent(loginHint));\r\n    }\r\n\r\n    /**\r\n     * Adds the CCS (Cache Credential Service) query parameter for login_hint\r\n     * @param loginHint\r\n     */\r\n    addCcsUpn(loginHint: string): void {\r\n        this.parameters.set(HeaderNames.CCS_HEADER, encodeURIComponent(`UPN:${loginHint}`));\r\n    }\r\n\r\n    /**\r\n     * Adds the CCS (Cache Credential Service) query parameter for account object\r\n     * @param loginHint\r\n     */\r\n    addCcsOid(clientInfo: ClientInfo): void {\r\n        this.parameters.set(HeaderNames.CCS_HEADER, encodeURIComponent(`Oid:${clientInfo.uid}@${clientInfo.utid}`));\r\n    }\r\n\r\n    /**\r\n     * add sid\r\n     * @param sid\r\n     */\r\n    addSid(sid: string): void {\r\n        this.parameters.set(SSOTypes.SID, encodeURIComponent(sid));\r\n    }\r\n\r\n    /**\r\n     * add claims\r\n     * @param claims\r\n     */\r\n    addClaims(claims?: string, clientCapabilities?: Array<string>): void {\r\n        const mergedClaims = this.addClientCapabilitiesToClaims(claims, clientCapabilities);\r\n        RequestValidator.validateClaims(mergedClaims);\r\n        this.parameters.set(AADServerParamKeys.CLAIMS, encodeURIComponent(mergedClaims));\r\n    }\r\n\r\n    /**\r\n     * add correlationId\r\n     * @param correlationId\r\n     */\r\n    addCorrelationId(correlationId: string): void {\r\n        this.parameters.set(AADServerParamKeys.CLIENT_REQUEST_ID, encodeURIComponent(correlationId));\r\n    }\r\n\r\n    /**\r\n     * add library info query params\r\n     * @param libraryInfo\r\n     */\r\n    addLibraryInfo(libraryInfo: LibraryInfo): void {\r\n        // Telemetry Info\r\n        this.parameters.set(AADServerParamKeys.X_CLIENT_SKU, libraryInfo.sku);\r\n        this.parameters.set(AADServerParamKeys.X_CLIENT_VER, libraryInfo.version);\r\n        if (libraryInfo.os) {\r\n            this.parameters.set(AADServerParamKeys.X_CLIENT_OS, libraryInfo.os);\r\n        }\r\n        if (libraryInfo.cpu) {\r\n            this.parameters.set(AADServerParamKeys.X_CLIENT_CPU, libraryInfo.cpu);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Add client telemetry parameters\r\n     * @param appTelemetry\r\n     */\r\n    addApplicationTelemetry(appTelemetry: ApplicationTelemetry): void {\r\n        if (appTelemetry?.appName) {\r\n            this.parameters.set(AADServerParamKeys.X_APP_NAME, appTelemetry.appName);\r\n        }\r\n\r\n        if (appTelemetry?.appVersion) {\r\n            this.parameters.set(AADServerParamKeys.X_APP_VER, appTelemetry.appVersion);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * add prompt\r\n     * @param prompt\r\n     */\r\n    addPrompt(prompt: string): void {\r\n        RequestValidator.validatePrompt(prompt);\r\n        this.parameters.set(`${AADServerParamKeys.PROMPT}`, encodeURIComponent(prompt));\r\n    }\r\n\r\n    /**\r\n     * add state\r\n     * @param state\r\n     */\r\n    addState(state: string): void {\r\n        if (!StringUtils.isEmpty(state)) {\r\n            this.parameters.set(AADServerParamKeys.STATE, encodeURIComponent(state));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * add nonce\r\n     * @param nonce\r\n     */\r\n    addNonce(nonce: string): void {\r\n        this.parameters.set(AADServerParamKeys.NONCE, encodeURIComponent(nonce));\r\n    }\r\n\r\n    /**\r\n     * add code_challenge and code_challenge_method\r\n     * - throw if either of them are not passed\r\n     * @param codeChallenge\r\n     * @param codeChallengeMethod\r\n     */\r\n    addCodeChallengeParams(\r\n        codeChallenge: string,\r\n        codeChallengeMethod: string\r\n    ): void {\r\n        RequestValidator.validateCodeChallengeParams(codeChallenge, codeChallengeMethod);\r\n        if (codeChallenge && codeChallengeMethod) {\r\n            this.parameters.set(AADServerParamKeys.CODE_CHALLENGE, encodeURIComponent(codeChallenge));\r\n            this.parameters.set(AADServerParamKeys.CODE_CHALLENGE_METHOD, encodeURIComponent(codeChallengeMethod));\r\n        } else {\r\n            throw ClientConfigurationError.createInvalidCodeChallengeParamsError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * add the `authorization_code` passed by the user to exchange for a token\r\n     * @param code\r\n     */\r\n    addAuthorizationCode(code: string): void {\r\n        this.parameters.set(AADServerParamKeys.CODE, encodeURIComponent(code));\r\n    }\r\n\r\n    /**\r\n     * add the `authorization_code` passed by the user to exchange for a token\r\n     * @param code\r\n     */\r\n    addDeviceCode(code: string): void {\r\n        this.parameters.set(AADServerParamKeys.DEVICE_CODE, encodeURIComponent(code));\r\n    }\r\n\r\n    /**\r\n     * add the `refreshToken` passed by the user\r\n     * @param refreshToken\r\n     */\r\n    addRefreshToken(refreshToken: string): void {\r\n        this.parameters.set(AADServerParamKeys.REFRESH_TOKEN, encodeURIComponent(refreshToken));\r\n    }\r\n\r\n    /**\r\n     * add the `code_verifier` passed by the user to exchange for a token\r\n     * @param codeVerifier\r\n     */\r\n    addCodeVerifier(codeVerifier: string): void {\r\n        this.parameters.set(AADServerParamKeys.CODE_VERIFIER, encodeURIComponent(codeVerifier));\r\n    }\r\n\r\n    /**\r\n     * add client_secret\r\n     * @param clientSecret\r\n     */\r\n    addClientSecret(clientSecret: string): void {\r\n        this.parameters.set(AADServerParamKeys.CLIENT_SECRET, encodeURIComponent(clientSecret));\r\n    }\r\n\r\n    /**\r\n     * add clientAssertion for confidential client flows\r\n     * @param clientAssertion\r\n     */\r\n    addClientAssertion(clientAssertion: string): void {\r\n        if (!StringUtils.isEmpty(clientAssertion)) {\r\n            this.parameters.set(AADServerParamKeys.CLIENT_ASSERTION, encodeURIComponent(clientAssertion));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * add clientAssertionType for confidential client flows\r\n     * @param clientAssertionType\r\n     */\r\n    addClientAssertionType(clientAssertionType: string): void {\r\n        if (!StringUtils.isEmpty(clientAssertionType)) {\r\n            this.parameters.set(AADServerParamKeys.CLIENT_ASSERTION_TYPE, encodeURIComponent(clientAssertionType));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * add OBO assertion for confidential client flows\r\n     * @param clientAssertion\r\n     */\r\n    addOboAssertion(oboAssertion: string): void {\r\n        this.parameters.set(AADServerParamKeys.OBO_ASSERTION, encodeURIComponent(oboAssertion));\r\n    }\r\n\r\n    /**\r\n     * add grant type\r\n     * @param grantType\r\n     */\r\n    addRequestTokenUse(tokenUse: string): void {\r\n        this.parameters.set(AADServerParamKeys.REQUESTED_TOKEN_USE, encodeURIComponent(tokenUse));\r\n    }\r\n\r\n    /**\r\n     * add grant type\r\n     * @param grantType\r\n     */\r\n    addGrantType(grantType: string): void {\r\n        this.parameters.set(AADServerParamKeys.GRANT_TYPE, encodeURIComponent(grantType));\r\n    }\r\n\r\n    /**\r\n     * add client info\r\n     *\r\n     */\r\n    addClientInfo(): void {\r\n        this.parameters.set(CLIENT_INFO, \"1\");\r\n    }\r\n\r\n    /**\r\n     * add extraQueryParams\r\n     * @param eQParams\r\n     */\r\n    addExtraQueryParameters(eQParams: StringDict): void {\r\n        const sanitizedEQParams = RequestValidator.sanitizeEQParams(eQParams, this.parameters);\r\n        Object.keys(sanitizedEQParams).forEach((key) => {\r\n            this.parameters.set(key, eQParams[key]);\r\n        });\r\n    }\r\n\r\n    addClientCapabilitiesToClaims(claims?: string, clientCapabilities?: Array<string>): string {\r\n        let mergedClaims: object;\r\n\r\n        // Parse provided claims into JSON object or initialize empty object\r\n        if (!claims) {\r\n            mergedClaims = {};\r\n        } else {\r\n            try {\r\n                mergedClaims = JSON.parse(claims);\r\n            } catch(e) {\r\n                throw ClientConfigurationError.createInvalidClaimsRequestError();\r\n            }\r\n        }\r\n\r\n        if (clientCapabilities && clientCapabilities.length > 0) {\r\n            if (!mergedClaims.hasOwnProperty(ClaimsRequestKeys.ACCESS_TOKEN)){\r\n                // Add access_token key to claims object\r\n                mergedClaims[ClaimsRequestKeys.ACCESS_TOKEN] = {};\r\n            }\r\n\r\n            // Add xms_cc claim with provided clientCapabilities to access_token key\r\n            mergedClaims[ClaimsRequestKeys.ACCESS_TOKEN][ClaimsRequestKeys.XMS_CC] = {\r\n                values: clientCapabilities\r\n            };\r\n        }\r\n\r\n        return JSON.stringify(mergedClaims);\r\n    }\r\n\r\n    /**\r\n     * adds `username` for Password Grant flow\r\n     * @param username\r\n     */\r\n    addUsername(username: string): void {\r\n        this.parameters.set(PasswordGrantConstants.username, encodeURIComponent(username));\r\n    }\r\n\r\n    /**\r\n     * adds `password` for Password Grant flow\r\n     * @param password\r\n     */\r\n    addPassword(password: string): void {\r\n        this.parameters.set(PasswordGrantConstants.password, encodeURIComponent(password));\r\n    }\r\n\r\n    /**\r\n     * add pop_jwk to query params\r\n     * @param cnfString\r\n     */\r\n    addPopToken(cnfString: string): void {\r\n        if (!StringUtils.isEmpty(cnfString)) {\r\n            this.parameters.set(AADServerParamKeys.TOKEN_TYPE, AuthenticationScheme.POP);\r\n            this.parameters.set(AADServerParamKeys.REQ_CNF, encodeURIComponent(cnfString));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * add SSH JWK and key ID to query params\r\n     */\r\n    addSshJwk(sshJwkString: string): void {\r\n        if(!StringUtils.isEmpty(sshJwkString)) {\r\n            this.parameters.set(AADServerParamKeys.TOKEN_TYPE, AuthenticationScheme.SSH);\r\n            this.parameters.set(AADServerParamKeys.REQ_CNF, encodeURIComponent(sshJwkString));\r\n        }\r\n    }\r\n\r\n    /**\r\n     * add server telemetry fields\r\n     * @param serverTelemetryManager\r\n     */\r\n    addServerTelemetry(serverTelemetryManager: ServerTelemetryManager): void {\r\n        this.parameters.set(AADServerParamKeys.X_CLIENT_CURR_TELEM, serverTelemetryManager.generateCurrentRequestHeaderValue());\r\n        this.parameters.set(AADServerParamKeys.X_CLIENT_LAST_TELEM, serverTelemetryManager.generateLastRequestHeaderValue());\r\n    }\r\n\r\n    /**\r\n     * Adds parameter that indicates to the server that throttling is supported\r\n     */\r\n    addThrottling(): void {\r\n        this.parameters.set(AADServerParamKeys.X_MS_LIB_CAPABILITY, ThrottlingConstants.X_MS_LIB_CAPABILITY_VALUE);\r\n    }\r\n\r\n    /**\r\n     * Adds logout_hint parameter for \"silent\" logout which prevent server account picker\r\n     */\r\n    addLogoutHint(logoutHint: string): void {\r\n        this.parameters.set(AADServerParamKeys.LOGOUT_HINT, encodeURIComponent(logoutHint));\r\n    }\r\n\r\n    /**\r\n     * Utility to create a URL from the params map\r\n     */\r\n    createQueryString(): string {\r\n        const queryParameterArray: Array<string> = new Array<string>();\r\n\r\n        this.parameters.forEach((value, key) => {\r\n            queryParameterArray.push(`${key}=${value}`);\r\n        });\r\n\r\n        return queryParameterArray.join(\"&\");\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAGG;AAYH,IAAA,uBAAA,kBAAA,YAAA;AAII,IAAA,SAAA,uBAAA,GAAA;AACI,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;KAC/C;AAED;;AAEG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAnB,YAAA;AACI,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACf,kBAAkB,CAAC,aAAa,EAAE,kBAAkB,CAAC,SAAS,CAAC,kBAAkB,CAAC,CACrF,CAAC;KACL,CAAA;AAED;;AAEG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,iCAAiC,GAAjC,YAAA;QACI,IAAI,CAAC,UAAU,CAAC,GAAG,CACf,kBAAkB,CAAC,aAAa,EAAE,kBAAkB,CAAI,SAAS,CAAC,mBAAmB,GAAI,GAAA,GAAA,SAAS,CAAC,sBAAwB,CAAC,CAC/H,CAAC;KACL,CAAA;AAED;;;AAGG;IACH,uBAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,YAA2B,EAAA;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CACf,kBAAkB,CAAC,aAAa,EAChC,kBAAkB,CAAC,CAAC,YAAY,IAAI,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CACzE,CAAC;KACL,CAAA;AAED;;AAEG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,YAAA;AACI,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CACf,kBAAkB,CAAC,aAAa,EAChC,kBAAkB,CAAC,GAAG,CAAC,CAC1B,CAAC;KACL,CAAA;AAED;;;;AAIG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,MAAgB,EAAE,aAA6B,EAAA;AAA7B,QAAA,IAAA,aAAA,KAAA,KAAA,CAAA,EAAA,EAAA,aAA6B,GAAA,IAAA,CAAA,EAAA;AACrD,QAAA,IAAM,aAAa,GAAG,aAAa,kBAAO,MAAM,IAAI,EAAE,EAAK,mBAAmB,CAAE,GAAE,MAAM,IAAI,EAAE,CAAC;AAC/F,QAAA,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,EAAE,kBAAkB,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;KAC7F,CAAA;AAED;;;AAGG;IACH,uBAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,QAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,SAAS,EAAE,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;KACnF,CAAA;AAED;;;AAGG;IACH,uBAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,WAAmB,EAAA;AAC9B,QAAA,gBAAgB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;AAClD,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,YAAY,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC;KACzF,CAAA;AAED;;;AAGG;IACH,uBAAwB,CAAA,SAAA,CAAA,wBAAA,GAAxB,UAAyB,WAAmB,EAAA;AACxC,QAAA,gBAAgB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;AAClD,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,eAAe,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC;KAC5F,CAAA;AAED;;;AAGG;IACH,uBAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,WAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,aAAa,EAAE,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC;KAC1F,CAAA;AAED;;;AAGG;IACH,uBAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,UAAkB,EAAA;AAC5B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;KAC7E,CAAA;AAED;;;AAGG;IACH,uBAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,SAAiB,EAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;KAC3E,CAAA;AAED;;;AAGG;IACH,uBAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,SAAiB,EAAA;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,kBAAkB,CAAC,MAAA,GAAO,SAAW,CAAC,CAAC,CAAC;KACvF,CAAA;AAED;;;AAGG;IACH,uBAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,UAAsB,EAAA;QAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,kBAAkB,CAAC,MAAO,GAAA,UAAU,CAAC,GAAG,GAAA,GAAA,GAAI,UAAU,CAAC,IAAM,CAAC,CAAC,CAAC;KAC/G,CAAA;AAED;;;AAGG;IACH,uBAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,GAAW,EAAA;AACd,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;KAC9D,CAAA;AAED;;;AAGG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,MAAe,EAAE,kBAAkC,EAAA;QACzD,IAAM,YAAY,GAAG,IAAI,CAAC,6BAA6B,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;AACpF,QAAA,gBAAgB,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,MAAM,EAAE,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC;KACpF,CAAA;AAED;;;AAGG;IACH,uBAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,aAAqB,EAAA;AAClC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC;KAChG,CAAA;AAED;;;AAGG;IACH,uBAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,WAAwB,EAAA;;AAEnC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,YAAY,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;AACtE,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,YAAY,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;QAC1E,IAAI,WAAW,CAAC,EAAE,EAAE;AAChB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC;AACvE,SAAA;QACD,IAAI,WAAW,CAAC,GAAG,EAAE;AACjB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,YAAY,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;AACzE,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,uBAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvB,UAAwB,YAAkC,EAAA;AACtD,QAAA,IAAI,YAAY,KAAZ,IAAA,IAAA,YAAY,uBAAZ,YAAY,CAAE,OAAO,EAAE;AACvB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;AAC5E,SAAA;AAED,QAAA,IAAI,YAAY,KAAZ,IAAA,IAAA,YAAY,uBAAZ,YAAY,CAAE,UAAU,EAAE;AAC1B,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,SAAS,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;AAC9E,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,uBAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,MAAc,EAAA;AACpB,QAAA,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAG,GAAA,kBAAkB,CAAC,MAAQ,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;KACnF,CAAA;AAED;;;AAGG;IACH,uBAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,KAAa,EAAA;AAClB,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC7B,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC5E,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,uBAAQ,CAAA,SAAA,CAAA,QAAA,GAAR,UAAS,KAAa,EAAA;AAClB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;KAC5E,CAAA;AAED;;;;;AAKG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtB,UACI,aAAqB,EACrB,mBAA2B,EAAA;AAE3B,QAAA,gBAAgB,CAAC,2BAA2B,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;QACjF,IAAI,aAAa,IAAI,mBAAmB,EAAE;AACtC,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,cAAc,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC;AAC1F,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,mBAAmB,CAAC,CAAC,CAAC;AAC1G,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,wBAAwB,CAAC,qCAAqC,EAAE,CAAC;AAC1E,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,uBAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,IAAY,EAAA;AAC7B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;KAC1E,CAAA;AAED;;;AAGG;IACH,uBAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,IAAY,EAAA;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;KACjF,CAAA;AAED;;;AAGG;IACH,uBAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,YAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,aAAa,EAAE,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC;KAC3F,CAAA;AAED;;;AAGG;IACH,uBAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,YAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,aAAa,EAAE,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC;KAC3F,CAAA;AAED;;;AAGG;IACH,uBAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,YAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,aAAa,EAAE,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC;KAC3F,CAAA;AAED;;;AAGG;IACH,uBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,eAAuB,EAAA;AACtC,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;AACvC,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC;AACjG,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,uBAAsB,CAAA,SAAA,CAAA,sBAAA,GAAtB,UAAuB,mBAA2B,EAAA;AAC9C,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;AAC3C,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,kBAAkB,CAAC,mBAAmB,CAAC,CAAC,CAAC;AAC1G,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,uBAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,YAAoB,EAAA;AAChC,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,aAAa,EAAE,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC;KAC3F,CAAA;AAED;;;AAGG;IACH,uBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,QAAgB,EAAA;AAC/B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC7F,CAAA;AAED;;;AAGG;IACH,uBAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,SAAiB,EAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,UAAU,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;KACrF,CAAA;AAED;;;AAGG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;QACI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;KACzC,CAAA;AAED;;;AAGG;IACH,uBAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvB,UAAwB,QAAoB,EAAA;QAA5C,IAKC,KAAA,GAAA,IAAA,CAAA;AAJG,QAAA,IAAM,iBAAiB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACvF,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;AACvC,YAAA,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,SAAC,CAAC,CAAC;KACN,CAAA;AAED,IAAA,uBAAA,CAAA,SAAA,CAAA,6BAA6B,GAA7B,UAA8B,MAAe,EAAE,kBAAkC,EAAA;AAC7E,QAAA,IAAI,YAAoB,CAAC;;QAGzB,IAAI,CAAC,MAAM,EAAE;YACT,YAAY,GAAG,EAAE,CAAC;AACrB,SAAA;AAAM,aAAA;YACH,IAAI;AACA,gBAAA,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACrC,aAAA;AAAC,YAAA,OAAM,CAAC,EAAE;AACP,gBAAA,MAAM,wBAAwB,CAAC,+BAA+B,EAAE,CAAC;AACpE,aAAA;AACJ,SAAA;AAED,QAAA,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAC;;AAE7D,gBAAA,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;AACrD,aAAA;;YAGD,YAAY,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG;AACrE,gBAAA,MAAM,EAAE,kBAAkB;aAC7B,CAAC;AACL,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;KACvC,CAAA;AAED;;;AAGG;IACH,uBAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,QAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,sBAAsB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;KACtF,CAAA;AAED;;;AAGG;IACH,uBAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,QAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,sBAAsB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;KACtF,CAAA;AAED;;;AAGG;IACH,uBAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,SAAiB,EAAA;AACzB,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AACjC,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,UAAU,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC7E,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,OAAO,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC;AAClF,SAAA;KACJ,CAAA;AAED;;AAEG;IACH,uBAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UAAU,YAAoB,EAAA;AAC1B,QAAA,IAAG,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;AACnC,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,UAAU,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC7E,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,OAAO,EAAE,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC;AACrF,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,uBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,sBAA8C,EAAA;AAC7D,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,iCAAiC,EAAE,CAAC,CAAC;AACxH,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,8BAA8B,EAAE,CAAC,CAAC;KACxH,CAAA;AAED;;AAEG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;AACI,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;KAC9G,CAAA;AAED;;AAEG;IACH,uBAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,UAAkB,EAAA;AAC5B,QAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;KACvF,CAAA;AAED;;AAEG;AACH,IAAA,uBAAA,CAAA,SAAA,CAAA,iBAAiB,GAAjB,YAAA;AACI,QAAA,IAAM,mBAAmB,GAAkB,IAAI,KAAK,EAAU,CAAC;QAE/D,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG,EAAA;AAC/B,YAAA,mBAAmB,CAAC,IAAI,CAAI,GAAG,GAAI,GAAA,GAAA,KAAO,CAAC,CAAC;AAChD,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACxC,CAAA;IACL,OAAC,uBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}