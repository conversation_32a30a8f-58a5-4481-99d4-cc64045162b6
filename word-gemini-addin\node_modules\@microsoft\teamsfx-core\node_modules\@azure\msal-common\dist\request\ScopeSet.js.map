{"version": 3, "file": "ScopeSet.js", "sources": ["../../src/request/ScopeSet.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { Constants, OIDC_SCOPES } from \"../utils/Constants\";\r\n\r\n/**\r\n * The ScopeSet class creates a set of scopes. Scopes are case-insensitive, unique values, so the Set object in JS makes\r\n * the most sense to implement for this class. All scopes are trimmed and converted to lower case strings in intersection and union functions\r\n * to ensure uniqueness of strings.\r\n */\r\nexport class ScopeSet {\r\n    // Scopes as a Set of strings\r\n    private scopes: Set<string>;\r\n\r\n    constructor(inputScopes: Array<string>) {\r\n        // Filter empty string and null/undefined array items\r\n        const scopeArr = inputScopes ? StringUtils.trimArrayEntries([...inputScopes]) : [];\r\n        const filteredInput = scopeArr ? StringUtils.removeEmptyStringsFromArray(scopeArr) : [];\r\n\r\n        // Validate and filter scopes (validate function throws if validation fails)\r\n        this.validateInputScopes(filteredInput);\r\n\r\n        this.scopes = new Set<string>(); // Iterator in constructor not supported by IE11\r\n        filteredInput.forEach(scope => this.scopes.add(scope));\r\n    }\r\n\r\n    /**\r\n     * Factory method to create ScopeSet from space-delimited string\r\n     * @param inputScopeString\r\n     * @param appClientId\r\n     * @param scopesRequired\r\n     */\r\n    static fromString(inputScopeString: string): ScopeSet {\r\n        const scopeString = inputScopeString || Constants.EMPTY_STRING;\r\n        const inputScopes: Array<string> = scopeString.split(\" \");\r\n        return new ScopeSet(inputScopes);\r\n    }\r\n\r\n    /**\r\n     * Creates the set of scopes to search for in cache lookups\r\n     * @param inputScopeString \r\n     * @returns \r\n     */\r\n    static createSearchScopes(inputScopeString: Array<string>): ScopeSet {\r\n        const scopeSet = new ScopeSet(inputScopeString);\r\n        if (!scopeSet.containsOnlyOIDCScopes()) {\r\n            scopeSet.removeOIDCScopes();\r\n        } else {\r\n            scopeSet.removeScope(Constants.OFFLINE_ACCESS_SCOPE);\r\n        }\r\n\r\n        return scopeSet;\r\n    }\r\n\r\n    /**\r\n     * Used to validate the scopes input parameter requested  by the developer.\r\n     * @param {Array<string>} inputScopes - Developer requested permissions. Not all scopes are guaranteed to be included in the access token returned.\r\n     * @param {boolean} scopesRequired - Boolean indicating whether the scopes array is required or not\r\n     */\r\n    private validateInputScopes(inputScopes: Array<string>): void {\r\n        // Check if scopes are required but not given or is an empty array\r\n        if (!inputScopes || inputScopes.length < 1) {\r\n            throw ClientConfigurationError.createEmptyScopesArrayError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Check if a given scope is present in this set of scopes.\r\n     * @param scope\r\n     */\r\n    containsScope(scope: string): boolean {\r\n        const lowerCaseScopes = this.printScopesLowerCase().split(\" \");\r\n        const lowerCaseScopesSet = new ScopeSet(lowerCaseScopes);\r\n        // compare lowercase scopes\r\n        return !StringUtils.isEmpty(scope) ? lowerCaseScopesSet.scopes.has(scope.toLowerCase()) : false;\r\n    }\r\n\r\n    /**\r\n     * Check if a set of scopes is present in this set of scopes.\r\n     * @param scopeSet\r\n     */\r\n    containsScopeSet(scopeSet: ScopeSet): boolean {\r\n        if (!scopeSet || scopeSet.scopes.size <= 0) {\r\n            return false;\r\n        }\r\n\r\n        return (this.scopes.size >= scopeSet.scopes.size && scopeSet.asArray().every(scope => this.containsScope(scope)));\r\n    }\r\n\r\n    /**\r\n     * Check if set of scopes contains only the defaults\r\n     */\r\n    containsOnlyOIDCScopes(): boolean {\r\n        let defaultScopeCount = 0;\r\n        OIDC_SCOPES.forEach((defaultScope: string) => {\r\n            if (this.containsScope(defaultScope)) {\r\n                defaultScopeCount += 1;\r\n            }\r\n        });\r\n\r\n        return this.scopes.size === defaultScopeCount;\r\n    }\r\n\r\n    /**\r\n     * Appends single scope if passed\r\n     * @param newScope\r\n     */\r\n    appendScope(newScope: string): void {\r\n        if (!StringUtils.isEmpty(newScope)) {\r\n            this.scopes.add(newScope.trim());\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Appends multiple scopes if passed\r\n     * @param newScopes\r\n     */\r\n    appendScopes(newScopes: Array<string>): void {\r\n        try {\r\n            newScopes.forEach(newScope => this.appendScope(newScope));\r\n        } catch (e) {\r\n            throw ClientAuthError.createAppendScopeSetError(e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes element from set of scopes.\r\n     * @param scope\r\n     */\r\n    removeScope(scope: string): void {\r\n        if (StringUtils.isEmpty(scope)) {\r\n            throw ClientAuthError.createRemoveEmptyScopeFromSetError(scope);\r\n        }\r\n        this.scopes.delete(scope.trim());\r\n    }\r\n\r\n    /**\r\n     * Removes default scopes from set of scopes\r\n     * Primarily used to prevent cache misses if the default scopes are not returned from the server\r\n     */\r\n    removeOIDCScopes(): void {\r\n        OIDC_SCOPES.forEach((defaultScope: string) => {\r\n            this.scopes.delete(defaultScope);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Combines an array of scopes with the current set of scopes.\r\n     * @param otherScopes\r\n     */\r\n    unionScopeSets(otherScopes: ScopeSet): Set<string> {\r\n        if (!otherScopes) {\r\n            throw ClientAuthError.createEmptyInputScopeSetError();\r\n        }\r\n        const unionScopes = new Set<string>(); // Iterator in constructor not supported in IE11\r\n        otherScopes.scopes.forEach(scope => unionScopes.add(scope.toLowerCase()));\r\n        this.scopes.forEach(scope => unionScopes.add(scope.toLowerCase()));\r\n        return unionScopes;\r\n    }\r\n\r\n    /**\r\n     * Check if scopes intersect between this set and another.\r\n     * @param otherScopes\r\n     */\r\n    intersectingScopeSets(otherScopes: ScopeSet): boolean {\r\n        if (!otherScopes) {\r\n            throw ClientAuthError.createEmptyInputScopeSetError();\r\n        }\r\n        \r\n        // Do not allow OIDC scopes to be the only intersecting scopes\r\n        if (!otherScopes.containsOnlyOIDCScopes()) {\r\n            otherScopes.removeOIDCScopes();\r\n        }\r\n        const unionScopes = this.unionScopeSets(otherScopes);\r\n        const sizeOtherScopes = otherScopes.getScopeCount();\r\n        const sizeThisScopes = this.getScopeCount();\r\n        const sizeUnionScopes = unionScopes.size;\r\n        return sizeUnionScopes < (sizeThisScopes + sizeOtherScopes);\r\n    }\r\n\r\n    /**\r\n     * Returns size of set of scopes.\r\n     */\r\n    getScopeCount(): number {\r\n        return this.scopes.size;\r\n    }\r\n\r\n    /**\r\n     * Returns the scopes as an array of string values\r\n     */\r\n    asArray(): Array<string> {\r\n        const array: Array<string> = [];\r\n        this.scopes.forEach(val => array.push(val));\r\n        return array;\r\n    }\r\n\r\n    /**\r\n     * Prints scopes into a space-delimited string\r\n     */\r\n    printScopes(): string {\r\n        if (this.scopes) {\r\n            const scopeArr = this.asArray();\r\n            return scopeArr.join(\" \");\r\n        }\r\n        return Constants.EMPTY_STRING;\r\n    }\r\n\r\n    /**\r\n     * Prints scopes into a space-delimited lower-case string (used for caching)\r\n     */\r\n    printScopesLowerCase(): string {\r\n        return this.printScopes().toLowerCase();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAGG;AAOH;;;;AAIG;AACH,IAAA,QAAA,kBAAA,YAAA;AAII,IAAA,SAAA,QAAA,CAAY,WAA0B,EAAA;QAAtC,IAUC,KAAA,GAAA,IAAA,CAAA;;AARG,QAAA,IAAM,QAAQ,GAAG,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAA,cAAA,CAAK,WAAW,CAAE,CAAA,GAAG,EAAE,CAAC;AACnF,QAAA,IAAM,aAAa,GAAG,QAAQ,GAAG,WAAW,CAAC,2BAA2B,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;;AAGxF,QAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;QAExC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;AAChC,QAAA,aAAa,CAAC,OAAO,CAAC,UAAA,KAAK,EAAA,EAAI,OAAA,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAtB,EAAsB,CAAC,CAAC;KAC1D;AAED;;;;;AAKG;IACI,QAAU,CAAA,UAAA,GAAjB,UAAkB,gBAAwB,EAAA;AACtC,QAAA,IAAM,WAAW,GAAG,gBAAgB,IAAI,SAAS,CAAC,YAAY,CAAC;QAC/D,IAAM,WAAW,GAAkB,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC1D,QAAA,OAAO,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC;KACpC,CAAA;AAED;;;;AAIG;IACI,QAAkB,CAAA,kBAAA,GAAzB,UAA0B,gBAA+B,EAAA;AACrD,QAAA,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAE,EAAE;YACpC,QAAQ,CAAC,gBAAgB,EAAE,CAAC;AAC/B,SAAA;AAAM,aAAA;AACH,YAAA,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;AACxD,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB,CAAA;AAED;;;;AAIG;IACK,QAAmB,CAAA,SAAA,CAAA,mBAAA,GAA3B,UAA4B,WAA0B,EAAA;;QAElD,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AACxC,YAAA,MAAM,wBAAwB,CAAC,2BAA2B,EAAE,CAAC;AAChE,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,QAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,KAAa,EAAA;QACvB,IAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAC/D,QAAA,IAAM,kBAAkB,GAAG,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC;;QAEzD,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC;KACnG,CAAA;AAED;;;AAGG;IACH,QAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,UAAiB,QAAkB,EAAA;QAAnC,IAMC,KAAA,GAAA,IAAA,CAAA;QALG,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,EAAE;AACxC,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,UAAA,KAAK,EAAA,EAAI,OAAA,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA,EAAA,CAAC,EAAE;KACrH,CAAA;AAED;;AAEG;AACH,IAAA,QAAA,CAAA,SAAA,CAAA,sBAAsB,GAAtB,YAAA;QAAA,IASC,KAAA,GAAA,IAAA,CAAA;QARG,IAAI,iBAAiB,GAAG,CAAC,CAAC;AAC1B,QAAA,WAAW,CAAC,OAAO,CAAC,UAAC,YAAoB,EAAA;AACrC,YAAA,IAAI,KAAI,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE;gBAClC,iBAAiB,IAAI,CAAC,CAAC;AAC1B,aAAA;AACL,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,iBAAiB,CAAC;KACjD,CAAA;AAED;;;AAGG;IACH,QAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,QAAgB,EAAA;AACxB,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;AACpC,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,QAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,SAAwB,EAAA;QAArC,IAMC,KAAA,GAAA,IAAA,CAAA;QALG,IAAI;AACA,YAAA,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ,IAAI,OAAA,KAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAA1B,EAA0B,CAAC,CAAC;AAC7D,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,eAAe,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;AACtD,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,QAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAAY,KAAa,EAAA;AACrB,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAA,MAAM,eAAe,CAAC,kCAAkC,CAAC,KAAK,CAAC,CAAC;AACnE,SAAA;QACD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;KACpC,CAAA;AAED;;;AAGG;AACH,IAAA,QAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;QAAA,IAIC,KAAA,GAAA,IAAA,CAAA;AAHG,QAAA,WAAW,CAAC,OAAO,CAAC,UAAC,YAAoB,EAAA;AACrC,YAAA,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACrC,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;AAGG;IACH,QAAc,CAAA,SAAA,CAAA,cAAA,GAAd,UAAe,WAAqB,EAAA;QAChC,IAAI,CAAC,WAAW,EAAE;AACd,YAAA,MAAM,eAAe,CAAC,6BAA6B,EAAE,CAAC;AACzD,SAAA;AACD,QAAA,IAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;QACtC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK,IAAI,OAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAA,EAAA,CAAC,CAAC;QAC1E,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK,IAAI,OAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAA,EAAA,CAAC,CAAC;AACnE,QAAA,OAAO,WAAW,CAAC;KACtB,CAAA;AAED;;;AAGG;IACH,QAAqB,CAAA,SAAA,CAAA,qBAAA,GAArB,UAAsB,WAAqB,EAAA;QACvC,IAAI,CAAC,WAAW,EAAE;AACd,YAAA,MAAM,eAAe,CAAC,6BAA6B,EAAE,CAAC;AACzD,SAAA;;AAGD,QAAA,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,EAAE;YACvC,WAAW,CAAC,gBAAgB,EAAE,CAAC;AAClC,SAAA;QACD,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AACrD,QAAA,IAAM,eAAe,GAAG,WAAW,CAAC,aAAa,EAAE,CAAC;AACpD,QAAA,IAAM,cAAc,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AAC5C,QAAA,IAAM,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC;AACzC,QAAA,OAAO,eAAe,IAAI,cAAc,GAAG,eAAe,CAAC,CAAC;KAC/D,CAAA;AAED;;AAEG;AACH,IAAA,QAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;AACI,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;KAC3B,CAAA;AAED;;AAEG;AACH,IAAA,QAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QACI,IAAM,KAAK,GAAkB,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAA,GAAG,EAAI,EAAA,OAAA,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAf,EAAe,CAAC,CAAC;AAC5C,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;AAEG;AACH,IAAA,QAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;QACI,IAAI,IAAI,CAAC,MAAM,EAAE;AACb,YAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AAChC,YAAA,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,SAAA;QACD,OAAO,SAAS,CAAC,YAAY,CAAC;KACjC,CAAA;AAED;;AAEG;AACH,IAAA,QAAA,CAAA,SAAA,CAAA,oBAAoB,GAApB,YAAA;AACI,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,WAAW,EAAE,CAAC;KAC3C,CAAA;IACL,OAAC,QAAA,CAAA;AAAD,CAAC,EAAA;;;;"}