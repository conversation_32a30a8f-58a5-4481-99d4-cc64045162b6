{"version": 3, "file": "ResponseHandler.js", "sources": ["../../src/response/ResponseHandler.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ServerAuthorizationTokenResponse } from \"./ServerAuthorizationTokenResponse\";\r\nimport { buildClientInfo} from \"../account/ClientInfo\";\r\nimport { ICrypto } from \"../crypto/ICrypto\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { ServerAuthorizationCodeResponse } from \"./ServerAuthorizationCodeResponse\";\r\nimport { Logger } from \"../logger/Logger\";\r\nimport { ServerError } from \"../error/ServerError\";\r\nimport { AuthToken } from \"../account/AuthToken\";\r\nimport { ScopeSet } from \"../request/ScopeSet\";\r\nimport { AuthenticationResult } from \"./AuthenticationResult\";\r\nimport { AccountEntity } from \"../cache/entities/AccountEntity\";\r\nimport { Authority } from \"../authority/Authority\";\r\nimport { AuthorityType } from \"../authority/AuthorityType\";\r\nimport { IdTokenEntity } from \"../cache/entities/IdTokenEntity\";\r\nimport { AccessTokenEntity } from \"../cache/entities/AccessTokenEntity\";\r\nimport { RefreshTokenEntity } from \"../cache/entities/RefreshTokenEntity\";\r\nimport { InteractionRequiredAuthError } from \"../error/InteractionRequiredAuthError\";\r\nimport { CacheRecord } from \"../cache/entities/CacheRecord\";\r\nimport { CacheManager } from \"../cache/CacheManager\";\r\nimport { ProtocolUtils, RequestStateObject } from \"../utils/ProtocolUtils\";\r\nimport { AuthenticationScheme, Constants, THE_FAMILY_ID } from \"../utils/Constants\";\r\nimport { PopTokenGenerator } from \"../crypto/PopTokenGenerator\";\r\nimport { AppMetadataEntity } from \"../cache/entities/AppMetadataEntity\";\r\nimport { ICachePlugin } from \"../cache/interface/ICachePlugin\";\r\nimport { TokenCacheContext } from \"../cache/persistence/TokenCacheContext\";\r\nimport { ISerializableTokenCache } from \"../cache/interface/ISerializableTokenCache\";\r\nimport { AuthorizationCodePayload } from \"./AuthorizationCodePayload\";\r\nimport { BaseAuthRequest } from \"../request/BaseAuthRequest\";\r\nimport { IPerformanceClient } from \"../telemetry/performance/IPerformanceClient\";\r\nimport { PerformanceEvents } from \"../telemetry/performance/PerformanceEvent\";\r\n\r\n/**\r\n * Class that handles response parsing.\r\n */\r\nexport class ResponseHandler {\r\n    private clientId: string;\r\n    private cacheStorage: CacheManager;\r\n    private cryptoObj: ICrypto;\r\n    private logger: Logger;\r\n    private homeAccountIdentifier: string;\r\n    private serializableCache: ISerializableTokenCache | null;\r\n    private persistencePlugin: ICachePlugin | null;\r\n    private performanceClient?: IPerformanceClient;\r\n\r\n    constructor(clientId: string, cacheStorage: CacheManager, cryptoObj: ICrypto, logger: Logger, serializableCache: ISerializableTokenCache | null, persistencePlugin: ICachePlugin | null, performanceClient?: IPerformanceClient) {\r\n        this.clientId = clientId;\r\n        this.cacheStorage = cacheStorage;\r\n        this.cryptoObj = cryptoObj;\r\n        this.logger = logger;\r\n        this.serializableCache = serializableCache;\r\n        this.persistencePlugin = persistencePlugin;\r\n        this.performanceClient = performanceClient;\r\n    }\r\n\r\n    /**\r\n     * Function which validates server authorization code response.\r\n     * @param serverResponseHash\r\n     * @param cachedState\r\n     * @param cryptoObj\r\n     */\r\n    validateServerAuthorizationCodeResponse(serverResponseHash: ServerAuthorizationCodeResponse, cachedState: string, cryptoObj: ICrypto): void {\r\n\r\n        if (!serverResponseHash.state || !cachedState) {\r\n            throw !serverResponseHash.state ? ClientAuthError.createStateNotFoundError(\"Server State\") : ClientAuthError.createStateNotFoundError(\"Cached State\");\r\n        }\r\n\r\n        if (decodeURIComponent(serverResponseHash.state) !== decodeURIComponent(cachedState)) {\r\n            throw ClientAuthError.createStateMismatchError();\r\n        }\r\n\r\n        // Check for error\r\n        if (serverResponseHash.error || serverResponseHash.error_description || serverResponseHash.suberror) {\r\n            if (InteractionRequiredAuthError.isInteractionRequiredError(serverResponseHash.error, serverResponseHash.error_description, serverResponseHash.suberror)) {\r\n                throw new InteractionRequiredAuthError(\r\n                    serverResponseHash.error || Constants.EMPTY_STRING,\r\n                    serverResponseHash.error_description,\r\n                    serverResponseHash.suberror,\r\n                    serverResponseHash.timestamp || Constants.EMPTY_STRING,\r\n                    serverResponseHash.trace_id || Constants.EMPTY_STRING,\r\n                    serverResponseHash.correlation_id || Constants.EMPTY_STRING,\r\n                    serverResponseHash.claims || Constants.EMPTY_STRING,\r\n                );\r\n            }\r\n\r\n            throw new ServerError(serverResponseHash.error || Constants.EMPTY_STRING, serverResponseHash.error_description, serverResponseHash.suberror);\r\n        }\r\n\r\n        if (serverResponseHash.client_info) {\r\n            buildClientInfo(serverResponseHash.client_info, cryptoObj);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Function which validates server authorization token response.\r\n     * @param serverResponse\r\n     */\r\n    validateTokenResponse(serverResponse: ServerAuthorizationTokenResponse): void {\r\n        // Check for error\r\n        if (serverResponse.error || serverResponse.error_description || serverResponse.suberror) {\r\n            if (InteractionRequiredAuthError.isInteractionRequiredError(serverResponse.error, serverResponse.error_description, serverResponse.suberror)) {\r\n                throw new InteractionRequiredAuthError(\r\n                    serverResponse.error,\r\n                    serverResponse.error_description,\r\n                    serverResponse.suberror,\r\n                    serverResponse.timestamp || Constants.EMPTY_STRING,\r\n                    serverResponse.trace_id || Constants.EMPTY_STRING,\r\n                    serverResponse.correlation_id || Constants.EMPTY_STRING,\r\n                    serverResponse.claims || Constants.EMPTY_STRING,\r\n                );\r\n            }\r\n\r\n            const errString = `${serverResponse.error_codes} - [${serverResponse.timestamp}]: ${serverResponse.error_description} - Correlation ID: ${serverResponse.correlation_id} - Trace ID: ${serverResponse.trace_id}`;\r\n            throw new ServerError(serverResponse.error, errString, serverResponse.suberror);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns a constructed token response based on given string. Also manages the cache updates and cleanups.\r\n     * @param serverTokenResponse\r\n     * @param authority\r\n     */\r\n    async handleServerTokenResponse(\r\n        serverTokenResponse: ServerAuthorizationTokenResponse,\r\n        authority: Authority,\r\n        reqTimestamp: number,\r\n        request: BaseAuthRequest,\r\n        authCodePayload?: AuthorizationCodePayload,\r\n        userAssertionHash?: string,\r\n        handlingRefreshTokenResponse?: boolean,\r\n        forceCacheRefreshTokenResponse?: boolean,\r\n        serverRequestId?: string): Promise<AuthenticationResult> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.HandleServerTokenResponse, serverTokenResponse.correlation_id);\r\n\r\n        // create an idToken object (not entity)\r\n        let idTokenObj: AuthToken | undefined;\r\n        if (serverTokenResponse.id_token) {\r\n            idTokenObj = new AuthToken(serverTokenResponse.id_token || Constants.EMPTY_STRING, this.cryptoObj);\r\n\r\n            // token nonce check (TODO: Add a warning if no nonce is given?)\r\n            if (authCodePayload && !StringUtils.isEmpty(authCodePayload.nonce)) {\r\n                if (idTokenObj.claims.nonce !== authCodePayload.nonce) {\r\n                    throw ClientAuthError.createNonceMismatchError();\r\n                }\r\n            }\r\n\r\n            // token max_age check\r\n            if (request.maxAge || (request.maxAge === 0)) {\r\n                const authTime = idTokenObj.claims.auth_time;\r\n                if (!authTime) {\r\n                    throw ClientAuthError.createAuthTimeNotFoundError();\r\n                }\r\n\r\n                AuthToken.checkMaxAge(authTime, request.maxAge);\r\n            }\r\n        }\r\n\r\n        // generate homeAccountId\r\n        this.homeAccountIdentifier = AccountEntity.generateHomeAccountId(serverTokenResponse.client_info || Constants.EMPTY_STRING, authority.authorityType, this.logger, this.cryptoObj, idTokenObj);\r\n\r\n        // save the response tokens\r\n        let requestStateObj: RequestStateObject | undefined;\r\n        if (!!authCodePayload && !!authCodePayload.state) {\r\n            requestStateObj = ProtocolUtils.parseRequestState(this.cryptoObj, authCodePayload.state);\r\n        }\r\n\r\n        // Add keyId from request to serverTokenResponse if defined\r\n        serverTokenResponse.key_id = serverTokenResponse.key_id || request.sshKid || undefined;\r\n\r\n        const cacheRecord = this.generateCacheRecord(serverTokenResponse, authority, reqTimestamp, request, idTokenObj, userAssertionHash, authCodePayload);\r\n        let cacheContext;\r\n        try {\r\n            if (this.persistencePlugin && this.serializableCache) {\r\n                this.logger.verbose(\"Persistence enabled, calling beforeCacheAccess\");\r\n                cacheContext = new TokenCacheContext(this.serializableCache, true);\r\n                await this.persistencePlugin.beforeCacheAccess(cacheContext);\r\n            }\r\n            /*\r\n             * When saving a refreshed tokens to the cache, it is expected that the account that was used is present in the cache.\r\n             * If not present, we should return null, as it's the case that another application called removeAccount in between\r\n             * the calls to getAllAccounts and acquireTokenSilent. We should not overwrite that removal, unless explicitly flagged by\r\n             * the developer, as in the case of refresh token flow used in ADAL Node to MSAL Node migration.\r\n             */\r\n            if (handlingRefreshTokenResponse && !forceCacheRefreshTokenResponse && cacheRecord.account) {\r\n                const key = cacheRecord.account.generateAccountKey();\r\n                const account = this.cacheStorage.getAccount(key);\r\n                if (!account) {\r\n                    this.logger.warning(\"Account used to refresh tokens not in persistence, refreshed tokens will not be stored in the cache\");\r\n                    return ResponseHandler.generateAuthenticationResult(this.cryptoObj, authority, cacheRecord, false, request, idTokenObj, requestStateObj, undefined, serverRequestId);\r\n                }\r\n            }\r\n            await this.cacheStorage.saveCacheRecord(cacheRecord);\r\n        } finally {\r\n            if (this.persistencePlugin && this.serializableCache && cacheContext) {\r\n                this.logger.verbose(\"Persistence enabled, calling afterCacheAccess\");\r\n                await this.persistencePlugin.afterCacheAccess(cacheContext);\r\n            }\r\n        }\r\n        return ResponseHandler.generateAuthenticationResult(this.cryptoObj, authority, cacheRecord, false, request, idTokenObj, requestStateObj, serverTokenResponse, serverRequestId);\r\n    }\r\n\r\n    /**\r\n     * Generates CacheRecord\r\n     * @param serverTokenResponse\r\n     * @param idTokenObj\r\n     * @param authority\r\n     */\r\n    private generateCacheRecord(serverTokenResponse: ServerAuthorizationTokenResponse, authority: Authority, reqTimestamp: number, request: BaseAuthRequest, idTokenObj?: AuthToken, userAssertionHash?: string, authCodePayload?: AuthorizationCodePayload): CacheRecord {\r\n        const env = authority.getPreferredCache();\r\n        if (StringUtils.isEmpty(env)) {\r\n            throw ClientAuthError.createInvalidCacheEnvironmentError();\r\n        }\r\n\r\n        // IdToken: non AAD scenarios can have empty realm\r\n        let cachedIdToken: IdTokenEntity | undefined;\r\n        let cachedAccount: AccountEntity | undefined;\r\n        if (!StringUtils.isEmpty(serverTokenResponse.id_token) && !!idTokenObj) {\r\n            cachedIdToken = IdTokenEntity.createIdTokenEntity(\r\n                this.homeAccountIdentifier,\r\n                env,\r\n                serverTokenResponse.id_token || Constants.EMPTY_STRING,\r\n                this.clientId,\r\n                idTokenObj.claims.tid || Constants.EMPTY_STRING,\r\n            );\r\n\r\n            cachedAccount = this.generateAccountEntity(\r\n                serverTokenResponse,\r\n                idTokenObj,\r\n                authority,\r\n                authCodePayload\r\n            );\r\n        }\r\n\r\n        // AccessToken\r\n        let cachedAccessToken: AccessTokenEntity | null = null;\r\n        if (!StringUtils.isEmpty(serverTokenResponse.access_token)) {\r\n\r\n            // If scopes not returned in server response, use request scopes\r\n            const responseScopes = serverTokenResponse.scope ? ScopeSet.fromString(serverTokenResponse.scope) : new ScopeSet(request.scopes || []);\r\n\r\n            /*\r\n             * Use timestamp calculated before request\r\n             * Server may return timestamps as strings, parse to numbers if so.\r\n             */\r\n            const expiresIn: number = (typeof serverTokenResponse.expires_in === \"string\" ? parseInt(serverTokenResponse.expires_in, 10) : serverTokenResponse.expires_in) || 0;\r\n            const extExpiresIn: number = (typeof serverTokenResponse.ext_expires_in === \"string\" ? parseInt(serverTokenResponse.ext_expires_in, 10) : serverTokenResponse.ext_expires_in) || 0;\r\n            const refreshIn: number | undefined = (typeof serverTokenResponse.refresh_in === \"string\" ? parseInt(serverTokenResponse.refresh_in, 10) : serverTokenResponse.refresh_in) || undefined;\r\n            const tokenExpirationSeconds = reqTimestamp + expiresIn;\r\n            const extendedTokenExpirationSeconds = tokenExpirationSeconds + extExpiresIn;\r\n            const refreshOnSeconds = refreshIn && refreshIn > 0 ? reqTimestamp + refreshIn : undefined;\r\n\r\n            // non AAD scenarios can have empty realm\r\n            cachedAccessToken = AccessTokenEntity.createAccessTokenEntity(\r\n                this.homeAccountIdentifier,\r\n                env,\r\n                serverTokenResponse.access_token || Constants.EMPTY_STRING,\r\n                this.clientId,\r\n                idTokenObj ? idTokenObj.claims.tid || Constants.EMPTY_STRING : authority.tenant,\r\n                responseScopes.printScopes(),\r\n                tokenExpirationSeconds,\r\n                extendedTokenExpirationSeconds,\r\n                this.cryptoObj,\r\n                refreshOnSeconds,\r\n                serverTokenResponse.token_type,\r\n                userAssertionHash,\r\n                serverTokenResponse.key_id,\r\n                request.claims,\r\n                request.requestedClaimsHash\r\n            );\r\n        }\r\n\r\n        // refreshToken\r\n        let cachedRefreshToken: RefreshTokenEntity | null = null;\r\n        if (!StringUtils.isEmpty(serverTokenResponse.refresh_token)) {\r\n            cachedRefreshToken = RefreshTokenEntity.createRefreshTokenEntity(\r\n                this.homeAccountIdentifier,\r\n                env,\r\n                serverTokenResponse.refresh_token || Constants.EMPTY_STRING,\r\n                this.clientId,\r\n                serverTokenResponse.foci,\r\n                userAssertionHash\r\n            );\r\n        }\r\n\r\n        // appMetadata\r\n        let cachedAppMetadata: AppMetadataEntity | null = null;\r\n        if (!StringUtils.isEmpty(serverTokenResponse.foci)) {\r\n            cachedAppMetadata = AppMetadataEntity.createAppMetadataEntity(this.clientId, env, serverTokenResponse.foci);\r\n        }\r\n\r\n        return new CacheRecord(cachedAccount, cachedIdToken, cachedAccessToken, cachedRefreshToken, cachedAppMetadata);\r\n    }\r\n\r\n    /**\r\n     * Generate Account\r\n     * @param serverTokenResponse\r\n     * @param idToken\r\n     * @param authority\r\n     */\r\n    private generateAccountEntity(serverTokenResponse: ServerAuthorizationTokenResponse, idToken: AuthToken, authority: Authority, authCodePayload?: AuthorizationCodePayload): AccountEntity {\r\n        const authorityType = authority.authorityType;\r\n        const cloudGraphHostName = authCodePayload ? authCodePayload.cloud_graph_host_name : Constants.EMPTY_STRING;\r\n        const msGraphhost = authCodePayload ? authCodePayload.msgraph_host : Constants.EMPTY_STRING;\r\n\r\n        // ADFS does not require client_info in the response\r\n        if (authorityType === AuthorityType.Adfs) {\r\n            this.logger.verbose(\"Authority type is ADFS, creating ADFS account\");\r\n            return AccountEntity.createGenericAccount(this.homeAccountIdentifier, idToken, authority, cloudGraphHostName, msGraphhost);\r\n        }\r\n\r\n        // This fallback applies to B2C as well as they fall under an AAD account type.\r\n        if (StringUtils.isEmpty(serverTokenResponse.client_info) && authority.protocolMode === \"AAD\") {\r\n            throw ClientAuthError.createClientInfoEmptyError();\r\n        }\r\n\r\n        return serverTokenResponse.client_info ?\r\n            AccountEntity.createAccount(serverTokenResponse.client_info, this.homeAccountIdentifier, idToken, authority, cloudGraphHostName, msGraphhost) :\r\n            AccountEntity.createGenericAccount(this.homeAccountIdentifier, idToken, authority, cloudGraphHostName, msGraphhost);\r\n    }\r\n\r\n    /**\r\n     * Creates an @AuthenticationResult from @CacheRecord , @IdToken , and a boolean that states whether or not the result is from cache.\r\n     *\r\n     * Optionally takes a state string that is set as-is in the response.\r\n     *\r\n     * @param cacheRecord\r\n     * @param idTokenObj\r\n     * @param fromTokenCache\r\n     * @param stateString\r\n     */\r\n    static async generateAuthenticationResult(\r\n        cryptoObj: ICrypto,\r\n        authority: Authority,\r\n        cacheRecord: CacheRecord,\r\n        fromTokenCache: boolean,\r\n        request: BaseAuthRequest,\r\n        idTokenObj?: AuthToken,\r\n        requestState?: RequestStateObject,\r\n        serverTokenResponse?: ServerAuthorizationTokenResponse,\r\n        requestId?: string,\r\n    ): Promise<AuthenticationResult> {\r\n        let accessToken: string = Constants.EMPTY_STRING;\r\n        let responseScopes: Array<string> = [];\r\n        let expiresOn: Date | null = null;\r\n        let extExpiresOn: Date | undefined;\r\n        let familyId: string = Constants.EMPTY_STRING;\r\n\r\n        if (cacheRecord.accessToken) {\r\n            if (cacheRecord.accessToken.tokenType === AuthenticationScheme.POP) {\r\n                const popTokenGenerator: PopTokenGenerator = new PopTokenGenerator(cryptoObj);\r\n                const { secret, keyId } = cacheRecord.accessToken;\r\n\r\n                if (!keyId) {\r\n                    throw ClientAuthError.createKeyIdMissingError();\r\n                }\r\n\r\n                accessToken = await popTokenGenerator.signPopToken(secret, keyId, request);\r\n            } else {\r\n                accessToken = cacheRecord.accessToken.secret;\r\n            }\r\n            responseScopes = ScopeSet.fromString(cacheRecord.accessToken.target).asArray();\r\n            expiresOn = new Date(Number(cacheRecord.accessToken.expiresOn) * 1000);\r\n            extExpiresOn = new Date(Number(cacheRecord.accessToken.extendedExpiresOn) * 1000);\r\n        }\r\n\r\n        if (cacheRecord.appMetadata) {\r\n            familyId = cacheRecord.appMetadata.familyId === THE_FAMILY_ID ? THE_FAMILY_ID : Constants.EMPTY_STRING;\r\n        }\r\n        const uid = idTokenObj?.claims.oid || idTokenObj?.claims.sub || Constants.EMPTY_STRING;\r\n        const tid = idTokenObj?.claims.tid || Constants.EMPTY_STRING;\r\n\r\n        // for hybrid + native bridge enablement, send back the native account Id\r\n        if(serverTokenResponse?.spa_accountid && !!cacheRecord.account){\r\n            cacheRecord.account.nativeAccountId = serverTokenResponse?.spa_accountid;\r\n        }\r\n\r\n        return {\r\n            authority: authority.canonicalAuthority,\r\n            uniqueId: uid,\r\n            tenantId: tid,\r\n            scopes: responseScopes,\r\n            account: cacheRecord.account ? cacheRecord.account.getAccountInfo() : null,\r\n            idToken: idTokenObj ? idTokenObj.rawToken : Constants.EMPTY_STRING,\r\n            idTokenClaims: idTokenObj ? idTokenObj.claims : {},\r\n            accessToken: accessToken,\r\n            fromCache: fromTokenCache,\r\n            expiresOn: expiresOn,\r\n            correlationId: request.correlationId,\r\n            requestId: requestId || Constants.EMPTY_STRING,\r\n            extExpiresOn: extExpiresOn,\r\n            familyId: familyId,\r\n            tokenType: cacheRecord.accessToken?.tokenType || Constants.EMPTY_STRING,\r\n            state: requestState ? requestState.userRequestState : Constants.EMPTY_STRING,\r\n            cloudGraphHostName: cacheRecord.account?.cloudGraphHostName || Constants.EMPTY_STRING,\r\n            msGraphHost: cacheRecord.account?.msGraphHost || Constants.EMPTY_STRING,\r\n            code: serverTokenResponse?.spa_code,\r\n            fromNativeBroker: false,\r\n        };\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AAkCH;;AAEG;AACH,IAAA,eAAA,kBAAA,YAAA;AAUI,IAAA,SAAA,eAAA,CAAY,QAAgB,EAAE,YAA0B,EAAE,SAAkB,EAAE,MAAc,EAAE,iBAAiD,EAAE,iBAAsC,EAAE,iBAAsC,EAAA;AAC3N,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAED;;;;;AAKG;AACH,IAAA,eAAA,CAAA,SAAA,CAAA,uCAAuC,GAAvC,UAAwC,kBAAmD,EAAE,WAAmB,EAAE,SAAkB,EAAA;AAEhI,QAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,IAAI,CAAC,WAAW,EAAE;YAC3C,MAAM,CAAC,kBAAkB,CAAC,KAAK,GAAG,eAAe,CAAC,wBAAwB,CAAC,cAAc,CAAC,GAAG,eAAe,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;AACzJ,SAAA;QAED,IAAI,kBAAkB,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,kBAAkB,CAAC,WAAW,CAAC,EAAE;AAClF,YAAA,MAAM,eAAe,CAAC,wBAAwB,EAAE,CAAC;AACpD,SAAA;;QAGD,IAAI,kBAAkB,CAAC,KAAK,IAAI,kBAAkB,CAAC,iBAAiB,IAAI,kBAAkB,CAAC,QAAQ,EAAE;AACjG,YAAA,IAAI,4BAA4B,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,KAAK,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,QAAQ,CAAC,EAAE;gBACtJ,MAAM,IAAI,4BAA4B,CAClC,kBAAkB,CAAC,KAAK,IAAI,SAAS,CAAC,YAAY,EAClD,kBAAkB,CAAC,iBAAiB,EACpC,kBAAkB,CAAC,QAAQ,EAC3B,kBAAkB,CAAC,SAAS,IAAI,SAAS,CAAC,YAAY,EACtD,kBAAkB,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY,EACrD,kBAAkB,CAAC,cAAc,IAAI,SAAS,CAAC,YAAY,EAC3D,kBAAkB,CAAC,MAAM,IAAI,SAAS,CAAC,YAAY,CACtD,CAAC;AACL,aAAA;AAED,YAAA,MAAM,IAAI,WAAW,CAAC,kBAAkB,CAAC,KAAK,IAAI,SAAS,CAAC,YAAY,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAChJ,SAAA;QAED,IAAI,kBAAkB,CAAC,WAAW,EAAE;AAChC,YAAA,eAAe,CAAC,kBAAkB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;AAC9D,SAAA;KACJ,CAAA;AAED;;;AAGG;IACH,eAAqB,CAAA,SAAA,CAAA,qBAAA,GAArB,UAAsB,cAAgD,EAAA;;QAElE,IAAI,cAAc,CAAC,KAAK,IAAI,cAAc,CAAC,iBAAiB,IAAI,cAAc,CAAC,QAAQ,EAAE;AACrF,YAAA,IAAI,4BAA4B,CAAC,0BAA0B,CAAC,cAAc,CAAC,KAAK,EAAE,cAAc,CAAC,iBAAiB,EAAE,cAAc,CAAC,QAAQ,CAAC,EAAE;gBAC1I,MAAM,IAAI,4BAA4B,CAClC,cAAc,CAAC,KAAK,EACpB,cAAc,CAAC,iBAAiB,EAChC,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,SAAS,IAAI,SAAS,CAAC,YAAY,EAClD,cAAc,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY,EACjD,cAAc,CAAC,cAAc,IAAI,SAAS,CAAC,YAAY,EACvD,cAAc,CAAC,MAAM,IAAI,SAAS,CAAC,YAAY,CAClD,CAAC;AACL,aAAA;YAED,IAAM,SAAS,GAAM,cAAc,CAAC,WAAW,GAAO,MAAA,GAAA,cAAc,CAAC,SAAS,GAAA,KAAA,GAAM,cAAc,CAAC,iBAAiB,2BAAsB,cAAc,CAAC,cAAc,GAAgB,eAAA,GAAA,cAAc,CAAC,QAAU,CAAC;AACjN,YAAA,MAAM,IAAI,WAAW,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC;AACnF,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACG,IAAA,eAAA,CAAA,SAAA,CAAA,yBAAyB,GAA/B,UACI,mBAAqD,EACrD,SAAoB,EACpB,YAAoB,EACpB,OAAwB,EACxB,eAA0C,EAC1C,iBAA0B,EAC1B,4BAAsC,EACtC,8BAAwC,EACxC,eAAwB,EAAA;;;;;;;AACxB,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,yBAAyB,EAAE,mBAAmB,CAAC,cAAc,CAAE,CAAA;wBAI7H,IAAI,mBAAmB,CAAC,QAAQ,EAAE;AAC9B,4BAAA,UAAU,GAAG,IAAI,SAAS,CAAC,mBAAmB,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;;4BAGnG,IAAI,eAAe,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;gCAChE,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,EAAE;AACnD,oCAAA,MAAM,eAAe,CAAC,wBAAwB,EAAE,CAAC;AACpD,iCAAA;AACJ,6BAAA;;4BAGD,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;AACpC,gCAAA,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC;gCAC7C,IAAI,CAAC,QAAQ,EAAE;AACX,oCAAA,MAAM,eAAe,CAAC,2BAA2B,EAAE,CAAC;AACvD,iCAAA;gCAED,SAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACnD,6BAAA;AACJ,yBAAA;;AAGD,wBAAA,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,WAAW,IAAI,SAAS,CAAC,YAAY,EAAE,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;wBAI9L,IAAI,CAAC,CAAC,eAAe,IAAI,CAAC,CAAC,eAAe,CAAC,KAAK,EAAE;AAC9C,4BAAA,eAAe,GAAG,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;AAC5F,yBAAA;;AAGD,wBAAA,mBAAmB,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC;AAEjF,wBAAA,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;;;;8BAG5I,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAA,EAAhD,OAAgD,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAChD,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;wBACtE,YAAY,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;wBACnE,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAA,CAAA;;AAA5D,wBAAA,EAAA,CAAA,IAAA,EAA4D,CAAC;;;AAEjE;;;;;AAKG;wBACH,IAAI,4BAA4B,IAAI,CAAC,8BAA8B,IAAI,WAAW,CAAC,OAAO,EAAE;AAClF,4BAAA,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;4BAC/C,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;4BAClD,IAAI,CAAC,OAAO,EAAE;AACV,gCAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qGAAqG,CAAC,CAAC;gCAC3H,OAAO,CAAA,CAAA,aAAA,eAAe,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC,CAAA;AACxK,6BAAA;AACJ,yBAAA;wBACD,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA,CAAA;;AAApD,wBAAA,EAAA,CAAA,IAAA,EAAoD,CAAC;;;8BAEjD,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,IAAI,YAAY,CAAA,EAAhE,OAAgE,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAChE,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;wBACrE,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA,CAAA;;AAA3D,wBAAA,EAAA,CAAA,IAAA,EAA2D,CAAC;;;4BAGpE,OAAO,CAAA,CAAA,aAAA,eAAe,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,eAAe,CAAC,CAAC,CAAA;;;;AAClL,KAAA,CAAA;AAED;;;;;AAKG;AACK,IAAA,eAAA,CAAA,SAAA,CAAA,mBAAmB,GAA3B,UAA4B,mBAAqD,EAAE,SAAoB,EAAE,YAAoB,EAAE,OAAwB,EAAE,UAAsB,EAAE,iBAA0B,EAAE,eAA0C,EAAA;AACnP,QAAA,IAAM,GAAG,GAAG,SAAS,CAAC,iBAAiB,EAAE,CAAC;AAC1C,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AAC1B,YAAA,MAAM,eAAe,CAAC,kCAAkC,EAAE,CAAC;AAC9D,SAAA;;AAGD,QAAA,IAAI,aAAwC,CAAC;AAC7C,QAAA,IAAI,aAAwC,CAAC;AAC7C,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE;AACpE,YAAA,aAAa,GAAG,aAAa,CAAC,mBAAmB,CAC7C,IAAI,CAAC,qBAAqB,EAC1B,GAAG,EACH,mBAAmB,CAAC,QAAQ,IAAI,SAAS,CAAC,YAAY,EACtD,IAAI,CAAC,QAAQ,EACb,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,YAAY,CAClD,CAAC;AAEF,YAAA,aAAa,GAAG,IAAI,CAAC,qBAAqB,CACtC,mBAAmB,EACnB,UAAU,EACV,SAAS,EACT,eAAe,CAClB,CAAC;AACL,SAAA;;QAGD,IAAI,iBAAiB,GAA6B,IAAI,CAAC;QACvD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE;;AAGxD,YAAA,IAAM,cAAc,GAAG,mBAAmB,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;AAEvI;;;AAGG;AACH,YAAA,IAAM,SAAS,GAAW,CAAC,OAAO,mBAAmB,CAAC,UAAU,KAAK,QAAQ,GAAG,QAAQ,CAAC,mBAAmB,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,mBAAmB,CAAC,UAAU,KAAK,CAAC,CAAC;AACpK,YAAA,IAAM,YAAY,GAAW,CAAC,OAAO,mBAAmB,CAAC,cAAc,KAAK,QAAQ,GAAG,QAAQ,CAAC,mBAAmB,CAAC,cAAc,EAAE,EAAE,CAAC,GAAG,mBAAmB,CAAC,cAAc,KAAK,CAAC,CAAC;AACnL,YAAA,IAAM,SAAS,GAAuB,CAAC,OAAO,mBAAmB,CAAC,UAAU,KAAK,QAAQ,GAAG,QAAQ,CAAC,mBAAmB,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,mBAAmB,CAAC,UAAU,KAAK,SAAS,CAAC;AACxL,YAAA,IAAM,sBAAsB,GAAG,YAAY,GAAG,SAAS,CAAC;AACxD,YAAA,IAAM,8BAA8B,GAAG,sBAAsB,GAAG,YAAY,CAAC;AAC7E,YAAA,IAAM,gBAAgB,GAAG,SAAS,IAAI,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,SAAS,GAAG,SAAS,CAAC;;AAG3F,YAAA,iBAAiB,GAAG,iBAAiB,CAAC,uBAAuB,CACzD,IAAI,CAAC,qBAAqB,EAC1B,GAAG,EACH,mBAAmB,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,EAC1D,IAAI,CAAC,QAAQ,EACb,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC,MAAM,EAC/E,cAAc,CAAC,WAAW,EAAE,EAC5B,sBAAsB,EACtB,8BAA8B,EAC9B,IAAI,CAAC,SAAS,EACd,gBAAgB,EAChB,mBAAmB,CAAC,UAAU,EAC9B,iBAAiB,EACjB,mBAAmB,CAAC,MAAM,EAC1B,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,mBAAmB,CAC9B,CAAC;AACL,SAAA;;QAGD,IAAI,kBAAkB,GAA8B,IAAI,CAAC;QACzD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAE;AACzD,YAAA,kBAAkB,GAAG,kBAAkB,CAAC,wBAAwB,CAC5D,IAAI,CAAC,qBAAqB,EAC1B,GAAG,EACH,mBAAmB,CAAC,aAAa,IAAI,SAAS,CAAC,YAAY,EAC3D,IAAI,CAAC,QAAQ,EACb,mBAAmB,CAAC,IAAI,EACxB,iBAAiB,CACpB,CAAC;AACL,SAAA;;QAGD,IAAI,iBAAiB,GAA6B,IAAI,CAAC;QACvD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;AAChD,YAAA,iBAAiB,GAAG,iBAAiB,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAC/G,SAAA;AAED,QAAA,OAAO,IAAI,WAAW,CAAC,aAAa,EAAE,aAAa,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;KAClH,CAAA;AAED;;;;;AAKG;IACK,eAAqB,CAAA,SAAA,CAAA,qBAAA,GAA7B,UAA8B,mBAAqD,EAAE,OAAkB,EAAE,SAAoB,EAAE,eAA0C,EAAA;AACrK,QAAA,IAAM,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;AAC9C,QAAA,IAAM,kBAAkB,GAAG,eAAe,GAAG,eAAe,CAAC,qBAAqB,GAAG,SAAS,CAAC,YAAY,CAAC;AAC5G,QAAA,IAAM,WAAW,GAAG,eAAe,GAAG,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;;AAG5F,QAAA,IAAI,aAAa,KAAK,aAAa,CAAC,IAAI,EAAE;AACtC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;AACrE,YAAA,OAAO,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;AAC9H,SAAA;;AAGD,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC,YAAY,KAAK,KAAK,EAAE;AAC1F,YAAA,MAAM,eAAe,CAAC,0BAA0B,EAAE,CAAC;AACtD,SAAA;AAED,QAAA,OAAO,mBAAmB,CAAC,WAAW;YAClC,aAAa,CAAC,aAAa,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,WAAW,CAAC;AAC7I,YAAA,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;KAC3H,CAAA;AAED;;;;;;;;;AASG;AACU,IAAA,eAAA,CAAA,4BAA4B,GAAzC,UACI,SAAkB,EAClB,SAAoB,EACpB,WAAwB,EACxB,cAAuB,EACvB,OAAwB,EACxB,UAAsB,EACtB,YAAiC,EACjC,mBAAsD,EACtD,SAAkB,EAAA;;;;;;;AAEd,wBAAA,WAAW,GAAW,SAAS,CAAC,YAAY,CAAC;wBAC7C,cAAc,GAAkB,EAAE,CAAC;wBACnC,SAAS,GAAgB,IAAI,CAAC;AAE9B,wBAAA,QAAQ,GAAW,SAAS,CAAC,YAAY,CAAC;6BAE1C,WAAW,CAAC,WAAW,EAAvB,OAAuB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;8BACnB,WAAW,CAAC,WAAW,CAAC,SAAS,KAAK,oBAAoB,CAAC,GAAG,CAAA,EAA9D,OAA8D,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACxD,wBAAA,iBAAiB,GAAsB,IAAI,iBAAiB,CAAC,SAAS,CAAC,CAAC;wBACxE,EAAoB,GAAA,WAAW,CAAC,WAAW,EAAzC,MAAM,GAAA,EAAA,CAAA,MAAA,EAAE,KAAK,GAAA,EAAA,CAAA,KAAA,CAA6B;wBAElD,IAAI,CAAC,KAAK,EAAE;AACR,4BAAA,MAAM,eAAe,CAAC,uBAAuB,EAAE,CAAC;AACnD,yBAAA;wBAEa,OAAM,CAAA,CAAA,YAAA,iBAAiB,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA,CAAA;;wBAA1E,WAAW,GAAG,SAA4D,CAAC;;;AAE3E,wBAAA,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC;;;AAEjD,wBAAA,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;AAC/E,wBAAA,SAAS,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;AACvE,wBAAA,YAAY,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,CAAC;;;wBAGtF,IAAI,WAAW,CAAC,WAAW,EAAE;AACzB,4BAAA,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,QAAQ,KAAK,aAAa,GAAG,aAAa,GAAG,SAAS,CAAC,YAAY,CAAC;AAC1G,yBAAA;wBACK,GAAG,GAAG,CAAA,UAAU,KAAV,IAAA,IAAA,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,MAAM,CAAC,GAAG,MAAI,UAAU,aAAV,UAAU,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAV,UAAU,CAAE,MAAM,CAAC,GAAG,CAAA,IAAI,SAAS,CAAC,YAAY,CAAC;AACjF,wBAAA,GAAG,GAAG,CAAA,UAAU,KAAA,IAAA,IAAV,UAAU,KAAV,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAU,CAAE,MAAM,CAAC,GAAG,KAAI,SAAS,CAAC,YAAY,CAAC;;AAG7D,wBAAA,IAAG,CAAA,mBAAmB,KAAnB,IAAA,IAAA,mBAAmB,uBAAnB,mBAAmB,CAAE,aAAa,KAAI,CAAC,CAAC,WAAW,CAAC,OAAO,EAAC;AAC3D,4BAAA,WAAW,CAAC,OAAO,CAAC,eAAe,GAAG,mBAAmB,KAAnB,IAAA,IAAA,mBAAmB,KAAnB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,mBAAmB,CAAE,aAAa,CAAC;AAC5E,yBAAA;wBAED,OAAO,CAAA,CAAA,aAAA;gCACH,SAAS,EAAE,SAAS,CAAC,kBAAkB;AACvC,gCAAA,QAAQ,EAAE,GAAG;AACb,gCAAA,QAAQ,EAAE,GAAG;AACb,gCAAA,MAAM,EAAE,cAAc;AACtB,gCAAA,OAAO,EAAE,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,IAAI;AAC1E,gCAAA,OAAO,EAAE,UAAU,GAAG,UAAU,CAAC,QAAQ,GAAG,SAAS,CAAC,YAAY;gCAClE,aAAa,EAAE,UAAU,GAAG,UAAU,CAAC,MAAM,GAAG,EAAE;AAClD,gCAAA,WAAW,EAAE,WAAW;AACxB,gCAAA,SAAS,EAAE,cAAc;AACzB,gCAAA,SAAS,EAAE,SAAS;gCACpB,aAAa,EAAE,OAAO,CAAC,aAAa;AACpC,gCAAA,SAAS,EAAE,SAAS,IAAI,SAAS,CAAC,YAAY;AAC9C,gCAAA,YAAY,EAAE,YAAY;AAC1B,gCAAA,QAAQ,EAAE,QAAQ;gCAClB,SAAS,EAAE,CAAA,CAAA,EAAA,GAAA,WAAW,CAAC,WAAW,0CAAE,SAAS,KAAI,SAAS,CAAC,YAAY;AACvE,gCAAA,KAAK,EAAE,YAAY,GAAG,YAAY,CAAC,gBAAgB,GAAG,SAAS,CAAC,YAAY;gCAC5E,kBAAkB,EAAE,CAAA,CAAA,EAAA,GAAA,WAAW,CAAC,OAAO,0CAAE,kBAAkB,KAAI,SAAS,CAAC,YAAY;gCACrF,WAAW,EAAE,CAAA,CAAA,EAAA,GAAA,WAAW,CAAC,OAAO,0CAAE,WAAW,KAAI,SAAS,CAAC,YAAY;AACvE,gCAAA,IAAI,EAAE,mBAAmB,KAAA,IAAA,IAAnB,mBAAmB,KAAnB,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,mBAAmB,CAAE,QAAQ;AACnC,gCAAA,gBAAgB,EAAE,KAAK;6BAC1B,CAAC,CAAA;;;;AACL,KAAA,CAAA;IACL,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA;;;;"}