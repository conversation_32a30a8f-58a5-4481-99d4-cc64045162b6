{"version": 3, "file": "StubPerformanceClient.js", "sources": ["../../../src/telemetry/performance/StubPerformanceClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { IPerformanceClient } from \"./IPerformanceClient\";\r\nimport { IPerformanceMeasurement } from \"./IPerformanceMeasurement\";\r\nimport { PerformanceClient } from \"./PerformanceClient\";\r\nimport { PerformanceEvents } from \"./PerformanceEvent\";\r\n\r\nexport class StubPerformanceMeasurement implements IPerformanceMeasurement {\r\n    /* eslint-disable-next-line @typescript-eslint/no-empty-function */\r\n    startMeasurement(): void { }\r\n    /* eslint-disable-next-line @typescript-eslint/no-empty-function */\r\n    endMeasurement(): void { }\r\n    flushMeasurement(): number | null {\r\n        return null;\r\n    }\r\n    \r\n}\r\n\r\nexport class StubPerformanceClient extends PerformanceClient implements IPerformanceClient {\r\n    generateId(): string {\r\n        return \"callback-id\";\r\n    }\r\n    \r\n    startPerformanceMeasuremeant(): IPerformanceMeasurement {\r\n        return new StubPerformanceMeasurement();\r\n    }\r\n\r\n    startPerformanceMeasurement(): IPerformanceMeasurement {\r\n        return new StubPerformanceMeasurement();\r\n    }\r\n\r\n    /* eslint-disable-next-line @typescript-eslint/no-unused-vars */\r\n    calculateQueuedTime(preQueueTime: number, currentTime: number): number {\r\n        return 0;\r\n    }\r\n\r\n    /* eslint-disable-next-line @typescript-eslint/no-unused-vars */\r\n    addQueueMeasurement(eventName: PerformanceEvents, correlationId: string, queueTime: number): void {\r\n        return;\r\n    }\r\n\r\n    /* eslint-disable-next-line @typescript-eslint/no-unused-vars */\r\n    setPreQueueTime(eventName: PerformanceEvents, correlationId?: string | undefined): void {\r\n        return;\r\n    }\r\n\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAOH,IAAA,0BAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,0BAAA,GAAA;KASC;;IAPG,0BAAgB,CAAA,SAAA,CAAA,gBAAA,GAAhB,eAA4B,CAAA;;IAE5B,0BAAc,CAAA,SAAA,CAAA,cAAA,GAAd,eAA0B,CAAA;AAC1B,IAAA,0BAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;AACI,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;IAEL,OAAC,0BAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,IAAA,qBAAA,kBAAA,UAAA,MAAA,EAAA;IAA2C,SAAiB,CAAA,qBAAA,EAAA,MAAA,CAAA,CAAA;AAA5D,IAAA,SAAA,qBAAA,GAAA;;KA4BC;AA3BG,IAAA,qBAAA,CAAA,SAAA,CAAA,UAAU,GAAV,YAAA;AACI,QAAA,OAAO,aAAa,CAAC;KACxB,CAAA;AAED,IAAA,qBAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5B,YAAA;QACI,OAAO,IAAI,0BAA0B,EAAE,CAAC;KAC3C,CAAA;AAED,IAAA,qBAAA,CAAA,SAAA,CAAA,2BAA2B,GAA3B,YAAA;QACI,OAAO,IAAI,0BAA0B,EAAE,CAAC;KAC3C,CAAA;;AAGD,IAAA,qBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAnB,UAAoB,YAAoB,EAAE,WAAmB,EAAA;AACzD,QAAA,OAAO,CAAC,CAAC;KACZ,CAAA;;AAGD,IAAA,qBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAnB,UAAoB,SAA4B,EAAE,aAAqB,EAAE,SAAiB,EAAA;QACtF,OAAO;KACV,CAAA;;AAGD,IAAA,qBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,SAA4B,EAAE,aAAkC,EAAA;QAC5E,OAAO;KACV,CAAA;IAEL,OAAC,qBAAA,CAAA;AAAD,CA5BA,CAA2C,iBAAiB,CA4B3D;;;;"}