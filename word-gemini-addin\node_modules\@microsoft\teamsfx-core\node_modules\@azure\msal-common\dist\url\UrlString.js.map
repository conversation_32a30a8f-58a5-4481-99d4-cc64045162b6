{"version": 3, "file": "UrlString.js", "sources": ["../../src/url/UrlString.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ServerAuthorizationCodeResponse } from \"../response/ServerAuthorizationCodeResponse\";\r\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { IUri } from \"./IUri\";\r\nimport { AADAuthorityConstants, Constants } from \"../utils/Constants\";\r\n\r\n/**\r\n * Url object class which can perform various transformations on url strings.\r\n */\r\nexport class UrlString {\r\n\r\n    // internal url string field\r\n    private _urlString: string;\r\n    public get urlString(): string {\r\n        return this._urlString;\r\n    }\r\n\r\n    constructor(url: string) {\r\n        this._urlString = url;\r\n        if (StringUtils.isEmpty(this._urlString)) {\r\n            // Throws error if url is empty\r\n            throw ClientConfigurationError.createUrlEmptyError();\r\n        }\r\n\r\n        if (StringUtils.isEmpty(this.getHash())) {\r\n            this._urlString = UrlString.canonicalizeUri(url);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Ensure urls are lower case and end with a / character.\r\n     * @param url\r\n     */\r\n    static canonicalizeUri(url: string): string {\r\n        if (url) {\r\n            let lowerCaseUrl = url.toLowerCase();\r\n\r\n            if (StringUtils.endsWith(lowerCaseUrl, \"?\")) {\r\n                lowerCaseUrl = lowerCaseUrl.slice(0, -1);\r\n            } else if (StringUtils.endsWith(lowerCaseUrl, \"?/\")) {\r\n                lowerCaseUrl = lowerCaseUrl.slice(0, -2);\r\n            }\r\n\r\n            if (!StringUtils.endsWith(lowerCaseUrl, \"/\")) {\r\n                lowerCaseUrl += \"/\";\r\n            }\r\n\r\n            return lowerCaseUrl;\r\n        }\r\n\r\n        return url;\r\n    }\r\n\r\n    /**\r\n     * Throws if urlString passed is not a valid authority URI string.\r\n     */\r\n    validateAsUri(): void {\r\n        // Attempts to parse url for uri components\r\n        let components;\r\n        try {\r\n            components = this.getUrlComponents();\r\n        } catch (e) {\r\n            throw ClientConfigurationError.createUrlParseError(e);\r\n        }\r\n\r\n        // Throw error if URI or path segments are not parseable.\r\n        if (!components.HostNameAndPort || !components.PathSegments) {\r\n            throw ClientConfigurationError.createUrlParseError(`Given url string: ${this.urlString}`);\r\n        }\r\n\r\n        // Throw error if uri is insecure.\r\n        if(!components.Protocol || components.Protocol.toLowerCase() !== \"https:\") {\r\n            throw ClientConfigurationError.createInsecureAuthorityUriError(this.urlString);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Given a url and a query string return the url with provided query string appended\r\n     * @param url\r\n     * @param queryString\r\n     */\r\n    static appendQueryString(url: string, queryString: string): string {\r\n        if (StringUtils.isEmpty(queryString)) {\r\n            return url;\r\n        }\r\n\r\n        return url.indexOf(\"?\") < 0 ? `${url}?${queryString}` : `${url}&${queryString}`;\r\n    }\r\n\r\n    /**\r\n     * Returns a url with the hash removed\r\n     * @param url\r\n     */\r\n    static removeHashFromUrl(url: string): string {\r\n        return UrlString.canonicalizeUri(url.split(\"#\")[0]);\r\n    }\r\n\r\n    /**\r\n     * Given a url like https://a:b/common/d?e=f#g, and a tenantId, returns https://a:b/tenantId/d\r\n     * @param href The url\r\n     * @param tenantId The tenant id to replace\r\n     */\r\n    replaceTenantPath(tenantId: string): UrlString {\r\n        const urlObject = this.getUrlComponents();\r\n        const pathArray = urlObject.PathSegments;\r\n        if (tenantId && (pathArray.length !== 0 && (pathArray[0] === AADAuthorityConstants.COMMON || pathArray[0] === AADAuthorityConstants.ORGANIZATIONS))) {\r\n            pathArray[0] = tenantId;\r\n        }\r\n        return UrlString.constructAuthorityUriFromObject(urlObject);\r\n    }\r\n\r\n    /**\r\n     * Returns the anchor part(#) of the URL\r\n     */\r\n    getHash(): string {\r\n        return UrlString.parseHash(this.urlString);\r\n    }\r\n\r\n    /**\r\n     * Parses out the components from a url string.\r\n     * @returns An object with the various components. Please cache this value insted of calling this multiple times on the same url.\r\n     */\r\n    getUrlComponents(): IUri {\r\n        // https://gist.github.com/curtisz/11139b2cfcaef4a261e0\r\n        const regEx = RegExp(\"^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\\\?([^#]*))?(#(.*))?\");\r\n\r\n        // If url string does not match regEx, we throw an error\r\n        const match = this.urlString.match(regEx);\r\n        if (!match) {\r\n            throw ClientConfigurationError.createUrlParseError(`Given url string: ${this.urlString}`);\r\n        }\r\n\r\n        // Url component object\r\n        const urlComponents = {\r\n            Protocol: match[1],\r\n            HostNameAndPort: match[4],\r\n            AbsolutePath: match[5],\r\n            QueryString: match[7]\r\n        } as IUri;\r\n\r\n        let pathSegments = urlComponents.AbsolutePath.split(\"/\");\r\n        pathSegments = pathSegments.filter((val) => val && val.length > 0); // remove empty elements\r\n        urlComponents.PathSegments = pathSegments;\r\n\r\n        if (!StringUtils.isEmpty(urlComponents.QueryString) && urlComponents.QueryString.endsWith(\"/\")) {\r\n            urlComponents.QueryString = urlComponents.QueryString.substring(0, urlComponents.QueryString.length-1);\r\n        }\r\n        return urlComponents;\r\n    }\r\n\r\n    static getDomainFromUrl(url: string): string {\r\n        const regEx = RegExp(\"^([^:/?#]+://)?([^/?#]*)\");\r\n\r\n        const match = url.match(regEx);\r\n\r\n        if (!match) {\r\n            throw ClientConfigurationError.createUrlParseError(`Given url string: ${url}`);\r\n        }\r\n\r\n        return match[2];\r\n    }\r\n\r\n    static getAbsoluteUrl(relativeUrl: string, baseUrl: string): string {\r\n        if (relativeUrl[0] === Constants.FORWARD_SLASH) {\r\n            const url = new UrlString(baseUrl);\r\n            const baseComponents = url.getUrlComponents();\r\n\r\n            return baseComponents.Protocol + \"//\" + baseComponents.HostNameAndPort + relativeUrl;\r\n        }\r\n\r\n        return relativeUrl;\r\n    }\r\n\r\n    /**\r\n     * Parses hash string from given string. Returns empty string if no hash symbol is found.\r\n     * @param hashString\r\n     */\r\n    static parseHash(hashString: string): string {\r\n        const hashIndex1 = hashString.indexOf(\"#\");\r\n        const hashIndex2 = hashString.indexOf(\"#/\");\r\n        if (hashIndex2 > -1) {\r\n            return hashString.substring(hashIndex2 + 2);\r\n        } else if (hashIndex1 > -1) {\r\n            return hashString.substring(hashIndex1 + 1);\r\n        }\r\n        return Constants.EMPTY_STRING;\r\n    }\r\n\r\n    /**\r\n     * Parses query string from given string. Returns empty string if no query symbol is found.\r\n     * @param queryString\r\n     */\r\n    static parseQueryString(queryString: string): string {\r\n        const queryIndex1 = queryString.indexOf(\"?\");\r\n        const queryIndex2 = queryString.indexOf(\"/?\");\r\n        if (queryIndex2 > -1) {\r\n            return queryString.substring(queryIndex2 + 2);\r\n        } else if (queryIndex1 > -1) {\r\n            return queryString.substring(queryIndex1 + 1);\r\n        }\r\n        return Constants.EMPTY_STRING;\r\n    }\r\n\r\n    static constructAuthorityUriFromObject(urlObject: IUri): UrlString {\r\n        return new UrlString(urlObject.Protocol + \"//\" + urlObject.HostNameAndPort + \"/\" + urlObject.PathSegments.join(\"/\"));\r\n    }\r\n\r\n    /**\r\n     * Returns URL hash as server auth code response object.\r\n     */\r\n    static getDeserializedHash(hash: string): ServerAuthorizationCodeResponse {\r\n        // Check if given hash is empty\r\n        if (StringUtils.isEmpty(hash)) {\r\n            return {};\r\n        }\r\n        // Strip the # symbol if present\r\n        const parsedHash = UrlString.parseHash(hash);\r\n        // If # symbol was not present, above will return empty string, so give original hash value\r\n        const deserializedHash: ServerAuthorizationCodeResponse = StringUtils.queryStringToObject<ServerAuthorizationCodeResponse>(StringUtils.isEmpty(parsedHash) ? hash : parsedHash);\r\n        // Check if deserialization didn't work\r\n        if (!deserializedHash) {\r\n            throw ClientAuthError.createHashNotDeserializedError(JSON.stringify(deserializedHash));\r\n        }\r\n        return deserializedHash;\r\n    }\r\n\r\n    /**\r\n     * Returns URL query string as server auth code response object.\r\n     */\r\n    static getDeserializedQueryString(query: string): ServerAuthorizationCodeResponse {\r\n        // Check if given query is empty\r\n        if (StringUtils.isEmpty(query)) {\r\n            return {};\r\n        }\r\n        // Strip the ? symbol if present\r\n        const parsedQueryString = UrlString.parseQueryString(query);\r\n        // If ? symbol was not present, above will return empty string, so give original query value\r\n        const deserializedQueryString: ServerAuthorizationCodeResponse = StringUtils.queryStringToObject<ServerAuthorizationCodeResponse>(StringUtils.isEmpty(parsedQueryString) ? query : parsedQueryString);\r\n        // Check if deserialization didn't work\r\n        if (!deserializedQueryString) {\r\n            throw ClientAuthError.createHashNotDeserializedError(JSON.stringify(deserializedQueryString));\r\n        }\r\n        return deserializedQueryString;\r\n    }\r\n\r\n    /**\r\n     * Check if the hash of the URL string contains known properties\r\n     */\r\n    static hashContainsKnownProperties(hash: string): boolean {\r\n        if (StringUtils.isEmpty(hash) || hash.indexOf(\"=\") < 0) {\r\n            // Hash doesn't contain key/value pairs\r\n            return false;\r\n        }\r\n\r\n        const parameters: ServerAuthorizationCodeResponse = UrlString.getDeserializedHash(hash);\r\n        return !!(\r\n            parameters.code ||\r\n            parameters.error_description ||\r\n            parameters.error ||\r\n            parameters.state\r\n        );\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAGG;AASH;;AAEG;AACH,IAAA,SAAA,kBAAA,YAAA;AAQI,IAAA,SAAA,SAAA,CAAY,GAAW,EAAA;AACnB,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QACtB,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;;AAEtC,YAAA,MAAM,wBAAwB,CAAC,mBAAmB,EAAE,CAAC;AACxD,SAAA;QAED,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE;YACrC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AACpD,SAAA;KACJ;AAdD,IAAA,MAAA,CAAA,cAAA,CAAW,SAAS,CAAA,SAAA,EAAA,WAAA,EAAA;AAApB,QAAA,GAAA,EAAA,YAAA;YACI,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;;;AAAA,KAAA,CAAA,CAAA;AAcD;;;AAGG;IACI,SAAe,CAAA,eAAA,GAAtB,UAAuB,GAAW,EAAA;AAC9B,QAAA,IAAI,GAAG,EAAE;AACL,YAAA,IAAI,YAAY,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;YAErC,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE;gBACzC,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5C,aAAA;iBAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE;gBACjD,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5C,aAAA;YAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE;gBAC1C,YAAY,IAAI,GAAG,CAAC;AACvB,aAAA;AAED,YAAA,OAAO,YAAY,CAAC;AACvB,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;KACd,CAAA;AAED;;AAEG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,aAAa,GAAb,YAAA;;AAEI,QAAA,IAAI,UAAU,CAAC;QACf,IAAI;AACA,YAAA,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxC,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,wBAAwB,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;AACzD,SAAA;;QAGD,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;YACzD,MAAM,wBAAwB,CAAC,mBAAmB,CAAC,uBAAqB,IAAI,CAAC,SAAW,CAAC,CAAC;AAC7F,SAAA;;AAGD,QAAA,IAAG,CAAC,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE;YACvE,MAAM,wBAAwB,CAAC,+BAA+B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAClF,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACI,IAAA,SAAA,CAAA,iBAAiB,GAAxB,UAAyB,GAAW,EAAE,WAAmB,EAAA;AACrD,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAClC,YAAA,OAAO,GAAG,CAAC;AACd,SAAA;QAED,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAM,GAAG,GAAA,GAAA,GAAI,WAAa,GAAM,GAAG,GAAA,GAAA,GAAI,WAAa,CAAC;KACnF,CAAA;AAED;;;AAGG;IACI,SAAiB,CAAA,iBAAA,GAAxB,UAAyB,GAAW,EAAA;AAChC,QAAA,OAAO,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvD,CAAA;AAED;;;;AAIG;IACH,SAAiB,CAAA,SAAA,CAAA,iBAAA,GAAjB,UAAkB,QAAgB,EAAA;AAC9B,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC1C,QAAA,IAAM,SAAS,GAAG,SAAS,CAAC,YAAY,CAAC;AACzC,QAAA,IAAI,QAAQ,KAAK,SAAS,CAAC,MAAM,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,qBAAqB,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,qBAAqB,CAAC,aAAa,CAAC,CAAC,EAAE;AACjJ,YAAA,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;AAC3B,SAAA;AACD,QAAA,OAAO,SAAS,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;KAC/D,CAAA;AAED;;AAEG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,OAAO,GAAP,YAAA;QACI,OAAO,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KAC9C,CAAA;AAED;;;AAGG;AACH,IAAA,SAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,YAAA;;AAEI,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,4DAA4D,CAAC,CAAC;;QAGnF,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,EAAE;YACR,MAAM,wBAAwB,CAAC,mBAAmB,CAAC,uBAAqB,IAAI,CAAC,SAAW,CAAC,CAAC;AAC7F,SAAA;;AAGD,QAAA,IAAM,aAAa,GAAG;AAClB,YAAA,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;AAClB,YAAA,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC;AACzB,YAAA,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;AACtB,YAAA,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;SAChB,CAAC;QAEV,IAAI,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzD,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,GAAA,CAAC,CAAC;AACnE,QAAA,aAAa,CAAC,YAAY,GAAG,YAAY,CAAC;AAE1C,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC5F,YAAA,aAAa,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,WAAW,CAAC,MAAM,GAAC,CAAC,CAAC,CAAC;AAC1G,SAAA;AACD,QAAA,OAAO,aAAa,CAAC;KACxB,CAAA;IAEM,SAAgB,CAAA,gBAAA,GAAvB,UAAwB,GAAW,EAAA;AAC/B,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,0BAA0B,CAAC,CAAC;QAEjD,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAE/B,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,MAAM,wBAAwB,CAAC,mBAAmB,CAAC,oBAAqB,GAAA,GAAK,CAAC,CAAC;AAClF,SAAA;AAED,QAAA,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;KACnB,CAAA;AAEM,IAAA,SAAA,CAAA,cAAc,GAArB,UAAsB,WAAmB,EAAE,OAAe,EAAA;QACtD,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,aAAa,EAAE;AAC5C,YAAA,IAAM,GAAG,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC;AACnC,YAAA,IAAM,cAAc,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAE9C,OAAO,cAAc,CAAC,QAAQ,GAAG,IAAI,GAAG,cAAc,CAAC,eAAe,GAAG,WAAW,CAAC;AACxF,SAAA;AAED,QAAA,OAAO,WAAW,CAAC;KACtB,CAAA;AAED;;;AAGG;IACI,SAAS,CAAA,SAAA,GAAhB,UAAiB,UAAkB,EAAA;QAC/B,IAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5C,QAAA,IAAI,UAAU,GAAG,CAAC,CAAC,EAAE;YACjB,OAAO,UAAU,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AAC/C,SAAA;AAAM,aAAA,IAAI,UAAU,GAAG,CAAC,CAAC,EAAE;YACxB,OAAO,UAAU,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AAC/C,SAAA;QACD,OAAO,SAAS,CAAC,YAAY,CAAC;KACjC,CAAA;AAED;;;AAGG;IACI,SAAgB,CAAA,gBAAA,GAAvB,UAAwB,WAAmB,EAAA;QACvC,IAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9C,QAAA,IAAI,WAAW,GAAG,CAAC,CAAC,EAAE;YAClB,OAAO,WAAW,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AACjD,SAAA;AAAM,aAAA,IAAI,WAAW,GAAG,CAAC,CAAC,EAAE;YACzB,OAAO,WAAW,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AACjD,SAAA;QACD,OAAO,SAAS,CAAC,YAAY,CAAC;KACjC,CAAA;IAEM,SAA+B,CAAA,+BAAA,GAAtC,UAAuC,SAAe,EAAA;QAClD,OAAO,IAAI,SAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,GAAG,SAAS,CAAC,eAAe,GAAG,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;KACxH,CAAA;AAED;;AAEG;IACI,SAAmB,CAAA,mBAAA,GAA1B,UAA2B,IAAY,EAAA;;AAEnC,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;;QAED,IAAM,UAAU,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;;QAE7C,IAAM,gBAAgB,GAAoC,WAAW,CAAC,mBAAmB,CAAkC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,UAAU,CAAC,CAAC;;QAEhL,IAAI,CAAC,gBAAgB,EAAE;YACnB,MAAM,eAAe,CAAC,8BAA8B,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC1F,SAAA;AACD,QAAA,OAAO,gBAAgB,CAAC;KAC3B,CAAA;AAED;;AAEG;IACI,SAA0B,CAAA,0BAAA,GAAjC,UAAkC,KAAa,EAAA;;AAE3C,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;;QAED,IAAM,iBAAiB,GAAG,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;;QAE5D,IAAM,uBAAuB,GAAoC,WAAW,CAAC,mBAAmB,CAAkC,WAAW,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG,KAAK,GAAG,iBAAiB,CAAC,CAAC;;QAEtM,IAAI,CAAC,uBAAuB,EAAE;YAC1B,MAAM,eAAe,CAAC,8BAA8B,CAAC,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC;AACjG,SAAA;AACD,QAAA,OAAO,uBAAuB,CAAC;KAClC,CAAA;AAED;;AAEG;IACI,SAA2B,CAAA,2BAAA,GAAlC,UAAmC,IAAY,EAAA;AAC3C,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;;AAEpD,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;QAED,IAAM,UAAU,GAAoC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;AACxF,QAAA,OAAO,CAAC,EACJ,UAAU,CAAC,IAAI;AACf,YAAA,UAAU,CAAC,iBAAiB;AAC5B,YAAA,UAAU,CAAC,KAAK;YAChB,UAAU,CAAC,KAAK,CACnB,CAAC;KACL,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;;;"}