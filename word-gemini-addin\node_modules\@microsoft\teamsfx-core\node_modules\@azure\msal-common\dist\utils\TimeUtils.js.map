{"version": 3, "file": "TimeUtils.js", "sources": ["../../src/utils/TimeUtils.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\n/**\r\n * Utility class which exposes functions for managing date and time operations.\r\n */\r\nexport class TimeUtils {\r\n\r\n    /**\r\n     * return the current time in Unix time (seconds).\r\n     */\r\n    static nowSeconds(): number {\r\n        // Date.getTime() returns in milliseconds.\r\n        return Math.round(new Date().getTime() / 1000.0);\r\n    }\r\n    \r\n    /**\r\n     * check if a token is expired based on given UTC time in seconds.\r\n     * @param expiresOn\r\n     */\r\n    static isTokenExpired(expiresOn: string, offset: number): boolean {\r\n        // check for access token expiry\r\n        const expirationSec = Number(expiresOn) || 0;\r\n        const offsetCurrentTimeSec = TimeUtils.nowSeconds() + offset;\r\n\r\n        // If current time + offset is greater than token expiration time, then token is expired.\r\n        return (offsetCurrentTimeSec > expirationSec);\r\n    }\r\n\r\n    /**\r\n     * If the current time is earlier than the time that a token was cached at, we must discard the token\r\n     * i.e. The system clock was turned back after acquiring the cached token\r\n     * @param cachedAt \r\n     * @param offset \r\n     */\r\n    static wasClockTurnedBack(cachedAt: string): boolean {\r\n        const cachedAtSec = Number(cachedAt);\r\n\r\n        return cachedAtSec > TimeUtils.nowSeconds();\r\n    }\r\n\r\n    /**\r\n     * Waits for t number of milliseconds\r\n     * @param t number\r\n     * @param value T\r\n     */\r\n    static delay<T>(t: number, value?: T): Promise<T | void> {\r\n        return new Promise((resolve) => setTimeout(() => resolve(value), t));\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAEH;;AAEG;AACH,IAAA,SAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,SAAA,GAAA;KA2CC;AAzCG;;AAEG;AACI,IAAA,SAAA,CAAA,UAAU,GAAjB,YAAA;;AAEI,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC;KACpD,CAAA;AAED;;;AAGG;AACI,IAAA,SAAA,CAAA,cAAc,GAArB,UAAsB,SAAiB,EAAE,MAAc,EAAA;;QAEnD,IAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAM,oBAAoB,GAAG,SAAS,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC;;AAG7D,QAAA,QAAQ,oBAAoB,GAAG,aAAa,EAAE;KACjD,CAAA;AAED;;;;;AAKG;IACI,SAAkB,CAAA,kBAAA,GAAzB,UAA0B,QAAgB,EAAA;AACtC,QAAA,IAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAErC,QAAA,OAAO,WAAW,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;KAC/C,CAAA;AAED;;;;AAIG;AACI,IAAA,SAAA,CAAA,KAAK,GAAZ,UAAgB,CAAS,EAAE,KAAS,EAAA;QAChC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAK,EAAA,OAAA,UAAU,CAAC,YAAM,EAAA,OAAA,OAAO,CAAC,KAAK,CAAC,CAAA,EAAA,EAAE,CAAC,CAAC,CAAA,EAAA,CAAC,CAAC;KACxE,CAAA;IACL,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;;;"}