{"version": 3, "file": "ClientAssertion.d.ts", "sourceRoot": "", "sources": ["../src/client/ClientAssertion.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAI1D;;;GAGG;AACH,qBAAa,eAAe;IAExB,OAAO,CAAC,GAAG,CAAS;IACpB,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,cAAc,CAAS;IAC/B,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,WAAW,CAAS;IAC5B,OAAO,CAAC,iBAAiB,CAAgB;IAEzC;;;OAGG;WACW,aAAa,CAAC,SAAS,EAAE,MAAM,GAAG,eAAe;IAM/D;;;;;OAKG;WACW,eAAe,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,MAAM,GAAG,eAAe;IAUlH;;;;;OAKG;IACI,MAAM,CAAC,cAAc,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,MAAM;IAsB1F;;OAEG;IACH,OAAO,CAAC,SAAS;IA+BjB;;OAEG;IACH,OAAO,CAAC,SAAS;IAIjB;;;OAGG;WACW,gBAAgB,CAAC,iBAAiB,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;CAmB3E"}