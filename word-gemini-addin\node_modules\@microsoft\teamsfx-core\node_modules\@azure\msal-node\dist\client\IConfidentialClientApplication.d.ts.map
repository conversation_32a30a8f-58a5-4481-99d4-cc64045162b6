{"version": 3, "file": "IConfidentialClientApplication.d.ts", "sourceRoot": "", "sources": ["../src/client/IConfidentialClientApplication.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AACrF,OAAO,EAAE,wBAAwB,EAAE,MAAM,qCAAqC,CAAC;AAC/E,OAAO,EAAE,uBAAuB,EAAE,MAAM,oCAAoC,CAAC;AAC7E,OAAO,EAAE,uBAAuB,EAAE,MAAM,oCAAoC,CAAC;AAC7E,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AACjE,OAAO,EAAE,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AACrE,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AACjE,OAAO,EAAE,uBAAuB,EAAE,MAAM,oCAAoC,CAAC;AAC7E,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAEjD;;;GAGG;AACH,MAAM,WAAW,8BAA8B;IAE3C,mDAAmD;IACnD,cAAc,CAAC,OAAO,EAAE,uBAAuB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAElE,+HAA+H;IAC/H,kBAAkB,CAAC,OAAO,EAAE,wBAAwB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAErF,8FAA8F;IAC9F,kBAAkB,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAErF,wFAAwF;IACxF,0BAA0B,CAAC,OAAO,EAAE,mBAAmB,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAE/F,mFAAmF;IACnF,8BAA8B,CAAC,OAAO,EAAE,uBAAuB,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAEvG,6DAA6D;IAC7D,sBAAsB,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAEzF,kHAAkH;IAClH,8BAA8B,CAAC,OAAO,EAAE,uBAAuB,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAEvG,+CAA+C;IAC/C,aAAa,IAAI,UAAU,CAAC;IAE5B,kCAAkC;IAClC,SAAS,IAAI,MAAM,CAAC;IAEpB,gGAAgG;IAChG,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEhC,sBAAsB;IACtB,UAAU,IAAI,IAAI,CAAC;IAEnB,0FAA0F;IAC1F,mBAAmB,CAAC,QAAQ,EAAE,iBAAiB,GAAG,IAAI,CAAA;CACzD"}