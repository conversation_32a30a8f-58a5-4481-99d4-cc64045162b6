{"version": 3, "file": "IPublicClientApplication.d.ts", "sourceRoot": "", "sources": ["../src/client/IPublicClientApplication.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,WAAW,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC/E,OAAO,EAAE,wBAAwB,EAAE,MAAM,qCAAqC,CAAC;AAC/E,OAAO,EAAE,uBAAuB,EAAE,MAAM,oCAAoC,CAAC;AAC7E,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AACjE,OAAO,EAAE,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AACrE,OAAO,EAAE,iBAAiB,EAAE,MAAM,8BAA8B,CAAC;AACjE,OAAO,EAAE,uBAAuB,EAAE,MAAM,oCAAoC,CAAC;AAC7E,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAC;AACnE,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D;;;GAGG;AACH,MAAM,WAAW,wBAAwB;IAErC,mDAAmD;IACnD,cAAc,CAAC,OAAO,EAAE,uBAAuB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAElE,8HAA8H;IAC9H,kBAAkB,CAAC,OAAO,EAAE,wBAAwB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAErF,qCAAqC;IACrC,uBAAuB,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAEpF,6FAA6F;IAC7F,kBAAkB,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAErF,wFAAwF;IACxF,0BAA0B,CAAC,OAAO,EAAE,mBAAmB,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAE/F,0EAA0E;IAC1E,wBAAwB,CAAC,OAAO,EAAE,iBAAiB,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAE3F,kHAAkH;IAClH,8BAA8B,CAAC,OAAO,EAAE,uBAAuB,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAEvG,+CAA+C;IAC/C,aAAa,IAAI,UAAU,CAAC;IAE5B,kCAAkC;IAClC,SAAS,IAAI,MAAM,CAAC;IAEpB,gGAAgG;IAChG,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEhC,sBAAsB;IACtB,UAAU,IAAI,IAAI,CAAC;IAEnB,+BAA+B;IAC/B,cAAc,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IAEzC,gEAAgE;IAChE,OAAO,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CACnD"}