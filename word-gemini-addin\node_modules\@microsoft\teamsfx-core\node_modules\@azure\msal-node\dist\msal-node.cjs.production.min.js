"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e.default:e}Object.defineProperty(exports,"__esModule",{value:!0});var t,r,n,i=require("@azure/msal-common"),o=require("http"),s=e(o),a=e(require("https")),c=require("uuid"),l=e(require("crypto")),h=require("jsonwebtoken");!function(e){e.GET="get",e.POST="post"}(t||(t={})),function(e){e[e.SUCCESS_RANGE_START=200]="SUCCESS_RANGE_START",e[e.SUCCESS_RANGE_END=299]="SUCCESS_RANGE_END",e[e.REDIRECT=302]="REDIRECT",e[e.CLIENT_ERROR_RANGE_START=400]="CLIENT_ERROR_RANGE_START",e[e.CLIENT_ERROR_RANGE_END=499]="CLIENT_ERROR_RANGE_END",e[e.SERVER_ERROR_RANGE_START=500]="SERVER_ERROR_RANGE_START",e[e.SERVER_ERROR_RANGE_END=599]="SERVER_ERROR_RANGE_END"}(r||(r={})),function(e){e[e.SUCCESS_RANGE_START=200]="SUCCESS_RANGE_START",e[e.SUCCESS_RANGE_END=299]="SUCCESS_RANGE_END",e[e.SERVER_ERROR=500]="SERVER_ERROR"}(n||(n={}));const u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";var d;!function(e){e[e.acquireTokenSilent=62]="acquireTokenSilent",e[e.acquireTokenByUsernamePassword=371]="acquireTokenByUsernamePassword",e[e.acquireTokenByDeviceCode=671]="acquireTokenByDeviceCode",e[e.acquireTokenByClientCredential=771]="acquireTokenByClientCredential",e[e.acquireTokenByCode=871]="acquireTokenByCode",e[e.acquireTokenByRefreshToken=872]="acquireTokenByRefreshToken"}(d||(d={}));class g{static getNetworkResponse(e,t,r){return{headers:e,body:t,status:r}}static urlToHttpOptions(e){const t={protocol:e.protocol,hostname:e.hostname&&e.hostname.startsWith("[")?e.hostname.slice(1,-1):e.hostname,hash:e.hash,search:e.search,pathname:e.pathname,path:`${e.pathname||""}${e.search||""}`,href:e.href};return""!==e.port&&(t.port=Number(e.port)),(e.username||e.password)&&(t.auth=`${decodeURIComponent(e.username)}:${decodeURIComponent(e.password)}`),t}}class p{constructor(e,t){this.proxyUrl=e||"",this.customAgentOptions=t||{}}async sendGetRequestAsync(e,r){return this.proxyUrl?y(e,this.proxyUrl,t.GET,r,this.customAgentOptions):m(e,t.GET,r,this.customAgentOptions)}async sendPostRequestAsync(e,r,n){return this.proxyUrl?y(e,this.proxyUrl,t.POST,r,this.customAgentOptions,n):m(e,t.POST,r,this.customAgentOptions,n)}}const y=(e,i,o,a,c,l)=>{const h=new URL(e),u=new URL(i),d={host:u.hostname,port:u.port,method:"CONNECT",path:h.hostname,headers:(null==a?void 0:a.headers)||{}};l&&(d.timeout=l),c&&Object.keys(c).length&&(d.agent=new s.Agent(c));let p="";if(o===t.POST){const e=(null==a?void 0:a.body)||"";p=`Content-Type: application/x-www-form-urlencoded\r\nContent-Length: ${e.length}\r\n\r\n`+e}const y=`${o.toUpperCase()} ${h.href} HTTP/1.1\r\nHost: ${h.host}\r\nConnection: close\r\n`+p+"\r\n";return new Promise((e,t)=>{const i=s.request(d);d.timeout&&i.on("timeout",()=>{i.destroy(),t(new Error("Request time out"))}),i.end(),i.on("connect",(o,s)=>{const a=(null==o?void 0:o.statusCode)||n.SERVER_ERROR;(a<n.SUCCESS_RANGE_START||a>n.SUCCESS_RANGE_END)&&(i.destroy(),s.destroy(),t(new Error(`Error connecting to proxy. Http status code: ${o.statusCode}. Http status message: ${(null==o?void 0:o.statusMessage)||"Unknown"}`))),d.timeout&&(s.setTimeout(d.timeout),s.on("timeout",()=>{i.destroy(),s.destroy(),t(new Error("Request time out"))})),s.write(y);const c=[];s.on("data",e=>{c.push(e)}),s.on("end",()=>{const t=Buffer.concat([...c]).toString().split("\r\n"),n=parseInt(t[0].split(" ")[1]),o=t[0].split(" ").slice(2).join(" "),s=t[t.length-1],a=t.slice(1,t.length-2),l=new Map;a.forEach(e=>{const t=e.split(new RegExp(/:\s(.*)/s)),r=t[0];let n=t[1];try{const e=JSON.parse(n);e&&"object"==typeof e&&(n=e)}catch(e){}l.set(r,n)});const h=Object.fromEntries(l),u=g.getNetworkResponse(h,C(n,o,h,s),n);(n<r.SUCCESS_RANGE_START||n>r.SUCCESS_RANGE_END)&&"authorization_pending"!==u.body.error&&i.destroy(),e(u)}),s.on("error",e=>{i.destroy(),s.destroy(),t(new Error(e.toString()))})}),i.on("error",e=>{i.destroy(),t(new Error(e.toString()))})})},m=(e,n,i,o,s)=>{const c=n===t.POST,l=(null==i?void 0:i.body)||"",h=new URL(e),u={method:n,headers:(null==i?void 0:i.headers)||{},...g.urlToHttpOptions(h)};return s&&(u.timeout=s),o&&Object.keys(o).length&&(u.agent=new a.Agent(o)),c&&(u.headers={...u.headers,"Content-Length":l.length}),new Promise((e,t)=>{const n=a.request(u);s&&n.on("timeout",()=>{n.destroy(),t(new Error("Request time out"))}),c&&n.write(l),n.end(),n.on("response",t=>{const i=t.headers,o=t.statusCode,s=t.statusMessage,a=[];t.on("data",e=>{a.push(e)}),t.on("end",()=>{const t=Buffer.concat([...a]).toString(),c=g.getNetworkResponse(i,C(o,s,i,t),o);(o<r.SUCCESS_RANGE_START||o>r.SUCCESS_RANGE_END)&&"authorization_pending"!==c.body.error&&n.destroy(),e(c)})}),n.on("error",e=>{n.destroy(),t(new Error(e.toString()))})})},C=(e,t,n,i)=>{let o;try{o=JSON.parse(i)}catch(i){let s,a;e>=r.CLIENT_ERROR_RANGE_START&&e<=r.CLIENT_ERROR_RANGE_END?(s="client_error",a="A client"):e>=r.SERVER_ERROR_RANGE_START&&e<=r.SERVER_ERROR_RANGE_END?(s="server_error",a="A server"):(s="unknown_error",a="An unknown"),o={error:s,error_description:`${a} error occured.\nHttp status code: ${e}\nHttp status message: ${t||"Unknown"}\nHeaders: ${JSON.stringify(n)}`}}return o},f={clientId:i.Constants.EMPTY_STRING,authority:i.Constants.DEFAULT_AUTHORITY,clientSecret:i.Constants.EMPTY_STRING,clientAssertion:i.Constants.EMPTY_STRING,clientCertificate:{thumbprint:i.Constants.EMPTY_STRING,privateKey:i.Constants.EMPTY_STRING,x5c:i.Constants.EMPTY_STRING},knownAuthorities:[],cloudDiscoveryMetadata:i.Constants.EMPTY_STRING,authorityMetadata:i.Constants.EMPTY_STRING,clientCapabilities:[],protocolMode:i.ProtocolMode.AAD,azureCloudOptions:{azureCloudInstance:i.AzureCloudInstance.None,tenant:i.Constants.EMPTY_STRING},skipAuthorityMetadataCache:!1},T={claimsBasedCachingEnabled:!0},A={loggerCallback:()=>{},piiLoggingEnabled:!1,logLevel:i.LogLevel.Info},E={loggerOptions:A,networkClient:new p,proxyUrl:i.Constants.EMPTY_STRING,customAgentOptions:{}},k={application:{appName:i.Constants.EMPTY_STRING,appVersion:i.Constants.EMPTY_STRING}};function R({auth:e,broker:t,cache:r,system:n,telemetry:i}){const o={...E,networkClient:new p(null==n?void 0:n.proxyUrl,null==n?void 0:n.customAgentOptions),loggerOptions:(null==n?void 0:n.loggerOptions)||A};return{auth:{...f,...e},broker:{...t},cache:{...T,...r},system:{...o,...n},telemetry:{...k,...i}}}class I{generateGuid(){return c.v4()}isGuid(e){return/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e)}}class v{static base64Encode(e,t){return Buffer.from(e,t).toString("base64")}static base64EncodeUrl(e,t){return v.base64Encode(e,t).replace(/=/g,i.Constants.EMPTY_STRING).replace(/\+/g,"-").replace(/\//g,"_")}static base64Decode(e){return Buffer.from(e,"base64").toString("utf8")}static base64DecodeUrl(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");for(;t.length%4;)t+="=";return v.base64Decode(t)}}class S{sha256(e){return l.createHash("sha256").update(e).digest()}}class w{constructor(){this.hashUtils=new S}async generatePkceCodes(){const e=this.generateCodeVerifier();return{verifier:e,challenge:this.generateCodeChallengeFromVerifier(e)}}generateCodeVerifier(){const e=[],t=256-256%u.length;for(;e.length<=32;){const r=l.randomBytes(1)[0];r>=t||e.push(u[r%u.length])}const r=e.join(i.Constants.EMPTY_STRING);return v.base64EncodeUrl(r)}generateCodeChallengeFromVerifier(e){return v.base64EncodeUrl(this.hashUtils.sha256(e).toString("base64"),"base64")}}class _{constructor(){this.pkceGenerator=new w,this.guidGenerator=new I,this.hashUtils=new S}createNewGuid(){return this.guidGenerator.generateGuid()}base64Encode(e){return v.base64Encode(e)}base64Decode(e){return v.base64Decode(e)}generatePkceCodes(){return this.pkceGenerator.generatePkceCodes()}getPublicKeyThumbprint(){throw new Error("Method not implemented.")}removeTokenBindingKey(){throw new Error("Method not implemented.")}clearKeystore(){throw new Error("Method not implemented.")}signJwt(){throw new Error("Method not implemented.")}async hashString(e){return v.base64EncodeUrl(this.hashUtils.sha256(e).toString("base64"),"base64")}}class b{static deserializeJSONBlob(e){return i.StringUtils.isEmpty(e)?{}:JSON.parse(e)}static deserializeAccounts(e){const t={};return e&&Object.keys(e).map((function(r){const n=e[r],o={homeAccountId:n.home_account_id,environment:n.environment,realm:n.realm,localAccountId:n.local_account_id,username:n.username,authorityType:n.authority_type,name:n.name,clientInfo:n.client_info,lastModificationTime:n.last_modification_time,lastModificationApp:n.last_modification_app},s=new i.AccountEntity;i.CacheManager.toObject(s,o),t[r]=s})),t}static deserializeIdTokens(e){const t={};return e&&Object.keys(e).map((function(r){const n=e[r],o={homeAccountId:n.home_account_id,environment:n.environment,credentialType:n.credential_type,clientId:n.client_id,secret:n.secret,realm:n.realm},s=new i.IdTokenEntity;i.CacheManager.toObject(s,o),t[r]=s})),t}static deserializeAccessTokens(e){const t={};return e&&Object.keys(e).map((function(r){const n=e[r],o={homeAccountId:n.home_account_id,environment:n.environment,credentialType:n.credential_type,clientId:n.client_id,secret:n.secret,realm:n.realm,target:n.target,cachedAt:n.cached_at,expiresOn:n.expires_on,extendedExpiresOn:n.extended_expires_on,refreshOn:n.refresh_on,keyId:n.key_id,tokenType:n.token_type,requestedClaims:n.requestedClaims,requestedClaimsHash:n.requestedClaimsHash,userAssertionHash:n.userAssertionHash},s=new i.AccessTokenEntity;i.CacheManager.toObject(s,o),t[r]=s})),t}static deserializeRefreshTokens(e){const t={};return e&&Object.keys(e).map((function(r){const n=e[r],o={homeAccountId:n.home_account_id,environment:n.environment,credentialType:n.credential_type,clientId:n.client_id,secret:n.secret,familyId:n.family_id,target:n.target,realm:n.realm},s=new i.RefreshTokenEntity;i.CacheManager.toObject(s,o),t[r]=s})),t}static deserializeAppMetadata(e){const t={};return e&&Object.keys(e).map((function(r){const n=e[r],o={clientId:n.client_id,environment:n.environment,familyId:n.family_id},s=new i.AppMetadataEntity;i.CacheManager.toObject(s,o),t[r]=s})),t}static deserializeAllCache(e){return{accounts:e.Account?this.deserializeAccounts(e.Account):{},idTokens:e.IdToken?this.deserializeIdTokens(e.IdToken):{},accessTokens:e.AccessToken?this.deserializeAccessTokens(e.AccessToken):{},refreshTokens:e.RefreshToken?this.deserializeRefreshTokens(e.RefreshToken):{},appMetadata:e.AppMetadata?this.deserializeAppMetadata(e.AppMetadata):{}}}}class O{static serializeJSONBlob(e){return JSON.stringify(e)}static serializeAccounts(e){const t={};return Object.keys(e).map((function(r){const n=e[r];t[r]={home_account_id:n.homeAccountId,environment:n.environment,realm:n.realm,local_account_id:n.localAccountId,username:n.username,authority_type:n.authorityType,name:n.name,client_info:n.clientInfo,last_modification_time:n.lastModificationTime,last_modification_app:n.lastModificationApp}})),t}static serializeIdTokens(e){const t={};return Object.keys(e).map((function(r){const n=e[r];t[r]={home_account_id:n.homeAccountId,environment:n.environment,credential_type:n.credentialType,client_id:n.clientId,secret:n.secret,realm:n.realm}})),t}static serializeAccessTokens(e){const t={};return Object.keys(e).map((function(r){const n=e[r];t[r]={home_account_id:n.homeAccountId,environment:n.environment,credential_type:n.credentialType,client_id:n.clientId,secret:n.secret,realm:n.realm,target:n.target,cached_at:n.cachedAt,expires_on:n.expiresOn,extended_expires_on:n.extendedExpiresOn,refresh_on:n.refreshOn,key_id:n.keyId,token_type:n.tokenType,requestedClaims:n.requestedClaims,requestedClaimsHash:n.requestedClaimsHash,userAssertionHash:n.userAssertionHash}})),t}static serializeRefreshTokens(e){const t={};return Object.keys(e).map((function(r){const n=e[r];t[r]={home_account_id:n.homeAccountId,environment:n.environment,credential_type:n.credentialType,client_id:n.clientId,secret:n.secret,family_id:n.familyId,target:n.target,realm:n.realm}})),t}static serializeAppMetadata(e){const t={};return Object.keys(e).map((function(r){const n=e[r];t[r]={client_id:n.clientId,environment:n.environment,family_id:n.familyId}})),t}static serializeAllCache(e){return{Account:this.serializeAccounts(e.accounts),IdToken:this.serializeIdTokens(e.idTokens),AccessToken:this.serializeAccessTokens(e.accessTokens),RefreshToken:this.serializeRefreshTokens(e.refreshTokens),AppMetadata:this.serializeAppMetadata(e.appMetadata)}}}class M extends i.CacheManager{constructor(e,t,r){super(t,r,e),this.cache={},this.changeEmitters=[],this.logger=e}registerChangeEmitter(e){this.changeEmitters.push(e)}emitChange(){this.changeEmitters.forEach(e=>e.call(null))}cacheToInMemoryCache(e){const t={accounts:{},idTokens:{},accessTokens:{},refreshTokens:{},appMetadata:{}};for(const r in e)if(e[r]instanceof i.AccountEntity)t.accounts[r]=e[r];else if(e[r]instanceof i.IdTokenEntity)t.idTokens[r]=e[r];else if(e[r]instanceof i.AccessTokenEntity)t.accessTokens[r]=e[r];else if(e[r]instanceof i.RefreshTokenEntity)t.refreshTokens[r]=e[r];else{if(!(e[r]instanceof i.AppMetadataEntity))continue;t.appMetadata[r]=e[r]}return t}inMemoryCacheToCache(e){let t=this.getCache();return t={...t,...e.accounts,...e.idTokens,...e.accessTokens,...e.refreshTokens,...e.appMetadata},t}getInMemoryCache(){return this.logger.trace("Getting in-memory cache"),this.cacheToInMemoryCache(this.getCache())}setInMemoryCache(e){this.logger.trace("Setting in-memory cache");const t=this.inMemoryCacheToCache(e);this.setCache(t),this.emitChange()}getCache(){return this.logger.trace("Getting cache key-value store"),this.cache}setCache(e){this.logger.trace("Setting cache key value store"),this.cache=e,this.emitChange()}getItem(e){return this.logger.tracePii("Item key: "+e),this.getCache()[e]}setItem(e,t){this.logger.tracePii("Item key: "+e);const r=this.getCache();r[e]=t,this.setCache(r)}getAccountKeys(){const e=this.getInMemoryCache();return Object.keys(e.accounts)}getTokenKeys(){const e=this.getInMemoryCache();return{idToken:Object.keys(e.idTokens),accessToken:Object.keys(e.accessTokens),refreshToken:Object.keys(e.refreshTokens)}}getAccount(e){const t=this.getItem(e);return i.AccountEntity.isAccountEntity(t)?t:null}setAccount(e){const t=e.generateAccountKey();this.setItem(t,e)}getIdTokenCredential(e){const t=this.getItem(e);return i.IdTokenEntity.isIdTokenEntity(t)?t:null}setIdTokenCredential(e){const t=e.generateCredentialKey();this.setItem(t,e)}getAccessTokenCredential(e){const t=this.getItem(e);return i.AccessTokenEntity.isAccessTokenEntity(t)?t:null}setAccessTokenCredential(e){const t=e.generateCredentialKey();this.setItem(t,e)}getRefreshTokenCredential(e){const t=this.getItem(e);return i.RefreshTokenEntity.isRefreshTokenEntity(t)?t:null}setRefreshTokenCredential(e){const t=e.generateCredentialKey();this.setItem(t,e)}getAppMetadata(e){const t=this.getItem(e);return i.AppMetadataEntity.isAppMetadataEntity(e,t)?t:null}setAppMetadata(e){const t=e.generateAppMetadataKey();this.setItem(t,e)}getServerTelemetry(e){const t=this.getItem(e);return t&&i.ServerTelemetryEntity.isServerTelemetryEntity(e,t)?t:null}setServerTelemetry(e,t){this.setItem(e,t)}getAuthorityMetadata(e){const t=this.getItem(e);return t&&i.AuthorityMetadataEntity.isAuthorityMetadataEntity(e,t)?t:null}getAuthorityMetadataKeys(){return this.getKeys().filter(e=>this.isAuthorityMetadata(e))}setAuthorityMetadata(e,t){this.setItem(e,t)}getThrottlingCache(e){const t=this.getItem(e);return t&&i.ThrottlingEntity.isThrottlingEntity(e,t)?t:null}setThrottlingCache(e,t){this.setItem(e,t)}removeItem(e){this.logger.tracePii("Item key: "+e);let t=!1;const r=this.getCache();return r[e]&&(delete r[e],t=!0),t&&(this.setCache(r),this.emitChange()),t}containsKey(e){return this.getKeys().includes(e)}getKeys(){this.logger.trace("Retrieving all cache keys");const e=this.getCache();return[...Object.keys(e)]}async clear(){this.logger.trace("Clearing cache entries created by MSAL"),this.getKeys().forEach(e=>{this.removeItem(e)}),this.emitChange()}static generateInMemoryCache(e){return b.deserializeAllCache(b.deserializeJSONBlob(e))}static generateJsonCache(e){return O.serializeAllCache(e)}updateCredentialCacheKey(e,t){const r=t.generateCredentialKey();if(e!==r){const n=this.getItem(e);if(n)return this.removeItem(e),this.setItem(r,n),this.logger.verbose(`Updated an outdated ${t.credentialType} cache key`),r;this.logger.error(`Attempted to update an outdated ${t.credentialType} cache key but no item matching the outdated key was found in storage`)}return e}}const P={},N={},q={},U={},x={};class z{constructor(e,t,r){this.cacheHasChanged=!1,this.storage=e,this.storage.registerChangeEmitter(this.handleChangeEvent.bind(this)),r&&(this.persistence=r),this.logger=t}hasChanged(){return this.cacheHasChanged}serialize(){this.logger.trace("Serializing in-memory cache");let e=O.serializeAllCache(this.storage.getInMemoryCache());return i.StringUtils.isEmpty(this.cacheSnapshot)?this.logger.trace("No cache snapshot to merge"):(this.logger.trace("Reading cache snapshot from disk"),e=this.mergeState(JSON.parse(this.cacheSnapshot),e)),this.cacheHasChanged=!1,JSON.stringify(e)}deserialize(e){if(this.logger.trace("Deserializing JSON to in-memory cache"),this.cacheSnapshot=e,i.StringUtils.isEmpty(this.cacheSnapshot))this.logger.trace("No cache snapshot to deserialize");else{this.logger.trace("Reading cache snapshot from disk");const e=b.deserializeAllCache(this.overlayDefaults(JSON.parse(this.cacheSnapshot)));this.storage.setInMemoryCache(e)}}getKVStore(){return this.storage.getCache()}async getAllAccounts(){let e;this.logger.trace("getAllAccounts called");try{return this.persistence&&(e=new i.TokenCacheContext(this,!1),await this.persistence.beforeCacheAccess(e)),this.storage.getAllAccounts()}finally{this.persistence&&e&&await this.persistence.afterCacheAccess(e)}}async getAccountByHomeId(e){const t=await this.getAllAccounts();return!i.StringUtils.isEmpty(e)&&t&&t.length&&t.filter(t=>t.homeAccountId===e)[0]||null}async getAccountByLocalId(e){const t=await this.getAllAccounts();return!i.StringUtils.isEmpty(e)&&t&&t.length&&t.filter(t=>t.localAccountId===e)[0]||null}async removeAccount(e){let t;this.logger.trace("removeAccount called");try{this.persistence&&(t=new i.TokenCacheContext(this,!0),await this.persistence.beforeCacheAccess(t)),await this.storage.removeAccount(i.AccountEntity.generateAccountCacheKey(e))}finally{this.persistence&&t&&await this.persistence.afterCacheAccess(t)}}handleChangeEvent(){this.cacheHasChanged=!0}mergeState(e,t){this.logger.trace("Merging in-memory cache with cache snapshot");const r=this.mergeRemovals(e,t);return this.mergeUpdates(r,t)}mergeUpdates(e,t){return Object.keys(t).forEach(r=>{const n=t[r];if(e.hasOwnProperty(r)){const t=null!==n,i="object"==typeof n,o=!Array.isArray(n),s=null!=e[r];t&&i&&o&&s?this.mergeUpdates(e[r],n):e[r]=n}else null!==n&&(e[r]=n)}),e}mergeRemovals(e,t){this.logger.trace("Remove updated entries in cache");const r=e.Account?this.mergeRemovalsDict(e.Account,t.Account):e.Account,n=e.AccessToken?this.mergeRemovalsDict(e.AccessToken,t.AccessToken):e.AccessToken,i=e.RefreshToken?this.mergeRemovalsDict(e.RefreshToken,t.RefreshToken):e.RefreshToken,o=e.IdToken?this.mergeRemovalsDict(e.IdToken,t.IdToken):e.IdToken,s=e.AppMetadata?this.mergeRemovalsDict(e.AppMetadata,t.AppMetadata):e.AppMetadata;return{...e,Account:r,AccessToken:n,RefreshToken:i,IdToken:o,AppMetadata:s}}mergeRemovalsDict(e,t){const r={...e};return Object.keys(e).forEach(e=>{t&&t.hasOwnProperty(e)||delete r[e]}),r}overlayDefaults(e){return this.logger.trace("Overlaying input cache with the default cache"),{Account:{...P,...e.Account},IdToken:{...N,...e.IdToken},AccessToken:{...q,...e.AccessToken},RefreshToken:{...U,...e.RefreshToken},AppMetadata:{...x,...e.AppMetadata}}}}class B extends i.AuthError{constructor(e,t){super(e,t),this.name="NodeAuthError"}static createInvalidLoopbackAddressTypeError(){return new B("invalid_loopback_server_address_type","Loopback server address is not type string. This is unexpected.")}static createUnableToLoadRedirectUrlError(){return new B("unable_to_load_redirectUrl","Loopback server callback was invoked without a url. This is unexpected.")}static createNoAuthCodeInResponseError(){return new B("no_auth_code_in_response","No auth code found in the server response. Please check your network trace to determine what happened.")}static createNoLoopbackServerExistsError(){return new B("no_loopback_server_exists","No loopback server exists yet.")}static createLoopbackServerAlreadyExistsError(){return new B("loopback_server_already_exists","Loopback server already exists. Cannot create another.")}static createLoopbackServerTimeoutError(){return new B("loopback_server_timeout","Timed out waiting for auth code listener to be registered.")}static createStateNotFoundError(){return new B("state_not_found","State not found. Please verify that the request originated from msal.")}}class G{constructor(e){this.config=R(e),this.cryptoProvider=new _,this.logger=new i.Logger(this.config.system.loggerOptions,"@azure/msal-node","1.18.4"),this.storage=new M(this.logger,this.config.auth.clientId,this.cryptoProvider),this.tokenCache=new z(this.storage,this.logger,this.config.cache.cachePlugin)}async getAuthCodeUrl(e){this.logger.info("getAuthCodeUrl called",e.correlationId);const t={...e,...await this.initializeBaseRequest(e),responseMode:e.responseMode||i.ResponseMode.QUERY,authenticationScheme:i.AuthenticationScheme.BEARER},r=await this.buildOauthClientConfiguration(t.authority,t.correlationId,void 0,void 0,e.azureCloudOptions),n=new i.AuthorizationCodeClient(r);return this.logger.verbose("Auth code client created",t.correlationId),n.getAuthCodeUrl(t)}async acquireTokenByCode(e,t){this.logger.info("acquireTokenByCode called"),e.state&&t&&(this.logger.info("acquireTokenByCode - validating state"),this.validateState(e.state,t.state||""),t={...t,state:""});const r={...e,...await this.initializeBaseRequest(e),authenticationScheme:i.AuthenticationScheme.BEARER},n=this.initializeServerTelemetryManager(d.acquireTokenByCode,r.correlationId);try{const o=await this.buildOauthClientConfiguration(r.authority,r.correlationId,n,void 0,e.azureCloudOptions),s=new i.AuthorizationCodeClient(o);return this.logger.verbose("Auth code client created",r.correlationId),s.acquireToken(r,t)}catch(e){throw e instanceof i.AuthError&&e.setCorrelationId(r.correlationId),n.cacheFailedRequest(e),e}}async acquireTokenByRefreshToken(e){this.logger.info("acquireTokenByRefreshToken called",e.correlationId);const t={...e,...await this.initializeBaseRequest(e),authenticationScheme:i.AuthenticationScheme.BEARER},r=this.initializeServerTelemetryManager(d.acquireTokenByRefreshToken,t.correlationId);try{const n=await this.buildOauthClientConfiguration(t.authority,t.correlationId,r,void 0,e.azureCloudOptions),o=new i.RefreshTokenClient(n);return this.logger.verbose("Refresh token client created",t.correlationId),o.acquireToken(t)}catch(e){throw e instanceof i.AuthError&&e.setCorrelationId(t.correlationId),r.cacheFailedRequest(e),e}}async acquireTokenSilent(e){const t={...e,...await this.initializeBaseRequest(e),forceRefresh:e.forceRefresh||!1},r=this.initializeServerTelemetryManager(d.acquireTokenSilent,t.correlationId,t.forceRefresh);try{const n=await this.buildOauthClientConfiguration(t.authority,t.correlationId,r,void 0,e.azureCloudOptions),o=new i.SilentFlowClient(n);return this.logger.verbose("Silent flow client created",t.correlationId),o.acquireToken(t)}catch(e){throw e instanceof i.AuthError&&e.setCorrelationId(t.correlationId),r.cacheFailedRequest(e),e}}async acquireTokenByUsernamePassword(e){this.logger.info("acquireTokenByUsernamePassword called",e.correlationId);const t={...e,...await this.initializeBaseRequest(e)},r=this.initializeServerTelemetryManager(d.acquireTokenByUsernamePassword,t.correlationId);try{const n=await this.buildOauthClientConfiguration(t.authority,t.correlationId,r,void 0,e.azureCloudOptions),o=new i.UsernamePasswordClient(n);return this.logger.verbose("Username password client created",t.correlationId),o.acquireToken(t)}catch(e){throw e instanceof i.AuthError&&e.setCorrelationId(t.correlationId),r.cacheFailedRequest(e),e}}getTokenCache(){return this.logger.info("getTokenCache called"),this.tokenCache}validateState(e,t){if(!e)throw B.createStateNotFoundError();if(e!==t)throw i.ClientAuthError.createStateMismatchError()}getLogger(){return this.logger}setLogger(e){this.logger=e}async buildOauthClientConfiguration(e,t,r,n,o){this.logger.verbose("buildOauthClientConfiguration called",t);const s=o||this.config.auth.azureCloudOptions;this.logger.verbose("building oauth client configuration with the authority: "+e,t);const a=await this.createAuthority(e,n,t,s);return null==r||r.updateRegionDiscoveryMetadata(a.regionDiscoveryMetadata),{authOptions:{clientId:this.config.auth.clientId,authority:a,clientCapabilities:this.config.auth.clientCapabilities},loggerOptions:{logLevel:this.config.system.loggerOptions.logLevel,loggerCallback:this.config.system.loggerOptions.loggerCallback,piiLoggingEnabled:this.config.system.loggerOptions.piiLoggingEnabled,correlationId:t},cacheOptions:{claimsBasedCachingEnabled:this.config.cache.claimsBasedCachingEnabled},cryptoInterface:this.cryptoProvider,networkInterface:this.config.system.networkClient,storageInterface:this.storage,serverTelemetryManager:r,clientCredentials:{clientSecret:this.clientSecret,clientAssertion:this.clientAssertion?this.getClientAssertion(a):void 0},libraryInfo:{sku:"msal.js.node",version:"1.18.4",cpu:process.arch||i.Constants.EMPTY_STRING,os:process.platform||i.Constants.EMPTY_STRING},telemetry:this.config.telemetry,persistencePlugin:this.config.cache.cachePlugin,serializableCache:this.tokenCache}}getClientAssertion(e){return{assertion:this.clientAssertion.getJwt(this.cryptoProvider,this.config.auth.clientId,e.tokenEndpoint),assertionType:"urn:ietf:params:oauth:client-assertion-type:jwt-bearer"}}async initializeBaseRequest(e){return this.logger.verbose("initializeRequestScopes called",e.correlationId),e.authenticationScheme&&e.authenticationScheme===i.AuthenticationScheme.POP&&this.logger.verbose("Authentication Scheme 'pop' is not supported yet, setting Authentication Scheme to 'Bearer' for request",e.correlationId),e.authenticationScheme=i.AuthenticationScheme.BEARER,this.config.cache.claimsBasedCachingEnabled&&e.claims&&!i.StringUtils.isEmptyObj(e.claims)&&(e.requestedClaimsHash=await this.cryptoProvider.hashString(e.claims)),{...e,scopes:[...e&&e.scopes||[],...i.OIDC_DEFAULT_SCOPES],correlationId:e&&e.correlationId||this.cryptoProvider.createNewGuid(),authority:e.authority||this.config.auth.authority}}initializeServerTelemetryManager(e,t,r){return new i.ServerTelemetryManager({clientId:this.config.auth.clientId,correlationId:t,apiId:e,forceRefresh:r||!1},this.storage)}async createAuthority(e,t,r,n){this.logger.verbose("createAuthority called",r);const o=i.Authority.generateAuthority(e,n),s={protocolMode:this.config.auth.protocolMode,knownAuthorities:this.config.auth.knownAuthorities,cloudDiscoveryMetadata:this.config.auth.cloudDiscoveryMetadata,authorityMetadata:this.config.auth.authorityMetadata,azureRegionConfiguration:t,skipAuthorityMetadataCache:this.config.auth.skipAuthorityMetadataCache};return await i.AuthorityFactory.createDiscoveredInstance(o,this.config.system.networkClient,this.storage,s,this.logger)}clearCache(){this.storage.clear()}}class j{async listenForAuthCode(e,t){if(this.server)throw B.createLoopbackServerAlreadyExistsError();const n=new Promise((n,s)=>{this.server=o.createServer(async(o,a)=>{const c=o.url;if(!c)return a.end(t||"Error occurred loading redirectUrl"),void s(B.createUnableToLoadRedirectUrlError());if(c===i.Constants.FORWARD_SLASH)return void a.end(e||"Auth code was successfully acquired. You can close this window now.");const l=i.UrlString.getDeserializedQueryString(c);if(l.code){const e=await this.getRedirectUri();a.writeHead(r.REDIRECT,{location:e}),a.end()}n(l)}),this.server.listen(0)});return await new Promise(e=>{let t=0;const r=setInterval(()=>{if(50<t)throw B.createLoopbackServerTimeoutError();this.server.listening&&(clearInterval(r),e()),t++},100)}),n}getRedirectUri(){if(!this.server)throw B.createNoLoopbackServerExistsError();const e=this.server.address();if(!e||"string"==typeof e||!e.port)throw this.closeServer(),B.createInvalidLoopbackAddressTypeError();return"http://localhost:"+(e&&e.port)}closeServer(){this.server&&this.server.close()}}class D{static fromAssertion(e){const t=new D;return t.jwt=e,t}static fromCertificate(e,t,r){const n=new D;return n.privateKey=t,n.thumbprint=e,r&&(n.publicCertificate=this.parseCertificate(r)),n}getJwt(e,t,r){if(this.privateKey&&this.thumbprint)return this.jwt&&!this.isExpired()&&t===this.issuer&&r===this.jwtAudience?this.jwt:this.createJwt(e,t,r);if(this.jwt)return this.jwt;throw i.ClientAuthError.createInvalidAssertionError()}createJwt(e,t,r){this.issuer=t,this.jwtAudience=r;const n=i.TimeUtils.nowSeconds();this.expirationTime=n+600;const o={alg:"RS256",x5t:v.base64EncodeUrl(this.thumbprint,"hex")};this.publicCertificate&&Object.assign(o,{x5c:this.publicCertificate});const s={aud:this.jwtAudience,exp:this.expirationTime,iss:this.issuer,sub:this.issuer,nbf:n,jti:e.createNewGuid()};return this.jwt=h.sign(s,this.privateKey,{header:o}),this.jwt}isExpired(){return this.expirationTime<i.TimeUtils.nowSeconds()}static parseCertificate(e){const t=/-----BEGIN CERTIFICATE-----\r*\n(.+?)\r*\n-----END CERTIFICATE-----/gs,r=[];let n;for(;null!==(n=t.exec(e));)r.push(n[1].replace(/\r*\n/g,i.Constants.EMPTY_STRING));return r}}Object.defineProperty(exports,"AuthError",{enumerable:!0,get:function(){return i.AuthError}}),Object.defineProperty(exports,"AuthErrorMessage",{enumerable:!0,get:function(){return i.AuthErrorMessage}}),Object.defineProperty(exports,"AzureCloudInstance",{enumerable:!0,get:function(){return i.AzureCloudInstance}}),Object.defineProperty(exports,"ClientAuthError",{enumerable:!0,get:function(){return i.ClientAuthError}}),Object.defineProperty(exports,"ClientAuthErrorMessage",{enumerable:!0,get:function(){return i.ClientAuthErrorMessage}}),Object.defineProperty(exports,"ClientConfigurationError",{enumerable:!0,get:function(){return i.ClientConfigurationError}}),Object.defineProperty(exports,"ClientConfigurationErrorMessage",{enumerable:!0,get:function(){return i.ClientConfigurationErrorMessage}}),Object.defineProperty(exports,"InteractionRequiredAuthError",{enumerable:!0,get:function(){return i.InteractionRequiredAuthError}}),Object.defineProperty(exports,"InteractionRequiredAuthErrorMessage",{enumerable:!0,get:function(){return i.InteractionRequiredAuthErrorMessage}}),Object.defineProperty(exports,"LogLevel",{enumerable:!0,get:function(){return i.LogLevel}}),Object.defineProperty(exports,"Logger",{enumerable:!0,get:function(){return i.Logger}}),Object.defineProperty(exports,"PromptValue",{enumerable:!0,get:function(){return i.PromptValue}}),Object.defineProperty(exports,"ProtocolMode",{enumerable:!0,get:function(){return i.ProtocolMode}}),Object.defineProperty(exports,"ResponseMode",{enumerable:!0,get:function(){return i.ResponseMode}}),Object.defineProperty(exports,"ServerError",{enumerable:!0,get:function(){return i.ServerError}}),Object.defineProperty(exports,"TokenCacheContext",{enumerable:!0,get:function(){return i.TokenCacheContext}}),exports.ClientApplication=G,exports.ClientAssertion=D,exports.ConfidentialClientApplication=class extends G{constructor(e){super(e),this.setClientCredential(this.config),this.appTokenProvider=void 0}SetAppTokenProvider(e){this.appTokenProvider=e}async acquireTokenByClientCredential(e){let t;this.logger.info("acquireTokenByClientCredential called",e.correlationId),e.clientAssertion&&(t={assertion:e.clientAssertion,assertionType:"urn:ietf:params:oauth:client-assertion-type:jwt-bearer"});const r=await this.initializeBaseRequest(e),n={...r,scopes:r.scopes.filter(e=>!i.OIDC_DEFAULT_SCOPES.includes(e))},o={...e,...n,clientAssertion:t},s={azureRegion:o.azureRegion,environmentRegion:process.env.REGION_NAME},a=this.initializeServerTelemetryManager(d.acquireTokenByClientCredential,o.correlationId,o.skipCache);try{const t=await this.buildOauthClientConfiguration(o.authority,o.correlationId,a,s,e.azureCloudOptions),r=new i.ClientCredentialClient(t,this.appTokenProvider);return this.logger.verbose("Client credential client created",o.correlationId),r.acquireToken(o)}catch(e){throw e instanceof i.AuthError&&e.setCorrelationId(o.correlationId),a.cacheFailedRequest(e),e}}async acquireTokenOnBehalfOf(e){this.logger.info("acquireTokenOnBehalfOf called",e.correlationId);const t={...e,...await this.initializeBaseRequest(e)};try{const r=await this.buildOauthClientConfiguration(t.authority,t.correlationId,void 0,void 0,e.azureCloudOptions),n=new i.OnBehalfOfClient(r);return this.logger.verbose("On behalf of client created",t.correlationId),n.acquireToken(t)}catch(e){throw e instanceof i.AuthError&&e.setCorrelationId(t.correlationId),e}}setClientCredential(e){const t=!i.StringUtils.isEmpty(e.auth.clientSecret),r=!i.StringUtils.isEmpty(e.auth.clientAssertion),n=e.auth.clientCertificate||{thumbprint:i.Constants.EMPTY_STRING,privateKey:i.Constants.EMPTY_STRING},o=!i.StringUtils.isEmpty(n.thumbprint)||!i.StringUtils.isEmpty(n.privateKey);if(!this.appTokenProvider){if(t&&r||r&&o||t&&o)throw i.ClientAuthError.createInvalidCredentialError();if(e.auth.clientSecret)this.clientSecret=e.auth.clientSecret;else if(e.auth.clientAssertion)this.clientAssertion=D.fromAssertion(e.auth.clientAssertion);else{if(!o)throw i.ClientAuthError.createInvalidCredentialError();var s;this.clientAssertion=D.fromCertificate(n.thumbprint,n.privateKey,null==(s=e.auth.clientCertificate)?void 0:s.x5c)}}}},exports.CryptoProvider=_,exports.DistributedCachePlugin=class{constructor(e,t){this.client=e,this.partitionManager=t}async beforeCacheAccess(e){const t=await this.partitionManager.getKey(),r=await this.client.get(t);e.tokenCache.deserialize(r)}async afterCacheAccess(e){if(e.cacheHasChanged){const t=e.tokenCache.getKVStore(),r=Object.values(t).filter(e=>i.AccountEntity.isAccountEntity(e));if(r.length>0){const t=r[0],n=await this.partitionManager.extractKey(t);await this.client.set(n,e.tokenCache.serialize())}}}},exports.NodeStorage=M,exports.PublicClientApplication=class extends G{constructor(e){super(e),this.config.broker.nativeBrokerPlugin&&(this.config.broker.nativeBrokerPlugin.isBrokerAvailable?(this.nativeBrokerPlugin=this.config.broker.nativeBrokerPlugin,this.nativeBrokerPlugin.setLogger(this.config.system.loggerOptions)):this.logger.warning("NativeBroker implementation was provided but the broker is unavailable."))}async acquireTokenByDeviceCode(e){this.logger.info("acquireTokenByDeviceCode called",e.correlationId);const t=Object.assign(e,await this.initializeBaseRequest(e)),r=this.initializeServerTelemetryManager(d.acquireTokenByDeviceCode,t.correlationId);try{const n=await this.buildOauthClientConfiguration(t.authority,t.correlationId,r,void 0,e.azureCloudOptions),o=new i.DeviceCodeClient(n);return this.logger.verbose("Device code client created",t.correlationId),o.acquireToken(t)}catch(e){throw e instanceof i.AuthError&&e.setCorrelationId(t.correlationId),r.cacheFailedRequest(e),e}}async acquireTokenInteractive(e){const t=e.correlationId||this.cryptoProvider.createNewGuid();this.logger.trace("acquireTokenInteractive called",t);const{openBrowser:r,successTemplate:n,errorTemplate:o,windowHandle:s,loopbackClient:a,...c}=e;if(this.nativeBrokerPlugin){var l;const r={...c,clientId:this.config.auth.clientId,scopes:e.scopes||i.OIDC_DEFAULT_SCOPES,redirectUri:"http://localhost",authority:e.authority||this.config.auth.authority,correlationId:t,extraParameters:{...c.extraQueryParameters,...c.tokenQueryParameters},accountId:null==(l=c.account)?void 0:l.nativeAccountId};return this.nativeBrokerPlugin.acquireTokenInteractive(r,s)}const{verifier:h,challenge:u}=await this.cryptoProvider.generatePkceCodes(),d=a||new j;try{const s=d.listenForAuthCode(n,o),a=d.getRedirectUri(),l={...c,correlationId:t,scopes:e.scopes||i.OIDC_DEFAULT_SCOPES,redirectUri:a,responseMode:i.ResponseMode.QUERY,codeChallenge:u,codeChallengeMethod:i.CodeChallengeMethodValues.S256},g=await this.getAuthCodeUrl(l);await r(g);const p=await s.finally(()=>{d.closeServer()});if(p.error)throw new i.ServerError(p.error,p.error_description,p.suberror);if(!p.code)throw B.createNoAuthCodeInResponseError();const y={code:p.code,codeVerifier:h,clientInfo:p.client_info||i.Constants.EMPTY_STRING,...l};return this.acquireTokenByCode(y)}catch(e){throw d.closeServer(),e}}async acquireTokenSilent(e){const t=e.correlationId||this.cryptoProvider.createNewGuid();if(this.logger.trace("acquireTokenSilent called",t),this.nativeBrokerPlugin){const r={...e,clientId:this.config.auth.clientId,scopes:e.scopes||i.OIDC_DEFAULT_SCOPES,redirectUri:"http://localhost",authority:e.authority||this.config.auth.authority,correlationId:t,extraParameters:e.tokenQueryParameters,accountId:e.account.nativeAccountId,forceRefresh:e.forceRefresh||!1};return this.nativeBrokerPlugin.acquireTokenSilent(r)}return super.acquireTokenSilent(e)}async signOut(e){if(this.nativeBrokerPlugin&&e.account.nativeAccountId){const t={clientId:this.config.auth.clientId,accountId:e.account.nativeAccountId,correlationId:e.correlationId||this.cryptoProvider.createNewGuid()};await this.nativeBrokerPlugin.signOut(t)}await this.getTokenCache().removeAccount(e.account)}async getAllAccounts(){if(this.nativeBrokerPlugin){const e=this.cryptoProvider.createNewGuid();return this.nativeBrokerPlugin.getAllAccounts(this.config.auth.clientId,e)}return this.getTokenCache().getAllAccounts()}},exports.TokenCache=z,exports.buildAppConfiguration=R,exports.version="1.18.4";
//# sourceMappingURL=msal-node.cjs.production.min.js.map
