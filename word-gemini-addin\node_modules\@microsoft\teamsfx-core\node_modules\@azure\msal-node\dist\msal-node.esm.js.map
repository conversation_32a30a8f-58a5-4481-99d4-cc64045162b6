{"version": 3, "file": "msal-node.esm.js", "sources": ["../src/utils/Constants.ts", "../src/utils/NetworkUtils.ts", "../src/network/HttpClient.ts", "../src/config/Configuration.ts", "../src/crypto/GuidGenerator.ts", "../src/utils/EncodingUtils.ts", "../src/crypto/HashUtils.ts", "../src/crypto/PkceGenerator.ts", "../src/crypto/CryptoProvider.ts", "../src/cache/serializer/Deserializer.ts", "../src/cache/serializer/Serializer.ts", "../src/cache/NodeStorage.ts", "../src/cache/TokenCache.ts", "../src/packageMetadata.ts", "../src/error/NodeAuthError.ts", "../src/client/ClientApplication.ts", "../src/network/LoopbackClient.ts", "../src/client/PublicClientApplication.ts", "../src/client/ClientAssertion.ts", "../src/client/ConfidentialClientApplication.ts", "../src/cache/distributed/DistributedCachePlugin.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\n/**\r\n * http methods\r\n */\r\nexport enum HttpMethod {\r\n    GET = \"get\",\r\n    POST = \"post\",\r\n}\r\n\r\nexport enum HttpStatus {\r\n    SUCCESS_RANGE_START = 200,\r\n    SUCCESS_RANGE_END = 299,\r\n    REDIRECT = 302,\r\n    CLIENT_ERROR_RANGE_START = 400,\r\n    CLIENT_ERROR_RANGE_END = 499,\r\n    SERVER_ERROR_RANGE_START = 500,\r\n    SERVER_ERROR_RANGE_END = 599\r\n}\r\n\r\nexport enum ProxyStatus {\r\n    SUCCESS_RANGE_START = 200,\r\n    SUCCESS_RANGE_END = 299,\r\n    SERVER_ERROR = 500\r\n}\r\n\r\n/**\r\n * Constants used for region discovery\r\n */\r\nexport const REGION_ENVIRONMENT_VARIABLE = \"REGION_NAME\";\r\n\r\n/**\r\n * Constant used for PKCE\r\n */\r\nexport const RANDOM_OCTET_SIZE = 32;\r\n\r\n/**\r\n * Constants used in PKCE\r\n */\r\nexport const Hash = {\r\n    SHA256: \"sha256\",\r\n};\r\n\r\n/**\r\n * Constants for encoding schemes\r\n */\r\nexport const CharSet = {\r\n    CV_CHARSET:\r\n        \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~\",\r\n};\r\n\r\n/**\r\n * Cache Constants\r\n */\r\nexport const CACHE = {\r\n    FILE_CACHE: \"fileCache\",\r\n    EXTENSION_LIB: \"extenstion_library\",\r\n};\r\n\r\n/**\r\n * Constants\r\n */\r\nexport const Constants = {\r\n    MSAL_SKU: \"msal.js.node\",\r\n    JWT_BEARER_ASSERTION_TYPE: \"urn:ietf:params:oauth:client-assertion-type:jwt-bearer\",\r\n    AUTHORIZATION_PENDING: \"authorization_pending\",\r\n    HTTP_PROTOCOL: \"http://\",\r\n    LOCALHOST: \"localhost\"\r\n};\r\n\r\n/**\r\n * API Codes for Telemetry purposes.\r\n * Before adding a new code you must claim it in the MSAL Telemetry tracker as these number spaces are shared across all MSALs\r\n * 0-99 Silent Flow\r\n * 600-699 Device Code Flow\r\n * 800-899 Auth Code Flow\r\n */\r\nexport enum ApiId {\r\n    acquireTokenSilent = 62,\r\n    acquireTokenByUsernamePassword = 371,\r\n    acquireTokenByDeviceCode = 671,\r\n    acquireTokenByClientCredential = 771,\r\n    acquireTokenByCode = 871,\r\n    acquireTokenByRefreshToken = 872\r\n}\r\n\r\n/**\r\n * JWT  constants\r\n */\r\nexport const JwtConstants = {\r\n    ALGORITHM: \"alg\",\r\n    RSA_256: \"RS256\",\r\n    X5T: \"x5t\", \r\n    X5C: \"x5c\",\r\n    AUDIENCE: \"aud\",\r\n    EXPIRATION_TIME: \"exp\",\r\n    ISSUER: \"iss\",\r\n    SUBJECT: \"sub\",\r\n    NOT_BEFORE: \"nbf\",\r\n    JWT_ID: \"jti\",\r\n};\r\n\r\nexport const LOOPBACK_SERVER_CONSTANTS = {\r\n    INTERVAL_MS: 100,\r\n    TIMEOUT_MS: 5000\r\n};\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { NetworkResponse, UrlToHttpRequestOptions } from \"@azure/msal-common\";\r\n\r\nexport class NetworkUtils {\r\n    static getNetworkResponse<T>(headers: Record<string, string>, body: T, statusCode: number): NetworkResponse<T> {\r\n        return {\r\n            headers: headers,\r\n            body: body,\r\n            status: statusCode,\r\n        };\r\n    }\r\n\r\n    /*\r\n     * Utility function that converts a URL object into an ordinary options object as expected by the\r\n     * http.request and https.request APIs.\r\n     * https://github.com/nodejs/node/blob/main/lib/internal/url.js#L1090\r\n     */\r\n    static urlToHttpOptions(url: URL): UrlToHttpRequestOptions {\r\n        const options: UrlToHttpRequestOptions = {\r\n            protocol: url.protocol,\r\n            hostname: url.hostname && url.hostname.startsWith(\"[\") ?\r\n                url.hostname.slice(1, -1) :\r\n                url.hostname,\r\n            hash: url.hash,\r\n            search: url.search,\r\n            pathname: url.pathname,\r\n            path: `${url.pathname || \"\"}${url.search || \"\"}`,\r\n            href: url.href,\r\n        };\r\n        if (url.port !== \"\") {\r\n            options.port = Number(url.port);\r\n        }\r\n        if (url.username || url.password) {\r\n            options.auth = `${decodeURIComponent(url.username)}:${decodeURIComponent(url.password)}`;\r\n        }\r\n        return options;\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { INetworkModule, NetworkRequestOptions, NetworkResponse } from \"@azure/msal-common\";\r\nimport { HttpMethod, Constants, HttpStatus, ProxyStatus } from \"../utils/Constants\";\r\nimport { NetworkUtils } from \"../utils/NetworkUtils\";\r\nimport http from \"http\";\r\nimport https from \"https\";\r\n\r\n/**\r\n * This class implements the API for network requests.\r\n */\r\nexport class HttpClient implements INetworkModule {\r\n    private proxyUrl: string;\r\n    private customAgentOptions: http.AgentOptions | https.AgentOptions;\r\n\r\n    constructor(\r\n        proxyUrl?: string,\r\n        customAgentOptions?: http.AgentOptions | https.AgentOptions,\r\n    ) {\r\n        this.proxyUrl = proxyUrl || \"\";\r\n        this.customAgentOptions = customAgentOptions || {};\r\n    }\r\n\r\n    /**\r\n     * Http Get request\r\n     * @param url\r\n     * @param options\r\n     */\r\n    async sendGetRequestAsync<T>(\r\n        url: string,\r\n        options?: NetworkRequestOptions,\r\n    ): Promise<NetworkResponse<T>> {\r\n        if (this.proxyUrl) {\r\n            return networkRequestViaProxy(url, this.proxyUrl, HttpMethod.GET, options, this.customAgentOptions as http.AgentOptions);\r\n        } else {\r\n            return networkRequestViaHttps(url, HttpMethod.GET, options, this.customAgentOptions as https.AgentOptions);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Http Post request\r\n     * @param url\r\n     * @param options\r\n     */\r\n    async sendPostRequestAsync<T>(\r\n        url: string,\r\n        options?: NetworkRequestOptions,\r\n        cancellationToken?: number,\r\n    ): Promise<NetworkResponse<T>> {\r\n        if (this.proxyUrl) {\r\n            return networkRequestViaProxy(url, this.proxyUrl, HttpMethod.POST, options, this.customAgentOptions as http.AgentOptions, cancellationToken);\r\n        } else {\r\n            return networkRequestViaHttps(url, HttpMethod.POST, options, this.customAgentOptions as https.AgentOptions, cancellationToken);\r\n        }\r\n    }\r\n}\r\n\r\nconst networkRequestViaProxy = <T>(\r\n    destinationUrlString: string,\r\n    proxyUrlString: string,\r\n    httpMethod: string,\r\n    options?: NetworkRequestOptions,\r\n    agentOptions?: http.AgentOptions,\r\n    timeout?: number,\r\n): Promise<NetworkResponse<T>> => {\r\n    const destinationUrl = new URL(destinationUrlString);\r\n    const proxyUrl = new URL(proxyUrlString);\r\n\r\n    // \"method: connect\" must be used to establish a connection to the proxy\r\n    const headers = options?.headers || {} as Record<string, string>;\r\n    const tunnelRequestOptions: https.RequestOptions = {\r\n        host: proxyUrl.hostname,\r\n        port: proxyUrl.port,\r\n        method: \"CONNECT\",\r\n        path: destinationUrl.hostname,\r\n        headers: headers,\r\n    };\r\n\r\n    if (timeout) {\r\n        tunnelRequestOptions.timeout = timeout;\r\n    }\r\n\r\n    if (agentOptions && Object.keys(agentOptions).length) {\r\n        tunnelRequestOptions.agent = new http.Agent(agentOptions);\r\n    }\r\n\r\n    // compose a request string for the socket\r\n    let postRequestStringContent: string = \"\";\r\n    if (httpMethod === HttpMethod.POST) {\r\n        const body = options?.body || \"\";\r\n        postRequestStringContent =\r\n            \"Content-Type: application/x-www-form-urlencoded\\r\\n\" +\r\n            `Content-Length: ${body.length}\\r\\n` +\r\n            `\\r\\n${body}`;\r\n    }\r\n    const outgoingRequestString = `${httpMethod.toUpperCase()} ${destinationUrl.href} HTTP/1.1\\r\\n` +\r\n        `Host: ${destinationUrl.host}\\r\\n` +\r\n        \"Connection: close\\r\\n\" +\r\n        postRequestStringContent +\r\n        \"\\r\\n\";\r\n\r\n    return new Promise<NetworkResponse<T>>(((resolve, reject) => {\r\n        const request = http.request(tunnelRequestOptions);\r\n\r\n        if (tunnelRequestOptions.timeout) {\r\n            request.on(\"timeout\", () => {\r\n                request.destroy();\r\n                reject(new Error(\"Request time out\"));\r\n            });\r\n        }\r\n\r\n        request.end();\r\n\r\n        // establish connection to the proxy\r\n        request.on(\"connect\", (response, socket) => {\r\n            const proxyStatusCode = response?.statusCode || ProxyStatus.SERVER_ERROR;\r\n            if ((proxyStatusCode < ProxyStatus.SUCCESS_RANGE_START) || (proxyStatusCode > ProxyStatus.SUCCESS_RANGE_END)) {\r\n                request.destroy();\r\n                socket.destroy();\r\n                reject(new Error(`Error connecting to proxy. Http status code: ${response.statusCode}. Http status message: ${response?.statusMessage || \"Unknown\"}`));\r\n            }\r\n            if (tunnelRequestOptions.timeout) {\r\n                socket.setTimeout(tunnelRequestOptions.timeout);\r\n                socket.on(\"timeout\", () => {\r\n                    request.destroy();\r\n                    socket.destroy();\r\n                    reject(new Error(\"Request time out\"));\r\n                });\r\n            }\r\n\r\n            // make a request over an HTTP tunnel\r\n            socket.write(outgoingRequestString);\r\n\r\n            const data: Buffer[] = [];\r\n            socket.on(\"data\", (chunk) => {\r\n                data.push(chunk);\r\n            });\r\n\r\n            socket.on(\"end\", () => {\r\n                // combine all received buffer streams into one buffer, and then into a string\r\n                const dataString = Buffer.concat([...data]).toString();\r\n\r\n                // separate each line into it's own entry in an arry\r\n                const dataStringArray = dataString.split(\"\\r\\n\");\r\n                // the first entry will contain the statusCode and statusMessage\r\n                const httpStatusCode = parseInt(dataStringArray[0].split(\" \")[1]);\r\n                // remove \"HTTP/1.1\" and the status code to get the status message\r\n                const statusMessage = dataStringArray[0].split(\" \").slice(2).join(\" \");\r\n                // the last entry will contain the body\r\n                const body = dataStringArray[dataStringArray.length - 1];\r\n\r\n                // everything in between the first and last entries are the headers\r\n                const headersArray = dataStringArray.slice(1, dataStringArray.length - 2);\r\n\r\n                // build an object out of all the headers\r\n                const entries = new Map();\r\n                headersArray.forEach((header) => {\r\n                    /**\r\n                     * the header might look like \"Content-Length: 1531\", but that is just a string\r\n                     * it needs to be converted to a key/value pair\r\n                     * split the string at the first instance of \":\"\r\n                     * there may be more than one \":\" if the value of the header is supposed to be a JSON object\r\n                     */\r\n                    const headerKeyValue = header.split(new RegExp(/:\\s(.*)/s));\r\n                    const headerKey = headerKeyValue[0];\r\n                    let headerValue = headerKeyValue[1];\r\n\r\n                    // check if the value of the header is supposed to be a JSON object\r\n                    try {\r\n                        const object = JSON.parse(headerValue);\r\n\r\n                        // if it is, then convert it from a string to a JSON object\r\n                        if (object && (typeof object === \"object\")) {\r\n                            headerValue = object;\r\n                        }\r\n                    } catch (e) {\r\n                        // otherwise, leave it as a string\r\n                    }\r\n\r\n                    entries.set(headerKey, headerValue);\r\n                });\r\n                const headers = Object.fromEntries(entries);\r\n\r\n                const parsedHeaders = headers as Record<string, string>;\r\n                const networkResponse = NetworkUtils.getNetworkResponse(\r\n                    parsedHeaders,\r\n                    parseBody(httpStatusCode, statusMessage, parsedHeaders, body) as T,\r\n                    httpStatusCode\r\n                );\r\n\r\n                if (((httpStatusCode < HttpStatus.SUCCESS_RANGE_START) || (httpStatusCode > HttpStatus.SUCCESS_RANGE_END)) &&\r\n                    // do not destroy the request for the device code flow\r\n                    networkResponse.body[\"error\"] !== Constants.AUTHORIZATION_PENDING) {\r\n                    request.destroy();\r\n                }\r\n                resolve(networkResponse);\r\n            });\r\n\r\n            socket.on(\"error\", (chunk) => {\r\n                request.destroy();\r\n                socket.destroy();\r\n                reject(new Error(chunk.toString()));\r\n            });\r\n        });\r\n\r\n        request.on(\"error\", (chunk) => {\r\n            request.destroy();\r\n            reject(new Error(chunk.toString()));\r\n        });\r\n    }));\r\n};\r\n\r\nconst networkRequestViaHttps = <T>(\r\n    urlString: string,\r\n    httpMethod: string,\r\n    options?: NetworkRequestOptions,\r\n    agentOptions?: https.AgentOptions,\r\n    timeout?: number,\r\n): Promise<NetworkResponse<T>> => {\r\n    const isPostRequest = httpMethod === HttpMethod.POST;\r\n    const body: string = options?.body || \"\";\r\n\r\n    const url = new URL(urlString);\r\n    const headers = options?.headers || {} as Record<string, string>;\r\n    const customOptions: https.RequestOptions = {\r\n        method: httpMethod,\r\n        headers: headers,\r\n        ...NetworkUtils.urlToHttpOptions(url),\r\n    };\r\n\r\n    if (timeout) {\r\n        customOptions.timeout = timeout;\r\n    }\r\n\r\n    if (agentOptions && Object.keys(agentOptions).length) {\r\n        customOptions.agent = new https.Agent(agentOptions);\r\n    }\r\n\r\n    if (isPostRequest) {\r\n        // needed for post request to work\r\n        customOptions.headers = {\r\n            ...customOptions.headers,\r\n            \"Content-Length\": body.length,\r\n        };\r\n    }\r\n\r\n    return new Promise<NetworkResponse<T>>((resolve, reject) => {\r\n        const request = https.request(customOptions);\r\n\r\n        if (timeout) {\r\n            request.on(\"timeout\", () => {\r\n                request.destroy();\r\n                reject(new Error(\"Request time out\"));\r\n            });\r\n        }\r\n\r\n        if (isPostRequest) {\r\n            request.write(body);\r\n        }\r\n\r\n        request.end();\r\n\r\n        request.on(\"response\", (response) => {\r\n            const headers = response.headers;\r\n            const statusCode = response.statusCode as number;\r\n            const statusMessage = response.statusMessage;\r\n\r\n            const data: Buffer[] = [];\r\n            response.on(\"data\", (chunk) => {\r\n                data.push(chunk);\r\n            });\r\n\r\n            response.on(\"end\", () => {\r\n                // combine all received buffer streams into one buffer, and then into a string\r\n                const body = Buffer.concat([...data]).toString();\r\n\r\n                const parsedHeaders = headers as Record<string, string>;\r\n                const networkResponse = NetworkUtils.getNetworkResponse(\r\n                    parsedHeaders,\r\n                    parseBody(statusCode, statusMessage, parsedHeaders, body) as T,\r\n                    statusCode\r\n                );\r\n\r\n                if (((statusCode < HttpStatus.SUCCESS_RANGE_START) || (statusCode > HttpStatus.SUCCESS_RANGE_END)) &&\r\n                    // do not destroy the request for the device code flow\r\n                    networkResponse.body[\"error\"] !== Constants.AUTHORIZATION_PENDING) {\r\n                    request.destroy();\r\n                }\r\n                resolve(networkResponse);\r\n            });\r\n        });\r\n\r\n        request.on(\"error\", (chunk) => {\r\n            request.destroy();\r\n            reject(new Error(chunk.toString()));\r\n        });\r\n    });\r\n};\r\n\r\n/**\r\n * Check if extra parsing is needed on the repsonse from the server\r\n * @param statusCode {number} the status code of the response from the server\r\n * @param statusMessage {string | undefined} the status message of the response from the server\r\n * @param headers {Record<string, string>} the headers of the response from the server\r\n * @param body {string} the body from the response of the server\r\n * @returns {Object} JSON parsed body or error object\r\n */\r\nconst parseBody = (statusCode: number, statusMessage: string | undefined, headers: Record<string, string>, body: string) => {\r\n    /*\r\n     * Informational responses (100 – 199)\r\n     * Successful responses (200 – 299)\r\n     * Redirection messages (300 – 399)\r\n     * Client error responses (400 – 499)\r\n     * Server error responses (500 – 599)\r\n     */\r\n    \r\n    let parsedBody;\r\n    try {\r\n        parsedBody = JSON.parse(body);\r\n    } catch (error) {\r\n        let errorType;\r\n        let errorDescriptionHelper;\r\n        if ((statusCode >= HttpStatus.CLIENT_ERROR_RANGE_START) && (statusCode <= HttpStatus.CLIENT_ERROR_RANGE_END)) {\r\n            errorType = \"client_error\";\r\n            errorDescriptionHelper = \"A client\";\r\n        } else if ((statusCode >= HttpStatus.SERVER_ERROR_RANGE_START) && (statusCode <= HttpStatus.SERVER_ERROR_RANGE_END)) {\r\n            errorType = \"server_error\";\r\n            errorDescriptionHelper = \"A server\";\r\n        } else {\r\n            errorType = \"unknown_error\";\r\n            errorDescriptionHelper = \"An unknown\";\r\n        }\r\n\r\n        parsedBody = {\r\n            error: errorType,\r\n            error_description: `${errorDescriptionHelper} error occured.\\nHttp status code: ${statusCode}\\nHttp status message: ${statusMessage || \"Unknown\"}\\nHeaders: ${JSON.stringify(headers)}`\r\n        };\r\n    }\r\n\r\n    return parsedBody;\r\n};\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport {\r\n    LoggerOptions,\r\n    INetworkModule,\r\n    LogLevel,\r\n    ProtocolMode,\r\n    ICachePlugin,\r\n    Constants,\r\n    AzureCloudInstance,\r\n    AzureCloudOptions,\r\n    ApplicationTelemetry,\r\n    INativeBrokerPlugin\r\n} from \"@azure/msal-common\";\r\nimport { HttpClient } from \"../network/HttpClient\";\r\nimport { AgentOptions as httpAgentOptions } from \"http\";\r\nimport { AgentOptions as httpsAgentOptions } from \"https\";\r\n\r\n/**\r\n * - clientId               - Client id of the application.\r\n * - authority              - Url of the authority. If no value is set, defaults to https://login.microsoftonline.com/common.\r\n * - knownAuthorities       - Needed for Azure B2C and ADFS. All authorities that will be used in the client application. Only the host of the authority should be passed in.\r\n * - clientSecret           - Secret string that the application uses when requesting a token. Only used in confidential client applications. Can be created in the Azure app registration portal.\r\n * - clientAssertion        - Assertion string that the application uses when requesting a token. Only used in confidential client applications. Assertion should be of type urn:ietf:params:oauth:client-assertion-type:jwt-bearer.\r\n * - clientCertificate      - Certificate that the application uses when requesting a token. Only used in confidential client applications. Requires hex encoded X.509 SHA-1 thumbprint of the certificiate, and the PEM encoded private key (string should contain -----BEGIN PRIVATE KEY----- ... -----END PRIVATE KEY----- )\r\n * - protocolMode           - Enum that represents the protocol that msal follows. Used for configuring proper endpoints.\r\n * - skipAuthorityMetadataCache - A flag to choose whether to use or not use the local metadata cache during authority initialization. Defaults to false.\r\n * @public\r\n */\r\nexport type NodeAuthOptions = {\r\n    clientId: string;\r\n    authority?: string;\r\n    clientSecret?: string;\r\n    clientAssertion?: string;\r\n    clientCertificate?: {\r\n        thumbprint: string,\r\n        privateKey: string,\r\n        x5c?: string\r\n    };\r\n    knownAuthorities?: Array<string>;\r\n    cloudDiscoveryMetadata?: string;\r\n    authorityMetadata?: string;\r\n    clientCapabilities?: Array<string>;\r\n    protocolMode?: ProtocolMode;\r\n    azureCloudOptions?: AzureCloudOptions;\r\n    skipAuthorityMetadataCache?: boolean;\r\n};\r\n\r\n/**\r\n * Use this to configure the below cache configuration options:\r\n *\r\n * - cachePlugin   - Plugin for reading and writing token cache to disk.\r\n * - claimsBasedCachingEnabled - Flag to enable/disable claims based caching. Set to true by default.\r\n * @public\r\n */\r\nexport type CacheOptions = {\r\n    cachePlugin?: ICachePlugin;\r\n    claimsBasedCachingEnabled?: boolean;\r\n};\r\n\r\n/**\r\n * Use this to configure the below broker options:\r\n * - nativeBrokerPlugin - Native broker implementation (should be imported from msal-node-extensions)\r\n * \r\n * Note: These options are only available for PublicClientApplications using the Authorization Code Flow\r\n * @public\r\n */\r\nexport type BrokerOptions = {\r\n    nativeBrokerPlugin?: INativeBrokerPlugin;\r\n};\r\n\r\n/**\r\n * Type for configuring logger and http client options\r\n *\r\n * - logger                       - Used to initialize the Logger object; TODO: Expand on logger details or link to the documentation on logger\r\n * - networkClient                - Http client used for all http get and post calls. Defaults to using MSAL's default http client.\r\n * @public\r\n */\r\nexport type NodeSystemOptions = {\r\n    loggerOptions?: LoggerOptions;\r\n    networkClient?: INetworkModule;\r\n    proxyUrl?: string;\r\n    customAgentOptions?: httpAgentOptions | httpsAgentOptions;\r\n};\r\n\r\nexport type NodeTelemetryOptions = {\r\n    application?: ApplicationTelemetry;\r\n};\r\n\r\n/**\r\n * Use the configuration object to configure MSAL and initialize the client application object\r\n *\r\n * - auth: this is where you configure auth elements like clientID, authority used for authenticating against the Microsoft Identity Platform\r\n * - broker: this is where you configure broker options\r\n * - cache: this is where you configure cache location\r\n * - system: this is where you can configure the network client, logger\r\n * - telemetry: this is where you can configure telemetry options\r\n * @public\r\n */\r\nexport type Configuration = {\r\n    auth: NodeAuthOptions;\r\n    broker?: BrokerOptions;\r\n    cache?: CacheOptions;\r\n    system?: NodeSystemOptions;\r\n    telemetry?: NodeTelemetryOptions;\r\n};\r\n\r\nconst DEFAULT_AUTH_OPTIONS: Required<NodeAuthOptions> = {\r\n    clientId: Constants.EMPTY_STRING,\r\n    authority: Constants.DEFAULT_AUTHORITY,\r\n    clientSecret: Constants.EMPTY_STRING,\r\n    clientAssertion: Constants.EMPTY_STRING,\r\n    clientCertificate: {\r\n        thumbprint: Constants.EMPTY_STRING,\r\n        privateKey: Constants.EMPTY_STRING,\r\n        x5c: Constants.EMPTY_STRING\r\n    },\r\n    knownAuthorities: [],\r\n    cloudDiscoveryMetadata: Constants.EMPTY_STRING,\r\n    authorityMetadata: Constants.EMPTY_STRING,\r\n    clientCapabilities: [],\r\n    protocolMode: ProtocolMode.AAD,\r\n    azureCloudOptions: {\r\n        azureCloudInstance: AzureCloudInstance.None,\r\n        tenant: Constants.EMPTY_STRING\r\n    },\r\n    skipAuthorityMetadataCache: false,\r\n};\r\n\r\nconst DEFAULT_CACHE_OPTIONS: CacheOptions = {\r\n    claimsBasedCachingEnabled: true\r\n};\r\n\r\nconst DEFAULT_LOGGER_OPTIONS: LoggerOptions = {\r\n    loggerCallback: (): void => {\r\n        // allow users to not set logger call back \r\n    },\r\n    piiLoggingEnabled: false,\r\n    logLevel: LogLevel.Info,\r\n};\r\n\r\nconst DEFAULT_SYSTEM_OPTIONS: Required<NodeSystemOptions> = {\r\n    loggerOptions: DEFAULT_LOGGER_OPTIONS,\r\n    networkClient: new HttpClient(),\r\n    proxyUrl: Constants.EMPTY_STRING,\r\n    customAgentOptions: {} as httpAgentOptions | httpsAgentOptions,\r\n};\r\n\r\nconst DEFAULT_TELEMETRY_OPTIONS: Required<NodeTelemetryOptions> = {\r\n    application: {\r\n        appName: Constants.EMPTY_STRING,\r\n        appVersion: Constants.EMPTY_STRING\r\n    }\r\n};\r\n\r\nexport type NodeConfiguration = {\r\n    auth: Required<NodeAuthOptions>;\r\n    broker: BrokerOptions;\r\n    cache: CacheOptions;\r\n    system: Required<NodeSystemOptions>;\r\n    telemetry: Required<NodeTelemetryOptions>;\r\n};\r\n\r\n/**\r\n * Sets the default options when not explicitly configured from app developer\r\n *\r\n * @param auth - Authentication options\r\n * @param cache - Cache options\r\n * @param system - System options\r\n * @param telemetry - Telemetry options\r\n *\r\n * @returns Configuration\r\n * @public\r\n */\r\nexport function buildAppConfiguration({\r\n    auth,\r\n    broker,\r\n    cache,\r\n    system,\r\n    telemetry\r\n}: Configuration): NodeConfiguration {\r\n    const systemOptions: Required<NodeSystemOptions> = {\r\n        ...DEFAULT_SYSTEM_OPTIONS,\r\n        networkClient: new HttpClient(system?.proxyUrl, (system?.customAgentOptions as httpAgentOptions | httpsAgentOptions)),\r\n        loggerOptions: system?.loggerOptions || DEFAULT_LOGGER_OPTIONS,\r\n    };\r\n\r\n    return {\r\n        auth: { ...DEFAULT_AUTH_OPTIONS, ...auth },\r\n        broker: { ...broker},\r\n        cache: { ...DEFAULT_CACHE_OPTIONS, ...cache },\r\n        system: { ...systemOptions, ...system },\r\n        telemetry: { ...DEFAULT_TELEMETRY_OPTIONS, ...telemetry }\r\n    };\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { IGuidGenerator } from \"@azure/msal-common\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\n\r\nexport class GuidGenerator implements IGuidGenerator {\r\n    /**\r\n     *\r\n     * RFC4122: The version 4 UUID is meant for generating UUIDs from truly-random or pseudo-random numbers.\r\n     * uuidv4 generates guids from cryprtographically-string random\r\n     */\r\n    generateGuid(): string {\r\n        return uuidv4();\r\n    }\r\n\r\n    /**\r\n     * verifies if a string is  GUID\r\n     * @param guid\r\n     */\r\n    isGuid(guid: string): boolean {\r\n        const regexGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n        return regexGuid.test(guid);\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants } from \"@azure/msal-common\";\r\n\r\nexport class EncodingUtils {\r\n    /**\r\n     * 'utf8': Multibyte encoded Unicode characters. Many web pages and other document formats use UTF-8.\r\n     * 'base64': Base64 encoding.\r\n     *\r\n     * @param str text\r\n     */\r\n    static base64Encode(str: string, encoding?: BufferEncoding): string {\r\n        return Buffer.from(str, encoding).toString(\"base64\");\r\n    }\r\n\r\n    /**\r\n     * encode a URL\r\n     * @param str\r\n     */\r\n    static base64EncodeUrl(str: string, encoding?: BufferEncoding): string {\r\n        return EncodingUtils.base64Encode(str, encoding)\r\n            .replace(/=/g, Constants.EMPTY_STRING)\r\n            .replace(/\\+/g, \"-\")\r\n            .replace(/\\//g, \"_\");\r\n    }\r\n\r\n    /**\r\n     * 'utf8': Multibyte encoded Unicode characters. Many web pages and other document formats use UTF-8.\r\n     * 'base64': Base64 encoding.\r\n     *\r\n     * @param base64Str Base64 encoded text\r\n     */\r\n    static base64Decode(base64Str: string): string {\r\n        return Buffer.from(base64Str, \"base64\").toString(\"utf8\");\r\n    }\r\n\r\n    /**\r\n     * @param base64Str Base64 encoded Url\r\n     */\r\n    static base64DecodeUrl(base64Str: string): string {\r\n        let str = base64Str.replace(/-/g, \"+\").replace(/_/g, \"/\");\r\n        while (str.length % 4) {\r\n            str += \"=\";\r\n        }\r\n        return EncodingUtils.base64Decode(str);\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Hash } from \"../utils/Constants\";\r\nimport crypto from \"crypto\";\r\n\r\nexport class HashUtils {\r\n    /**\r\n     * generate 'SHA256' hash\r\n     * @param buffer\r\n     */\r\n    sha256(buffer: string): Buffer {\r\n        return crypto\r\n            .createHash(Hash.SHA256)\r\n            .update(buffer)\r\n            .digest();\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants, PkceCodes } from \"@azure/msal-common\";\r\nimport { CharSet, RANDOM_OCTET_SIZE } from \"../utils/Constants\";\r\nimport { EncodingUtils } from \"../utils/EncodingUtils\";\r\nimport { HashUtils } from \"./HashUtils\";\r\nimport crypto from \"crypto\";\r\n\r\n/**\r\n * https://tools.ietf.org/html/rfc7636#page-8\r\n */\r\nexport class PkceGenerator {\r\n    private hashUtils: HashUtils;\r\n\r\n    constructor() {\r\n        this.hashUtils = new HashUtils();\r\n    }\r\n    /**\r\n     * generates the codeVerfier and the challenge from the codeVerfier\r\n     * reference: https://tools.ietf.org/html/rfc7636#section-4.1 and https://tools.ietf.org/html/rfc7636#section-4.2\r\n     */\r\n    async generatePkceCodes(): Promise<PkceCodes> {\r\n        const verifier = this.generateCodeVerifier();\r\n        const challenge = this.generateCodeChallengeFromVerifier(verifier);\r\n        return { verifier, challenge };\r\n    }\r\n\r\n    /**\r\n     * generates the codeVerfier; reference: https://tools.ietf.org/html/rfc7636#section-4.1\r\n     */\r\n    private generateCodeVerifier(): string {\r\n        const charArr = [];\r\n        const maxNumber = 256 - (256 % CharSet.CV_CHARSET.length);\r\n        while (charArr.length <= RANDOM_OCTET_SIZE) {\r\n            const byte = crypto.randomBytes(1)[0];\r\n            if (byte >= maxNumber) {\r\n                /* \r\n                 * Ignore this number to maintain randomness.\r\n                 * Including it would result in an unequal distribution of characters after doing the modulo\r\n                 */\r\n                continue;\r\n            }\r\n            const index = byte % CharSet.CV_CHARSET.length;\r\n            charArr.push(CharSet.CV_CHARSET[index]);\r\n        }\r\n        const verifier: string = charArr.join(Constants.EMPTY_STRING);\r\n        return EncodingUtils.base64EncodeUrl(verifier);\r\n    }\r\n\r\n    /**\r\n     * generate the challenge from the codeVerfier; reference: https://tools.ietf.org/html/rfc7636#section-4.2\r\n     * @param codeVerifier\r\n     */\r\n    private generateCodeChallengeFromVerifier(codeVerifier: string): string {\r\n        return EncodingUtils.base64EncodeUrl(\r\n            this.hashUtils.sha256(codeVerifier).toString(\"base64\"), \r\n            \"base64\" \r\n        );\r\n    }\r\n\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ICrypto, PkceCodes } from \"@azure/msal-common\";\r\nimport { GuidGenerator } from \"./GuidGenerator\";\r\nimport { EncodingUtils } from \"../utils/EncodingUtils\";\r\nimport { PkceGenerator } from \"./PkceGenerator\";\r\nimport { HashUtils } from \"./HashUtils\";\r\n\r\n/**\r\n * This class implements MSAL node's crypto interface, which allows it to perform base64 encoding and decoding, generating cryptographically random GUIDs and\r\n * implementing Proof Key for Code Exchange specs for the OAuth Authorization Code Flow using PKCE (rfc here: https://tools.ietf.org/html/rfc7636).\r\n * @public\r\n */\r\nexport class CryptoProvider implements ICrypto {\r\n    private pkceGenerator: PkceGenerator;\r\n    private guidGenerator: GuidGenerator;\r\n    private hashUtils: HashUtils;\r\n\r\n    constructor() {\r\n        // Browser crypto needs to be validated first before any other classes can be set.\r\n        this.pkceGenerator = new PkceGenerator();\r\n        this.guidGenerator = new GuidGenerator();\r\n        this.hashUtils = new HashUtils();\r\n    }\r\n\r\n    /**\r\n     * Creates a new random GUID - used to populate state and nonce.\r\n     * @returns string (GUID)\r\n     */\r\n    createNewGuid(): string {\r\n        return this.guidGenerator.generateGuid();\r\n    }\r\n\r\n    /**\r\n     * Encodes input string to base64.\r\n     * @param input - string to be encoded\r\n     */\r\n    base64Encode(input: string): string {\r\n        return EncodingUtils.base64Encode(input);\r\n    }\r\n\r\n    /**\r\n     * Decodes input string from base64.\r\n     * @param input - string to be decoded\r\n     */\r\n    base64Decode(input: string): string {\r\n        return EncodingUtils.base64Decode(input);\r\n    }\r\n\r\n    /**\r\n     * Generates PKCE codes used in Authorization Code Flow.\r\n     */\r\n    generatePkceCodes(): Promise<PkceCodes> {\r\n        return this.pkceGenerator.generatePkceCodes();\r\n    }\r\n\r\n    /**\r\n     * Generates a keypair, stores it and returns a thumbprint - not yet implemented for node\r\n     */\r\n    getPublicKeyThumbprint(): Promise<string> {\r\n        throw new Error(\"Method not implemented.\");\r\n    }\r\n\r\n    /**\r\n     * Removes cryptographic keypair from key store matching the keyId passed in\r\n     * @param kid \r\n     */\r\n    removeTokenBindingKey(): Promise<boolean> {\r\n        throw new Error(\"Method not implemented.\");\r\n    }\r\n\r\n    /**\r\n     * Removes all cryptographic keys from Keystore\r\n     */\r\n    clearKeystore(): Promise<boolean> {\r\n        throw new Error(\"Method not implemented.\");\r\n    }\r\n\r\n    /**\r\n     * Signs the given object as a jwt payload with private key retrieved by given kid - currently not implemented for node\r\n     */\r\n    signJwt(): Promise<string> {\r\n        throw new Error(\"Method not implemented.\");\r\n    }\r\n\r\n    /**\r\n     * Returns the SHA-256 hash of an input string\r\n     */\r\n    async hashString(plainText: string): Promise<string> {\r\n        return EncodingUtils.base64EncodeUrl(\r\n            this.hashUtils.sha256(plainText).toString(\"base64\"), \r\n            \"base64\" \r\n        );\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { StringUtils, AccountCache, IdTokenCache, AccessTokenCache, RefreshTokenCache, AppMetadataCache, AccountEntity, IdTokenEntity, AccessTokenEntity, RefreshTokenEntity, AppMetadataEntity, CacheManager } from \"@azure/msal-common\";\r\nimport { JsonCache, InMemoryCache, SerializedAccountEntity, SerializedIdTokenEntity, SerializedAccessTokenEntity, SerializedRefreshTokenEntity, SerializedAppMetadataEntity } from \"./SerializerTypes\";\r\n\r\n/**\r\n * This class deserializes cache entities read from the file into in memory object types defined internally\r\n */\r\nexport class Deserializer {\r\n    /**\r\n     * Parse the JSON blob in memory and deserialize the content\r\n     * @param cachedJson\r\n     */\r\n    static deserializeJSONBlob(jsonFile: string): JsonCache {\r\n        const deserializedCache = StringUtils.isEmpty(jsonFile)\r\n            ? {}\r\n            : JSON.parse(jsonFile);\r\n        return deserializedCache;\r\n    }\r\n\r\n    /**\r\n     * Deserializes accounts to AccountEntity objects\r\n     * @param accounts\r\n     */\r\n    static deserializeAccounts(accounts: Record<string, SerializedAccountEntity>): AccountCache {\r\n        const accountObjects: AccountCache = {};\r\n        if (accounts) {\r\n            Object.keys(accounts).map(function (key) {\r\n                const serializedAcc = accounts[key];\r\n                const mappedAcc = {\r\n                    homeAccountId: serializedAcc.home_account_id,\r\n                    environment: serializedAcc.environment,\r\n                    realm: serializedAcc.realm,\r\n                    localAccountId: serializedAcc.local_account_id,\r\n                    username: serializedAcc.username,\r\n                    authorityType: serializedAcc.authority_type,\r\n                    name: serializedAcc.name,\r\n                    clientInfo: serializedAcc.client_info,\r\n                    lastModificationTime: serializedAcc.last_modification_time,\r\n                    lastModificationApp: serializedAcc.last_modification_app,\r\n                };\r\n                const account: AccountEntity = new AccountEntity();\r\n                CacheManager.toObject(account, mappedAcc);\r\n                accountObjects[key] = account;\r\n            });\r\n        }\r\n\r\n        return accountObjects;\r\n    }\r\n\r\n    /**\r\n     * Deserializes id tokens to IdTokenEntity objects\r\n     * @param idTokens\r\n     */\r\n    static deserializeIdTokens(idTokens: Record<string, SerializedIdTokenEntity>): IdTokenCache {\r\n        const idObjects: IdTokenCache = {};\r\n        if (idTokens) {\r\n            Object.keys(idTokens).map(function (key) {\r\n                const serializedIdT = idTokens[key];\r\n                const mappedIdT = {\r\n                    homeAccountId: serializedIdT.home_account_id,\r\n                    environment: serializedIdT.environment,\r\n                    credentialType: serializedIdT.credential_type,\r\n                    clientId: serializedIdT.client_id,\r\n                    secret: serializedIdT.secret,\r\n                    realm: serializedIdT.realm,\r\n                };\r\n                const idToken: IdTokenEntity = new IdTokenEntity();\r\n                CacheManager.toObject(idToken, mappedIdT);\r\n                idObjects[key] = idToken;\r\n            });\r\n        }\r\n        return idObjects;\r\n    }\r\n\r\n    /**\r\n     * Deserializes access tokens to AccessTokenEntity objects\r\n     * @param accessTokens\r\n     */\r\n    static deserializeAccessTokens(accessTokens: Record<string, SerializedAccessTokenEntity>): AccessTokenCache {\r\n        const atObjects: AccessTokenCache = {};\r\n        if (accessTokens) {\r\n            Object.keys(accessTokens).map(function (key) {\r\n                const serializedAT = accessTokens[key];\r\n                const mappedAT = {\r\n                    homeAccountId: serializedAT.home_account_id,\r\n                    environment: serializedAT.environment,\r\n                    credentialType: serializedAT.credential_type,\r\n                    clientId: serializedAT.client_id,\r\n                    secret: serializedAT.secret,\r\n                    realm: serializedAT.realm,\r\n                    target: serializedAT.target,\r\n                    cachedAt: serializedAT.cached_at,\r\n                    expiresOn: serializedAT.expires_on,\r\n                    extendedExpiresOn: serializedAT.extended_expires_on,\r\n                    refreshOn: serializedAT.refresh_on,\r\n                    keyId: serializedAT.key_id,\r\n                    tokenType: serializedAT.token_type,\r\n                    requestedClaims: serializedAT.requestedClaims,\r\n                    requestedClaimsHash: serializedAT.requestedClaimsHash,\r\n                    userAssertionHash: serializedAT.userAssertionHash,\r\n                };\r\n                const accessToken: AccessTokenEntity = new AccessTokenEntity();\r\n                CacheManager.toObject(accessToken, mappedAT);\r\n                atObjects[key] = accessToken;\r\n            });\r\n        }\r\n\r\n        return atObjects;\r\n    }\r\n\r\n    /**\r\n     * Deserializes refresh tokens to RefreshTokenEntity objects\r\n     * @param refreshTokens\r\n     */\r\n    static deserializeRefreshTokens(refreshTokens: Record<string, SerializedRefreshTokenEntity>): RefreshTokenCache {\r\n        const rtObjects: RefreshTokenCache = {};\r\n        if (refreshTokens) {\r\n            Object.keys(refreshTokens).map(function (key) {\r\n                const serializedRT = refreshTokens[key];\r\n                const mappedRT = {\r\n                    homeAccountId: serializedRT.home_account_id,\r\n                    environment: serializedRT.environment,\r\n                    credentialType: serializedRT.credential_type,\r\n                    clientId: serializedRT.client_id,\r\n                    secret: serializedRT.secret,\r\n                    familyId: serializedRT.family_id,\r\n                    target: serializedRT.target,\r\n                    realm: serializedRT.realm,\r\n                };\r\n                const refreshToken: RefreshTokenEntity = new RefreshTokenEntity();\r\n                CacheManager.toObject(refreshToken, mappedRT);\r\n                rtObjects[key] = refreshToken;\r\n            });\r\n        }\r\n\r\n        return rtObjects;\r\n    }\r\n\r\n    /**\r\n     * Deserializes appMetadata to AppMetaData objects\r\n     * @param appMetadata\r\n     */\r\n    static deserializeAppMetadata(appMetadata: Record<string, SerializedAppMetadataEntity>): AppMetadataCache {\r\n        const appMetadataObjects: AppMetadataCache = {};\r\n        if (appMetadata) {\r\n            Object.keys(appMetadata).map(function (key) {\r\n                const serializedAmdt = appMetadata[key];\r\n                const mappedAmd = {\r\n                    clientId: serializedAmdt.client_id,\r\n                    environment: serializedAmdt.environment,\r\n                    familyId: serializedAmdt.family_id,\r\n                };\r\n                const amd: AppMetadataEntity = new AppMetadataEntity();\r\n                CacheManager.toObject(amd, mappedAmd);\r\n                appMetadataObjects[key] = amd;\r\n            });\r\n        }\r\n\r\n        return appMetadataObjects;\r\n    }\r\n\r\n    /**\r\n     * Deserialize an inMemory Cache\r\n     * @param jsonCache\r\n     */\r\n    static deserializeAllCache(jsonCache: JsonCache): InMemoryCache {\r\n        return {\r\n            accounts: jsonCache.Account\r\n                ? this.deserializeAccounts(jsonCache.Account)\r\n                : {},\r\n            idTokens: jsonCache.IdToken\r\n                ? this.deserializeIdTokens(jsonCache.IdToken)\r\n                : {},\r\n            accessTokens: jsonCache.AccessToken\r\n                ? this.deserializeAccessTokens(jsonCache.AccessToken)\r\n                : {},\r\n            refreshTokens: jsonCache.RefreshToken\r\n                ? this.deserializeRefreshTokens(jsonCache.RefreshToken)\r\n                : {},\r\n            appMetadata: jsonCache.AppMetadata\r\n                ? this.deserializeAppMetadata(jsonCache.AppMetadata)\r\n                : {},\r\n        };\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AccountCache, IdTokenCache, AccessTokenCache, RefreshTokenCache, AppMetadataCache } from \"@azure/msal-common\";\r\nimport { InMemoryCache, JsonCache, SerializedAccountEntity, SerializedIdTokenEntity, SerializedAccessTokenEntity, SerializedRefreshTokenEntity, SerializedAppMetadataEntity } from \"./SerializerTypes\";\r\n\r\nexport class Serializer {\r\n    /**\r\n     * serialize the JSON blob\r\n     * @param data\r\n     */\r\n    static serializeJSONBlob(data: JsonCache): string {\r\n        return JSON.stringify(data);\r\n    }\r\n\r\n    /**\r\n     * Serialize Accounts\r\n     * @param accCache\r\n     */\r\n    static serializeAccounts(accCache: AccountCache): Record<string, SerializedAccountEntity> {\r\n        const accounts: Record<string, SerializedAccountEntity> = {};\r\n        Object.keys(accCache).map(function (key) {\r\n            const accountEntity = accCache[key];\r\n            accounts[key] = {\r\n                home_account_id: accountEntity.homeAccountId,\r\n                environment: accountEntity.environment,\r\n                realm: accountEntity.realm,\r\n                local_account_id: accountEntity.localAccountId,\r\n                username: accountEntity.username,\r\n                authority_type: accountEntity.authorityType,\r\n                name: accountEntity.name,\r\n                client_info: accountEntity.clientInfo,\r\n                last_modification_time: accountEntity.lastModificationTime,\r\n                last_modification_app: accountEntity.lastModificationApp,\r\n            };\r\n        });\r\n\r\n        return accounts;\r\n    }\r\n\r\n    /**\r\n     * Serialize IdTokens\r\n     * @param idTCache\r\n     */\r\n    static serializeIdTokens(idTCache: IdTokenCache): Record<string, SerializedIdTokenEntity> {\r\n        const idTokens: Record<string, SerializedIdTokenEntity> = {};\r\n        Object.keys(idTCache).map(function (key) {\r\n            const idTEntity = idTCache[key];\r\n            idTokens[key] = {\r\n                home_account_id: idTEntity.homeAccountId,\r\n                environment: idTEntity.environment,\r\n                credential_type: idTEntity.credentialType,\r\n                client_id: idTEntity.clientId,\r\n                secret: idTEntity.secret,\r\n                realm: idTEntity.realm,\r\n            };\r\n        });\r\n\r\n        return idTokens;\r\n    }\r\n\r\n    /**\r\n     * Serializes AccessTokens\r\n     * @param atCache\r\n     */\r\n    static serializeAccessTokens(atCache: AccessTokenCache): Record<string, SerializedAccessTokenEntity> {\r\n        const accessTokens: Record<string, SerializedAccessTokenEntity> = {};\r\n        Object.keys(atCache).map(function (key) {\r\n            const atEntity = atCache[key];\r\n            accessTokens[key] = {\r\n                home_account_id: atEntity.homeAccountId,\r\n                environment: atEntity.environment,\r\n                credential_type: atEntity.credentialType,\r\n                client_id: atEntity.clientId,\r\n                secret: atEntity.secret,\r\n                realm: atEntity.realm,\r\n                target: atEntity.target,\r\n                cached_at: atEntity.cachedAt,\r\n                expires_on: atEntity.expiresOn,\r\n                extended_expires_on: atEntity.extendedExpiresOn,\r\n                refresh_on: atEntity.refreshOn,\r\n                key_id: atEntity.keyId,\r\n                token_type: atEntity.tokenType,\r\n                requestedClaims: atEntity.requestedClaims,\r\n                requestedClaimsHash: atEntity.requestedClaimsHash,\r\n                userAssertionHash: atEntity.userAssertionHash\r\n            };\r\n        });\r\n\r\n        return accessTokens;\r\n    }\r\n\r\n    /**\r\n     * Serialize refreshTokens\r\n     * @param rtCache\r\n     */\r\n    static serializeRefreshTokens(rtCache: RefreshTokenCache): Record<string, SerializedRefreshTokenEntity> {\r\n        const refreshTokens: Record<string, SerializedRefreshTokenEntity> = {};\r\n        Object.keys(rtCache).map(function (key) {\r\n            const rtEntity = rtCache[key];\r\n            refreshTokens[key] = {\r\n                home_account_id: rtEntity.homeAccountId,\r\n                environment: rtEntity.environment,\r\n                credential_type: rtEntity.credentialType,\r\n                client_id: rtEntity.clientId,\r\n                secret: rtEntity.secret,\r\n                family_id: rtEntity.familyId,\r\n                target: rtEntity.target,\r\n                realm: rtEntity.realm\r\n            };\r\n        });\r\n\r\n        return refreshTokens;\r\n    }\r\n\r\n    /**\r\n     * Serialize amdtCache\r\n     * @param amdtCache\r\n     */\r\n    static serializeAppMetadata(amdtCache: AppMetadataCache): Record<string, SerializedAppMetadataEntity> {\r\n        const appMetadata: Record<string, SerializedAppMetadataEntity> = {};\r\n        Object.keys(amdtCache).map(function (key) {\r\n            const amdtEntity = amdtCache[key];\r\n            appMetadata[key] = {\r\n                client_id: amdtEntity.clientId,\r\n                environment: amdtEntity.environment,\r\n                family_id: amdtEntity.familyId,\r\n            };\r\n        });\r\n\r\n        return appMetadata;\r\n    }\r\n\r\n    /**\r\n     * Serialize the cache\r\n     * @param jsonContent\r\n     */\r\n    static serializeAllCache(inMemCache: InMemoryCache): JsonCache {\r\n        return {\r\n            Account: this.serializeAccounts(inMemCache.accounts),\r\n            IdToken: this.serializeIdTokens(inMemCache.idTokens),\r\n            AccessToken: this.serializeAccessTokens(inMemCache.accessTokens),\r\n            RefreshToken: this.serializeRefreshTokens(inMemCache.refreshTokens),\r\n            AppMetadata: this.serializeAppMetadata(inMemCache.appMetadata),\r\n        };\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON> ,\r\n    AccountEntity,\r\n    IdTokenEntity,\r\n    AccessTokenEntity,\r\n    RefreshTokenEntity,\r\n    AppMetadataEntity,\r\n    ServerTelemetryEntity,\r\n    ThrottlingEntity,\r\n    CacheManager,\r\n    Logger,\r\n    ValidCacheType,\r\n    ICrypto,\r\n    AuthorityMetadataEntity,\r\n    ValidCredentialType\r\n} from \"@azure/msal-common\";\r\n\r\nimport { Deserializer } from \"./serializer/Deserializer\";\r\nimport { Serializer } from \"./serializer/Serializer\";\r\nimport { InMemoryCache, JsonCache, CacheKVStore } from \"./serializer/SerializerTypes\";\r\n\r\n/**\r\n * This class implements Storage for node, reading cache from user specified storage location or an  extension library\r\n * @public\r\n */\r\nexport class NodeStorage extends CacheManager {\r\n    // Cache configuration, either set by user or default values.\r\n    private logger: Logger;\r\n    private cache: CacheKVStore = {};\r\n    private changeEmitters: Array<Function> = [];\r\n\r\n    constructor(logger: Logger, clientId: string, cryptoImpl: ICrypto) {\r\n        super(clientId, cryptoImpl, logger);\r\n        this.logger = logger;\r\n    }\r\n\r\n    /**\r\n     * Queue up callbacks\r\n     * @param func - a callback function for cache change indication\r\n     */\r\n    registerChangeEmitter(func: () => void): void {\r\n        this.changeEmitters.push(func);\r\n    }\r\n\r\n    /**\r\n     * Invoke the callback when cache changes\r\n     */\r\n    emitChange(): void {\r\n        this.changeEmitters.forEach(func => func.call(null));\r\n    }\r\n\r\n    /**\r\n     * Converts cacheKVStore to InMemoryCache\r\n     * @param cache - key value store\r\n     */\r\n    cacheToInMemoryCache(cache: CacheKVStore): InMemoryCache {\r\n        const inMemoryCache: InMemoryCache = {\r\n            accounts: {},\r\n            idTokens: {},\r\n            accessTokens: {},\r\n            refreshTokens: {},\r\n            appMetadata: {},\r\n        };\r\n\r\n        for (const key in cache) {\r\n            if (cache[key as string] instanceof AccountEntity) {\r\n                inMemoryCache.accounts[key] = cache[key] as AccountEntity;\r\n            } else if (cache[key] instanceof IdTokenEntity) {\r\n                inMemoryCache.idTokens[key] = cache[key] as IdTokenEntity;\r\n            } else if (cache[key] instanceof AccessTokenEntity) {\r\n                inMemoryCache.accessTokens[key] = cache[key] as AccessTokenEntity;\r\n            } else if (cache[key] instanceof RefreshTokenEntity) {\r\n                inMemoryCache.refreshTokens[key] = cache[key] as RefreshTokenEntity;\r\n            } else if (cache[key] instanceof AppMetadataEntity) {\r\n                inMemoryCache.appMetadata[key] = cache[key] as AppMetadataEntity;\r\n            } else {\r\n                continue;\r\n            }\r\n        }\r\n\r\n        return inMemoryCache;\r\n    }\r\n\r\n    /**\r\n     * converts inMemoryCache to CacheKVStore\r\n     * @param inMemoryCache - kvstore map for inmemory\r\n     */\r\n    inMemoryCacheToCache(inMemoryCache: InMemoryCache): CacheKVStore {\r\n\r\n        // convert in memory cache to a flat Key-Value map\r\n        let cache = this.getCache();\r\n\r\n        cache = {\r\n            ...cache,\r\n            ...inMemoryCache.accounts,\r\n            ...inMemoryCache.idTokens,\r\n            ...inMemoryCache.accessTokens,\r\n            ...inMemoryCache.refreshTokens,\r\n            ...inMemoryCache.appMetadata\r\n        };\r\n\r\n        // convert in memory cache to a flat Key-Value map\r\n        return cache;\r\n    }\r\n\r\n    /**\r\n     * gets the current in memory cache for the client\r\n     */\r\n    getInMemoryCache(): InMemoryCache {\r\n        this.logger.trace(\"Getting in-memory cache\");\r\n\r\n        // convert the cache key value store to inMemoryCache\r\n        const inMemoryCache = this.cacheToInMemoryCache(this.getCache());\r\n        return inMemoryCache;\r\n    }\r\n\r\n    /**\r\n     * sets the current in memory cache for the client\r\n     * @param inMemoryCache - key value map in memory\r\n     */\r\n    setInMemoryCache(inMemoryCache: InMemoryCache): void{\r\n        this.logger.trace(\"Setting in-memory cache\");\r\n\r\n        // convert and append the inMemoryCache to cacheKVStore\r\n        const cache = this.inMemoryCacheToCache(inMemoryCache);\r\n        this.setCache(cache);\r\n\r\n        this.emitChange();\r\n    }\r\n\r\n    /**\r\n     * get the current cache key-value store\r\n     */\r\n    getCache(): CacheKVStore {\r\n        this.logger.trace(\"Getting cache key-value store\");\r\n        return this.cache;\r\n    }\r\n\r\n    /**\r\n     * sets the current cache (key value store)\r\n     * @param cacheMap - key value map\r\n     */\r\n    setCache(cache: CacheKVStore): void {\r\n        this.logger.trace(\"Setting cache key value store\");\r\n        this.cache = cache;\r\n\r\n        // mark change in cache\r\n        this.emitChange();\r\n    }\r\n\r\n    /**\r\n     * Gets cache item with given key.\r\n     * @param key - lookup key for the cache entry\r\n     */\r\n    getItem(key: string): ValidCacheType {\r\n        this.logger.tracePii(`Item key: ${key}`);\r\n\r\n        // read cache\r\n        const cache = this.getCache();\r\n        return cache[key];\r\n    }\r\n\r\n    /**\r\n     * Gets cache item with given key-value\r\n     * @param key - lookup key for the cache entry\r\n     * @param value - value of the cache entry\r\n     */\r\n    setItem(key: string, value: ValidCacheType): void {\r\n        this.logger.tracePii(`Item key: ${key}`);\r\n\r\n        // read cache\r\n        const cache = this.getCache();\r\n        cache[key] = value;\r\n\r\n        // write to cache\r\n        this.setCache(cache);\r\n    }\r\n\r\n    getAccountKeys(): string[] {\r\n        const inMemoryCache = this.getInMemoryCache();\r\n        const accountKeys = Object.keys(inMemoryCache.accounts);\r\n\r\n        return accountKeys;\r\n    }\r\n\r\n    getTokenKeys(): TokenKeys {\r\n        const inMemoryCache = this.getInMemoryCache();\r\n        const tokenKeys = {\r\n            idToken: Object.keys(inMemoryCache.idTokens),\r\n            accessToken: Object.keys(inMemoryCache.accessTokens),\r\n            refreshToken: Object.keys(inMemoryCache.refreshTokens)\r\n        };\r\n\r\n        return tokenKeys;\r\n    }\r\n\r\n    /**\r\n     * fetch the account entity\r\n     * @param accountKey - lookup key to fetch cache type AccountEntity\r\n     */\r\n    getAccount(accountKey: string): AccountEntity | null {\r\n        const account = this.getItem(accountKey) as AccountEntity;\r\n        if (AccountEntity.isAccountEntity(account)) {\r\n            return account;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * set account entity\r\n     * @param account - cache value to be set of type AccountEntity\r\n     */\r\n    setAccount(account: AccountEntity): void {\r\n        const accountKey = account.generateAccountKey();\r\n        this.setItem(accountKey, account);\r\n    }\r\n\r\n    /**\r\n     * fetch the idToken credential\r\n     * @param idTokenKey - lookup key to fetch cache type IdTokenEntity\r\n     */\r\n    getIdTokenCredential(idTokenKey: string): IdTokenEntity | null {\r\n        const idToken = this.getItem(idTokenKey) as IdTokenEntity;\r\n        if (IdTokenEntity.isIdTokenEntity(idToken)) {\r\n            return idToken;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * set idToken credential\r\n     * @param idToken - cache value to be set of type IdTokenEntity\r\n     */\r\n    setIdTokenCredential(idToken: IdTokenEntity): void {\r\n        const idTokenKey = idToken.generateCredentialKey();\r\n        this.setItem(idTokenKey, idToken);\r\n    }\r\n\r\n    /**\r\n     * fetch the accessToken credential\r\n     * @param accessTokenKey - lookup key to fetch cache type AccessTokenEntity\r\n     */\r\n    getAccessTokenCredential(accessTokenKey: string): AccessTokenEntity | null {\r\n        const accessToken = this.getItem(accessTokenKey) as AccessTokenEntity;\r\n        if (AccessTokenEntity.isAccessTokenEntity(accessToken)) {\r\n            return accessToken;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * set accessToken credential\r\n     * @param accessToken -  cache value to be set of type AccessTokenEntity\r\n     */\r\n    setAccessTokenCredential(accessToken: AccessTokenEntity): void {\r\n        const accessTokenKey = accessToken.generateCredentialKey();\r\n        this.setItem(accessTokenKey, accessToken);\r\n    }\r\n\r\n    /**\r\n     * fetch the refreshToken credential\r\n     * @param refreshTokenKey - lookup key to fetch cache type RefreshTokenEntity\r\n     */\r\n    getRefreshTokenCredential(refreshTokenKey: string): RefreshTokenEntity | null {\r\n        const refreshToken = this.getItem(refreshTokenKey) as RefreshTokenEntity;\r\n        if (RefreshTokenEntity.isRefreshTokenEntity(refreshToken)) {\r\n            return refreshToken as RefreshTokenEntity;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * set refreshToken credential\r\n     * @param refreshToken - cache value to be set of type RefreshTokenEntity\r\n     */\r\n    setRefreshTokenCredential(refreshToken: RefreshTokenEntity): void {\r\n        const refreshTokenKey = refreshToken.generateCredentialKey();\r\n        this.setItem(refreshTokenKey, refreshToken);\r\n    }\r\n\r\n    /**\r\n     * fetch appMetadata entity from the platform cache\r\n     * @param appMetadataKey - lookup key to fetch cache type AppMetadataEntity\r\n     */\r\n    getAppMetadata(appMetadataKey: string): AppMetadataEntity | null {\r\n        const appMetadata: AppMetadataEntity = this.getItem(appMetadataKey) as AppMetadataEntity;\r\n        if (AppMetadataEntity.isAppMetadataEntity(appMetadataKey, appMetadata)) {\r\n            return appMetadata;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * set appMetadata entity to the platform cache\r\n     * @param appMetadata - cache value to be set of type AppMetadataEntity\r\n     */\r\n    setAppMetadata(appMetadata: AppMetadataEntity): void {\r\n        const appMetadataKey = appMetadata.generateAppMetadataKey();\r\n        this.setItem(appMetadataKey, appMetadata);\r\n    }\r\n\r\n    /**\r\n     * fetch server telemetry entity from the platform cache\r\n     * @param serverTelemetrykey - lookup key to fetch cache type ServerTelemetryEntity\r\n     */\r\n    getServerTelemetry(serverTelemetrykey: string): ServerTelemetryEntity | null {\r\n        const serverTelemetryEntity: ServerTelemetryEntity = this.getItem(serverTelemetrykey) as ServerTelemetryEntity;\r\n        if (serverTelemetryEntity && ServerTelemetryEntity.isServerTelemetryEntity(serverTelemetrykey, serverTelemetryEntity)) {\r\n            return serverTelemetryEntity;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * set server telemetry entity to the platform cache\r\n     * @param serverTelemetryKey - lookup key to fetch cache type ServerTelemetryEntity\r\n     * @param serverTelemetry - cache value to be set of type ServerTelemetryEntity\r\n     */\r\n    setServerTelemetry(serverTelemetryKey: string, serverTelemetry: ServerTelemetryEntity): void {\r\n        this.setItem(serverTelemetryKey, serverTelemetry);\r\n    }\r\n\r\n    /**\r\n     * fetch authority metadata entity from the platform cache\r\n     * @param key - lookup key to fetch cache type AuthorityMetadataEntity\r\n     */\r\n    getAuthorityMetadata(key: string): AuthorityMetadataEntity | null {\r\n        const authorityMetadataEntity: AuthorityMetadataEntity = this.getItem(key) as AuthorityMetadataEntity;\r\n        if (authorityMetadataEntity && AuthorityMetadataEntity.isAuthorityMetadataEntity(key, authorityMetadataEntity)) {\r\n            return authorityMetadataEntity;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Get all authority metadata keys\r\n     */\r\n    getAuthorityMetadataKeys(): Array<string> {\r\n        return this.getKeys().filter((key) => {\r\n            return this.isAuthorityMetadata(key);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * set authority metadata entity to the platform cache\r\n     * @param key - lookup key to fetch cache type AuthorityMetadataEntity\r\n     * @param metadata - cache value to be set of type AuthorityMetadataEntity\r\n     */\r\n    setAuthorityMetadata(key: string, metadata: AuthorityMetadataEntity): void {\r\n        this.setItem(key, metadata);\r\n    }\r\n\r\n    /**\r\n     * fetch throttling entity from the platform cache\r\n     * @param throttlingCacheKey - lookup key to fetch cache type ThrottlingEntity\r\n     */\r\n    getThrottlingCache(throttlingCacheKey: string): ThrottlingEntity | null {\r\n        const throttlingCache: ThrottlingEntity = this.getItem(throttlingCacheKey) as ThrottlingEntity;\r\n        if (throttlingCache && ThrottlingEntity.isThrottlingEntity(throttlingCacheKey, throttlingCache)) {\r\n            return throttlingCache;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * set throttling entity to the platform cache\r\n     * @param throttlingCacheKey - lookup key to fetch cache type ThrottlingEntity\r\n     * @param throttlingCache - cache value to be set of type ThrottlingEntity\r\n     */\r\n    setThrottlingCache(throttlingCacheKey: string, throttlingCache: ThrottlingEntity): void {\r\n        this.setItem(throttlingCacheKey, throttlingCache);\r\n    }\r\n\r\n    /**\r\n     * Removes the cache item from memory with the given key.\r\n     * @param key - lookup key to remove a cache entity\r\n     * @param inMemory - key value map of the cache\r\n     */\r\n    removeItem(key: string): boolean {\r\n        this.logger.tracePii(`Item key: ${key}`);\r\n\r\n        // read inMemoryCache\r\n        let result: boolean = false;\r\n        const cache = this.getCache();\r\n\r\n        if (!!cache[key]) {\r\n            delete cache[key];\r\n            result = true;\r\n        }\r\n\r\n        // write to the cache after removal\r\n        if (result) {\r\n            this.setCache(cache);\r\n            this.emitChange();\r\n        }\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Checks whether key is in cache.\r\n     * @param key - look up key for a cache entity\r\n     */\r\n    containsKey(key: string): boolean {\r\n        return this.getKeys().includes(key);\r\n    }\r\n\r\n    /**\r\n     * Gets all keys in window.\r\n     */\r\n    getKeys(): string[] {\r\n        this.logger.trace(\"Retrieving all cache keys\");\r\n\r\n        // read cache\r\n        const cache = this.getCache();\r\n        return [ ...Object.keys(cache)];\r\n    }\r\n\r\n    /**\r\n     * Clears all cache entries created by MSAL (except tokens).\r\n     */\r\n    async clear(): Promise<void> {\r\n        this.logger.trace(\"Clearing cache entries created by MSAL\");\r\n\r\n        // read inMemoryCache\r\n        const cacheKeys = this.getKeys();\r\n\r\n        // delete each element\r\n        cacheKeys.forEach(key => {\r\n            this.removeItem(key);\r\n        });\r\n        this.emitChange();\r\n    }\r\n\r\n    /**\r\n     * Initialize in memory cache from an exisiting cache vault\r\n     * @param cache - blob formatted cache (JSON)\r\n     */\r\n    static generateInMemoryCache(cache: string): InMemoryCache {\r\n        return Deserializer.deserializeAllCache(\r\n            Deserializer.deserializeJSONBlob(cache)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * retrieves the final JSON\r\n     * @param inMemoryCache - itemised cache read from the JSON\r\n     */\r\n    static generateJsonCache(inMemoryCache: InMemoryCache): JsonCache {\r\n        return Serializer.serializeAllCache(inMemoryCache);\r\n    }\r\n\r\n    /**\r\n     * Updates a credential's cache key if the current cache key is outdated\r\n     */\r\n    updateCredentialCacheKey(currentCacheKey: string, credential: ValidCredentialType): string {\r\n        const updatedCacheKey = credential.generateCredentialKey();\r\n\r\n        if (currentCacheKey !== updatedCacheKey) {\r\n            const cacheItem = this.getItem(currentCacheKey);\r\n            if (cacheItem) {\r\n                this.removeItem(currentCacheKey);\r\n                this.setItem(updatedCacheKey, cacheItem);\r\n                this.logger.verbose(`Updated an outdated ${credential.credentialType} cache key`);\r\n                return updatedCacheKey;\r\n            } else {\r\n                this.logger.error(`Attempted to update an outdated ${credential.credentialType} cache key but no item matching the outdated key was found in storage`);\r\n            }\r\n        }\r\n\r\n        return currentCacheKey;\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { NodeStorage } from \"./NodeStorage\";\r\nimport { StringUtils, AccountEntity, AccountInfo, Logger, ISerializableTokenCache, ICachePlugin, TokenCacheContext } from \"@azure/msal-common\";\r\nimport { InMemoryCache, JsonCache, SerializedAccountEntity, SerializedAccessTokenEntity, SerializedRefreshTokenEntity, SerializedIdTokenEntity, SerializedAppMetadataEntity, CacheKVStore } from \"./serializer/SerializerTypes\";\r\nimport { Deserializer } from \"./serializer/Deserializer\";\r\nimport { Serializer } from \"./serializer/Serializer\";\r\nimport { ITokenCache } from \"./ITokenCache\";\r\n\r\nconst defaultSerializedCache: JsonCache = {\r\n    Account: {},\r\n    IdToken: {},\r\n    AccessToken: {},\r\n    RefreshToken: {},\r\n    AppMetadata: {},\r\n};\r\n\r\n/**\r\n * In-memory token cache manager\r\n * @public\r\n */\r\nexport class TokenCache implements ISerializableTokenCache, ITokenCache {\r\n\r\n    private storage: NodeStorage;\r\n    private cacheHasChanged: boolean;\r\n    private cacheSnapshot: string;\r\n    private readonly persistence: ICachePlugin;\r\n    private logger: Logger;\r\n\r\n    constructor(storage: NodeStorage, logger: Logger, cachePlugin?: ICachePlugin) {\r\n        this.cacheHasChanged = false;\r\n        this.storage = storage;\r\n        this.storage.registerChangeEmitter(this.handleChangeEvent.bind(this));\r\n        if (cachePlugin) {\r\n            this.persistence = cachePlugin;\r\n        }\r\n        this.logger = logger;\r\n    }\r\n\r\n    /**\r\n     * Set to true if cache state has changed since last time serialize or writeToPersistence was called\r\n     */\r\n    hasChanged(): boolean {\r\n        return this.cacheHasChanged;\r\n    }\r\n\r\n    /**\r\n     * Serializes in memory cache to JSON\r\n     */\r\n    serialize(): string {\r\n        this.logger.trace(\"Serializing in-memory cache\");\r\n        let finalState = Serializer.serializeAllCache(\r\n            this.storage.getInMemoryCache() as InMemoryCache\r\n        );\r\n\r\n        // if cacheSnapshot not null or empty, merge\r\n        if (!StringUtils.isEmpty(this.cacheSnapshot)) {\r\n            this.logger.trace(\"Reading cache snapshot from disk\");\r\n            finalState = this.mergeState(\r\n                JSON.parse(this.cacheSnapshot),\r\n                finalState\r\n            );\r\n        } else {\r\n            this.logger.trace(\"No cache snapshot to merge\");\r\n        }\r\n        this.cacheHasChanged = false;\r\n\r\n        return JSON.stringify(finalState);\r\n    }\r\n\r\n    /**\r\n     * Deserializes JSON to in-memory cache. JSON should be in MSAL cache schema format\r\n     * @param cache - blob formatted cache\r\n     */\r\n    deserialize(cache: string): void {\r\n        this.logger.trace(\"Deserializing JSON to in-memory cache\");\r\n        this.cacheSnapshot = cache;\r\n\r\n        if (!StringUtils.isEmpty(this.cacheSnapshot)) {\r\n            this.logger.trace(\"Reading cache snapshot from disk\");\r\n            const deserializedCache = Deserializer.deserializeAllCache(\r\n                this.overlayDefaults(JSON.parse(this.cacheSnapshot))\r\n            );\r\n            this.storage.setInMemoryCache(deserializedCache);\r\n        } else {\r\n            this.logger.trace(\"No cache snapshot to deserialize\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Fetches the cache key-value map\r\n     */\r\n    getKVStore(): CacheKVStore {\r\n        return this.storage.getCache();\r\n    }\r\n\r\n    /**\r\n     * API that retrieves all accounts currently in cache to the user\r\n     */\r\n    async getAllAccounts(): Promise<AccountInfo[]> {\r\n\r\n        this.logger.trace(\"getAllAccounts called\");\r\n        let cacheContext;\r\n        try {\r\n            if (this.persistence) {\r\n                cacheContext = new TokenCacheContext(this, false);\r\n                await this.persistence.beforeCacheAccess(cacheContext);\r\n            }\r\n            return this.storage.getAllAccounts();\r\n        } finally {\r\n            if (this.persistence && cacheContext) {\r\n                await this.persistence.afterCacheAccess(cacheContext);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the signed in account matching homeAccountId.\r\n     * (the account object is created at the time of successful login)\r\n     * or null when no matching account is found\r\n     * @param homeAccountId - unique identifier for an account (uid.utid)\r\n     */\r\n    async getAccountByHomeId(homeAccountId: string): Promise<AccountInfo | null> {\r\n        const allAccounts = await this.getAllAccounts();\r\n        if (!StringUtils.isEmpty(homeAccountId) && allAccounts && allAccounts.length) {\r\n            return allAccounts.filter(accountObj => accountObj.homeAccountId === homeAccountId)[0] || null;\r\n        } else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the signed in account matching localAccountId.\r\n     * (the account object is created at the time of successful login)\r\n     * or null when no matching account is found\r\n     * @param localAccountId - unique identifier of an account (sub/obj when homeAccountId cannot be populated)\r\n     */\r\n    async getAccountByLocalId(localAccountId: string): Promise<AccountInfo | null> {\r\n        const allAccounts = await this.getAllAccounts();\r\n        if (!StringUtils.isEmpty(localAccountId) && allAccounts && allAccounts.length) {\r\n            return allAccounts.filter(accountObj => accountObj.localAccountId === localAccountId)[0] || null;\r\n        } else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * API to remove a specific account and the relevant data from cache\r\n     * @param account - AccountInfo passed by the user\r\n     */\r\n    async removeAccount(account: AccountInfo): Promise<void> {\r\n        this.logger.trace(\"removeAccount called\");\r\n        let cacheContext;\r\n        try {\r\n            if (this.persistence) {\r\n                cacheContext = new TokenCacheContext(this, true);\r\n                await this.persistence.beforeCacheAccess(cacheContext);\r\n            }\r\n            await this.storage.removeAccount(AccountEntity.generateAccountCacheKey(account));\r\n        } finally {\r\n            if (this.persistence && cacheContext) {\r\n                await this.persistence.afterCacheAccess(cacheContext);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Called when the cache has changed state.\r\n     */\r\n    private handleChangeEvent() {\r\n        this.cacheHasChanged = true;\r\n    }\r\n\r\n    /**\r\n     * Merge in memory cache with the cache snapshot.\r\n     * @param oldState - cache before changes\r\n     * @param currentState - current cache state in the library\r\n     */\r\n    private mergeState(oldState: JsonCache, currentState: JsonCache): JsonCache {\r\n        this.logger.trace(\"Merging in-memory cache with cache snapshot\");\r\n        const stateAfterRemoval = this.mergeRemovals(oldState, currentState);\r\n        return this.mergeUpdates(stateAfterRemoval, currentState);\r\n    }\r\n\r\n    /**\r\n     * Deep update of oldState based on newState values\r\n     * @param oldState - cache before changes\r\n     * @param newState - updated cache\r\n     */\r\n    private mergeUpdates(oldState: object, newState: object): JsonCache {\r\n        Object.keys(newState).forEach((newKey: string) => {\r\n            const newValue = newState[newKey];\r\n\r\n            // if oldState does not contain value but newValue does, add it\r\n            if (!oldState.hasOwnProperty(newKey)) {\r\n                if (newValue !== null) {\r\n                    oldState[newKey] = newValue;\r\n                }\r\n            } else {\r\n                // both oldState and newState contain the key, do deep update\r\n                const newValueNotNull = newValue !== null;\r\n                const newValueIsObject = typeof newValue === \"object\";\r\n                const newValueIsNotArray = !Array.isArray(newValue);\r\n                const oldStateNotUndefinedOrNull = typeof oldState[newKey] !== \"undefined\" && oldState[newKey] !== null;\r\n\r\n                if (newValueNotNull && newValueIsObject && newValueIsNotArray && oldStateNotUndefinedOrNull) {\r\n                    this.mergeUpdates(oldState[newKey], newValue);\r\n                } else {\r\n                    oldState[newKey] = newValue;\r\n                }\r\n            }\r\n        });\r\n\r\n        return oldState as JsonCache;\r\n    }\r\n\r\n    /**\r\n     * Removes entities in oldState that the were removed from newState. If there are any unknown values in root of\r\n     * oldState that are not recognized, they are left untouched.\r\n     * @param oldState - cache before changes\r\n     * @param newState - updated cache\r\n     */\r\n    private mergeRemovals(oldState: JsonCache, newState: JsonCache): JsonCache {\r\n        this.logger.trace(\"Remove updated entries in cache\");\r\n        const accounts = oldState.Account ? this.mergeRemovalsDict<SerializedAccountEntity>(oldState.Account, newState.Account) : oldState.Account;\r\n        const accessTokens = oldState.AccessToken ? this.mergeRemovalsDict<SerializedAccessTokenEntity>(oldState.AccessToken, newState.AccessToken) : oldState.AccessToken;\r\n        const refreshTokens = oldState.RefreshToken ? this.mergeRemovalsDict<SerializedRefreshTokenEntity>(oldState.RefreshToken, newState.RefreshToken) : oldState.RefreshToken;\r\n        const idTokens = oldState.IdToken ? this.mergeRemovalsDict<SerializedIdTokenEntity>(oldState.IdToken, newState.IdToken) : oldState.IdToken;\r\n        const appMetadata = oldState.AppMetadata ? this.mergeRemovalsDict<SerializedAppMetadataEntity>(oldState.AppMetadata, newState.AppMetadata) : oldState.AppMetadata;\r\n\r\n        return {\r\n            ...oldState,\r\n            Account: accounts,\r\n            AccessToken: accessTokens,\r\n            RefreshToken: refreshTokens,\r\n            IdToken: idTokens,\r\n            AppMetadata: appMetadata\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Helper to merge new cache with the old one\r\n     * @param oldState - cache before changes\r\n     * @param newState - updated cache\r\n     */\r\n    private mergeRemovalsDict<T>(oldState: Record<string, T>, newState?: Record<string, T>): Record<string, T> {\r\n        const finalState = { ...oldState };\r\n        Object.keys(oldState).forEach((oldKey) => {\r\n            if (!newState || !(newState.hasOwnProperty(oldKey))) {\r\n                delete finalState[oldKey];\r\n            }\r\n        });\r\n        return finalState;\r\n    }\r\n\r\n    /**\r\n     * Helper to overlay as a part of cache merge\r\n     * @param passedInCache - cache read from the blob\r\n     */\r\n    private overlayDefaults(passedInCache: JsonCache): JsonCache {\r\n        this.logger.trace(\"Overlaying input cache with the default cache\");\r\n        return {\r\n            Account: {\r\n                ...defaultSerializedCache.Account,\r\n                ...passedInCache.Account,\r\n            },\r\n            IdToken: {\r\n                ...defaultSerializedCache.IdToken,\r\n                ...passedInCache.IdToken,\r\n            },\r\n            AccessToken: {\r\n                ...defaultSerializedCache.AccessToken,\r\n                ...passedInCache.AccessToken,\r\n            },\r\n            RefreshToken: {\r\n                ...defaultSerializedCache.RefreshToken,\r\n                ...passedInCache.RefreshToken,\r\n            },\r\n            AppMetadata: {\r\n                ...defaultSerializedCache.AppMetadata,\r\n                ...passedInCache.AppMetadata,\r\n            },\r\n        };\r\n    }\r\n}\r\n", "/* eslint-disable header/header */\r\nexport const name = \"@azure/msal-node\";\r\nexport const version = \"1.18.4\";\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthError } from \"@azure/msal-common\";\r\n\r\n/**\r\n * NodeAuthErrorMessage class containing string constants used by error codes and messages.\r\n */\r\nexport const NodeAuthErrorMessage = {\r\n    invalidLoopbackAddressType: {\r\n        code: \"invalid_loopback_server_address_type\",\r\n        desc: \"Loopback server address is not type string. This is unexpected.\"\r\n    },\r\n    unableToLoadRedirectUri: {\r\n        code: \"unable_to_load_redirectUrl\",\r\n        desc: \"Loopback server callback was invoked without a url. This is unexpected.\"\r\n    },\r\n    noAuthCodeInResponse: {\r\n        code: \"no_auth_code_in_response\",\r\n        desc: \"No auth code found in the server response. Please check your network trace to determine what happened.\"\r\n    },\r\n    noLoopbackServerExists: {\r\n        code: \"no_loopback_server_exists\",\r\n        desc: \"No loopback server exists yet.\"\r\n    },\r\n    loopbackServerAlreadyExists: {\r\n        code: \"loopback_server_already_exists\",\r\n        desc: \"Loopback server already exists. Cannot create another.\"\r\n    },\r\n    loopbackServerTimeout: {\r\n        code: \"loopback_server_timeout\",\r\n        desc: \"Timed out waiting for auth code listener to be registered.\"\r\n    },\r\n    stateNotFoundError: {\r\n        code: \"state_not_found\",\r\n        desc: \"State not found. Please verify that the request originated from msal.\"\r\n    },\r\n};\r\n\r\nexport class NodeAuthError extends AuthError {\r\n    constructor(errorCode: string, errorMessage?: string) {\r\n        super(errorCode, errorMessage);\r\n        this.name = \"NodeAuthError\";\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if loopback server address is of type string.\r\n     */\r\n    static createInvalidLoopbackAddressTypeError(): NodeAuthError {\r\n        return new NodeAuthError(NodeAuthErrorMessage.invalidLoopbackAddressType.code,\r\n            `${NodeAuthErrorMessage.invalidLoopbackAddressType.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if the loopback server is unable to get a url.\r\n     */\r\n    static createUnableToLoadRedirectUrlError(): NodeAuthError {\r\n        return new NodeAuthError(NodeAuthErrorMessage.unableToLoadRedirectUri.code,\r\n            `${NodeAuthErrorMessage.unableToLoadRedirectUri.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if the server response does not contain an auth code.\r\n     */\r\n    static createNoAuthCodeInResponseError(): NodeAuthError {\r\n        return new NodeAuthError(NodeAuthErrorMessage.noAuthCodeInResponse.code,\r\n            `${NodeAuthErrorMessage.noAuthCodeInResponse.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if the loopback server has not been spun up yet.\r\n     */\r\n    static createNoLoopbackServerExistsError(): NodeAuthError {\r\n        return new NodeAuthError(NodeAuthErrorMessage.noLoopbackServerExists.code,\r\n            `${NodeAuthErrorMessage.noLoopbackServerExists.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if a loopback server already exists when attempting to create another one.\r\n     */\r\n    static createLoopbackServerAlreadyExistsError(): NodeAuthError {\r\n        return new NodeAuthError(NodeAuthErrorMessage.loopbackServerAlreadyExists.code,\r\n            `${NodeAuthErrorMessage.loopbackServerAlreadyExists.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown if the loopback server times out registering the auth code listener.\r\n     */\r\n    static createLoopbackServerTimeoutError(): NodeAuthError {\r\n        return new NodeAuthError(NodeAuthErrorMessage.loopbackServerTimeout.code,\r\n            `${NodeAuthErrorMessage.loopbackServerTimeout.desc}`);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when the state is not present.\r\n     */\r\n    static createStateNotFoundError(): NodeAuthError {\r\n        return new NodeAuthError(NodeAuthErrorMessage.stateNotFoundError.code, NodeAuthErrorMessage.stateNotFoundError.desc);\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport {\r\n    AuthorizationCodeClient,\r\n    ClientConfiguration,\r\n    RefreshTokenClient,\r\n    AuthenticationResult,\r\n    Authority,\r\n    AuthorityFactory,\r\n    BaseAuthRequest,\r\n    SilentFlowClient,\r\n    Logger,\r\n    ServerTelemetryManager,\r\n    ServerTelemetryRequest,\r\n    CommonSilentFlowRequest,\r\n    CommonRefreshTokenRequest,\r\n    CommonAuthorizationCodeRequest,\r\n    CommonAuthorizationUrlRequest,\r\n    CommonUsernamePasswordRequest,\r\n    UsernamePasswordClient,\r\n    AuthenticationScheme,\r\n    ResponseMode,\r\n    AuthorityOptions,\r\n    OIDC_DEFAULT_SCOPES,\r\n    AzureRegionConfiguration,\r\n    AuthError,\r\n    AzureCloudOptions,\r\n    AuthorizationCodePayload,\r\n    StringUtils,\r\n    ClientAuthError,\r\n    Constants, } from \"@azure/msal-common\";\r\nimport { Configuration, buildAppConfiguration, NodeConfiguration } from \"../config/Configuration\";\r\nimport { CryptoProvider } from \"../crypto/CryptoProvider\";\r\nimport { NodeStorage } from \"../cache/NodeStorage\";\r\nimport { Constants as NodeConstants, ApiId } from \"../utils/Constants\";\r\nimport { TokenCache } from \"../cache/TokenCache\";\r\nimport { ClientAssertion } from \"./ClientAssertion\";\r\nimport { AuthorizationUrlRequest } from \"../request/AuthorizationUrlRequest\";\r\nimport { AuthorizationCodeRequest } from \"../request/AuthorizationCodeRequest\";\r\nimport { RefreshTokenRequest } from \"../request/RefreshTokenRequest\";\r\nimport { SilentFlowRequest } from \"../request/SilentFlowRequest\";\r\nimport { version, name } from \"../packageMetadata\";\r\nimport { UsernamePasswordRequest } from \"../request/UsernamePasswordRequest\";\r\nimport { NodeAuthError } from \"../error/NodeAuthError\";\r\n\r\n/**\r\n * Base abstract class for all ClientApplications - public and confidential\r\n * @public\r\n */\r\nexport abstract class ClientApplication {\r\n\r\n    protected readonly cryptoProvider: CryptoProvider;\r\n    private tokenCache: TokenCache;\r\n\r\n    /**\r\n     * Platform storage object\r\n     */\r\n    protected storage: NodeStorage;\r\n    /**\r\n     * Logger object to log the application flow\r\n     */\r\n    protected logger: Logger;\r\n    /**\r\n     * Platform configuration initialized by the application\r\n     */\r\n    protected config: NodeConfiguration;\r\n    /**\r\n     * Client assertion passed by the user for confidential client flows\r\n     */\r\n    protected clientAssertion: ClientAssertion;\r\n    /**\r\n     * Client secret passed by the user for confidential client flows\r\n     */\r\n    protected clientSecret: string;\r\n\r\n    /**\r\n     * Constructor for the ClientApplication\r\n     */\r\n    protected constructor(configuration: Configuration) {\r\n        this.config = buildAppConfiguration(configuration);\r\n        this.cryptoProvider = new CryptoProvider();\r\n        this.logger = new Logger(this.config.system.loggerOptions, name, version);\r\n        this.storage = new NodeStorage(this.logger, this.config.auth.clientId, this.cryptoProvider);\r\n        this.tokenCache = new TokenCache(\r\n            this.storage,\r\n            this.logger,\r\n            this.config.cache.cachePlugin\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Creates the URL of the authorization request, letting the user input credentials and consent to the\r\n     * application. The URL targets the /authorize endpoint of the authority configured in the\r\n     * application object.\r\n     *\r\n     * Once the user inputs their credentials and consents, the authority will send a response to the redirect URI\r\n     * sent in the request and should contain an authorization code, which can then be used to acquire tokens via\r\n     * `acquireTokenByCode(AuthorizationCodeRequest)`.\r\n     */\r\n    async getAuthCodeUrl(request: AuthorizationUrlRequest): Promise<string> {\r\n        this.logger.info(\"getAuthCodeUrl called\", request.correlationId);\r\n        const validRequest: CommonAuthorizationUrlRequest = {\r\n            ...request,\r\n            ... await this.initializeBaseRequest(request),\r\n            responseMode: request.responseMode || ResponseMode.QUERY,\r\n            authenticationScheme: AuthenticationScheme.BEARER\r\n        };\r\n\r\n        const authClientConfig = await this.buildOauthClientConfiguration(\r\n            validRequest.authority,\r\n            validRequest.correlationId,\r\n            undefined,\r\n            undefined,\r\n            request.azureCloudOptions\r\n        );\r\n        const authorizationCodeClient = new AuthorizationCodeClient(\r\n            authClientConfig\r\n        );\r\n        this.logger.verbose(\"Auth code client created\", validRequest.correlationId);\r\n        return authorizationCodeClient.getAuthCodeUrl(validRequest);\r\n    }\r\n\r\n    /**\r\n     * Acquires a token by exchanging the Authorization Code received from the first step of OAuth2.0\r\n     * Authorization Code flow.\r\n     *\r\n     * `getAuthCodeUrl(AuthorizationCodeUrlRequest)` can be used to create the URL for the first step of OAuth2.0\r\n     * Authorization Code flow. Ensure that values for redirectUri and scopes in AuthorizationCodeUrlRequest and\r\n     * AuthorizationCodeRequest are the same.\r\n     */\r\n    async acquireTokenByCode(request: AuthorizationCodeRequest, authCodePayLoad?: AuthorizationCodePayload): Promise<AuthenticationResult> {\r\n        this.logger.info(\"acquireTokenByCode called\");\r\n        if (request.state && authCodePayLoad){\r\n            this.logger.info(\"acquireTokenByCode - validating state\");\r\n            this.validateState(request.state, authCodePayLoad.state || \"\");\r\n            // eslint-disable-next-line no-param-reassign\r\n            authCodePayLoad= {...authCodePayLoad, state: \"\"};\r\n        }\r\n        const validRequest: CommonAuthorizationCodeRequest = {\r\n            ...request,\r\n            ... await this.initializeBaseRequest(request),\r\n            authenticationScheme: AuthenticationScheme.BEARER\r\n        };\r\n\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenByCode, validRequest.correlationId);\r\n        try {\r\n            const authClientConfig = await this.buildOauthClientConfiguration(\r\n                validRequest.authority,\r\n                validRequest.correlationId,\r\n                serverTelemetryManager,\r\n                undefined,\r\n                request.azureCloudOptions\r\n            );\r\n            const authorizationCodeClient = new AuthorizationCodeClient(\r\n                authClientConfig\r\n            );\r\n            this.logger.verbose(\"Auth code client created\", validRequest.correlationId);\r\n            return authorizationCodeClient.acquireToken(validRequest, authCodePayLoad);\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                e.setCorrelationId(validRequest.correlationId);\r\n            }\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Acquires a token by exchanging the refresh token provided for a new set of tokens.\r\n     *\r\n     * This API is provided only for scenarios where you would like to migrate from ADAL to MSAL. Otherwise, it is\r\n     * recommended that you use `acquireTokenSilent()` for silent scenarios. When using `acquireTokenSilent()`, MSAL will\r\n     * handle the caching and refreshing of tokens automatically.\r\n     */\r\n    async acquireTokenByRefreshToken(request: RefreshTokenRequest): Promise<AuthenticationResult | null> {\r\n        this.logger.info(\"acquireTokenByRefreshToken called\", request.correlationId);\r\n        const validRequest: CommonRefreshTokenRequest = {\r\n            ...request,\r\n            ... await this.initializeBaseRequest(request),\r\n            authenticationScheme: AuthenticationScheme.BEARER\r\n        };\r\n\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenByRefreshToken, validRequest.correlationId);\r\n        try {\r\n            const refreshTokenClientConfig = await this.buildOauthClientConfiguration(\r\n                validRequest.authority,\r\n                validRequest.correlationId,\r\n                serverTelemetryManager,\r\n                undefined,\r\n                request.azureCloudOptions\r\n            );\r\n            const refreshTokenClient = new RefreshTokenClient(\r\n                refreshTokenClientConfig\r\n            );\r\n            this.logger.verbose(\"Refresh token client created\", validRequest.correlationId);\r\n            return refreshTokenClient.acquireToken(validRequest);\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                e.setCorrelationId(validRequest.correlationId);\r\n            }\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Acquires a token silently when a user specifies the account the token is requested for.\r\n     *\r\n     * This API expects the user to provide an account object and looks into the cache to retrieve the token if present.\r\n     * There is also an optional \"forceRefresh\" boolean the user can send to bypass the cache for access_token and id_token.\r\n     * In case the refresh_token is expired or not found, an error is thrown\r\n     * and the guidance is for the user to call any interactive token acquisition API (eg: `acquireTokenByCode()`).\r\n     */\r\n    async acquireTokenSilent(request: SilentFlowRequest): Promise<AuthenticationResult | null> {\r\n        const validRequest: CommonSilentFlowRequest = {\r\n            ...request,\r\n            ... await this.initializeBaseRequest(request),\r\n            forceRefresh: request.forceRefresh || false\r\n        };\r\n\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenSilent, validRequest.correlationId, validRequest.forceRefresh);\r\n        try {\r\n            const silentFlowClientConfig = await this.buildOauthClientConfiguration(\r\n                validRequest.authority,\r\n                validRequest.correlationId,\r\n                serverTelemetryManager,\r\n                undefined,\r\n                request.azureCloudOptions\r\n            );\r\n            const silentFlowClient = new SilentFlowClient(\r\n                silentFlowClientConfig\r\n            );\r\n            this.logger.verbose(\"Silent flow client created\", validRequest.correlationId);\r\n            return silentFlowClient.acquireToken(validRequest);\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                e.setCorrelationId(validRequest.correlationId);\r\n            }\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Acquires tokens with password grant by exchanging client applications username and password for credentials\r\n     *\r\n     * The latest OAuth 2.0 Security Best Current Practice disallows the password grant entirely.\r\n     * More details on this recommendation at https://tools.ietf.org/html/draft-ietf-oauth-security-topics-13#section-3.4\r\n     * Microsoft's documentation and recommendations are at:\r\n     * https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-authentication-flows#usernamepassword\r\n     *\r\n     * @param request - UsenamePasswordRequest\r\n     */\r\n    async acquireTokenByUsernamePassword(request: UsernamePasswordRequest): Promise<AuthenticationResult | null> {\r\n        this.logger.info(\"acquireTokenByUsernamePassword called\", request.correlationId);\r\n        const validRequest: CommonUsernamePasswordRequest = {\r\n            ...request,\r\n            ... await this.initializeBaseRequest(request)\r\n        };\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenByUsernamePassword, validRequest.correlationId);\r\n        try {\r\n            const usernamePasswordClientConfig = await this.buildOauthClientConfiguration(\r\n                validRequest.authority,\r\n                validRequest.correlationId,\r\n                serverTelemetryManager,\r\n                undefined,\r\n                request.azureCloudOptions\r\n            );\r\n            const usernamePasswordClient = new UsernamePasswordClient(usernamePasswordClientConfig);\r\n            this.logger.verbose(\"Username password client created\", validRequest.correlationId);\r\n            return usernamePasswordClient.acquireToken(validRequest);\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                e.setCorrelationId(validRequest.correlationId);\r\n            }\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the token cache for the application.\r\n     */\r\n    getTokenCache(): TokenCache {\r\n        this.logger.info(\"getTokenCache called\");\r\n        return this.tokenCache;\r\n    }\r\n\r\n    /**\r\n     * Validates OIDC state by comparing the user cached state with the state received from the server.\r\n     * \r\n     * This API is provided for scenarios where you would use OAuth2.0 state parameter to mitigate against\r\n     * CSRF attacks.\r\n     * For more information about state, visit https://datatracker.ietf.org/doc/html/rfc6819#section-3.6.\r\n     * @param state\r\n     * @param cachedState\r\n     */\r\n    protected validateState(state: string, cachedState: string): void {\r\n        if(!state) {\r\n            throw NodeAuthError.createStateNotFoundError();\r\n        }\r\n\r\n        if(state !== cachedState) {\r\n            throw ClientAuthError.createStateMismatchError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the logger instance\r\n     */\r\n    getLogger(): Logger {\r\n        return this.logger;\r\n    }\r\n\r\n    /**\r\n     * Replaces the default logger set in configurations with new Logger with new configurations\r\n     * @param logger - Logger instance\r\n     */\r\n    setLogger(logger: Logger): void {\r\n        this.logger = logger;\r\n    }\r\n\r\n    /**\r\n     * Builds the common configuration to be passed to the common component based on the platform configurarion\r\n     * @param authority - user passed authority in configuration\r\n     * @param serverTelemetryManager - initializes servertelemetry if passed\r\n     */\r\n    protected async buildOauthClientConfiguration(\r\n        authority: string,\r\n        requestCorrelationId?: string, \r\n        serverTelemetryManager?: ServerTelemetryManager,\r\n        azureRegionConfiguration?: AzureRegionConfiguration, \r\n        azureCloudOptions?: AzureCloudOptions): Promise<ClientConfiguration> {\r\n        \r\n        this.logger.verbose(\"buildOauthClientConfiguration called\", requestCorrelationId);\r\n\r\n        // precedence - azureCloudInstance + tenant >> authority and request  >> config\r\n        const userAzureCloudOptions = azureCloudOptions ? azureCloudOptions : this.config.auth.azureCloudOptions;\r\n\r\n        // using null assertion operator as we ensure that all config values have default values in buildConfiguration()\r\n        this.logger.verbose(`building oauth client configuration with the authority: ${authority}`, requestCorrelationId);\r\n        const discoveredAuthority = await this.createAuthority(authority, azureRegionConfiguration, requestCorrelationId, userAzureCloudOptions);\r\n\r\n        serverTelemetryManager?.updateRegionDiscoveryMetadata(discoveredAuthority.regionDiscoveryMetadata);\r\n\r\n        const clientConfiguration: ClientConfiguration = {\r\n            authOptions: {\r\n                clientId: this.config.auth.clientId,\r\n                authority: discoveredAuthority,\r\n                clientCapabilities: this.config.auth.clientCapabilities\r\n            },\r\n            loggerOptions: {\r\n                logLevel: this.config.system.loggerOptions.logLevel ,\r\n                loggerCallback: this.config.system.loggerOptions.loggerCallback ,\r\n                piiLoggingEnabled: this.config.system.loggerOptions.piiLoggingEnabled ,\r\n                correlationId: requestCorrelationId\r\n            },\r\n            cacheOptions: {\r\n                claimsBasedCachingEnabled: this.config.cache.claimsBasedCachingEnabled,\r\n            },\r\n            cryptoInterface: this.cryptoProvider,\r\n            networkInterface: this.config.system.networkClient,\r\n            storageInterface: this.storage,\r\n            serverTelemetryManager: serverTelemetryManager,\r\n            clientCredentials: {\r\n                clientSecret: this.clientSecret,\r\n                clientAssertion: this.clientAssertion ? this.getClientAssertion(discoveredAuthority) : undefined,\r\n            },\r\n            libraryInfo: {\r\n                sku: NodeConstants.MSAL_SKU,\r\n                version: version,\r\n                cpu: process.arch || Constants.EMPTY_STRING,\r\n                os: process.platform || Constants.EMPTY_STRING,\r\n            },\r\n            telemetry: this.config.telemetry,\r\n            persistencePlugin: this.config.cache.cachePlugin,\r\n            serializableCache: this.tokenCache            \r\n        };\r\n\r\n        return clientConfiguration;\r\n    }\r\n\r\n    private getClientAssertion(authority: Authority): { assertion: string, assertionType: string } {\r\n        return {\r\n            assertion: this.clientAssertion.getJwt(this.cryptoProvider, this.config.auth.clientId, authority.tokenEndpoint),\r\n            assertionType: NodeConstants.JWT_BEARER_ASSERTION_TYPE\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Generates a request with the default scopes & generates a correlationId.\r\n     * @param authRequest - BaseAuthRequest for initialization\r\n     */\r\n    protected async initializeBaseRequest(authRequest: Partial<BaseAuthRequest>): Promise<BaseAuthRequest> {\r\n        this.logger.verbose(\"initializeRequestScopes called\", authRequest.correlationId);\r\n        // Default authenticationScheme to Bearer, log that POP isn't supported yet\r\n        if (authRequest.authenticationScheme && authRequest.authenticationScheme === AuthenticationScheme.POP) {\r\n            this.logger.verbose(\"Authentication Scheme 'pop' is not supported yet, setting Authentication Scheme to 'Bearer' for request\", authRequest.correlationId);\r\n        }\r\n\r\n        authRequest.authenticationScheme = AuthenticationScheme.BEARER;\r\n\r\n        // Set requested claims hash if claims-based caching is enabled and claims were requested\r\n        if (this.config.cache.claimsBasedCachingEnabled &&\r\n            authRequest.claims &&\r\n            // Checks for empty stringified object \"{}\" which doesn't qualify as requested claims\r\n            !StringUtils.isEmptyObj(authRequest.claims)) {\r\n            authRequest.requestedClaimsHash = await this.cryptoProvider.hashString(authRequest.claims);\r\n        }\r\n\r\n        return {\r\n            ...authRequest,\r\n            scopes: [...((authRequest && authRequest.scopes) || []), ...OIDC_DEFAULT_SCOPES],\r\n            correlationId: authRequest && authRequest.correlationId || this.cryptoProvider.createNewGuid(),\r\n            authority: authRequest.authority || this.config.auth.authority\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Initializes the server telemetry payload\r\n     * @param apiId - Id for a specific request\r\n     * @param correlationId - GUID\r\n     * @param forceRefresh - boolean to indicate network call\r\n     */\r\n    protected initializeServerTelemetryManager(apiId: number, correlationId: string, forceRefresh?: boolean): ServerTelemetryManager {\r\n        const telemetryPayload: ServerTelemetryRequest = {\r\n            clientId: this.config.auth.clientId,\r\n            correlationId: correlationId,\r\n            apiId: apiId,\r\n            forceRefresh: forceRefresh || false\r\n        };\r\n\r\n        return new ServerTelemetryManager(telemetryPayload, this.storage);\r\n    }\r\n\r\n    /**\r\n     * Create authority instance. If authority not passed in request, default to authority set on the application\r\n     * object. If no authority set in application object, then default to common authority.\r\n     * @param authorityString - authority from user configuration\r\n     */\r\n    private async createAuthority(authorityString: string, azureRegionConfiguration?: AzureRegionConfiguration, requestCorrelationId?: string, azureCloudOptions?: AzureCloudOptions): Promise<Authority> {\r\n        this.logger.verbose(\"createAuthority called\", requestCorrelationId);\r\n\r\n        // build authority string based on auth params - azureCloudInstance is prioritized if provided\r\n        const authorityUrl = Authority.generateAuthority(authorityString, azureCloudOptions);\r\n\r\n        const authorityOptions: AuthorityOptions = {\r\n            protocolMode: this.config.auth.protocolMode,\r\n            knownAuthorities: this.config.auth.knownAuthorities,\r\n            cloudDiscoveryMetadata: this.config.auth.cloudDiscoveryMetadata,\r\n            authorityMetadata: this.config.auth.authorityMetadata,\r\n            azureRegionConfiguration,\r\n            skipAuthorityMetadataCache: this.config.auth.skipAuthorityMetadataCache,\r\n        };\r\n\r\n        return await AuthorityFactory.createDiscoveredInstance(authorityUrl, this.config.system.networkClient, this.storage, authorityOptions, this.logger);\r\n    }\r\n\r\n    /**\r\n     * Clear the cache\r\n     */\r\n    clearCache(): void {\r\n        this.storage.clear();\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { Constants as CommonConstants, ServerAuthorizationCodeResponse, UrlString } from \"@azure/msal-common\";\r\nimport { createServer, IncomingMessage, Server, ServerResponse } from \"http\";\r\nimport { NodeAuthError } from \"../error/NodeAuthError\";\r\nimport { Constants, HttpStatus, LOOPBACK_SERVER_CONSTANTS } from \"../utils/Constants\";\r\nimport { ILoopbackClient } from \"./ILoopbackClient\";\r\n\r\nexport class LoopbackClient implements ILoopbackClient {\r\n    private server: Server;\r\n\r\n    /**\r\n     * Spins up a loopback server which returns the server response when the localhost redirectUri is hit\r\n     * @param successTemplate\r\n     * @param errorTemplate\r\n     * @returns\r\n     */\r\n    async listenForAuthCode(successTemplate?: string, errorTemplate?: string): Promise<ServerAuthorizationCodeResponse> {\r\n        if (!!this.server) {\r\n            throw NodeAuthError.createLoopbackServerAlreadyExistsError();\r\n        }\r\n\r\n        const authCodeListener = new Promise<ServerAuthorizationCodeResponse>((resolve, reject) => {\r\n            this.server = createServer(async (req: IncomingMessage, res: ServerResponse) => {\r\n                const url = req.url;\r\n                if (!url) {\r\n                    res.end(errorTemplate || \"Error occurred loading redirectUrl\");\r\n                    reject(NodeAuthError.createUnableToLoadRedirectUrlError());\r\n                    return;\r\n                } else if (url === CommonConstants.FORWARD_SLASH) {\r\n                    res.end(successTemplate || \"Auth code was successfully acquired. You can close this window now.\");\r\n                    return;\r\n                }\r\n\r\n                const authCodeResponse = UrlString.getDeserializedQueryString(url);\r\n                if (authCodeResponse.code) {\r\n                    const redirectUri = await this.getRedirectUri();\r\n                    res.writeHead(HttpStatus.REDIRECT, { location: redirectUri }); // Prevent auth code from being saved in the browser history\r\n                    res.end();\r\n                }\r\n                resolve(authCodeResponse);\r\n            });\r\n            this.server.listen(0); // Listen on any available port\r\n        });\r\n\r\n        // Wait for server to be listening\r\n        await new Promise<void>((resolve) => {\r\n            let ticks = 0;\r\n            const id = setInterval(() => {\r\n                if ((LOOPBACK_SERVER_CONSTANTS.TIMEOUT_MS / LOOPBACK_SERVER_CONSTANTS.INTERVAL_MS) < ticks) {\r\n                    throw NodeAuthError.createLoopbackServerTimeoutError();\r\n                }\r\n\r\n                if (this.server.listening) {\r\n                    clearInterval(id);\r\n                    resolve();\r\n                }\r\n                ticks++;\r\n            }, LOOPBACK_SERVER_CONSTANTS.INTERVAL_MS);\r\n        });\r\n\r\n        return authCodeListener;\r\n    }\r\n\r\n    /**\r\n     * Get the port that the loopback server is running on\r\n     * @returns\r\n     */\r\n    getRedirectUri(): string {\r\n        if (!this.server) {\r\n            throw NodeAuthError.createNoLoopbackServerExistsError();\r\n        }\r\n\r\n        const address = this.server.address();\r\n        if (!address || typeof address === \"string\" || !address.port) {\r\n            this.closeServer();\r\n            throw NodeAuthError.createInvalidLoopbackAddressTypeError();\r\n        }\r\n\r\n        const port = address && address.port;\r\n\r\n        return `${Constants.HTTP_PROTOCOL}${Constants.LOCALHOST}:${port}`;\r\n    }\r\n\r\n    /**\r\n     * Close the loopback server\r\n     */\r\n    closeServer(): void {\r\n        if (!!this.server) {\r\n            this.server.close();\r\n        }\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ApiId, Constants } from \"../utils/Constants\";\r\nimport {\r\n    DeviceCodeClient,\r\n    AuthenticationResult,\r\n    CommonDeviceCodeRequest,\r\n    AuthError,\r\n    ResponseMode,\r\n    OIDC_DEFAULT_SCOPES,\r\n    CodeChallengeMethodValues,\r\n    Constants as CommonConstants,\r\n    ServerError,\r\n    NativeRequest,\r\n    NativeSignOutRequest,\r\n    AccountInfo,\r\n    INativeBrokerPlugin\r\n} from \"@azure/msal-common\";\r\nimport { Configuration } from \"../config/Configuration\";\r\nimport { ClientApplication } from \"./ClientApplication\";\r\nimport { IPublicClientApplication } from \"./IPublicClientApplication\";\r\nimport { DeviceCodeRequest } from \"../request/DeviceCodeRequest\";\r\nimport { AuthorizationUrlRequest } from \"../request/AuthorizationUrlRequest\";\r\nimport { AuthorizationCodeRequest } from \"../request/AuthorizationCodeRequest\";\r\nimport { InteractiveRequest } from \"../request/InteractiveRequest\";\r\nimport { NodeAuthError } from \"../error/NodeAuthError\";\r\nimport { LoopbackClient } from \"../network/LoopbackClient\";\r\nimport { SilentFlowRequest } from \"../request/SilentFlowRequest\";\r\nimport { SignOutRequest } from \"../request/SignOutRequest\";\r\nimport { ILoopbackClient } from \"../network/ILoopbackClient\";\r\n\r\n/**\r\n * This class is to be used to acquire tokens for public client applications (desktop, mobile). Public client applications\r\n * are not trusted to safely store application secrets, and therefore can only request tokens in the name of an user.\r\n * @public\r\n */\r\nexport class PublicClientApplication extends ClientApplication implements IPublicClientApplication {\r\n    private nativeBrokerPlugin?: INativeBrokerPlugin;\r\n    /**\r\n     * Important attributes in the Configuration object for auth are:\r\n     * - clientID: the application ID of your application. You can obtain one by registering your application with our Application registration portal.\r\n     * - authority: the authority URL for your application.\r\n     *\r\n     * AAD authorities are of the form https://login.microsoftonline.com/\\{Enter_the_Tenant_Info_Here\\}.\r\n     * - If your application supports Accounts in one organizational directory, replace \"Enter_the_Tenant_Info_Here\" value with the Tenant Id or Tenant name (for example, contoso.microsoft.com).\r\n     * - If your application supports Accounts in any organizational directory, replace \"Enter_the_Tenant_Info_Here\" value with organizations.\r\n     * - If your application supports Accounts in any organizational directory and personal Microsoft accounts, replace \"Enter_the_Tenant_Info_Here\" value with common.\r\n     * - To restrict support to Personal Microsoft accounts only, replace \"Enter_the_Tenant_Info_Here\" value with consumers.\r\n     *\r\n     * Azure B2C authorities are of the form https://\\{instance\\}/\\{tenant\\}/\\{policy\\}. Each policy is considered\r\n     * its own authority. You will have to set the all of the knownAuthorities at the time of the client application\r\n     * construction.\r\n     *\r\n     * ADFS authorities are of the form https://\\{instance\\}/adfs.\r\n     */\r\n    constructor(configuration: Configuration) {\r\n        super(configuration);\r\n        if (this.config.broker.nativeBrokerPlugin) {\r\n            if (this.config.broker.nativeBrokerPlugin.isBrokerAvailable) {\r\n                this.nativeBrokerPlugin = this.config.broker.nativeBrokerPlugin;\r\n                this.nativeBrokerPlugin.setLogger(this.config.system.loggerOptions); \r\n            } else {\r\n                this.logger.warning(\"NativeBroker implementation was provided but the broker is unavailable.\");\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Acquires a token from the authority using OAuth2.0 device code flow.\r\n     * This flow is designed for devices that do not have access to a browser or have input constraints.\r\n     * The authorization server issues a DeviceCode object with a verification code, an end-user code,\r\n     * and the end-user verification URI. The DeviceCode object is provided through a callback, and the end-user should be\r\n     * instructed to use another device to navigate to the verification URI to input credentials.\r\n     * Since the client cannot receive incoming requests, it polls the authorization server repeatedly\r\n     * until the end-user completes input of credentials.\r\n     */\r\n    public async acquireTokenByDeviceCode(request: DeviceCodeRequest): Promise<AuthenticationResult | null> {\r\n        this.logger.info(\"acquireTokenByDeviceCode called\", request.correlationId);\r\n        const validRequest: CommonDeviceCodeRequest = Object.assign(request,  await this.initializeBaseRequest(request));\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenByDeviceCode, validRequest.correlationId);\r\n        try {\r\n            const deviceCodeConfig = await this.buildOauthClientConfiguration(\r\n                validRequest.authority,\r\n                validRequest.correlationId,\r\n                serverTelemetryManager,\r\n                undefined,\r\n                request.azureCloudOptions\r\n            );\r\n            const deviceCodeClient = new DeviceCodeClient(deviceCodeConfig);\r\n            this.logger.verbose(\"Device code client created\", validRequest.correlationId);\r\n            return deviceCodeClient.acquireToken(validRequest);\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                e.setCorrelationId(validRequest.correlationId);\r\n            }\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Acquires a token interactively via the browser by requesting an authorization code then exchanging it for a token.\r\n     */\r\n    async acquireTokenInteractive(request: InteractiveRequest): Promise<AuthenticationResult> {\r\n        const correlationId = request.correlationId || this.cryptoProvider.createNewGuid();\r\n        this.logger.trace(\"acquireTokenInteractive called\", correlationId);\r\n        const { openBrowser, successTemplate, errorTemplate, windowHandle, loopbackClient: customLoopbackClient, ...remainingProperties } = request;\r\n\r\n        if (this.nativeBrokerPlugin) {\r\n            const brokerRequest: NativeRequest = {\r\n                ...remainingProperties,\r\n                clientId: this.config.auth.clientId,\r\n                scopes: request.scopes || OIDC_DEFAULT_SCOPES,\r\n                redirectUri: `${Constants.HTTP_PROTOCOL}${Constants.LOCALHOST}`,\r\n                authority: request.authority || this.config.auth.authority,\r\n                correlationId: correlationId,\r\n                extraParameters: {\r\n                    ...remainingProperties.extraQueryParameters,\r\n                    ...remainingProperties.tokenQueryParameters\r\n                },\r\n                accountId: remainingProperties.account?.nativeAccountId\r\n            };\r\n            return this.nativeBrokerPlugin.acquireTokenInteractive(brokerRequest, windowHandle);\r\n        }\r\n\r\n        const { verifier, challenge } = await this.cryptoProvider.generatePkceCodes();\r\n\r\n        const loopbackClient: ILoopbackClient = customLoopbackClient || new LoopbackClient();\r\n\r\n        try {\r\n            const authCodeListener = loopbackClient.listenForAuthCode(successTemplate, errorTemplate);\r\n            const redirectUri = loopbackClient.getRedirectUri();\r\n    \r\n            const validRequest: AuthorizationUrlRequest = {\r\n                ...remainingProperties,\r\n                correlationId: correlationId,\r\n                scopes: request.scopes || OIDC_DEFAULT_SCOPES,\r\n                redirectUri: redirectUri,\r\n                responseMode: ResponseMode.QUERY,\r\n                codeChallenge: challenge,\r\n                codeChallengeMethod: CodeChallengeMethodValues.S256\r\n            };\r\n    \r\n            const authCodeUrl = await this.getAuthCodeUrl(validRequest);\r\n            await openBrowser(authCodeUrl);\r\n            const authCodeResponse = await authCodeListener.finally(() => {\r\n                loopbackClient.closeServer();\r\n            });\r\n    \r\n            if (authCodeResponse.error) {\r\n                throw new ServerError(authCodeResponse.error, authCodeResponse.error_description, authCodeResponse.suberror);\r\n            } else if (!authCodeResponse.code) {\r\n                throw NodeAuthError.createNoAuthCodeInResponseError();\r\n            }\r\n    \r\n            const clientInfo = authCodeResponse.client_info;\r\n            const tokenRequest: AuthorizationCodeRequest = {\r\n                code: authCodeResponse.code,\r\n                codeVerifier: verifier,\r\n                clientInfo: clientInfo || CommonConstants.EMPTY_STRING,\r\n                ...validRequest\r\n            };\r\n            return this.acquireTokenByCode(tokenRequest);\r\n        } catch (e) {\r\n            loopbackClient.closeServer();\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns a token retrieved either from the cache or by exchanging the refresh token for a fresh access token. If brokering is enabled the token request will be serviced by the broker.\r\n     * @param request \r\n     * @returns \r\n     */\r\n    async acquireTokenSilent(request: SilentFlowRequest): Promise<AuthenticationResult | null> {\r\n        const correlationId = request.correlationId || this.cryptoProvider.createNewGuid();\r\n        this.logger.trace(\"acquireTokenSilent called\", correlationId);\r\n\r\n        if (this.nativeBrokerPlugin) {\r\n            const brokerRequest: NativeRequest = {\r\n                ...request,\r\n                clientId: this.config.auth.clientId,\r\n                scopes: request.scopes || OIDC_DEFAULT_SCOPES,\r\n                redirectUri: `${Constants.HTTP_PROTOCOL}${Constants.LOCALHOST}`,\r\n                authority: request.authority || this.config.auth.authority,\r\n                correlationId: correlationId,\r\n                extraParameters: request.tokenQueryParameters,\r\n                accountId: request.account.nativeAccountId,\r\n                forceRefresh: request.forceRefresh || false\r\n            };\r\n            return this.nativeBrokerPlugin.acquireTokenSilent(brokerRequest);\r\n        }\r\n\r\n        return super.acquireTokenSilent(request);\r\n    }\r\n\r\n    /**\r\n     * Removes cache artifacts associated with the given account\r\n     * @param request \r\n     * @returns \r\n     */\r\n    async signOut(request: SignOutRequest): Promise<void> {\r\n        if (this.nativeBrokerPlugin && request.account.nativeAccountId) {\r\n            const signoutRequest: NativeSignOutRequest = {\r\n                clientId: this.config.auth.clientId,\r\n                accountId: request.account.nativeAccountId,\r\n                correlationId: request.correlationId || this.cryptoProvider.createNewGuid()\r\n            };\r\n            await this.nativeBrokerPlugin.signOut(signoutRequest);\r\n        }\r\n\r\n        await this.getTokenCache().removeAccount(request.account);\r\n    }\r\n\r\n    /**\r\n     * Returns all cached accounts for this application. If brokering is enabled this request will be serviced by the broker.\r\n     * @returns \r\n     */\r\n    async getAllAccounts(): Promise<AccountInfo[]> {\r\n        if (this.nativeBrokerPlugin) {\r\n            const correlationId = this.cryptoProvider.createNewGuid();\r\n            return this.nativeBrokerPlugin.getAllAccounts(this.config.auth.clientId, correlationId);\r\n        }\r\n\r\n        return this.getTokenCache().getAllAccounts();\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { JwtHeader, sign } from \"jsonwebtoken\";\r\nimport { TimeUtils, ClientAuthError, Constants } from \"@azure/msal-common\";\r\nimport { CryptoProvider } from \"../crypto/CryptoProvider\";\r\nimport { EncodingUtils } from \"../utils/EncodingUtils\";\r\nimport { JwtConstants } from \"../utils/Constants\";\r\n\r\n/**\r\n * Client assertion of type jwt-bearer used in confidential client flows\r\n * @public\r\n */\r\nexport class ClientAssertion {\r\n\r\n    private jwt: string;\r\n    private privateKey: string;\r\n    private thumbprint: string;\r\n    private expirationTime: number;\r\n    private issuer: string;\r\n    private jwtAudience: string;\r\n    private publicCertificate: Array<string>;\r\n\r\n    /**\r\n     * Initialize the ClientAssertion class from the clientAssertion passed by the user\r\n     * @param assertion - refer https://tools.ietf.org/html/rfc7521\r\n     */\r\n    public static fromAssertion(assertion: string): ClientAssertion {\r\n        const clientAssertion = new ClientAssertion();\r\n        clientAssertion.jwt = assertion;\r\n        return clientAssertion;\r\n    }\r\n\r\n    /**\r\n     * Initialize the ClientAssertion class from the certificate passed by the user\r\n     * @param thumbprint - identifier of a certificate\r\n     * @param privateKey - secret key\r\n     * @param publicCertificate - electronic document provided to prove the ownership of the public key\r\n     */\r\n    public static fromCertificate(thumbprint: string, privateKey: string, publicCertificate?: string): ClientAssertion {\r\n        const clientAssertion = new ClientAssertion();\r\n        clientAssertion.privateKey = privateKey;\r\n        clientAssertion.thumbprint = thumbprint;\r\n        if (publicCertificate) {\r\n            clientAssertion.publicCertificate = this.parseCertificate(publicCertificate);\r\n        }\r\n        return clientAssertion;\r\n    }\r\n\r\n    /**\r\n     * Update JWT for certificate based clientAssertion, if passed by the user, uses it as is\r\n     * @param cryptoProvider - library's crypto helper\r\n     * @param issuer - iss claim\r\n     * @param jwtAudience - aud claim\r\n     */\r\n    public getJwt(cryptoProvider: CryptoProvider, issuer: string, jwtAudience: string): string {\r\n        // if assertion was created from certificate, check if jwt is expired and create new one.\r\n        if (this.privateKey && this.thumbprint) {\r\n\r\n            if (this.jwt && !this.isExpired() && issuer === this.issuer && jwtAudience === this.jwtAudience) {\r\n                return this.jwt;\r\n            }\r\n\r\n            return this.createJwt(cryptoProvider, issuer, jwtAudience);\r\n        }\r\n\r\n        /*\r\n         * if assertion was created by caller, then we just append it. It is up to the caller to\r\n         * ensure that it contains necessary claims and that it is not expired.\r\n         */\r\n        if (this.jwt) {\r\n            return this.jwt;\r\n        }\r\n\r\n        throw ClientAuthError.createInvalidAssertionError();\r\n    }\r\n\r\n    /**\r\n     * JWT format and required claims specified: https://tools.ietf.org/html/rfc7523#section-3\r\n     */\r\n    private createJwt(cryptoProvider: CryptoProvider, issuer: string, jwtAudience: string): string {\r\n\r\n        this.issuer = issuer;\r\n        this.jwtAudience = jwtAudience;\r\n        const issuedAt = TimeUtils.nowSeconds();\r\n        this.expirationTime = issuedAt + 600;\r\n\r\n        const header: JwtHeader = {\r\n            alg: JwtConstants.RSA_256,\r\n            x5t: EncodingUtils.base64EncodeUrl(this.thumbprint, \"hex\")\r\n        };\r\n\r\n        if (this.publicCertificate) {\r\n            Object.assign(header, {\r\n                x5c: this.publicCertificate\r\n            } as Partial<JwtHeader>);\r\n        }\r\n\r\n        const payload = {\r\n            [JwtConstants.AUDIENCE]: this.jwtAudience,\r\n            [JwtConstants.EXPIRATION_TIME]: this.expirationTime,\r\n            [JwtConstants.ISSUER]: this.issuer,\r\n            [JwtConstants.SUBJECT]: this.issuer,\r\n            [JwtConstants.NOT_BEFORE]: issuedAt,\r\n            [JwtConstants.JWT_ID]: cryptoProvider.createNewGuid()\r\n        };\r\n\r\n        this.jwt = sign(payload, this.privateKey, { header });\r\n        return this.jwt;\r\n    }\r\n\r\n    /**\r\n     * Utility API to check expiration\r\n     */\r\n    private isExpired(): boolean {\r\n        return this.expirationTime < TimeUtils.nowSeconds();\r\n    }\r\n\r\n    /**\r\n     * Extracts the raw certs from a given certificate string and returns them in an array.\r\n     * @param publicCertificate - electronic document provided to prove the ownership of the public key\r\n     */\r\n    public static parseCertificate(publicCertificate: string): Array<string> {\r\n        /**\r\n         * This is regex to identify the certs in a given certificate string.\r\n         * We want to look for the contents between the BEGIN and END certificate strings, without the associated newlines.\r\n         * The information in parens \"(.+?)\" is the capture group to represent the cert we want isolated.\r\n         * \".\" means any string character, \"+\" means match 1 or more times, and \"?\" means the shortest match.\r\n         * The \"g\" at the end of the regex means search the string globally, and the \"s\" enables the \".\" to match newlines.\r\n         */\r\n        const regexToFindCerts = /-----BEGIN CERTIFICATE-----\\r*\\n(.+?)\\r*\\n-----END CERTIFICATE-----/gs;\r\n        const certs: string[] = [];\r\n\r\n        let matches;\r\n        while ((matches = regexToFindCerts.exec(publicCertificate)) !== null) {\r\n            // matches[1] represents the first parens capture group in the regex.\r\n            certs.push(matches[1].replace(/\\r*\\n/g, Constants.EMPTY_STRING));\r\n        }\r\n\r\n        return certs;\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ClientApplication } from \"./ClientApplication\";\r\nimport { Configuration } from \"../config/Configuration\";\r\nimport { ClientAssertion } from \"./ClientAssertion\";\r\nimport { Constants as NodeConstants, ApiId, REGION_ENVIRONMENT_VARIABLE } from \"../utils/Constants\";\r\nimport {\r\n    ClientCredentialClient,\r\n    OnBehalfOfClient,\r\n    CommonClientCredentialRequest,\r\n    CommonOnBehalfOfRequest,\r\n    AuthenticationResult,\r\n    StringUtils,\r\n    ClientAuthError,\r\n    AzureRegionConfiguration,\r\n    AuthError,\r\n    Constants,\r\n    IAppTokenProvider,\r\n    OIDC_DEFAULT_SCOPES\r\n} from \"@azure/msal-common\";\r\nimport { IConfidentialClientApplication } from \"./IConfidentialClientApplication\";\r\nimport { OnBehalfOfRequest } from \"../request/OnBehalfOfRequest\";\r\nimport { ClientCredentialRequest } from \"../request/ClientCredentialRequest\";\r\n\r\n/**\r\n *  This class is to be used to acquire tokens for confidential client applications (webApp, webAPI). Confidential client applications\r\n *  will configure application secrets, client certificates/assertions as applicable\r\n * @public\r\n */\r\nexport class ConfidentialClientApplication extends ClientApplication implements IConfidentialClientApplication {\r\n    private appTokenProvider?: IAppTokenProvider;\r\n\r\n    /**\r\n     * Constructor for the ConfidentialClientApplication\r\n     *\r\n     * Required attributes in the Configuration object are:\r\n     * - clientID: the application ID of your application. You can obtain one by registering your application with our application registration portal\r\n     * - authority: the authority URL for your application.\r\n     * - client credential: Must set either client secret, certificate, or assertion for confidential clients. You can obtain a client secret from the application registration portal.\r\n     *\r\n     * In Azure AD, authority is a URL indicating of the form https://login.microsoftonline.com/\\{Enter_the_Tenant_Info_Here\\}.\r\n     * If your application supports Accounts in one organizational directory, replace \"Enter_the_Tenant_Info_Here\" value with the Tenant Id or Tenant name (for example, contoso.microsoft.com).\r\n     * If your application supports Accounts in any organizational directory, replace \"Enter_the_Tenant_Info_Here\" value with organizations.\r\n     * If your application supports Accounts in any organizational directory and personal Microsoft accounts, replace \"Enter_the_Tenant_Info_Here\" value with common.\r\n     * To restrict support to Personal Microsoft accounts only, replace \"Enter_the_Tenant_Info_Here\" value with consumers.\r\n     *\r\n     * In Azure B2C, authority is of the form https://\\{instance\\}/tfp/\\{tenant\\}/\\{policyName\\}/\r\n     * Full B2C functionality will be available in this library in future versions.\r\n     *\r\n     * @param Configuration - configuration object for the MSAL ConfidentialClientApplication instance\r\n     */\r\n    constructor(configuration: Configuration) {\r\n        super(configuration);\r\n        this.setClientCredential(this.config);\r\n        this.appTokenProvider = undefined;\r\n    }\r\n\r\n    /**               \r\n     * This extensibility point only works for the client_credential flow, i.e. acquireTokenByClientCredential and\r\n     * is meant for Azure SDK to enhance Managed Identity support.\r\n     * \r\n     * @param IAppTokenProvider  - Extensibility interface, which allows the app developer to return a token from a custom source.     \r\n     */\r\n    SetAppTokenProvider(provider: IAppTokenProvider): void {\r\n        this.appTokenProvider = provider;\r\n    }\r\n\r\n    /**\r\n     * Acquires tokens from the authority for the application (not for an end user).\r\n     */\r\n    public async acquireTokenByClientCredential(request: ClientCredentialRequest): Promise<AuthenticationResult | null> {\r\n        this.logger.info(\"acquireTokenByClientCredential called\", request.correlationId);\r\n\r\n        // If there is a client assertion present in the request, it overrides the one present in the client configuration\r\n        let clientAssertion;\r\n        if (request.clientAssertion) {\r\n            clientAssertion = {\r\n                assertion: request.clientAssertion,\r\n                assertionType: NodeConstants.JWT_BEARER_ASSERTION_TYPE\r\n            };\r\n        }\r\n\r\n        const baseRequest = await this.initializeBaseRequest(request);\r\n\r\n        // valid base request should not contain oidc scopes in this grant type\r\n        const validBaseRequest = {\r\n            ...baseRequest,\r\n            scopes: baseRequest.scopes.filter((scope: string) => !OIDC_DEFAULT_SCOPES.includes(scope))\r\n        };\r\n\r\n        const validRequest: CommonClientCredentialRequest = {\r\n            ...request,\r\n            ...validBaseRequest,\r\n            clientAssertion\r\n        };\r\n\r\n        const azureRegionConfiguration: AzureRegionConfiguration = {\r\n            azureRegion: validRequest.azureRegion,\r\n            environmentRegion: process.env[REGION_ENVIRONMENT_VARIABLE]\r\n        };\r\n\r\n        const serverTelemetryManager = this.initializeServerTelemetryManager(ApiId.acquireTokenByClientCredential, validRequest.correlationId, validRequest.skipCache);\r\n        try {\r\n            const clientCredentialConfig = await this.buildOauthClientConfiguration(\r\n                validRequest.authority,\r\n                validRequest.correlationId,\r\n                serverTelemetryManager,\r\n                azureRegionConfiguration,\r\n                request.azureCloudOptions\r\n            );\r\n            const clientCredentialClient = new ClientCredentialClient(clientCredentialConfig, this.appTokenProvider);\r\n            this.logger.verbose(\"Client credential client created\", validRequest.correlationId);\r\n            return clientCredentialClient.acquireToken(validRequest);\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                e.setCorrelationId(validRequest.correlationId);\r\n            }\r\n            serverTelemetryManager.cacheFailedRequest(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Acquires tokens from the authority for the application.\r\n     *\r\n     * Used in scenarios where the current app is a middle-tier service which was called with a token\r\n     * representing an end user. The current app can use the token (oboAssertion) to request another\r\n     * token to access downstream web API, on behalf of that user.\r\n     *\r\n     * The current middle-tier app has no user interaction to obtain consent.\r\n     * See how to gain consent upfront for your middle-tier app from this article.\r\n     * https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-on-behalf-of-flow#gaining-consent-for-the-middle-tier-application\r\n     */\r\n    public async acquireTokenOnBehalfOf(request: OnBehalfOfRequest): Promise<AuthenticationResult | null> {\r\n        this.logger.info(\"acquireTokenOnBehalfOf called\", request.correlationId);\r\n        const validRequest: CommonOnBehalfOfRequest = {\r\n            ...request,\r\n            ... await this.initializeBaseRequest(request)\r\n        };\r\n        try {\r\n            const onBehalfOfConfig = await this.buildOauthClientConfiguration(\r\n                validRequest.authority,\r\n                validRequest.correlationId,\r\n                undefined,\r\n                undefined,\r\n                request.azureCloudOptions\r\n            );\r\n            const oboClient = new OnBehalfOfClient(onBehalfOfConfig);\r\n            this.logger.verbose(\"On behalf of client created\", validRequest.correlationId);\r\n            return oboClient.acquireToken(validRequest);\r\n        } catch (e) {\r\n            if (e instanceof AuthError) {\r\n                e.setCorrelationId(validRequest.correlationId);\r\n            }\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    private setClientCredential(configuration: Configuration): void {\r\n        const clientSecretNotEmpty = !StringUtils.isEmpty(configuration.auth.clientSecret);\r\n        const clientAssertionNotEmpty = !StringUtils.isEmpty(configuration.auth.clientAssertion);\r\n        const certificate = configuration.auth.clientCertificate || {\r\n            thumbprint: Constants.EMPTY_STRING,\r\n            privateKey: Constants.EMPTY_STRING\r\n        };\r\n        const certificateNotEmpty = !StringUtils.isEmpty(certificate.thumbprint) || !StringUtils.isEmpty(certificate.privateKey);\r\n\r\n        /*\r\n         * If app developer configures this callback, they don't need a credential\r\n         * i.e. AzureSDK can get token from Managed Identity without a cert / secret\r\n         */\r\n        if (this.appTokenProvider) {\r\n            return;\r\n        }\r\n\r\n        // Check that at most one credential is set on the application\r\n        if (\r\n            clientSecretNotEmpty && clientAssertionNotEmpty ||\r\n            clientAssertionNotEmpty && certificateNotEmpty ||\r\n            clientSecretNotEmpty && certificateNotEmpty) {\r\n            throw ClientAuthError.createInvalidCredentialError();\r\n        }\r\n\r\n        if (configuration.auth.clientSecret) {\r\n            this.clientSecret = configuration.auth.clientSecret;\r\n            return;\r\n        }\r\n\r\n        if (configuration.auth.clientAssertion) {\r\n            this.clientAssertion = ClientAssertion.fromAssertion(configuration.auth.clientAssertion);\r\n            return;\r\n        }\r\n\r\n        if (!certificateNotEmpty) {\r\n            throw ClientAuthError.createInvalidCredentialError();\r\n        } else {\r\n            this.clientAssertion = ClientAssertion.fromCertificate(certificate.thumbprint, certificate.privateKey, configuration.auth.clientCertificate?.x5c);\r\n        }\r\n    }\r\n}\r\n", "/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AccountEntity, ICachePlugin, TokenCacheContext } from \"@azure/msal-common\";\r\nimport { TokenCache } from \"../TokenCache\";\r\nimport { IPartitionManager } from \"./IPartitionManager\";\r\nimport { ICacheClient } from \"./ICacheClient\";\r\n\r\nexport class DistributedCachePlugin implements ICachePlugin {\r\n    private client: ICacheClient;\r\n    private partitionManager: IPartitionManager;\r\n\r\n    constructor(client: ICacheClient, partitionManager: IPartitionManager) {\r\n        this.client = client;\r\n        this.partitionManager = partitionManager;\r\n    }\r\n  \r\n    public async beforeCacheAccess(cacheContext: TokenCacheContext): Promise<void> {\r\n        const partitionKey = await this.partitionManager.getKey();\r\n        const cacheData = await this.client.get(partitionKey);\r\n        cacheContext.tokenCache.deserialize(cacheData);\r\n    }\r\n  \r\n    public async afterCacheAccess(cacheContext: TokenCacheContext): Promise<void> {\r\n        if (cacheContext.cacheHasChanged) {\r\n            const kvStore = (cacheContext.tokenCache as TokenCache).getKVStore();\r\n            const accountEntities = Object.values(kvStore).filter(value => AccountEntity.isAccountEntity(value as object));\r\n\r\n            if (accountEntities.length > 0) {\r\n                const accountEntity = accountEntities[0] as AccountEntity;\r\n                const partitionKey = await this.partitionManager.extractKey(accountEntity);\r\n                          \r\n                await this.client.set(partitionKey, cacheContext.tokenCache.serialize());           \r\n            }\r\n        }\r\n    }\r\n}\r\n"], "names": ["HttpMethod", "HttpStatus", "ProxyStatus", "REGION_ENVIRONMENT_VARIABLE", "RANDOM_OCTET_SIZE", "Hash", "SHA256", "CharSet", "CV_CHARSET", "Constants", "MSAL_SKU", "JWT_BEARER_ASSERTION_TYPE", "AUTHORIZATION_PENDING", "HTTP_PROTOCOL", "LOCALHOST", "ApiId", "JwtConstants", "ALGORITHM", "RSA_256", "X5T", "X5C", "AUDIENCE", "EXPIRATION_TIME", "ISSUER", "SUBJECT", "NOT_BEFORE", "JWT_ID", "LOOPBACK_SERVER_CONSTANTS", "INTERVAL_MS", "TIMEOUT_MS", "NetworkUtils", "getNetworkResponse", "headers", "body", "statusCode", "status", "urlToHttpOptions", "url", "options", "protocol", "hostname", "startsWith", "slice", "hash", "search", "pathname", "path", "href", "port", "Number", "username", "password", "auth", "decodeURIComponent", "HttpClient", "constructor", "proxyUrl", "customAgentOptions", "sendGetRequestAsync", "networkRequestViaProxy", "GET", "networkRequestViaHttps", "sendPostRequestAsync", "cancellationToken", "POST", "destinationUrlString", "proxyUrlString", "httpMethod", "agentOptions", "timeout", "destinationUrl", "URL", "tunnelRequestOptions", "host", "method", "Object", "keys", "length", "agent", "http", "Agent", "postRequestStringContent", "outgoingRequestString", "toUpperCase", "Promise", "resolve", "reject", "request", "on", "destroy", "Error", "end", "response", "socket", "proxyStatusCode", "SERVER_ERROR", "SUCCESS_RANGE_START", "SUCCESS_RANGE_END", "statusMessage", "setTimeout", "write", "data", "chunk", "push", "dataString", "<PERSON><PERSON><PERSON>", "concat", "toString", "dataStringArray", "split", "httpStatusCode", "parseInt", "join", "headersArray", "entries", "Map", "for<PERSON>ach", "header", "headerKeyValue", "RegExp", "<PERSON><PERSON><PERSON>", "headerValue", "object", "JSON", "parse", "e", "set", "fromEntries", "parsedHeaders", "networkResponse", "parseBody", "urlString", "isPostRequest", "customOptions", "https", "parsedBody", "error", "errorType", "errorDescription<PERSON>elper", "CLIENT_ERROR_RANGE_START", "CLIENT_ERROR_RANGE_END", "SERVER_ERROR_RANGE_START", "SERVER_ERROR_RANGE_END", "error_description", "stringify", "DEFAULT_AUTH_OPTIONS", "clientId", "EMPTY_STRING", "authority", "DEFAULT_AUTHORITY", "clientSecret", "clientAssertion", "clientCertificate", "thumbprint", "privateKey", "x5c", "knownAuthorities", "cloudDiscoveryMetadata", "authorityMetadata", "clientCapabilities", "protocolMode", "ProtocolMode", "AAD", "azureCloudOptions", "azureCloudInstance", "AzureCloudInstance", "None", "tenant", "skipAuthorityMetadataCache", "DEFAULT_CACHE_OPTIONS", "claimsBasedCachingEnabled", "DEFAULT_LOGGER_OPTIONS", "logger<PERSON><PERSON><PERSON>", "piiLoggingEnabled", "logLevel", "LogLevel", "Info", "DEFAULT_SYSTEM_OPTIONS", "loggerOptions", "networkClient", "DEFAULT_TELEMETRY_OPTIONS", "application", "appName", "appVersion", "buildAppConfiguration", "broker", "cache", "system", "telemetry", "systemOptions", "GuidGenerator", "generateGuid", "uuidv4", "isGuid", "guid", "regexGuid", "test", "EncodingUtils", "base64Encode", "str", "encoding", "from", "base64EncodeUrl", "replace", "base64Decode", "base64Str", "base64DecodeUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sha256", "buffer", "crypto", "createHash", "update", "digest", "PkceGenerator", "hashUtils", "generatePkceCodes", "verifier", "generateCodeVerifier", "challenge", "generateCodeChallengeFromVerifier", "char<PERSON><PERSON>", "maxNumber", "byte", "randomBytes", "index", "codeVerifier", "CryptoProvider", "pkceGenerator", "guidGenerator", "createNewGuid", "input", "getPublicKeyThumbprint", "removeTokenBindingKey", "clearKeystore", "signJwt", "hashString", "plainText", "Deserializer", "deserializeJSONBlob", "jsonFile", "deserializedCache", "StringUtils", "isEmpty", "deserializeAccounts", "accounts", "accountObjects", "map", "key", "serializedAcc", "mappedAcc", "homeAccountId", "home_account_id", "environment", "realm", "localAccountId", "local_account_id", "authorityType", "authority_type", "name", "clientInfo", "client_info", "lastModificationTime", "last_modification_time", "lastModificationApp", "last_modification_app", "account", "AccountEntity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toObject", "deserializeIdTokens", "idTokens", "idObjects", "serializedIdT", "mappedIdT", "credentialType", "credential_type", "client_id", "secret", "idToken", "IdTokenEntity", "deserializeAccessTokens", "accessTokens", "atObjects", "serializedAT", "mappedAT", "target", "cachedAt", "cached_at", "expiresOn", "expires_on", "extendedExpiresOn", "extended_expires_on", "refreshOn", "refresh_on", "keyId", "key_id", "tokenType", "token_type", "requestedClaims", "requestedClaimsHash", "userAssertionHash", "accessToken", "AccessTokenEntity", "deserializeRefreshTokens", "refreshTokens", "rtObjects", "serializedRT", "mappedRT", "familyId", "family_id", "refreshToken", "RefreshTokenEntity", "deserializeAppMetadata", "appMetadata", "appMetadataObjects", "serializedAmdt", "mappedAmd", "amd", "AppMetadataEntity", "deserializeAllCache", "jsonCache", "Account", "IdToken", "AccessToken", "RefreshToken", "AppMetadata", "Serializer", "serializeJSONBlob", "serializeAccounts", "accCache", "accountEntity", "serializeIdTokens", "idTCache", "idTEntity", "serializeAccessTokens", "atCache", "atEntity", "serializeRefreshTokens", "rtCache", "rtEntity", "serializeAppMetadata", "amdtCache", "amdtEntity", "serializeAllCache", "inMemCache", "NodeStorage", "logger", "cryptoImpl", "registerChangeEmitter", "func", "changeEmitters", "emitChange", "call", "cacheToInMemoryCache", "inMemory<PERSON>ache", "inMemoryCacheToCache", "getCache", "getInMemoryCache", "trace", "setInMemoryCache", "setCache", "getItem", "tracePii", "setItem", "value", "getAccount<PERSON><PERSON>s", "accountKeys", "getToken<PERSON>eys", "tokenKeys", "getAccount", "accountKey", "isAccountEntity", "setAccount", "generateAccountKey", "getIdTokenCredential", "idToken<PERSON>ey", "isIdTokenEntity", "setIdTokenCredential", "generateCredentialKey", "getAccessTokenCredential", "accessTokenKey", "isAccessTokenEntity", "setAccessTokenCredential", "getRefreshTokenCredential", "refreshT<PERSON><PERSON><PERSON>", "isRefreshTokenEntity", "setRefreshTokenCredential", "getAppMetadata", "appMetadataKey", "isAppMetadataEntity", "setAppMetadata", "generateAppMetadataKey", "getServerTelemetry", "serverTelemetrykey", "serverTelemetryEntity", "ServerTelemetryEntity", "isServerTelemetryEntity", "setServerTelemetry", "serverTelemetryKey", "serverTelemetry", "getAuthorityMetadata", "authorityMetadataEntity", "AuthorityMetadataEntity", "isAuthorityMetadataEntity", "getAuthorityMetadataKeys", "get<PERSON><PERSON><PERSON>", "filter", "isAuthorityMetadata", "setAuthorityMetadata", "metadata", "getThrottlingCache", "throttling<PERSON><PERSON><PERSON><PERSON>", "throttlingCache", "ThrottlingEntity", "isThrottlingEntity", "setThrottlingCache", "removeItem", "result", "<PERSON><PERSON><PERSON>", "includes", "clear", "cacheKeys", "generateInMemoryCache", "generateJsonCache", "updateCredentialCacheKey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "credential", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheItem", "verbose", "defaultSerializedCache", "TokenCache", "storage", "cachePlugin", "cacheHasChanged", "handleChangeEvent", "bind", "persistence", "has<PERSON><PERSON>ed", "serialize", "finalState", "cacheSnapshot", "mergeState", "deserialize", "overlayDefaults", "getKVStore", "getAllAccounts", "cacheContext", "TokenCacheContext", "beforeCacheAccess", "afterCacheAccess", "getAccountByHomeId", "allAccounts", "accountObj", "getAccountByLocalId", "removeAccount", "generateAccountCacheKey", "oldState", "currentState", "stateAfterRemoval", "mergeRemovals", "mergeUpdates", "newState", "new<PERSON>ey", "newValue", "hasOwnProperty", "newValueNotNull", "newValueIsObject", "newValueIsNotArray", "Array", "isArray", "oldStateNotUndefinedOrNull", "mergeRemovalsDict", "<PERSON><PERSON><PERSON>", "passedInCache", "version", "NodeAuthErrorMessage", "invalidLoopbackAddressType", "code", "desc", "unableToLoadRedirectUri", "noAuthCodeInResponse", "noLoopbackServerExists", "loopbackServerAlreadyExists", "loopbackServerTimeout", "stateNotFoundError", "NodeAuthError", "<PERSON>th<PERSON><PERSON><PERSON>", "errorCode", "errorMessage", "createInvalidLoopbackAddressTypeError", "createUnableToLoadRedirectUrlError", "createNoAuthCodeInResponseError", "createNoLoopbackServerExistsError", "createLoopbackServerAlreadyExistsError", "createLoopbackServerTimeoutError", "createStateNotFoundError", "ClientApplication", "configuration", "config", "cryptoProvider", "<PERSON><PERSON>", "tokenCache", "getAuthCodeUrl", "info", "correlationId", "validRequest", "initializeBaseRequest", "responseMode", "ResponseMode", "QUERY", "authenticationScheme", "AuthenticationScheme", "BEARER", "authClientConfig", "buildOauthClientConfiguration", "undefined", "authorizationCodeClient", "AuthorizationCodeClient", "acquireTokenByCode", "authCodePayLoad", "state", "validateState", "serverTelemetryManager", "initializeServerTelemetryManager", "acquireToken", "setCorrelationId", "cacheFailedRequest", "acquireTokenByRefreshToken", "refreshTokenClientConfig", "refreshTokenClient", "RefreshTokenClient", "acquireTokenSilent", "forceRefresh", "silentFlowClientConfig", "silentFlowClient", "SilentFlowClient", "acquireTokenByUsernamePassword", "usernamePasswordClientConfig", "usernamePasswordClient", "UsernamePasswordClient", "getTokenCache", "cachedState", "ClientAuthError", "createStateMismatchError", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "requestCorrelationId", "azureRegionConfiguration", "userAzureCloudOptions", "discoveredAuthority", "createAuthority", "updateRegionDiscoveryMetadata", "regionDiscoveryMetadata", "clientConfiguration", "authOptions", "cacheOptions", "cryptoInterface", "networkInterface", "storageInterface", "clientCredentials", "getClientAssertion", "libraryInfo", "sku", "NodeConstants", "cpu", "process", "arch", "os", "platform", "persistencePlugin", "serializableCache", "assertion", "getJwt", "tokenEndpoint", "assertionType", "authRequest", "POP", "claims", "isEmptyObj", "scopes", "OIDC_DEFAULT_SCOPES", "apiId", "telemetryPayload", "ServerTelemetryManager", "authorityString", "authorityUrl", "Authority", "generateAuthority", "authorityOptions", "AuthorityFactory", "createDiscoveredInstance", "clearCache", "LoopbackClient", "listenForAuthCode", "successTemplate", "errorTemplate", "server", "authCodeListener", "createServer", "req", "res", "CommonConstants", "FORWARD_SLASH", "authCodeResponse", "UrlString", "getDeserializedQueryString", "redirectUri", "getRedirectUri", "writeHead", "REDIRECT", "location", "listen", "ticks", "id", "setInterval", "listening", "clearInterval", "address", "closeServer", "close", "PublicClientApplication", "nativeBrokerPlugin", "isBrokerAvailable", "warning", "acquireTokenByDeviceCode", "assign", "deviceCodeConfig", "deviceCodeClient", "DeviceCodeClient", "acquireTokenInteractive", "openBrowser", "windowHandle", "loopbackClient", "customLoopbackClient", "remainingProperties", "brokerRequest", "extraParameters", "extraQueryParameters", "tokenQueryParameters", "accountId", "nativeAccountId", "codeChallenge", "codeChallengeMethod", "CodeChallengeMethodValues", "S256", "authCodeUrl", "finally", "ServerError", "suberror", "tokenRequest", "signOut", "signoutRequest", "ClientAssertion", "fromAssertion", "jwt", "fromCertificate", "publicCertificate", "parseCertificate", "issuer", "jwtAudience", "isExpired", "createJwt", "createInvalidAssertionError", "issuedAt", "TimeUtils", "nowSeconds", "expirationTime", "alg", "x5t", "payload", "sign", "regexToFindCerts", "certs", "matches", "exec", "ConfidentialClientApplication", "setClientCredential", "appTokenProvider", "SetAppTokenProvider", "provider", "acquireTokenByClientCredential", "baseRequest", "validBaseRequest", "scope", "azureRegion", "environmentRegion", "env", "<PERSON><PERSON><PERSON>", "clientCredentialConfig", "clientCredentialClient", "ClientCredentialClient", "acquireTokenOnBehalfOf", "onBehalfOfConfig", "oboClient", "OnBehalfOfClient", "clientSecretNotEmpty", "clientAssertionNotEmpty", "certificate", "certificateNotEmpty", "createInvalidCredentialError", "DistributedCachePlugin", "client", "partitionManager", "partition<PERSON>ey", "<PERSON><PERSON><PERSON>", "cacheData", "get", "kvStore", "accountEntities", "values", "extractKey"], "mappings": ";;;;;;;;AAAA;;;;AAKA;;;AAGA,IAAYA,UAGX;AAHD,WAAYA,UAAU;EAClBA,yBAAW;EACXA,2BAAa;AACjB,CAAC,EAHWA,UAAU,KAAVA,UAAU;AAKtB,IAAYC,UAQX;AARD,WAAYA,UAAU;EAClBA,2EAAyB;EACzBA,uEAAuB;EACvBA,qDAAc;EACdA,qFAA8B;EAC9BA,iFAA4B;EAC5BA,qFAA8B;EAC9BA,iFAA4B;AAChC,CAAC,EARWA,UAAU,KAAVA,UAAU;AAUtB,IAAYC,WAIX;AAJD,WAAYA,WAAW;EACnBA,6EAAyB;EACzBA,yEAAuB;EACvBA,+DAAkB;AACtB,CAAC,EAJWA,WAAW,KAAXA,WAAW;AAMvB;;;AAGO,MAAMC,2BAA2B,GAAG,aAAa;AAExD;;;AAGO,MAAMC,iBAAiB,GAAG,EAAE;AAEnC;;;AAGO,MAAMC,IAAI,GAAG;EAChBC,MAAM,EAAE;CACX;AAED;;;AAGO,MAAMC,OAAO,GAAG;EACnBC,UAAU,EACN;CACP;AAUD;;;AAGO,MAAMC,SAAS,GAAG;EACrBC,QAAQ,EAAE,cAAc;EACxBC,yBAAyB,EAAE,wDAAwD;EACnFC,qBAAqB,EAAE,uBAAuB;EAC9CC,aAAa,EAAE,SAAS;EACxBC,SAAS,EAAE;CACd;AAED;;;;;;;AAOA,IAAYC,KAOX;AAPD,WAAYA,KAAK;EACbA,8DAAuB;EACvBA,uFAAoC;EACpCA,2EAA8B;EAC9BA,uFAAoC;EACpCA,+DAAwB;EACxBA,+EAAgC;AACpC,CAAC,EAPWA,KAAK,KAALA,KAAK;AASjB;;;AAGO,MAAMC,YAAY,GAAG;EACxBC,SAAS,EAAE,KAAK;EAChBC,OAAO,EAAE,OAAO;EAChBC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,KAAK;EACfC,eAAe,EAAE,KAAK;EACtBC,MAAM,EAAE,KAAK;EACbC,OAAO,EAAE,KAAK;EACdC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE;CACX;AAEM,MAAMC,yBAAyB,GAAG;EACrCC,WAAW,EAAE,GAAG;EAChBC,UAAU,EAAE;CACf;;AC5GD;;;;AAOA,MAAaC,YAAY;EACrB,OAAOC,kBAAkB,CAAIC,OAA+B,EAAEC,IAAO,EAAEC,UAAkB;IACrF,OAAO;MACHF,OAAO,EAAEA,OAAO;MAChBC,IAAI,EAAEA,IAAI;MACVE,MAAM,EAAED;KACX;;;;;;;EAQL,OAAOE,gBAAgB,CAACC,GAAQ;IAC5B,MAAMC,OAAO,GAA4B;MACrCC,QAAQ,EAAEF,GAAG,CAACE,QAAQ;MACtBC,QAAQ,EAAEH,GAAG,CAACG,QAAQ,IAAIH,GAAG,CAACG,QAAQ,CAACC,UAAU,CAAC,GAAG,CAAC,GAClDJ,GAAG,CAACG,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACzBL,GAAG,CAACG,QAAQ;MAChBG,IAAI,EAAEN,GAAG,CAACM,IAAI;MACdC,MAAM,EAAEP,GAAG,CAACO,MAAM;MAClBC,QAAQ,EAAER,GAAG,CAACQ,QAAQ;MACtBC,IAAI,KAAKT,GAAG,CAACQ,QAAQ,IAAI,KAAKR,GAAG,CAACO,MAAM,IAAI,IAAI;MAChDG,IAAI,EAAEV,GAAG,CAACU;KACb;IACD,IAAIV,GAAG,CAACW,IAAI,KAAK,EAAE,EAAE;MACjBV,OAAO,CAACU,IAAI,GAAGC,MAAM,CAACZ,GAAG,CAACW,IAAI,CAAC;;IAEnC,IAAIX,GAAG,CAACa,QAAQ,IAAIb,GAAG,CAACc,QAAQ,EAAE;MAC9Bb,OAAO,CAACc,IAAI,MAAMC,kBAAkB,CAAChB,GAAG,CAACa,QAAQ,KAAKG,kBAAkB,CAAChB,GAAG,CAACc,QAAQ,GAAG;;IAE5F,OAAOb,OAAO;;;;ACvCtB;;;;AAMA,AAKA;;;AAGA,MAAagB,UAAU;EAInBC,YACIC,QAAiB,EACjBC,kBAA2D;IAE3D,IAAI,CAACD,QAAQ,GAAGA,QAAQ,IAAI,EAAE;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE;;;;;;;EAQtD,MAAMC,mBAAmB,CACrBrB,GAAW,EACXC,OAA+B;IAE/B,IAAI,IAAI,CAACkB,QAAQ,EAAE;MACf,OAAOG,sBAAsB,CAACtB,GAAG,EAAE,IAAI,CAACmB,QAAQ,EAAExD,UAAU,CAAC4D,GAAG,EAAEtB,OAAO,EAAE,IAAI,CAACmB,kBAAuC,CAAC;KAC3H,MAAM;MACH,OAAOI,sBAAsB,CAACxB,GAAG,EAAErC,UAAU,CAAC4D,GAAG,EAAEtB,OAAO,EAAE,IAAI,CAACmB,kBAAwC,CAAC;;;;;;;;EASlH,MAAMK,oBAAoB,CACtBzB,GAAW,EACXC,OAA+B,EAC/ByB,iBAA0B;IAE1B,IAAI,IAAI,CAACP,QAAQ,EAAE;MACf,OAAOG,sBAAsB,CAACtB,GAAG,EAAE,IAAI,CAACmB,QAAQ,EAAExD,UAAU,CAACgE,IAAI,EAAE1B,OAAO,EAAE,IAAI,CAACmB,kBAAuC,EAAEM,iBAAiB,CAAC;KAC/I,MAAM;MACH,OAAOF,sBAAsB,CAACxB,GAAG,EAAErC,UAAU,CAACgE,IAAI,EAAE1B,OAAO,EAAE,IAAI,CAACmB,kBAAwC,EAAEM,iBAAiB,CAAC;;;;AAK1I,MAAMJ,sBAAsB,GAAG,CAC3BM,oBAA4B,EAC5BC,cAAsB,EACtBC,UAAkB,EAClB7B,OAA+B,EAC/B8B,YAAgC,EAChCC,OAAgB;EAEhB,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAACN,oBAAoB,CAAC;EACpD,MAAMT,QAAQ,GAAG,IAAIe,GAAG,CAACL,cAAc,CAAC;;EAGxC,MAAMlC,OAAO,GAAG,CAAAM,OAAO,oBAAPA,OAAO,CAAEN,OAAO,KAAI,EAA4B;EAChE,MAAMwC,oBAAoB,GAAyB;IAC/CC,IAAI,EAAEjB,QAAQ,CAAChB,QAAQ;IACvBQ,IAAI,EAAEQ,QAAQ,CAACR,IAAI;IACnB0B,MAAM,EAAE,SAAS;IACjB5B,IAAI,EAAEwB,cAAc,CAAC9B,QAAQ;IAC7BR,OAAO,EAAEA;GACZ;EAED,IAAIqC,OAAO,EAAE;IACTG,oBAAoB,CAACH,OAAO,GAAGA,OAAO;;EAG1C,IAAID,YAAY,IAAIO,MAAM,CAACC,IAAI,CAACR,YAAY,CAAC,CAACS,MAAM,EAAE;IAClDL,oBAAoB,CAACM,KAAK,GAAG,IAAIC,IAAI,CAACC,KAAK,CAACZ,YAAY,CAAC;;;EAI7D,IAAIa,wBAAwB,GAAW,EAAE;EACzC,IAAId,UAAU,KAAKnE,UAAU,CAACgE,IAAI,EAAE;IAChC,MAAM/B,IAAI,GAAG,CAAAK,OAAO,oBAAPA,OAAO,CAAEL,IAAI,KAAI,EAAE;IAChCgD,wBAAwB,GACpB,qDAAqD,sBAClChD,IAAI,CAAC4C,YAAY,UAC7B5C,MAAM;;EAErB,MAAMiD,qBAAqB,MAAMf,UAAU,CAACgB,WAAW,MAAMb,cAAc,CAACvB,mBAAmB,YAClFuB,cAAc,CAACG,UAAU,GAClC,uBAAuB,GACvBQ,wBAAwB,GACxB,MAAM;EAEV,OAAO,IAAIG,OAAO,CAAsB,CAACC,OAAO,EAAEC,MAAM;IACpD,MAAMC,OAAO,GAAGR,IAAI,CAACQ,OAAO,CAACf,oBAAoB,CAAC;IAElD,IAAIA,oBAAoB,CAACH,OAAO,EAAE;MAC9BkB,OAAO,CAACC,EAAE,CAAC,SAAS,EAAE;QAClBD,OAAO,CAACE,OAAO,EAAE;QACjBH,MAAM,CAAC,IAAII,KAAK,CAAC,kBAAkB,CAAC,CAAC;OACxC,CAAC;;IAGNH,OAAO,CAACI,GAAG,EAAE;;IAGbJ,OAAO,CAACC,EAAE,CAAC,SAAS,EAAE,CAACI,QAAQ,EAAEC,MAAM;MACnC,MAAMC,eAAe,GAAG,CAAAF,QAAQ,oBAARA,QAAQ,CAAE1D,UAAU,KAAIhC,WAAW,CAAC6F,YAAY;MACxE,IAAKD,eAAe,GAAG5F,WAAW,CAAC8F,mBAAmB,IAAMF,eAAe,GAAG5F,WAAW,CAAC+F,iBAAkB,EAAE;QAC1GV,OAAO,CAACE,OAAO,EAAE;QACjBI,MAAM,CAACJ,OAAO,EAAE;QAChBH,MAAM,CAAC,IAAII,KAAK,iDAAiDE,QAAQ,CAAC1D,oCAAoC,CAAA0D,QAAQ,oBAARA,QAAQ,CAAEM,aAAa,KAAI,WAAW,CAAC,CAAC;;MAE1J,IAAI1B,oBAAoB,CAACH,OAAO,EAAE;QAC9BwB,MAAM,CAACM,UAAU,CAAC3B,oBAAoB,CAACH,OAAO,CAAC;QAC/CwB,MAAM,CAACL,EAAE,CAAC,SAAS,EAAE;UACjBD,OAAO,CAACE,OAAO,EAAE;UACjBI,MAAM,CAACJ,OAAO,EAAE;UAChBH,MAAM,CAAC,IAAII,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACxC,CAAC;;;MAING,MAAM,CAACO,KAAK,CAAClB,qBAAqB,CAAC;MAEnC,MAAMmB,IAAI,GAAa,EAAE;MACzBR,MAAM,CAACL,EAAE,CAAC,MAAM,EAAGc,KAAK;QACpBD,IAAI,CAACE,IAAI,CAACD,KAAK,CAAC;OACnB,CAAC;MAEFT,MAAM,CAACL,EAAE,CAAC,KAAK,EAAE;;QAEb,MAAMgB,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC,CAACM,QAAQ,EAAE;;QAGtD,MAAMC,eAAe,GAAGJ,UAAU,CAACK,KAAK,CAAC,MAAM,CAAC;;QAEhD,MAAMC,cAAc,GAAGC,QAAQ,CAACH,eAAe,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjE,MAAMX,aAAa,GAAGU,eAAe,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACnE,KAAK,CAAC,CAAC,CAAC,CAACsE,IAAI,CAAC,GAAG,CAAC;;QAEtE,MAAM/E,IAAI,GAAG2E,eAAe,CAACA,eAAe,CAAC/B,MAAM,GAAG,CAAC,CAAC;;QAGxD,MAAMoC,YAAY,GAAGL,eAAe,CAAClE,KAAK,CAAC,CAAC,EAAEkE,eAAe,CAAC/B,MAAM,GAAG,CAAC,CAAC;;QAGzE,MAAMqC,OAAO,GAAG,IAAIC,GAAG,EAAE;QACzBF,YAAY,CAACG,OAAO,CAAEC,MAAM;;;;;;;UAOxB,MAAMC,cAAc,GAAGD,MAAM,CAACR,KAAK,CAAC,IAAIU,MAAM,CAAC,UAAU,CAAC,CAAC;UAC3D,MAAMC,SAAS,GAAGF,cAAc,CAAC,CAAC,CAAC;UACnC,IAAIG,WAAW,GAAGH,cAAc,CAAC,CAAC,CAAC;;UAGnC,IAAI;YACA,MAAMI,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,CAAC;;YAGtC,IAAIC,MAAM,IAAK,OAAOA,MAAM,KAAK,QAAS,EAAE;cACxCD,WAAW,GAAGC,MAAM;;WAE3B,CAAC,OAAOG,CAAC,EAAE;;;UAIZX,OAAO,CAACY,GAAG,CAACN,SAAS,EAAEC,WAAW,CAAC;SACtC,CAAC;QACF,MAAMzF,OAAO,GAAG2C,MAAM,CAACoD,WAAW,CAACb,OAAO,CAAC;QAE3C,MAAMc,aAAa,GAAGhG,OAAiC;QACvD,MAAMiG,eAAe,GAAGnG,YAAY,CAACC,kBAAkB,CACnDiG,aAAa,EACbE,SAAS,CAACpB,cAAc,EAAEZ,aAAa,EAAE8B,aAAa,EAAE/F,IAAI,CAAM,EAClE6E,cAAc,CACjB;QAED,IAAI,CAAEA,cAAc,GAAG7G,UAAU,CAAC+F,mBAAmB,IAAMc,cAAc,GAAG7G,UAAU,CAACgG,iBAAkB;;QAErGgC,eAAe,CAAChG,IAAI,CAAC,OAAO,CAAC,KAAKxB,SAAS,CAACG,qBAAqB,EAAE;UACnE2E,OAAO,CAACE,OAAO,EAAE;;QAErBJ,OAAO,CAAC4C,eAAe,CAAC;OAC3B,CAAC;MAEFpC,MAAM,CAACL,EAAE,CAAC,OAAO,EAAGc,KAAK;QACrBf,OAAO,CAACE,OAAO,EAAE;QACjBI,MAAM,CAACJ,OAAO,EAAE;QAChBH,MAAM,CAAC,IAAII,KAAK,CAACY,KAAK,CAACK,QAAQ,EAAE,CAAC,CAAC;OACtC,CAAC;KACL,CAAC;IAEFpB,OAAO,CAACC,EAAE,CAAC,OAAO,EAAGc,KAAK;MACtBf,OAAO,CAACE,OAAO,EAAE;MACjBH,MAAM,CAAC,IAAII,KAAK,CAACY,KAAK,CAACK,QAAQ,EAAE,CAAC,CAAC;KACtC,CAAC;GACL,CAAE;AACP,CAAC;AAED,MAAM9C,sBAAsB,GAAG,CAC3BsE,SAAiB,EACjBhE,UAAkB,EAClB7B,OAA+B,EAC/B8B,YAAiC,EACjCC,OAAgB;EAEhB,MAAM+D,aAAa,GAAGjE,UAAU,KAAKnE,UAAU,CAACgE,IAAI;EACpD,MAAM/B,IAAI,GAAW,CAAAK,OAAO,oBAAPA,OAAO,CAAEL,IAAI,KAAI,EAAE;EAExC,MAAMI,GAAG,GAAG,IAAIkC,GAAG,CAAC4D,SAAS,CAAC;EAC9B,MAAMnG,OAAO,GAAG,CAAAM,OAAO,oBAAPA,OAAO,CAAEN,OAAO,KAAI,EAA4B;EAChE,MAAMqG,aAAa,GAAyB;IACxC3D,MAAM,EAAEP,UAAU;IAClBnC,OAAO,EAAEA,OAAO;IAChB,GAAGF,YAAY,CAACM,gBAAgB,CAACC,GAAG;GACvC;EAED,IAAIgC,OAAO,EAAE;IACTgE,aAAa,CAAChE,OAAO,GAAGA,OAAO;;EAGnC,IAAID,YAAY,IAAIO,MAAM,CAACC,IAAI,CAACR,YAAY,CAAC,CAACS,MAAM,EAAE;IAClDwD,aAAa,CAACvD,KAAK,GAAG,IAAIwD,KAAK,CAACtD,KAAK,CAACZ,YAAY,CAAC;;EAGvD,IAAIgE,aAAa,EAAE;;IAEfC,aAAa,CAACrG,OAAO,GAAG;MACpB,GAAGqG,aAAa,CAACrG,OAAO;MACxB,gBAAgB,EAAEC,IAAI,CAAC4C;KAC1B;;EAGL,OAAO,IAAIO,OAAO,CAAqB,CAACC,OAAO,EAAEC,MAAM;IACnD,MAAMC,OAAO,GAAG+C,KAAK,CAAC/C,OAAO,CAAC8C,aAAa,CAAC;IAE5C,IAAIhE,OAAO,EAAE;MACTkB,OAAO,CAACC,EAAE,CAAC,SAAS,EAAE;QAClBD,OAAO,CAACE,OAAO,EAAE;QACjBH,MAAM,CAAC,IAAII,KAAK,CAAC,kBAAkB,CAAC,CAAC;OACxC,CAAC;;IAGN,IAAI0C,aAAa,EAAE;MACf7C,OAAO,CAACa,KAAK,CAACnE,IAAI,CAAC;;IAGvBsD,OAAO,CAACI,GAAG,EAAE;IAEbJ,OAAO,CAACC,EAAE,CAAC,UAAU,EAAGI,QAAQ;MAC5B,MAAM5D,OAAO,GAAG4D,QAAQ,CAAC5D,OAAO;MAChC,MAAME,UAAU,GAAG0D,QAAQ,CAAC1D,UAAoB;MAChD,MAAMgE,aAAa,GAAGN,QAAQ,CAACM,aAAa;MAE5C,MAAMG,IAAI,GAAa,EAAE;MACzBT,QAAQ,CAACJ,EAAE,CAAC,MAAM,EAAGc,KAAK;QACtBD,IAAI,CAACE,IAAI,CAACD,KAAK,CAAC;OACnB,CAAC;MAEFV,QAAQ,CAACJ,EAAE,CAAC,KAAK,EAAE;;QAEf,MAAMvD,IAAI,GAAGwE,MAAM,CAACC,MAAM,CAAC,CAAC,GAAGL,IAAI,CAAC,CAAC,CAACM,QAAQ,EAAE;QAEhD,MAAMqB,aAAa,GAAGhG,OAAiC;QACvD,MAAMiG,eAAe,GAAGnG,YAAY,CAACC,kBAAkB,CACnDiG,aAAa,EACbE,SAAS,CAAChG,UAAU,EAAEgE,aAAa,EAAE8B,aAAa,EAAE/F,IAAI,CAAM,EAC9DC,UAAU,CACb;QAED,IAAI,CAAEA,UAAU,GAAGjC,UAAU,CAAC+F,mBAAmB,IAAM9D,UAAU,GAAGjC,UAAU,CAACgG,iBAAkB;;QAE7FgC,eAAe,CAAChG,IAAI,CAAC,OAAO,CAAC,KAAKxB,SAAS,CAACG,qBAAqB,EAAE;UACnE2E,OAAO,CAACE,OAAO,EAAE;;QAErBJ,OAAO,CAAC4C,eAAe,CAAC;OAC3B,CAAC;KACL,CAAC;IAEF1C,OAAO,CAACC,EAAE,CAAC,OAAO,EAAGc,KAAK;MACtBf,OAAO,CAACE,OAAO,EAAE;MACjBH,MAAM,CAAC,IAAII,KAAK,CAACY,KAAK,CAACK,QAAQ,EAAE,CAAC,CAAC;KACtC,CAAC;GACL,CAAC;AACN,CAAC;AAED;;;;;;;;AAQA,MAAMuB,SAAS,GAAG,CAAChG,UAAkB,EAAEgE,aAAiC,EAAElE,OAA+B,EAAEC,IAAY;;;;;;;;EASnH,IAAIsG,UAAU;EACd,IAAI;IACAA,UAAU,GAAGZ,IAAI,CAACC,KAAK,CAAC3F,IAAI,CAAC;GAChC,CAAC,OAAOuG,KAAK,EAAE;IACZ,IAAIC,SAAS;IACb,IAAIC,sBAAsB;IAC1B,IAAKxG,UAAU,IAAIjC,UAAU,CAAC0I,wBAAwB,IAAMzG,UAAU,IAAIjC,UAAU,CAAC2I,sBAAuB,EAAE;MAC1GH,SAAS,GAAG,cAAc;MAC1BC,sBAAsB,GAAG,UAAU;KACtC,MAAM,IAAKxG,UAAU,IAAIjC,UAAU,CAAC4I,wBAAwB,IAAM3G,UAAU,IAAIjC,UAAU,CAAC6I,sBAAuB,EAAE;MACjHL,SAAS,GAAG,cAAc;MAC1BC,sBAAsB,GAAG,UAAU;KACtC,MAAM;MACHD,SAAS,GAAG,eAAe;MAC3BC,sBAAsB,GAAG,YAAY;;IAGzCH,UAAU,GAAG;MACTC,KAAK,EAAEC,SAAS;MAChBM,iBAAiB,KAAKL,4DAA4DxG,oCAAoCgE,aAAa,IAAI,uBAAuByB,IAAI,CAACqB,SAAS,CAAChH,OAAO;KACvL;;EAGL,OAAOuG,UAAU;AACrB,CAAC;;ACvVD;;;;AAKA,AAyGA,MAAMU,oBAAoB,GAA8B;EACpDC,QAAQ,EAAEzI,WAAS,CAAC0I,YAAY;EAChCC,SAAS,EAAE3I,WAAS,CAAC4I,iBAAiB;EACtCC,YAAY,EAAE7I,WAAS,CAAC0I,YAAY;EACpCI,eAAe,EAAE9I,WAAS,CAAC0I,YAAY;EACvCK,iBAAiB,EAAE;IACfC,UAAU,EAAEhJ,WAAS,CAAC0I,YAAY;IAClCO,UAAU,EAAEjJ,WAAS,CAAC0I,YAAY;IAClCQ,GAAG,EAAElJ,WAAS,CAAC0I;GAClB;EACDS,gBAAgB,EAAE,EAAE;EACpBC,sBAAsB,EAAEpJ,WAAS,CAAC0I,YAAY;EAC9CW,iBAAiB,EAAErJ,WAAS,CAAC0I,YAAY;EACzCY,kBAAkB,EAAE,EAAE;EACtBC,YAAY,EAAEC,YAAY,CAACC,GAAG;EAC9BC,iBAAiB,EAAE;IACfC,kBAAkB,EAAEC,kBAAkB,CAACC,IAAI;IAC3CC,MAAM,EAAE9J,WAAS,CAAC0I;GACrB;EACDqB,0BAA0B,EAAE;CAC/B;AAED,MAAMC,qBAAqB,GAAiB;EACxCC,yBAAyB,EAAE;CAC9B;AAED,MAAMC,sBAAsB,GAAkB;EAC1CC,cAAc,EAAE;;GAEf;EACDC,iBAAiB,EAAE,KAAK;EACxBC,QAAQ,EAAEC,QAAQ,CAACC;CACtB;AAED,MAAMC,sBAAsB,GAAgC;EACxDC,aAAa,EAAEP,sBAAsB;EACrCQ,aAAa,eAAE,IAAI7H,UAAU,EAAE;EAC/BE,QAAQ,EAAE/C,WAAS,CAAC0I,YAAY;EAChC1F,kBAAkB,EAAE;CACvB;AAED,MAAM2H,yBAAyB,GAAmC;EAC9DC,WAAW,EAAE;IACTC,OAAO,EAAE7K,WAAS,CAAC0I,YAAY;IAC/BoC,UAAU,EAAE9K,WAAS,CAAC0I;;CAE7B;AAUD;;;;;;;;;;;AAWA,SAAgBqC,qBAAqB,CAAC;EAClCpI,IAAI;EACJqI,MAAM;EACNC,KAAK;EACLC,MAAM;EACNC;CACY;EACZ,MAAMC,aAAa,GAAgC;IAC/C,GAAGZ,sBAAsB;IACzBE,aAAa,EAAE,IAAI7H,UAAU,CAACqI,MAAM,oBAANA,MAAM,CAAEnI,QAAQ,EAAGmI,MAAM,oBAANA,MAAM,CAAElI,kBAA2D,CAAC;IACrHyH,aAAa,EAAE,CAAAS,MAAM,oBAANA,MAAM,CAAET,aAAa,KAAIP;GAC3C;EAED,OAAO;IACHvH,IAAI,EAAE;MAAE,GAAG6F,oBAAoB;MAAE,GAAG7F;KAAM;IAC1CqI,MAAM,EAAE;MAAE,GAAGA;KAAO;IACpBC,KAAK,EAAE;MAAE,GAAGjB,qBAAqB;MAAE,GAAGiB;KAAO;IAC7CC,MAAM,EAAE;MAAE,GAAGE,aAAa;MAAE,GAAGF;KAAQ;IACvCC,SAAS,EAAE;MAAE,GAAGR,yBAAyB;MAAE,GAAGQ;;GACjD;AACL;;ACrMA;;;;AAMA,MAEaE,aAAa;;;;;;EAMtBC,YAAY;IACR,OAAOC,EAAM,EAAE;;;;;;EAOnBC,MAAM,CAACC,IAAY;IACf,MAAMC,SAAS,GAAG,4EAA4E;IAC9F,OAAOA,SAAS,CAACC,IAAI,CAACF,IAAI,CAAC;;;;ACxBnC;;;;AAKA,MAEaG,aAAa;;;;;;;EAOtB,OAAOC,YAAY,CAACC,GAAW,EAAEC,QAAyB;IACtD,OAAO/F,MAAM,CAACgG,IAAI,CAACF,GAAG,EAAEC,QAAQ,CAAC,CAAC7F,QAAQ,CAAC,QAAQ,CAAC;;;;;;EAOxD,OAAO+F,eAAe,CAACH,GAAW,EAAEC,QAAyB;IACzD,OAAOH,aAAa,CAACC,YAAY,CAACC,GAAG,EAAEC,QAAQ,CAAC,CAC3CG,OAAO,CAAC,IAAI,EAAElM,WAAS,CAAC0I,YAAY,CAAC,CACrCwD,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;;;;;;;;EAS5B,OAAOC,YAAY,CAACC,SAAiB;IACjC,OAAOpG,MAAM,CAACgG,IAAI,CAACI,SAAS,EAAE,QAAQ,CAAC,CAAClG,QAAQ,CAAC,MAAM,CAAC;;;;;EAM5D,OAAOmG,eAAe,CAACD,SAAiB;IACpC,IAAIN,GAAG,GAAGM,SAAS,CAACF,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACzD,OAAOJ,GAAG,CAAC1H,MAAM,GAAG,CAAC,EAAE;MACnB0H,GAAG,IAAI,GAAG;;IAEd,OAAOF,aAAa,CAACO,YAAY,CAACL,GAAG,CAAC;;;;AC/C9C;;;;AAKA,MAGaQ,SAAS;;;;;EAKlBC,MAAM,CAACC,MAAc;IACjB,OAAOC,MAAM,CACRC,UAAU,CAAC9M,IAAI,CAACC,MAAM,CAAC,CACvB8M,MAAM,CAACH,MAAM,CAAC,CACdI,MAAM,EAAE;;;;ACjBrB;;;;AAKA,AAMA;;;AAGA,MAAaC,aAAa;EAGtB/J;IACI,IAAI,CAACgK,SAAS,GAAG,IAAIR,SAAS,EAAE;;;;;;EAMpC,MAAMS,iBAAiB;IACnB,MAAMC,QAAQ,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAC5C,MAAMC,SAAS,GAAG,IAAI,CAACC,iCAAiC,CAACH,QAAQ,CAAC;IAClE,OAAO;MAAEA,QAAQ;MAAEE;KAAW;;;;;EAM1BD,oBAAoB;IACxB,MAAMG,OAAO,GAAG,EAAE;IAClB,MAAMC,SAAS,GAAG,GAAG,GAAI,GAAG,GAAGvN,OAAO,CAACC,UAAU,CAACqE,MAAO;IACzD,OAAOgJ,OAAO,CAAChJ,MAAM,IAAIzE,iBAAiB,EAAE;MACxC,MAAM2N,IAAI,GAAGb,MAAM,CAACc,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,IAAID,IAAI,IAAID,SAAS,EAAE;;;;;QAKnB;;MAEJ,MAAMG,KAAK,GAAGF,IAAI,GAAGxN,OAAO,CAACC,UAAU,CAACqE,MAAM;MAC9CgJ,OAAO,CAACtH,IAAI,CAAChG,OAAO,CAACC,UAAU,CAACyN,KAAK,CAAC,CAAC;;IAE3C,MAAMR,QAAQ,GAAWI,OAAO,CAAC7G,IAAI,CAACvG,WAAS,CAAC0I,YAAY,CAAC;IAC7D,OAAOkD,aAAa,CAACK,eAAe,CAACe,QAAQ,CAAC;;;;;;EAO1CG,iCAAiC,CAACM,YAAoB;IAC1D,OAAO7B,aAAa,CAACK,eAAe,CAChC,IAAI,CAACa,SAAS,CAACP,MAAM,CAACkB,YAAY,CAAC,CAACvH,QAAQ,CAAC,QAAQ,CAAC,EACtD,QAAQ,CACX;;;;AC5DT;;;;AAMA,AAKA;;;;;AAKA,MAAawH,cAAc;EAKvB5K;;IAEI,IAAI,CAAC6K,aAAa,GAAG,IAAId,aAAa,EAAE;IACxC,IAAI,CAACe,aAAa,GAAG,IAAIvC,aAAa,EAAE;IACxC,IAAI,CAACyB,SAAS,GAAG,IAAIR,SAAS,EAAE;;;;;;EAOpCuB,aAAa;IACT,OAAO,IAAI,CAACD,aAAa,CAACtC,YAAY,EAAE;;;;;;EAO5CO,YAAY,CAACiC,KAAa;IACtB,OAAOlC,aAAa,CAACC,YAAY,CAACiC,KAAK,CAAC;;;;;;EAO5C3B,YAAY,CAAC2B,KAAa;IACtB,OAAOlC,aAAa,CAACO,YAAY,CAAC2B,KAAK,CAAC;;;;;EAM5Cf,iBAAiB;IACb,OAAO,IAAI,CAACY,aAAa,CAACZ,iBAAiB,EAAE;;;;;EAMjDgB,sBAAsB;IAClB,MAAM,IAAI9I,KAAK,CAAC,yBAAyB,CAAC;;;;;;EAO9C+I,qBAAqB;IACjB,MAAM,IAAI/I,KAAK,CAAC,yBAAyB,CAAC;;;;;EAM9CgJ,aAAa;IACT,MAAM,IAAIhJ,KAAK,CAAC,yBAAyB,CAAC;;;;;EAM9CiJ,OAAO;IACH,MAAM,IAAIjJ,KAAK,CAAC,yBAAyB,CAAC;;;;;EAM9C,MAAMkJ,UAAU,CAACC,SAAiB;IAC9B,OAAOxC,aAAa,CAACK,eAAe,CAChC,IAAI,CAACa,SAAS,CAACP,MAAM,CAAC6B,SAAS,CAAC,CAAClI,QAAQ,CAAC,QAAQ,CAAC,EACnD,QAAQ,CACX;;;;AC/FT;;;;AAKA,AAGA;;;AAGA,MAAamI,YAAY;;;;;EAKrB,OAAOC,mBAAmB,CAACC,QAAgB;IACvC,MAAMC,iBAAiB,GAAGC,WAAW,CAACC,OAAO,CAACH,QAAQ,CAAC,GACjD,EAAE,GACFrH,IAAI,CAACC,KAAK,CAACoH,QAAQ,CAAC;IAC1B,OAAOC,iBAAiB;;;;;;EAO5B,OAAOG,mBAAmB,CAACC,QAAiD;IACxE,MAAMC,cAAc,GAAiB,EAAE;IACvC,IAAID,QAAQ,EAAE;MACV1K,MAAM,CAACC,IAAI,CAACyK,QAAQ,CAAC,CAACE,GAAG,CAAC,UAAUC,GAAG;QACnC,MAAMC,aAAa,GAAGJ,QAAQ,CAACG,GAAG,CAAC;QACnC,MAAME,SAAS,GAAG;UACdC,aAAa,EAAEF,aAAa,CAACG,eAAe;UAC5CC,WAAW,EAAEJ,aAAa,CAACI,WAAW;UACtCC,KAAK,EAAEL,aAAa,CAACK,KAAK;UAC1BC,cAAc,EAAEN,aAAa,CAACO,gBAAgB;UAC9C9M,QAAQ,EAAEuM,aAAa,CAACvM,QAAQ;UAChC+M,aAAa,EAAER,aAAa,CAACS,cAAc;UAC3CC,IAAI,EAAEV,aAAa,CAACU,IAAI;UACxBC,UAAU,EAAEX,aAAa,CAACY,WAAW;UACrCC,oBAAoB,EAAEb,aAAa,CAACc,sBAAsB;UAC1DC,mBAAmB,EAAEf,aAAa,CAACgB;SACtC;QACD,MAAMC,OAAO,GAAkB,IAAIC,aAAa,EAAE;QAClDC,YAAY,CAACC,QAAQ,CAACH,OAAO,EAAEhB,SAAS,CAAC;QACzCJ,cAAc,CAACE,GAAG,CAAC,GAAGkB,OAAO;OAChC,CAAC;;IAGN,OAAOpB,cAAc;;;;;;EAOzB,OAAOwB,mBAAmB,CAACC,QAAiD;IACxE,MAAMC,SAAS,GAAiB,EAAE;IAClC,IAAID,QAAQ,EAAE;MACVpM,MAAM,CAACC,IAAI,CAACmM,QAAQ,CAAC,CAACxB,GAAG,CAAC,UAAUC,GAAG;QACnC,MAAMyB,aAAa,GAAGF,QAAQ,CAACvB,GAAG,CAAC;QACnC,MAAM0B,SAAS,GAAG;UACdvB,aAAa,EAAEsB,aAAa,CAACrB,eAAe;UAC5CC,WAAW,EAAEoB,aAAa,CAACpB,WAAW;UACtCsB,cAAc,EAAEF,aAAa,CAACG,eAAe;UAC7ClI,QAAQ,EAAE+H,aAAa,CAACI,SAAS;UACjCC,MAAM,EAAEL,aAAa,CAACK,MAAM;UAC5BxB,KAAK,EAAEmB,aAAa,CAACnB;SACxB;QACD,MAAMyB,OAAO,GAAkB,IAAIC,aAAa,EAAE;QAClDZ,YAAY,CAACC,QAAQ,CAACU,OAAO,EAAEL,SAAS,CAAC;QACzCF,SAAS,CAACxB,GAAG,CAAC,GAAG+B,OAAO;OAC3B,CAAC;;IAEN,OAAOP,SAAS;;;;;;EAOpB,OAAOS,uBAAuB,CAACC,YAAyD;IACpF,MAAMC,SAAS,GAAqB,EAAE;IACtC,IAAID,YAAY,EAAE;MACd/M,MAAM,CAACC,IAAI,CAAC8M,YAAY,CAAC,CAACnC,GAAG,CAAC,UAAUC,GAAG;QACvC,MAAMoC,YAAY,GAAGF,YAAY,CAAClC,GAAG,CAAC;QACtC,MAAMqC,QAAQ,GAAG;UACblC,aAAa,EAAEiC,YAAY,CAAChC,eAAe;UAC3CC,WAAW,EAAE+B,YAAY,CAAC/B,WAAW;UACrCsB,cAAc,EAAES,YAAY,CAACR,eAAe;UAC5ClI,QAAQ,EAAE0I,YAAY,CAACP,SAAS;UAChCC,MAAM,EAAEM,YAAY,CAACN,MAAM;UAC3BxB,KAAK,EAAE8B,YAAY,CAAC9B,KAAK;UACzBgC,MAAM,EAAEF,YAAY,CAACE,MAAM;UAC3BC,QAAQ,EAAEH,YAAY,CAACI,SAAS;UAChCC,SAAS,EAAEL,YAAY,CAACM,UAAU;UAClCC,iBAAiB,EAAEP,YAAY,CAACQ,mBAAmB;UACnDC,SAAS,EAAET,YAAY,CAACU,UAAU;UAClCC,KAAK,EAAEX,YAAY,CAACY,MAAM;UAC1BC,SAAS,EAAEb,YAAY,CAACc,UAAU;UAClCC,eAAe,EAAEf,YAAY,CAACe,eAAe;UAC7CC,mBAAmB,EAAEhB,YAAY,CAACgB,mBAAmB;UACrDC,iBAAiB,EAAEjB,YAAY,CAACiB;SACnC;QACD,MAAMC,WAAW,GAAsB,IAAIC,iBAAiB,EAAE;QAC9DnC,YAAY,CAACC,QAAQ,CAACiC,WAAW,EAAEjB,QAAQ,CAAC;QAC5CF,SAAS,CAACnC,GAAG,CAAC,GAAGsD,WAAW;OAC/B,CAAC;;IAGN,OAAOnB,SAAS;;;;;;EAOpB,OAAOqB,wBAAwB,CAACC,aAA2D;IACvF,MAAMC,SAAS,GAAsB,EAAE;IACvC,IAAID,aAAa,EAAE;MACftO,MAAM,CAACC,IAAI,CAACqO,aAAa,CAAC,CAAC1D,GAAG,CAAC,UAAUC,GAAG;QACxC,MAAM2D,YAAY,GAAGF,aAAa,CAACzD,GAAG,CAAC;QACvC,MAAM4D,QAAQ,GAAG;UACbzD,aAAa,EAAEwD,YAAY,CAACvD,eAAe;UAC3CC,WAAW,EAAEsD,YAAY,CAACtD,WAAW;UACrCsB,cAAc,EAAEgC,YAAY,CAAC/B,eAAe;UAC5ClI,QAAQ,EAAEiK,YAAY,CAAC9B,SAAS;UAChCC,MAAM,EAAE6B,YAAY,CAAC7B,MAAM;UAC3B+B,QAAQ,EAAEF,YAAY,CAACG,SAAS;UAChCxB,MAAM,EAAEqB,YAAY,CAACrB,MAAM;UAC3BhC,KAAK,EAAEqD,YAAY,CAACrD;SACvB;QACD,MAAMyD,YAAY,GAAuB,IAAIC,kBAAkB,EAAE;QACjE5C,YAAY,CAACC,QAAQ,CAAC0C,YAAY,EAAEH,QAAQ,CAAC;QAC7CF,SAAS,CAAC1D,GAAG,CAAC,GAAG+D,YAAY;OAChC,CAAC;;IAGN,OAAOL,SAAS;;;;;;EAOpB,OAAOO,sBAAsB,CAACC,WAAwD;IAClF,MAAMC,kBAAkB,GAAqB,EAAE;IAC/C,IAAID,WAAW,EAAE;MACb/O,MAAM,CAACC,IAAI,CAAC8O,WAAW,CAAC,CAACnE,GAAG,CAAC,UAAUC,GAAG;QACtC,MAAMoE,cAAc,GAAGF,WAAW,CAAClE,GAAG,CAAC;QACvC,MAAMqE,SAAS,GAAG;UACd3K,QAAQ,EAAE0K,cAAc,CAACvC,SAAS;UAClCxB,WAAW,EAAE+D,cAAc,CAAC/D,WAAW;UACvCwD,QAAQ,EAAEO,cAAc,CAACN;SAC5B;QACD,MAAMQ,GAAG,GAAsB,IAAIC,iBAAiB,EAAE;QACtDnD,YAAY,CAACC,QAAQ,CAACiD,GAAG,EAAED,SAAS,CAAC;QACrCF,kBAAkB,CAACnE,GAAG,CAAC,GAAGsE,GAAG;OAChC,CAAC;;IAGN,OAAOH,kBAAkB;;;;;;EAO7B,OAAOK,mBAAmB,CAACC,SAAoB;IAC3C,OAAO;MACH5E,QAAQ,EAAE4E,SAAS,CAACC,OAAO,GACrB,IAAI,CAAC9E,mBAAmB,CAAC6E,SAAS,CAACC,OAAO,CAAC,GAC3C,EAAE;MACRnD,QAAQ,EAAEkD,SAAS,CAACE,OAAO,GACrB,IAAI,CAACrD,mBAAmB,CAACmD,SAAS,CAACE,OAAO,CAAC,GAC3C,EAAE;MACRzC,YAAY,EAAEuC,SAAS,CAACG,WAAW,GAC7B,IAAI,CAAC3C,uBAAuB,CAACwC,SAAS,CAACG,WAAW,CAAC,GACnD,EAAE;MACRnB,aAAa,EAAEgB,SAAS,CAACI,YAAY,GAC/B,IAAI,CAACrB,wBAAwB,CAACiB,SAAS,CAACI,YAAY,CAAC,GACrD,EAAE;MACRX,WAAW,EAAEO,SAAS,CAACK,WAAW,GAC5B,IAAI,CAACb,sBAAsB,CAACQ,SAAS,CAACK,WAAW,CAAC,GAClD;KACT;;;;AC1LT;;;;AAQA,MAAaC,UAAU;;;;;EAKnB,OAAOC,iBAAiB,CAACnO,IAAe;IACpC,OAAOsB,IAAI,CAACqB,SAAS,CAAC3C,IAAI,CAAC;;;;;;EAO/B,OAAOoO,iBAAiB,CAACC,QAAsB;IAC3C,MAAMrF,QAAQ,GAA4C,EAAE;IAC5D1K,MAAM,CAACC,IAAI,CAAC8P,QAAQ,CAAC,CAACnF,GAAG,CAAC,UAAUC,GAAG;MACnC,MAAMmF,aAAa,GAAGD,QAAQ,CAAClF,GAAG,CAAC;MACnCH,QAAQ,CAACG,GAAG,CAAC,GAAG;QACZI,eAAe,EAAE+E,aAAa,CAAChF,aAAa;QAC5CE,WAAW,EAAE8E,aAAa,CAAC9E,WAAW;QACtCC,KAAK,EAAE6E,aAAa,CAAC7E,KAAK;QAC1BE,gBAAgB,EAAE2E,aAAa,CAAC5E,cAAc;QAC9C7M,QAAQ,EAAEyR,aAAa,CAACzR,QAAQ;QAChCgN,cAAc,EAAEyE,aAAa,CAAC1E,aAAa;QAC3CE,IAAI,EAAEwE,aAAa,CAACxE,IAAI;QACxBE,WAAW,EAAEsE,aAAa,CAACvE,UAAU;QACrCG,sBAAsB,EAAEoE,aAAa,CAACrE,oBAAoB;QAC1DG,qBAAqB,EAAEkE,aAAa,CAACnE;OACxC;KACJ,CAAC;IAEF,OAAOnB,QAAQ;;;;;;EAOnB,OAAOuF,iBAAiB,CAACC,QAAsB;IAC3C,MAAM9D,QAAQ,GAA4C,EAAE;IAC5DpM,MAAM,CAACC,IAAI,CAACiQ,QAAQ,CAAC,CAACtF,GAAG,CAAC,UAAUC,GAAG;MACnC,MAAMsF,SAAS,GAAGD,QAAQ,CAACrF,GAAG,CAAC;MAC/BuB,QAAQ,CAACvB,GAAG,CAAC,GAAG;QACZI,eAAe,EAAEkF,SAAS,CAACnF,aAAa;QACxCE,WAAW,EAAEiF,SAAS,CAACjF,WAAW;QAClCuB,eAAe,EAAE0D,SAAS,CAAC3D,cAAc;QACzCE,SAAS,EAAEyD,SAAS,CAAC5L,QAAQ;QAC7BoI,MAAM,EAAEwD,SAAS,CAACxD,MAAM;QACxBxB,KAAK,EAAEgF,SAAS,CAAChF;OACpB;KACJ,CAAC;IAEF,OAAOiB,QAAQ;;;;;;EAOnB,OAAOgE,qBAAqB,CAACC,OAAyB;IAClD,MAAMtD,YAAY,GAAgD,EAAE;IACpE/M,MAAM,CAACC,IAAI,CAACoQ,OAAO,CAAC,CAACzF,GAAG,CAAC,UAAUC,GAAG;MAClC,MAAMyF,QAAQ,GAAGD,OAAO,CAACxF,GAAG,CAAC;MAC7BkC,YAAY,CAAClC,GAAG,CAAC,GAAG;QAChBI,eAAe,EAAEqF,QAAQ,CAACtF,aAAa;QACvCE,WAAW,EAAEoF,QAAQ,CAACpF,WAAW;QACjCuB,eAAe,EAAE6D,QAAQ,CAAC9D,cAAc;QACxCE,SAAS,EAAE4D,QAAQ,CAAC/L,QAAQ;QAC5BoI,MAAM,EAAE2D,QAAQ,CAAC3D,MAAM;QACvBxB,KAAK,EAAEmF,QAAQ,CAACnF,KAAK;QACrBgC,MAAM,EAAEmD,QAAQ,CAACnD,MAAM;QACvBE,SAAS,EAAEiD,QAAQ,CAAClD,QAAQ;QAC5BG,UAAU,EAAE+C,QAAQ,CAAChD,SAAS;QAC9BG,mBAAmB,EAAE6C,QAAQ,CAAC9C,iBAAiB;QAC/CG,UAAU,EAAE2C,QAAQ,CAAC5C,SAAS;QAC9BG,MAAM,EAAEyC,QAAQ,CAAC1C,KAAK;QACtBG,UAAU,EAAEuC,QAAQ,CAACxC,SAAS;QAC9BE,eAAe,EAAEsC,QAAQ,CAACtC,eAAe;QACzCC,mBAAmB,EAAEqC,QAAQ,CAACrC,mBAAmB;QACjDC,iBAAiB,EAAEoC,QAAQ,CAACpC;OAC/B;KACJ,CAAC;IAEF,OAAOnB,YAAY;;;;;;EAOvB,OAAOwD,sBAAsB,CAACC,OAA0B;IACpD,MAAMlC,aAAa,GAAiD,EAAE;IACtEtO,MAAM,CAACC,IAAI,CAACuQ,OAAO,CAAC,CAAC5F,GAAG,CAAC,UAAUC,GAAG;MAClC,MAAM4F,QAAQ,GAAGD,OAAO,CAAC3F,GAAG,CAAC;MAC7ByD,aAAa,CAACzD,GAAG,CAAC,GAAG;QACjBI,eAAe,EAAEwF,QAAQ,CAACzF,aAAa;QACvCE,WAAW,EAAEuF,QAAQ,CAACvF,WAAW;QACjCuB,eAAe,EAAEgE,QAAQ,CAACjE,cAAc;QACxCE,SAAS,EAAE+D,QAAQ,CAAClM,QAAQ;QAC5BoI,MAAM,EAAE8D,QAAQ,CAAC9D,MAAM;QACvBgC,SAAS,EAAE8B,QAAQ,CAAC/B,QAAQ;QAC5BvB,MAAM,EAAEsD,QAAQ,CAACtD,MAAM;QACvBhC,KAAK,EAAEsF,QAAQ,CAACtF;OACnB;KACJ,CAAC;IAEF,OAAOmD,aAAa;;;;;;EAOxB,OAAOoC,oBAAoB,CAACC,SAA2B;IACnD,MAAM5B,WAAW,GAAgD,EAAE;IACnE/O,MAAM,CAACC,IAAI,CAAC0Q,SAAS,CAAC,CAAC/F,GAAG,CAAC,UAAUC,GAAG;MACpC,MAAM+F,UAAU,GAAGD,SAAS,CAAC9F,GAAG,CAAC;MACjCkE,WAAW,CAAClE,GAAG,CAAC,GAAG;QACf6B,SAAS,EAAEkE,UAAU,CAACrM,QAAQ;QAC9B2G,WAAW,EAAE0F,UAAU,CAAC1F,WAAW;QACnCyD,SAAS,EAAEiC,UAAU,CAAClC;OACzB;KACJ,CAAC;IAEF,OAAOK,WAAW;;;;;;EAOtB,OAAO8B,iBAAiB,CAACC,UAAyB;IAC9C,OAAO;MACHvB,OAAO,EAAE,IAAI,CAACO,iBAAiB,CAACgB,UAAU,CAACpG,QAAQ,CAAC;MACpD8E,OAAO,EAAE,IAAI,CAACS,iBAAiB,CAACa,UAAU,CAAC1E,QAAQ,CAAC;MACpDqD,WAAW,EAAE,IAAI,CAACW,qBAAqB,CAACU,UAAU,CAAC/D,YAAY,CAAC;MAChE2C,YAAY,EAAE,IAAI,CAACa,sBAAsB,CAACO,UAAU,CAACxC,aAAa,CAAC;MACnEqB,WAAW,EAAE,IAAI,CAACe,oBAAoB,CAACI,UAAU,CAAC/B,WAAW;KAChE;;;;AClJT;;;;AAKA,AAoBA;;;;AAIA,MAAagC,WAAY,SAAQ9E,YAAY;EAMzCrN,YAAYoS,MAAc,EAAEzM,QAAgB,EAAE0M,UAAmB;IAC7D,KAAK,CAAC1M,QAAQ,EAAE0M,UAAU,EAAED,MAAM,CAAC;IAJ/B,UAAK,GAAiB,EAAE;IACxB,mBAAc,GAAoB,EAAE;IAIxC,IAAI,CAACA,MAAM,GAAGA,MAAM;;;;;;EAOxBE,qBAAqB,CAACC,IAAgB;IAClC,IAAI,CAACC,cAAc,CAACxP,IAAI,CAACuP,IAAI,CAAC;;;;;EAMlCE,UAAU;IACN,IAAI,CAACD,cAAc,CAAC3O,OAAO,CAAC0O,IAAI,IAAIA,IAAI,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;;;;;;EAOxDC,oBAAoB,CAACxK,KAAmB;IACpC,MAAMyK,aAAa,GAAkB;MACjC9G,QAAQ,EAAE,EAAE;MACZ0B,QAAQ,EAAE,EAAE;MACZW,YAAY,EAAE,EAAE;MAChBuB,aAAa,EAAE,EAAE;MACjBS,WAAW,EAAE;KAChB;IAED,KAAK,MAAMlE,GAAG,IAAI9D,KAAK,EAAE;MACrB,IAAIA,KAAK,CAAC8D,GAAa,CAAC,YAAYmB,aAAa,EAAE;QAC/CwF,aAAa,CAAC9G,QAAQ,CAACG,GAAG,CAAC,GAAG9D,KAAK,CAAC8D,GAAG,CAAkB;OAC5D,MAAM,IAAI9D,KAAK,CAAC8D,GAAG,CAAC,YAAYgC,aAAa,EAAE;QAC5C2E,aAAa,CAACpF,QAAQ,CAACvB,GAAG,CAAC,GAAG9D,KAAK,CAAC8D,GAAG,CAAkB;OAC5D,MAAM,IAAI9D,KAAK,CAAC8D,GAAG,CAAC,YAAYuD,iBAAiB,EAAE;QAChDoD,aAAa,CAACzE,YAAY,CAAClC,GAAG,CAAC,GAAG9D,KAAK,CAAC8D,GAAG,CAAsB;OACpE,MAAM,IAAI9D,KAAK,CAAC8D,GAAG,CAAC,YAAYgE,kBAAkB,EAAE;QACjD2C,aAAa,CAAClD,aAAa,CAACzD,GAAG,CAAC,GAAG9D,KAAK,CAAC8D,GAAG,CAAuB;OACtE,MAAM,IAAI9D,KAAK,CAAC8D,GAAG,CAAC,YAAYuE,iBAAiB,EAAE;QAChDoC,aAAa,CAACzC,WAAW,CAAClE,GAAG,CAAC,GAAG9D,KAAK,CAAC8D,GAAG,CAAsB;OACnE,MAAM;QACH;;;IAIR,OAAO2G,aAAa;;;;;;EAOxBC,oBAAoB,CAACD,aAA4B;;IAG7C,IAAIzK,KAAK,GAAG,IAAI,CAAC2K,QAAQ,EAAE;IAE3B3K,KAAK,GAAG;MACJ,GAAGA,KAAK;MACR,GAAGyK,aAAa,CAAC9G,QAAQ;MACzB,GAAG8G,aAAa,CAACpF,QAAQ;MACzB,GAAGoF,aAAa,CAACzE,YAAY;MAC7B,GAAGyE,aAAa,CAAClD,aAAa;MAC9B,GAAGkD,aAAa,CAACzC;KACpB;;IAGD,OAAOhI,KAAK;;;;;EAMhB4K,gBAAgB;IACZ,IAAI,CAACX,MAAM,CAACY,KAAK,CAAC,yBAAyB,CAAC;;IAG5C,MAAMJ,aAAa,GAAG,IAAI,CAACD,oBAAoB,CAAC,IAAI,CAACG,QAAQ,EAAE,CAAC;IAChE,OAAOF,aAAa;;;;;;EAOxBK,gBAAgB,CAACL,aAA4B;IACzC,IAAI,CAACR,MAAM,CAACY,KAAK,CAAC,yBAAyB,CAAC;;IAG5C,MAAM7K,KAAK,GAAG,IAAI,CAAC0K,oBAAoB,CAACD,aAAa,CAAC;IACtD,IAAI,CAACM,QAAQ,CAAC/K,KAAK,CAAC;IAEpB,IAAI,CAACsK,UAAU,EAAE;;;;;EAMrBK,QAAQ;IACJ,IAAI,CAACV,MAAM,CAACY,KAAK,CAAC,+BAA+B,CAAC;IAClD,OAAO,IAAI,CAAC7K,KAAK;;;;;;EAOrB+K,QAAQ,CAAC/K,KAAmB;IACxB,IAAI,CAACiK,MAAM,CAACY,KAAK,CAAC,+BAA+B,CAAC;IAClD,IAAI,CAAC7K,KAAK,GAAGA,KAAK;;IAGlB,IAAI,CAACsK,UAAU,EAAE;;;;;;EAOrBU,OAAO,CAAClH,GAAW;IACf,IAAI,CAACmG,MAAM,CAACgB,QAAQ,cAAcnH,KAAK,CAAC;;IAGxC,MAAM9D,KAAK,GAAG,IAAI,CAAC2K,QAAQ,EAAE;IAC7B,OAAO3K,KAAK,CAAC8D,GAAG,CAAC;;;;;;;EAQrBoH,OAAO,CAACpH,GAAW,EAAEqH,KAAqB;IACtC,IAAI,CAAClB,MAAM,CAACgB,QAAQ,cAAcnH,KAAK,CAAC;;IAGxC,MAAM9D,KAAK,GAAG,IAAI,CAAC2K,QAAQ,EAAE;IAC7B3K,KAAK,CAAC8D,GAAG,CAAC,GAAGqH,KAAK;;IAGlB,IAAI,CAACJ,QAAQ,CAAC/K,KAAK,CAAC;;EAGxBoL,cAAc;IACV,MAAMX,aAAa,GAAG,IAAI,CAACG,gBAAgB,EAAE;IAC7C,MAAMS,WAAW,GAAGpS,MAAM,CAACC,IAAI,CAACuR,aAAa,CAAC9G,QAAQ,CAAC;IAEvD,OAAO0H,WAAW;;EAGtBC,YAAY;IACR,MAAMb,aAAa,GAAG,IAAI,CAACG,gBAAgB,EAAE;IAC7C,MAAMW,SAAS,GAAG;MACd1F,OAAO,EAAE5M,MAAM,CAACC,IAAI,CAACuR,aAAa,CAACpF,QAAQ,CAAC;MAC5C+B,WAAW,EAAEnO,MAAM,CAACC,IAAI,CAACuR,aAAa,CAACzE,YAAY,CAAC;MACpD6B,YAAY,EAAE5O,MAAM,CAACC,IAAI,CAACuR,aAAa,CAAClD,aAAa;KACxD;IAED,OAAOgE,SAAS;;;;;;EAOpBC,UAAU,CAACC,UAAkB;IACzB,MAAMzG,OAAO,GAAG,IAAI,CAACgG,OAAO,CAACS,UAAU,CAAkB;IACzD,IAAIxG,aAAa,CAACyG,eAAe,CAAC1G,OAAO,CAAC,EAAE;MACxC,OAAOA,OAAO;;IAElB,OAAO,IAAI;;;;;;EAOf2G,UAAU,CAAC3G,OAAsB;IAC7B,MAAMyG,UAAU,GAAGzG,OAAO,CAAC4G,kBAAkB,EAAE;IAC/C,IAAI,CAACV,OAAO,CAACO,UAAU,EAAEzG,OAAO,CAAC;;;;;;EAOrC6G,oBAAoB,CAACC,UAAkB;IACnC,MAAMjG,OAAO,GAAG,IAAI,CAACmF,OAAO,CAACc,UAAU,CAAkB;IACzD,IAAIhG,aAAa,CAACiG,eAAe,CAAClG,OAAO,CAAC,EAAE;MACxC,OAAOA,OAAO;;IAElB,OAAO,IAAI;;;;;;EAOfmG,oBAAoB,CAACnG,OAAsB;IACvC,MAAMiG,UAAU,GAAGjG,OAAO,CAACoG,qBAAqB,EAAE;IAClD,IAAI,CAACf,OAAO,CAACY,UAAU,EAAEjG,OAAO,CAAC;;;;;;EAOrCqG,wBAAwB,CAACC,cAAsB;IAC3C,MAAM/E,WAAW,GAAG,IAAI,CAAC4D,OAAO,CAACmB,cAAc,CAAsB;IACrE,IAAI9E,iBAAiB,CAAC+E,mBAAmB,CAAChF,WAAW,CAAC,EAAE;MACpD,OAAOA,WAAW;;IAEtB,OAAO,IAAI;;;;;;EAOfiF,wBAAwB,CAACjF,WAA8B;IACnD,MAAM+E,cAAc,GAAG/E,WAAW,CAAC6E,qBAAqB,EAAE;IAC1D,IAAI,CAACf,OAAO,CAACiB,cAAc,EAAE/E,WAAW,CAAC;;;;;;EAO7CkF,yBAAyB,CAACC,eAAuB;IAC7C,MAAM1E,YAAY,GAAG,IAAI,CAACmD,OAAO,CAACuB,eAAe,CAAuB;IACxE,IAAIzE,kBAAkB,CAAC0E,oBAAoB,CAAC3E,YAAY,CAAC,EAAE;MACvD,OAAOA,YAAkC;;IAE7C,OAAO,IAAI;;;;;;EAOf4E,yBAAyB,CAAC5E,YAAgC;IACtD,MAAM0E,eAAe,GAAG1E,YAAY,CAACoE,qBAAqB,EAAE;IAC5D,IAAI,CAACf,OAAO,CAACqB,eAAe,EAAE1E,YAAY,CAAC;;;;;;EAO/C6E,cAAc,CAACC,cAAsB;IACjC,MAAM3E,WAAW,GAAsB,IAAI,CAACgD,OAAO,CAAC2B,cAAc,CAAsB;IACxF,IAAItE,iBAAiB,CAACuE,mBAAmB,CAACD,cAAc,EAAE3E,WAAW,CAAC,EAAE;MACpE,OAAOA,WAAW;;IAEtB,OAAO,IAAI;;;;;;EAOf6E,cAAc,CAAC7E,WAA8B;IACzC,MAAM2E,cAAc,GAAG3E,WAAW,CAAC8E,sBAAsB,EAAE;IAC3D,IAAI,CAAC5B,OAAO,CAACyB,cAAc,EAAE3E,WAAW,CAAC;;;;;;EAO7C+E,kBAAkB,CAACC,kBAA0B;IACzC,MAAMC,qBAAqB,GAA0B,IAAI,CAACjC,OAAO,CAACgC,kBAAkB,CAA0B;IAC9G,IAAIC,qBAAqB,IAAIC,qBAAqB,CAACC,uBAAuB,CAACH,kBAAkB,EAAEC,qBAAqB,CAAC,EAAE;MACnH,OAAOA,qBAAqB;;IAEhC,OAAO,IAAI;;;;;;;EAQfG,kBAAkB,CAACC,kBAA0B,EAAEC,eAAsC;IACjF,IAAI,CAACpC,OAAO,CAACmC,kBAAkB,EAAEC,eAAe,CAAC;;;;;;EAOrDC,oBAAoB,CAACzJ,GAAW;IAC5B,MAAM0J,uBAAuB,GAA4B,IAAI,CAACxC,OAAO,CAAClH,GAAG,CAA4B;IACrG,IAAI0J,uBAAuB,IAAIC,uBAAuB,CAACC,yBAAyB,CAAC5J,GAAG,EAAE0J,uBAAuB,CAAC,EAAE;MAC5G,OAAOA,uBAAuB;;IAElC,OAAO,IAAI;;;;;EAMfG,wBAAwB;IACpB,OAAO,IAAI,CAACC,OAAO,EAAE,CAACC,MAAM,CAAE/J,GAAG;MAC7B,OAAO,IAAI,CAACgK,mBAAmB,CAAChK,GAAG,CAAC;KACvC,CAAC;;;;;;;EAQNiK,oBAAoB,CAACjK,GAAW,EAAEkK,QAAiC;IAC/D,IAAI,CAAC9C,OAAO,CAACpH,GAAG,EAAEkK,QAAQ,CAAC;;;;;;EAO/BC,kBAAkB,CAACC,kBAA0B;IACzC,MAAMC,eAAe,GAAqB,IAAI,CAACnD,OAAO,CAACkD,kBAAkB,CAAqB;IAC9F,IAAIC,eAAe,IAAIC,gBAAgB,CAACC,kBAAkB,CAACH,kBAAkB,EAAEC,eAAe,CAAC,EAAE;MAC7F,OAAOA,eAAe;;IAE1B,OAAO,IAAI;;;;;;;EAQfG,kBAAkB,CAACJ,kBAA0B,EAAEC,eAAiC;IAC5E,IAAI,CAACjD,OAAO,CAACgD,kBAAkB,EAAEC,eAAe,CAAC;;;;;;;EAQrDI,UAAU,CAACzK,GAAW;IAClB,IAAI,CAACmG,MAAM,CAACgB,QAAQ,cAAcnH,KAAK,CAAC;;IAGxC,IAAI0K,MAAM,GAAY,KAAK;IAC3B,MAAMxO,KAAK,GAAG,IAAI,CAAC2K,QAAQ,EAAE;IAE7B,IAAI,CAAC,CAAC3K,KAAK,CAAC8D,GAAG,CAAC,EAAE;MACd,OAAO9D,KAAK,CAAC8D,GAAG,CAAC;MACjB0K,MAAM,GAAG,IAAI;;;IAIjB,IAAIA,MAAM,EAAE;MACR,IAAI,CAACzD,QAAQ,CAAC/K,KAAK,CAAC;MACpB,IAAI,CAACsK,UAAU,EAAE;;IAErB,OAAOkE,MAAM;;;;;;EAOjBC,WAAW,CAAC3K,GAAW;IACnB,OAAO,IAAI,CAAC8J,OAAO,EAAE,CAACc,QAAQ,CAAC5K,GAAG,CAAC;;;;;EAMvC8J,OAAO;IACH,IAAI,CAAC3D,MAAM,CAACY,KAAK,CAAC,2BAA2B,CAAC;;IAG9C,MAAM7K,KAAK,GAAG,IAAI,CAAC2K,QAAQ,EAAE;IAC7B,OAAO,CAAE,GAAG1R,MAAM,CAACC,IAAI,CAAC8G,KAAK,CAAC,CAAC;;;;;EAMnC,MAAM2O,KAAK;IACP,IAAI,CAAC1E,MAAM,CAACY,KAAK,CAAC,wCAAwC,CAAC;;IAG3D,MAAM+D,SAAS,GAAG,IAAI,CAAChB,OAAO,EAAE;;IAGhCgB,SAAS,CAAClT,OAAO,CAACoI,GAAG;MACjB,IAAI,CAACyK,UAAU,CAACzK,GAAG,CAAC;KACvB,CAAC;IACF,IAAI,CAACwG,UAAU,EAAE;;;;;;EAOrB,OAAOuE,qBAAqB,CAAC7O,KAAa;IACtC,OAAOoD,YAAY,CAACkF,mBAAmB,CACnClF,YAAY,CAACC,mBAAmB,CAACrD,KAAK,CAAC,CAC1C;;;;;;EAOL,OAAO8O,iBAAiB,CAACrE,aAA4B;IACjD,OAAO5B,UAAU,CAACiB,iBAAiB,CAACW,aAAa,CAAC;;;;;EAMtDsE,wBAAwB,CAACC,eAAuB,EAAEC,UAA+B;IAC7E,MAAMC,eAAe,GAAGD,UAAU,CAAChD,qBAAqB,EAAE;IAE1D,IAAI+C,eAAe,KAAKE,eAAe,EAAE;MACrC,MAAMC,SAAS,GAAG,IAAI,CAACnE,OAAO,CAACgE,eAAe,CAAC;MAC/C,IAAIG,SAAS,EAAE;QACX,IAAI,CAACZ,UAAU,CAACS,eAAe,CAAC;QAChC,IAAI,CAAC9D,OAAO,CAACgE,eAAe,EAAEC,SAAS,CAAC;QACxC,IAAI,CAAClF,MAAM,CAACmF,OAAO,wBAAwBH,UAAU,CAACxJ,0BAA0B,CAAC;QACjF,OAAOyJ,eAAe;OACzB,MAAM;QACH,IAAI,CAACjF,MAAM,CAACnN,KAAK,oCAAoCmS,UAAU,CAACxJ,qFAAqF,CAAC;;;IAI9J,OAAOuJ,eAAe;;;;ACzd9B;;;;AAMA,AAMA,MAAMK,sBAAsB,GAAc;EACtC7G,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAE;CAChB;AAED;;;;AAIA,MAAa0G,UAAU;EAQnBzX,YAAY0X,OAAoB,EAAEtF,MAAc,EAAEuF,WAA0B;IACxE,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACA,OAAO,CAACpF,qBAAqB,CAAC,IAAI,CAACuF,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrE,IAAIH,WAAW,EAAE;MACb,IAAI,CAACI,WAAW,GAAGJ,WAAW;;IAElC,IAAI,CAACvF,MAAM,GAAGA,MAAM;;;;;EAMxB4F,UAAU;IACN,OAAO,IAAI,CAACJ,eAAe;;;;;EAM/BK,SAAS;IACL,IAAI,CAAC7F,MAAM,CAACY,KAAK,CAAC,6BAA6B,CAAC;IAChD,IAAIkF,UAAU,GAAGlH,UAAU,CAACiB,iBAAiB,CACzC,IAAI,CAACyF,OAAO,CAAC3E,gBAAgB,EAAmB,CACnD;;IAGD,IAAI,CAACpH,WAAW,CAACC,OAAO,CAAC,IAAI,CAACuM,aAAa,CAAC,EAAE;MAC1C,IAAI,CAAC/F,MAAM,CAACY,KAAK,CAAC,kCAAkC,CAAC;MACrDkF,UAAU,GAAG,IAAI,CAACE,UAAU,CACxBhU,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC8T,aAAa,CAAC,EAC9BD,UAAU,CACb;KACJ,MAAM;MACH,IAAI,CAAC9F,MAAM,CAACY,KAAK,CAAC,4BAA4B,CAAC;;IAEnD,IAAI,CAAC4E,eAAe,GAAG,KAAK;IAE5B,OAAOxT,IAAI,CAACqB,SAAS,CAACyS,UAAU,CAAC;;;;;;EAOrCG,WAAW,CAAClQ,KAAa;IACrB,IAAI,CAACiK,MAAM,CAACY,KAAK,CAAC,uCAAuC,CAAC;IAC1D,IAAI,CAACmF,aAAa,GAAGhQ,KAAK;IAE1B,IAAI,CAACwD,WAAW,CAACC,OAAO,CAAC,IAAI,CAACuM,aAAa,CAAC,EAAE;MAC1C,IAAI,CAAC/F,MAAM,CAACY,KAAK,CAAC,kCAAkC,CAAC;MACrD,MAAMtH,iBAAiB,GAAGH,YAAY,CAACkF,mBAAmB,CACtD,IAAI,CAAC6H,eAAe,CAAClU,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC8T,aAAa,CAAC,CAAC,CACvD;MACD,IAAI,CAACT,OAAO,CAACzE,gBAAgB,CAACvH,iBAAiB,CAAC;KACnD,MAAM;MACH,IAAI,CAAC0G,MAAM,CAACY,KAAK,CAAC,kCAAkC,CAAC;;;;;;EAO7DuF,UAAU;IACN,OAAO,IAAI,CAACb,OAAO,CAAC5E,QAAQ,EAAE;;;;;EAMlC,MAAM0F,cAAc;IAEhB,IAAI,CAACpG,MAAM,CAACY,KAAK,CAAC,uBAAuB,CAAC;IAC1C,IAAIyF,YAAY;IAChB,IAAI;MACA,IAAI,IAAI,CAACV,WAAW,EAAE;QAClBU,YAAY,GAAG,IAAIC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC;QACjD,MAAM,IAAI,CAACX,WAAW,CAACY,iBAAiB,CAACF,YAAY,CAAC;;MAE1D,OAAO,IAAI,CAACf,OAAO,CAACc,cAAc,EAAE;KACvC,SAAS;MACN,IAAI,IAAI,CAACT,WAAW,IAAIU,YAAY,EAAE;QAClC,MAAM,IAAI,CAACV,WAAW,CAACa,gBAAgB,CAACH,YAAY,CAAC;;;;;;;;;;EAWjE,MAAMI,kBAAkB,CAACzM,aAAqB;IAC1C,MAAM0M,WAAW,GAAG,MAAM,IAAI,CAACN,cAAc,EAAE;IAC/C,IAAI,CAAC7M,WAAW,CAACC,OAAO,CAACQ,aAAa,CAAC,IAAI0M,WAAW,IAAIA,WAAW,CAACxX,MAAM,EAAE;MAC1E,OAAOwX,WAAW,CAAC9C,MAAM,CAAC+C,UAAU,IAAIA,UAAU,CAAC3M,aAAa,KAAKA,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;KACjG,MAAM;MACH,OAAO,IAAI;;;;;;;;;EAUnB,MAAM4M,mBAAmB,CAACxM,cAAsB;IAC5C,MAAMsM,WAAW,GAAG,MAAM,IAAI,CAACN,cAAc,EAAE;IAC/C,IAAI,CAAC7M,WAAW,CAACC,OAAO,CAACY,cAAc,CAAC,IAAIsM,WAAW,IAAIA,WAAW,CAACxX,MAAM,EAAE;MAC3E,OAAOwX,WAAW,CAAC9C,MAAM,CAAC+C,UAAU,IAAIA,UAAU,CAACvM,cAAc,KAAKA,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;KACnG,MAAM;MACH,OAAO,IAAI;;;;;;;EAQnB,MAAMyM,aAAa,CAAC9L,OAAoB;IACpC,IAAI,CAACiF,MAAM,CAACY,KAAK,CAAC,sBAAsB,CAAC;IACzC,IAAIyF,YAAY;IAChB,IAAI;MACA,IAAI,IAAI,CAACV,WAAW,EAAE;QAClBU,YAAY,GAAG,IAAIC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC;QAChD,MAAM,IAAI,CAACX,WAAW,CAACY,iBAAiB,CAACF,YAAY,CAAC;;MAE1D,MAAM,IAAI,CAACf,OAAO,CAACuB,aAAa,CAAC7L,aAAa,CAAC8L,uBAAuB,CAAC/L,OAAO,CAAC,CAAC;KACnF,SAAS;MACN,IAAI,IAAI,CAAC4K,WAAW,IAAIU,YAAY,EAAE;QAClC,MAAM,IAAI,CAACV,WAAW,CAACa,gBAAgB,CAACH,YAAY,CAAC;;;;;;;EAQzDZ,iBAAiB;IACrB,IAAI,CAACD,eAAe,GAAG,IAAI;;;;;;;EAQvBQ,UAAU,CAACe,QAAmB,EAAEC,YAAuB;IAC3D,IAAI,CAAChH,MAAM,CAACY,KAAK,CAAC,6CAA6C,CAAC;IAChE,MAAMqG,iBAAiB,GAAG,IAAI,CAACC,aAAa,CAACH,QAAQ,EAAEC,YAAY,CAAC;IACpE,OAAO,IAAI,CAACG,YAAY,CAACF,iBAAiB,EAAED,YAAY,CAAC;;;;;;;EAQrDG,YAAY,CAACJ,QAAgB,EAAEK,QAAgB;IACnDpY,MAAM,CAACC,IAAI,CAACmY,QAAQ,CAAC,CAAC3V,OAAO,CAAE4V,MAAc;MACzC,MAAMC,QAAQ,GAAGF,QAAQ,CAACC,MAAM,CAAC;;MAGjC,IAAI,CAACN,QAAQ,CAACQ,cAAc,CAACF,MAAM,CAAC,EAAE;QAClC,IAAIC,QAAQ,KAAK,IAAI,EAAE;UACnBP,QAAQ,CAACM,MAAM,CAAC,GAAGC,QAAQ;;OAElC,MAAM;;QAEH,MAAME,eAAe,GAAGF,QAAQ,KAAK,IAAI;QACzC,MAAMG,gBAAgB,GAAG,OAAOH,QAAQ,KAAK,QAAQ;QACrD,MAAMI,kBAAkB,GAAG,CAACC,KAAK,CAACC,OAAO,CAACN,QAAQ,CAAC;QACnD,MAAMO,0BAA0B,GAAG,OAAOd,QAAQ,CAACM,MAAM,CAAC,KAAK,WAAW,IAAIN,QAAQ,CAACM,MAAM,CAAC,KAAK,IAAI;QAEvG,IAAIG,eAAe,IAAIC,gBAAgB,IAAIC,kBAAkB,IAAIG,0BAA0B,EAAE;UACzF,IAAI,CAACV,YAAY,CAACJ,QAAQ,CAACM,MAAM,CAAC,EAAEC,QAAQ,CAAC;SAChD,MAAM;UACHP,QAAQ,CAACM,MAAM,CAAC,GAAGC,QAAQ;;;KAGtC,CAAC;IAEF,OAAOP,QAAqB;;;;;;;;EASxBG,aAAa,CAACH,QAAmB,EAAEK,QAAmB;IAC1D,IAAI,CAACpH,MAAM,CAACY,KAAK,CAAC,iCAAiC,CAAC;IACpD,MAAMlH,QAAQ,GAAGqN,QAAQ,CAACxI,OAAO,GAAG,IAAI,CAACuJ,iBAAiB,CAA0Bf,QAAQ,CAACxI,OAAO,EAAE6I,QAAQ,CAAC7I,OAAO,CAAC,GAAGwI,QAAQ,CAACxI,OAAO;IAC1I,MAAMxC,YAAY,GAAGgL,QAAQ,CAACtI,WAAW,GAAG,IAAI,CAACqJ,iBAAiB,CAA8Bf,QAAQ,CAACtI,WAAW,EAAE2I,QAAQ,CAAC3I,WAAW,CAAC,GAAGsI,QAAQ,CAACtI,WAAW;IAClK,MAAMnB,aAAa,GAAGyJ,QAAQ,CAACrI,YAAY,GAAG,IAAI,CAACoJ,iBAAiB,CAA+Bf,QAAQ,CAACrI,YAAY,EAAE0I,QAAQ,CAAC1I,YAAY,CAAC,GAAGqI,QAAQ,CAACrI,YAAY;IACxK,MAAMtD,QAAQ,GAAG2L,QAAQ,CAACvI,OAAO,GAAG,IAAI,CAACsJ,iBAAiB,CAA0Bf,QAAQ,CAACvI,OAAO,EAAE4I,QAAQ,CAAC5I,OAAO,CAAC,GAAGuI,QAAQ,CAACvI,OAAO;IAC1I,MAAMT,WAAW,GAAGgJ,QAAQ,CAACpI,WAAW,GAAG,IAAI,CAACmJ,iBAAiB,CAA8Bf,QAAQ,CAACpI,WAAW,EAAEyI,QAAQ,CAACzI,WAAW,CAAC,GAAGoI,QAAQ,CAACpI,WAAW;IAEjK,OAAO;MACH,GAAGoI,QAAQ;MACXxI,OAAO,EAAE7E,QAAQ;MACjB+E,WAAW,EAAE1C,YAAY;MACzB2C,YAAY,EAAEpB,aAAa;MAC3BkB,OAAO,EAAEpD,QAAQ;MACjBuD,WAAW,EAAEZ;KAChB;;;;;;;EAQG+J,iBAAiB,CAAIf,QAA2B,EAAEK,QAA4B;IAClF,MAAMtB,UAAU,GAAG;MAAE,GAAGiB;KAAU;IAClC/X,MAAM,CAACC,IAAI,CAAC8X,QAAQ,CAAC,CAACtV,OAAO,CAAEsW,MAAM;MACjC,IAAI,CAACX,QAAQ,IAAI,CAAEA,QAAQ,CAACG,cAAc,CAACQ,MAAM,CAAE,EAAE;QACjD,OAAOjC,UAAU,CAACiC,MAAM,CAAC;;KAEhC,CAAC;IACF,OAAOjC,UAAU;;;;;;EAObI,eAAe,CAAC8B,aAAwB;IAC5C,IAAI,CAAChI,MAAM,CAACY,KAAK,CAAC,+CAA+C,CAAC;IAClE,OAAO;MACHrC,OAAO,EAAE;QACL,GAAG6G,sBAAsB,CAAC7G,OAAO;QACjC,GAAGyJ,aAAa,CAACzJ;OACpB;MACDC,OAAO,EAAE;QACL,GAAG4G,sBAAsB,CAAC5G,OAAO;QACjC,GAAGwJ,aAAa,CAACxJ;OACpB;MACDC,WAAW,EAAE;QACT,GAAG2G,sBAAsB,CAAC3G,WAAW;QACrC,GAAGuJ,aAAa,CAACvJ;OACpB;MACDC,YAAY,EAAE;QACV,GAAG0G,sBAAsB,CAAC1G,YAAY;QACtC,GAAGsJ,aAAa,CAACtJ;OACpB;MACDC,WAAW,EAAE;QACT,GAAGyG,sBAAsB,CAACzG,WAAW;QACrC,GAAGqJ,aAAa,CAACrJ;;KAExB;;;;AC7RT;AACA,AAAO,MAAMnE,IAAI,GAAG,kBAAkB;AACtC,MAAayN,OAAO,GAAG,QAAQ;;ACF/B;;;;AAKA,AAEA;;;AAGA,AAAO,MAAMC,oBAAoB,GAAG;EAChCC,0BAA0B,EAAE;IACxBC,IAAI,EAAE,sCAAsC;IAC5CC,IAAI,EAAE;GACT;EACDC,uBAAuB,EAAE;IACrBF,IAAI,EAAE,4BAA4B;IAClCC,IAAI,EAAE;GACT;EACDE,oBAAoB,EAAE;IAClBH,IAAI,EAAE,0BAA0B;IAChCC,IAAI,EAAE;GACT;EACDG,sBAAsB,EAAE;IACpBJ,IAAI,EAAE,2BAA2B;IACjCC,IAAI,EAAE;GACT;EACDI,2BAA2B,EAAE;IACzBL,IAAI,EAAE,gCAAgC;IACtCC,IAAI,EAAE;GACT;EACDK,qBAAqB,EAAE;IACnBN,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE;GACT;EACDM,kBAAkB,EAAE;IAChBP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE;;CAEb;AAED,MAAaO,aAAc,SAAQC,SAAS;EACxCjb,YAAYkb,SAAiB,EAAEC,YAAqB;IAChD,KAAK,CAACD,SAAS,EAAEC,YAAY,CAAC;IAC9B,IAAI,CAACvO,IAAI,GAAG,eAAe;;;;;EAM/B,OAAOwO,qCAAqC;IACxC,OAAO,IAAIJ,aAAa,CAACV,oBAAoB,CAACC,0BAA0B,CAACC,IAAI,KACtEF,oBAAoB,CAACC,0BAA0B,CAACE,MAAM,CAAC;;;;;EAMlE,OAAOY,kCAAkC;IACrC,OAAO,IAAIL,aAAa,CAACV,oBAAoB,CAACI,uBAAuB,CAACF,IAAI,KACnEF,oBAAoB,CAACI,uBAAuB,CAACD,MAAM,CAAC;;;;;EAM/D,OAAOa,+BAA+B;IAClC,OAAO,IAAIN,aAAa,CAACV,oBAAoB,CAACK,oBAAoB,CAACH,IAAI,KAChEF,oBAAoB,CAACK,oBAAoB,CAACF,MAAM,CAAC;;;;;EAM5D,OAAOc,iCAAiC;IACpC,OAAO,IAAIP,aAAa,CAACV,oBAAoB,CAACM,sBAAsB,CAACJ,IAAI,KAClEF,oBAAoB,CAACM,sBAAsB,CAACH,MAAM,CAAC;;;;;EAM9D,OAAOe,sCAAsC;IACzC,OAAO,IAAIR,aAAa,CAACV,oBAAoB,CAACO,2BAA2B,CAACL,IAAI,KACvEF,oBAAoB,CAACO,2BAA2B,CAACJ,MAAM,CAAC;;;;;EAMnE,OAAOgB,gCAAgC;IACnC,OAAO,IAAIT,aAAa,CAACV,oBAAoB,CAACQ,qBAAqB,CAACN,IAAI,KACjEF,oBAAoB,CAACQ,qBAAqB,CAACL,MAAM,CAAC;;;;;EAM7D,OAAOiB,wBAAwB;IAC3B,OAAO,IAAIV,aAAa,CAACV,oBAAoB,CAACS,kBAAkB,CAACP,IAAI,EAAEF,oBAAoB,CAACS,kBAAkB,CAACN,IAAI,CAAC;;;;ACnG5H;;;;AAKA,AA2CA;;;;AAIA,MAAsBkB,iBAAiB;;;;EA6BnC3b,YAAsB4b,aAA4B;IAC9C,IAAI,CAACC,MAAM,GAAG5T,qBAAqB,CAAC2T,aAAa,CAAC;IAClD,IAAI,CAACE,cAAc,GAAG,IAAIlR,cAAc,EAAE;IAC1C,IAAI,CAACwH,MAAM,GAAG,IAAI2J,MAAM,CAAC,IAAI,CAACF,MAAM,CAACzT,MAAM,CAACT,aAAa,EAAEiF,IAAI,EAAEyN,OAAO,CAAC;IACzE,IAAI,CAAC3C,OAAO,GAAG,IAAIvF,WAAW,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACyJ,MAAM,CAAChc,IAAI,CAAC8F,QAAQ,EAAE,IAAI,CAACmW,cAAc,CAAC;IAC3F,IAAI,CAACE,UAAU,GAAG,IAAIvE,UAAU,CAC5B,IAAI,CAACC,OAAO,EACZ,IAAI,CAACtF,MAAM,EACX,IAAI,CAACyJ,MAAM,CAAC1T,KAAK,CAACwP,WAAW,CAChC;;;;;;;;;;;EAYL,MAAMsE,cAAc,CAACja,OAAgC;IACjD,IAAI,CAACoQ,MAAM,CAAC8J,IAAI,CAAC,uBAAuB,EAAEla,OAAO,CAACma,aAAa,CAAC;IAChE,MAAMC,YAAY,GAAkC;MAChD,GAAGpa,OAAO;MACV,IAAI,MAAM,IAAI,CAACqa,qBAAqB,CAACra,OAAO,CAAC;MAC7Csa,YAAY,EAAEta,OAAO,CAACsa,YAAY,IAAIC,YAAY,CAACC,KAAK;MACxDC,oBAAoB,EAAEC,oBAAoB,CAACC;KAC9C;IAED,MAAMC,gBAAgB,GAAG,MAAM,IAAI,CAACC,6BAA6B,CAC7DT,YAAY,CAACvW,SAAS,EACtBuW,YAAY,CAACD,aAAa,EAC1BW,SAAS,EACTA,SAAS,EACT9a,OAAO,CAAC4E,iBAAiB,CAC5B;IACD,MAAMmW,uBAAuB,GAAG,IAAIC,uBAAuB,CACvDJ,gBAAgB,CACnB;IACD,IAAI,CAACxK,MAAM,CAACmF,OAAO,CAAC,0BAA0B,EAAE6E,YAAY,CAACD,aAAa,CAAC;IAC3E,OAAOY,uBAAuB,CAACd,cAAc,CAACG,YAAY,CAAC;;;;;;;;;;EAW/D,MAAMa,kBAAkB,CAACjb,OAAiC,EAAEkb,eAA0C;IAClG,IAAI,CAAC9K,MAAM,CAAC8J,IAAI,CAAC,2BAA2B,CAAC;IAC7C,IAAIla,OAAO,CAACmb,KAAK,IAAID,eAAe,EAAC;MACjC,IAAI,CAAC9K,MAAM,CAAC8J,IAAI,CAAC,uCAAuC,CAAC;MACzD,IAAI,CAACkB,aAAa,CAACpb,OAAO,CAACmb,KAAK,EAAED,eAAe,CAACC,KAAK,IAAI,EAAE,CAAC;;MAE9DD,eAAe,GAAE;QAAC,GAAGA,eAAe;QAAEC,KAAK,EAAE;OAAG;;IAEpD,MAAMf,YAAY,GAAmC;MACjD,GAAGpa,OAAO;MACV,IAAI,MAAM,IAAI,CAACqa,qBAAqB,CAACra,OAAO,CAAC;MAC7Cya,oBAAoB,EAAEC,oBAAoB,CAACC;KAC9C;IAED,MAAMU,sBAAsB,GAAG,IAAI,CAACC,gCAAgC,CAAC9f,KAAK,CAACyf,kBAAkB,EAAEb,YAAY,CAACD,aAAa,CAAC;IAC1H,IAAI;MACA,MAAMS,gBAAgB,GAAG,MAAM,IAAI,CAACC,6BAA6B,CAC7DT,YAAY,CAACvW,SAAS,EACtBuW,YAAY,CAACD,aAAa,EAC1BkB,sBAAsB,EACtBP,SAAS,EACT9a,OAAO,CAAC4E,iBAAiB,CAC5B;MACD,MAAMmW,uBAAuB,GAAG,IAAIC,uBAAuB,CACvDJ,gBAAgB,CACnB;MACD,IAAI,CAACxK,MAAM,CAACmF,OAAO,CAAC,0BAA0B,EAAE6E,YAAY,CAACD,aAAa,CAAC;MAC3E,OAAOY,uBAAuB,CAACQ,YAAY,CAACnB,YAAY,EAAEc,eAAe,CAAC;KAC7E,CAAC,OAAO5Y,CAAC,EAAE;MACR,IAAIA,CAAC,YAAY2W,SAAS,EAAE;QACxB3W,CAAC,CAACkZ,gBAAgB,CAACpB,YAAY,CAACD,aAAa,CAAC;;MAElDkB,sBAAsB,CAACI,kBAAkB,CAACnZ,CAAC,CAAC;MAC5C,MAAMA,CAAC;;;;;;;;;;EAWf,MAAMoZ,0BAA0B,CAAC1b,OAA4B;IACzD,IAAI,CAACoQ,MAAM,CAAC8J,IAAI,CAAC,mCAAmC,EAAEla,OAAO,CAACma,aAAa,CAAC;IAC5E,MAAMC,YAAY,GAA8B;MAC5C,GAAGpa,OAAO;MACV,IAAI,MAAM,IAAI,CAACqa,qBAAqB,CAACra,OAAO,CAAC;MAC7Cya,oBAAoB,EAAEC,oBAAoB,CAACC;KAC9C;IAED,MAAMU,sBAAsB,GAAG,IAAI,CAACC,gCAAgC,CAAC9f,KAAK,CAACkgB,0BAA0B,EAAEtB,YAAY,CAACD,aAAa,CAAC;IAClI,IAAI;MACA,MAAMwB,wBAAwB,GAAG,MAAM,IAAI,CAACd,6BAA6B,CACrET,YAAY,CAACvW,SAAS,EACtBuW,YAAY,CAACD,aAAa,EAC1BkB,sBAAsB,EACtBP,SAAS,EACT9a,OAAO,CAAC4E,iBAAiB,CAC5B;MACD,MAAMgX,kBAAkB,GAAG,IAAIC,kBAAkB,CAC7CF,wBAAwB,CAC3B;MACD,IAAI,CAACvL,MAAM,CAACmF,OAAO,CAAC,8BAA8B,EAAE6E,YAAY,CAACD,aAAa,CAAC;MAC/E,OAAOyB,kBAAkB,CAACL,YAAY,CAACnB,YAAY,CAAC;KACvD,CAAC,OAAO9X,CAAC,EAAE;MACR,IAAIA,CAAC,YAAY2W,SAAS,EAAE;QACxB3W,CAAC,CAACkZ,gBAAgB,CAACpB,YAAY,CAACD,aAAa,CAAC;;MAElDkB,sBAAsB,CAACI,kBAAkB,CAACnZ,CAAC,CAAC;MAC5C,MAAMA,CAAC;;;;;;;;;;;EAYf,MAAMwZ,kBAAkB,CAAC9b,OAA0B;IAC/C,MAAMoa,YAAY,GAA4B;MAC1C,GAAGpa,OAAO;MACV,IAAI,MAAM,IAAI,CAACqa,qBAAqB,CAACra,OAAO,CAAC;MAC7C+b,YAAY,EAAE/b,OAAO,CAAC+b,YAAY,IAAI;KACzC;IAED,MAAMV,sBAAsB,GAAG,IAAI,CAACC,gCAAgC,CAAC9f,KAAK,CAACsgB,kBAAkB,EAAE1B,YAAY,CAACD,aAAa,EAAEC,YAAY,CAAC2B,YAAY,CAAC;IACrJ,IAAI;MACA,MAAMC,sBAAsB,GAAG,MAAM,IAAI,CAACnB,6BAA6B,CACnET,YAAY,CAACvW,SAAS,EACtBuW,YAAY,CAACD,aAAa,EAC1BkB,sBAAsB,EACtBP,SAAS,EACT9a,OAAO,CAAC4E,iBAAiB,CAC5B;MACD,MAAMqX,gBAAgB,GAAG,IAAIC,gBAAgB,CACzCF,sBAAsB,CACzB;MACD,IAAI,CAAC5L,MAAM,CAACmF,OAAO,CAAC,4BAA4B,EAAE6E,YAAY,CAACD,aAAa,CAAC;MAC7E,OAAO8B,gBAAgB,CAACV,YAAY,CAACnB,YAAY,CAAC;KACrD,CAAC,OAAO9X,CAAC,EAAE;MACR,IAAIA,CAAC,YAAY2W,SAAS,EAAE;QACxB3W,CAAC,CAACkZ,gBAAgB,CAACpB,YAAY,CAACD,aAAa,CAAC;;MAElDkB,sBAAsB,CAACI,kBAAkB,CAACnZ,CAAC,CAAC;MAC5C,MAAMA,CAAC;;;;;;;;;;;;;EAcf,MAAM6Z,8BAA8B,CAACnc,OAAgC;IACjE,IAAI,CAACoQ,MAAM,CAAC8J,IAAI,CAAC,uCAAuC,EAAEla,OAAO,CAACma,aAAa,CAAC;IAChF,MAAMC,YAAY,GAAkC;MAChD,GAAGpa,OAAO;MACV,IAAI,MAAM,IAAI,CAACqa,qBAAqB,CAACra,OAAO,CAAC;KAChD;IACD,MAAMqb,sBAAsB,GAAG,IAAI,CAACC,gCAAgC,CAAC9f,KAAK,CAAC2gB,8BAA8B,EAAE/B,YAAY,CAACD,aAAa,CAAC;IACtI,IAAI;MACA,MAAMiC,4BAA4B,GAAG,MAAM,IAAI,CAACvB,6BAA6B,CACzET,YAAY,CAACvW,SAAS,EACtBuW,YAAY,CAACD,aAAa,EAC1BkB,sBAAsB,EACtBP,SAAS,EACT9a,OAAO,CAAC4E,iBAAiB,CAC5B;MACD,MAAMyX,sBAAsB,GAAG,IAAIC,sBAAsB,CAACF,4BAA4B,CAAC;MACvF,IAAI,CAAChM,MAAM,CAACmF,OAAO,CAAC,kCAAkC,EAAE6E,YAAY,CAACD,aAAa,CAAC;MACnF,OAAOkC,sBAAsB,CAACd,YAAY,CAACnB,YAAY,CAAC;KAC3D,CAAC,OAAO9X,CAAC,EAAE;MACR,IAAIA,CAAC,YAAY2W,SAAS,EAAE;QACxB3W,CAAC,CAACkZ,gBAAgB,CAACpB,YAAY,CAACD,aAAa,CAAC;;MAElDkB,sBAAsB,CAACI,kBAAkB,CAACnZ,CAAC,CAAC;MAC5C,MAAMA,CAAC;;;;;;EAOfia,aAAa;IACT,IAAI,CAACnM,MAAM,CAAC8J,IAAI,CAAC,sBAAsB,CAAC;IACxC,OAAO,IAAI,CAACF,UAAU;;;;;;;;;;;EAYhBoB,aAAa,CAACD,KAAa,EAAEqB,WAAmB;IACtD,IAAG,CAACrB,KAAK,EAAE;MACP,MAAMnC,aAAa,CAACU,wBAAwB,EAAE;;IAGlD,IAAGyB,KAAK,KAAKqB,WAAW,EAAE;MACtB,MAAMC,eAAe,CAACC,wBAAwB,EAAE;;;;;;EAOxDC,SAAS;IACL,OAAO,IAAI,CAACvM,MAAM;;;;;;EAOtBwM,SAAS,CAACxM,MAAc;IACpB,IAAI,CAACA,MAAM,GAAGA,MAAM;;;;;;;EAQd,MAAMyK,6BAA6B,CACzChX,SAAiB,EACjBgZ,oBAA6B,EAC7BxB,sBAA+C,EAC/CyB,wBAAmD,EACnDlY,iBAAqC;IAErC,IAAI,CAACwL,MAAM,CAACmF,OAAO,CAAC,sCAAsC,EAAEsH,oBAAoB,CAAC;;IAGjF,MAAME,qBAAqB,GAAGnY,iBAAiB,GAAGA,iBAAiB,GAAG,IAAI,CAACiV,MAAM,CAAChc,IAAI,CAAC+G,iBAAiB;;IAGxG,IAAI,CAACwL,MAAM,CAACmF,OAAO,4DAA4D1R,WAAW,EAAEgZ,oBAAoB,CAAC;IACjH,MAAMG,mBAAmB,GAAG,MAAM,IAAI,CAACC,eAAe,CAACpZ,SAAS,EAAEiZ,wBAAwB,EAAED,oBAAoB,EAAEE,qBAAqB,CAAC;IAExI1B,sBAAsB,oBAAtBA,sBAAsB,CAAE6B,6BAA6B,CAACF,mBAAmB,CAACG,uBAAuB,CAAC;IAElG,MAAMC,mBAAmB,GAAwB;MAC7CC,WAAW,EAAE;QACT1Z,QAAQ,EAAE,IAAI,CAACkW,MAAM,CAAChc,IAAI,CAAC8F,QAAQ;QACnCE,SAAS,EAAEmZ,mBAAmB;QAC9BxY,kBAAkB,EAAE,IAAI,CAACqV,MAAM,CAAChc,IAAI,CAAC2G;OACxC;MACDmB,aAAa,EAAE;QACXJ,QAAQ,EAAE,IAAI,CAACsU,MAAM,CAACzT,MAAM,CAACT,aAAa,CAACJ,QAAQ;QACnDF,cAAc,EAAE,IAAI,CAACwU,MAAM,CAACzT,MAAM,CAACT,aAAa,CAACN,cAAc;QAC/DC,iBAAiB,EAAE,IAAI,CAACuU,MAAM,CAACzT,MAAM,CAACT,aAAa,CAACL,iBAAiB;QACrE6U,aAAa,EAAE0C;OAClB;MACDS,YAAY,EAAE;QACVnY,yBAAyB,EAAE,IAAI,CAAC0U,MAAM,CAAC1T,KAAK,CAAChB;OAChD;MACDoY,eAAe,EAAE,IAAI,CAACzD,cAAc;MACpC0D,gBAAgB,EAAE,IAAI,CAAC3D,MAAM,CAACzT,MAAM,CAACR,aAAa;MAClD6X,gBAAgB,EAAE,IAAI,CAAC/H,OAAO;MAC9B2F,sBAAsB,EAAEA,sBAAsB;MAC9CqC,iBAAiB,EAAE;QACf3Z,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BC,eAAe,EAAE,IAAI,CAACA,eAAe,GAAG,IAAI,CAAC2Z,kBAAkB,CAACX,mBAAmB,CAAC,GAAGlC;OAC1F;MACD8C,WAAW,EAAE;QACTC,GAAG,EAAEC,SAAa,CAAC3iB,QAAQ;QAC3Bkd,OAAO,EAAEA,OAAO;QAChB0F,GAAG,EAAEC,OAAO,CAACC,IAAI,IAAI/iB,WAAS,CAAC0I,YAAY;QAC3Csa,EAAE,EAAEF,OAAO,CAACG,QAAQ,IAAIjjB,WAAS,CAAC0I;OACrC;MACDyC,SAAS,EAAE,IAAI,CAACwT,MAAM,CAACxT,SAAS;MAChC+X,iBAAiB,EAAE,IAAI,CAACvE,MAAM,CAAC1T,KAAK,CAACwP,WAAW;MAChD0I,iBAAiB,EAAE,IAAI,CAACrE;KAC3B;IAED,OAAOoD,mBAAmB;;EAGtBO,kBAAkB,CAAC9Z,SAAoB;IAC3C,OAAO;MACHya,SAAS,EAAE,IAAI,CAACta,eAAe,CAACua,MAAM,CAAC,IAAI,CAACzE,cAAc,EAAE,IAAI,CAACD,MAAM,CAAChc,IAAI,CAAC8F,QAAQ,EAAEE,SAAS,CAAC2a,aAAa,CAAC;MAC/GC,aAAa,EAAEX,SAAa,CAAC1iB;KAChC;;;;;;EAOK,MAAMif,qBAAqB,CAACqE,WAAqC;IACvE,IAAI,CAACtO,MAAM,CAACmF,OAAO,CAAC,gCAAgC,EAAEmJ,WAAW,CAACvE,aAAa,CAAC;;IAEhF,IAAIuE,WAAW,CAACjE,oBAAoB,IAAIiE,WAAW,CAACjE,oBAAoB,KAAKC,oBAAoB,CAACiE,GAAG,EAAE;MACnG,IAAI,CAACvO,MAAM,CAACmF,OAAO,CAAC,yGAAyG,EAAEmJ,WAAW,CAACvE,aAAa,CAAC;;IAG7JuE,WAAW,CAACjE,oBAAoB,GAAGC,oBAAoB,CAACC,MAAM;;IAG9D,IAAI,IAAI,CAACd,MAAM,CAAC1T,KAAK,CAAChB,yBAAyB,IAC3CuZ,WAAW,CAACE,MAAM;;IAElB,CAACjV,WAAW,CAACkV,UAAU,CAACH,WAAW,CAACE,MAAM,CAAC,EAAE;MAC7CF,WAAW,CAACrR,mBAAmB,GAAG,MAAM,IAAI,CAACyM,cAAc,CAACzQ,UAAU,CAACqV,WAAW,CAACE,MAAM,CAAC;;IAG9F,OAAO;MACH,GAAGF,WAAW;MACdI,MAAM,EAAE,CAAC,IAAKJ,WAAW,IAAIA,WAAW,CAACI,MAAM,IAAK,EAAE,CAAC,EAAE,GAAGC,mBAAmB,CAAC;MAChF5E,aAAa,EAAEuE,WAAW,IAAIA,WAAW,CAACvE,aAAa,IAAI,IAAI,CAACL,cAAc,CAAC/Q,aAAa,EAAE;MAC9FlF,SAAS,EAAE6a,WAAW,CAAC7a,SAAS,IAAI,IAAI,CAACgW,MAAM,CAAChc,IAAI,CAACgG;KACxD;;;;;;;;EASKyX,gCAAgC,CAAC0D,KAAa,EAAE7E,aAAqB,EAAE4B,YAAsB;IACnG,MAAMkD,gBAAgB,GAA2B;MAC7Ctb,QAAQ,EAAE,IAAI,CAACkW,MAAM,CAAChc,IAAI,CAAC8F,QAAQ;MACnCwW,aAAa,EAAEA,aAAa;MAC5B6E,KAAK,EAAEA,KAAK;MACZjD,YAAY,EAAEA,YAAY,IAAI;KACjC;IAED,OAAO,IAAImD,sBAAsB,CAACD,gBAAgB,EAAE,IAAI,CAACvJ,OAAO,CAAC;;;;;;;EAQ7D,MAAMuH,eAAe,CAACkC,eAAuB,EAAErC,wBAAmD,EAAED,oBAA6B,EAAEjY,iBAAqC;IAC5K,IAAI,CAACwL,MAAM,CAACmF,OAAO,CAAC,wBAAwB,EAAEsH,oBAAoB,CAAC;;IAGnE,MAAMuC,YAAY,GAAGC,SAAS,CAACC,iBAAiB,CAACH,eAAe,EAAEva,iBAAiB,CAAC;IAEpF,MAAM2a,gBAAgB,GAAqB;MACvC9a,YAAY,EAAE,IAAI,CAACoV,MAAM,CAAChc,IAAI,CAAC4G,YAAY;MAC3CJ,gBAAgB,EAAE,IAAI,CAACwV,MAAM,CAAChc,IAAI,CAACwG,gBAAgB;MACnDC,sBAAsB,EAAE,IAAI,CAACuV,MAAM,CAAChc,IAAI,CAACyG,sBAAsB;MAC/DC,iBAAiB,EAAE,IAAI,CAACsV,MAAM,CAAChc,IAAI,CAAC0G,iBAAiB;MACrDuY,wBAAwB;MACxB7X,0BAA0B,EAAE,IAAI,CAAC4U,MAAM,CAAChc,IAAI,CAACoH;KAChD;IAED,OAAO,MAAMua,gBAAgB,CAACC,wBAAwB,CAACL,YAAY,EAAE,IAAI,CAACvF,MAAM,CAACzT,MAAM,CAACR,aAAa,EAAE,IAAI,CAAC8P,OAAO,EAAE6J,gBAAgB,EAAE,IAAI,CAACnP,MAAM,CAAC;;;;;EAMvJsP,UAAU;IACN,IAAI,CAAChK,OAAO,CAACZ,KAAK,EAAE;;;;ACjd5B;;;;AAKA,MAMa6K,cAAc;;;;;;;EASvB,MAAMC,iBAAiB,CAACC,eAAwB,EAAEC,aAAsB;IACpE,IAAI,CAAC,CAAC,IAAI,CAACC,MAAM,EAAE;MACf,MAAM/G,aAAa,CAACQ,sCAAsC,EAAE;;IAGhE,MAAMwG,gBAAgB,GAAG,IAAIngB,OAAO,CAAkC,CAACC,OAAO,EAAEC,MAAM;MAClF,IAAI,CAACggB,MAAM,GAAGE,YAAY,CAAC,OAAOC,GAAoB,EAAEC,GAAmB;QACvE,MAAMrjB,GAAG,GAAGojB,GAAG,CAACpjB,GAAG;QACnB,IAAI,CAACA,GAAG,EAAE;UACNqjB,GAAG,CAAC/f,GAAG,CAAC0f,aAAa,IAAI,oCAAoC,CAAC;UAC9D/f,MAAM,CAACiZ,aAAa,CAACK,kCAAkC,EAAE,CAAC;UAC1D;SACH,MAAM,IAAIvc,GAAG,KAAKsjB,WAAe,CAACC,aAAa,EAAE;UAC9CF,GAAG,CAAC/f,GAAG,CAACyf,eAAe,IAAI,qEAAqE,CAAC;UACjG;;QAGJ,MAAMS,gBAAgB,GAAGC,SAAS,CAACC,0BAA0B,CAAC1jB,GAAG,CAAC;QAClE,IAAIwjB,gBAAgB,CAAC9H,IAAI,EAAE;UACvB,MAAMiI,WAAW,GAAG,MAAM,IAAI,CAACC,cAAc,EAAE;UAC/CP,GAAG,CAACQ,SAAS,CAACjmB,UAAU,CAACkmB,QAAQ,EAAE;YAAEC,QAAQ,EAAEJ;WAAa,CAAC,CAAC;UAC9DN,GAAG,CAAC/f,GAAG,EAAE;;QAEbN,OAAO,CAACwgB,gBAAgB,CAAC;OAC5B,CAAC;MACF,IAAI,CAACP,MAAM,CAACe,MAAM,CAAC,CAAC,CAAC,CAAC;KACzB,CAAC;;IAGF,MAAM,IAAIjhB,OAAO,CAAQC,OAAO;MAC5B,IAAIihB,KAAK,GAAG,CAAC;MACb,MAAMC,EAAE,GAAGC,WAAW,CAAC;QACnB,IAAK7kB,yBAAyB,CAACE,UAAU,GAAGF,yBAAyB,CAACC,WAAW,GAAI0kB,KAAK,EAAE;UACxF,MAAM/H,aAAa,CAACS,gCAAgC,EAAE;;QAG1D,IAAI,IAAI,CAACsG,MAAM,CAACmB,SAAS,EAAE;UACvBC,aAAa,CAACH,EAAE,CAAC;UACjBlhB,OAAO,EAAE;;QAEbihB,KAAK,EAAE;OACV,EAAE3kB,yBAAyB,CAACC,WAAW,CAAC;KAC5C,CAAC;IAEF,OAAO2jB,gBAAgB;;;;;;EAO3BU,cAAc;IACV,IAAI,CAAC,IAAI,CAACX,MAAM,EAAE;MACd,MAAM/G,aAAa,CAACO,iCAAiC,EAAE;;IAG3D,MAAM6H,OAAO,GAAG,IAAI,CAACrB,MAAM,CAACqB,OAAO,EAAE;IACrC,IAAI,CAACA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACA,OAAO,CAAC3jB,IAAI,EAAE;MAC1D,IAAI,CAAC4jB,WAAW,EAAE;MAClB,MAAMrI,aAAa,CAACI,qCAAqC,EAAE;;IAG/D,MAAM3b,IAAI,GAAG2jB,OAAO,IAAIA,OAAO,CAAC3jB,IAAI;IAEpC,UAAUvC,SAAS,CAACI,gBAAgBJ,SAAS,CAACK,aAAakC,MAAM;;;;;EAMrE4jB,WAAW;IACP,IAAI,CAAC,CAAC,IAAI,CAACtB,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACuB,KAAK,EAAE;;;;;AC5F/B;;;;AAKA,AA6BA;;;;;AAKA,MAAaC,uBAAwB,SAAQ5H,iBAAiB;;;;;;;;;;;;;;;;;;EAmB1D3b,YAAY4b,aAA4B;IACpC,KAAK,CAACA,aAAa,CAAC;IACpB,IAAI,IAAI,CAACC,MAAM,CAAC3T,MAAM,CAACsb,kBAAkB,EAAE;MACvC,IAAI,IAAI,CAAC3H,MAAM,CAAC3T,MAAM,CAACsb,kBAAkB,CAACC,iBAAiB,EAAE;QACzD,IAAI,CAACD,kBAAkB,GAAG,IAAI,CAAC3H,MAAM,CAAC3T,MAAM,CAACsb,kBAAkB;QAC/D,IAAI,CAACA,kBAAkB,CAAC5E,SAAS,CAAC,IAAI,CAAC/C,MAAM,CAACzT,MAAM,CAACT,aAAa,CAAC;OACtE,MAAM;QACH,IAAI,CAACyK,MAAM,CAACsR,OAAO,CAAC,yEAAyE,CAAC;;;;;;;;;;;;;EAcnG,MAAMC,wBAAwB,CAAC3hB,OAA0B;IAC5D,IAAI,CAACoQ,MAAM,CAAC8J,IAAI,CAAC,iCAAiC,EAAEla,OAAO,CAACma,aAAa,CAAC;IAC1E,MAAMC,YAAY,GAA4Bhb,MAAM,CAACwiB,MAAM,CAAC5hB,OAAO,EAAG,MAAM,IAAI,CAACqa,qBAAqB,CAACra,OAAO,CAAC,CAAC;IAChH,MAAMqb,sBAAsB,GAAG,IAAI,CAACC,gCAAgC,CAAC9f,KAAK,CAACmmB,wBAAwB,EAAEvH,YAAY,CAACD,aAAa,CAAC;IAChI,IAAI;MACA,MAAM0H,gBAAgB,GAAG,MAAM,IAAI,CAAChH,6BAA6B,CAC7DT,YAAY,CAACvW,SAAS,EACtBuW,YAAY,CAACD,aAAa,EAC1BkB,sBAAsB,EACtBP,SAAS,EACT9a,OAAO,CAAC4E,iBAAiB,CAC5B;MACD,MAAMkd,gBAAgB,GAAG,IAAIC,gBAAgB,CAACF,gBAAgB,CAAC;MAC/D,IAAI,CAACzR,MAAM,CAACmF,OAAO,CAAC,4BAA4B,EAAE6E,YAAY,CAACD,aAAa,CAAC;MAC7E,OAAO2H,gBAAgB,CAACvG,YAAY,CAACnB,YAAY,CAAC;KACrD,CAAC,OAAO9X,CAAC,EAAE;MACR,IAAIA,CAAC,YAAY2W,SAAS,EAAE;QACxB3W,CAAC,CAACkZ,gBAAgB,CAACpB,YAAY,CAACD,aAAa,CAAC;;MAElDkB,sBAAsB,CAACI,kBAAkB,CAACnZ,CAAC,CAAC;MAC5C,MAAMA,CAAC;;;;;;EAOf,MAAM0f,uBAAuB,CAAChiB,OAA2B;IACrD,MAAMma,aAAa,GAAGna,OAAO,CAACma,aAAa,IAAI,IAAI,CAACL,cAAc,CAAC/Q,aAAa,EAAE;IAClF,IAAI,CAACqH,MAAM,CAACY,KAAK,CAAC,gCAAgC,EAAEmJ,aAAa,CAAC;IAClE,MAAM;MAAE8H,WAAW;MAAEpC,eAAe;MAAEC,aAAa;MAAEoC,YAAY;MAAEC,cAAc,EAAEC,oBAAoB;MAAE,GAAGC;KAAqB,GAAGriB,OAAO;IAE3I,IAAI,IAAI,CAACwhB,kBAAkB,EAAE;MAAA;MACzB,MAAMc,aAAa,GAAkB;QACjC,GAAGD,mBAAmB;QACtB1e,QAAQ,EAAE,IAAI,CAACkW,MAAM,CAAChc,IAAI,CAAC8F,QAAQ;QACnCmb,MAAM,EAAE9e,OAAO,CAAC8e,MAAM,IAAIC,mBAAmB;QAC7C0B,WAAW,KAAKvlB,SAAS,CAACI,gBAAgBJ,SAAS,CAACK,WAAW;QAC/DsI,SAAS,EAAE7D,OAAO,CAAC6D,SAAS,IAAI,IAAI,CAACgW,MAAM,CAAChc,IAAI,CAACgG,SAAS;QAC1DsW,aAAa,EAAEA,aAAa;QAC5BoI,eAAe,EAAE;UACb,GAAGF,mBAAmB,CAACG,oBAAoB;UAC3C,GAAGH,mBAAmB,CAACI;SAC1B;QACDC,SAAS,2BAAEL,mBAAmB,CAAClX,OAAO,qBAA3B,sBAA6BwX;OAC3C;MACD,OAAO,IAAI,CAACnB,kBAAkB,CAACQ,uBAAuB,CAACM,aAAa,EAAEJ,YAAY,CAAC;;IAGvF,MAAM;MAAEha,QAAQ;MAAEE;KAAW,GAAG,MAAM,IAAI,CAAC0R,cAAc,CAAC7R,iBAAiB,EAAE;IAE7E,MAAMka,cAAc,GAAoBC,oBAAoB,IAAI,IAAIzC,cAAc,EAAE;IAEpF,IAAI;MACA,MAAMK,gBAAgB,GAAGmC,cAAc,CAACvC,iBAAiB,CAACC,eAAe,EAAEC,aAAa,CAAC;MACzF,MAAMW,WAAW,GAAG0B,cAAc,CAACzB,cAAc,EAAE;MAEnD,MAAMtG,YAAY,GAA4B;QAC1C,GAAGiI,mBAAmB;QACtBlI,aAAa,EAAEA,aAAa;QAC5B2E,MAAM,EAAE9e,OAAO,CAAC8e,MAAM,IAAIC,mBAAmB;QAC7C0B,WAAW,EAAEA,WAAW;QACxBnG,YAAY,EAAEC,YAAY,CAACC,KAAK;QAChCoI,aAAa,EAAExa,SAAS;QACxBya,mBAAmB,EAAEC,yBAAyB,CAACC;OAClD;MAED,MAAMC,WAAW,GAAG,MAAM,IAAI,CAAC/I,cAAc,CAACG,YAAY,CAAC;MAC3D,MAAM6H,WAAW,CAACe,WAAW,CAAC;MAC9B,MAAM1C,gBAAgB,GAAG,MAAMN,gBAAgB,CAACiD,OAAO,CAAC;QACpDd,cAAc,CAACd,WAAW,EAAE;OAC/B,CAAC;MAEF,IAAIf,gBAAgB,CAACrd,KAAK,EAAE;QACxB,MAAM,IAAIigB,WAAW,CAAC5C,gBAAgB,CAACrd,KAAK,EAAEqd,gBAAgB,CAAC9c,iBAAiB,EAAE8c,gBAAgB,CAAC6C,QAAQ,CAAC;OAC/G,MAAM,IAAI,CAAC7C,gBAAgB,CAAC9H,IAAI,EAAE;QAC/B,MAAMQ,aAAa,CAACM,+BAA+B,EAAE;;MAGzD,MAAMzO,UAAU,GAAGyV,gBAAgB,CAACxV,WAAW;MAC/C,MAAMsY,YAAY,GAA6B;QAC3C5K,IAAI,EAAE8H,gBAAgB,CAAC9H,IAAI;QAC3B7P,YAAY,EAAET,QAAQ;QACtB2C,UAAU,EAAEA,UAAU,IAAIuV,WAAe,CAACxc,YAAY;QACtD,GAAGwW;OACN;MACD,OAAO,IAAI,CAACa,kBAAkB,CAACmI,YAAY,CAAC;KAC/C,CAAC,OAAO9gB,CAAC,EAAE;MACR6f,cAAc,CAACd,WAAW,EAAE;MAC5B,MAAM/e,CAAC;;;;;;;;EASf,MAAMwZ,kBAAkB,CAAC9b,OAA0B;IAC/C,MAAMma,aAAa,GAAGna,OAAO,CAACma,aAAa,IAAI,IAAI,CAACL,cAAc,CAAC/Q,aAAa,EAAE;IAClF,IAAI,CAACqH,MAAM,CAACY,KAAK,CAAC,2BAA2B,EAAEmJ,aAAa,CAAC;IAE7D,IAAI,IAAI,CAACqH,kBAAkB,EAAE;MACzB,MAAMc,aAAa,GAAkB;QACjC,GAAGtiB,OAAO;QACV2D,QAAQ,EAAE,IAAI,CAACkW,MAAM,CAAChc,IAAI,CAAC8F,QAAQ;QACnCmb,MAAM,EAAE9e,OAAO,CAAC8e,MAAM,IAAIC,mBAAmB;QAC7C0B,WAAW,KAAKvlB,SAAS,CAACI,gBAAgBJ,SAAS,CAACK,WAAW;QAC/DsI,SAAS,EAAE7D,OAAO,CAAC6D,SAAS,IAAI,IAAI,CAACgW,MAAM,CAAChc,IAAI,CAACgG,SAAS;QAC1DsW,aAAa,EAAEA,aAAa;QAC5BoI,eAAe,EAAEviB,OAAO,CAACyiB,oBAAoB;QAC7CC,SAAS,EAAE1iB,OAAO,CAACmL,OAAO,CAACwX,eAAe;QAC1C5G,YAAY,EAAE/b,OAAO,CAAC+b,YAAY,IAAI;OACzC;MACD,OAAO,IAAI,CAACyF,kBAAkB,CAAC1F,kBAAkB,CAACwG,aAAa,CAAC;;IAGpE,OAAO,KAAK,CAACxG,kBAAkB,CAAC9b,OAAO,CAAC;;;;;;;EAQ5C,MAAMqjB,OAAO,CAACrjB,OAAuB;IACjC,IAAI,IAAI,CAACwhB,kBAAkB,IAAIxhB,OAAO,CAACmL,OAAO,CAACwX,eAAe,EAAE;MAC5D,MAAMW,cAAc,GAAyB;QACzC3f,QAAQ,EAAE,IAAI,CAACkW,MAAM,CAAChc,IAAI,CAAC8F,QAAQ;QACnC+e,SAAS,EAAE1iB,OAAO,CAACmL,OAAO,CAACwX,eAAe;QAC1CxI,aAAa,EAAEna,OAAO,CAACma,aAAa,IAAI,IAAI,CAACL,cAAc,CAAC/Q,aAAa;OAC5E;MACD,MAAM,IAAI,CAACyY,kBAAkB,CAAC6B,OAAO,CAACC,cAAc,CAAC;;IAGzD,MAAM,IAAI,CAAC/G,aAAa,EAAE,CAACtF,aAAa,CAACjX,OAAO,CAACmL,OAAO,CAAC;;;;;;EAO7D,MAAMqL,cAAc;IAChB,IAAI,IAAI,CAACgL,kBAAkB,EAAE;MACzB,MAAMrH,aAAa,GAAG,IAAI,CAACL,cAAc,CAAC/Q,aAAa,EAAE;MACzD,OAAO,IAAI,CAACyY,kBAAkB,CAAChL,cAAc,CAAC,IAAI,CAACqD,MAAM,CAAChc,IAAI,CAAC8F,QAAQ,EAAEwW,aAAa,CAAC;;IAG3F,OAAO,IAAI,CAACoC,aAAa,EAAE,CAAC/F,cAAc,EAAE;;;;ACnOpD;;;;AAKA,AAMA;;;;AAIA,MAAa+M,eAAe;;;;;EAcjB,OAAOC,aAAa,CAAClF,SAAiB;IACzC,MAAMta,eAAe,GAAG,IAAIuf,eAAe,EAAE;IAC7Cvf,eAAe,CAACyf,GAAG,GAAGnF,SAAS;IAC/B,OAAOta,eAAe;;;;;;;;EASnB,OAAO0f,eAAe,CAACxf,UAAkB,EAAEC,UAAkB,EAAEwf,iBAA0B;IAC5F,MAAM3f,eAAe,GAAG,IAAIuf,eAAe,EAAE;IAC7Cvf,eAAe,CAACG,UAAU,GAAGA,UAAU;IACvCH,eAAe,CAACE,UAAU,GAAGA,UAAU;IACvC,IAAIyf,iBAAiB,EAAE;MACnB3f,eAAe,CAAC2f,iBAAiB,GAAG,IAAI,CAACC,gBAAgB,CAACD,iBAAiB,CAAC;;IAEhF,OAAO3f,eAAe;;;;;;;;EASnBua,MAAM,CAACzE,cAA8B,EAAE+J,MAAc,EAAEC,WAAmB;;IAE7E,IAAI,IAAI,CAAC3f,UAAU,IAAI,IAAI,CAACD,UAAU,EAAE;MAEpC,IAAI,IAAI,CAACuf,GAAG,IAAI,CAAC,IAAI,CAACM,SAAS,EAAE,IAAIF,MAAM,KAAK,IAAI,CAACA,MAAM,IAAIC,WAAW,KAAK,IAAI,CAACA,WAAW,EAAE;QAC7F,OAAO,IAAI,CAACL,GAAG;;MAGnB,OAAO,IAAI,CAACO,SAAS,CAAClK,cAAc,EAAE+J,MAAM,EAAEC,WAAW,CAAC;;;;;;IAO9D,IAAI,IAAI,CAACL,GAAG,EAAE;MACV,OAAO,IAAI,CAACA,GAAG;;IAGnB,MAAMhH,eAAe,CAACwH,2BAA2B,EAAE;;;;;EAM/CD,SAAS,CAAClK,cAA8B,EAAE+J,MAAc,EAAEC,WAAmB;IAEjF,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,MAAMI,QAAQ,GAAGC,SAAS,CAACC,UAAU,EAAE;IACvC,IAAI,CAACC,cAAc,GAAGH,QAAQ,GAAG,GAAG;IAEpC,MAAMpiB,MAAM,GAAc;MACtBwiB,GAAG,EAAE7oB,YAAY,CAACE,OAAO;MACzB4oB,GAAG,EAAEzd,aAAa,CAACK,eAAe,CAAC,IAAI,CAACjD,UAAU,EAAE,KAAK;KAC5D;IAED,IAAI,IAAI,CAACyf,iBAAiB,EAAE;MACxBvkB,MAAM,CAACwiB,MAAM,CAAC9f,MAAM,EAAE;QAClBsC,GAAG,EAAE,IAAI,CAACuf;OACS,CAAC;;IAG5B,MAAMa,OAAO,GAAG;MACZ,CAAC/oB,YAAY,CAACK,QAAQ,GAAG,IAAI,CAACgoB,WAAW;MACzC,CAACroB,YAAY,CAACM,eAAe,GAAG,IAAI,CAACsoB,cAAc;MACnD,CAAC5oB,YAAY,CAACO,MAAM,GAAG,IAAI,CAAC6nB,MAAM;MAClC,CAACpoB,YAAY,CAACQ,OAAO,GAAG,IAAI,CAAC4nB,MAAM;MACnC,CAACpoB,YAAY,CAACS,UAAU,GAAGgoB,QAAQ;MACnC,CAACzoB,YAAY,CAACU,MAAM,GAAG2d,cAAc,CAAC/Q,aAAa;KACtD;IAED,IAAI,CAAC0a,GAAG,GAAGgB,IAAI,CAACD,OAAO,EAAE,IAAI,CAACrgB,UAAU,EAAE;MAAErC;KAAQ,CAAC;IACrD,OAAO,IAAI,CAAC2hB,GAAG;;;;;EAMXM,SAAS;IACb,OAAO,IAAI,CAACM,cAAc,GAAGF,SAAS,CAACC,UAAU,EAAE;;;;;;EAOhD,OAAOR,gBAAgB,CAACD,iBAAyB;;;;;;;;IAQpD,MAAMe,gBAAgB,GAAG,uEAAuE;IAChG,MAAMC,KAAK,GAAa,EAAE;IAE1B,IAAIC,OAAO;IACX,OAAO,CAACA,OAAO,GAAGF,gBAAgB,CAACG,IAAI,CAAClB,iBAAiB,CAAC,MAAM,IAAI,EAAE;;MAElEgB,KAAK,CAAC3jB,IAAI,CAAC4jB,OAAO,CAAC,CAAC,CAAC,CAACxd,OAAO,CAAC,QAAQ,EAAElM,WAAS,CAAC0I,YAAY,CAAC,CAAC;;IAGpE,OAAO+gB,KAAK;;;;AC7IpB;;;;AAKA,AAsBA;;;;;AAKA,MAAaG,6BAA8B,SAAQnL,iBAAiB;;;;;;;;;;;;;;;;;;;;EAsBhE3b,YAAY4b,aAA4B;IACpC,KAAK,CAACA,aAAa,CAAC;IACpB,IAAI,CAACmL,mBAAmB,CAAC,IAAI,CAAClL,MAAM,CAAC;IACrC,IAAI,CAACmL,gBAAgB,GAAGlK,SAAS;;;;;;;;EASrCmK,mBAAmB,CAACC,QAA2B;IAC3C,IAAI,CAACF,gBAAgB,GAAGE,QAAQ;;;;;EAM7B,MAAMC,8BAA8B,CAACnlB,OAAgC;IACxE,IAAI,CAACoQ,MAAM,CAAC8J,IAAI,CAAC,uCAAuC,EAAEla,OAAO,CAACma,aAAa,CAAC;;IAGhF,IAAInW,eAAe;IACnB,IAAIhE,OAAO,CAACgE,eAAe,EAAE;MACzBA,eAAe,GAAG;QACdsa,SAAS,EAAEte,OAAO,CAACgE,eAAe;QAClCya,aAAa,EAAEX,SAAa,CAAC1iB;OAChC;;IAGL,MAAMgqB,WAAW,GAAG,MAAM,IAAI,CAAC/K,qBAAqB,CAACra,OAAO,CAAC;;IAG7D,MAAMqlB,gBAAgB,GAAG;MACrB,GAAGD,WAAW;MACdtG,MAAM,EAAEsG,WAAW,CAACtG,MAAM,CAAC9K,MAAM,CAAEsR,KAAa,IAAK,CAACvG,mBAAmB,CAAClK,QAAQ,CAACyQ,KAAK,CAAC;KAC5F;IAED,MAAMlL,YAAY,GAAkC;MAChD,GAAGpa,OAAO;MACV,GAAGqlB,gBAAgB;MACnBrhB;KACH;IAED,MAAM8Y,wBAAwB,GAA6B;MACvDyI,WAAW,EAAEnL,YAAY,CAACmL,WAAW;MACrCC,iBAAiB,EAAExH,OAAO,CAACyH,GAAG,CAAC7qB,2BAA2B;KAC7D;IAED,MAAMygB,sBAAsB,GAAG,IAAI,CAACC,gCAAgC,CAAC9f,KAAK,CAAC2pB,8BAA8B,EAAE/K,YAAY,CAACD,aAAa,EAAEC,YAAY,CAACsL,SAAS,CAAC;IAC9J,IAAI;MACA,MAAMC,sBAAsB,GAAG,MAAM,IAAI,CAAC9K,6BAA6B,CACnET,YAAY,CAACvW,SAAS,EACtBuW,YAAY,CAACD,aAAa,EAC1BkB,sBAAsB,EACtByB,wBAAwB,EACxB9c,OAAO,CAAC4E,iBAAiB,CAC5B;MACD,MAAMghB,sBAAsB,GAAG,IAAIC,sBAAsB,CAACF,sBAAsB,EAAE,IAAI,CAACX,gBAAgB,CAAC;MACxG,IAAI,CAAC5U,MAAM,CAACmF,OAAO,CAAC,kCAAkC,EAAE6E,YAAY,CAACD,aAAa,CAAC;MACnF,OAAOyL,sBAAsB,CAACrK,YAAY,CAACnB,YAAY,CAAC;KAC3D,CAAC,OAAO9X,CAAC,EAAE;MACR,IAAIA,CAAC,YAAY2W,SAAS,EAAE;QACxB3W,CAAC,CAACkZ,gBAAgB,CAACpB,YAAY,CAACD,aAAa,CAAC;;MAElDkB,sBAAsB,CAACI,kBAAkB,CAACnZ,CAAC,CAAC;MAC5C,MAAMA,CAAC;;;;;;;;;;;;;;EAeR,MAAMwjB,sBAAsB,CAAC9lB,OAA0B;IAC1D,IAAI,CAACoQ,MAAM,CAAC8J,IAAI,CAAC,+BAA+B,EAAEla,OAAO,CAACma,aAAa,CAAC;IACxE,MAAMC,YAAY,GAA4B;MAC1C,GAAGpa,OAAO;MACV,IAAI,MAAM,IAAI,CAACqa,qBAAqB,CAACra,OAAO,CAAC;KAChD;IACD,IAAI;MACA,MAAM+lB,gBAAgB,GAAG,MAAM,IAAI,CAAClL,6BAA6B,CAC7DT,YAAY,CAACvW,SAAS,EACtBuW,YAAY,CAACD,aAAa,EAC1BW,SAAS,EACTA,SAAS,EACT9a,OAAO,CAAC4E,iBAAiB,CAC5B;MACD,MAAMohB,SAAS,GAAG,IAAIC,gBAAgB,CAACF,gBAAgB,CAAC;MACxD,IAAI,CAAC3V,MAAM,CAACmF,OAAO,CAAC,6BAA6B,EAAE6E,YAAY,CAACD,aAAa,CAAC;MAC9E,OAAO6L,SAAS,CAACzK,YAAY,CAACnB,YAAY,CAAC;KAC9C,CAAC,OAAO9X,CAAC,EAAE;MACR,IAAIA,CAAC,YAAY2W,SAAS,EAAE;QACxB3W,CAAC,CAACkZ,gBAAgB,CAACpB,YAAY,CAACD,aAAa,CAAC;;MAElD,MAAM7X,CAAC;;;EAIPyiB,mBAAmB,CAACnL,aAA4B;IACpD,MAAMsM,oBAAoB,GAAG,CAACvc,WAAW,CAACC,OAAO,CAACgQ,aAAa,CAAC/b,IAAI,CAACkG,YAAY,CAAC;IAClF,MAAMoiB,uBAAuB,GAAG,CAACxc,WAAW,CAACC,OAAO,CAACgQ,aAAa,CAAC/b,IAAI,CAACmG,eAAe,CAAC;IACxF,MAAMoiB,WAAW,GAAGxM,aAAa,CAAC/b,IAAI,CAACoG,iBAAiB,IAAI;MACxDC,UAAU,EAAEhJ,WAAS,CAAC0I,YAAY;MAClCO,UAAU,EAAEjJ,WAAS,CAAC0I;KACzB;IACD,MAAMyiB,mBAAmB,GAAG,CAAC1c,WAAW,CAACC,OAAO,CAACwc,WAAW,CAACliB,UAAU,CAAC,IAAI,CAACyF,WAAW,CAACC,OAAO,CAACwc,WAAW,CAACjiB,UAAU,CAAC;;;;;IAMxH,IAAI,IAAI,CAAC6gB,gBAAgB,EAAE;MACvB;;;IAIJ,IACIkB,oBAAoB,IAAIC,uBAAuB,IAC/CA,uBAAuB,IAAIE,mBAAmB,IAC9CH,oBAAoB,IAAIG,mBAAmB,EAAE;MAC7C,MAAM5J,eAAe,CAAC6J,4BAA4B,EAAE;;IAGxD,IAAI1M,aAAa,CAAC/b,IAAI,CAACkG,YAAY,EAAE;MACjC,IAAI,CAACA,YAAY,GAAG6V,aAAa,CAAC/b,IAAI,CAACkG,YAAY;MACnD;;IAGJ,IAAI6V,aAAa,CAAC/b,IAAI,CAACmG,eAAe,EAAE;MACpC,IAAI,CAACA,eAAe,GAAGuf,eAAe,CAACC,aAAa,CAAC5J,aAAa,CAAC/b,IAAI,CAACmG,eAAe,CAAC;MACxF;;IAGJ,IAAI,CAACqiB,mBAAmB,EAAE;MACtB,MAAM5J,eAAe,CAAC6J,4BAA4B,EAAE;KACvD,MAAM;MAAA;MACH,IAAI,CAACtiB,eAAe,GAAGuf,eAAe,CAACG,eAAe,CAAC0C,WAAW,CAACliB,UAAU,EAAEkiB,WAAW,CAACjiB,UAAU,2BAAEyV,aAAa,CAAC/b,IAAI,CAACoG,iBAAiB,qBAApC,sBAAsCG,GAAG,CAAC;;;;;ACvM7J;;;;AAKA,MAKamiB,sBAAsB;EAI/BvoB,YAAYwoB,MAAoB,EAAEC,gBAAmC;IACjE,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;;EAGrC,MAAM9P,iBAAiB,CAACF,YAA+B;IAC1D,MAAMiQ,YAAY,GAAG,MAAM,IAAI,CAACD,gBAAgB,CAACE,MAAM,EAAE;IACzD,MAAMC,SAAS,GAAG,MAAM,IAAI,CAACJ,MAAM,CAACK,GAAG,CAACH,YAAY,CAAC;IACrDjQ,YAAY,CAACuD,UAAU,CAAC3D,WAAW,CAACuQ,SAAS,CAAC;;EAG3C,MAAMhQ,gBAAgB,CAACH,YAA+B;IACzD,IAAIA,YAAY,CAACb,eAAe,EAAE;MAC9B,MAAMkR,OAAO,GAAIrQ,YAAY,CAACuD,UAAyB,CAACzD,UAAU,EAAE;MACpE,MAAMwQ,eAAe,GAAG3nB,MAAM,CAAC4nB,MAAM,CAACF,OAAO,CAAC,CAAC9S,MAAM,CAAC1C,KAAK,IAAIlG,aAAa,CAACyG,eAAe,CAACP,KAAe,CAAC,CAAC;MAE9G,IAAIyV,eAAe,CAACznB,MAAM,GAAG,CAAC,EAAE;QAC5B,MAAM8P,aAAa,GAAG2X,eAAe,CAAC,CAAC,CAAkB;QACzD,MAAML,YAAY,GAAG,MAAM,IAAI,CAACD,gBAAgB,CAACQ,UAAU,CAAC7X,aAAa,CAAC;QAE1E,MAAM,IAAI,CAACoX,MAAM,CAACjkB,GAAG,CAACmkB,YAAY,EAAEjQ,YAAY,CAACuD,UAAU,CAAC/D,SAAS,EAAE,CAAC;;;;;;;;"}