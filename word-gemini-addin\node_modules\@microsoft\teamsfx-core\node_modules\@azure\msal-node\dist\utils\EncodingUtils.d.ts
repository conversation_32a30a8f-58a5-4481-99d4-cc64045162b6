/// <reference types="node" />
export declare class EncodingUtils {
    /**
     * 'utf8': Multibyte encoded Unicode characters. Many web pages and other document formats use UTF-8.
     * 'base64': Base64 encoding.
     *
     * @param str text
     */
    static base64Encode(str: string, encoding?: BufferEncoding): string;
    /**
     * encode a URL
     * @param str
     */
    static base64EncodeUrl(str: string, encoding?: BufferEncoding): string;
    /**
     * 'utf8': Multibyte encoded Unicode characters. Many web pages and other document formats use UTF-8.
     * 'base64': Base64 encoding.
     *
     * @param base64Str Base64 encoded text
     */
    static base64Decode(base64Str: string): string;
    /**
     * @param base64Str Base64 encoded Url
     */
    static base64DecodeUrl(base64Str: string): string;
}
//# sourceMappingURL=EncodingUtils.d.ts.map