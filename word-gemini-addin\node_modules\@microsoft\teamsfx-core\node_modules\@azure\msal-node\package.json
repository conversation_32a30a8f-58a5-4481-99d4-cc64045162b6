{"name": "@azure/msal-node", "version": "1.18.4", "author": {"name": "Microsoft", "email": "<EMAIL>", "url": "https://www.microsoft.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/AzureAD/microsoft-authentication-library-for-js.git"}, "description": "Microsoft Authentication Library for Node", "keywords": ["js", "ts", "node", "AAD", "msal", "o<PERSON>h"], "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"start": "tsdx watch --tsconfig ./tsconfig.build.json", "build": "tsdx build --tsconfig ./tsconfig.build.json", "build:modules:watch": "tsdx watch --verbose", "test": "tsdx test .*.spec.*", "test:watch": "tsdx test .*.spec.* --watch", "test:coverage": "tsdx test .*.spec.* --coverage", "lint": "cd ../../ && npm run lint:node", "lint:fix": "npm run lint -- -- --fix", "build:all": "npm run build:common && npm run build", "build:common": "cd ../msal-common && npm run build", "link:localDeps": "npx lerna bootstrap --scope @azure/msal-common --scope @azure/msal-node", "prepack": "npm run build:all"}, "beachball": {"disallowedChangeTypes": ["major"]}, "module": "dist/msal-node.esm.js", "devDependencies": {"@microsoft/api-extractor": "^7.19.4", "@types/jest": "^25.2.3", "@types/jsonwebtoken": "^8.5.5", "@types/node": "^13.13.4", "@types/uuid": "^7.0.0", "husky": "^4.2.3", "tsdx": "^0.14.1", "tslib": "^1.10.0", "typescript": "^3.8.3", "yargs": "^17.3.1"}, "dependencies": {"@azure/msal-common": "13.3.1", "jsonwebtoken": "^9.0.0", "uuid": "^8.3.0"}, "engines": {"node": "10 || 12 || 14 || 16 || 18"}}