/**
 * This file was automatically generated by json-schema-to-typescript.
 * DO NOT MODIFY IT BY HAND. Instead, modify the source JSONSchema file,
 * and run json-schema-to-typescript to regenerate this file.
 */
export type ExtensionRuntimesActions = ExtensionRuntimesActionsItem[];
export type ExtensionRuntimesArray = {
    requirements?: RequirementsExtensionElement;
    /**
     * A unique identifier for this runtime within the app.  Maximum length is 64 characters.
     */
    id: string;
    /**
     * Supports running functions and launching pages.
     */
    type?: "general";
    code: {
        /**
         * URL of the .html page to be loaded in browser-based runtimes.
         */
        page: string;
        /**
         * URL of the .js script file to be loaded in UI-less runtimes.
         */
        script?: string;
    };
    /**
     * Runtimes with a short lifetime do not preserve state across executions. Runtimes with a long lifetime do.
     */
    lifetime?: "short" | "long";
    actions?: ExtensionRuntimesActions;
}[];
export type ExtensionContexts = ("mailRead" | "mailCompose" | "meetingDetailsOrganizer" | "meetingDetailsAttendee")[];
export type ExtensionRibbonsArray = {
    requirements?: RequirementsExtensionElement;
    contexts?: ExtensionContexts;
    tabs: ExtensionRibbonsArrayTabsItem[];
}[];
export type ExtensionAutoRunEventsArray = {
    requirements?: RequirementsExtensionElement;
    events: {
        type: string;
        /**
         * The ID of an action defined in runtimes. Maximum length is 64 characters.
         */
        actionId: string;
        options?: {
            sendMode: "promptUser" | "softBlock" | "block";
        };
    }[];
}[];
export type ExtensionAlternateVersionsArray = {
    requirements?: RequirementsExtensionElement;
    prefer?: {
        comAddin?: {
            /**
             * Program ID of the alternate com extension. Maximum length is 64 characters.
             */
            progId: string;
        };
        [k: string]: unknown;
    };
    hide?: {
        storeOfficeAddin?: {
            /**
             * Solution ID of an in-market add-in to hide. Maximum length is 64 characters.
             */
            officeAddinId: string;
            /**
             * Asset ID of the in-market add-in to hide. Maximum length is 64 characters.
             */
            assetId: string;
        };
        customOfficeAddin?: {
            /**
             * Solution ID of the in-market add-in to hide. Maximum length is 64 characters.
             */
            officeAddinId: string;
        };
        [k: string]: unknown;
    };
}[];
/**
 * The set of extensions for this app. Currently only one extensions per app is supported.
 */
export type ElementExtensions = {
    requirements?: RequirementsExtensionElement;
    runtimes?: ExtensionRuntimesArray;
    ribbons?: ExtensionRibbonsArray;
    autoRunEvents?: ExtensionAutoRunEventsArray;
    alternates?: ExtensionAlternateVersionsArray;
    /**
     * The url for your extension, used to validate Exchange user identity tokens.
     */
    audienceClaimUrl?: string;
}[];
export interface DevPreviewSchema {
    $schema?: string;
    /**
     * The version of the schema this manifest is using.
     */
    manifestVersion: "devPreview" | "m365DevPreview";
    /**
     * The version of the app. Changes to your manifest should cause a version change. This version string must follow the semver standard (http://semver.org).
     */
    version: string;
    /**
     * A unique identifier for this app. This id must be a GUID.
     */
    id: string;
    /**
     * A unique identifier for this app in reverse domain notation. E.g: com.example.myapp
     */
    packageName?: string;
    localizationInfo?: {
        /**
         * The language tag of the strings in this top level manifest file.
         */
        defaultLanguageTag: string;
        additionalLanguages?: {
            /**
             * The language tag of the strings in the provided file.
             */
            languageTag: string;
            /**
             * A relative file path to a the .json file containing the translated strings.
             */
            file: string;
        }[];
    };
    developer: {
        /**
         * The display name for the developer.
         */
        name: string;
        /**
         * The Microsoft Partner Network ID that identifies the partner organization building the app. This field is not required, and should only be used if you are already part of the Microsoft Partner Network. More info at https://aka.ms/partner
         */
        mpnId?: string;
        /**
         * The url to the page that provides support information for the app.
         */
        websiteUrl: string;
        /**
         * The url to the page that provides privacy information for the app.
         */
        privacyUrl: string;
        /**
         * The url to the page that provides the terms of use for the app.
         */
        termsOfUseUrl: string;
    };
    name: {
        /**
         * A short display name for the app.
         */
        short: string;
        /**
         * The full name of the app, used if the full app name exceeds 30 characters.
         */
        full: string;
    };
    description: {
        /**
         * A short description of the app used when space is limited. Maximum length is 80 characters.
         */
        short: string;
        /**
         * The full description of the app. Maximum length is 4000 characters.
         */
        full: string;
    };
    icons: {
        /**
         * A relative file path to a transparent PNG outline icon. The border color needs to be white. Size 32x32.
         */
        outline: string;
        /**
         * A relative file path to a full color PNG icon. Size 192x192.
         */
        color: string;
    };
    /**
     * A color to use in conjunction with the icon. The value must be a valid HTML color code starting with '#', for example `#4464ee`.
     */
    accentColor: string;
    /**
     * These are tabs users can optionally add to their channels and 1:1 or group chats and require extra configuration before they are added. Configurable tabs are not supported in the personal scope. Currently only one configurable tab per app is supported.
     */
    configurableTabs?: {
        /**
         * The url to use when configuring the tab.
         */
        configurationUrl: string;
        /**
         * A value indicating whether an instance of the tab's configuration can be updated by the user after creation.
         */
        canUpdateConfiguration?: boolean;
        /**
         * Specifies whether the tab offers an experience in the context of a channel in a team, in a 1:1 or group chat, or in an experience scoped to an individual user alone. These options are non-exclusive. Currently, configurable tabs are only supported in the teams and groupchats scopes.
         */
        scopes: ("team" | "groupChat")[];
        /**
         * The set of meetingSurfaceItem scopes that a tab belong to
         */
        meetingSurfaces?: ("sidePanel" | "stage")[];
        /**
         * The set of contextItem scopes that a tab belong to
         */
        context?: ("personalTab" | "channelTab" | "privateChatTab" | "meetingChatTab" | "meetingDetailsTab" | "meetingSidePanel" | "meetingStage" | "callingSidePanel")[];
        /**
         * The set of supportedPlatform scopes that a tab belong to
         */
        supportedPlatform?: ("desktop" | "mobile" | "teamsMeetingDevices")[];
        /**
         * A relative file path to a tab preview image for use in SharePoint. Size 1024x768.
         */
        sharePointPreviewImage?: string;
        /**
         * Defines how your tab will be made available in SharePoint.
         */
        supportedSharePointHosts?: ("sharePointFullPage" | "sharePointWebPart")[];
    }[];
    /**
     * A set of tabs that may be 'pinned' by default, without the user adding them manually. Static tabs declared in personal scope are always pinned to the app's personal experience. Static tabs do not currently support the 'teams' scope.
     */
    staticTabs?: {
        /**
         * A unique identifier for the entity which the tab displays.
         */
        entityId: string;
        /**
         * The display name of the tab.
         */
        name?: string;
        /**
         * The url which points to the entity UI to be displayed in the Teams canvas.
         */
        contentUrl?: string;
        /**
         * The Microsoft App ID specified for the bot in the Bot Framework portal (https://dev.botframework.com/bots)
         */
        contentBotId?: string;
        /**
         * The url to point at if a user opts to view in a browser.
         */
        websiteUrl?: string;
        /**
         * The url to direct a user's search queries.
         */
        searchUrl?: string;
        /**
         * Specifies whether the tab offers an experience in the context of a channel in a team, or an experience scoped to an individual user alone or a group chat. These options are non-exclusive. Currently static tabs are only supported in the 'personal' scope.
         */
        scopes: ("team" | "personal" | "groupChat")[];
        /**
         * The set of contextItem scopes that a tab belong to
         */
        context?: ("personalTab" | "channelTab" | "privateChatTab" | "meetingChatTab" | "meetingDetailsTab" | "meetingSidePanel" | "meetingStage" | "teamLevelApp")[];
        /**
         * The set of supportedPlatform scopes that a tab belong to
         */
        supportedPlatform?: ("desktop" | "mobile" | "teamsMeetingDevices")[];
    }[];
    /**
     * The set of bots for this app. Currently only one bot per app is supported.
     */
    bots?: {
        /**
         * The Microsoft App ID specified for the bot in the Bot Framework portal (https://dev.botframework.com/bots)
         */
        botId: string;
        configuration?: {
            team?: {
                parameters?: {
                    /**
                     * Name of the parameter.
                     */
                    name: string;
                    /**
                     * Type of the parameter
                     */
                    inputType?: "text" | "textarea" | "number" | "date" | "time" | "toggle" | "choiceset";
                    /**
                     * Title of the parameter.
                     */
                    title: string;
                    /**
                     * Description of the parameter.
                     */
                    description?: string;
                    /**
                     * Initial value for the parameter
                     */
                    value?: string;
                    /**
                     * The choice options for the parameter
                     */
                    choices?: {
                        /**
                         * Title of the choice
                         */
                        title: string;
                        /**
                         * Value of the choice
                         */
                        value: string;
                    }[];
                }[];
                taskInfo?: {
                    /**
                     * Task module title
                     */
                    title?: string;
                    /**
                     * Task module width - either a number in pixels or default layout such as 'large', 'medium', or 'small'
                     */
                    width?: string;
                    /**
                     * Task module height - either a number in pixels or default layout such as 'large', 'medium', or 'small'
                     */
                    height?: string;
                    /**
                     * Task module URL
                     */
                    url?: string;
                };
            };
        };
        /**
         * This value describes whether or not the bot utilizes a user hint to add the bot to a specific channel.
         */
        needsChannelSelector?: boolean;
        /**
         * A value indicating whether or not the bot is a one-way notification only bot, as opposed to a conversational bot.
         */
        isNotificationOnly?: boolean;
        /**
         * A value indicating whether the team's Office group needs to be security enabled.
         */
        requiresSecurityEnabledGroup?: boolean;
        /**
         * A value indicating whether the bot supports uploading/downloading of files.
         */
        supportsFiles?: boolean;
        /**
         * A value indicating whether the bot supports audio calling.
         */
        supportsCalling?: boolean;
        /**
         * A value indicating whether the bot supports video calling.
         */
        supportsVideo?: boolean;
        /**
         * Specifies whether the bot offers an experience in the context of a channel in a team, in a 1:1 or group chat, or in an experience scoped to an individual user alone. These options are non-exclusive.
         */
        scopes: ("team" | "personal" | "groupChat")[];
        /**
         * The list of commands that the bot supplies, including their usage, description, and the scope for which the commands are valid. A separate command list should be used for each scope.
         */
        commandLists?: {
            /**
             * Specifies the scopes for which the command list is valid
             */
            scopes: ("team" | "personal" | "groupChat")[];
            commands: {
                /**
                 * The bot command name
                 */
                title: string;
                /**
                 * A simple text description or an example of the command syntax and its arguments.
                 */
                description: string;
            }[];
        }[];
    }[];
    /**
     * The set of Office365 connectors for this app. Currently only one connector per app is supported.
     */
    connectors?: {
        /**
         * A unique identifier for the connector which matches its ID in the Connectors Developer Portal.
         */
        connectorId: string;
        /**
         * The url to use for configuring the connector using the inline configuration experience.
         */
        configurationUrl?: string;
        /**
         * Specifies whether the connector offers an experience in the context of a channel in a team, or an experience scoped to an individual user alone. Currently, only the team scope is supported.
         */
        scopes: "team"[];
    }[];
    /**
     * Subscription offer associated with this app.
     */
    subscriptionOffer?: {
        /**
         * A unique identifier for the Commercial Marketplace Software as a Service Offer.
         */
        offerId: string;
    };
    /**
     * The set of compose extensions for this app. Currently only one compose extension per app is supported.
     */
    composeExtensions?: {
        /**
         * The Microsoft App ID specified for the bot powering the compose extension in the Bot Framework portal (https://dev.botframework.com/bots)
         */
        botId: string;
        /**
         * A value indicating whether the configuration of a compose extension can be updated by the user.
         */
        canUpdateConfiguration?: boolean;
        commands: {
            /**
             * Id of the command.
             */
            id: string;
            /**
             * Type of the command
             */
            type?: "query" | "action";
            /**
             * Context where the command would apply
             */
            context?: ("compose" | "commandBox" | "message")[];
            /**
             * Title of the command.
             */
            title: string;
            /**
             * Description of the command.
             */
            description?: string;
            /**
             * A boolean value that indicates if the command should be run once initially with no parameter.
             */
            initialRun?: boolean;
            /**
             * A boolean value that indicates if it should fetch task module dynamically
             */
            fetchTask?: boolean;
            parameters?: {
                /**
                 * Name of the parameter.
                 */
                name: string;
                /**
                 * Type of the parameter
                 */
                inputType?: "text" | "textarea" | "number" | "date" | "time" | "toggle" | "choiceset";
                /**
                 * Title of the parameter.
                 */
                title: string;
                /**
                 * Description of the parameter.
                 */
                description?: string;
                /**
                 * Initial value for the parameter
                 */
                value?: string;
                /**
                 * The choice options for the parameter
                 */
                choices?: {
                    /**
                     * Title of the choice
                     */
                    title: string;
                    /**
                     * Value of the choice
                     */
                    value: string;
                }[];
            }[];
            taskInfo?: {
                /**
                 * Initial dialog title
                 */
                title?: string;
                /**
                 * Dialog width - either a number in pixels or default layout such as 'large', 'medium', or 'small'
                 */
                width?: string;
                /**
                 * Dialog height - either a number in pixels or default layout such as 'large', 'medium', or 'small'
                 */
                height?: string;
                /**
                 * Initial webview URL
                 */
                url?: string;
            };
        }[];
        /**
         * A list of handlers that allow apps to be invoked when certain conditions are met
         */
        messageHandlers?: {
            /**
             * Type of the message handler
             */
            type: "link";
            value: {
                /**
                 * A list of domains that the link message handler can register for, and when they are matched the app will be invoked
                 */
                domains?: string[];
                /**
                 * A boolean value that indicates whether the app's link message handler supports anonymous invoke flow. [Deprecated]. This property has been superceded by 'supportsAnonymizedPayloads'.
                 */
                supportsAnonymousAccess?: boolean;
                /**
                 * A boolean value that indicates whether the app's link message handler supports anonymous invoke flow.
                 */
                supportsAnonymizedPayloads?: boolean;
                [k: string]: unknown;
            };
        }[];
    }[];
    scopeConstraints?: {
        /**
         * A list of team thread ids to which your app is restricted to
         */
        teams?: {
            /**
             * Team's thread Id
             */
            id: string;
        }[];
        /**
         * A list of chat thread ids to which your app is restricted to
         */
        groupChats?: {
            /**
             * Chat's thread Id
             */
            id: string;
        }[];
    };
    /**
     * Specifies the permissions the app requests from users.
     */
    permissions?: ("identity" | "messageTeamMembers")[];
    /**
     * Specify the native features on a user's device that your app may request access to.
     */
    devicePermissions?: ("geolocation" | "media" | "notifications" | "midi" | "openExternal")[];
    /**
     * A list of valid domains from which the tabs expect to load any content. Domain listings can include wildcards, for example `*.example.com`. If your tab configuration or content UI needs to navigate to any other domain besides the one use for tab configuration, that domain must be specified here.
     */
    validDomains?: string[];
    /**
     * Specify your AAD App ID and Graph information to help users seamlessly sign into your AAD app.
     */
    webApplicationInfo?: {
        /**
         * AAD application id of the app. This id must be a GUID.
         */
        id: string;
        /**
         * Resource url of app for acquiring auth token for SSO.
         */
        resource?: string;
    };
    /**
     * Specify the app's Graph connector configuration. If this is present then webApplicationInfo.id must also be specified.
     */
    graphConnector?: {
        /**
         * The url where Graph-connector notifications for the application should be sent.
         */
        notificationUrl: string;
    };
    /**
     * A value indicating whether or not show loading indicator when app/tab is loading
     */
    showLoadingIndicator?: boolean;
    /**
     * A value indicating whether a personal app is rendered without a tab header-bar
     */
    isFullScreen?: boolean;
    activities?: {
        /**
         * Specify the types of activites that your app can post to a users activity feed
         */
        activityTypes?: {
            type: string;
            description: string;
            templateText: string;
        }[];
    };
    /**
     * The set of supported channel type that an app belongs to
     */
    supportedChannelTypes?: ("sharedChannels" | "privateChannels")[];
    /**
     * A list of tenant configured properties for an app
     */
    configurableProperties?: ("name" | "shortDescription" | "longDescription" | "smallImageUrl" | "largeImageUrl" | "accentColor" | "developerUrl" | "privacyUrl" | "termsOfUseUrl")[];
    /**
     * A value indicating whether an app is blocked by default until admin allows it
     */
    defaultBlockUntilAdminAction?: boolean;
    /**
     * The url to the page that provides additional app information for the admins
     */
    publisherDocsUrl?: string;
    /**
     * The install scope defined for this app by default. This will be the option displayed on the button when a user tries to add the app
     */
    defaultInstallScope?: "personal" | "team" | "groupChat" | "meetings";
    /**
     * When a group install scope is selected, this will define the default capability when the user installs the app
     */
    defaultGroupCapability?: {
        /**
         * When the install scope selected is Team, this field specifies the default capability available
         */
        team?: "tab" | "bot" | "connector";
        /**
         * When the install scope selected is GroupChat, this field specifies the default capability available
         */
        groupchat?: "tab" | "bot" | "connector";
        /**
         * When the install scope selected is Meetings, this field specifies the default capability available
         */
        meetings?: "tab" | "bot" | "connector";
    };
    /**
     * Specify meeting extension definition.
     */
    meetingExtensionDefinition?: {
        /**
         * Meeting supported scenes.
         */
        scenes?: {
            /**
             * A unique identifier for this scene. This id must be a GUID.
             */
            id: string;
            /**
             * Scene name.
             */
            name: string;
            /**
             * A relative file path to a scene metadata json file.
             */
            file: string;
            /**
             * A relative file path to a scene PNG preview icon.
             */
            preview: string;
            /**
             * Maximum audiences supported in scene.
             */
            maxAudience: number;
            /**
             * Number of seats reserved for organizers or presenters.
             */
            seatsReservedForOrganizersOrPresenters: number;
        }[];
        /**
         * Meeting supported video filters.
         */
        videoFilters?: {
            /**
             * A unique identifier for this video filter. This id must be a GUID.
             */
            id: string;
            /**
             * Video filter's name.
             */
            name: string;
            /**
             * A relative file path to a video filter's thumbnail.
             */
            thumbnail: string;
        }[];
        /**
         * A URL for configuring the video filters.
         */
        videoFiltersConfigurationUrl?: string;
        /**
         * A boolean value indicating whether this app can stream the meeting's audio video content to an RTMP endpoint.
         */
        supportsStreaming?: boolean;
        /**
         * A boolean value indicating whether this app supports access by anonymous guest users.
         */
        supportsAnonymousGuestUsers?: boolean;
    };
    /**
     * Specify and consolidates authorization related information for the App.
     */
    authorization?: {
        /**
         * List of permissions that the app needs to function.
         */
        permissions?: {
            /**
             * Permissions that guard data access on a resource instance level.
             */
            resourceSpecific?: {
                /**
                 * The name of the resource-specific permission.
                 */
                name: string;
                /**
                 * The type of the resource-specific permission.
                 */
                type: "Application" | "Delegated";
            }[];
        };
    };
    extensions?: ElementExtensions;
}
export interface RequirementsExtensionElement {
    capabilities?: {
        name: string;
        minVersion?: string;
        maxVersion?: string;
    }[];
    scopes?: "mail"[];
    formFactors?: ("desktop" | "mobile")[];
}
export interface ExtensionRuntimesActionsItem {
    /**
     * Identifier for this action. Maximum length is 64 characters. This value is passed to the code file.
     */
    id: string;
    /**
     * executeFunction: Run a script function without waiting for it to finish. openPate: Open a page in a view.
     */
    type: "executeFunction" | "openPage";
    /**
     * Display name of the action. Maximum length is 64 characters.
     */
    displayName?: string;
    /**
     * Specifies that a task pane supports pinning, which keeps the task pane open when the user changes the selection.
     */
    pinnable?: boolean;
    /**
     * View where the page should be opened. Maximum length is 64 characters.
     */
    view?: string;
}
export interface ExtensionRibbonsArrayTabsItem {
    /**
     * A unique identifier for this tab within the app. Maximum length is 64 characters.
     */
    id?: string;
    /**
     * Displayed text for the tab. Maximum length is 64 characters.
     */
    label?: string;
    position?: {
        /**
         * The id of the built-in tab. Maximum length is 64 characters.
         */
        builtInTabId: string;
        /**
         * Define alignment of this custom tab relative to the specified built-in tab.
         */
        align: "after" | "before";
    };
    /**
     * Id of the existing office Tab. Maximum length is 64 characters.
     */
    builtInTabId?: string;
    groups?: ExtensionRibbonsCustomTabGroupsItem[];
}
export interface ExtensionRibbonsCustomTabGroupsItem {
    /**
     * A unique identifier for this group within the app. Maximum length is 64 characters.
     */
    id?: string;
    /**
     * Displayed text for the group. Maximum length is 64 characters.
     */
    label?: string;
    icons?: ExtensionCommonIcon[];
    controls?: ExtensionCommonCustomGroupControlsItem[];
    /**
     * Id of a built-in Group. Maximum length is 64 characters.
     */
    builtInGroupId?: string;
}
export interface ExtensionCommonIcon {
    /**
     * Size in pixels of the icon. Three image sizes are required (16, 32, and 80 pixels)
     */
    size: 16 | 20 | 24 | 32 | 40 | 48 | 64 | 80;
    /**
     * Url to the icon.
     */
    url?: string;
    /**
     * Relative path to the file that contains the icon. This property is currently not supported as a relative path.
     */
    file?: string;
}
export interface ExtensionCommonCustomGroupControlsItem {
    /**
     * A unique identifier for this control within the app. Maximum length is 64 characters.
     */
    id: string;
    type: "button" | "menu";
    /**
     * Id of the existing office control. Maximum length is 64 characters.
     */
    builtInControlId?: string;
    /**
     * Displayed text for the control. Maximum length is 64 characters.
     */
    label: string;
    icons: ExtensionCommonIcon[];
    supertip: ExtensionCommonSuperToolTip;
    /**
     * The ID of an execution-type action that handles this key combination. Maximum length is 64 characters.
     */
    actionId?: string;
    overriddenByRibbonApi?: boolean & string;
    /**
     * Whether the control is initially enabled.
     */
    enabled?: boolean;
    items?: ExtensionCommonCustomControlMenuItem[];
}
export interface ExtensionCommonSuperToolTip {
    /**
     * Title text of the super tip. Maximum length is 64 characters.
     */
    title: string;
    /**
     * Description of the super tip. Maximum length is 128 characters.
     */
    description: string;
}
export interface ExtensionCommonCustomControlMenuItem {
    /**
     * A unique identifier for this control within the app. Maximum length is 64 characters.
     */
    id: string;
    type: "menuItem";
    /**
     * Displayed text for the control. Maximum length is 64 characters.
     */
    label: string;
    icons?: ExtensionCommonIcon[];
    supertip: ExtensionCommonSuperToolTip;
    /**
     * The ID of an action defined in runtimes. Maximum length is 64 characters.
     */
    actionId: string;
    /**
     * Whether the control is initially enabled.
     */
    enabled?: boolean;
    overriddenByRibbonApi?: boolean & string;
}
//# sourceMappingURL=devPreviewManifest.d.ts.map