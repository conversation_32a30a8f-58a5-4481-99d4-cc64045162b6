import { TeamsAppManifest } from "./manifest";
import { JSONSchemaType } from "ajv";
import { DevPreviewSchema } from "./devPreviewManifest";
import { ManifestCommonProperties } from "./ManifestCommonProperties";
export * from "./manifest";
export * as devPreview from "./devPreviewManifest";
export type TeamsAppManifestJSONSchema = JSONSchemaType<TeamsAppManifest>;
export type DevPreviewManifestJSONSchema = JSONSchemaType<DevPreviewSchema>;
export type Manifest = TeamsAppManifest | DevPreviewSchema;
export declare class ManifestUtil {
    /**
     * Loads the manifest from the given path without validating its schema.
     *
     * @param path - The path to the manifest file.
     * @throws Will propagate any error thrown by the fs-extra#readJson.
     *
     * @returns The Manifest Object.
     */
    static loadFromPath<T extends Manifest = TeamsAppManifest>(path: string): Promise<T>;
    /**
     * Writes the manifest object to the given path.
     *
     * @param path - Where to write
     * @param manifest - Manifest object to be saved
     * @throws Will propagate any error thrown by the fs-extra#writeJson.
     *
     */
    static writeToPath<T extends Manifest = TeamsAppManifest>(path: string, manifest: T): Promise<void>;
    /**
     * Validate manifest against json schema.
     *
     * @param manifest - Manifest object to be validated
     * @param schema - teams-app-manifest schema
     * @returns An empty array if it passes validation, or an array of error string otherwise.
     */
    static validateManifestAgainstSchema<T extends Manifest = TeamsAppManifest>(manifest: T, schema: JSONSchemaType<T>): Promise<string[]>;
    /**
     * Validate manifest against {@link TeamsAppManifest#$schema}.
     *
     * @param manifest - Manifest object to be validated
     * @throws Will throw if {@link TeamsAppManifest#$schema} is undefined, not valid
     *         or there is any network failure when getting the schema.
     *
     * @returns An empty array if schema validation passes, or an array of error string otherwise.
     */
    static validateManifest<T extends Manifest = TeamsAppManifest>(manifest: T): Promise<string[]>;
    /**
     * Parse the manifest and get properties
     * @param manifest
     */
    static parseCommonProperties<T extends Manifest = TeamsAppManifest>(manifest: T): ManifestCommonProperties;
    /**
     * Parse the manifest and get telemetry propreties e.g. appId, capabilities etc.
     * @param manifest
     * @returns Telemetry properties
     */
    static parseCommonTelemetryProperties(manifest: TeamsAppManifest): {
        [p: string]: string;
    };
}
//# sourceMappingURL=index.d.ts.map