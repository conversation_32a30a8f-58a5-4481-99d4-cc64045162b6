{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGlC,wDAA0B;AAC1B,gEAA+B;AAE/B,kDAA6C;AAG7C,2CAA8C;AAE9C,6CAA2B;AAC3B,mEAAmD;AAMnD,MAAa,YAAY;IACvB;;;;;;;OAOG;IACH,MAAM,CAAC,KAAK,CAAC,YAAY,CAAwC,IAAY;QAC3E,OAAO,kBAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,KAAK,CAAC,WAAW,CACtB,IAAY,EACZ,QAAW;QAEX,OAAO,kBAAE,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,6BAA6B,CAClC,QAAW,EACX,MAAyB;;QAEzB,MAAM,GAAG,GAAG,IAAI,sBAAG,CAAC,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACjE,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE;YAC7B,OAAO,OAAO,CAAC,OAAO,CACpB,MAAA,QAAQ,CAAC,MAAM,0CAAE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,YAAY,IAAI,KAAK,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC,CAChF,CAAC;SACH;aAAM;YACL,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SAC5B;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAC3B,QAAW;QAEX,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;SAC9D;QACD,IAAI,MAA0B,CAAC;QAC/B,IAAI;YACF,MAAM,aAAa,GAAG,eAAK,CAAC,MAAM,EAAE,CAAC;YACrC,MAAM,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;SACpD;QAAC,OAAO,CAAU,EAAE;YACnB,IAAI,CAAC,YAAY,KAAK,EAAE;gBACtB,MAAM,IAAI,KAAK,CAAC,iCAAiC,QAAQ,CAAC,OAAO,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;aAC3F;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,iCAAiC,QAAQ,CAAC,OAAO,wBAAwB,CAAC,CAAC;aAC5F;SACF;QAED,OAAO,YAAY,CAAC,6BAA6B,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;IAC3E,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,qBAAqB,CAC1B,QAAW;QAEX,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAChC;QACD,IAAI,QAAQ,CAAC,gBAAgB,IAAI,QAAQ,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;YACrE,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACtC;QACD,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7C,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;QACD,IAAI,QAAQ,CAAC,iBAAiB,EAAE;YAC9B,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACvC;QAED,MAAM,UAAU,GAA6B;YAC3C,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,YAAY,EAAE,YAAY;YAC1B,eAAe,EAAE,QAAQ,CAAC,eAAe;YACzC,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,KAAK;SACd,CAAC;QAEF,6BAA6B;QAC7B,IACE,QAAQ,CAAC,iBAAiB;YAC1B,QAAQ,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC;YACpC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAuB,CAAC,oBAAoB,IAAI,UAAU,EACvF;YACA,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;SAC3B;QAED,mBAAmB;QACnB,IACE,QAAQ,CAAC,kBAAkB;YAC3B,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC9B,QAAQ,CAAC,kBAAkB,CAAC,EAAE,IAAI,2BAAe,EACjD;YACA,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;SAC1B;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,8BAA8B,CAAC,QAA0B;QAC9D,MAAM,UAAU,GAAG,YAAY,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAEhE,MAAM,mBAAmB,GAA4B,EAAE,CAAC;QACxD,MAAM,aAAa,GAAG,IAAI,GAAG,CAAc,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACvE,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACnC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACxB,mBAAmB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAC5C;iBAAM;gBACL,mBAAmB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;aAClC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,mBAAmB,CAAC;IAC7B,CAAC;CACF;AAxJD,oCAwJC"}