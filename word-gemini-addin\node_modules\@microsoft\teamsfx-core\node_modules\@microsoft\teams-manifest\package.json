{"name": "@microsoft/teams-manifest", "version": "0.1.3", "main": "build/index.js", "types": "build/index.d.ts", "license": "MIT", "dependencies": {"@types/fs-extra": "^11.0.1", "ajv": "^8.5.0", "ajv-draft-04": "^1.0.0", "axios": "^1.6.2", "fs-extra": "^9.1.0"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.1", "@types/chai": "^4.2.21", "@types/chai-as-promised": "^7.1.3", "@types/mocha": "^8.2.3", "@types/node": "^16.0.0", "@types/sinon": "^9.0.10", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "chai": "^4.3.4", "chai-as-promised": "^7.1.1", "eslint": "^7.22.0", "eslint-plugin-header": "^3.1.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-no-secrets": "^0.8.9", "eslint-plugin-prettier": "^4.0.0", "json-schema-to-typescript": "^10.1.5", "lint-staged": "^10.5.4", "mocha": "^9.2.2", "nyc": "^15.1.0", "prettier": "^2.4.1", "rimraf": "^3.0.0", "sinon": "^9.2.2", "ts-node": "^10.1.0", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^5.0.4"}, "scripts": {"prebuild": "npm run generate-manifest", "build": "rimraf build && npx tsc -p ./", "test": "npm run test:unit", "test:unit": "npx nyc --reporter=lcov mocha --no-timeouts --require ts-node/register test/**/*.test.ts ", "lint": "eslint \"**/*.ts\"", "precommit": "lint-staged", "lint:staged": "lint-staged", "generate-manifest": "npx json2ts -i devPreviewSchema.json -o src/devPreviewManifest.ts --ignoreMinAndMaxItems"}, "files": ["build/**/*"], "publishConfig": {"access": "public"}, "lint-staged": {"*.{js,jsx,css,ts,tsx}": ["npx eslint --cache --fix --quiet"]}, "gitHead": "ec70ec3dd8743aed886978c953ddc33ba62ad4c5"}