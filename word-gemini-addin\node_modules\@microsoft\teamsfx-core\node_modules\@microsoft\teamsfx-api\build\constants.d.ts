export declare const ConfigFolderName = "fx";
export declare const AppPackageFolderName = "appPackage";
export declare const BuildFolderName = "build";
export declare const ResponseTemplatesFolderName = "responseTemplates";
export declare const TemplateFolderName = "templates";
export declare const ProductName = "teamsfx";
export declare const AutoGeneratedReadme = "README-auto-generated.md";
export declare const DefaultReadme = "README.md";
export declare const SettingsFolderName = "teamsfx";
export declare const ManifestTemplateFileName = "manifest.json";
/**
 * questions for VS and CLI_HELP platforms are static question which don't depend on project context
 * questions for VSCode and CLI platforms are dynamic question which depend on project context
 */
export declare enum Platform {
    VSCode = "vsc",
    CLI = "cli",
    VS = "vs",
    CLI_HELP = "cli_help"
}
export declare const StaticPlatforms: Platform[];
export declare const DynamicPlatforms: Platform[];
export declare const CLIPlatforms: Platform[];
export declare enum VsCodeEnv {
    local = "local",
    codespaceBrowser = "codespaceBrowser",
    codespaceVsCode = "codespaceVsCode",
    remote = "remote"
}
export declare enum Stage {
    create = "create",
    build = "build",
    debug = "debug",
    provision = "provision",
    deploy = "deploy",
    package = "package",
    publish = "publish",
    createEnv = "createEnv",
    listEnv = "listEnv",
    removeEnv = "removeEnv",
    switchEnv = "switchEnv",
    userTask = "userTask",
    update = "update",
    grantPermission = "grantPermission",
    checkPermission = "checkPermission",
    listCollaborator = "listCollaborator",
    getQuestions = "getQuestions",
    getProjectConfig = "getProjectConfig",
    addFeature = "addFeature",
    addWebpart = "addWebpart",
    addResource = "addResource",
    addCapability = "addCapability",
    addCiCdFlow = "addCiCdFlow",
    deployAad = "deployAad",
    buildAad = "buildAad",
    deployTeams = "deployTeams",
    initDebug = "initDebug",
    initInfra = "initInfra",
    publishInDeveloperPortal = "publishInDeveloperPortal",
    validateApplication = "validateApplication",
    createAppPackage = "createAppPackage",
    previewWithManifest = "previewWithManifest",
    copilotPluginAddAPI = "copilotPluginAddAPI"
}
export declare enum TelemetryEvent {
    askQuestion = "askQuestion"
}
export declare enum TelemetryProperty {
    answerType = "answerType",
    question = "question",
    answer = "answer",
    platform = "platform",
    stage = "stage"
}
/**
 * You can register your callback function when you want to be notified
 * at some predefined events.
 */
export declare enum CoreCallbackEvent {
    lock = "lock",
    unlock = "unlock"
}
//# sourceMappingURL=constants.d.ts.map