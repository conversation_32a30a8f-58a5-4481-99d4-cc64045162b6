// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoreCallbackEvent = exports.TelemetryProperty = exports.TelemetryEvent = exports.Stage = exports.VsCodeEnv = exports.CLIPlatforms = exports.DynamicPlatforms = exports.StaticPlatforms = exports.Platform = exports.ManifestTemplateFileName = exports.SettingsFolderName = exports.DefaultReadme = exports.AutoGeneratedReadme = exports.ProductName = exports.TemplateFolderName = exports.ResponseTemplatesFolderName = exports.BuildFolderName = exports.AppPackageFolderName = exports.ConfigFolderName = void 0;
exports.ConfigFolderName = "fx";
exports.AppPackageFolderName = "appPackage";
exports.BuildFolderName = "build";
exports.ResponseTemplatesFolderName = "responseTemplates";
exports.TemplateFolderName = "templates";
exports.ProductName = "teamsfx";
exports.AutoGeneratedReadme = "README-auto-generated.md";
exports.DefaultReadme = "README.md";
exports.SettingsFolderName = "teamsfx";
exports.ManifestTemplateFileName = "manifest.json";
/**
 * questions for VS and CLI_HELP platforms are static question which don't depend on project context
 * questions for VSCode and CLI platforms are dynamic question which depend on project context
 */
var Platform;
(function (Platform) {
    Platform["VSCode"] = "vsc";
    Platform["CLI"] = "cli";
    Platform["VS"] = "vs";
    Platform["CLI_HELP"] = "cli_help";
})(Platform = exports.Platform || (exports.Platform = {}));
exports.StaticPlatforms = [Platform.CLI_HELP];
exports.DynamicPlatforms = [Platform.VSCode, Platform.CLI, Platform.VS];
exports.CLIPlatforms = [Platform.CLI, Platform.CLI_HELP];
var VsCodeEnv;
(function (VsCodeEnv) {
    VsCodeEnv["local"] = "local";
    VsCodeEnv["codespaceBrowser"] = "codespaceBrowser";
    VsCodeEnv["codespaceVsCode"] = "codespaceVsCode";
    VsCodeEnv["remote"] = "remote";
})(VsCodeEnv = exports.VsCodeEnv || (exports.VsCodeEnv = {}));
var Stage;
(function (Stage) {
    Stage["create"] = "create";
    Stage["build"] = "build";
    Stage["debug"] = "debug";
    Stage["provision"] = "provision";
    Stage["deploy"] = "deploy";
    Stage["package"] = "package";
    Stage["publish"] = "publish";
    Stage["createEnv"] = "createEnv";
    Stage["listEnv"] = "listEnv";
    Stage["removeEnv"] = "removeEnv";
    Stage["switchEnv"] = "switchEnv";
    Stage["userTask"] = "userTask";
    Stage["update"] = "update";
    Stage["grantPermission"] = "grantPermission";
    Stage["checkPermission"] = "checkPermission";
    Stage["listCollaborator"] = "listCollaborator";
    Stage["getQuestions"] = "getQuestions";
    Stage["getProjectConfig"] = "getProjectConfig";
    Stage["addFeature"] = "addFeature";
    Stage["addWebpart"] = "addWebpart";
    Stage["addResource"] = "addResource";
    Stage["addCapability"] = "addCapability";
    Stage["addCiCdFlow"] = "addCiCdFlow";
    Stage["deployAad"] = "deployAad";
    Stage["buildAad"] = "buildAad";
    Stage["deployTeams"] = "deployTeams";
    Stage["initDebug"] = "initDebug";
    Stage["initInfra"] = "initInfra";
    Stage["publishInDeveloperPortal"] = "publishInDeveloperPortal";
    Stage["validateApplication"] = "validateApplication";
    Stage["createAppPackage"] = "createAppPackage";
    Stage["previewWithManifest"] = "previewWithManifest";
    Stage["copilotPluginAddAPI"] = "copilotPluginAddAPI";
})(Stage = exports.Stage || (exports.Stage = {}));
var TelemetryEvent;
(function (TelemetryEvent) {
    TelemetryEvent["askQuestion"] = "askQuestion";
})(TelemetryEvent = exports.TelemetryEvent || (exports.TelemetryEvent = {}));
var TelemetryProperty;
(function (TelemetryProperty) {
    TelemetryProperty["answerType"] = "answerType";
    TelemetryProperty["question"] = "question";
    TelemetryProperty["answer"] = "answer";
    TelemetryProperty["platform"] = "platform";
    TelemetryProperty["stage"] = "stage";
})(TelemetryProperty = exports.TelemetryProperty || (exports.TelemetryProperty = {}));
/**
 * You can register your callback function when you want to be notified
 * at some predefined events.
 */
var CoreCallbackEvent;
(function (CoreCallbackEvent) {
    CoreCallbackEvent["lock"] = "lock";
    CoreCallbackEvent["unlock"] = "unlock";
})(CoreCallbackEvent = exports.CoreCallbackEvent || (exports.CoreCallbackEvent = {}));
//# sourceMappingURL=constants.js.map