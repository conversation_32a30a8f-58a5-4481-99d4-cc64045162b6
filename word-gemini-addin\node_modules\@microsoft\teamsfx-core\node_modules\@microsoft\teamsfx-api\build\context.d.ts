import { UserInteraction } from "./qm";
import { Log<PERSON><PERSON><PERSON>, Telem<PERSON>ry<PERSON><PERSON><PERSON><PERSON>, TokenProvider } from "./utils";
import { ExpServiceProvider } from "./utils/exp";
export interface Context {
    userInteraction: UserInteraction;
    logProvider: LogProvider;
    telemetryReporter: TelemetryReporter;
    expServiceProvider?: ExpServiceProvider;
    tokenProvider?: TokenProvider;
    projectPath?: string;
    templateVariables?: {
        [key: string]: string;
    };
}
//# sourceMappingURL=context.d.ts.map