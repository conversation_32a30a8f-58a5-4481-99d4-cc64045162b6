export interface FxError extends Error {
    /**
     * Custom error details.
     */
    innerError?: any;
    /**
     * Source name of error. (plugin name, eg: tab-scaffhold-plugin)
     */
    source: string;
    /**
     * Time of error.
     */
    timestamp: Date;
    userData?: any;
    categories?: string[];
    /**
     * recommended operation for user to fix the error
     * e.g. "debug-in-test-tool"
     */
    recommendedOperation?: string;
}
export interface ErrorOptionBase {
    source?: string;
    name?: string;
    message?: string;
    error?: Error;
    userData?: any;
    displayMessage?: string;
    categories?: string[];
}
export interface UserErrorOptions extends ErrorOptionBase {
    helpLink?: string;
}
export interface SystemErrorOptions extends ErrorOptionBase {
    issueLink?: string;
}
/**
 * Users can recover by themselves, e.g., users input invalid app names.
 */
export declare class UserError extends Error implements FxError {
    /**
     * Custom error details .
     */
    innerError?: any;
    /**
     * Source name of error. (plugin name, eg: tab-scaffold-plugin)
     */
    source: string;
    /**
     * Time of error.
     */
    timestamp: Date;
    /**
     * A wiki website that shows mapping relationship between error names, descriptions, and fix solutions.
     */
    helpLink?: string;
    /**
     * data that only be reported to github issue  manually by user and will not be reported as telemetry data
     */
    userData?: string;
    /**
     * message show in the UI
     */
    displayMessage?: string;
    categories?: string[];
    /**
     * recommended operation for user to fix the error
     * e.g. "debug-in-test-tool"
     */
    recommendedOperation?: string;
    constructor(opt: UserErrorOptions);
    constructor(source: string, name: string, message: string, displayMessage?: string);
}
/**
 * Users cannot handle it by themselves.
 */
export declare class SystemError extends Error implements FxError {
    /**
     * Custom error details.
     */
    innerError?: any;
    /**
     * Source name of error. (plugin name, eg: tab-scaffold-plugin)
     */
    source: string;
    /**
     * Time of error.
     */
    timestamp: Date;
    /**
     * A github issue page where users can submit a new issue.
     */
    issueLink?: string;
    /**
     * data that only be reported to github issue  manually by user and will not be reported as telemetry data
     */
    userData?: string;
    /**
     * message show in the UI
     */
    displayMessage?: string;
    categories?: string[];
    /**
     * recommended operation for user to fix the error
     * e.g. "debug-in-test-tool"
     */
    recommendedOperation?: string;
    constructor(opt: SystemErrorOptions);
    constructor(source: string, name: string, message: string, displayMessage?: string);
}
//# sourceMappingURL=error.d.ts.map