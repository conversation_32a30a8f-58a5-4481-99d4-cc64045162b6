"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemError = exports.UserError = void 0;
/**
 * Users can recover by themselves, e.g., users input invalid app names.
 */
class UserError extends Error {
    constructor(param1, param2, param3, param4) {
        var _a;
        let option;
        if (typeof param1 === "string") {
            option = {
                source: param1,
                name: param2,
                message: param3,
                displayMessage: param4,
            };
        }
        else {
            option = param1;
        }
        // message
        const message = option.message || ((_a = option.error) === null || _a === void 0 ? void 0 : _a.message);
        super(message);
        //name
        this.name = option.name || new.target.name;
        //source
        this.source = option.source || "unknown";
        //stack
        Error.captureStackTrace(this, new.target);
        //prototype
        Object.setPrototypeOf(this, new.target.prototype);
        //innerError
        this.innerError = option.error;
        //other fields
        this.helpLink = option.helpLink;
        this.userData = option.userData;
        this.displayMessage = option.displayMessage;
        this.timestamp = new Date();
        this.categories = option.categories;
    }
}
exports.UserError = UserError;
/**
 * Users cannot handle it by themselves.
 */
class SystemError extends Error {
    constructor(param1, param2, param3, param4) {
        var _a;
        let option;
        if (typeof param1 === "string") {
            option = {
                source: param1,
                name: param2,
                message: param3,
                displayMessage: param4,
            };
        }
        else {
            option = param1;
        }
        // message
        const message = option.message || ((_a = option.error) === null || _a === void 0 ? void 0 : _a.message);
        super(message);
        //name
        this.name = option.name || new.target.name;
        //source
        this.source = option.source || "unknown";
        //stack
        Error.captureStackTrace(this, new.target);
        //prototype
        Object.setPrototypeOf(this, new.target.prototype);
        //innerError
        this.innerError = option.error;
        //other fields
        this.issueLink = option.issueLink;
        this.userData = option.userData;
        this.displayMessage = option.displayMessage;
        this.timestamp = new Date();
        this.categories = option.categories;
    }
}
exports.SystemError = SystemError;
//# sourceMappingURL=error.js.map