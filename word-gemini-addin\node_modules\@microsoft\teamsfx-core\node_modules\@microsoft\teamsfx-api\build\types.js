// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIManifestAuthType = exports.Void = void 0;
exports.Void = {};
var OpenAIManifestAuthType;
(function (OpenAIManifestAuthType) {
    OpenAIManifestAuthType["None"] = "none";
    OpenAIManifestAuthType["UserHttp"] = "user_http";
    OpenAIManifestAuthType["ServiceHttp"] = "service_http";
    OpenAIManifestAuthType["OAuth"] = "oauth";
})(OpenAIManifestAuthType = exports.OpenAIManifestAuthType || (exports.OpenAIManifestAuthType = {}));
//# sourceMappingURL=types.js.map