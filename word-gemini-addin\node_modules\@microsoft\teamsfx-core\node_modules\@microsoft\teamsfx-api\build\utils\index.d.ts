import { UserInteraction } from "../qm/ui";
import { CryptoProvider } from "./crypto";
import { ExpServiceProvider } from "./exp";
import { LogProvider } from "./log";
import { TokenProvider } from "./login";
import { PermissionRequestProvider } from "./permissionRequest";
import { TelemetryReporter } from "./telemetry";
import { TreeProvider } from "./tree";
export * from "./login";
export * from "./log";
export * from "./telemetry";
export * from "./tree";
export * from "./crypto";
export * from "./permissionRequest";
export * from "./exp";
export interface Tools {
    logProvider: LogProvider;
    tokenProvider: TokenProvider;
    telemetryReporter?: TelemetryReporter;
    treeProvider?: TreeProvider;
    ui: UserInteraction;
    cryptoProvider?: CryptoProvider;
    permissionRequest?: PermissionRequestProvider;
    expServiceProvider?: ExpServiceProvider;
}
//# sourceMappingURL=index.d.ts.map