// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Colors = exports.LogLevel = void 0;
var LogLevel;
(function (LogLevel) {
    /**
     * For debugging and development.
     */
    LogLevel[LogLevel["Debug"] = 1] = "Debug";
    /**
     * Contain the most detailed messages.
     */
    LogLevel[LogLevel["Verbose"] = 2] = "Verbose";
    /**
     * Tracks the general flow of the app. May have long-term value.
     */
    LogLevel[LogLevel["Info"] = 3] = "Info";
    /**
     * For abnormal or unexpected events. Typically includes errors or conditions that don't cause the app to fail.
     */
    LogLevel[LogLevel["Warning"] = 4] = "Warning";
    /**
     * For errors and exceptions that cannot be handled. These messages indicate a failure in the current operation or request, not an app-wide failure.
     */
    LogLevel[LogLevel["Error"] = 5] = "Error";
})(LogLevel = exports.LogLevel || (exports.LogLevel = {}));
/**
 * Colors for CLI output message
 */
var Colors;
(function (Colors) {
    /**
     * Primary text color
     */
    Colors[Colors["BRIGHT_WHITE"] = 0] = "BRIGHT_WHITE";
    /**
     * Secondary text color
     */
    Colors[Colors["WHITE"] = 1] = "WHITE";
    /**
     * Important text color
     */
    Colors[Colors["BRIGHT_MAGENTA"] = 2] = "BRIGHT_MAGENTA";
    /**
     * Success message indicator
     */
    Colors[Colors["BRIGHT_GREEN"] = 3] = "BRIGHT_GREEN";
    /**
     * Warning message indicator
     */
    Colors[Colors["BRIGHT_YELLOW"] = 4] = "BRIGHT_YELLOW";
    /**
     * Error message indicator
     */
    Colors[Colors["BRIGHT_RED"] = 5] = "BRIGHT_RED";
    /**
     * Hyperlink
     */
    Colors[Colors["BRIGHT_CYAN"] = 6] = "BRIGHT_CYAN";
})(Colors = exports.Colors || (exports.Colors = {}));
//# sourceMappingURL=log.js.map