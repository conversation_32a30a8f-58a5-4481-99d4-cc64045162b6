// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BasicLogin = void 0;
const neverthrow_1 = require("neverthrow");
/**
 * Provide basic login framework
 */
class BasicLogin {
    constructor() {
        this.statusChangeMap = new Map();
    }
    async setStatusChangeMap(name, tokenRequest, statusChange, immediateCall = true) {
        this.statusChangeMap.set(name, statusChange);
        if (immediateCall) {
            const loginStatusRes = await this.getStatus(tokenRequest);
            if (loginStatusRes.isOk()) {
                await statusChange(loginStatusRes.value.status, loginStatusRes.value.token, loginStatusRes.value.accountInfo);
            }
        }
        return (0, neverthrow_1.ok)(true);
    }
    removeStatusChangeMap(name) {
        this.statusChangeMap.delete(name);
        return Promise.resolve((0, neverthrow_1.ok)(true));
    }
    async notifyStatus(tokenRequest) {
        const loginStatusRes = await this.getStatus(tokenRequest);
        if (loginStatusRes.isOk()) {
            for (const entry of this.statusChangeMap.entries()) {
                entry[1](loginStatusRes.value.status, loginStatusRes.value.token, loginStatusRes.value.accountInfo);
            }
        }
    }
}
exports.BasicLogin = BasicLogin;
//# sourceMappingURL=login.js.map