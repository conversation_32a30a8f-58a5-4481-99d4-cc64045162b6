{"name": "@microsoft/teamsfx-api", "version": "0.22.7", "description": "teamsfx framework api", "main": "build/index.js", "types": "build/index.d.ts", "license": "MIT", "keywords": ["teamsfx", "api"], "repository": "https://github.com/", "files": ["build/**/*"], "scripts": {"lint:staged": "lint-staged", "lint": "eslint \"src/**/*.ts\" \"tests/**/*.ts\"", "prebuild": "npx json2ts --input src/schemas/envConfig.json --output src/schemas/envConfig.ts", "build": "tsc -p ./ && npx api-extractor run --local", "build:api-markdown": "npm run build && rimraf ../../docs/api && npx api-documenter markdown -i temp -o ../../docs/api", "postbuild": "npx copyfiles src/schemas/*.json build/schemas/", "lint:fix": "eslint \"src/**/*.ts\" \"tests/**/*.ts\" --fix", "doc": "typedoc", "test:unit": "nyc --no-clean mocha --no-timeouts --require ts-node/register \"tests/**/*.ts\" ", "prepublishOnly": "npm run test:unit && npm run build", "check-sensitive": "npx eslint --plugin 'no-secrets' --cache --ignore-pattern 'package.json' --ignore-pattern 'package-lock.json'", "precommit": "npm run check-sensitive && lint-staged"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.1", "@microsoft/api-documenter": "^7.15.3", "@microsoft/api-extractor": "^7.18.4", "@types/chai": "^4.2.14", "@types/chai-as-promised": "^7.1.3", "@types/chai-spies": "^1.0.3", "@types/fs-extra": "^9.0.10", "@types/mocha": "^8.2.2", "@types/node": "^14.14.10", "@types/sinon": "^9.0.10", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "chai": "4.3.4", "chai-as-promised": "^7.1.1", "chai-spies": "^1.0.0", "copyfiles": "^2.4.1", "cpy-cli": "^4.0.0", "eslint": "^7.22.0", "eslint-plugin-header": "^3.1.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-no-secrets": "^0.8.9", "eslint-plugin-prettier": "^4.0.0", "json-schema-to-typescript": "^10.1.4", "lint-staged": "^10.5.4", "mocha": "^9.2.0", "nyc": "^15.1.0", "prettier": "^2.4.1", "sinon": "^9.2.2", "source-map-support": "^0.5.19", "ts-node": "^9.1.1", "tslint-config-prettier": "^1.18.0", "typescript": "^5.0.4"}, "dependencies": {"@azure/core-auth": "^1.4.0", "@microsoft/teams-manifest": "0.1.3", "axios": "^1.6.7", "jsonschema": "^1.4.0", "neverthrow": "^3.2.0", "tslib": "^2.3.1"}, "gitHead": "b0e94768545a70d86f128aa04be72151747f23be", "publishConfig": {"access": "public"}, "lint-staged": {"*.{js,jsx,css,ts,tsx}": ["npx eslint --cache --fix --quiet"]}}