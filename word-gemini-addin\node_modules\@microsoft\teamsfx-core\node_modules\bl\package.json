{"name": "bl", "version": "5.1.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "license": "MIT", "main": "bl.js", "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --allowJs --noEmit test/test.js", "build": "true"}, "repository": {"type": "git", "url": "https://github.com/rvagg/bl.git"}, "homepage": "https://github.com/rvagg/bl", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}, "devDependencies": {"@types/readable-stream": "^2.3.13", "faucet": "~0.0.1", "standard": "^17.0.0", "tape": "^5.2.2", "typescript": "~4.7.3"}, "release": {"branches": ["master"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"breaking": true, "release": "major"}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"]}}