{"version": 3, "file": "Connection.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/Connection.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAC,eAAe,EAAC,MAAM,6CAA6C,CAAC;AAG5E,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAC,mBAAmB,EAAC,MAAM,0BAA0B,CAAC;AAE7D,OAAO,EAAmB,aAAa,EAAC,MAAM,aAAa,CAAC;AAC5D,OAAO,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAC;AAM/C;;GAEG;AACH,OAAO,EAAC,mBAAmB,EAAE,eAAe,EAAC,CAAC;AAE9C;;;;GAIG;AACH,eAAO,MAAM,uBAAuB;;CAE1B,CAAC;AAiBX;;GAEG;AACH,qBAAa,QAAQ;;gBAOP,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM;IAevD,OAAO,CAAC,KAAK,EAAE,OAAO,GAAG,IAAI;IAK7B,MAAM,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAK1B,IAAI,EAAE,IAAI,MAAM,CAEf;IAED,IAAI,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,CAE/B;IAED,IAAI,KAAK,IAAI,aAAa,CAEzB;IAED,IAAI,KAAK,IAAI,MAAM,CAElB;CACF;AAED;;;;GAIG;AACH,qBAAa,gBAAgB;;IAI3B,MAAM,CACJ,KAAK,EAAE,MAAM,EACb,OAAO,EAAE,MAAM,GAAG,SAAS,EAC3B,OAAO,EAAE,CAAC,EAAE,EAAE,MAAM,KAAK,IAAI,GAC5B,OAAO,CAAC,OAAO,CAAC;IAuBnB,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,CAAC,EAAE,MAAM,GAAG,IAAI;IAQnE,OAAO,CACL,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,MAAM,GAAG,aAAa,EACpC,eAAe,CAAC,EAAE,MAAM,GACvB,IAAI;IAcP,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,GAAG,IAAI;IAQzC,KAAK,IAAI,IAAI;CAOd;AAED;;GAEG;AACH,qBAAa,UAAW,SAAQ,YAAY;;gBAWxC,GAAG,EAAE,MAAM,EACX,SAAS,EAAE,mBAAmB,EAC9B,KAAK,SAAI,EACT,OAAO,CAAC,EAAE,MAAM;IAYlB,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,UAAU,GAAG,UAAU,GAAG,SAAS;IAI/D,IAAI,OAAO,IAAI,MAAM,CAEpB;IAED;;OAEG;IACH,IAAI,OAAO,IAAI,OAAO,CAErB;IAED;;OAEG;IACH,IAAI,SAAS,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAEvC;IAED;;;OAGG;IACH,OAAO,CAAC,SAAS,EAAE,MAAM,GAAG,UAAU,GAAG,IAAI;IAI7C,GAAG,IAAI,MAAM;IAIb,IAAI,CAAC,CAAC,SAAS,MAAM,eAAe,CAAC,QAAQ,EAC3C,MAAM,EAAE,CAAC,EACT,GAAG,SAAS,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GACtD,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;IAWrD;;OAEG;IACH,QAAQ,CAAC,CAAC,SAAS,MAAM,eAAe,CAAC,QAAQ,EAC/C,SAAS,EAAE,gBAAgB,EAC3B,MAAM,EAAE,CAAC,EACT,MAAM,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EACpD,SAAS,CAAC,EAAE,MAAM,GACjB,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;IAarD;;OAEG;IACG,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC;IAInC;;OAEG;cACa,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAoEzD,OAAO,IAAI,IAAI;IAKf;;OAEG;IACH,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO;IAIzC;;OAEG;IACG,cAAc,CAClB,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,EACtC,oBAAoB,UAAO,GAC1B,OAAO,CAAC,UAAU,CAAC;IAgBtB;;;OAGG;IACG,aAAa,CACjB,UAAU,EAAE,QAAQ,CAAC,MAAM,CAAC,UAAU,GACrC,OAAO,CAAC,UAAU,CAAC;CAGvB;AAED;;GAEG;AACH,MAAM,WAAW,yBAAyB;IACxC,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAChC,KAAK,EAAE;QAAC,OAAO,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,GAAG,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAC,CAAC;IAClD,MAAM,CAAC,EAAE,GAAG,CAAC;CACd;AAED;;;;GAIG;AACH,eAAO,MAAM,uBAAuB;;CAE1B,CAAC;AAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,qBAAa,UAAW,SAAQ,YAAY;IAC1C;;OAEG;;IAKH,UAAU,IAAI,UAAU,GAAG,SAAS;IAIpC,IAAI,CAAC,CAAC,SAAS,MAAM,eAAe,CAAC,QAAQ,EAC3C,MAAM,EAAE,CAAC,EACT,GAAG,SAAS,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GACtD,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;IAOrD;;;OAGG;IACG,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAI7B;;OAEG;IACH,EAAE,IAAI,MAAM;CAGb;AAED;;GAEG;AACH,qBAAa,cAAe,SAAQ,UAAU;;IAM5C;;OAEG;gBACS,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;IAOhE,UAAU,IAAI,UAAU,GAAG,SAAS;IAIpC,IAAI,CAAC,CAAC,SAAS,MAAM,eAAe,CAAC,QAAQ,EACpD,MAAM,EAAE,CAAC,EACT,GAAG,SAAS,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GACtD,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;IAoBrD;;OAEG;IACH,UAAU,CAAC,MAAM,EAAE,yBAAyB,GAAG,IAAI;IAiBnD;;;OAGG;IACY,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAatC;;OAEG;IACH,SAAS,IAAI,IAAI;IAMjB;;OAEG;IACM,EAAE,IAAI,MAAM;CAGtB;AA4BD;;GAEG;AACH,wBAAgB,mBAAmB,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO,CAEzD"}