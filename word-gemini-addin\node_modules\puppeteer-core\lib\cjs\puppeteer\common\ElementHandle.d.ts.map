{"version": 3, "file": "ElementHandle.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/ElementHandle.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;AAEH,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EACL,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,YAAY,EACZ,aAAa,EACb,MAAM,EACN,KAAK,EACN,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAC,eAAe,EAAE,mBAAmB,EAAC,MAAM,iBAAiB,CAAC;AACrE,OAAO,EAAO,iBAAiB,EAAC,MAAM,gBAAgB,CAAC;AAGvD,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAC3C,OAAO,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AAEjC,OAAO,EAAC,sBAAsB,EAAC,MAAM,oBAAoB,CAAC;AAC1D,OAAO,EAAC,WAAW,EAAC,MAAM,eAAe,CAAC;AAE1C,OAAO,EAAC,OAAO,EAAC,MAAM,YAAY,CAAC;AACnC,OAAO,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAC;AAa/C;;;;;;GAMG;AACH,qBAAa,gBAAgB,CAC3B,WAAW,SAAS,IAAI,GAAG,OAAO,CAClC,SAAQ,aAAa,CAAC,WAAW,CAAC;;IAE1B,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC;gBAGvC,OAAO,EAAE,gBAAgB,EACzB,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,EAC3C,KAAK,EAAE,KAAK;IAMd;;OAEG;IACM,gBAAgB,IAAI,gBAAgB;IAI7C;;OAEG;IACH,IAAa,MAAM,IAAI,UAAU,CAEhC;IAEQ,YAAY,IAAI,QAAQ,CAAC,OAAO,CAAC,YAAY;IAYtD,IAAa,KAAK,IAAI,KAAK,CAE1B;IAEc,CAAC,CAAC,QAAQ,SAAS,MAAM,EACtC,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IAMvC,EAAE,CAAC,QAAQ,SAAS,MAAM,EACvC,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAMvC,eAAe,CAAC,QAAQ,SAAS,MAAM,EACpD,QAAQ,EAAE,QAAQ,EAClB,OAAO,CAAC,EAAE,sBAAsB,GAC/B,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IAMvC,YAAY,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IAUrC,cAAc,CAC3B,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAC9B,OAAO,CAAC,IAAI,CAAC;IA2CD,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;IAoG9D;;;;OAIG;IACY,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAMpE;;;;OAIG;IACY,KAAK,CAClB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,OAAO,GAAE,QAAQ,CAAC,YAAY,CAAM,GACnC,OAAO,CAAC,IAAI,CAAC;IAMhB;;OAEG;IACY,IAAI,CACjB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,MAAM,EAAE,KAAK,GACZ,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;IAUpB,SAAS,CACtB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA6C,GACjE,OAAO,CAAC,IAAI,CAAC;IAMD,QAAQ,CACrB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA6C,GACjE,OAAO,CAAC,IAAI,CAAC;IAMD,IAAI,CACjB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,IAAI,GAAE,QAAQ,CAAC,KAAK,CAAC,QAA6C,GACjE,OAAO,CAAC,IAAI,CAAC;IAMD,WAAW,CACxB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC,EAC9B,OAAO,CAAC,EAAE;QAAC,KAAK,EAAE,MAAM,CAAA;KAAC,GACxB,OAAO,CAAC,IAAI,CAAC;IAWD,UAAU,CACvB,IAAI,EAAE,gBAAgB,CAAC,gBAAgB,CAAC,EACxC,GAAG,SAAS,EAAE,MAAM,EAAE,GACrB,OAAO,CAAC,IAAI,CAAC;IAsDD,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAOnD,UAAU,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAM1D,SAAS,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAMzD,QAAQ,CAAC,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAKxD,IAAI,CACjB,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,QAAQ,CAAC,mBAAmB,CAAC,GACtC,OAAO,CAAC,IAAI,CAAC;IAKD,KAAK,CAClB,GAAG,EAAE,QAAQ,EACb,OAAO,CAAC,EAAE,QAAQ,CAAC,eAAe,CAAC,GAClC,OAAO,CAAC,IAAI,CAAC;IAKD,WAAW,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAiB1C,QAAQ,IAAI,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IAoCpC,UAAU,CACvB,IAAI,EAAE,gBAAgB,CAAC,OAAO,CAAC,EAC/B,OAAO,GAAE,iBAAsB,GAC9B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IAuDZ,QAAQ,CAAC,IAAI,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;CAY3D"}