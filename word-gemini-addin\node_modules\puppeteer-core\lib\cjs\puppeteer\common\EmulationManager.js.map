{"version": 3, "file": "EmulationManager.js", "sourceRoot": "", "sources": ["../../../../src/common/EmulationManager.ts"], "names": [], "mappings": ";;;AAkBA,iDAAyC;AACzC,uDAAiD;AAKjD;;GAEG;AACH,MAAa,gBAAgB;IAC3B,OAAO,CAAa;IACpB,gBAAgB,GAAG,KAAK,CAAC;IACzB,SAAS,GAAG,KAAK,CAAC;IAClB,kBAAkB,GAAG,IAAI,CAAC;IAE1B,YAAY,MAAkB;QAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAAkB;QACtC,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC;QAC1C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC/B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,IAAI,CAAC,CAAC;QAC1D,MAAM,iBAAiB,GACrB,QAAQ,CAAC,WAAW;YAClB,CAAC,CAAC,EAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAC;YACvC,CAAC,CAAC,EAAC,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAC,CAAC;QAC1C,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,IAAI,KAAK,CAAC;QAE5C,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE;gBACtD,MAAM;gBACN,KAAK;gBACL,MAAM;gBACN,iBAAiB;gBACjB,iBAAiB;aAClB,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE;gBACtD,OAAO,EAAE,QAAQ;aAClB,CAAC;SACH,CAAC,CAAC;QAEH,MAAM,YAAY,GAChB,IAAI,CAAC,gBAAgB,KAAK,MAAM,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC;QAClE,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAGtB;QACC,IAAI,SAAS,EAAE;YACb,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACnD,YAAY,EAAE,SAAS,CAAC,YAAY;gBACpC,gBAAgB,EAAE,SAAS,CAAC,gBAAgB;aAC7C,CAAC,CAAC;SACJ;aAAM;YACL,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;SACxD;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,UAAmB;QACvC,IAAI;YACF,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBACvD,UAAU,EAAE,UAAU,IAAI,EAAE;aAC7B,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,IAAA,0BAAW,EAAC,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;gBACpE,MAAM,IAAI,KAAK,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;aACvD;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,IAAoE;QAEpE,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAEhC;YACA,MAAM;YACN,eAAe;YACf,eAAe;YACf,cAAc;YACd,YAAY;YACZ,YAAY;SACb,CAAC,CAAC;QACH,IAAI;YACF,IAAA,kBAAM,EACJ,CAAC,IAAI,IAAI,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EACrC,kCAAkC,IAAI,EAAE,CACzC,CAAC;YACF,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE;gBAC/D,IAAI,EAAE,IAAI,IAAI,MAAM;aACrB,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAqB;QAC9C,IAAA,kBAAM,EACJ,MAAM,KAAK,IAAI,IAAI,MAAM,IAAI,CAAC,EAC9B,iDAAiD,CAClD,CAAC;QACF,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE;YACxD,IAAI,EAAE,MAAM,IAAI,CAAC;SAClB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,QAAyB;QAClD,IAAI,CAAC,QAAQ,EAAE;YACb,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;SAC3D;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,KAAK,MAAM,YAAY,IAAI,QAAQ,EAAE;gBACnC,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;gBAC/B,IAAA,kBAAM,EACJ,2DAA2D,CAAC,IAAI,CAC9D,IAAI,CACL,EACD,6BAA6B,GAAG,IAAI,CACrC,CAAC;aACH;YACD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACpD,QAAQ,EAAE,QAAQ;aACnB,CAAC,CAAC;SACJ;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,IAAa;QAClC,IAAA,kBAAM,EACJ,IAAI,KAAK,QAAQ;YACf,IAAI,KAAK,OAAO;YAChB,CAAC,IAAI,IAAI,SAAS,CAAC,KAAK,SAAS,EACnC,0BAA0B,GAAG,IAAI,CAClC,CAAC;QACF,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACpD,KAAK,EAAE,IAAI,IAAI,EAAE;SAClB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAA2B;QAC9C,MAAM,EAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC,EAAC,GAAG,OAAO,CAAC;QACpD,IAAI,SAAS,GAAG,CAAC,GAAG,IAAI,SAAS,GAAG,GAAG,EAAE;YACvC,MAAM,IAAI,KAAK,CACb,sBAAsB,SAAS,kDAAkD,CAClF,CAAC;SACH;QACD,IAAI,QAAQ,GAAG,CAAC,EAAE,IAAI,QAAQ,GAAG,EAAE,EAAE;YACnC,MAAM,IAAI,KAAK,CACb,qBAAqB,QAAQ,+CAA+C,CAC7E,CAAC;SACH;QACD,IAAI,QAAQ,GAAG,CAAC,EAAE;YAChB,MAAM,IAAI,KAAK,CACb,qBAAqB,QAAQ,uCAAuC,CACrE,CAAC;SACH;QACD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kCAAkC,EAAE;YAC1D,SAAS;YACT,QAAQ;YACR,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,2BAA2B;QAC/B,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,6BAA6B;QACjC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,6CAA6C,EAAE;YACrE,KAAK,EAAE,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC;SAChC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,OAAgB;QACzC,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE;YACvC,OAAO;SACR;QACD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QAClC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sCAAsC,EAAE;YAC9D,KAAK,EAAE,CAAC,OAAO;SAChB,CAAC,CAAC;IACL,CAAC;CACF;AA7LD,4CA6LC"}