{"version": 3, "file": "ExecutionContext.js", "sourceRoot": "", "sources": ["../../../../src/common/ExecutionContext.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAOH,uEAA+D;AAC/D,qDAAsD;AAEtD,+DAAuD;AACvD,6CAAqC;AAErC,yDAAoD;AAEpD,+CAA0C;AAC1C,6CAAqC;AACrC,2DAAmD;AAEnD,uCAOmB;AAEnB,MAAM,gBAAgB,GAAG,6CAA6C,CAAC;AAEvE,MAAM,mBAAmB,GAAG,CAAC,GAAW,EAAE,EAAE;IAC1C,OAAO,iBAAiB,GAAG,EAAE,CAAC;AAChC,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAa,gBAAgB;IAC3B,OAAO,CAAa;IACpB,MAAM,CAAiB;IACvB,UAAU,CAAS;IACnB,YAAY,CAAU;IAEtB,YACE,MAAkB,EAClB,cAA4D,EAC5D,KAAqB;QAErB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,EAAE,CAAC;QACpC,IAAI,cAAc,CAAC,IAAI,EAAE;YACvB,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC;SACzC;IACH,CAAC;IAED,kBAAkB,GAAG,KAAK,CAAC;IAC3B,cAAc,CAAoC;IAClD,IAAI,aAAa;QACf,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,EAAsB,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,qBAAqB,CACxB,IAAI,oBAAO,CACT,qBAAqB,EACrB,sCAAgB,CAAC,QAA2C,CAC7D,CACF;gBACD,IAAI,CAAC,qBAAqB,CACxB,IAAI,oBAAO,CAAC,wBAAwB,EAAE,CAAC,KAAK,EAC1C,OAA4B,EAC5B,QAAgB,EACW,EAAE;oBAC7B,MAAM,OAAO,GAAG,sCAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;oBAC7D,OAAO,OAAO,CAAC,gBAAgB,EAAE,CAAC,cAAc,CAC9C,CAAC,GAAG,QAAQ,EAAE,EAAE;wBACd,OAAO,QAAQ,CAAC;oBAClB,CAAC,EACD,GAAG,CAAC,MAAM,wCAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAC9C,CAAC;gBACJ,CAAC,CAAoC,CAAC,CACvC;aACF,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;SAChC;QACD,kCAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACrC,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;aACJ;YACD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;gBACtC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAqC,CAAC;YACzE,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,cAAkD,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,OAAgB;QAC1C,IAAI;YACF,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACjD,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;aAC5D;SACF;QAAC,MAAM;YACN,0EAA0E;YAC1E,uEAAuE;YACvE,gCAAgC;SACjC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACH,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgDG;IACH,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACtD,CAAC;IAkBD,KAAK,CAAC,SAAS,CAIb,aAAsB,EACtB,YAA2B,EAC3B,GAAG,IAAY;QAEf,MAAM,gBAAgB,GAAG,mBAAmB,CAC1C,IAAA,0CAAgC,EAAC,YAAY,CAAC,EAAE,QAAQ,EAAE;YACxD,sBAAY,CAAC,YAAY,CAC5B,CAAC;QAEF,IAAI,IAAA,kBAAQ,EAAC,YAAY,CAAC,EAAE;YAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;YAClC,MAAM,UAAU,GAAG,YAAY,CAAC;YAChC,MAAM,uBAAuB,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC/D,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,GAAG,UAAU,KAAK,gBAAgB,IAAI,CAAC;YAE3C,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO;iBAChE,IAAI,CAAC,kBAAkB,EAAE;gBACxB,UAAU,EAAE,uBAAuB;gBACnC,SAAS;gBACT,aAAa;gBACb,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,IAAI;aAClB,CAAC;iBACD,KAAK,CAAC,YAAY,CAAC,CAAC;YAEvB,IAAI,gBAAgB,EAAE;gBACpB,MAAM,IAAA,+BAAqB,EAAC,gBAAgB,CAAC,CAAC;aAC/C;YAED,OAAO,aAAa;gBAClB,CAAC,CAAC,IAAA,+BAAqB,EAAC,YAAY,CAAC;gBACrC,CAAC,CAAC,IAAA,wBAAc,EAAC,IAAI,EAAE,YAAY,CAAC,CAAC;SACxC;QAED,MAAM,mBAAmB,GAAG,IAAA,+BAAiB,EAAC,YAAY,CAAC,CAAC;QAC5D,MAAM,gCAAgC,GAAG,gBAAgB,CAAC,IAAI,CAC5D,mBAAmB,CACpB;YACC,CAAC,CAAC,mBAAmB;YACrB,CAAC,CAAC,GAAG,mBAAmB,KAAK,gBAAgB,IAAI,CAAC;QACpD,IAAI,qBAAqB,CAAC;QAC1B,IAAI;YACF,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAClE,mBAAmB,EAAE,gCAAgC;gBACrD,kBAAkB,EAAE,IAAI,CAAC,UAAU;gBACnC,SAAS,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAClE,aAAa;gBACb,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,IACE,KAAK,YAAY,SAAS;gBAC1B,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,uCAAuC,CAAC,EACjE;gBACA,KAAK,CAAC,OAAO,IAAI,qCAAqC,CAAC;aACxD;YACD,MAAM,KAAK,CAAC;SACb;QACD,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAC,GAC5C,MAAM,qBAAqB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,gBAAgB,EAAE;YACpB,MAAM,IAAA,+BAAqB,EAAC,gBAAgB,CAAC,CAAC;SAC/C;QACD,OAAO,aAAa;YAClB,CAAC,CAAC,IAAA,+BAAqB,EAAC,YAAY,CAAC;YACrC,CAAC,CAAC,IAAA,wBAAc,EAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAEvC,KAAK,UAAU,eAAe,CAE5B,GAAY;YAEZ,IAAI,GAAG,YAAY,oBAAO,EAAE;gBAC1B,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAC3B;YACD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBAC3B,mCAAmC;gBACnC,OAAO,EAAC,mBAAmB,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAC,CAAC;aACpD;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE;gBACtB,OAAO,EAAC,mBAAmB,EAAE,IAAI,EAAC,CAAC;aACpC;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;gBAC5B,OAAO,EAAC,mBAAmB,EAAE,UAAU,EAAC,CAAC;aAC1C;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE;gBAC7B,OAAO,EAAC,mBAAmB,EAAE,WAAW,EAAC,CAAC;aAC3C;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;gBACvB,OAAO,EAAC,mBAAmB,EAAE,KAAK,EAAC,CAAC;aACrC;YACD,MAAM,YAAY,GAChB,GAAG,IAAI,CAAC,GAAG,YAAY,yBAAW,IAAI,GAAG,YAAY,mCAAgB,CAAC;gBACpE,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,IAAI,CAAC;YACX,IAAI,YAAY,EAAE;gBAChB,IAAI,YAAY,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;oBAC5C,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;iBACH;gBACD,IAAI,YAAY,CAAC,QAAQ,EAAE;oBACzB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;iBAC1C;gBACD,IAAI,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB,EAAE;oBACnD,OAAO;wBACL,mBAAmB,EACjB,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB;qBAClD,CAAC;iBACH;gBACD,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE;oBACzC,OAAO,EAAC,KAAK,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,KAAK,EAAC,CAAC;iBACnD;gBACD,OAAO,EAAC,QAAQ,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAC,CAAC;aACzD;YACD,OAAO,EAAC,KAAK,EAAE,GAAG,EAAC,CAAC;QACtB,CAAC;IACH,CAAC;CACF;AAlUD,4CAkUC;AAED,MAAM,YAAY,GAAG,CAAC,KAAY,EAAqC,EAAE;IACvE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oCAAoC,CAAC,EAAE;QAChE,OAAO,EAAC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,EAAC,CAAC;KACtC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC,EAAE;QAClE,OAAO,EAAC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,EAAC,CAAC;KACtC;IAED,IACE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC;QAC/D,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC,EAC9D;QACA,MAAM,IAAI,KAAK,CACb,uEAAuE,CACxE,CAAC;KACH;IACD,MAAM,KAAK,CAAC;AACd,CAAC,CAAC"}