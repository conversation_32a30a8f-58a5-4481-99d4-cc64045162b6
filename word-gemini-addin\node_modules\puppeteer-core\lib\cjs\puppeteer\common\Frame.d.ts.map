{"version": 3, "file": "Frame.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/Frame.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAC,aAAa,EAAC,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAC,KAAK,IAAI,SAAS,EAAC,MAAM,iBAAiB,CAAC;AACnD,OAAO,EAAC,YAAY,EAAC,MAAM,wBAAwB,CAAC;AACpD,OAAO,EAAC,IAAI,EAAE,kBAAkB,EAAC,MAAM,gBAAgB,CAAC;AAKxD,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAC3C,OAAO,EACL,mBAAmB,EACnB,0BAA0B,EAC3B,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAEjD,OAAO,EAAmB,uBAAuB,EAAC,MAAM,uBAAuB,CAAC;AAChF,OAAO,EAAC,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAC,MAAM,YAAY,CAAC;AAG9E;;GAEG;AACH,qBAAa,KAAM,SAAQ,SAAS;;IAKlC,aAAa,EAAE,YAAY,CAAC;IACnB,GAAG,EAAE,MAAM,CAAC;IACrB,SAAS,SAAM;IACN,kBAAkB,UAAS;IACpC,gBAAgB,cAAqB;IAC5B,SAAS,CAAC,EAAE,MAAM,CAAC;gBAG1B,YAAY,EAAE,YAAY,EAC1B,OAAO,EAAE,MAAM,EACf,aAAa,EAAE,MAAM,GAAG,SAAS,EACjC,MAAM,EAAE,UAAU;IAcpB,YAAY,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI;IAQ7B,IAAI,IAAI,IAAI;IAIZ,UAAU,IAAI,OAAO;IAIf,IAAI,CACjB,GAAG,EAAE,MAAM,EACX,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IA2EhB,iBAAiB,CAC9B,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IA0BtB,OAAO,IAAI,UAAU;IAIrB,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAItD;;OAEG;IACM,SAAS,IAAI,aAAa;IAInC;;OAEG;IACM,aAAa,IAAI,aAAa;IAIxB,cAAc,CAC3B,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,EAExD,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAQjC,QAAQ,CACrB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,EAExD,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAQtB,CAAC,CAAC,QAAQ,SAAS,MAAM,EACtC,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IAIpC,EAAE,CAAC,QAAQ,SAAS,MAAM,EACvC,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAIpC,KAAK,CAClB,QAAQ,SAAS,MAAM,EACvB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,GAAG,gBAAgB,CACzE,OAAO,CAAC,QAAQ,CAAC,EACjB,MAAM,CACP,EAED,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAKtB,MAAM,CACnB,QAAQ,SAAS,MAAM,EACvB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,gBAAgB,CAC3B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EACxB,MAAM,CACP,GAAG,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,EAEtD,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAKtB,EAAE,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAI3D,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAI1B,UAAU,CACvB,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,IAAI,CAAC;IAIP,IAAI,IAAI,MAAM;IAId,GAAG,IAAI,MAAM;IAIb,WAAW,IAAI,KAAK,GAAG,IAAI;IAI3B,WAAW,IAAI,KAAK,EAAE;IAItB,UAAU,IAAI,OAAO;IAIf,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC;IAIvC,2BAA2B,IAAI,0BAA0B;IAShD,mBAAmB,CAC1B,OAAO,GAAE,kBAAuB,GAC/B,OAAO,CAAC,mBAAmB,CAAC;IAI/B,UAAU,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI;IAKnD,wBAAwB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAI3C,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI;IAQvD,iBAAiB,IAAI,IAAI;IAKzB,iBAAiB,IAAI,IAAI;IAIzB,OAAO,IAAI,IAAI;CAKhB"}