{"version": 3, "file": "Frame.js", "sourceRoot": "", "sources": ["../../../../src/common/Frame.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,8CAAmD;AAGnD,iDAAyC;AACzC,qDAA6C;AAC7C,uDAAiD;AASjD,yDAAiD;AACjD,2DAAgE;AAChE,+DAAgF;AAEhF,uCAAuD;AAEvD;;GAEG;AACH,MAAa,KAAM,SAAQ,gBAAS;IAClC,IAAI,GAAG,EAAE,CAAC;IACV,SAAS,GAAG,KAAK,CAAC;IAClB,OAAO,CAAc;IAErB,aAAa,CAAe;IACnB,GAAG,CAAS;IACrB,SAAS,GAAG,EAAE,CAAC;IACN,kBAAkB,GAAG,KAAK,CAAC;IACpC,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;IAC5B,SAAS,CAAU;IAE5B,YACE,YAA0B,EAC1B,OAAe,EACf,aAAiC,EACjC,MAAkB;QAElB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED,YAAY,CAAC,MAAkB;QAC7B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG;YACZ,CAAC,8BAAU,CAAC,EAAE,IAAI,gCAAa,CAAC,IAAI,CAAC;YACrC,CAAC,mCAAe,CAAC,EAAE,IAAI,gCAAa,CAAC,IAAI,CAAC;SAC3C,CAAC;IACJ,CAAC;IAEQ,IAAI;QACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEQ,UAAU;QACjB,OAAO,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;IACpD,CAAC;IAEQ,KAAK,CAAC,IAAI,CACjB,GAAW,EACX,UAKI,EAAE;QAEN,MAAM,EACJ,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,EACzE,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,gBAAgB,EAAE,CACnE,gBAAgB,CACjB,EACD,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,GACjE,GAAG,OAAO,CAAC;QAEZ,IAAI,2BAA2B,GAAG,KAAK,CAAC;QACxC,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,IAAI,CAAC,aAAa,EAClB,IAAI,EACJ,SAAS,EACT,OAAO,CACR,CAAC;QACF,IAAI,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAAC;YAC9B,QAAQ,CACN,IAAI,CAAC,OAAO,EACZ,GAAG,EACH,OAAO,EACP,cAA8C,EAC9C,IAAI,CAAC,GAAG,CACT;YACD,OAAO,CAAC,kBAAkB,EAAE;SAC7B,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,EAAE;YACV,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAAC;gBAC1B,OAAO,CAAC,kBAAkB,EAAE;gBAC5B,2BAA2B;oBACzB,CAAC,CAAC,OAAO,CAAC,4BAA4B,EAAE;oBACxC,CAAC,CAAC,OAAO,CAAC,6BAA6B,EAAE;aAC5C,CAAC,CAAC;SACJ;QAED,IAAI;YACF,IAAI,KAAK,EAAE;gBACT,MAAM,KAAK,CAAC;aACb;YACD,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;SAC3C;gBAAS;YACR,OAAO,CAAC,OAAO,EAAE,CAAC;SACnB;QAED,KAAK,UAAU,QAAQ,CACrB,MAAkB,EAClB,GAAW,EACX,QAA4B,EAC5B,cAAwD,EACxD,OAAe;YAEf,IAAI;gBACF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;oBAClD,GAAG;oBACH,QAAQ;oBACR,OAAO;oBACP,cAAc;iBACf,CAAC,CAAC;gBACH,2BAA2B,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAClD,IAAI,QAAQ,CAAC,SAAS,KAAK,qCAAqC,EAAE;oBAChE,OAAO,IAAI,CAAC;iBACb;gBACD,OAAO,QAAQ,CAAC,SAAS;oBACvB,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,QAAQ,CAAC,SAAS,OAAO,GAAG,EAAE,CAAC;oBAC9C,CAAC,CAAC,IAAI,CAAC;aACV;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,IAAA,0BAAW,EAAC,KAAK,CAAC,EAAE;oBACtB,OAAO,KAAK,CAAC;iBACd;gBACD,MAAM,KAAK,CAAC;aACb;QACH,CAAC;IACH,CAAC;IAEQ,KAAK,CAAC,iBAAiB,CAC9B,UAGI,EAAE;QAEN,MAAM,EACJ,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,iBAAiB,EAAE,GACjE,GAAG,OAAO,CAAC;QACZ,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,IAAI,CAAC,aAAa,EAClB,IAAI,EACJ,SAAS,EACT,OAAO,CACR,CAAC;QACF,MAAM,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAAC;YAChC,OAAO,CAAC,kBAAkB,EAAE;YAC5B,OAAO,CAAC,6BAA6B,EAAE;YACvC,OAAO,CAAC,4BAA4B,EAAE;SACvC,CAAC,CAAC;QACH,IAAI;YACF,IAAI,KAAK,EAAE;gBACT,MAAM,KAAK,CAAC;aACb;YACD,OAAO,MAAM,OAAO,CAAC,kBAAkB,EAAE,CAAC;SAC3C;gBAAS;YACR,OAAO,CAAC,OAAO,EAAE,CAAC;SACnB;IACH,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,gBAAgB;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC,gBAAgB,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACM,SAAS;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACM,aAAa;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC;IACtC,CAAC;IAEQ,KAAK,CAAC,cAAc,CAI3B,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EACzC,IAAI,CAAC,cAAc,CAAC,IAAI,EACxB,YAAY,CACb,CAAC;QACF,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAChE,CAAC;IAEQ,KAAK,CAAC,QAAQ,CAIrB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAClB,YAAY,CACb,CAAC;QACF,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1D,CAAC;IAEQ,KAAK,CAAC,CAAC,CACd,QAAkB;QAElB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAEQ,KAAK,CAAC,EAAE,CACf,QAAkB;QAElB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAEQ,KAAK,CAAC,KAAK,CAQlB,QAAkB,EAClB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACjE,CAAC;IAEQ,KAAK,CAAC,MAAM,CAQnB,QAAkB,EAClB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAClE,CAAC;IAEQ,KAAK,CAAC,EAAE,CAAC,UAAkB;QAClC,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAEQ,KAAK,CAAC,UAAU,CACvB,IAAY,EACZ,UAGI,EAAE;QAEN,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IAEQ,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;IAC1B,CAAC;IAEQ,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAEQ,WAAW;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IACrE,CAAC;IAEQ,WAAW;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC7D,CAAC;IAEQ,UAAU;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,KAAK,EAAE,CAAC;IACtC,CAAC;IAED,2BAA2B;QACzB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,OAAO,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACrE;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACvC,IAAA,kBAAM,EAAC,WAAW,KAAK,IAAI,CAAC,CAAC;QAC7B,OAAO,WAAW,CAAC,2BAA2B,EAAE,CAAC;IACnD,CAAC;IAEQ,mBAAmB,CAC1B,UAA8B,EAAE;QAEhC,OAAO,IAAI,CAAC,2BAA2B,EAAE,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;IACzE,CAAC;IAED,UAAU,CAAC,YAAiC;QAC1C,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC;IACrE,CAAC;IAED,wBAAwB,CAAC,GAAW;QAClC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IAClB,CAAC;IAED,iBAAiB,CAAC,QAAgB,EAAE,IAAY;QAC9C,IAAI,IAAI,KAAK,MAAM,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;SAC/B;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAC9C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACjC,CAAC;IAED,OAAO;QACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC,OAAO,EAAE,CAAC;QAClC,IAAI,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;CACF;AAtVD,sBAsVC"}