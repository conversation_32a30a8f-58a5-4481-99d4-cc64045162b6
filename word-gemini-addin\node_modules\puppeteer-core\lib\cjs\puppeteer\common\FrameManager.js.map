{"version": 3, "file": "FrameManager.js", "sourceRoot": "", "sources": ["../../../../src/common/FrameManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,iDAAyC;AACzC,uDAAiD;AAEjD,mDAAgE;AAChE,qEAAoE;AACpE,uDAA+C;AAC/C,+DAAuD;AAEvD,yCAA6C;AAC7C,iDAAyC;AAEzC,2DAAgE;AAChE,2DAAmD;AAGnD,uCAAmD;AAEnD;;GAEG;AACU,QAAA,kBAAkB,GAAG,6BAA6B,CAAC;AAEhE;;;;;GAKG;AACU,QAAA,yBAAyB,GAAG;IACvC,aAAa,EAAE,MAAM,CAAC,4BAA4B,CAAC;IACnD,cAAc,EAAE,MAAM,CAAC,6BAA6B,CAAC;IACrD,aAAa,EAAE,MAAM,CAAC,4BAA4B,CAAC;IACnD,YAAY,EAAE,MAAM,CAAC,2BAA2B,CAAC;IACjD,cAAc,EAAE,MAAM,CAAC,6BAA6B,CAAC;IACrD,4BAA4B,EAAE,MAAM,CAClC,2CAA2C,CAC5C;IACD,uBAAuB,EAAE,MAAM,CAAC,sCAAsC,CAAC;IACvE,yBAAyB,EAAE,MAAM,CAAC,wCAAwC,CAAC;CAC5E,CAAC;AAEF;;;;GAIG;AACH,MAAa,YAAa,SAAQ,8BAAY;IAC5C,KAAK,CAAO;IACZ,eAAe,CAAiB;IAChC,gBAAgB,CAAkB;IAClC,mBAAmB,GAAG,IAAI,GAAG,EAA4B,CAAC;IAC1D,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;IACpC,OAAO,CAAa;IACpB;;OAEG;IACH,UAAU,GAAG,IAAI,wBAAS,EAAS,CAAC;IAEpC;;;;OAIG;IACH,uBAAuB,GAAG,IAAI,GAAG,EAAU,CAAC;IAE5C,8BAA8B,GAAG,IAAI,OAAO,EAGzC,CAAC;IAEJ,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,YACE,MAAkB,EAClB,IAAU,EACV,iBAA0B,EAC1B,eAAgC;QAEhC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,kCAAc,CAAC,MAAM,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAEO,mBAAmB,CAAC,OAAmB;QAC7C,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,KAAK,CAAC,EAAE;YACvC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,KAAK,CAAC,EAAE;YACxC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACjD,KAAK,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,8BAA8B,EAAE,KAAK,CAAC,EAAE;YACjD,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CACR,oBAAoB,EACpB,CAAC,KAAuC,EAAE,EAAE;YAC1C,IAAI,CAAC,gBAAgB,CACnB,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,MAAgD,CACvD,CAAC;QACJ,CAAC,CACF,CAAC;QACF,OAAO,CAAC,EAAE,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE;YAC7C,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,0BAA0B,EAAE,KAAK,CAAC,EAAE;YAC7C,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAE;YACpD,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,mCAAmC,EAAE,KAAK,CAAC,EAAE;YACtD,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAClD,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,KAAK,CAAC,EAAE;YACxC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAqB,IAAI,CAAC,OAAO;QAChD,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC/B,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC;aACjC,CAAC,CAAC;YAEH,MAAM,EAAC,SAAS,EAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;gBAC9D,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBACtC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,0BAAkB,CAAC,CAAC;gBAC/D,CAAC,CAAC;gBACF,yDAAyD;gBACzD,MAAM,KAAK,IAAI,CAAC,OAAO;oBACrB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE;oBACnC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;aACtB,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,wEAAwE;YACxE,IAAI,IAAA,0BAAW,EAAC,KAAK,CAAC,IAAI,IAAA,mCAAmB,EAAC,KAAK,CAAC,EAAE;gBACpD,OAAO;aACR;YAED,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED,oBAAoB,CAClB,SAAiB,EACjB,UAAsB,IAAI,CAAC,OAAO;QAElC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACjE,IAAA,kBAAM,EAAC,OAAO,EAAE,4CAA4C,GAAG,SAAS,CAAC,CAAC;QAC1E,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,uBAAuB,CACrB,SAAiB,EACjB,UAAsB,IAAI,CAAC,OAAO;QAElC,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,SAAS,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,SAAS;QACP,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QACjD,IAAA,kBAAM,EAAC,SAAS,EAAE,kCAAkC,CAAC,CAAC;QACtD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM;QACJ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,OAAe;QACnB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;IAED,kBAAkB,CAAC,MAAc;QAC/B,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC7C,OAAO;SACR;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAG,CAAC,CAAC;SACxC;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAG,CAAC,CAAC;QAC7C,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,2BAA2B,CAAC,MAAkB;QAC5C,IAAI,OAAO,GAAG,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,OAAO,GAAG,IAAI,mDAA0B,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACxE,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SAC1D;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,iBAAiB,CAAC,KAAwC;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;QACD,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,sBAAsB,CAAC,OAAe;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC5B,CAAC;IAED,sBAAsB,CAAC,OAAe;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,gBAAgB,CACd,OAAmB,EACnB,SAAkC;QAElC,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE;YAC5B,IAAI,CAAC,gBAAgB,CACnB,OAAO,EACP,SAAS,CAAC,KAAK,CAAC,EAAE,EAClB,SAAS,CAAC,KAAK,CAAC,QAAQ,CACzB,CAAC;SACH;QACD,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;YACzD,KAAK,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAC9C;aAAM;YACL,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAC1B,OAAO;SACR;QAED,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,WAAW,EAAE;YACzC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;SACvC;IACH,CAAC;IAED,gBAAgB,CACd,OAAmB,EACnB,OAAe,EACf,aAAqB;QAErB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,KAAK,EAAE;YACT,IAAI,OAAO,IAAI,KAAK,CAAC,UAAU,EAAE,EAAE;gBACjC,kDAAkD;gBAClD,iDAAiD;gBACjD,yBAAyB;gBACzB,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;aAC7B;YACD,OAAO;SACR;QAED,KAAK,GAAG,IAAI,gBAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,YAAiC;QACvD,MAAM,OAAO,GAAG,YAAY,CAAC,EAAE,CAAC;QAChC,MAAM,WAAW,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC;QAE3C,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE7C,iCAAiC;QACjC,IAAI,KAAK,EAAE;YACT,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;gBACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;aACtC;SACF;QAED,+BAA+B;QAC/B,IAAI,WAAW,EAAE;YACf,IAAI,KAAK,EAAE;gBACT,wEAAwE;gBACxE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACnC,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC;aACrB;iBAAM;gBACL,iCAAiC;gBACjC,KAAK,GAAG,IAAI,gBAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aAC9D;YACD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACjC;QAED,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACpD,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,OAAmB,EAAE,IAAY;QAC1D,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACjC,OAAO;SACR;QAED,MAAM,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE;YAC1D,MAAM,EAAE,iBAAiB,sBAAY,CAAC,YAAY,EAAE;YACpD,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,MAAM,EAAE;aACV,MAAM,CAAC,KAAK,CAAC,EAAE;YACd,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,OAAO,CAAC;QACrC,CAAC,CAAC;aACD,GAAG,CAAC,KAAK,CAAC,EAAE;YACX,mEAAmE;YACnE,kBAAkB;YAClB,OAAO,OAAO;iBACX,IAAI,CAAC,0BAA0B,EAAE;gBAChC,OAAO,EAAE,KAAK,CAAC,GAAG;gBAClB,SAAS,EAAE,IAAI;gBACf,mBAAmB,EAAE,IAAI;aAC1B,CAAC;iBACD,KAAK,CAAC,oBAAU,CAAC,CAAC;QACvB,CAAC,CAAC,CACL,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,+BAA+B,CAAC,OAAe,EAAE,GAAW;QAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;QACD,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACzE,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED,gBAAgB,CACd,OAAe,EACf,MAA8C;QAE9C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,MAAM,KAAK,QAAQ,EAAE;YACvB,gEAAgE;YAChE,qCAAqC;YACrC,kEAAkE;YAClE,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;aACtC;SACF;aAAM,IAAI,MAAM,KAAK,MAAM,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;SAC1D;IACH,CAAC;IAED,0BAA0B,CACxB,cAA4D,EAC5D,OAAmB;QAEnB,MAAM,OAAO,GAAG,cAAc,CAAC,OAAyC,CAAC;QACzE,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;QAC3C,MAAM,KAAK,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC5E,IAAI,KAAgC,CAAC;QACrC,IAAI,KAAK,EAAE;YACT,sEAAsE;YACtE,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,OAAO,EAAE;gBAC/B,OAAO;aACR;YACD,IAAI,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;gBACjE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC;aAClC;iBAAM,IACL,cAAc,CAAC,IAAI,KAAK,0BAAkB;gBAC1C,CAAC,KAAK,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC,UAAU,EAAE,EAC3C;gBACA,0EAA0E;gBAC1E,oEAAoE;gBACpE,qBAAqB;gBACrB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC;aACvC;SACF;QACD,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,KAAK,EAAE,OAAO,EAAE,IAAI,IAAI,CAAC,OAAO,EAChC,cAAc,EACd,KAAK,CACN,CAAC;QACF,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;SAC3B;QACD,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,cAAc,CAAC,EAAE,EAAE,CAAC;QACnD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED,4BAA4B,CAC1B,kBAA0B,EAC1B,OAAmB;QAEnB,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,kBAAkB,EAAE,CAAC;QACpD,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;SAC/B;IACH,CAAC;IAED,2BAA2B,CAAC,OAAmB;QAC7C,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE;YAC/D,yDAAyD;YACzD,0BAA0B;YAC1B,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE;gBAC/B,SAAS;aACV;YACD,IAAI,OAAO,CAAC,MAAM,EAAE;gBAClB,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;aAC/B;YACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SACtC;IACH,CAAC;IAED,wBAAwB,CAAC,KAAY;QACnC,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;YACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;SACtC;QACD,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,iCAAyB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;CACF;AAhaD,oCAgaC"}