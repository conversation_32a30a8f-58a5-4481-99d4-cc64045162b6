{"version": 3, "file": "FrameTree.js", "sourceRoot": "", "sources": ["../../../../src/common/FrameTree.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,qDAA6C;AAE7C;;;;;;GAMG;AACH,MAAa,SAAS;IACpB,OAAO,GAAG,IAAI,GAAG,EAAiB,CAAC;IACnC,2BAA2B;IAC3B,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAC;IACvC,2BAA2B;IAC3B,SAAS,GAAG,IAAI,GAAG,EAAuB,CAAC;IAC3C,UAAU,CAAS;IACnB,aAAa,GAAG,IAAI,GAAG,EAAgC,CAAC;IAExD,YAAY;QACV,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,OAAO,CAAC,OAAe;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,OAAe;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,KAAK,EAAE;YACT,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC/B;QACD,MAAM,QAAQ,GAAG,sBAAQ,CAAC,MAAM,EAAS,CAAC;QAC1C,MAAM,SAAS,GACb,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,GAAG,EAAmB,CAAC;QAChE,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxB,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;IACjC,CAAC;IAED,MAAM;QACJ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,QAAQ,CAAC,KAAY;QACnB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,KAAK,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;YAChD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;gBACxC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;aAChD;YACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAE,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACrD;aAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC3B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;SACzB;QACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE;YACnD,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,KAAY;QACtB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,KAAK,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACxD;aAAM;YACL,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;SAC7B;IACH,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,EAAE,CAAC;SACX;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;aACxB,GAAG,CAAC,EAAE,CAAC,EAAE;YACR,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,KAAK,EAAkB,EAAE;YAChC,OAAO,KAAK,KAAK,SAAS,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC9C,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACvD,CAAC;CACF;AAjFD,8BAiFC"}