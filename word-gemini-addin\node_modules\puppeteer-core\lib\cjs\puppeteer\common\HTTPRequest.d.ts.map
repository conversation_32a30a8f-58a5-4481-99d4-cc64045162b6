{"version": 3, "file": "HTTPRequest.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/HTTPRequest.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAC;AACtC,OAAO,EACL,wBAAwB,EACxB,SAAS,EAET,WAAW,IAAI,eAAe,EAE9B,wBAAwB,EACxB,YAAY,EACZ,kBAAkB,EAEnB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAC,YAAY,EAAC,MAAM,wBAAwB,CAAC;AAGpD,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAI3C;;GAEG;AACH,qBAAa,WAAY,SAAQ,eAAe;;IACrC,UAAU,EAAE,MAAM,CAAC;IACnB,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;IACpC,YAAY,EAAE,MAAM,GAAG,IAAI,CAAQ;IACnC,SAAS,EAAE,YAAY,GAAG,IAAI,CAAQ;IACtC,gBAAgB,UAAS;IACzB,cAAc,EAAE,WAAW,EAAE,CAAC;IAsBvC,IAAa,MAAM,IAAI,UAAU,CAEhC;gBAGC,MAAM,EAAE,UAAU,EAClB,KAAK,EAAE,KAAK,GAAG,IAAI,EACnB,cAAc,EAAE,MAAM,GAAG,SAAS,EAClC,iBAAiB,EAAE,OAAO,EAC1B,IAAI,EAAE;QACJ;;WAEG;QACH,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;QACtC;;WAEG;QACH,QAAQ,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC;QACrC;;WAEG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB;;WAEG;QACH,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;QAClC;;WAEG;QACH,SAAS,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;QACvC;;WAEG;QACH,IAAI,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC;KACtC,EACD,aAAa,EAAE,WAAW,EAAE;IAwBrB,GAAG,IAAI,MAAM;IAIb,wBAAwB,IAAI,wBAAwB;IAKpD,kBAAkB,IAAI,OAAO,CAAC,kBAAkB,CAAC,GAAG,IAAI;IAKxD,gBAAgB,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI;IAKvD,wBAAwB,IAAI,wBAAwB;IAUpD,4BAA4B,IAAI,OAAO;IAIvC,sBAAsB,CAC7B,cAAc,EAAE,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,GAChD,IAAI;IAIQ,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC;IAkB5C,YAAY,IAAI,YAAY;IAI5B,MAAM,IAAI,MAAM;IAIhB,QAAQ,IAAI,MAAM,GAAG,SAAS;IAI9B,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAIjC,QAAQ,IAAI,YAAY,GAAG,IAAI;IAI/B,KAAK,IAAI,KAAK,GAAG,IAAI;IAIrB,mBAAmB,IAAI,OAAO;IAI9B,SAAS,IAAI,QAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS;IAInD,aAAa,IAAI,WAAW,EAAE;IAI9B,OAAO,IAAI;QAAC,SAAS,EAAE,MAAM,CAAA;KAAC,GAAG,IAAI;IAS/B,QAAQ,CACrB,SAAS,GAAE,wBAA6B,EACxC,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IA6DD,OAAO,CACpB,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,EACrC,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;IA8ED,KAAK,CAClB,SAAS,GAAE,SAAoB,EAC/B,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,IAAI,CAAC;CAyCjB"}