{"version": 3, "file": "HandleIterator.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/HandleIterator.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAC,QAAQ,EAAC,MAAM,oBAAoB,CAAC;AAE5C,OAAO,EAAC,iBAAiB,EAAE,SAAS,EAAC,MAAM,YAAY,CAAC;AAoDxD;;GAEG;AACH,wBAAuB,uBAAuB,CAAC,CAAC,EAC9C,MAAM,EAAE,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GACrC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAQrC"}