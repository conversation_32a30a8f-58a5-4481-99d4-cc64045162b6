{"version": 3, "file": "HandleIterator.js", "sourceRoot": "", "sources": ["../../../../src/common/HandleIterator.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAE9B;;;;;;GAMG;AACH,KAAK,SAAS,CAAC,CAAC,2BAA2B,CACzC,QAAwC,EACxC,IAAY;IAEZ,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;QACnE,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,OAAO,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE;YAC5B,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACrC,IAAI,MAAM,CAAC,IAAI,EAAE;gBACf,MAAM;aACP;YACD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAC5B;QACD,OAAO,OAAO,CAAC;IACjB,CAAC,EAAE,IAAI,CAAC,CAAC;IACT,MAAM,UAAU,GAAG,CAAC,MAAM,KAAK,CAAC,aAAa,EAAE,CAA8B,CAAC;IAC9E,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;IACtB,KAAK,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;IAC3B,OAAO,UAAU,CAAC,IAAI,KAAK,CAAC,CAAC;AAC/B,CAAC;AAED;;;GAGG;AAEH,KAAK,SAAS,CAAC,CAAC,uBAAuB,CACrC,QAAwC;IAExC,IAAI,IAAI,GAAG,kBAAkB,CAAC;IAC9B,IAAI;QACF,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,2BAA2B,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,EAAE;YAC5D,IAAI,KAAK,CAAC,CAAC;SACZ;KACF;YAAS;QACR,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC;KAC1B;AACH,CAAC;AAID;;GAEG;AACI,KAAK,SAAS,CAAC,CAAC,uBAAuB,CAC5C,MAAsC;IAEtC,KAAK,CAAC,CAAC,uBAAuB,CAC5B,MAAM,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;QACrC,OAAO,CAAC,KAAK,SAAS,CAAC;YACrB,KAAK,CAAC,CAAC,QAAQ,CAAC;QAClB,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CACH,CAAC;AACJ,CAAC;AAVD,0DAUC"}