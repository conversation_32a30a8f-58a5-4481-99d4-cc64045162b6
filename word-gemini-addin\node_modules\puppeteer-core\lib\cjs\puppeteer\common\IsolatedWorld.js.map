{"version": 3, "file": "IsolatedWorld.js", "sourceRoot": "", "sources": ["../../../../src/common/IsolatedWorld.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,iDAAyC;AACzC,qDAA6C;AAO7C,2DAAgE;AAChE,+DAAgF;AAUhF,uCAOmB;AACnB,+CAAoD;AAmDpD;;GAEG;AACH,MAAa,aAAa;IACxB,MAAM,CAAQ;IACd,SAAS,CAA2B;IACpC,QAAQ,GAAG,sBAAQ,CAAC,MAAM,EAAoB,CAAC;IAC/C,SAAS,GAAG,KAAK,CAAC;IAElB,oEAAoE;IACpE,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;IAErC,+EAA+E;IAC/E,SAAS,GAAG,IAAI,GAAG,EAAmB,CAAC;IACvC,YAAY,GAAG,IAAI,yBAAW,EAAE,CAAC;IAEjC,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,YAAY,KAAY;QACtB,+EAA+E;QAC/E,0BAA0B;QAC1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAClE,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;IACnC,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;IAC5C,CAAC;IAED,KAAK;QACH,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,YAAY;QACV,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,sBAAQ,CAAC,MAAM,EAAE,CAAC;IACpC,CAAC;IAED,UAAU,CAAC,OAAyB;QAClC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC/B,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;IACpC,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED,OAAO;QACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACjE,IAAI,CAAC,YAAY,CAAC,YAAY,CAC5B,IAAI,KAAK,CAAC,6CAA6C,CAAC,CACzD,CAAC;IACJ,CAAC;IAED,gBAAgB;QACd,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAI,KAAK,CACb,yDAAyD,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,iCAAiC,CAC5G,CAAC;SACH;QACD,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EACzC,IAAI,CAAC,cAAc,CAAC,IAAI,EACxB,YAAY,CACb,CAAC;QACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,OAAO,OAAO,CAAC,cAAc,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAClB,YAAY,CACb,CAAC;QACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,OAAO,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,CAAC,CACL,QAAkB;QAElB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,EAAE,CACN,QAAkB;QAElB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,CAAC,SAAS,GAAG,MAAM,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE;YACjD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,EAAE,CAAC,UAAkB;QACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,KAAK,CAQT,QAAkB,EAClB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAC3E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,MAAM,CAQV,QAAkB,EAClB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAC5E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,wBAAc,CAAC,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,UAAU,CACd,IAAY,EACZ,UAGI,EAAE;QAEN,MAAM,EACJ,SAAS,GAAG,CAAC,MAAM,CAAC,EACpB,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,GACpD,GAAG,OAAO,CAAC;QAEZ,MAAM,IAAA,wBAAc,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEjC,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,MAAM,EACX,SAAS,EACT,OAAO,CACR,CAAC;QACF,MAAM,KAAK,GAAG,MAAM,sBAAQ,CAAC,IAAI,CAA2B;YAC1D,OAAO,CAAC,kBAAkB,EAAE;YAC5B,OAAO,CAAC,gBAAgB,EAAE;SAC3B,CAAC,CAAC;QACH,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,IAAI,KAAK,EAAE;YACT,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED,KAAK,CAAC,KAAK,CACT,QAAgB,EAChB,OAAgC;QAEhC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;QACrB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;QACrB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,QAAgB,EAAE,GAAG,MAAgB;QAChD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;QAC9C,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,QAAgB;QACxB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC;QACnB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,IAAI,CACR,QAAgB,EAChB,IAAY,EACZ,OAAuC;QAEvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjC,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,yEAAyE;IACzE,yEAAyE;IACzE,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;IACrB,KAAK,CAAC,oBAAoB,CACxB,OAAyB,EACzB,IAAY;QAEZ,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACnC,OAAO;SACR;QAED,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAI;YACF,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CACxB,oBAAoB,EACpB,OAAO,CAAC,YAAY;gBAClB,CAAC,CAAC;oBACE,IAAI;oBACJ,oBAAoB,EAAE,OAAO,CAAC,YAAY;iBAC3C;gBACH,CAAC,CAAC;oBACE,IAAI;oBACJ,kBAAkB,EAAE,OAAO,CAAC,UAAU;iBACvC,CACN,CAAC;YAEF,MAAM,OAAO,CAAC,QAAQ,CAAC,wBAAc,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;YAEzD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACjC;QAAC,OAAO,KAAK,EAAE;YACd,iEAAiE;YACjE,uEAAuE;YACvE,mCAAmC;YACnC,IAAI,KAAK,YAAY,KAAK,EAAE;gBAC1B,qBAAqB;gBACrB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;oBAC7D,OAAO;iBACR;gBACD,mBAAmB;gBACnB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC,EAAE;oBACnE,OAAO;iBACR;aACF;YAED,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;SACnB;gBAAS;YACR,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;SACvB;IACH,CAAC;IAED,gBAAgB,GAAG,KAAK,EACtB,KAA0C,EAC3B,EAAE;QACjB,IAAI,OAAuB,CAAC;QAC5B,IAAI;YACF,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACrC;QAAC,MAAM;YACN,mEAAmE;YACnE,6CAA6C;YAC7C,OAAO;SACR;QACD,MAAM,EAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAC,GAAG,OAAO,CAAC;QACnD,IAAI,IAAI,KAAK,UAAU,EAAE;YACvB,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACpC,OAAO;SACR;QAED,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YACnD,IAAI,KAAK,CAAC,kBAAkB,KAAK,OAAO,CAAC,UAAU,EAAE;gBACnD,OAAO;aACR;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;SACnD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAA,oBAAU,EAAC,GAAG,CAAC,CAAC;SACjB;IACH,CAAC,CAAC;IAEF,eAAe,CAMb,YAA2B,EAC3B,UAKI,EAAE,EACN,GAAG,IAAY;QAEf,MAAM,EACJ,OAAO,GAAG,KAAK,EACf,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EACzC,IAAI,EACJ,MAAM,GACP,GAAG,OAAO,CAAC;QACZ,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,EAAE;YAC9C,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QACD,MAAM,QAAQ,GAAG,IAAI,sBAAQ,CAC3B,IAAI,EACJ;YACE,OAAO;YACP,IAAI;YACJ,OAAO;YACP,MAAM;SACP,EACD,YAEU,EACV,GAAG,IAAI,CACR,CAAC;QACF,OAAO,QAAQ,CAAC,MAAM,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;YACxB,OAAO,QAAQ,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,aAA0C;QAE1C,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACvD,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC1D,aAAa,EAAE,aAAa;YAC5B,kBAAkB,EAAE,gBAAgB,CAAC,UAAU;SAChD,CAAC,CAAC;QACH,OAAO,IAAA,wBAAc,EAAC,gBAAgB,EAAE,MAAM,CAAmB,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,WAAW,CAA2B,MAAS;QACnD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAA,kBAAM,EACJ,MAAM,CAAC,gBAAgB,EAAE,KAAK,OAAO,EACrC,oEAAoE,CACrE,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC3D,QAAQ,EAAE,MAAM,CAAC,EAAE;SACpB,CAAC,CAAC;QACH,OAAO,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAM,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,cAAc,CAA2B,MAAS;QACtD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,IAAI,MAAM,CAAC,gBAAgB,EAAE,KAAK,OAAO,EAAE;YACzC,OAAO,MAAM,CAAC;SACf;QACD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACvD,QAAQ,EAAE,MAAM,CAAC,YAAY,EAAE,CAAC,QAAQ;SACzC,CAAC,CAAC;QACH,MAAM,SAAS,GAAG,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAC5C,IAAI,CAAC,IAAI,CAAC,aAAa,CACxB,CAAM,CAAC;QACR,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAtaD,sCAsaC;AAED,MAAM,KAAK;IACT,OAAO,GAAG,KAAK,CAAC;IAChB,UAAU,GAAsB,EAAE,CAAC;IAEnC,gBAAgB;IAChB,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC1B;QACD,MAAM,QAAQ,GAAG,sBAAQ,CAAC,MAAM,EAAQ,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtD,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;IACjC,CAAC;IAED,OAAO;QACL,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,OAAO,EAAE;YACZ,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,OAAO;SACR;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;CACF"}