{"version": 3, "file": "JSHandle.js", "sourceRoot": "", "sources": ["../../../../src/common/JSHandle.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,oDAA4C;AAC5C,iDAAyC;AAMzC,uCAKmB;AAEnB;;GAEG;AACH,MAAa,WAAyB,SAAQ,sBAAW;IACvD,SAAS,GAAG,KAAK,CAAC;IAClB,QAAQ,CAAmB;IAC3B,aAAa,CAAgC;IAE7C,IAAa,QAAQ;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,YACE,OAAyB,EACzB,YAA2C;QAE3C,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACpC,CAAC;IAEQ,gBAAgB;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,IAAa,MAAM;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;IAC/B,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,QAAQ,CAIrB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAClB,YAAY,CACb,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACM,KAAK,CAAC,cAAc,CAI3B,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EACzC,IAAI,CAAC,cAAc,CAAC,IAAI,EACxB,YAAY,CACb,CAAC;QACF,OAAO,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,cAAc,CACjD,YAAY,EACZ,IAAI,EACJ,GAAG,IAAI,CACR,CAAC;IACJ,CAAC;IAMQ,KAAK,CAAC,WAAW,CACxB,YAAyB;QAEzB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE;YAClD,OAAO,MAAM,CAAC,YAAiB,CAAC,CAAC;QACnC,CAAC,EAAE,YAAY,CAAC,CAAC;IACnB,CAAC;IAEQ,KAAK,CAAC,aAAa;QAC1B,IAAA,kBAAM,EAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACpC,0EAA0E;QAC1E,wDAAwD;QACxD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/D,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ;YACrC,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;QAC3C,KAAK,MAAM,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;YACtC,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBAC3C,SAAS;aACV;YACD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAA,wBAAc,EAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;SAC1E;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEQ,KAAK,CAAC,SAAS;QACtB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAChC,OAAO,IAAA,+BAAqB,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAClD;QACD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACzC,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;QACH,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACM,SAAS;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,MAAM,IAAA,uBAAa,EAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACvD,CAAC;IAEQ,QAAQ;QACf,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAChC,OAAO,WAAW,GAAG,IAAA,+BAAqB,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAChE;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QACnE,OAAO,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,IAAa,EAAE;QACb,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;IACrC,CAAC;IAEQ,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;CACF;AA1ID,kCA0IC"}