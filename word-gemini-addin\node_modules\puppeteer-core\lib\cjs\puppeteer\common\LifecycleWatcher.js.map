{"version": 3, "file": "LifecycleWatcher.js", "sourceRoot": "", "sources": ["../../../../src/common/LifecycleWatcher.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,iDAAyC;AACzC,qDAA6C;AAE7C,mDAAwD;AAGxD,uDAA0E;AAE1E,2DAAgE;AAChE,uCAImB;AAmBnB,MAAM,4BAA4B,GAAG,IAAI,GAAG,CAG1C;IACA,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;IACxC,CAAC,cAAc,EAAE,aAAa,CAAC;IAC/B,CAAC,cAAc,EAAE,mBAAmB,CAAC;CACtC,CAAC,CAAC;AAEH;;GAEG;AACH,MAAa,gBAAgB;IAC3B,kBAAkB,CAA2B;IAC7C,aAAa,CAAe;IAC5B,MAAM,CAAQ;IACd,QAAQ,CAAS;IACjB,kBAAkB,GAAuB,IAAI,CAAC;IAC9C,eAAe,CAA2B;IAC1C,gBAAgB,CAAS;IAEzB,oBAAoB,CAAkB;IACtC,+BAA+B,GAAG,sBAAQ,CAAC,MAAM,EAAa,CAAC;IAC/D,kBAAkB,GAAG,sBAAQ,CAAC,MAAM,EAAQ,CAAC;IAC7C,8BAA8B,GAAG,sBAAQ,CAAC,MAAM,EAAa,CAAC;IAE9D,0BAA0B,CAAW;IACrC,QAAQ,CAAW;IAEnB,2BAA2B,CAAkB;IAE7C,YACE,YAA0B,EAC1B,KAAY,EACZ,SAA8D,EAC9D,OAAe;QAEf,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC5B,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;SAC/B;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACxC,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;SACzB;QACD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC;QACxC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC9C,MAAM,aAAa,GAAG,4BAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC9D,IAAA,kBAAM,EAAC,aAAa,EAAE,uCAAuC,GAAG,KAAK,CAAC,CAAC;YACvE,OAAO,aAAuC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG;YACrB,IAAA,0BAAgB,EACd,YAAY,CAAC,MAAM,EACnB,uCAAuB,CAAC,YAAY,EACpC,IAAI,CAAC,UAAU,CAAC,IAAI,CAClB,IAAI,EACJ,IAAI,KAAK,CAAC,qDAAqD,CAAC,CACjE,CACF;YACD,IAAA,0BAAgB,EACd,IAAI,CAAC,aAAa,EAClB,2CAAyB,CAAC,cAAc,EACxC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CACxC;YACD,IAAA,0BAAgB,EACd,IAAI,CAAC,aAAa,EAClB,2CAAyB,CAAC,4BAA4B,EACtD,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CACzC;YACD,IAAA,0BAAgB,EACd,IAAI,CAAC,aAAa,EAClB,2CAAyB,CAAC,cAAc,EACxC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAC3B;YACD,IAAA,0BAAgB,EACd,IAAI,CAAC,aAAa,EAClB,2CAAyB,CAAC,YAAY,EACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9B;YACD,IAAA,0BAAgB,EACd,IAAI,CAAC,aAAa,EAClB,2CAAyB,CAAC,aAAa,EACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC;YACD,IAAA,0BAAgB,EACd,IAAI,CAAC,aAAa,CAAC,cAAc,EACjC,+CAA2B,CAAC,OAAO,EACnC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAC3B;YACD,IAAA,0BAAgB,EACd,IAAI,CAAC,aAAa,CAAC,cAAc,EACjC,+CAA2B,CAAC,QAAQ,EACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5B;YACD,IAAA,0BAAgB,EACd,IAAI,CAAC,aAAa,CAAC,cAAc,EACjC,+CAA2B,CAAC,aAAa,EACzC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC;SACF,CAAC;QAEF,IAAI,CAAC,oBAAoB,GAAG,sBAAQ,CAAC,MAAM,CAAQ;YACjD,OAAO,EAAE,IAAI,CAAC,QAAQ;YACtB,OAAO,EAAE,yBAAyB,IAAI,CAAC,QAAQ,cAAc;SAC9D,CAAC,CAAC;QAEH,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,UAAU,CAAC,OAAoB;QAC7B,IAAI,OAAO,CAAC,KAAK,EAAE,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE;YACrE,OAAO;SACR;QACD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QAClC,kEAAkE;QAClE,yEAAyE;QACzE,yCAAyC;QACzC,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,CAAC;QAC5C,IAAI,CAAC,2BAA2B,GAAG,sBAAQ,CAAC,MAAM,EAAE,CAAC;QACrD,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;YAC/B,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,CAAC;SAC7C;IACH,CAAC;IAED,gBAAgB,CAAC,OAAoB;QACnC,IAAI,IAAI,CAAC,kBAAkB,EAAE,UAAU,KAAK,OAAO,CAAC,UAAU,EAAE;YAC9D,OAAO;SACR;QACD,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,CAAC;IAC9C,CAAC;IAED,WAAW,CAAC,QAAsB;QAChC,IAAI,IAAI,CAAC,kBAAkB,EAAE,UAAU,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE;YACzE,OAAO;SACR;QACD,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,CAAC;IAC9C,CAAC;IAED,gBAAgB,CAAC,KAAY;QAC3B,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YACzB,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAC/B,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAC3C,CAAC;YACF,OAAO;SACR;QACD,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,0CAA0C;QAC1C,MAAM,IAAI,CAAC,2BAA2B,EAAE,YAAY,EAAE,CAAC;QACvD,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,CAAC;IAED,UAAU,CAAC,KAAY;QACrB,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,6BAA6B;QAC3B,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,EAAE,CAAC;IAC7D,CAAC;IAED,4BAA4B;QAC1B,OAAO,IAAI,CAAC,8BAA8B,CAAC,YAAY,EAAE,CAAC;IAC5D,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;IAChD,CAAC;IAED,kBAAkB;QAChB,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,CAAC;IAClD,CAAC;IAED,wBAAwB,CAAC,KAAY;QACnC,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE;YACzB,OAAO;SACR;QACD,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,UAAU,CAAC,KAAY;QACrB,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE;YACzB,OAAO;SACR;QACD,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE;YACzB,OAAO;SACR;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED,uBAAuB;QACrB,kCAAkC;QAClC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,EAAE;YACzD,OAAO;SACR;QACD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAClC,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACnC,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACzD;QACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC,gBAAgB,EAAE;YACpE,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACxD;QAED,SAAS,cAAc,CACrB,KAAY,EACZ,iBAA2C;YAE3C,KAAK,MAAM,KAAK,IAAI,iBAAiB,EAAE;gBACrC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACtC,OAAO,KAAK,CAAC;iBACd;aACF;YACD,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;gBACvC,IACE,KAAK,CAAC,kBAAkB;oBACxB,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,EACzC;oBACA,OAAO,KAAK,CAAC;iBACd;aACF;YACD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAA,8BAAoB,EAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3C,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;IAC5E,CAAC;CACF;AAjOD,4CAiOC"}