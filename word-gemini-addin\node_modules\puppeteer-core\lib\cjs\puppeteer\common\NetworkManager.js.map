{"version": 3, "file": "NetworkManager.js", "sourceRoot": "", "sources": ["../../../../src/common/NetworkManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,iDAAyC;AACzC,yEAAuE;AAIvE,uDAA+C;AAE/C,qDAA6C;AAC7C,uDAA+C;AAC/C,qEAA6E;AAC7E,uCAA+C;AA4B/C;;;;;GAKG;AACU,QAAA,2BAA2B,GAAG;IACzC,OAAO,EAAE,MAAM,CAAC,wBAAwB,CAAC;IACzC,sBAAsB,EAAE,MAAM,CAAC,uCAAuC,CAAC;IACvE,QAAQ,EAAE,MAAM,CAAC,yBAAyB,CAAC;IAC3C,aAAa,EAAE,MAAM,CAAC,8BAA8B,CAAC;IACrD,eAAe,EAAE,MAAM,CAAC,gCAAgC,CAAC;CACjD,CAAC;AAEX;;GAEG;AACH,MAAa,cAAe,SAAQ,8BAAY;IAC9C,OAAO,CAAa;IACpB,kBAAkB,CAAU;IAC5B,aAAa,CAA8B;IAC3C,oBAAoB,GAAG,IAAI,4CAAmB,EAAE,CAAC;IACjD,iBAAiB,GAA2B,EAAE,CAAC;IAC/C,YAAY,CAAe;IAC3B,yBAAyB,GAAG,IAAI,GAAG,EAAU,CAAC;IAC9C,+BAA+B,GAAG,KAAK,CAAC;IACxC,mCAAmC,GAAG,KAAK,CAAC;IAC5C,kBAAkB,GAAG,KAAK,CAAC;IAC3B,0BAA0B,GAA8B;QACtD,OAAO,EAAE,KAAK;QACd,MAAM,EAAE,CAAC,CAAC;QACV,QAAQ,EAAE,CAAC,CAAC;QACZ,OAAO,EAAE,CAAC;KACX,CAAC;IACF,aAAa,CAAkB;IAE/B,YACE,MAAkB,EAClB,iBAA0B,EAC1B,YAAyC;QAEzC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,OAAO,CAAC,EAAE,CACb,2BAA2B,EAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CACrC,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,EAAE,CACb,gCAAgC,EAChC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAC1C,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,EAAE,CACb,0BAA0B,EAC1B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CACpC,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,EAAE,CACb,yBAAyB,EACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CACnC,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3E,IAAI,CAAC,OAAO,CAAC,EAAE,CACb,mCAAmC,EACnC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,CAC7C,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;SAC1C;QACD,IAAI,CAAC,aAAa,GAAG,IAAA,gDAAwB,EAC3C,yCAAyC,CAC1C,CAAC;QACF,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC;YACvB,IAAI,CAAC,kBAAkB;gBACrB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE;oBACvD,MAAM,EAAE,IAAI;iBACb,CAAC;gBACJ,CAAC,CAAC,IAAI;YACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;SACpC,CAAC,CAAC;QACH,MAAM,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC/C,IAAI;aACD,IAAI,CAAC,GAAG,EAAE;YACT,mBAAmB,CAAC,OAAO,EAAE,CAAC;QAChC,CAAC,CAAC;aACD,KAAK,CAAC,GAAG,CAAC,EAAE;YACX,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QACL,OAAO,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,WAAyB;QAC1C,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,MAAM,IAAI,CAAC,kCAAkC,EAAE,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,gBAAwC;QAExC,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;YAC/C,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;YACpC,IAAA,kBAAM,EACJ,IAAA,kBAAQ,EAAC,KAAK,CAAC,EACf,6BAA6B,GAAG,wBAAwB,OAAO,KAAK,aAAa,CAClF,CAAC;YACF,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC;SACnD;QACD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACrD,OAAO,EAAE,IAAI,CAAC,iBAAiB;SAChC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB;QACd,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACnD,CAAC;IAED,qBAAqB;QACnB,OAAO,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,EAAE,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,KAAc;QACjC,IAAI,CAAC,0BAA0B,CAAC,OAAO,GAAG,KAAK,CAAC;QAChD,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,wBAAwB,CAC5B,iBAA2C;QAE3C,IAAI,CAAC,0BAA0B,CAAC,MAAM,GAAG,iBAAiB;YACxD,CAAC,CAAC,iBAAiB,CAAC,MAAM;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,IAAI,CAAC,0BAA0B,CAAC,QAAQ,GAAG,iBAAiB;YAC1D,CAAC,CAAC,iBAAiB,CAAC,QAAQ;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,IAAI,CAAC,0BAA0B,CAAC,OAAO,GAAG,iBAAiB;YACzD,CAAC,CAAC,iBAAiB,CAAC,OAAO;YAC3B,CAAC,CAAC,CAAC,CAAC;QAEN,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,wBAAwB;QAC5B,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kCAAkC,EAAE;YAC1D,OAAO,EAAE,IAAI,CAAC,0BAA0B,CAAC,OAAO;YAChD,OAAO,EAAE,IAAI,CAAC,0BAA0B,CAAC,OAAO;YAChD,gBAAgB,EAAE,IAAI,CAAC,0BAA0B,CAAC,MAAM;YACxD,kBAAkB,EAAE,IAAI,CAAC,0BAA0B,CAAC,QAAQ;SAC7D,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,SAAiB,EACjB,iBAAwD;QAExD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE;YACtD,SAAS,EAAE,SAAS;YACpB,iBAAiB,EAAE,iBAAiB;SACrC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAgB;QACpC,IAAI,CAAC,kBAAkB,GAAG,CAAC,OAAO,CAAC;QACnC,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,KAAc;QACzC,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC;QAC7C,MAAM,IAAI,CAAC,kCAAkC,EAAE,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,kCAAkC;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,+BAA+B,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;QAC5E,IAAI,OAAO,KAAK,IAAI,CAAC,mCAAmC,EAAE;YACxD,OAAO;SACR;QACD,IAAI,CAAC,mCAAmC,GAAG,OAAO,CAAC;QACnD,IAAI,OAAO,EAAE;YACX,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,IAAI,CAAC,4BAA4B,EAAE;gBACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;oBAChC,kBAAkB,EAAE,IAAI;oBACxB,QAAQ,EAAE,CAAC,EAAC,UAAU,EAAE,GAAG,EAAC,CAAC;iBAC9B,CAAC;aACH,CAAC,CAAC;SACJ;aAAM;YACL,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,IAAI,CAAC,4BAA4B,EAAE;gBACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;aACnC,CAAC,CAAC;SACJ;IACH,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,4BAA4B;QAChC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE;YAClD,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE;SACrC,CAAC,CAAC;IACL,CAAC;IAED,oBAAoB,CAAC,KAA8C;QACjE,0EAA0E;QAC1E,IACE,IAAI,CAAC,+BAA+B;YACpC,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EACtC;YACA,MAAM,EAAC,SAAS,EAAE,gBAAgB,EAAC,GAAG,KAAK,CAAC;YAE5C,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAE1E;;eAEG;YACH,MAAM,kBAAkB,GACtB,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAC/D,IAAI,kBAAkB,EAAE;gBACtB,MAAM,EAAC,SAAS,EAAE,cAAc,EAAC,GAAG,kBAAkB,CAAC;gBACvD,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;gBAC1D,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;gBACvC,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;aACjE;YAED,OAAO;SACR;QACD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,eAAe,CAAC,KAAuC;QACrD,IAAI,QAAQ,GAAqD,SAAS,CAAC;QAC3E,IAAI,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;YACvD,QAAQ,GAAG,YAAY,CAAC;SACzB;aAAM,IAAI,IAAI,CAAC,YAAY,EAAE;YAC5B,QAAQ,GAAG,oBAAoB,CAAC;YAChC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;SACrD;QACD,MAAM,EAAC,QAAQ,EAAE,QAAQ,EAAC,GAAG,IAAI,CAAC,YAAY,IAAI;YAChD,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB,CAAC;QACF,IAAI,CAAC,OAAO;aACT,IAAI,CAAC,wBAAwB,EAAE;YAC9B,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,qBAAqB,EAAE,EAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAC;SACtD,CAAC;aACD,KAAK,CAAC,oBAAU,CAAC,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACH,gBAAgB,CAAC,KAAwC;QACvD,IACE,CAAC,IAAI,CAAC,+BAA+B;YACrC,IAAI,CAAC,mCAAmC,EACxC;YACA,IAAI,CAAC,OAAO;iBACT,IAAI,CAAC,uBAAuB,EAAE;gBAC7B,SAAS,EAAE,KAAK,CAAC,SAAS;aAC3B,CAAC;iBACD,KAAK,CAAC,oBAAU,CAAC,CAAC;SACtB;QAED,MAAM,EAAC,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,cAAc,EAAC,GAAG,KAAK,CAAC;QAEvE,IAAI,CAAC,gBAAgB,EAAE;YACrB,IAAI,CAAC,uCAAuC,CAAC,KAAK,CAAC,CAAC;YACpD,OAAO;SACR;QAED,MAAM,sBAAsB,GAAG,CAAC,GAAG,EAAE;YACnC,MAAM,sBAAsB,GAC1B,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YAEnE,+CAA+C;YAC/C,IACE,sBAAsB;gBACtB,CAAC,sBAAsB,CAAC,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG;oBACvD,sBAAsB,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EACjE;gBACA,IAAI,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;gBACpE,OAAO;aACR;YACD,OAAO,sBAAsB,CAAC;QAChC,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,sBAAsB,EAAE;YAC1B,IAAI,CAAC,yBAAyB,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC9D,IAAI,CAAC,UAAU,CAAC,sBAAsB,EAAE,cAAc,CAAC,CAAC;SACzD;aAAM;YACL,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;SACvE;IACH,CAAC;IAED,yBAAyB,CACvB,sBAA+D,EAC/D,kBAAqD;QAErD,sBAAsB,CAAC,OAAO,CAAC,OAAO,GAAG;YACvC,GAAG,sBAAsB,CAAC,OAAO,CAAC,OAAO;YACzC,+CAA+C;YAC/C,GAAG,kBAAkB,CAAC,OAAO,CAAC,OAAO;SACtC,CAAC;IACJ,CAAC;IAED,uCAAuC,CACrC,KAAwC;QAExC,yEAAyE;QACzE,8DAA8D;QAC9D,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO;YACzB,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;YACzC,CAAC,CAAC,IAAI,CAAC;QAET,MAAM,OAAO,GAAG,IAAI,4BAAW,CAC7B,IAAI,CAAC,OAAO,EACZ,KAAK,EACL,KAAK,CAAC,SAAS,EACf,IAAI,CAAC,+BAA+B,EACpC,KAAK,EACL,EAAE,CACH,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,mCAA2B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxD,KAAK,OAAO,CAAC,qBAAqB,EAAE,CAAC;IACvC,CAAC;IAED,UAAU,CACR,KAA8C,EAC9C,cAA+B;QAE/B,IAAI,aAAa,GAAkB,EAAE,CAAC;QACtC,IAAI,KAAK,CAAC,gBAAgB,EAAE;YAC1B,yDAAyD;YACzD,wDAAwD;YACxD,mEAAmE;YACnE,oEAAoE;YACpE,qEAAqE;YACrE,sEAAsE;YACtE,4BAA4B;YAC5B,IAAI,yBAAyB,GAAG,IAAI,CAAC;YACrC,IAAI,KAAK,CAAC,oBAAoB,EAAE;gBAC9B,yBAAyB,GAAG,IAAI,CAAC,oBAAoB;qBAClD,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC;qBAClC,KAAK,EAAE,CAAC;gBACX,IAAI,CAAC,yBAAyB,EAAE;oBAC9B,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,EAAE;wBAC3D,KAAK;wBACL,cAAc;qBACf,CAAC,CAAC;oBACH,OAAO;iBACR;aACF;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACtE,6DAA6D;YAC7D,2BAA2B;YAC3B,IAAI,OAAO,EAAE;gBACX,IAAI,CAAC,sBAAsB,CACzB,OAAO,EACP,KAAK,CAAC,gBAAgB,EACtB,yBAAyB,CAC1B,CAAC;gBACF,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC;aACxC;SACF;QACD,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO;YACzB,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;YACzC,CAAC,CAAC,IAAI,CAAC;QAET,MAAM,OAAO,GAAG,IAAI,4BAAW,CAC7B,IAAI,CAAC,OAAO,EACZ,KAAK,EACL,cAAc,EACd,IAAI,CAAC,+BAA+B,EACpC,KAAK,EACL,aAAa,CACd,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACjE,IAAI,CAAC,IAAI,CAAC,mCAA2B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxD,KAAK,OAAO,CAAC,qBAAqB,EAAE,CAAC;IACvC,CAAC;IAED,yBAAyB,CACvB,KAAmD;QAEnD,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;SACjC;QACD,IAAI,CAAC,IAAI,CAAC,mCAA2B,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAED,sBAAsB,CACpB,OAAoB,EACpB,eAA0C,EAC1C,SAAiE;QAEjE,MAAM,QAAQ,GAAG,IAAI,8BAAY,CAC/B,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,eAAe,EACf,SAAS,CACV,CAAC;QACF,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC7B,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrC,QAAQ,CAAC,YAAY,CACnB,IAAI,KAAK,CAAC,qDAAqD,CAAC,CACjE,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,mCAA2B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC1D,IAAI,CAAC,IAAI,CAAC,mCAA2B,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC;IAED,kBAAkB,CAChB,gBAAwD,EACxD,SAAiE;QAEjE,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAClD,gBAAgB,CAAC,SAAS,CAC3B,CAAC;QACF,0DAA0D;QAC1D,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAC5D,gBAAgB,CAAC,SAAS,CAC3B,CAAC;QACF,IAAI,UAAU,CAAC,MAAM,EAAE;YACrB,IAAA,oBAAU,EACR,IAAI,KAAK,CACP,0CAA0C;gBACxC,gBAAgB,CAAC,SAAS,CAC7B,CACF,CAAC;SACH;QAED,yEAAyE;QACzE,6DAA6D;QAC7D,6BAA6B;QAC7B,IAAI,gBAAgB,CAAC,QAAQ,CAAC,aAAa,EAAE;YAC3C,SAAS,GAAG,IAAI,CAAC;SAClB;QAED,MAAM,QAAQ,GAAG,IAAI,8BAAY,CAC/B,IAAI,CAAC,OAAO,EACZ,OAAO,EACP,gBAAgB,CAAC,QAAQ,EACzB,SAAS,CACV,CAAC;QACF,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,mCAA2B,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAED,mBAAmB,CAAC,KAA6C;QAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,KAAK,CAAC,YAAY,EAAE;YAC9D,SAAS,GAAG,IAAI,CAAC,oBAAoB;iBAClC,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC;iBAClC,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,SAAS,EAAE;gBACd,uDAAuD;gBACvD,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,EAAE;oBACzD,qBAAqB,EAAE,KAAK;iBAC7B,CAAC,CAAC;gBACH,OAAO;aACR;SACF;QACD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAC5C,CAAC;IAED,4BAA4B,CAC1B,KAAsD;QAEtD,0EAA0E;QAC1E,uEAAuE;QACvE,WAAW;QACX,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CACnE,KAAK,CAAC,SAAS,CAChB,CAAC;QACF,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACzE,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC;YACjE,OAAO;SACR;QAED,yEAAyE;QACzE,0DAA0D;QAC1D,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAChE,KAAK,CAAC,SAAS,CAChB,CAAC;QACF,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAClE,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YACnE,IAAI,YAAY,CAAC,oBAAoB,EAAE;gBACrC,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;aAC9D;YACD,IAAI,YAAY,CAAC,kBAAkB,EAAE;gBACnC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;aAC1D;YACD,OAAO;SACR;QAED,qEAAqE;QACrE,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3E,CAAC;IAED,cAAc,CAAC,OAAoB,EAAE,MAAe;QAClD,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,MAAM,cAAc,GAAG,OAAO,CAAC,eAAe,CAAC;QAE/C,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACnD,cAAc,KAAK,SAAS;YAC1B,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAExD,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SAC7C;IACH,CAAC;IAED,kBAAkB,CAAC,KAA4C;QAC7D,+DAA+D;QAC/D,mEAAmE;QACnE,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAChE,KAAK,CAAC,SAAS,CAChB,CAAC;QACF,IAAI,YAAY,EAAE;YAChB,YAAY,CAAC,oBAAoB,GAAG,KAAK,CAAC;SAC3C;aAAM;YACL,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;SAClC;IACH,CAAC;IAED,oBAAoB,CAAC,KAA4C;QAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACtE,mEAAmE;QACnE,gCAAgC;QAChC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;QAED,qEAAqE;QACrE,qDAAqD;QACrD,IAAI,OAAO,CAAC,QAAQ,EAAE,EAAE;YACtB,OAAO,CAAC,QAAQ,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;SACxC;QACD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,mCAA2B,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC;IAED,gBAAgB,CAAC,KAA0C;QACzD,+DAA+D;QAC/D,mEAAmE;QACnE,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAChE,KAAK,CAAC,SAAS,CAChB,CAAC;QACF,IAAI,YAAY,EAAE;YAChB,YAAY,CAAC,kBAAkB,GAAG,KAAK,CAAC;SACzC;aAAM;YACL,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;SAChC;IACH,CAAC;IAED,kBAAkB,CAAC,KAA0C;QAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACtE,mEAAmE;QACnE,gCAAgC;QAChC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;QACD,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC;QACvC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QACpC,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,mCAA2B,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IAChE,CAAC;CACF;AAlkBD,wCAkkBC"}