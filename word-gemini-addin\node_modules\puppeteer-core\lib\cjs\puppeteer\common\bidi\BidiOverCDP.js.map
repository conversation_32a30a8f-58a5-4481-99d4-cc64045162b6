{"version": 3, "file": "BidiOverCDP.js", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/BidiOverCDP.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,2FAA6E;AAK7E,4CAA8C;AAG9C,mDAAiE;AAMjE;;GAEG;AACI,KAAK,UAAU,kBAAkB,CACtC,GAAsB;IAEtB,MAAM,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;IAC1C,MAAM,oBAAoB,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAC;IAC3D,MAAM,aAAa,GAAG;QACpB,IAAI,CAAC,OAAe;YAClB,4EAA4E;YAC5E,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,KAAK;YACH,UAAU,CAAC,KAAK,EAAE,CAAC;YACnB,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAC/B,CAAC;QACD,SAAS,CAAC,QAAgB;YACxB,8CAA8C;QAChD,CAAC;KACF,CAAC;IACF,aAAa,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,OAAe,EAAE,EAAE;QACnD,yDAAyD;QACzD,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IACH,MAAM,kBAAkB,GAAG,IAAI,0BAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,aAAa,CAAC,CAAC;IAC5E,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,cAAc,CAC3D,aAAa,EACb,oBAAoB,EACpB,EAAE,CACH,CAAC;IACF,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AA7BD,gDA6BC;AAED;;;GAGG;AACH,MAAM,oBAAoB;IACxB,IAAI,CAAoB;IACxB,SAAS,GAAG,IAAI,GAAG,EAA4C,CAAC;IAChE,QAAQ,CAAsC;IAE9C,YAAY,GAAsB;QAChC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,YAAY,CAAC,EAAU;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,6BAA6B,GAAG,EAAE,CAAC,CAAC;SACrD;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACrC,OAAO,OAAO,CAAC;SAChB;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;IACtC,CAAC;IAED,KAAK;QACH,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;YAC7C,OAAO,CAAC,KAAK,EAAE,CAAC;SACjB;IACH,CAAC;CACF;AAED;;;;;GAKG;AACH,MAAM,gBACJ,SAAQ,UAAU,CAAC,YAAuB;IAG1C,OAAO,GAAG,KAAK,CAAC;IAChB,OAAO,CAAI;IAEX,YAAY,MAAS;QACnB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,eAA+B,CAAC,CAAC;IAC7D,CAAC;IAED,eAAe,GAAG,CAChB,MAAS,EACT,KAAmB,EACnB,EAAE;QACF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,KAAK,CAAC,WAAW,CACf,MAAS,EACT,GAAG,MAAiD;QAEpD,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,OAAO;SACR;QACD,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC;SACnD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,OAAO;aACR;YACD,MAAM,GAAG,CAAC;SACX;IACH,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,eAA+B,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,YAAY,CAAC,KAAU;QACrB,OAAO,KAAK,YAAY,4BAAgB,CAAC;IAC3C,CAAC;CACF;AAED;;;;GAIG;AACH,MAAM,aACJ,SAAQ,UAAU,CAAC,YAAiB;IAGpC,UAAU,GAEkB,KAAK,EAC/B,EAAkC,EACnB,EAAE;QACjB,OAAO;IACT,CAAC,CAAC;IAEF,WAAW,CAAC,OAAuC;QACjD,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED,YAAY,CACV,SAA4E;QAE5E,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAqC;QACrD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,KAAK;QACH,IAAI,CAAC,UAAU,GAAG,KAAK,EACrB,EAAkC,EACnB,EAAE;YACjB,OAAO;QACT,CAAC,CAAC;IACJ,CAAC;CACF"}