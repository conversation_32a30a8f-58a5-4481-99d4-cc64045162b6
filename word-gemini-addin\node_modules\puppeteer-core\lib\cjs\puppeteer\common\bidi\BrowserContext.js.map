{"version": 3, "file": "BrowserContext.js", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/BrowserContext.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH,mEAAiF;AAEjF,wDAAgD;AAKhD,uCAA+B;AAC/B,yCAAsC;AAOtC;;GAEG;AACH,MAAa,cAAe,SAAQ,kCAAkB;IACpD,QAAQ,CAAU;IAClB,WAAW,CAAa;IACxB,gBAAgB,CAAkB;IAClC,MAAM,GAAG,IAAI,GAAG,EAAgB,CAAC;IACjC,uBAAuB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9D,KAAK,GAAG,sBAAQ,CAAC,MAAM,EAAQ,CAAC;IAChC,UAAU,GAAG,KAAK,CAAC;IAEnB,YAAY,OAAgB,EAAE,OAA8B;QAC1D,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,CAAC;QAChD,IAAI,CAAC,WAAW,CAAC,EAAE,CACjB,kCAAkC,EAClC,IAAI,CAAC,uBAAuB,CAC7B,CAAC;QACF,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,qBAAU,CAAC,CAAC;IACpC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACrB,OAAO;SACR;QACD,IAAI;YACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAC1C,yBAAyB,EACzB,EAAE,CACH,CAAC;YACF,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE;gBACrC,MAAM,IAAI,GAAG,IAAI,cAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;SACtB;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAY,CAAC,CAAC;SACjC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,KAA2D;QAE3D,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAChC,IAAA,qBAAU,EAAC,KAAK,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;QAEhC,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,wBAAwB,EAAE;YACrE,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;QACH,MAAM,IAAI,GAAG,IAAI,cAAI,CAAC,IAAI,EAAE;YAC1B,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,QAAQ,EAAE,EAAE;SACb,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI;gBACF,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAC/C;YAAC,MAAM;gBACN,yCAAyC;aAC1C;SACF;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEtC,OAAO,IAAI,CAAC;IACd,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACtD;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE;YACvC,MAAM,IAAI,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBAChC,IAAA,qBAAU,EAAC,KAAK,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;QAChC,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IACnC,CAAC;IAEQ,WAAW;QAClB,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA1GD,wCA0GC"}