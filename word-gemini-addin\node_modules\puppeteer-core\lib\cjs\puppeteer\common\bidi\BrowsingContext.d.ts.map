{"version": 3, "file": "BrowsingContext.d.ts", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/BrowsingContext.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,IAAI,MAAM,4CAA4C,CAAC;AACnE,OAAO,eAAe,MAAM,6CAA6C,CAAC;AAE1E,OAAO,EAAC,cAAc,EAAC,MAAM,mBAAmB,CAAC;AAGjD,OAAO,KAAK,EAAC,UAAU,EAAE,UAAU,IAAI,aAAa,EAAC,MAAM,kBAAkB,CAAC;AAE9E,OAAO,EAAC,YAAY,EAAC,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAC,uBAAuB,EAAC,MAAM,wBAAwB,CAAC;AAC/D,OAAO,EAAC,eAAe,EAAC,MAAM,uBAAuB,CAAC;AAGtD,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAC3C,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AAEjC;;GAEG;AACH,eAAO,MAAM,0BAA0B,sCAMrC,CAAC;AAaH;;GAEG;AACH,qBAAa,iBAAkB,SAAQ,YAAa,YAAW,UAAU;;gBAI3D,OAAO,EAAE,eAAe;IAepC,UAAU,IAAI,aAAa,GAAG,SAAS;IAGjC,IAAI,CAAC,CAAC,SAAS,MAAM,eAAe,CAAC,QAAQ,EACjD,MAAM,EAAE,CAAC,EACT,GAAG,SAAS,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GACtD,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;IAUrD,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;IAIvB,EAAE,IAAI,MAAM;CAIb;AAED;;GAEG;AACH,qBAAa,eAAgB,SAAQ,KAAK;;gBAOtC,UAAU,EAAE,UAAU,EACtB,eAAe,EAAE,eAAe,EAChC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;IAiBjC,kBAAkB,CAAC,OAAO,EAAE,MAAM,GAAG,KAAK;IAI1C,IAAI,GAAG,IAAI,MAAM,CAEhB;IAED,IAAI,EAAE,IAAI,MAAM,CAEf;IAED,IAAI,UAAU,IAAI,UAAU,CAE3B;IAED,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAItB,IAAI,CACR,GAAG,EAAE,MAAM,EACX,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAiCnB,MAAM,CAAC,OAAO,GAAE,cAAmB,GAAG,OAAO,CAAC,IAAI,CAAC;IAoBnD,UAAU,CACd,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KACjE,GACA,OAAO,CAAC,IAAI,CAAC;IAwBV,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAI1B,cAAc,CAAC,CAAC,SAAS,MAAM,eAAe,CAAC,QAAQ,EAC3D,MAAM,EAAE,CAAC,EACT,GAAG,SAAS,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GACtD,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;IAIrD,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC;IAMxB,OAAO,IAAI,IAAI;CAIhB;AAED;;GAEG;AACH,wBAAgB,kBAAkB,CAChC,KAAK,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,GACzD,OAAO,CAAC,uBAAuB,EAAE,MAAM,GAAG,kBAAkB,CAAC,CAoB/D"}