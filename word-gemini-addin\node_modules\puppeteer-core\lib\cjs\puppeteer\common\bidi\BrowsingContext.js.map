{"version": 3, "file": "BrowsingContext.js", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/BrowsingContext.ts"], "names": [], "mappings": ";;;AAIA,oDAA4C;AAC5C,wDAAgD;AAEhD,4CAAyD;AACzD,wDAAgD;AAGhD,wCAA2E;AAG3E,yCAAiC;AAEjC;;GAEG;AACU,QAAA,0BAA0B,GAAG,IAAI,GAAG,CAG/C;IACA,CAAC,MAAM,EAAE,sBAAsB,CAAC;IAChC,CAAC,kBAAkB,EAAE,kCAAkC,CAAC;CACzD,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,yBAAyB,GAAG,IAAI,GAAG,CAGvC;IACA,CAAC,MAAM,EAAE,UAAU,CAAC;IACpB,CAAC,kBAAkB,EAAE,aAAa,CAAC;CACpC,CAAC,CAAC;AAEH;;GAEG;AACH,MAAa,iBAAkB,SAAQ,8BAAY;IACjD,QAAQ,CAAkB;IAC1B,UAAU,GAAG,sBAAQ,CAAC,MAAM,EAAU,CAAC;IAEvC,YAAY,OAAwB;QAClC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,OAAO,CAAC,UAAU;aACf,IAAI,CAAC,gBAAgB,EAAE;YACtB,OAAO,EAAE,OAAO,CAAC,EAAE;SACpB,CAAC;aACD,IAAI,CAAC,OAAO,CAAC,EAAE;YACd,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,OAAQ,CAAC,CAAC;QACnD,CAAC,CAAC;aACD,KAAK,CAAC,GAAG,CAAC,EAAE;YACX,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;IAED,UAAU;QACR,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,KAAK,CAAC,IAAI,CACR,MAAS,EACT,GAAG,SAAoD;QAEvD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QACrD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACpE,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YACpB,OAAO;SACR,CAAC,CAAC;QACH,OAAO,MAAM,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,MAAM;QACJ,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAED,EAAE;QACA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACpC,OAAO,GAAG,YAAY,KAAK,IAAI,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IAC9D,CAAC;CACF;AA3CD,8CA2CC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,gBAAK;IACxC,gBAAgB,CAAkB;IAClC,GAAG,CAAS;IACZ,IAAI,CAAS;IACb,WAAW,CAAa;IAExB,YACE,UAAsB,EACtB,eAAgC,EAChC,IAA+B;QAE/B,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,EAAE,CACL,mCAAmC,EACnC,CAAC,IAAyC,EAAE,EAAE;YAC5C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;QACvB,CAAC,CACF,CAAC;IACJ,CAAC;IAED,kBAAkB,CAAC,OAAe;QAChC,OAAO,IAAI,gBAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,GAAG;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,SAAS,CAAC,GAAW;QACnB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,IAAI,CACR,GAAW,EACX,UAKI,EAAE;QAEN,MAAM,EACJ,SAAS,GAAG,MAAM,EAClB,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,GACpD,GAAG,OAAO,CAAC;QAEZ,MAAM,cAAc,GAAG,yBAAyB,CAAC,GAAG,CAClD,kBAAkB,CAAC,SAAS,CAAC,CACS,CAAC;QAEzC,IAAI;YACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAA,yBAAe,EACpC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,0BAA0B,EAAE;gBAC/C,GAAG,EAAE,GAAG;gBACR,OAAO,EAAE,IAAI,CAAC,GAAG;gBACjB,IAAI,EAAE,cAAc;aACrB,CAAC,EACF,YAAY,EACZ,OAAO,CACR,CAAC;YACF,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC;YAEvB,OAAO,MAAM,CAAC,UAAU,CAAC;SAC1B;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,yBAAa,EAAE;gBAClC,KAAK,CAAC,OAAO,IAAI,OAAO,GAAG,EAAE,CAAC;aAC/B;iBAAM,IAAI,KAAK,YAAY,wBAAY,EAAE;gBACxC,KAAK,CAAC,OAAO,GAAG,wBAAwB,GAAG,OAAO,GAAG,cAAc,CAAC;aACrE;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,UAA0B,EAAE;QACvC,MAAM,EACJ,SAAS,GAAG,MAAM,EAClB,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,GACpD,GAAG,OAAO,CAAC;QAEZ,MAAM,cAAc,GAAG,yBAAyB,CAAC,GAAG,CAClD,kBAAkB,CAAC,SAAS,CAAC,CACS,CAAC;QAEzC,MAAM,IAAA,yBAAe,EACnB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAC7C,OAAO,EAAE,IAAI,CAAC,GAAG;YACjB,IAAI,EAAE,cAAc;SACrB,CAAC,EACF,YAAY,EACZ,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU,CACd,IAAY,EACZ,OAGC;QAED,MAAM,EACJ,SAAS,GAAG,MAAM,EAClB,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,GACpD,GAAG,OAAO,CAAC;QAEZ,MAAM,cAAc,GAAG,kCAA0B,CAAC,GAAG,CACnD,kBAAkB,CAAC,SAAS,CAAC,CACpB,CAAC;QAEZ,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAA,wBAAc,EAAC,IAAI,EAAE,IAAI,CAAC;YAC1B,IAAA,yBAAe,EACb,IAAI,OAAO,CAAO,OAAO,CAAC,EAAE;gBAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE;oBAC7B,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,EACF,cAAc,EACd,OAAO,CACR;SACF,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,wBAAc,CAAC,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAAS,EACT,GAAG,SAAoD;QAEvD,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,CAAC;IACrD,CAAC;IAED,KAAK;QACH,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;YACxB,OAAO,QAAQ,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO;QACL,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvD,CAAC;CACF;AA9JD,0CA8JC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAChC,KAA0D;IAE1D,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;QAC5C,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;KAClE;IACD,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAC1C,CAAC,CAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACtB,OAAO,SAAS,KAAK,kBAAkB,IAAI,SAAS,KAAK,MAAM,CAAC;QAClE,CAAC,CAA6B;QAChC,CAAC,CAAC,KAAK,CAAC;IAEV,IACE,eAAe,KAAK,cAAc;QAClC,eAAe,KAAK,cAAc,EAClC;QACA,MAAM,IAAI,KAAK,CAAC,qCAAqC,eAAe,EAAE,CAAC,CAAC;KACzE;IAED,IAAA,kBAAM,EAAC,eAAe,EAAE,4BAA4B,eAAe,EAAE,CAAC,CAAC;IAEvE,OAAO,eAAe,CAAC;AACzB,CAAC;AAtBD,gDAsBC"}