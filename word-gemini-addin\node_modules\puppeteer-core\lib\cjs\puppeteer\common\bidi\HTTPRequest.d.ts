/**
 * Copyright 2020 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import * as Bidi from 'chromium-bidi/lib/cjs/protocol/protocol.js';
import { Frame } from '../../api/Frame.js';
import { HTTPRequest as BaseHTTPRequest, ResourceType } from '../../api/HTTPRequest.js';
import { HTTPResponse } from './HTTPResponse.js';
/**
 * @internal
 */
export declare class HTTPRequest extends BaseHTTPRequest {
    #private;
    _response: HTTPResponse | null;
    _redirectChain: HTTPRequest[];
    _navigationId: string | null;
    constructor(event: Bidi.Network.BeforeRequestSentParams, frame: Frame | null, redirectChain: HTTPRequest[]);
    url(): string;
    resourceType(): ResourceType;
    method(): string;
    postData(): string | undefined;
    headers(): Record<string, string>;
    response(): HTTPResponse | null;
    isNavigationRequest(): boolean;
    initiator(): Bidi.Network.Initiator;
    redirectChain(): HTTPRequest[];
    enqueueInterceptAction(pendingHandler: () => void | PromiseLike<unknown>): void;
    frame(): Frame | null;
}
//# sourceMappingURL=HTTPRequest.d.ts.map