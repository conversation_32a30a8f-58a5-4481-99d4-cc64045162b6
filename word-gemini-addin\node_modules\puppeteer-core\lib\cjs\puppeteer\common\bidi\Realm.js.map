{"version": 3, "file": "Realm.js", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/Realm.ts"], "names": [], "mappings": ";;;AAGA,wDAAyD;AACzD,wDAAgD;AAChD,4DAAoD;AAEpD,wCAIoB;AAGpB,yDAAiD;AAEjD,+CAAuC;AACvC,mDAA+C;AAC/C,yCAAiD;AAEpC,QAAA,gBAAgB,GAAG,6CAA6C,CAAC;AAEvE,MAAM,mBAAmB,GAAG,CAAC,GAAW,EAAU,EAAE;IACzD,OAAO,iBAAiB,GAAG,EAAE,CAAC;AAChC,CAAC,CAAC;AAFW,QAAA,mBAAmB,uBAE9B;AAEF,MAAa,KAAM,SAAQ,8BAAY;IACrC,UAAU,CAAa;IACvB,MAAM,CAAS;IACf,GAAG,CAAS;IACZ,QAAQ,CAAU;IAElB,YAAY,UAAsB,EAAE,EAAU,EAAE,OAAgB;QAC9D,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED,IAAI,MAAM;QACR,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,GAAG;YACjB,OAAO,EAAE,IAAI,CAAC,QAAQ;SACvB,CAAC;IACJ,CAAC;IAED,QAAQ,CAAC,KAAY;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAES,qBAAqB,CAAoC;IACnE,IAAI,aAAa;QACf,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,EAAsB,CAAC;QACtD,kCAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,qBAAqB,EAAE;gBAC9B,KAAK,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC5C,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;aACJ;YACD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC7C,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAqC,CAAC;YACzE,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC,qBAAyD,CAAC;IACxE,CAAC;IAED,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACrD,CAAC;IAkBD,KAAK,CAAC,SAAS,CAIb,aAAsB,EACtB,YAA2B,EAC3B,GAAG,IAAY;QAEf,MAAM,gBAAgB,GAAG,IAAA,2BAAmB,EAC1C,IAAA,0CAAgC,EAAC,YAAY,CAAC,EAAE,QAAQ,EAAE;YACxD,sBAAY,CAAC,YAAY,CAC5B,CAAC;QAEF,IAAI,eAAe,CAAC;QACpB,MAAM,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QACxD,IAAI,IAAA,kBAAQ,EAAC,YAAY,CAAC,EAAE;YAC1B,MAAM,UAAU,GAAG,wBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;gBACpD,CAAC,CAAC,YAAY;gBACd,CAAC,CAAC,GAAG,YAAY,KAAK,gBAAgB,IAAI,CAAC;YAE7C,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACxD,UAAU;gBACV,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,eAAe;gBACf,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,mBAAmB,GAAG,IAAA,+BAAiB,EAAC,YAAY,CAAC,CAAC;YAC1D,mBAAmB,GAAG,wBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC;gBAC9D,CAAC,CAAC,mBAAmB;gBACrB,CAAC,CAAC,GAAG,mBAAmB,KAAK,gBAAgB,IAAI,CAAC;YACpD,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC5D,mBAAmB;gBACnB,SAAS,EAAE,MAAM,OAAO,CAAC,GAAG,CAC1B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;oBACb,OAAO,8BAAc,CAAC,SAAS,CAAC,GAAG,EAAE,IAAW,CAAC,CAAC;gBACpD,CAAC,CAAC,CACH;gBACD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,eAAe;gBACf,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;SACJ;QAED,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,eAAe,CAAC;QAEvC,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE;YACnD,MAAM,IAAA,gCAAqB,EAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;SACtD;QAED,OAAO,aAAa;YAClB,CAAC,CAAC,8BAAc,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;YAC3C,CAAC,CAAC,aAAa,CAAC,IAAW,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7D,CAAC;CACF;AAlID,sBAkIC;AAED;;GAEG;AACH,SAAgB,aAAa,CAC3B,cAAqB,EACrB,MAAwC,EACxC,KAAY;IAEZ,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;QACtD,OAAO,IAAI,gCAAa,CAAC,cAAc,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;KACzD;IACD,OAAO,IAAI,sBAAQ,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;AAC9C,CAAC;AATD,sCASC"}