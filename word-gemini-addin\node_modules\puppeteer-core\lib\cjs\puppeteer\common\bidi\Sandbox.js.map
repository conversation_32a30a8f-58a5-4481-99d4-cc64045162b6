{"version": 3, "file": "Sandbox.js", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/Sandbox.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAMH,oDAA4C;AAS5C,wCAAwD;AACxD,gDAAqD;AAIrD;;;;;GAKG;AACU,QAAA,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAClD;;;;;GAKG;AACU,QAAA,iBAAiB,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAW5D;;GAEG;AACH,MAAa,OAAO;IAClB,SAAS,CAA2B;IACpC,MAAM,CAAQ;IAEd,gBAAgB,CAAkB;IAClC,YAAY,GAAG,IAAI,yBAAW,EAAE,CAAC;IAEjC,YAAY,OAAc,EAAE,eAAgC;QAC1D,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;QACtB,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;IAC1C,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;QACD,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE;YACrD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,CAAC,CACL,QAAkB;QAElB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,EAAE,CACN,QAAkB;QAElB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,KAAK,CAQT,QAAkB,EAClB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAC3E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,MAAM,CAQV,QAAkB,EAClB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAC5E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,EAAE,CAAC,UAAkB;QACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,OAAO,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EACzC,IAAI,CAAC,cAAc,CAAC,IAAI,EACxB,YAAY,CACb,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,YAAY,GAAG,IAAA,sCAA4B,EACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAClB,YAAY,CACb,CAAC;QACF,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,WAAW,CAA+B,MAAS;QACvD,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC;QACd,CAAC,EAAE,MAAM,CAAC,CAAiB,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,cAAc,CAA+B,MAAS;QAC1D,IAAK,MAA8B,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,MAAM,EAAE;YAC7D,OAAO,MAAM,CAAC;SACf;QACD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACzD,OAAO,IAAI,CAAC;QACd,CAAC,EAAE,MAAM,CAAC,CAAC;QAEX,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,iBAAiC,CAAC;IAC3C,CAAC;IAED,eAAe,CAMb,YAA2B,EAC3B,UAKI,EAAE,EACN,GAAG,IAAY;QAEf,MAAM,EACJ,OAAO,GAAG,KAAK,EACf,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EACzC,IAAI,EACJ,MAAM,GACP,GAAG,OAAO,CAAC;QACZ,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,EAAE;YAC9C,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QACD,MAAM,QAAQ,GAAG,IAAI,sBAAQ,CAC3B,IAAI,EACJ;YACE,OAAO;YACP,IAAI;YACJ,OAAO;YACP,MAAM;SACP,EACD,YAEU,EACV,GAAG,IAAI,CACR,CAAC;QACF,OAAO,QAAQ,CAAC,MAAM,CAAC;IACzB,CAAC;IAED,sBAAsB;IACtB,sBAAsB;IACtB,sBAAsB;IACtB,KAAK,CAAC,KAAK,CACT,QAAgB,EAChB,OAAgC;QAEhC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;QACrB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,QAAgB;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;QACrB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,QAAgB,EAAE,GAAG,MAAgB;QAChD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;QAC9C,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,QAAgB;QACxB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC;QACnB,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,IAAI,CACR,QAAgB,EAChB,IAAY,EACZ,OAAuC;QAEvC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,kBAAM,EAAC,MAAM,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjC,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;CACF;AAzND,0BAyNC"}