"use strict";
/**
 * Copyright 2022 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
__exportStar(require("./Accessibility.js"), exports);
__exportStar(require("./AriaQueryHandler.js"), exports);
__exportStar(require("./Browser.js"), exports);
__exportStar(require("./BrowserConnector.js"), exports);
__exportStar(require("./BrowserWebSocketTransport.js"), exports);
__exportStar(require("./ChromeTargetManager.js"), exports);
__exportStar(require("./Configuration.js"), exports);
__exportStar(require("./Connection.js"), exports);
__exportStar(require("./ConnectionTransport.js"), exports);
__exportStar(require("./ConsoleMessage.js"), exports);
__exportStar(require("./Coverage.js"), exports);
__exportStar(require("./CustomQueryHandler.js"), exports);
__exportStar(require("./Debug.js"), exports);
__exportStar(require("./Device.js"), exports);
__exportStar(require("./DeviceRequestPrompt.js"), exports);
__exportStar(require("./Dialog.js"), exports);
__exportStar(require("./ElementHandle.js"), exports);
__exportStar(require("./EmulationManager.js"), exports);
__exportStar(require("./Errors.js"), exports);
__exportStar(require("./EventEmitter.js"), exports);
__exportStar(require("./ExecutionContext.js"), exports);
__exportStar(require("./fetch.js"), exports);
__exportStar(require("./FileChooser.js"), exports);
__exportStar(require("./FirefoxTargetManager.js"), exports);
__exportStar(require("./FrameManager.js"), exports);
__exportStar(require("./FrameTree.js"), exports);
__exportStar(require("./Input.js"), exports);
__exportStar(require("./IsolatedWorld.js"), exports);
__exportStar(require("./IsolatedWorlds.js"), exports);
__exportStar(require("./JSHandle.js"), exports);
__exportStar(require("./LazyArg.js"), exports);
__exportStar(require("./LifecycleWatcher.js"), exports);
__exportStar(require("./NetworkEventManager.js"), exports);
__exportStar(require("./NetworkManager.js"), exports);
__exportStar(require("./NodeWebSocketTransport.js"), exports);
__exportStar(require("./Page.js"), exports);
__exportStar(require("./PDFOptions.js"), exports);
__exportStar(require("./PredefinedNetworkConditions.js"), exports);
__exportStar(require("./Product.js"), exports);
__exportStar(require("./Puppeteer.js"), exports);
__exportStar(require("./PuppeteerViewport.js"), exports);
__exportStar(require("./SecurityDetails.js"), exports);
__exportStar(require("./Target.js"), exports);
__exportStar(require("./TargetManager.js"), exports);
__exportStar(require("./TaskQueue.js"), exports);
__exportStar(require("./TimeoutSettings.js"), exports);
__exportStar(require("./Tracing.js"), exports);
__exportStar(require("./types.js"), exports);
__exportStar(require("./USKeyboardLayout.js"), exports);
__exportStar(require("./util.js"), exports);
__exportStar(require("./WaitTask.js"), exports);
__exportStar(require("./WebWorker.js"), exports);
__exportStar(require("./QueryHandler.js"), exports);
//# sourceMappingURL=common.js.map