{"version": 3, "file": "Poller.d.ts", "sourceRoot": "", "sources": ["../../../../src/injected/Poller.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH;;GAEG;AACH,MAAM,WAAW,MAAM,CAAC,CAAC;IACvB,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACtB,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;CACtB;AAED;;GAEG;AACH,qBAAa,cAAc,CAAC,CAAC,CAAE,YAAW,MAAM,CAAC,CAAC,CAAC;;gBAOrC,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI;IAKtC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAuBtB,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAW3B,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC;CAIrB;AAED;;GAEG;AACH,qBAAa,SAAS,CAAC,CAAC,CAAE,YAAW,MAAM,CAAC,CAAC,CAAC;;gBAGhC,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC;IAI1B,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAuBtB,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAO3B,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC;CAIrB;AAED;;GAEG;AAEH,qBAAa,cAAc,CAAC,CAAC,CAAE,YAAW,MAAM,CAAC,CAAC,CAAC;;gBAMrC,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM;IAKtC,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAkBtB,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAW3B,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC;CAIrB"}