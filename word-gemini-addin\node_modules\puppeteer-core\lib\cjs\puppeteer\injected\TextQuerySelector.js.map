{"version": 3, "file": "TextQuerySelector.js", "sourceRoot": "", "sources": ["../../../../src/injected/TextQuerySelector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,qDAG0B;AAE1B;;;;GAIG;AACI,MAAM,oBAAoB,GAAG,QAAQ,CAAC,EAC3C,IAAU,EACV,QAAgB;IAEhB,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;QAClC,IAAI,IAAI,YAAY,OAAO,IAAI,IAAA,8CAA6B,EAAC,IAAI,CAAC,EAAE;YAClE,IAAI,OAAoC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACpB,OAAO,GAAG,IAAA,4BAAoB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aAChD;iBAAM;gBACL,OAAO,GAAG,IAAA,4BAAoB,EAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;aAC3D;YACD,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;gBAC3B,MAAM,KAAK,CAAC;gBACZ,OAAO,GAAG,IAAI,CAAC;aAChB;SACF;KACF;IACD,IAAI,OAAO,EAAE;QACX,OAAO;KACR;IAED,IAAI,IAAI,YAAY,OAAO,IAAI,IAAA,8CAA6B,EAAC,IAAI,CAAC,EAAE;QAClE,MAAM,WAAW,GAAG,IAAA,kCAAiB,EAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACvC,MAAM,IAAI,CAAC;SACZ;KACF;AACH,CAAC,CAAC;AA7BW,QAAA,oBAAoB,wBA6B/B"}