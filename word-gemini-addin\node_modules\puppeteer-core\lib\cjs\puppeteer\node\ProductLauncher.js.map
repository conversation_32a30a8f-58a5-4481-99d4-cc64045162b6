{"version": 3, "file": "ProductLauncher.js", "sourceRoot": "", "sources": ["../../../../src/node/ProductLauncher.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;GAcG;AACH,2BAA8B;AAC9B,2BAA0B;AAC1B,+BAA0B;AAE1B,kDAO6B;AAG7B,qDAAgD;AAChD,2DAAmD;AACnD,mDAAiD;AACjD,mFAAiG;AAGjG,+CAA6C;AAO7C,yDAAiD;AAajD;;;;GAIG;AACH,MAAa,eAAe;IAC1B,QAAQ,CAAU;IAElB;;OAEG;IACH,SAAS,CAAgB;IAEzB;;OAEG;IACO,qBAAqB,CAAU;IAEzC;;OAEG;IACH,YAAY,SAAwB,EAAE,OAAgB;QACpD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,UAAsC,EAAE;QACnD,MAAM,EACJ,MAAM,GAAG,KAAK,EACd,GAAG,GAAG,OAAO,CAAC,GAAG,EACjB,YAAY,GAAG,IAAI,EACnB,aAAa,GAAG,IAAI,EACpB,YAAY,GAAG,IAAI,EACnB,iBAAiB,GAAG,KAAK,EACzB,eAAe,GAAG,EAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAC,EAC3C,MAAM,GAAG,CAAC,EACV,OAAO,GAAG,KAAK,EACf,kBAAkB,GAAG,IAAI,EACzB,QAAQ,EACR,eAAe,GAChB,GAAG,OAAO,CAAC;QAEZ,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAE9D,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;QAEpE,MAAM,aAAa,GAAG,KAAK,IAAI,EAAE;YAC/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,WAAW,EAAE;gBAClD,MAAM,EAAE,UAAU,CAAC,iBAAiB;aACrC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,IAAA,iBAAM,EAAC;YAC5B,cAAc,EAAE,UAAU,CAAC,cAAc;YACzC,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,YAAY;YACZ,aAAa;YACb,YAAY;YACZ,MAAM;YACN,GAAG;YACH,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,aAAa;SACtB,CAAC,CAAC;QAEH,IAAI,OAAgB,CAAC;QACrB,IAAI,UAAsB,CAAC;QAC3B,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,MAAM,oBAAoB,GAAG,KAAK,IAAI,EAAE;YACtC,IAAI,OAAO,EAAE;gBACX,OAAO;aACR;YACD,OAAO,GAAG,IAAI,CAAC;YACf,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC,CAAC;QAEF,IAAI;YACF,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,eAAe,EAAE;gBAC/D,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CACpC,cAAc,EACd,oBAAoB,EACpB;oBACE,OAAO;oBACP,eAAe;oBACf,MAAM;oBACN,eAAe;oBACf,iBAAiB;iBAClB,CACF,CAAC;aACH;iBAAM;gBACL,IAAI,OAAO,EAAE;oBACX,UAAU,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE;wBAC9D,OAAO;wBACP,eAAe;wBACf,MAAM;qBACP,CAAC,CAAC;iBACJ;qBAAM;oBACL,UAAU,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE;wBAChE,OAAO;wBACP,eAAe;wBACf,MAAM;qBACP,CAAC,CAAC;iBACJ;gBACD,IAAI,QAAQ,KAAK,eAAe,EAAE;oBAChC,OAAO,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAC3C,cAAc,EACd,UAAU,EACV,oBAAoB,EACpB;wBACE,OAAO;wBACP,eAAe;wBACf,MAAM;wBACN,eAAe;wBACf,iBAAiB;qBAClB,CACF,CAAC;iBACH;qBAAM;oBACL,OAAO,GAAG,MAAM,uBAAU,CAAC,OAAO,CAChC,IAAI,CAAC,OAAO,EACZ,UAAU,EACV,EAAE,EACF,iBAAiB,EACjB,eAAe,EACf,cAAc,CAAC,WAAW,EAC1B,oBAAoB,EACpB,OAAO,CAAC,YAAY,CACrB,CAAC;iBACH;aACF;SACF;QAAC,OAAO,KAAK,EAAE;YACd,KAAK,oBAAoB,EAAE,CAAC;YAC5B,IAAI,KAAK,YAAY,uBAAoB,EAAE;gBACzC,MAAM,IAAI,wBAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;aACvC;YACD,MAAM,KAAK,CAAC;SACb;QAED,IAAI,kBAAkB,IAAI,QAAQ,KAAK,eAAe,EAAE;YACtD,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;SAChD;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAGD,cAAc;QACZ,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAGD,WAAW;QACT,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,wBAAwB;QACtB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAQS,KAAK,CAAC,sBAAsB;QACpC,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IASS,KAAK,CAAC,gBAAgB;QAC9B,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,YAAY,CAC1B,cAAyC,EACzC,UAAuB;QAEvB,IAAI,UAAU,EAAE;YACd,0CAA0C;YAC1C,IAAI;gBACF,MAAM,UAAU,CAAC,YAAY,EAAE,CAAC;gBAChC,MAAM,cAAc,CAAC,SAAS,EAAE,CAAC;aAClC;YAAC,OAAO,KAAK,EAAE;gBACd,IAAA,oBAAU,EAAC,KAAK,CAAC,CAAC;gBAClB,MAAM,cAAc,CAAC,KAAK,EAAE,CAAC;aAC9B;SACF;aAAM;YACL,MAAM,cAAc,CAAC,KAAK,EAAE,CAAC;SAC9B;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,iBAAiB,CAC/B,OAAgB,EAChB,OAAe;QAEf,IAAI;YACF,MAAM,OAAO,CAAC,aAAa,CACzB,CAAC,CAAC,EAAE;gBACF,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC;YAC7B,CAAC,EACD,EAAC,OAAO,EAAC,CACV,CAAC;SACH;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,yBAAyB,CACvC,cAAyC,EACzC,IAA4E;QAE5E,MAAM,iBAAiB,GAAG,MAAM,cAAc,CAAC,iBAAiB,CAC9D,uCAA4B,EAC5B,IAAI,CAAC,OAAO,CACb,CAAC;QACF,MAAM,SAAS,GAAG,MAAM,kDAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrE,OAAO,IAAI,0BAAU,CACnB,iBAAiB,EACjB,SAAS,EACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,CACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CACrC,cAAyC,EACzC,IAA4E;QAE5E,0EAA0E;QAC1E,mCAAmC;QACnC,MAAM,EAAC,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAC,GAAG,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC;QACrE,MAAM,SAAS,GAAG,IAAI,gCAAa,CACjC,SAAkC,EAClC,QAAiC,CAClC,CAAC;QACF,OAAO,IAAI,0BAAU,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB,CACtC,cAAyC,EACzC,UAAsB,EACtB,aAAmC,EACnC,IAMC;QAED,+BAA+B;QAC/B,MAAM,IAAI,GAAG;QACX,yBAAyB,CAAC,wBAAwB,GACnD,CAAC;QACF,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QACjE,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,UAAU,EAAE,cAAc;YAC1B,aAAa;YACb,OAAO,EAAE,cAAc,CAAC,WAAW;YACnC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;SAC1C,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,iBAAiB,CAC/B,cAAyC,EACzC,aAAmC,EACnC,IAMC;QAED,MAAM,iBAAiB,GACrB,CAAC,MAAM,cAAc,CAAC,iBAAiB,CACrC,kDAAuC,EACvC,IAAI,CAAC,OAAO,CACb,CAAC,GAAG,UAAU,CAAC;QAClB,MAAM,SAAS,GAAG,MAAM,kDAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrE,MAAM,IAAI,GAAG;QACX,yBAAyB,CAAC,wBAAwB,GACnD,CAAC;QACF,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,UAAU,CACxC,iBAAiB,EACjB,SAAS,EACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,eAAe,CACrB,CAAC;QACF,+BAA+B;QAC/B,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YAC/B,UAAU,EAAE,cAAc;YAC1B,aAAa;YACb,OAAO,EAAE,cAAc,CAAC,WAAW;YACnC,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;SAC1C,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,cAAc;QACtB,OAAO,IAAA,WAAI,EACT,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,kBAAkB,IAAI,IAAA,WAAM,GAAE,EAC3D,iBAAiB,IAAI,CAAC,OAAO,WAAW,CACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,qBAAqB;QAC7B,IAAI,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,CAAC;QACjE,IAAI,cAAc,EAAE;YAClB,IAAI,CAAC,IAAA,eAAU,EAAC,cAAc,CAAC,EAAE;gBAC/B,MAAM,IAAI,KAAK,CACb,qDAAqD,cAAc,iCAAiC,CACrG,CAAC;aACH;YACD,OAAO,cAAc,CAAC;SACvB;QAED,SAAS,gBAAgB,CAAC,OAAiB;YACzC,QAAQ,OAAO,EAAE;gBACf,KAAK,QAAQ;oBACX,OAAO,kBAAgB,CAAC,MAAM,CAAC;gBACjC,KAAK,SAAS;oBACZ,OAAO,kBAAgB,CAAC,OAAO,CAAC;aACnC;YACD,OAAO,kBAAgB,CAAC,MAAM,CAAC;QACjC,CAAC;QAED,cAAc,GAAG,IAAA,gCAAqB,EAAC;YACrC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,mBAAoB;YAC7C,OAAO,EAAE,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC;YACvC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe;SACxC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAA,eAAU,EAAC,cAAc,CAAC,EAAE;YAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,eAAe,EAAE;gBAChD,MAAM,IAAI,KAAK,CACb,qDAAqD,cAAc,kBAAkB,IAAI,CAAC,SAAS,CAAC,eAAe,gCAAgC,CACpJ,CAAC;aACH;YACD,QAAQ,IAAI,CAAC,OAAO,EAAE;gBACpB,KAAK,QAAQ;oBACX,MAAM,IAAI,KAAK,CACb,+BAA+B,IAAI,CAAC,SAAS,CAAC,eAAe,+BAA+B;wBAC1F,6FAA6F;wBAC7F,4DAA4D,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,MAAM;wBAC7G,iGAAiG,CACpG,CAAC;gBACJ,KAAK,SAAS;oBACZ,MAAM,IAAI,KAAK,CACb,gCAAgC,IAAI,CAAC,SAAS,CAAC,eAAe,+BAA+B;wBAC3F,mIAAmI;wBACnI,4DAA4D,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,MAAM;wBAC7G,iGAAiG,CACpG,CAAC;aACL;SACF;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;CACF;AA1YD,0CA0YC"}