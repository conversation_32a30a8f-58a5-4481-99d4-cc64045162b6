"use strict";
/**
 * @internal
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isErrnoException = exports.isErrorLike = void 0;
/**
 * @internal
 */
function isErrorLike(obj) {
    return (typeof obj === 'object' && obj !== null && 'name' in obj && 'message' in obj);
}
exports.isErrorLike = isErrorLike;
/**
 * @internal
 */
function isErrnoException(obj) {
    return (isErrorLike(obj) &&
        ('errno' in obj || 'code' in obj || 'path' in obj || 'syscall' in obj));
}
exports.isErrnoException = isErrnoException;
//# sourceMappingURL=ErrorLike.js.map