{"version": 3, "file": "Target.js", "sourceRoot": "", "sources": ["../../../../src/common/Target.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAOH,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAG7C,OAAO,EAAC,OAAO,EAAC,MAAM,WAAW,CAAC;AAIlC,OAAO,EAAC,UAAU,EAAC,MAAM,WAAW,CAAC;AACrC,OAAO,EAAC,SAAS,EAAC,MAAM,gBAAgB,CAAC;AAEzC;;GAEG;AACH,MAAM,CAAN,IAAY,oBAGX;AAHD,WAAY,oBAAoB;IAC9B,2CAAmB,CAAA;IACnB,2CAAmB,CAAA;AACrB,CAAC,EAHW,oBAAoB,KAApB,oBAAoB,QAG/B;AAED;;;;;;;GAOG;AACH,MAAM,OAAO,MAAM;IACjB,eAAe,CAAiB;IAChC,QAAQ,CAAc;IACtB,WAAW,CAA6B;IACxC,cAAc,CAAgB;IAC9B,eAAe,CAAyD;IAExE;;OAEG;IACH,oBAAoB,GAAG,QAAQ,CAAC,MAAM,EAAwB,CAAC;IAC/D;;OAEG;IACH,iBAAiB,GAAG,QAAQ,CAAC,MAAM,EAAQ,CAAC;IAC5C;;OAEG;IACH,SAAS,CAAS;IAElB;;OAEG;IACH,YACE,UAAsC,EACtC,OAA+B,EAC/B,cAA8B,EAC9B,aAA4B,EAC5B,cAAsE;QAEtE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACO,eAAe;QAGvB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM;QACV,OAAO,IAAI,CAAC;IACd,CAAC;IAED,GAAG;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;IAC9B,CAAC;IAED;;;;;;OAMG;IACH,IAAI;QAQF,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACnC,IACE,IAAI,KAAK,MAAM;YACf,IAAI,KAAK,iBAAiB;YAC1B,IAAI,KAAK,gBAAgB;YACzB,IAAI,KAAK,eAAe;YACxB,IAAI,KAAK,SAAS;YAClB,IAAI,KAAK,SAAS,EAClB;YACA,OAAO,IAAI,CAAC;SACb;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,MAAM,EAAC,QAAQ,EAAC,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO;SACR;QACD,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAsC;QACvD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACO,WAAW;QACnB,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACO,mBAAmB;QAC3B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,EAAE;YACzC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;SACjE;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,IAAI;QACR,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,MAAM;IACpC,gBAAgB,CAAY;IAClB,WAAW,CAAiB;IACtC,oBAAoB,CAAY;IAChC,kBAAkB,CAAU;IAE5B;;OAEG;IACH,YACE,UAAsC,EACtC,OAA+B,EAC/B,cAA8B,EAC9B,aAA4B,EAC5B,cAAsE,EACtE,iBAA0B,EAC1B,eAAgC,EAChC,mBAA8B;QAE9B,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAC1E,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,eAAe,IAAI,SAAS,CAAC;QACrD,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;IAClD,CAAC;IAEkB,WAAW;QAC5B,IAAI,CAAC,oBAAoB;aACtB,YAAY,EAAE;aACd,IAAI,CAAC,KAAK,EAAC,MAAM,EAAC,EAAE;YACnB,IAAI,MAAM,KAAK,oBAAoB,CAAC,OAAO,EAAE;gBAC3C,OAAO;aACR;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC7B,IAAI,CAAC,CAAC,MAAM,YAAY,UAAU,CAAC,EAAE;gBACnC,OAAO;aACR;YACD,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE;gBAC5D,OAAO,IAAI,CAAC;aACb;YACD,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC;YAC5C,IAAI,CAAC,UAAU,CAAC,aAAa,uCAAyB,EAAE;gBACtD,OAAO,IAAI,CAAC;aACb;YACD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YACpC,UAAU,CAAC,IAAI,wCAA0B,SAAS,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,KAAK,CAAC,UAAU,CAAC,CAAC;QACrB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEQ,KAAK,CAAC,IAAI;QACjB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChC,IAAI,CAAC,WAAW,GAAG,CACjB,OAAO;gBACL,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC1B,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAC7D,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACd,OAAO,OAAO,CAAC,OAAO,CACpB,MAAM,EACN,IAAI,EACJ,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAC7B,IAAI,CAAC,oBAAoB,CAC1B,CAAC;YACJ,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;IAC1C,CAAC;IAEQ,mBAAmB;QAC1B,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,EAAE;YACxC,OAAO;SACR;QACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE;YACpC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;SACjE;IACH,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,YAAa,SAAQ,MAAM;IACtC,cAAc,CAAsB;IAE3B,KAAK,CAAC,MAAM;QACnB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChC,yDAAyD;YACzD,IAAI,CAAC,cAAc,GAAG,CACpB,OAAO;gBACL,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC1B,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAC7D,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACd,OAAO,IAAI,SAAS,CAClB,MAAM,EACN,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,EACzB,GAAG,EAAE,GAAE,CAAC,CAAC,sBAAsB,EAC/B,GAAG,EAAE,GAAE,CAAC,CAAC,qBAAqB,CAC/B,CAAC;YACJ,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,MAAM;CAAG"}