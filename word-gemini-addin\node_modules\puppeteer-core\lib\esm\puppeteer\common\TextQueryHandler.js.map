{"version": 3, "file": "TextQueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/TextQueryHandler.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAC,YAAY,EAAmB,MAAM,mBAAmB,CAAC;AAEjE;;GAEG;AACH,MAAM,OAAO,gBAAiB,SAAQ,YAAY;IAChD,MAAM,CAAU,gBAAgB,GAAqB,CACnD,OAAO,EACP,QAAQ,EACR,EAAC,oBAAoB,EAAC,EACtB,EAAE;QACF,OAAO,oBAAoB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACjD,CAAC,CAAC"}