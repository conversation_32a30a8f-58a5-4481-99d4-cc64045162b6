{"version": 3, "file": "Serializer.js", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/Serializer.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAIH,OAAO,EAAC,OAAO,EAAC,MAAM,eAAe,CAAC;AACtC,OAAO,EAAC,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAC,MAAM,YAAY,CAAC;AAGvE,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAC,QAAQ,EAAC,MAAM,eAAe,CAAC;AAEvC;;GAEG;AACH,MAAM,mBAAoB,SAAQ,KAAK;CAAG;AAE1C;;GAEG;AACH,MAAM,OAAO,cAAc;IACzB,MAAM,CAAC,eAAe,CAAC,GAAW;QAChC,IAAI,KAAkD,CAAC;QACvD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE;YACtB,KAAK,GAAG,IAAI,CAAC;SACd;aAAM,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;YACnC,KAAK,GAAG,UAAU,CAAC;SACpB;aAAM,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE;YACpC,KAAK,GAAG,WAAW,CAAC;SACrB;aAAM,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;YAC9B,KAAK,GAAG,KAAK,CAAC;SACf;aAAM;YACL,KAAK,GAAG,GAAG,CAAC;SACb;QACD,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,KAAK;SACN,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,GAAkB;QACvC,IAAI,GAAG,KAAK,IAAI,EAAE;YAChB,OAAO;gBACL,IAAI,EAAE,MAAM;aACb,CAAC;SACH;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7B,MAAM,WAAW,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACnC,OAAO,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,WAAW;aACnB,CAAC;SACH;aAAM,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE;YAC7B,IAAI;gBACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;aACrB;YAAC,OAAO,KAAK,EAAE;gBACd,IACE,KAAK,YAAY,SAAS;oBAC1B,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,uCAAuC,CAAC,EACjE;oBACA,KAAK,CAAC,OAAO,IAAI,qCAAqC,CAAC;iBACxD;gBACD,MAAM,KAAK,CAAC;aACb;YAED,MAAM,YAAY,GAA2C,EAAE,CAAC;YAChE,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;gBACrB,YAAY,CAAC,IAAI,CAAC;oBAChB,cAAc,CAAC,oBAAoB,CAAC,GAAG,CAAC;oBACxC,cAAc,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;iBAC9C,CAAC,CAAC;aACJ;YAED,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,YAAY;aACpB,CAAC;SACH;aAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACxB,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,GAAG,CAAC,MAAM;oBACnB,KAAK,EAAE,GAAG,CAAC,KAAK;iBACjB;aACF,CAAC;SACH;aAAM,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE;YACtB,OAAO;gBACL,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,GAAG,CAAC,WAAW,EAAE;aACzB,CAAC;SACH;QAED,MAAM,IAAI,mBAAmB,CAC3B,sEAAsE,CACvE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,GAAY;QACtC,QAAQ,OAAO,GAAG,EAAE;YAClB,KAAK,QAAQ,CAAC;YACd,KAAK,UAAU;gBACb,MAAM,IAAI,mBAAmB,CAAC,0BAA0B,OAAO,GAAG,EAAE,CAAC,CAAC;YACxE,KAAK,QAAQ;gBACX,OAAO,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAE7C,KAAK,WAAW;gBACd,OAAO;oBACL,IAAI,EAAE,WAAW;iBAClB,CAAC;YACJ,KAAK,QAAQ;gBACX,OAAO,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC7C,KAAK,QAAQ;gBACX,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,GAAG,CAAC,QAAQ,EAAE;iBACtB,CAAC;YACJ,KAAK,QAAQ;gBACX,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,KAAK,EAAE,GAAG;iBACX,CAAC;YACJ,KAAK,SAAS;gBACZ,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,GAAG;iBACX,CAAC;SACL;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,SAAS,CACpB,GAAY,EACZ,OAAwB;QAIxB,IAAI,GAAG,YAAY,OAAO,EAAE;YAC1B,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SAC9B;QACD,MAAM,YAAY,GAChB,GAAG,IAAI,CAAC,GAAG,YAAY,QAAQ,IAAI,GAAG,YAAY,aAAa,CAAC;YAC9D,CAAC,CAAC,GAAG;YACL,CAAC,CAAC,IAAI,CAAC;QACX,IAAI,YAAY,EAAE;YAChB,IACE,YAAY,CAAC,OAAO,EAAE,KAAK,OAAO;gBAClC,CAAC,CAAC,UAAU,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC,EAC3C;gBACA,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;aACH;YACD,IAAI,YAAY,CAAC,QAAQ,EAAE;gBACzB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;aAC1C;YACD,OAAO,YAAY,CAAC,WAAW,EAAE,CAAC;SACnC;QAED,OAAO,cAAc,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;IAClD,CAAC;IAED,MAAM,CAAC,iBAAiB,CACtB,KAAkD;QAElD,QAAQ,KAAK,EAAE;YACb,KAAK,IAAI;gBACP,OAAO,CAAC,CAAC,CAAC;YACZ,KAAK,KAAK;gBACR,OAAO,GAAG,CAAC;YACb,KAAK,UAAU;gBACb,OAAO,QAAQ,CAAC;YAClB,KAAK,WAAW;gBACd,OAAO,CAAC,QAAQ,CAAC;YACnB;gBACE,OAAO,KAAK,CAAC;SAChB;IACH,CAAC;IAED,MAAM,CAAC,qBAAqB,CAC1B,MAAwC;QAExC,QAAQ,MAAM,CAAC,IAAI,EAAE;YACnB,KAAK,OAAO;gBACV,sDAAsD;gBACtD,OAAO,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE;oBAC/B,OAAO,cAAc,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBACrD,CAAC,CAAC,CAAC;YACL,KAAK,KAAK;gBACR,sDAAsD;gBACtD,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAiB,EAAE,KAAK,EAAE,EAAE;oBACtD,OAAO,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC9D,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;YAChB,KAAK,QAAQ;gBACX,IAAI,MAAM,CAAC,KAAK,EAAE;oBAChB,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAyB,EAAE,KAAK,EAAE,EAAE;wBAC9D,MAAM,EAAC,GAAG,EAAE,KAAK,EAAC,GAAG,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBAC5D,GAAG,CAAC,GAAU,CAAC,GAAG,KAAK,CAAC;wBACxB,OAAO,GAAG,CAAC;oBACb,CAAC,EAAE,EAAE,CAAC,CAAC;iBACR;gBACD,MAAM;YAER,KAAK,KAAK;gBACR,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAA0B,EAAE,KAAK,EAAE,EAAE;oBAC/D,MAAM,EAAC,GAAG,EAAE,KAAK,EAAC,GAAG,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;oBAC5D,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC7B,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;YAChB,KAAK,SAAS;gBACZ,OAAO,EAAE,CAAC;YACZ,KAAK,QAAQ;gBACX,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9D,KAAK,MAAM;gBACT,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEhC,KAAK,WAAW;gBACd,OAAO,SAAS,CAAC;YACnB,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC;YACd,KAAK,QAAQ;gBACX,OAAO,cAAc,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACxD,KAAK,QAAQ;gBACX,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,KAAK,SAAS;gBACZ,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC/B,KAAK,QAAQ;gBACX,OAAO,MAAM,CAAC,KAAK,CAAC;SACvB;QAED,MAAM,IAAI,mBAAmB,CAC3B,2BAA2B,MAAM,CAAC,IAAI,iBAAiB,CACxD,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,CAAC,aAAa,EAAE,eAAe,CAGtD;QACC,MAAM,GAAG,GACP,OAAO,aAAa,KAAK,QAAQ;YAC/B,CAAC,CAAC,aAAa;YACf,CAAC,CAAC,cAAc,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;QAC1D,MAAM,KAAK,GAAG,cAAc,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;QAEpE,OAAO,EAAC,GAAG,EAAE,KAAK,EAAC,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,MAAwC;QACzD,IAAI,CAAC,MAAM,EAAE;YACX,UAAU,CAAC,mCAAmC,CAAC,CAAC;YAChD,OAAO,SAAS,CAAC;SAClB;QAED,IAAI;YACF,OAAO,cAAc,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;SACrD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,mBAAmB,EAAE;gBACxC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC1B,OAAO,SAAS,CAAC;aAClB;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;CACF"}