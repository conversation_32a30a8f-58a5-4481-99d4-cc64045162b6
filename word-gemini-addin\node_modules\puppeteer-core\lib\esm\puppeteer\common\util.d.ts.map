{"version": 3, "file": "util.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/util.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;AAEH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,QAAQ,CAAC;AAErC,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,KAAK,EAAC,aAAa,EAAC,MAAM,yBAAyB,CAAC;AAC3D,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAC,IAAI,EAAC,MAAM,gBAAgB,CAAC;AAGpC,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAG7C,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAGhD,OAAO,KAAK,EAAC,kBAAkB,EAAC,MAAM,mBAAmB,CAAC;AAC1D,OAAO,KAAK,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AAG5D;;GAEG;AACH,eAAO,MAAM,UAAU,8BAA2B,CAAC;AAEnD;;GAEG;AACH,wBAAgB,qBAAqB,CACnC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,gBAAgB,GACzC,OAAO,CAsDT;AAED;;GAEG;AACH,wBAAgB,iBAAiB,CAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,gBAAgB,GACzC,OAAO,CAsCT;AA0BD;;GAEG;AACH,qBAAa,YAAY;;IACvB,MAAM,CAAC,YAAY,SAAmB;IAEtC,MAAM,CAAC,YAAY,CACjB,YAAY,EAAE,MAAM,EACpB,IAAI,EAAE,MAAM,CAAC,QAAQ,GACpB,YAAY;IAOf,MAAM,CAAC,KAAK,QAAS,MAAM,KAAG,YAAY,CAOxC;IAEF,MAAM,CAAC,cAAc,QAAS,MAAM,KAAG,OAAO,CAE5C;IAKF,IAAI,YAAY,IAAI,MAAM,CAEzB;IAED,IAAI,UAAU,IAAI,MAAM,CAEvB;IAED,QAAQ,IAAI,MAAM;CAMnB;AAED;;GAEG;AACH,eAAO,MAAM,4BAA4B,+BACzB,MAAM,iBAkBrB,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,gCAAgC,+BAI1C,YAAY,GAAG,SAKjB,CAAC;AAEF;;GAEG;AACH,wBAAgB,qBAAqB,CACnC,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,GAC1C,GAAG,CAuBL;AAED;;GAEG;AACH,wBAAsB,aAAa,CACjC,MAAM,EAAE,UAAU,EAClB,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,GAC1C,OAAO,CAAC,IAAI,CAAC,CAWf;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC,OAAO,EAAE,kBAAkB,CAAC;IAC5B,SAAS,EAAE,MAAM,GAAG,MAAM,CAAC;IAC3B,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;CACnC;AAED;;GAEG;AACH,wBAAgB,gBAAgB,CAC9B,OAAO,EAAE,kBAAkB,EAC3B,SAAS,EAAE,MAAM,GAAG,MAAM,EAC1B,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAChC,sBAAsB,CAGxB;AAED;;GAEG;AACH,wBAAgB,oBAAoB,CAClC,SAAS,EAAE,KAAK,CAAC;IACf,OAAO,EAAE,kBAAkB,CAAC;IAC5B,SAAS,EAAE,MAAM,GAAG,MAAM,CAAC;IAC3B,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;CACnC,CAAC,GACD,IAAI,CAKN;AAED;;GAEG;AACH,eAAO,MAAM,QAAQ,QAAS,OAAO,kBAEpC,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,QAAQ,QAAS,OAAO,kBAEpC,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,aAAa,QAAS,OAAO,gCAEzC,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,QAAQ,QAAS,OAAO,kBAEpC,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,MAAM,QAAS,OAAO,gBAElC,CAAC;AAEF;;GAEG;AACH,wBAAsB,YAAY,CAAC,CAAC,EAClC,OAAO,EAAE,kBAAkB,EAC3B,SAAS,EAAE,MAAM,GAAG,MAAM,EAC1B,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,EACnD,OAAO,EAAE,MAAM,EACf,YAAY,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAC7C,OAAO,CAAC,CAAC,CAAC,CAuBZ;AAED;;GAEG;AACH,wBAAgB,cAAc,CAC5B,OAAO,EAAE,gBAAgB,EACzB,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,GAC1C,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,CAKhC;AAED;;GAEG;AACH,wBAAgB,gBAAgB,CAC9B,GAAG,EAAE,QAAQ,GAAG,MAAM,EACtB,GAAG,IAAI,EAAE,OAAO,EAAE,GACjB,MAAM,CAcR;AAED;;GAEG;AACH,wBAAgB,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,CA4C/D;AAED;;GAEG;AACH,wBAAgB,qBAAqB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,CAExE;AAED;;GAEG;AACH,wBAAsB,eAAe,CAAC,CAAC,EACrC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EACnB,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,CAAC,CAAC,CAOZ;AAMD;;GAEG;AACH,wBAAsB,gBAAgB,IAAI,OAAO,CAC/C,cAAc,aAAa,CAAC,CAC7B,CAcA;AAED;;GAEG;AACH,wBAAsB,mBAAmB,CACvC,QAAQ,EAAE,QAAQ,EAClB,IAAI,CAAC,EAAE,MAAM,GACZ,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAuBxB;AAED;;GAEG;AACH,wBAAsB,6BAA6B,CACjD,MAAM,EAAE,UAAU,EAClB,MAAM,EAAE,MAAM,GACb,OAAO,CAAC,QAAQ,CAAC,CAiCnB;AAED;;GAEG;AACH,wBAAsB,cAAc,CAClC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,EAC5B,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,IAAI,CAAC,CAQf;AAED;;GAEG;AACH,wBAAgB,cAAc,IAAI,MAAM,CAcvC"}