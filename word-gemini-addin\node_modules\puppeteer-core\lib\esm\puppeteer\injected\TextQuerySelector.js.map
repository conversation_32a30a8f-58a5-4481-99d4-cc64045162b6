{"version": 3, "file": "TextQuerySelector.js", "sourceRoot": "", "sources": ["../../../../src/injected/TextQuerySelector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,iBAAiB,EACjB,6BAA6B,GAC9B,MAAM,kBAAkB,CAAC;AAE1B;;;;GAIG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,QAAQ,CAAC,EAC3C,IAAU,EACV,QAAgB;IAEhB,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;QAClC,IAAI,IAAI,YAAY,OAAO,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE;YAClE,IAAI,OAAoC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACpB,OAAO,GAAG,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aAChD;iBAAM;gBACL,OAAO,GAAG,oBAAoB,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;aAC3D;YACD,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;gBAC3B,MAAM,KAAK,CAAC;gBACZ,OAAO,GAAG,IAAI,CAAC;aAChB;SACF;KACF;IACD,IAAI,OAAO,EAAE;QACX,OAAO;KACR;IAED,IAAI,IAAI,YAAY,OAAO,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE;QAClE,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACvC,MAAM,IAAI,CAAC;SACZ;KACF;AACH,CAAC,CAAC"}