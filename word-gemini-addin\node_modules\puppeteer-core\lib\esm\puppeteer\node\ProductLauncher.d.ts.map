{"version": 3, "file": "ProductLauncher.d.ts", "sourceRoot": "", "sources": ["../../../../src/node/ProductLauncher.ts"], "names": [], "mappings": "AAmBA,OAAO,EAGL,MAAM,EAIP,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EAAC,OAAO,EAAE,oBAAoB,EAAC,MAAM,mBAAmB,CAAC;AAEhE,OAAO,EAAC,UAAU,EAAC,MAAM,yBAAyB,CAAC;AAGnD,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAC7C,OAAO,EAAC,QAAQ,EAAC,MAAM,gCAAgC,CAAC;AAGxD,OAAO,EACL,4BAA4B,EAC5B,oBAAoB,EACpB,0BAA0B,EAC3B,MAAM,oBAAoB,CAAC;AAE5B,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAEjD;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,iBAAiB,EAAE,OAAO,CAAC;IAC3B,WAAW,EAAE,MAAM,CAAC;IACpB,cAAc,EAAE,MAAM,CAAC;IACvB,IAAI,EAAE,MAAM,EAAE,CAAC;CAChB;AAED;;;;GAIG;AACH,qBAAa,eAAe;;IAG1B;;OAEG;IACH,SAAS,EAAE,aAAa,CAAC;IAEzB;;OAEG;IACH,SAAS,CAAC,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAEzC;;OAEG;gBACS,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO;IAKtD,IAAI,OAAO,IAAI,OAAO,CAErB;IAEK,MAAM,CAAC,OAAO,GAAE,0BAA+B,GAAG,OAAO,CAAC,OAAO,CAAC;IAsHxE,cAAc,CAAC,OAAO,CAAC,EAAE,oBAAoB,GAAG,MAAM;IAKtD,WAAW,CAAC,MAAM,EAAE,4BAA4B,GAAG,MAAM,EAAE;IAK3D;;;;OAIG;IACH,wBAAwB,IAAI,MAAM,GAAG,SAAS;IAI9C;;OAEG;cACa,sBAAsB,CACpC,OAAO,EAAE,0BAA0B,GAClC,OAAO,CAAC,kBAAkB,CAAC;IAK9B;;OAEG;cACa,gBAAgB,CAC9B,IAAI,EAAE,MAAM,EACZ,IAAI,EAAE;QAAC,MAAM,EAAE,OAAO,CAAA;KAAC,GACtB,OAAO,CAAC,IAAI,CAAC;IAKhB;;OAEG;cACa,YAAY,CAC1B,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,UAAU,CAAC,EAAE,UAAU,GACtB,OAAO,CAAC,IAAI,CAAC;IAehB;;OAEG;cACa,iBAAiB,CAC/B,OAAO,EAAE,OAAO,EAChB,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,IAAI,CAAC;IAchB;;OAEG;cACa,yBAAyB,CACvC,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,IAAI,EAAE;QAAC,OAAO,EAAE,MAAM,CAAC;QAAC,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAC,GAC3E,OAAO,CAAC,UAAU,CAAC;IActB;;OAEG;cACa,uBAAuB,CACrC,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,IAAI,EAAE;QAAC,OAAO,EAAE,MAAM,CAAC;QAAC,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAC,GAC3E,OAAO,CAAC,UAAU,CAAC;IAWtB;;OAEG;cACa,wBAAwB,CACtC,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,UAAU,EAAE,UAAU,EACtB,aAAa,EAAE,oBAAoB,EACnC,IAAI,EAAE;QACJ,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QACpC,MAAM,EAAE,MAAM,CAAC;QACf,eAAe,EAAE,QAAQ,GAAG,IAAI,CAAC;QACjC,iBAAiB,CAAC,EAAE,OAAO,CAAC;KAC7B,GACA,OAAO,CAAC,OAAO,CAAC;IAenB;;OAEG;cACa,iBAAiB,CAC/B,cAAc,EAAE,UAAU,CAAC,OAAO,MAAM,CAAC,EACzC,aAAa,EAAE,oBAAoB,EACnC,IAAI,EAAE;QACJ,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,EAAE,MAAM,GAAG,SAAS,CAAC;QACpC,MAAM,EAAE,MAAM,CAAC;QACf,eAAe,EAAE,QAAQ,GAAG,IAAI,CAAC;QACjC,iBAAiB,CAAC,EAAE,OAAO,CAAC;KAC7B,GACA,OAAO,CAAC,OAAO,CAAC;IA0BnB;;OAEG;IACH,SAAS,CAAC,cAAc,IAAI,MAAM;IAOlC;;OAEG;IACH,SAAS,CAAC,qBAAqB,IAAI,MAAM;CAoD1C"}