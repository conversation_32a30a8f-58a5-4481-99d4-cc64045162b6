const set = require('regenerate')(0xA9, 0xAE, 0x203C, 0x2049, 0x2122, 0x2139, 0x2328, 0x2388, 0x23CF, 0x24C2, 0x25B6, 0x25C0, 0x2714, 0x2716, 0x271D, 0x2721, 0x2728, 0x2744, 0x2747, 0x274C, 0x274E, 0x2757, 0x27A1, 0x27B0, 0x27BF, 0x2B50, 0x2B55, 0x3030, 0x303D, 0x3297, 0x3299, 0x1F12F, 0x1F18E, 0x1F21A, 0x1F22F);
set.addRange(0x2194, 0x2199).addRange(0x21A9, 0x21AA).addRange(0x231A, 0x231B).addRange(0x23E9, 0x23F3).addRange(0x23F8, 0x23FA).addRange(0x25AA, 0x25AB).addRange(0x25FB, 0x25FE).addRange(0x2600, 0x2605).addRange(0x2607, 0x2612).addRange(0x2614, 0x2685).addRange(0x2690, 0x2705).addRange(0x2708, 0x2712).addRange(0x2733, 0x2734).addRange(0x2753, 0x2755).addRange(0x2763, 0x2767).addRange(0x2795, 0x2797).addRange(0x2934, 0x2935).addRange(0x2B05, 0x2B07).addRange(0x2B1B, 0x2B1C).addRange(0x1F000, 0x1F0FF).addRange(0x1F10D, 0x1F10F).addRange(0x1F16C, 0x1F171).addRange(0x1F17E, 0x1F17F).addRange(0x1F191, 0x1F19A).addRange(0x1F1AD, 0x1F1E5).addRange(0x1F201, 0x1F20F).addRange(0x1F232, 0x1F23A).addRange(0x1F23C, 0x1F23F).addRange(0x1F249, 0x1F3FA).addRange(0x1F400, 0x1F53D).addRange(0x1F546, 0x1F64F).addRange(0x1F680, 0x1F6FF).addRange(0x1F774, 0x1F77F).addRange(0x1F7D5, 0x1F7FF).addRange(0x1F80C, 0x1F80F).addRange(0x1F848, 0x1F84F).addRange(0x1F85A, 0x1F85F).addRange(0x1F888, 0x1F88F).addRange(0x1F8AE, 0x1F8FF).addRange(0x1F90C, 0x1F93A).addRange(0x1F93C, 0x1F945).addRange(0x1F947, 0x1FAFF).addRange(0x1FC00, 0x1FFFD);
exports.characters = set;
