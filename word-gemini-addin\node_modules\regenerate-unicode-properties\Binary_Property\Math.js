const set = require('regenerate')(0x2B, 0x5E, 0x7C, 0x7E, 0xAC, 0xB1, 0xD7, 0xF7, 0x3D5, 0x2016, 0x2040, 0x2044, 0x2052, 0x20E1, 0x2102, 0x2107, 0x2115, 0x2124, 0x214B, 0x21DD, 0x237C, 0x23B7, 0x23D0, 0x25E2, 0x25E4, 0x2640, 0x2642, 0xFB29, 0xFE68, 0xFF0B, 0xFF3C, 0xFF3E, 0xFF5C, 0xFF5E, 0xFFE2, 0x1D4A2, 0x1D4BB, 0x1D546, 0x1EE24, 0x1EE27, 0x1EE39, 0x1EE3B, 0x1EE42, 0x1EE47, 0x1EE49, 0x1EE4B, 0x1EE54, 0x1EE57, 0x1EE59, 0x1EE5B, 0x1EE5D, 0x1EE5F, 0x1EE64, 0x1EE7E);
set.addRange(0x3C, 0x3E).addRange(0x3D0, 0x3D2).addRange(0x3F0, 0x3F1).addRange(0x3F4, 0x3F6).addRange(0x606, 0x608).addRange(0x2032, 0x2034).addRange(0x2061, 0x2064).addRange(0x207A, 0x207E).addRange(0x208A, 0x208E).addRange(0x20D0, 0x20DC).addRange(0x20E5, 0x20E6).addRange(0x20EB, 0x20EF).addRange(0x210A, 0x2113).addRange(0x2118, 0x211D).addRange(0x2128, 0x2129).addRange(0x212C, 0x212D).addRange(0x212F, 0x2131).addRange(0x2133, 0x2138).addRange(0x213C, 0x2149).addRange(0x2190, 0x21A7).addRange(0x21A9, 0x21AE).addRange(0x21B0, 0x21B1).addRange(0x21B6, 0x21B7).addRange(0x21BC, 0x21DB).addRange(0x21E4, 0x21E5).addRange(0x21F4, 0x22FF).addRange(0x2308, 0x230B).addRange(0x2320, 0x2321).addRange(0x239B, 0x23B5).addRange(0x23DC, 0x23E2).addRange(0x25A0, 0x25A1).addRange(0x25AE, 0x25B7).addRange(0x25BC, 0x25C1).addRange(0x25C6, 0x25C7).addRange(0x25CA, 0x25CB).addRange(0x25CF, 0x25D3).addRange(0x25E7, 0x25EC).addRange(0x25F8, 0x25FF).addRange(0x2605, 0x2606).addRange(0x2660, 0x2663).addRange(0x266D, 0x266F).addRange(0x27C0, 0x27FF).addRange(0x2900, 0x2AFF).addRange(0x2B30, 0x2B44).addRange(0x2B47, 0x2B4C).addRange(0xFE61, 0xFE66).addRange(0xFF1C, 0xFF1E).addRange(0xFFE9, 0xFFEC).addRange(0x10D8E, 0x10D8F).addRange(0x1D400, 0x1D454).addRange(0x1D456, 0x1D49C);
set.addRange(0x1D49E, 0x1D49F).addRange(0x1D4A5, 0x1D4A6).addRange(0x1D4A9, 0x1D4AC).addRange(0x1D4AE, 0x1D4B9).addRange(0x1D4BD, 0x1D4C3).addRange(0x1D4C5, 0x1D505).addRange(0x1D507, 0x1D50A).addRange(0x1D50D, 0x1D514).addRange(0x1D516, 0x1D51C).addRange(0x1D51E, 0x1D539).addRange(0x1D53B, 0x1D53E).addRange(0x1D540, 0x1D544).addRange(0x1D54A, 0x1D550).addRange(0x1D552, 0x1D6A5).addRange(0x1D6A8, 0x1D7CB).addRange(0x1D7CE, 0x1D7FF).addRange(0x1EE00, 0x1EE03).addRange(0x1EE05, 0x1EE1F).addRange(0x1EE21, 0x1EE22).addRange(0x1EE29, 0x1EE32).addRange(0x1EE34, 0x1EE37).addRange(0x1EE4D, 0x1EE4F).addRange(0x1EE51, 0x1EE52).addRange(0x1EE61, 0x1EE62).addRange(0x1EE67, 0x1EE6A).addRange(0x1EE6C, 0x1EE72).addRange(0x1EE74, 0x1EE77).addRange(0x1EE79, 0x1EE7C).addRange(0x1EE80, 0x1EE89).addRange(0x1EE8B, 0x1EE9B).addRange(0x1EEA1, 0x1EEA3).addRange(0x1EEA5, 0x1EEA9).addRange(0x1EEAB, 0x1EEBB).addRange(0x1EEF0, 0x1EEF1);
exports.characters = set;
