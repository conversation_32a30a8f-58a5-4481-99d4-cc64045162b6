{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sDAAgC;AAChC,4DAAqC;AACrC,8DAAuC;AACvC,oDAA8B;AAoT5B,iBApTK,mBAAM,CAoTL;AAnTR,iCAKiB;AAwSf,wBA5SA,qBAAa,CA4SA;AAvSf,iCAAsE;AAEtE,SAAS,KAAK,CACZ,kBAAmD;IACnD,wBAAkC;SAAlC,UAAkC,EAAlC,qBAAkC,EAAlC,IAAkC;QAAlC,uCAAkC;;IAElC,OAAO,kBAAkB,CAAgB,EAAE,CAAC,8BAC1C,kBAAkB,UACf,cAAc,IACjB;AACJ,CAAC;AA8RC,sBAAK;AAEI,2BAAO;AA9RlB,SAAS,kBAAkB,CACzB,OAA0B;IAE1B,OAAO,SAAS,gBAAgB,CAC9B,kBAAmD;QACnD,wBAAkC;aAAlC,UAAkC,EAAlC,qBAAkC,EAAlC,IAAkC;YAAlC,uCAAkC;;QAElC,IAAI,mBAAW,CAAC,kBAAkB,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,mBAAW,CAAC,EAAE;YACvE,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;SAC3D;QAED,aAAa;QACb,IAAI,kBAAkB,CAAC,IAAI,EAAE;YAC3B,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;SACnD;QAED,0BAA0B;QAC1B,IAAI,CAAC,kBAAkB,EAAE;YACvB,OAAO,EAAmB,CAAC;SAC5B;QAED,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;gBACrC,cAAc;gBACd,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;oBACnC,OAAO,EAAmB,CAAC;iBAC5B;gBAED,IAAI,kBAAkB,CAAC,IAAI,CAAC,mBAAW,CAAC,EAAE;oBACxC,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;iBAC3D;gBAED,aAAa;gBACb,IAAI,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;oBAC9B,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;iBACnD;gBAED,OAAO,uBAAS,CACd,kBAAkB,EAClB,wBAAU,CAAC,OAAO,CAAC,CACH,CAAC;aACpB;YAED,OAAO,kBAAkB,CAAC;SAC3B;QAED,OAAO,uBAAS,CACd,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,EAC3C,wBAAU,CAAC,OAAO,CAAC,CACH,CAAC;IACrB,CAAC,CAAC;AACJ,CAAC;AA4OC,gDAAkB;AA1OpB,SAAS,cAAc,CAAC,KAEvB;IACC,OAAO,UAAC,CAAM,EAAE,CAAM,EAAE,GAAQ;QAC9B,IAAM,WAAW,GACf,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAC,IAAI,IAAK,OAAA,qBAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,EAAnB,CAAmB,CAAC,IAAI,EAAE,CAAC;QAE/D,IAAI,WAAW,EAAE;YACf,QAAQ,KAAK,CAAC,WAAW,CAAC,EAAE;gBAC1B,KAAK,qBAAa,CAAC,OAAO;oBACxB,8CAAW,CAAC,WAAK,CAAC,GAAE;gBACtB,KAAK,qBAAa,CAAC,OAAO;oBACxB,OAAO,CAAC,CAAC;gBACX,KAAK,qBAAa,CAAC,MAAM,CAAC;gBAC1B;oBACE,8CAAW,CAAC,WAAK,CAAC,GAAE;aACvB;SACF;IACH,CAAC,CAAC;AACJ,CAAC;AAiNC,wCAAc;AA7MhB,SAAS,cAAc,CAAC,KAAY;IAClC,OAAO,kBAAkB,CAAC;QACxB,cAAc,EAAE,UAAC,CAAM,EAAE,CAAM,EAAE,GAAQ;YACvC,IAAI,WAAW,GAAgD,KAAK,CAAC;YAErE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAC,CAAC;gBACvB,IAAI,CAAC,WAAW,EAAE;oBAChB,OAAO;iBACR;gBAED,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,IAAI,qBAAa,CAAC,WAAW,CAAC,EAAE;gBAC9B,OAAO,aAAa,CAAC,EAAE,WAAW,aAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,CAAC;aAC7C;YAED,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;gBACnC,OAAO,mBAAmB,CAAC,EAAE,WAAW,aAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,CAAC;aACnD;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AA4LC,wCAAc;AA1LhB,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAE9B,SAAS,aAAa,CAAC,EAQtB;QAPC,WAAW,iBAAA,EACX,CAAC,OAAA,EACD,CAAC,OAAA;IAMD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;QACf,OAAO,CAAC,CAAC;KACV;IAED,IAAM,WAAW,GAAU,EAAE,CAAC;IAC9B,IAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,UAAC,EAAE;QACnB,IAAI,CAAC,qBAAa,CAAC,WAAW,CAAC,EAAE;YAC/B,OAAO,EAAE,CAAC;SACX;QAED,IAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAM,YAAY,GAAa,EAAE,CAAC;QAClC,IAAM,UAAU,GAAG,EAAE,CAAC;QACtB,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAC,EAAM;gBAAN,KAAA,aAAM,EAAL,CAAC,QAAA,EAAE,CAAC,QAAA;YACxC,IAAI,CAAC,KAAK,qBAAa,CAAC,KAAK,EAAE;gBAC7B,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACtB;iBAAM;gBACL,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACnB;QACH,CAAC,CAAC,CAAC;QAEH,IAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC;YAC1B,IAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAChC,UAAC,IAAI,IAAK,OAAA,uBAAe,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAlC,CAAkC,CAC7C,CAAC;YAEF,IAAI,OAAO,EAAE;gBACX,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACrB;YAED,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,qBAAa,CAAC,EAAE,CAAC,EAAE;YACtB,OAAO,EAAE,CAAC;SACX;QAED,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAC,EAAM;gBAAN,KAAA,aAAM,EAAL,CAAC,QAAA,EAAE,CAAC,QAAA;YAC/B,IAAM,IAAI,GAAG,WAAW,CAAC;YAEzB,QAAQ,WAAW,CAAC,CAAC,CAAC,EAAE;gBACtB,KAAK,qBAAa,CAAC,KAAK;oBACtB,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAEX,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAC,EAAM;4BAAN,KAAA,aAAM,EAAL,CAAC,QAAA,EAAE,CAAC,QAAA;wBACjC,IAAI,CAAC,KAAK,qBAAa,CAAC,OAAO,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;4BACtD,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;4BAE9B,IAAI,OAAO,GAAG,KAAK,WAAW,EAAE;gCAC9B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;6BACd;yBACF;oBACH,CAAC,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,qBAAa,CAAC,MAAM;oBACvB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;wBACpB,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBAEX,MAAM;qBACP;oBAED,IAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEtC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;wBACxC,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAC;qBACpD;oBAED,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;oBAC/B,MAAM;gBACR,KAAK,qBAAa,CAAC,KAAK;oBACtB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;wBACpB,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBAEX,MAAM;qBACP;oBAED,IAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEpC,IAAI,CAAC,qBAAa,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAa,CAAC,SAAS,CAAC,EAAE;wBAClD,MAAM,IAAI,SAAS,CAAC,6BAA6B,CAAC,CAAC;qBACpD;oBAED,aAAa;oBACb,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;oBAC7B,MAAM;gBACR,KAAK,qBAAa,CAAC,OAAO;oBACxB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;wBACpB,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBAEX,MAAM;qBACP;oBAED,IAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEvC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;wBACzC,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC;qBACrD;oBAED,GAAG,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAChC,MAAM;gBACR,KAAK,qBAAa,CAAC,OAAO;oBACxB,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrD,MAAM;gBACR;oBACE,IAAM,aAAW,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;oBAElC,qCAAqC;oBACrC,IAAM,GAAC,GAAG,QAAQ;yBACf,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,CAAC,EAAJ,CAAI,CAAC;yBAChB,MAAM,CACL,UAAC,GAAG,EAAE,GAAG;wBACP,OAAA,OAAO,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,wCAAK,GAAG,WAAK,GAAG,GAAE,CAAC,CAAC,GAAG;oBAArD,CAAqD,EACvD,EAAE,CACH,CAAC;oBAEJ,GAAG,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,EAAE,WAAW,eAAA,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAA,EAAE,CAAC,CAAC;oBACjD,MAAM;aACT;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAxB,CAAwB,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,mBAAmB,CAAC,EAQ5B;QAPC,WAAW,iBAAA,EACX,CAAC,OAAA,EACD,CAAC,OAAA;IAMD,4BAA4B;IAC5B,QAAQ,WAAW,EAAE;QACnB,KAAK,qBAAa,CAAC,MAAM;YACvB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,KAAK,qBAAa,CAAC,OAAO;YACxB,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACrB,KAAK,qBAAa,CAAC,OAAO;YACxB,OAAO,CAAC,CAAC;KACZ;IAED,OAAO,CAAC,CAAC;AACX,CAAC;AAED,SAAS,IAAI,CAAC,GAAG;IACf,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,eAAe,CAAC,KAExB;IACC,OAAO,UAAC,CAAM,EAAE,CAAM,EAAE,GAAQ;QAC9B,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE;YAClB,KAAK,qBAAa,CAAC,OAAO;gBACxB,OAAO,uBAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,wBAAU,EAAE,CAAC,CAAC;YACzC,KAAK,qBAAa,CAAC,OAAO;gBACxB,OAAO,CAAC,CAAC;YACX,KAAK,qBAAa,CAAC,MAAM;gBACvB,OAAO,uBAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,wBAAU,EAAE,CAAC,CAAC;SAC1C;IACH,CAAC,CAAC;AACJ,CAAC;AAIC,0CAAe"}