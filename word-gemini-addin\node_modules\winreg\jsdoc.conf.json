{"tags": {"allowUnknownTags": false}, "opts": {"template": "./node_modules/ink-docstrap/template", "encoding": "utf8", "destination": "./docs/", "recurse": true, "private": true, "lenient": true, "verbose": true, "nocolor": true}, "source": {"include": ["./lib", "./README.md"], "includePattern": ".+\\.js(doc)?$"}, "plugins": ["plugins/markdown"], "templates": {"syntaxTheme": "dark", "cleverLinks": true, "monospaceLinks": true, "default": {"outputSourceFiles": true}, "systemName": "node-winreg", "footer": "<style>#forkongithub {display:none;}#forkongithub a{background:#000;color:#fff;text-decoration:none;font-family:arial, sans-serif;text-align:center;font-weight:bold;padding:5px 40px;font-size:1rem;line-height:2rem;position:relative;transition:0.5s;}#forkongithub a:hover{background:#33B5E5;color:#fff;}#forkongithub a::before,#forkongithub a::after{content:\"\";width:100%;display:block;position:absolute;top:1px;left:0;height:1px;background:#fff;}#forkongithub a::after{bottom:1px;top:auto;}@media screen and (min-width:800px){#forkongithub{z-index:10000;position:fixed;display:block;top:0;right:0;width:200px;overflow:hidden;height:200px;}#forkongithub a{width:200px;z-index:10000;position:fixed;top:25px;right:-60px;transform:rotate(45deg);-webkit-transform:rotate(45deg);box-shadow:4px 4px 10px rgba(0,0,0,0.8);}}</style><span id=\"forkongithub\"><a href=\"https://github.com/fresc81/node-winreg\">Fork me on GitHub</a></span>", "copyright": "Copyright &copy; 2023 <PERSON>.", "navType": "vertical", "linenums": true, "theme": "cyborg"}, "markdown": {"parser": "gfm", "hardwrap": true, "githubRepoName": "node-winreg", "githubRepoOwner": "fresc81"}}