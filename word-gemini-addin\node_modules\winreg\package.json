{"name": "winreg", "version": "1.2.5", "description": "provides access to the windows registry through the REG tool", "main": "lib/registry.js", "scripts": {"test": "mocha test", "generate-docs": "jsdoc -c ./jsdoc.conf.json", "download-docs": "git clone -b gh-pages https://github.com/fresc81/node-winreg.git docs", "checkout-docs": "git clone -b gh-pages **************:fresc81/node-winreg.git docs"}, "homepage": "http://fresc81.github.io/node-winreg", "repository": {"type": "git", "url": "git://github.com/fresc81/node-winreg.git"}, "keywords": ["windows", "registry"], "author": "<PERSON>", "license": "BSD-2-<PERSON><PERSON>", "readmeFilename": "README.md", "gitHead": "36cfe63a8dcb700cf44b0b7317827099d19c26c1", "devDependencies": {"ink-docstrap": "^1.3.2", "jsdoc": "^4.0.2", "mocha": "^10.2.0", "moment": "^2.11.2", "taffydb": "^2.7.3", "unit.js": "^2.0.0"}}