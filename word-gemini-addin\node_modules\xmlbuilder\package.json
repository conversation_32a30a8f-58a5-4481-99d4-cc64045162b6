{"name": "xmlbuilder", "version": "11.0.1", "keywords": ["xml", "xmlbuilder"], "homepage": "http://github.com/oozcitak/xmlbuilder-js", "description": "An XML builder for node.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/oozcitak/xmlbuilder-js.git"}, "bugs": {"url": "http://github.com/oozcitak/xmlbuilder-js/issues"}, "main": "./lib/index", "typings": "./typings/index.d.ts", "engines": {"node": ">=4.0"}, "dependencies": {}, "devDependencies": {"coffeescript": "1.*", "mocha": "*", "coffee-coverage": "2.*", "istanbul": "*", "coveralls": "*", "xpath": "*"}, "scripts": {"prepublishOnly": "coffee -co lib src", "postpublish": "rm -rf lib", "test": "mocha \"test/**/*.coffee\" && istanbul report text lcov"}}