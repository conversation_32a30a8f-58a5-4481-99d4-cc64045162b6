{"name": "zip-a-folder", "version": "0.0.12", "description": "Forked the idea of @sole to zip a complete folder into a zip file but now using promises", "main": "lib/zip-a-folder.js", "typings": "zip-a-folder.d.ts", "directories": {"lib": "lib", "test": "test"}, "scripts": {"test": "jest --coverage && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js"}, "repository": {"type": "git", "url": "git+https://github.com/maugenst/zip-a-folder.git"}, "keywords": ["zip", "folder", "async", "promise"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/maugenst/zip-a-folder/issues"}, "homepage": "https://github.com/maugenst/zip-a-folder#readme", "devDependencies": {"coveralls": "^3.0.9", "eslint-plugin-jest": "^22.21.0", "jest": "^25.1.0", "lodash": "^4.17.15", "prettier": "^1.19.1"}, "dependencies": {"archiver": "^3.1.1"}}