{"name": "zip-stream", "version": "2.1.3", "description": "a streaming zip archive generator.", "homepage": "https://github.com/archiverjs/node-zip-stream", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/node-zip-stream.git"}, "bugs": {"url": "https://github.com/archiverjs/node-zip-stream/issues"}, "license": "MIT", "main": "index.js", "files": ["index.js"], "engines": {"node": ">= 6"}, "scripts": {"test": "mocha --reporter dot", "jsdoc": "jsdoc -c jsdoc.json README.md"}, "dependencies": {"archiver-utils": "^2.1.0", "compress-commons": "^2.1.1", "readable-stream": "^3.4.0"}, "devDependencies": {"archiver-jsdoc-theme": "^1.1.1", "chai": "^4.2.0", "jsdoc": "^3.6.3", "minami": "^1.1.0", "mkdirp": "^0.5.0", "mocha": "^6.2.0", "rimraf": "^2.6.3"}, "keywords": ["archive", "stream", "zip-stream", "zip"], "publishConfig": {"registry": "https://registry.npmjs.org/"}}