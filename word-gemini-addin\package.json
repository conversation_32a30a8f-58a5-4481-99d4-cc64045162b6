{"name": "word-gemini-addin", "version": "1.0.0", "description": "Word加载项，集成Gemini AI助手", "main": "taskpane.html", "scripts": {"build": "webpack --mode=production", "build:dev": "webpack --mode=development", "dev-server": "webpack serve --mode=development", "start": "office-addin-dev-certs install --machine && npm run dev-server", "stop": "office-addin-debugging stop", "validate": "office-addin-manifest validate manifest.xml", "sideload": "office-addin-debugging start manifest.xml desktop"}, "dependencies": {"core-js": "^3.9.1"}, "devDependencies": {"@types/office-js": "^1.0.180", "html-webpack-plugin": "^5.6.3", "office-addin-debugging": "^4.6.2", "office-addin-dev-certs": "^1.11.3", "office-addin-manifest": "^1.12.3", "webpack": "^5.76.3", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.8.1"}, "browserslist": ["ie 11"], "keywords": [], "author": "", "license": "ISC"}