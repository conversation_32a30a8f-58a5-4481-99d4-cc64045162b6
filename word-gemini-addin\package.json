{"name": "word-gemini-addin", "version": "2.0.0", "description": "Word加载项，集成通义千问 AI 助手 - 重构优化版", "main": "src/main.js", "scripts": {"build": "webpack --mode=production", "build:dev": "webpack --mode=development", "build:analyze": "npm run build && npm run analyze", "dev": "webpack serve --mode=development", "dev-server": "webpack serve --mode=development", "start": "office-addin-dev-certs install --machine && npm run dev", "stop": "office-addin-debugging stop", "validate": "office-addin-manifest validate manifest.xml", "sideload": "office-addin-debugging start manifest.xml desktop", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "lint:check": "eslint src/**/*.js --max-warnings 0", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --coverage --watchAll=false --ci", "clean": "rimraf dist coverage .jest-cache", "clean:all": "npm run clean && rimraf node_modules package-lock.json", "analyze": "webpack-bundle-analyzer dist/taskpane.bundle.js", "deploy": "node scripts/deploy.js", "deploy:dev": "node scripts/deploy.js development", "deploy:staging": "node scripts/deploy.js staging", "deploy:prod": "node scripts/deploy.js production", "precommit": "npm run lint:check && npm run test:ci", "prepare": "npm run build:dev", "postinstall": "npm run validate", "security:audit": "npm audit", "security:fix": "npm audit fix", "deps:check": "npm outdated", "deps:update": "npm update", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}", "format:check": "prettier --check src/**/*.{js,jsx,ts,tsx,css,md}", "docs:generate": "jsdoc -c jsdoc.config.json", "performance:test": "lighthouse http://localhost:3001/taskpane.html --output=json --output-path=./performance-report.json"}, "dependencies": {"core-js": "^3.9.1"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/plugin-proposal-class-properties": "^7.18.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.28.0", "@types/office-js": "^1.0.180", "babel-jest": "^29.5.0", "babel-loader": "^9.2.1", "css-loader": "^6.8.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-jest": "^27.2.0", "html-webpack-plugin": "^5.6.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-junit": "^16.0.0", "jest-transform-css": "^6.0.1", "jsdoc": "^4.0.0", "lighthouse": "^10.2.0", "office-addin-debugging": "^4.6.2", "office-addin-dev-certs": "^1.11.3", "office-addin-manifest": "^1.12.3", "prettier": "^2.8.0", "rimraf": "^5.0.0", "style-loader": "^3.3.0", "webpack": "^5.76.3", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.8.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "keywords": ["office-addin", "word", "ai", "qwen", "javascript", "productivity"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/word-gemini-addin.git"}, "bugs": {"url": "https://github.com/yourusername/word-gemini-addin/issues"}, "homepage": "https://github.com/yourusername/word-gemini-addin#readme"}