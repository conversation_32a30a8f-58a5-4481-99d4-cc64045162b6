{"name": "word-gemini-addin", "version": "2.0.0", "description": "Word加载项，集成通义千问 AI 助手 - 重构优化版", "main": "src/main.js", "scripts": {"build": "webpack --mode=production", "build:dev": "webpack --mode=development", "dev": "webpack serve --mode=development", "dev-server": "webpack serve --mode=development", "start": "office-addin-dev-certs install --machine && npm run dev", "stop": "office-addin-debugging stop", "validate": "office-addin-manifest validate manifest.xml", "sideload": "office-addin-debugging start manifest.xml desktop", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "test": "jest", "test:watch": "jest --watch", "clean": "<PERSON><PERSON><PERSON> dist", "analyze": "webpack-bundle-analyzer dist/taskpane.bundle.js"}, "dependencies": {"core-js": "^3.9.1"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/plugin-proposal-class-properties": "^7.18.0", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.22.0", "@types/office-js": "^1.0.180", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "eslint": "^8.42.0", "html-webpack-plugin": "^5.6.3", "jest": "^29.5.0", "office-addin-debugging": "^4.6.2", "office-addin-dev-certs": "^1.11.3", "office-addin-manifest": "^1.12.3", "rimraf": "^5.0.0", "style-loader": "^3.3.0", "webpack": "^5.76.3", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.8.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "keywords": ["office-addin", "word", "ai", "qwen", "javascript", "productivity"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/word-gemini-addin.git"}, "bugs": {"url": "https://github.com/yourusername/word-gemini-addin/issues"}, "homepage": "https://github.com/yourusername/word-gemini-addin#readme"}