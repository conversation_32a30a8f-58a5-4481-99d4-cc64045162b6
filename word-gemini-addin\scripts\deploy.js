#!/usr/bin/env node

/**
 * 部署脚本
 * 自动化构建和部署流程
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const packageJson = require('../package.json');

// 配置
const config = {
    buildDir: 'dist',
    manifestFile: 'manifest.xml',
    version: packageJson.version,
    environments: {
        development: {
            url: 'https://localhost:3001',
            name: 'Development'
        },
        staging: {
            url: 'https://staging.example.com',
            name: 'Staging'
        },
        production: {
            url: 'https://production.example.com',
            name: 'Production'
        }
    }
};

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function error(message) {
    log(`❌ ${message}`, 'red');
}

function success(message) {
    log(`✅ ${message}`, 'green');
}

function info(message) {
    log(`ℹ️  ${message}`, 'blue');
}

function warning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

// 执行命令
function exec(command, options = {}) {
    try {
        const result = execSync(command, { 
            stdio: 'inherit', 
            encoding: 'utf8',
            ...options 
        });
        return result;
    } catch (err) {
        error(`命令执行失败: ${command}`);
        throw err;
    }
}

// 检查环境
function checkEnvironment() {
    info('检查部署环境...');
    
    // 检查 Node.js 版本
    const nodeVersion = process.version;
    info(`Node.js 版本: ${nodeVersion}`);
    
    // 检查 npm 版本
    try {
        const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
        info(`npm 版本: ${npmVersion}`);
    } catch (err) {
        error('npm 未安装或不可用');
        throw err;
    }
    
    // 检查必要文件
    const requiredFiles = ['package.json', 'webpack.config.js', 'src/main.js'];
    for (const file of requiredFiles) {
        if (!fs.existsSync(file)) {
            error(`缺少必要文件: ${file}`);
            throw new Error(`Missing required file: ${file}`);
        }
    }
    
    success('环境检查通过');
}

// 清理构建目录
function cleanBuild() {
    info('清理构建目录...');
    
    if (fs.existsSync(config.buildDir)) {
        exec(`rimraf ${config.buildDir}`);
    }
    
    success('构建目录已清理');
}

// 安装依赖
function installDependencies() {
    info('安装依赖...');
    
    exec('npm ci');
    
    success('依赖安装完成');
}

// 运行测试
function runTests() {
    info('运行测试...');
    
    try {
        exec('npm test -- --coverage --watchAll=false');
        success('所有测试通过');
    } catch (err) {
        error('测试失败');
        throw err;
    }
}

// 代码检查
function lintCode() {
    info('运行代码检查...');
    
    try {
        exec('npm run lint');
        success('代码检查通过');
    } catch (err) {
        warning('代码检查发现问题，尝试自动修复...');
        try {
            exec('npm run lint:fix');
            success('代码问题已自动修复');
        } catch (fixErr) {
            error('代码检查失败，请手动修复');
            throw fixErr;
        }
    }
}

// 构建项目
function buildProject(environment) {
    info(`构建项目 (${environment})...`);
    
    const env = config.environments[environment];
    if (!env) {
        error(`未知环境: ${environment}`);
        throw new Error(`Unknown environment: ${environment}`);
    }
    
    // 设置环境变量
    process.env.NODE_ENV = environment === 'development' ? 'development' : 'production';
    process.env.PUBLIC_URL = env.url;
    
    // 执行构建
    exec(`npm run build`);
    
    success(`项目构建完成 (${environment})`);
}

// 更新清单文件
function updateManifest(environment) {
    info('更新清单文件...');
    
    const manifestPath = path.join(config.buildDir, config.manifestFile);
    if (!fs.existsSync(manifestPath)) {
        error('清单文件不存在');
        throw new Error('Manifest file not found');
    }
    
    let manifest = fs.readFileSync(manifestPath, 'utf8');
    const env = config.environments[environment];
    
    // 更新版本号
    manifest = manifest.replace(
        /<Version>.*<\/Version>/,
        `<Version>${config.version}</Version>`
    );
    
    // 更新 URL
    manifest = manifest.replace(
        /https:\/\/localhost:3001/g,
        env.url
    );
    
    // 更新显示名称
    manifest = manifest.replace(
        /<DisplayName[^>]*>.*<\/DisplayName>/,
        `<DisplayName DefaultValue="Word Gemini - ${env.name}" />`
    );
    
    fs.writeFileSync(manifestPath, manifest);
    
    success('清单文件已更新');
}

// 验证构建
function validateBuild() {
    info('验证构建结果...');
    
    const requiredFiles = [
        'taskpane.html',
        'taskpane.bundle.js',
        'manifest.xml'
    ];
    
    for (const file of requiredFiles) {
        const filePath = path.join(config.buildDir, file);
        if (!fs.existsSync(filePath)) {
            error(`构建文件缺失: ${file}`);
            throw new Error(`Missing build file: ${file}`);
        }
    }
    
    // 检查文件大小
    const bundlePath = path.join(config.buildDir, 'taskpane.bundle.js');
    const stats = fs.statSync(bundlePath);
    const sizeInMB = stats.size / (1024 * 1024);
    
    info(`Bundle 大小: ${sizeInMB.toFixed(2)} MB`);
    
    if (sizeInMB > 5) {
        warning('Bundle 文件较大，考虑优化');
    }
    
    success('构建验证通过');
}

// 生成部署报告
function generateReport(environment, startTime) {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    const report = {
        environment,
        version: config.version,
        buildTime: new Date().toISOString(),
        duration: `${duration.toFixed(2)}s`,
        files: fs.readdirSync(config.buildDir)
    };
    
    const reportPath = path.join(config.buildDir, 'deploy-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    info('部署报告:');
    console.log(JSON.stringify(report, null, 2));
}

// 主部署函数
async function deploy(environment = 'development') {
    const startTime = Date.now();
    
    try {
        log(`🚀 开始部署到 ${environment} 环境`, 'cyan');
        
        checkEnvironment();
        cleanBuild();
        installDependencies();
        lintCode();
        runTests();
        buildProject(environment);
        updateManifest(environment);
        validateBuild();
        generateReport(environment, startTime);
        
        success(`🎉 部署完成! 环境: ${environment}`);
        
    } catch (err) {
        error(`部署失败: ${err.message}`);
        process.exit(1);
    }
}

// 命令行接口
if (require.main === module) {
    const args = process.argv.slice(2);
    const environment = args[0] || 'development';
    
    if (!config.environments[environment]) {
        error(`无效环境: ${environment}`);
        error(`可用环境: ${Object.keys(config.environments).join(', ')}`);
        process.exit(1);
    }
    
    deploy(environment);
}

module.exports = { deploy };
