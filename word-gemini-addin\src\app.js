/**
 * 主应用类
 * 协调各个服务和组件的工作
 */

import { ApiService } from './services/apiService.js';
import { StorageService } from './services/storageService.js';
import { WordService } from './services/wordService.js';
import { ErrorService } from './services/errorService.js';
import { ChatManager } from './ui/chatManager.js';
import { UI_CONFIG, PROMPTS } from './config/constants.js';

export class WordGeminiApp {
    constructor() {
        this.apiService = new ApiService();
        this.chatManager = new ChatManager();
        this.errorService = new ErrorService();
        this.isInitialized = false;
        this.currentJsCode = '';
    }

    /**
     * 初始化应用
     */
    async initialize() {
        try {
            // 检查 Office 环境
            if (!WordService.isWordApiAvailable()) {
                throw new Error('Word API 不可用');
            }

            // 检查本地存储
            if (!StorageService.isStorageAvailable()) {
                console.warn('本地存储不可用，某些功能可能受限');
            }

            // 初始化聊天管理器
            if (!this.chatManager.initialize()) {
                throw new Error('聊天界面初始化失败');
            }

            // 设置事件监听器
            this._setupEventListeners();

            // 检查并加载 API Key
            this._loadApiKey();

            // 加载用户偏好
            this._loadUserPreferences();

            this.isInitialized = true;
            console.log('Word Gemini 应用初始化成功');

        } catch (error) {
            console.error('应用初始化失败:', error);
            this._showInitializationError(error.message);
        }
    }

    /**
     * 保存 API Key
     * @param {string} apiKey - API 密钥
     */
    saveApiKey(apiKey) {
        if (!apiKey || apiKey.trim() === '') {
            this.chatManager.showError('API Key 不能为空');
            return false;
        }

        try {
            this.apiService.setApiKey(apiKey);
            StorageService.saveApiKey(apiKey);
            
            this._showChatInterface();
            this.chatManager.showSuccess('API Key 保存成功');
            return true;
        } catch (error) {
            console.error('保存 API Key 失败:', error);
            this.chatManager.showError('保存 API Key 失败');
            return false;
        }
    }

    /**
     * 发送消息
     * @param {string} message - 用户消息
     * @param {string} mode - 操作模式
     */
    async sendMessage(message, mode) {
        if (!message || message.trim() === '') {
            return;
        }

        if (!this.apiService.isApiKeyValid()) {
            this.chatManager.showError(PROMPTS.ERROR_MESSAGES.API_KEY_MISSING);
            return;
        }

        this.chatManager.setProcessing(true);
        this.chatManager.addMessage(message, UI_CONFIG.MESSAGE_TYPES.USER);
        this.chatManager.clearUserInput();

        try {
            if (mode === UI_CONFIG.MODES.JS_DIRECT) {
                await this._handleJavaScriptMode(message);
            } else {
                await this._handleNormalMode(message);
            }
        } catch (error) {
            const errorInfo = this.errorService.handleError(error, {
                type: 'SEND_MESSAGE_ERROR',
                mode: mode,
                messageLength: message.length
            });

            this.chatManager.showError(errorInfo.userMessage);

            // 如果可以重试，显示重试选项
            if (errorInfo.canRetry) {
                this._showRetryOption(() => this.sendMessage(message, mode));
            }
        } finally {
            this.chatManager.setProcessing(false);
        }
    }

    /**
     * 执行 JavaScript 代码
     */
    async executeJavaScript() {
        if (!this.currentJsCode) {
            this.chatManager.showError('没有可执行的代码');
            return;
        }

        this.chatManager.showLoading('正在执行代码...');

        try {
            const result = await WordService.executeJavaScript(this.currentJsCode);
            
            this.chatManager.hideLoading();
            
            if (result.success) {
                this.chatManager.showSuccess('代码执行成功！');
            } else {
                this.chatManager.showError(result.message);
            }
        } catch (error) {
            this.chatManager.hideLoading();
            this.chatManager.showError(`代码执行失败: ${error.message}`);
        }
    }

    /**
     * 插入文本到文档
     */
    async insertToDocument() {
        if (!this.chatManager.lastResponse) {
            this.chatManager.showError('没有可插入的内容');
            return;
        }

        try {
            await WordService.insertText(this.chatManager.lastResponse);
            this.chatManager.showSuccess('内容已插入到文档');
        } catch (error) {
            this.chatManager.showError(`插入失败: ${error.message}`);
        }
    }

    /**
     * 清除聊天记录
     */
    clearChat() {
        this.chatManager.clearChat();
        StorageService.clearChatHistory();
    }

    /**
     * 处理 JavaScript 模式
     * @param {string} message - 用户消息
     * @private
     */
    async _handleJavaScriptMode(message) {
        this.chatManager.showLoading('正在生成代码...');

        try {
            const jsCode = await this.apiService.generateWordJavaScript(message);
            this.currentJsCode = jsCode;
            
            this.chatManager.hideLoading();
            this.chatManager.addMessage(
                '代码生成完成，正在自动执行...', 
                UI_CONFIG.MESSAGE_TYPES.ASSISTANT, 
                { jsCode: jsCode }
            );

            // 启用执行按钮
            const executeButton = document.getElementById('execute-js-code');
            if (executeButton) {
                executeButton.disabled = false;
            }

            // 自动执行代码
            setTimeout(() => {
                this.executeJavaScript();
            }, 500);

        } catch (error) {
            this.chatManager.hideLoading();
            throw error;
        }
    }

    /**
     * 处理普通对话模式
     * @param {string} message - 用户消息
     * @private
     */
    async _handleNormalMode(message) {
        this.chatManager.showLoading('正在思考...');

        try {
            // 构建对话历史
            const messages = this._buildChatHistory(message);
            
            const response = await this.apiService.callApi(messages);
            this.chatManager.lastResponse = response;
            
            this.chatManager.hideLoading();
            this.chatManager.addMessage(response, UI_CONFIG.MESSAGE_TYPES.ASSISTANT);

            // 启用插入按钮
            const insertButton = document.getElementById('insert-to-doc');
            if (insertButton) {
                insertButton.disabled = false;
            }

        } catch (error) {
            this.chatManager.hideLoading();
            throw error;
        }
    }

    /**
     * 构建聊天历史
     * @param {string} newMessage - 新消息
     * @returns {Array} 聊天历史数组
     * @private
     */
    _buildChatHistory(newMessage) {
        const messages = [];
        
        // 添加系统消息
        messages.push({ role: 'system', content: PROMPTS.SYSTEM_PROMPT });
        
        // 添加历史对话（最近10条）
        const recentHistory = this.chatManager.chatHistory
            .filter(msg => msg.type === UI_CONFIG.MESSAGE_TYPES.USER || msg.type === UI_CONFIG.MESSAGE_TYPES.ASSISTANT)
            .slice(-10);
            
        recentHistory.forEach(msg => {
            const role = msg.type === UI_CONFIG.MESSAGE_TYPES.USER ? 'user' : 'assistant';
            messages.push({ role, content: msg.content });
        });
        
        // 添加当前消息
        messages.push({ role: 'user', content: newMessage });
        
        return messages;
    }

    /**
     * 设置事件监听器
     * @private
     */
    _setupEventListeners() {
        // API Key 保存
        const saveApiKeyBtn = document.getElementById('save-api-key');
        if (saveApiKeyBtn) {
            saveApiKeyBtn.addEventListener('click', () => {
                const apiKeyInput = document.getElementById('api-key');
                if (apiKeyInput) {
                    this.saveApiKey(apiKeyInput.value);
                }
            });
        }

        // 发送消息
        const sendMessageBtn = document.getElementById('send-message');
        if (sendMessageBtn) {
            sendMessageBtn.addEventListener('click', () => {
                const message = this.chatManager.getUserInput();
                const mode = this.chatManager.currentMode;
                this.sendMessage(message, mode);
            });
        }

        // 自定义事件监听
        document.addEventListener('sendMessage', (event) => {
            this.sendMessage(event.detail.message, event.detail.mode);
        });

        // 执行 JavaScript
        const executeJsBtn = document.getElementById('execute-js-code');
        if (executeJsBtn) {
            executeJsBtn.addEventListener('click', () => {
                this.executeJavaScript();
            });
        }

        // 插入到文档
        const insertToDocBtn = document.getElementById('insert-to-doc');
        if (insertToDocBtn) {
            insertToDocBtn.addEventListener('click', () => {
                this.insertToDocument();
            });
        }

        // 清除聊天
        const clearChatBtn = document.getElementById('clear-chat');
        if (clearChatBtn) {
            clearChatBtn.addEventListener('click', () => {
                this.clearChat();
            });
        }

        // 模式切换
        const modeRadios = document.querySelectorAll('input[name="mode"]');
        modeRadios.forEach(radio => {
            radio.addEventListener('change', (event) => {
                this.chatManager.setMode(event.target.value);
            });
        });
    }

    /**
     * 加载 API Key
     * @private
     */
    _loadApiKey() {
        const savedApiKey = StorageService.getApiKey();
        if (savedApiKey) {
            this.apiService.setApiKey(savedApiKey);
            const apiKeyInput = document.getElementById('api-key');
            if (apiKeyInput) {
                apiKeyInput.value = savedApiKey;
            }
            this._showChatInterface();
        }
    }

    /**
     * 加载用户偏好
     * @private
     */
    _loadUserPreferences() {
        const preferences = StorageService.getUserPreferences();
        
        // 设置默认模式
        if (preferences.defaultMode) {
            this.chatManager.setMode(preferences.defaultMode);
            const modeRadio = document.querySelector(`input[name="mode"][value="${preferences.defaultMode}"]`);
            if (modeRadio) {
                modeRadio.checked = true;
            }
        }
    }

    /**
     * 显示聊天界面
     * @private
     */
    _showChatInterface() {
        const apiConfig = document.getElementById('api-config');
        const chatContainer = document.getElementById('chat-container');
        
        if (apiConfig) apiConfig.style.display = 'none';
        if (chatContainer) chatContainer.style.display = 'flex';
        
        // 显示欢迎消息
        if (this.chatManager.chatHistory.length === 0) {
            this.chatManager.addMessage(PROMPTS.WELCOME_MESSAGE, UI_CONFIG.MESSAGE_TYPES.ASSISTANT);
        }
    }

    /**
     * 显示初始化错误
     * @param {string} error - 错误信息
     * @private
     */
    _showInitializationError(error) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'initialization-error';
        errorDiv.innerHTML = `
            <div class="error-content">
                <h2>❌ 初始化失败</h2>
                <p>插件初始化过程中发生错误：</p>
                <pre class="error-details">${error}</pre>
                <div class="error-actions">
                    <button onclick="location.reload()" class="ms-Button ms-Button--primary">
                        🔄 重新加载
                    </button>
                    <button onclick="this.showErrorReport()" class="ms-Button ms-Button--secondary">
                        📊 错误报告
                    </button>
                </div>
            </div>
        `;
        document.body.appendChild(errorDiv);
    }

    /**
     * 显示重试选项
     * @param {Function} retryCallback - 重试回调函数
     * @private
     */
    _showRetryOption(retryCallback) {
        const retryDiv = document.createElement('div');
        retryDiv.className = 'retry-option';
        retryDiv.innerHTML = `
            <div class="retry-content">
                <span>操作失败，是否重试？</span>
                <button class="ms-Button ms-Button--primary retry-btn">重试</button>
                <button class="ms-Button ms-Button--secondary cancel-btn">取消</button>
            </div>
        `;

        // 添加事件监听器
        const retryBtn = retryDiv.querySelector('.retry-btn');
        const cancelBtn = retryDiv.querySelector('.cancel-btn');

        retryBtn.addEventListener('click', () => {
            retryDiv.remove();
            retryCallback();
        });

        cancelBtn.addEventListener('click', () => {
            retryDiv.remove();
        });

        // 5秒后自动移除
        setTimeout(() => {
            if (retryDiv.parentNode) {
                retryDiv.remove();
            }
        }, 5000);

        document.body.appendChild(retryDiv);
    }

    /**
     * 显示错误报告
     */
    showErrorReport() {
        const report = this.errorService.generateErrorReport();
        const reportWindow = window.open('', '_blank', 'width=800,height=600');

        reportWindow.document.write(`
            <html>
                <head>
                    <title>错误报告</title>
                    <style>
                        body { font-family: 'Segoe UI', sans-serif; padding: 20px; }
                        .report-section { margin-bottom: 20px; }
                        .report-title { color: #d13438; font-size: 18px; margin-bottom: 10px; }
                        .report-content { background: #f5f5f5; padding: 15px; border-radius: 4px; }
                        .error-item { margin-bottom: 10px; padding: 10px; border-left: 3px solid #d13438; }
                        pre { background: #f0f0f0; padding: 10px; overflow-x: auto; }
                        .stats { display: flex; gap: 20px; }
                        .stat-item { text-align: center; }
                        .stat-number { font-size: 24px; font-weight: bold; color: #d13438; }
                    </style>
                </head>
                <body>
                    <h1>Word Gemini 插件错误报告</h1>

                    <div class="report-section">
                        <div class="report-title">📊 统计信息</div>
                        <div class="report-content">
                            <div class="stats">
                                <div class="stat-item">
                                    <div class="stat-number">${report.totalErrors}</div>
                                    <div>总错误数</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="report-section">
                        <div class="report-title">🏷️ 错误分类</div>
                        <div class="report-content">
                            <pre>${JSON.stringify(report.errorsByCategory, null, 2)}</pre>
                        </div>
                    </div>

                    <div class="report-section">
                        <div class="report-title">📝 最近错误</div>
                        <div class="report-content">
                            ${report.mostRecentError ? `
                                <div class="error-item">
                                    <strong>消息:</strong> ${report.mostRecentError.message}<br>
                                    <strong>类型:</strong> ${report.mostRecentError.type}<br>
                                    <strong>时间:</strong> ${new Date(report.mostRecentError.timestamp).toLocaleString()}
                                </div>
                            ` : '暂无错误记录'}
                        </div>
                    </div>

                    <div class="report-section">
                        <div class="report-title">🔧 系统信息</div>
                        <div class="report-content">
                            <pre>${JSON.stringify({
                                userAgent: navigator.userAgent,
                                timestamp: new Date().toISOString(),
                                officeVersion: typeof Office !== 'undefined' ? Office.context?.diagnostics?.version : 'N/A'
                            }, null, 2)}</pre>
                        </div>
                    </div>

                    <button onclick="navigator.clipboard.writeText(document.body.innerText)"
                            style="margin-top: 20px; padding: 10px 20px; background: #0078d4; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        📋 复制报告到剪贴板
                    </button>
                </body>
            </html>
        `);
    }

    /**
     * 获取错误历史
     * @returns {Array} 错误历史
     */
    getErrorHistory() {
        return this.errorService.getErrorHistory();
    }

    /**
     * 清除错误历史
     */
    clearErrorHistory() {
        this.errorService.clearErrorHistory();
        this.chatManager.showSuccess('错误历史已清除');
    }
}
