/**
 * 应用入口文件
 * 初始化和启动 Word Gemini 插件
 */

import { WordGeminiApp } from './app.js';

// 全局应用实例
let app = null;

/**
 * Office 初始化完成后的回调
 */
Office.onReady((info) => {
    if (info.host === Office.HostType.Word) {
        console.log('Word 环境检测成功');
        initializeApp();
    } else {
        console.error('此插件只能在 Microsoft Word 中运行');
        showHostError();
    }
});

/**
 * 初始化应用
 */
async function initializeApp() {
    try {
        // 等待 DOM 加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startApp);
        } else {
            startApp();
        }
    } catch (error) {
        console.error('应用初始化失败:', error);
        showInitializationError(error);
    }
}

/**
 * 启动应用
 */
async function startApp() {
    try {
        // 显示加载状态
        showLoadingState();

        // 创建应用实例
        app = new WordGeminiApp();

        // 初始化应用
        await app.initialize();

        // 隐藏加载状态
        hideLoadingState();

        console.log('Word Gemini 插件启动成功');
    } catch (error) {
        console.error('应用启动失败:', error);
        hideLoadingState();
        showInitializationError(error);
    }
}

/**
 * 显示加载状态
 */
function showLoadingState() {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'app-loading';
    loadingDiv.className = 'app-loading';
    loadingDiv.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在初始化 Word Gemini 插件...</p>
        </div>
    `;
    
    document.body.appendChild(loadingDiv);
}

/**
 * 隐藏加载状态
 */
function hideLoadingState() {
    const loadingDiv = document.getElementById('app-loading');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

/**
 * 显示主机错误
 */
function showHostError() {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'host-error';
    errorDiv.innerHTML = `
        <div class="error-content">
            <h2>❌ 环境错误</h2>
            <p>此插件只能在 Microsoft Word 中运行。</p>
            <p>请在 Word 中打开此插件。</p>
        </div>
    `;
    
    document.body.appendChild(errorDiv);
}

/**
 * 显示初始化错误
 * @param {Error} error - 错误对象
 */
function showInitializationError(error) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'initialization-error';
    errorDiv.innerHTML = `
        <div class="error-content">
            <h2>❌ 初始化失败</h2>
            <p>插件初始化过程中发生错误：</p>
            <pre class="error-details">${error.message}</pre>
            <div class="error-actions">
                <button onclick="location.reload()" class="ms-Button ms-Button--primary">
                    🔄 重新加载
                </button>
                <button onclick="showDebugInfo()" class="ms-Button ms-Button--secondary">
                    🔍 调试信息
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(errorDiv);
}

/**
 * 显示调试信息
 */
function showDebugInfo() {
    const debugInfo = {
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString(),
        officeVersion: Office.context?.diagnostics?.version || 'Unknown',
        host: Office.context?.host || 'Unknown',
        platform: Office.context?.platform || 'Unknown',
        localStorage: typeof Storage !== 'undefined',
        fetch: typeof fetch !== 'undefined'
    };

    const debugWindow = window.open('', '_blank', 'width=600,height=400');
    debugWindow.document.write(`
        <html>
            <head>
                <title>调试信息</title>
                <style>
                    body { font-family: monospace; padding: 20px; }
                    pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
                </style>
            </head>
            <body>
                <h2>Word Gemini 插件调试信息</h2>
                <pre>${JSON.stringify(debugInfo, null, 2)}</pre>
                <button onclick="navigator.clipboard.writeText(document.querySelector('pre').textContent)">
                    复制到剪贴板
                </button>
            </body>
        </html>
    `);
}

/**
 * 全局错误处理
 */
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
    
    // 如果应用还未初始化，显示错误信息
    if (!app) {
        showInitializationError(event.error);
    }
});

/**
 * 未处理的 Promise 拒绝
 */
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的 Promise 拒绝:', event.reason);
    event.preventDefault();
});

// 导出全局函数供 HTML 使用
window.showDebugInfo = showDebugInfo;

// 开发模式下的额外功能
if (process.env.NODE_ENV === 'development') {
    // 开发工具
    window.app = app;
    window.debugApp = () => {
        console.log('应用实例:', app);
        console.log('Office 上下文:', Office.context);
    };
    
    console.log('开发模式已启用');
    console.log('可用的调试命令:');
    console.log('- window.debugApp() - 查看应用状态');
    console.log('- window.showDebugInfo() - 显示调试信息');
}
