/**
 * API 服务模块
 * 处理与通义千问 API 的交互
 */

import { API_CONFIG, PROMPTS } from '../config/constants.js';
import { CacheService } from './cacheService.js';

export class ApiService {
    constructor() {
        this.apiKey = null;
        this.retryCount = 0;
        this.cache = new CacheService();
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.performanceService = null; // 将在应用初始化时设置
    }

    /**
     * 设置 API Key
     * @param {string} apiKey - API 密钥
     */
    setApiKey(apiKey) {
        this.apiKey = apiKey;
    }

    /**
     * 设置性能服务
     * @param {PerformanceService} performanceService - 性能服务实例
     */
    setPerformanceService(performanceService) {
        this.performanceService = performanceService;
    }

    /**
     * 验证 API Key 是否有效
     * @returns {boolean} 是否有效
     */
    isApiKeyValid() {
        return this.apiKey && this.apiKey.trim().length > 0;
    }

    /**
     * 调用通义千问 API
     * @param {Array} messages - 消息历史
     * @param {Object} options - 可选参数
     * @returns {Promise<string>} API 响应
     */
    async callApi(messages, options = {}) {
        if (!this.isApiKeyValid()) {
            throw new Error(PROMPTS.ERROR_MESSAGES.API_KEY_MISSING);
        }

        // 生成缓存键
        const cacheKey = CacheService.generateKey('api_call', {
            messages: messages.slice(-5), // 只使用最近5条消息作为缓存键
            model: API_CONFIG.QWEN_MODEL,
            temperature: options.temperature || 0.7,
            maxTokens: options.maxTokens || 2000
        });

        // 检查缓存
        const cachedResponse = this.cache.get(cacheKey);
        if (cachedResponse && options.useCache !== false) {
            console.log('使用缓存响应');
            return cachedResponse;
        }

        const startTime = performance.now();
        let success = false;
        let dataSize = 0;

        try {
            const requestBody = {
                model: API_CONFIG.QWEN_MODEL,
                input: {
                    messages: messages
                },
                parameters: {
                    temperature: options.temperature || 0.7,
                    max_tokens: options.maxTokens || 2000,
                    top_p: options.topP || 0.9
                }
            };

            const response = await this._makeRequest(requestBody);
            const result = this._parseResponse(response);

            success = true;
            dataSize = new Blob([JSON.stringify(response)]).size;

            // 缓存响应（如果不是一次性请求）
            if (options.cache !== false) {
                this.cache.set(cacheKey, result, {
                    ttl: options.cacheTTL || 10 * 60 * 1000, // 默认10分钟
                    tags: ['api_response'],
                    priority: 'normal'
                });
            }

            return result;
        } catch (error) {
            console.error('API 调用失败:', error);
            throw new Error(`${PROMPTS.ERROR_MESSAGES.API_CALL_FAILED}: ${error.message}`);
        } finally {
            // 记录性能数据
            const endTime = performance.now();
            if (this.performanceService) {
                this.performanceService.recordApiCall(
                    'qwen_api_call',
                    startTime,
                    endTime,
                    success,
                    dataSize
                );
            }
        }
    }

    /**
     * 生成 Word JavaScript 代码
     * @param {string} userRequest - 用户请求
     * @returns {Promise<string>} 生成的代码
     */
    async generateWordJavaScript(userRequest) {
        const messages = [
            { role: 'system', content: PROMPTS.SYSTEM_PROMPT },
            { role: 'user', content: userRequest }
        ];

        try {
            const response = await this.callApi(messages, { temperature: 0.3 });
            return this._extractJavaScriptCode(response);
        } catch (error) {
            console.error('代码生成失败:', error);
            throw error;
        }
    }

    /**
     * 发送 HTTP 请求
     * @param {Object} requestBody - 请求体
     * @returns {Promise<Object>} 响应数据
     * @private
     */
    async _makeRequest(requestBody) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.TIMEOUT);

        try {
            const response = await fetch(API_CONFIG.QWEN_API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`HTTP ${response.status}: ${errorData.message || response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请检查网络连接');
            }
            
            // 重试机制
            if (this.retryCount < API_CONFIG.MAX_RETRIES) {
                this.retryCount++;
                console.log(`重试第 ${this.retryCount} 次...`);
                await this._delay(1000 * this.retryCount); // 递增延迟
                return this._makeRequest(requestBody);
            }
            
            this.retryCount = 0;
            throw error;
        }
    }

    /**
     * 解析 API 响应
     * @param {Object} response - API 响应
     * @returns {string} 解析后的文本
     * @private
     */
    _parseResponse(response) {
        try {
            if (response.output && response.output.text) {
                return response.output.text.trim();
            } else if (response.output && response.output.choices && response.output.choices[0]) {
                return response.output.choices[0].message.content.trim();
            } else {
                throw new Error('响应格式不正确');
            }
        } catch (error) {
            console.error('响应解析失败:', response);
            throw new Error(PROMPTS.ERROR_MESSAGES.INVALID_RESPONSE);
        }
    }

    /**
     * 从响应中提取 JavaScript 代码
     * @param {string} response - API 响应文本
     * @returns {string} 提取的代码
     * @private
     */
    _extractJavaScriptCode(response) {
        // 尝试提取代码块中的内容
        const codeBlockRegex = /```(?:javascript|js)?\s*([\s\S]*?)```/i;
        const match = response.match(codeBlockRegex);
        
        if (match && match[1]) {
            return match[1].trim();
        }
        
        // 如果没有代码块，返回原始响应
        return response.trim();
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} Promise 对象
     * @private
     */
    _delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 重置重试计数
     */
    resetRetryCount() {
        this.retryCount = 0;
    }

    /**
     * 添加请求到队列
     * @param {Function} requestFunction - 请求函数
     * @param {number} priority - 优先级 (1-10, 10最高)
     * @returns {Promise} 请求结果
     */
    queueRequest(requestFunction, priority = 5) {
        return new Promise((resolve, reject) => {
            const request = {
                id: Date.now() + Math.random(),
                function: requestFunction,
                priority,
                resolve,
                reject,
                timestamp: Date.now()
            };

            this.requestQueue.push(request);
            this.requestQueue.sort((a, b) => b.priority - a.priority);

            if (!this.isProcessingQueue) {
                this._processQueue();
            }
        });
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} 缓存统计
     */
    getCacheStats() {
        return this.cache.getStats();
    }

    /**
     * 清除 API 缓存
     */
    clearCache() {
        this.cache.deleteByTag('api_response');
    }

    /**
     * 预热常用请求
     * @param {Array} commonRequests - 常用请求列表
     */
    async preloadCommonRequests(commonRequests) {
        const preloadPromises = commonRequests.map(async (request) => {
            try {
                const result = await this.callApi(request.messages, {
                    ...request.options,
                    cacheTTL: 30 * 60 * 1000 // 30分钟缓存
                });
                console.log(`预加载请求完成: ${request.name}`);
                return result;
            } catch (error) {
                console.warn(`预加载请求失败: ${request.name}`, error);
                return null;
            }
        });

        await Promise.allSettled(preloadPromises);
    }

    /**
     * 处理请求队列
     * @private
     */
    async _processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();

            try {
                const result = await request.function();
                request.resolve(result);
            } catch (error) {
                request.reject(error);
            }

            // 添加小延迟以避免过快的请求
            await this._delay(100);
        }

        this.isProcessingQueue = false;
    }

    /**
     * 批量处理请求
     * @param {Array} requests - 请求数组
     * @param {Object} options - 选项
     * @returns {Promise<Array>} 结果数组
     */
    async batchProcess(requests, options = {}) {
        const batchSize = options.batchSize || 3;
        const delay = options.delay || 1000;
        const results = [];

        for (let i = 0; i < requests.length; i += batchSize) {
            const batch = requests.slice(i, i + batchSize);

            const batchPromises = batch.map(request =>
                this.queueRequest(() => this.callApi(request.messages, request.options))
            );

            try {
                const batchResults = await Promise.allSettled(batchPromises);
                results.push(...batchResults);

                // 在批次之间添加延迟
                if (i + batchSize < requests.length) {
                    await this._delay(delay);
                }
            } catch (error) {
                console.error('批处理失败:', error);
                throw error;
            }
        }

        return results;
    }

    /**
     * 获取队列状态
     * @returns {Object} 队列状态
     */
    getQueueStatus() {
        return {
            queueLength: this.requestQueue.length,
            isProcessing: this.isProcessingQueue,
            oldestRequest: this.requestQueue.length > 0 ?
                Date.now() - this.requestQueue[this.requestQueue.length - 1].timestamp : 0
        };
    }
}
