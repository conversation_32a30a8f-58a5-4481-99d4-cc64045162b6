/**
 * 缓存服务
 * 提供智能缓存功能以优化性能
 */

export class CacheService {
    constructor() {
        this.cache = new Map();
        this.cacheConfig = {
            maxSize: 100, // 最大缓存条目数
            defaultTTL: 5 * 60 * 1000, // 默认5分钟过期
            maxMemoryUsage: 10 * 1024 * 1024, // 最大10MB内存使用
            cleanupInterval: 60 * 1000 // 每分钟清理一次过期缓存
        };
        
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            evictions: 0
        };

        this.init();
    }

    /**
     * 初始化缓存服务
     */
    init() {
        this.startCleanupTimer();
        this.setupMemoryPressureHandler();
    }

    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {Object} options - 选项
     */
    set(key, value, options = {}) {
        const ttl = options.ttl || this.cacheConfig.defaultTTL;
        const priority = options.priority || 'normal'; // low, normal, high
        const tags = options.tags || [];

        const cacheEntry = {
            key,
            value,
            createdAt: Date.now(),
            expiresAt: Date.now() + ttl,
            accessCount: 0,
            lastAccessed: Date.now(),
            priority,
            tags,
            size: this._calculateSize(value)
        };

        // 检查内存使用情况
        if (this._shouldEvict()) {
            this._evictLeastUsed();
        }

        this.cache.set(key, cacheEntry);
        this.stats.sets++;

        // 如果超过最大大小，移除最少使用的条目
        if (this.cache.size > this.cacheConfig.maxSize) {
            this._evictLeastUsed();
        }
    }

    /**
     * 获取缓存
     * @param {string} key - 缓存键
     * @returns {any} 缓存值或 null
     */
    get(key) {
        const entry = this.cache.get(key);
        
        if (!entry) {
            this.stats.misses++;
            return null;
        }

        // 检查是否过期
        if (this._isExpired(entry)) {
            this.delete(key);
            this.stats.misses++;
            return null;
        }

        // 更新访问信息
        entry.accessCount++;
        entry.lastAccessed = Date.now();
        
        this.stats.hits++;
        return entry.value;
    }

    /**
     * 删除缓存
     * @param {string} key - 缓存键
     * @returns {boolean} 是否删除成功
     */
    delete(key) {
        const deleted = this.cache.delete(key);
        if (deleted) {
            this.stats.deletes++;
        }
        return deleted;
    }

    /**
     * 检查缓存是否存在
     * @param {string} key - 缓存键
     * @returns {boolean} 是否存在
     */
    has(key) {
        const entry = this.cache.get(key);
        return entry && !this._isExpired(entry);
    }

    /**
     * 清空所有缓存
     */
    clear() {
        this.cache.clear();
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            evictions: 0
        };
    }

    /**
     * 根据标签删除缓存
     * @param {string} tag - 标签
     */
    deleteByTag(tag) {
        let deletedCount = 0;
        for (const [key, entry] of this.cache.entries()) {
            if (entry.tags.includes(tag)) {
                this.cache.delete(key);
                deletedCount++;
            }
        }
        this.stats.deletes += deletedCount;
        return deletedCount;
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const totalRequests = this.stats.hits + this.stats.misses;
        const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;
        
        return {
            ...this.stats,
            totalRequests,
            hitRate: Math.round(hitRate * 100) / 100,
            cacheSize: this.cache.size,
            memoryUsage: this._calculateTotalMemoryUsage()
        };
    }

    /**
     * 获取缓存信息
     * @returns {Object} 缓存信息
     */
    getCacheInfo() {
        const entries = Array.from(this.cache.values()).map(entry => ({
            key: entry.key,
            size: entry.size,
            createdAt: entry.createdAt,
            expiresAt: entry.expiresAt,
            accessCount: entry.accessCount,
            lastAccessed: entry.lastAccessed,
            priority: entry.priority,
            tags: entry.tags
        }));

        return {
            entries,
            totalEntries: this.cache.size,
            totalMemoryUsage: this._calculateTotalMemoryUsage(),
            config: this.cacheConfig
        };
    }

    /**
     * 预热缓存
     * @param {Array} preloadData - 预加载数据
     */
    preload(preloadData) {
        preloadData.forEach(({ key, value, options }) => {
            this.set(key, value, { ...options, priority: 'high' });
        });
    }

    /**
     * 生成缓存键
     * @param {string} prefix - 前缀
     * @param {Object} params - 参数
     * @returns {string} 缓存键
     */
    static generateKey(prefix, params) {
        const sortedParams = Object.keys(params)
            .sort()
            .map(key => `${key}:${JSON.stringify(params[key])}`)
            .join('|');
        return `${prefix}:${btoa(sortedParams)}`;
    }

    /**
     * 开始清理定时器
     * @private
     */
    startCleanupTimer() {
        setInterval(() => {
            this._cleanupExpired();
        }, this.cacheConfig.cleanupInterval);
    }

    /**
     * 设置内存压力处理器
     * @private
     */
    setupMemoryPressureHandler() {
        // 监听内存压力事件（如果支持）
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usageRatio = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
                
                if (usageRatio > 0.8) { // 如果内存使用超过80%
                    this._aggressiveCleanup();
                }
            }, 30000); // 每30秒检查一次
        }
    }

    /**
     * 清理过期缓存
     * @private
     */
    _cleanupExpired() {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, entry] of this.cache.entries()) {
            if (this._isExpired(entry)) {
                this.cache.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            console.log(`清理了 ${cleanedCount} 个过期缓存条目`);
        }
    }

    /**
     * 激进清理（内存压力时）
     * @private
     */
    _aggressiveCleanup() {
        const entries = Array.from(this.cache.entries());
        
        // 按优先级和访问频率排序
        entries.sort(([, a], [, b]) => {
            const priorityWeight = { low: 1, normal: 2, high: 3 };
            const scoreA = priorityWeight[a.priority] * a.accessCount;
            const scoreB = priorityWeight[b.priority] * b.accessCount;
            return scoreA - scoreB;
        });

        // 删除最低优先级的50%条目
        const toDelete = Math.floor(entries.length * 0.5);
        for (let i = 0; i < toDelete; i++) {
            this.cache.delete(entries[i][0]);
            this.stats.evictions++;
        }

        console.log(`内存压力清理：删除了 ${toDelete} 个缓存条目`);
    }

    /**
     * 移除最少使用的条目
     * @private
     */
    _evictLeastUsed() {
        let leastUsedKey = null;
        let leastUsedEntry = null;
        let lowestScore = Infinity;

        for (const [key, entry] of this.cache.entries()) {
            // 计算分数：访问次数 / 存在时间（小时）
            const ageHours = (Date.now() - entry.createdAt) / (1000 * 60 * 60);
            const score = entry.accessCount / Math.max(ageHours, 0.1);
            
            if (score < lowestScore && entry.priority !== 'high') {
                lowestScore = score;
                leastUsedKey = key;
                leastUsedEntry = entry;
            }
        }

        if (leastUsedKey) {
            this.cache.delete(leastUsedKey);
            this.stats.evictions++;
        }
    }

    /**
     * 检查是否应该驱逐缓存
     * @returns {boolean} 是否应该驱逐
     * @private
     */
    _shouldEvict() {
        const memoryUsage = this._calculateTotalMemoryUsage();
        return memoryUsage > this.cacheConfig.maxMemoryUsage;
    }

    /**
     * 检查缓存是否过期
     * @param {Object} entry - 缓存条目
     * @returns {boolean} 是否过期
     * @private
     */
    _isExpired(entry) {
        return Date.now() > entry.expiresAt;
    }

    /**
     * 计算值的大小
     * @param {any} value - 值
     * @returns {number} 大小（字节）
     * @private
     */
    _calculateSize(value) {
        try {
            return new Blob([JSON.stringify(value)]).size;
        } catch (error) {
            // 如果无法序列化，返回估算大小
            return JSON.stringify(value).length * 2; // 粗略估算
        }
    }

    /**
     * 计算总内存使用量
     * @returns {number} 总内存使用量（字节）
     * @private
     */
    _calculateTotalMemoryUsage() {
        let totalSize = 0;
        for (const entry of this.cache.values()) {
            totalSize += entry.size;
        }
        return totalSize;
    }
}
