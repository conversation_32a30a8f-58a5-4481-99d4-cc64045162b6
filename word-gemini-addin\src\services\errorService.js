/**
 * 错误处理服务
 * 统一处理应用中的错误和异常
 */

import { PROMPTS } from '../config/constants.js';

export class ErrorService {
    constructor() {
        this.errorHistory = [];
        this.maxHistorySize = 50;
        this.setupGlobalErrorHandlers();
    }

    /**
     * 处理错误
     * @param {Error|string} error - 错误对象或错误消息
     * @param {Object} context - 错误上下文信息
     * @returns {Object} 格式化的错误信息
     */
    handleError(error, context = {}) {
        const errorInfo = this._formatError(error, context);
        this._logError(errorInfo);
        this._saveToHistory(errorInfo);
        
        return {
            userMessage: this._getUserFriendlyMessage(errorInfo),
            technicalMessage: errorInfo.message,
            errorCode: errorInfo.code,
            canRetry: this._canRetry(errorInfo),
            suggestions: this._getSuggestions(errorInfo)
        };
    }

    /**
     * 处理 API 错误
     * @param {Error} error - API 错误
     * @param {Object} requestInfo - 请求信息
     * @returns {Object} 处理后的错误信息
     */
    handleApiError(error, requestInfo = {}) {
        const context = {
            type: 'API_ERROR',
            url: requestInfo.url,
            method: requestInfo.method,
            statusCode: error.status || error.statusCode,
            timestamp: Date.now()
        };

        // 根据状态码分类错误
        if (error.status) {
            switch (error.status) {
                case 401:
                    context.category = 'AUTHENTICATION';
                    break;
                case 403:
                    context.category = 'AUTHORIZATION';
                    break;
                case 429:
                    context.category = 'RATE_LIMIT';
                    break;
                case 500:
                case 502:
                case 503:
                    context.category = 'SERVER_ERROR';
                    break;
                default:
                    context.category = 'CLIENT_ERROR';
            }
        }

        return this.handleError(error, context);
    }

    /**
     * 处理 Word API 错误
     * @param {Error} error - Word API 错误
     * @param {string} operation - 执行的操作
     * @returns {Object} 处理后的错误信息
     */
    handleWordApiError(error, operation = '') {
        const context = {
            type: 'WORD_API_ERROR',
            operation: operation,
            timestamp: Date.now()
        };

        // Word API 特定错误分类
        if (error.code) {
            switch (error.code) {
                case 'InvalidArgument':
                    context.category = 'INVALID_ARGUMENT';
                    break;
                case 'ItemNotFound':
                    context.category = 'ITEM_NOT_FOUND';
                    break;
                case 'AccessDenied':
                    context.category = 'ACCESS_DENIED';
                    break;
                case 'GeneralException':
                    context.category = 'GENERAL_EXCEPTION';
                    break;
                default:
                    context.category = 'UNKNOWN_WORD_ERROR';
            }
        }

        return this.handleError(error, context);
    }

    /**
     * 处理网络错误
     * @param {Error} error - 网络错误
     * @returns {Object} 处理后的错误信息
     */
    handleNetworkError(error) {
        const context = {
            type: 'NETWORK_ERROR',
            timestamp: Date.now()
        };

        if (error.name === 'AbortError') {
            context.category = 'TIMEOUT';
        } else if (error.message.includes('Failed to fetch')) {
            context.category = 'CONNECTION_FAILED';
        } else {
            context.category = 'NETWORK_UNKNOWN';
        }

        return this.handleError(error, context);
    }

    /**
     * 获取错误历史
     * @param {number} limit - 返回的错误数量限制
     * @returns {Array} 错误历史数组
     */
    getErrorHistory(limit = 10) {
        return this.errorHistory.slice(-limit);
    }

    /**
     * 清除错误历史
     */
    clearErrorHistory() {
        this.errorHistory = [];
    }

    /**
     * 生成错误报告
     * @returns {Object} 错误报告
     */
    generateErrorReport() {
        const now = Date.now();
        const last24Hours = now - (24 * 60 * 60 * 1000);
        
        const recentErrors = this.errorHistory.filter(
            error => error.timestamp > last24Hours
        );

        const errorsByType = {};
        const errorsByCategory = {};

        recentErrors.forEach(error => {
            // 按类型统计
            errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
            
            // 按分类统计
            if (error.category) {
                errorsByCategory[error.category] = (errorsByCategory[error.category] || 0) + 1;
            }
        });

        return {
            totalErrors: recentErrors.length,
            errorsByType,
            errorsByCategory,
            mostRecentError: recentErrors[recentErrors.length - 1],
            generatedAt: now
        };
    }

    /**
     * 设置全局错误处理器
     * @private
     */
    setupGlobalErrorHandlers() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            this.handleError(event.error, {
                type: 'GLOBAL_ERROR',
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });

        // 未处理的 Promise 拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, {
                type: 'UNHANDLED_PROMISE_REJECTION'
            });
        });
    }

    /**
     * 格式化错误信息
     * @param {Error|string} error - 错误对象或消息
     * @param {Object} context - 上下文信息
     * @returns {Object} 格式化的错误信息
     * @private
     */
    _formatError(error, context) {
        const errorInfo = {
            message: typeof error === 'string' ? error : error.message,
            stack: error.stack,
            name: error.name,
            code: error.code,
            timestamp: Date.now(),
            ...context
        };

        // 生成唯一错误 ID
        errorInfo.id = this._generateErrorId(errorInfo);

        return errorInfo;
    }

    /**
     * 记录错误到控制台
     * @param {Object} errorInfo - 错误信息
     * @private
     */
    _logError(errorInfo) {
        const logLevel = this._getLogLevel(errorInfo);
        const message = `[${errorInfo.type || 'ERROR'}] ${errorInfo.message}`;
        
        switch (logLevel) {
            case 'error':
                console.error(message, errorInfo);
                break;
            case 'warn':
                console.warn(message, errorInfo);
                break;
            default:
                console.log(message, errorInfo);
        }
    }

    /**
     * 保存错误到历史记录
     * @param {Object} errorInfo - 错误信息
     * @private
     */
    _saveToHistory(errorInfo) {
        this.errorHistory.push(errorInfo);
        
        // 限制历史记录大小
        if (this.errorHistory.length > this.maxHistorySize) {
            this.errorHistory = this.errorHistory.slice(-this.maxHistorySize);
        }
    }

    /**
     * 获取用户友好的错误消息
     * @param {Object} errorInfo - 错误信息
     * @returns {string} 用户友好的消息
     * @private
     */
    _getUserFriendlyMessage(errorInfo) {
        switch (errorInfo.category) {
            case 'AUTHENTICATION':
                return 'API Key 无效或已过期，请检查您的 API Key 设置';
            case 'AUTHORIZATION':
                return '没有权限执行此操作，请检查 API Key 权限';
            case 'RATE_LIMIT':
                return 'API 调用频率过高，请稍后再试';
            case 'SERVER_ERROR':
                return '服务器暂时不可用，请稍后重试';
            case 'TIMEOUT':
                return '请求超时，请检查网络连接';
            case 'CONNECTION_FAILED':
                return '网络连接失败，请检查网络设置';
            case 'INVALID_ARGUMENT':
                return '操作参数无效，请检查输入内容';
            case 'ITEM_NOT_FOUND':
                return '未找到指定的文档内容';
            case 'ACCESS_DENIED':
                return '没有权限访问文档内容';
            default:
                return errorInfo.message || '发生未知错误';
        }
    }

    /**
     * 判断错误是否可以重试
     * @param {Object} errorInfo - 错误信息
     * @returns {boolean} 是否可以重试
     * @private
     */
    _canRetry(errorInfo) {
        const retryableCategories = [
            'TIMEOUT',
            'CONNECTION_FAILED',
            'SERVER_ERROR',
            'RATE_LIMIT'
        ];
        
        return retryableCategories.includes(errorInfo.category);
    }

    /**
     * 获取错误建议
     * @param {Object} errorInfo - 错误信息
     * @returns {Array} 建议数组
     * @private
     */
    _getSuggestions(errorInfo) {
        const suggestions = [];

        switch (errorInfo.category) {
            case 'AUTHENTICATION':
                suggestions.push('检查 API Key 是否正确');
                suggestions.push('确认 API Key 是否已激活');
                break;
            case 'RATE_LIMIT':
                suggestions.push('等待一段时间后重试');
                suggestions.push('减少 API 调用频率');
                break;
            case 'TIMEOUT':
                suggestions.push('检查网络连接');
                suggestions.push('尝试重新发送请求');
                break;
            case 'CONNECTION_FAILED':
                suggestions.push('检查网络设置');
                suggestions.push('确认防火墙设置');
                break;
            default:
                suggestions.push('尝试刷新页面');
                suggestions.push('联系技术支持');
        }

        return suggestions;
    }

    /**
     * 获取日志级别
     * @param {Object} errorInfo - 错误信息
     * @returns {string} 日志级别
     * @private
     */
    _getLogLevel(errorInfo) {
        if (errorInfo.category === 'RATE_LIMIT') {
            return 'warn';
        }
        return 'error';
    }

    /**
     * 生成错误 ID
     * @param {Object} errorInfo - 错误信息
     * @returns {string} 错误 ID
     * @private
     */
    _generateErrorId(errorInfo) {
        const data = `${errorInfo.message}-${errorInfo.type}-${errorInfo.timestamp}`;
        try {
            // 使用 UTF-8 安全的 Base64 编码
            return btoa(encodeURIComponent(data).replace(/%([0-9A-F]{2})/g, (match, p1) => {
                return String.fromCharCode(parseInt(p1, 16));
            })).substring(0, 8);
        } catch (error) {
            // 如果编码失败，使用简单哈希
            let hash = 0;
            for (let i = 0; i < data.length; i++) {
                const char = data.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            return Math.abs(hash).toString(36).substring(0, 8);
        }
    }
}
