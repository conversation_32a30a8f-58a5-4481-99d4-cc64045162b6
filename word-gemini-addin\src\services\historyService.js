/**
 * 历史记录服务
 * 管理聊天历史、代码历史和操作历史
 */

import { StorageService } from './storageService.js';

export class HistoryService {
    constructor() {
        this.chatHistory = [];
        this.codeHistory = [];
        this.operationHistory = [];
        this.maxHistorySize = 100;
        this.searchIndex = new Map();
        
        this.init();
    }

    /**
     * 初始化历史服务
     */
    init() {
        this.loadAllHistory();
        this.buildSearchIndex();
    }

    /**
     * 添加聊天记录
     * @param {Object} message - 消息对象
     */
    addChatMessage(message) {
        const historyItem = {
            id: this.generateId(),
            type: 'chat',
            timestamp: Date.now(),
            content: message.content,
            messageType: message.type,
            metadata: message.metadata || {},
            tags: this.extractTags(message.content)
        };

        this.chatHistory.unshift(historyItem);
        this.limitHistorySize(this.chatHistory);
        this.updateSearchIndex(historyItem);
        this.saveChatHistory();
    }

    /**
     * 添加代码记录
     * @param {Object} codeInfo - 代码信息
     */
    addCodeHistory(codeInfo) {
        const historyItem = {
            id: this.generateId(),
            type: 'code',
            timestamp: Date.now(),
            code: codeInfo.code,
            description: codeInfo.description,
            success: codeInfo.success,
            error: codeInfo.error,
            executionTime: codeInfo.executionTime,
            tags: this.extractCodeTags(codeInfo.code)
        };

        this.codeHistory.unshift(historyItem);
        this.limitHistorySize(this.codeHistory);
        this.updateSearchIndex(historyItem);
        this.saveCodeHistory();
    }

    /**
     * 添加操作记录
     * @param {Object} operation - 操作信息
     */
    addOperation(operation) {
        const historyItem = {
            id: this.generateId(),
            type: 'operation',
            timestamp: Date.now(),
            action: operation.action,
            target: operation.target,
            result: operation.result,
            duration: operation.duration,
            metadata: operation.metadata || {}
        };

        this.operationHistory.unshift(historyItem);
        this.limitHistorySize(this.operationHistory);
        this.updateSearchIndex(historyItem);
        this.saveOperationHistory();
    }

    /**
     * 搜索历史记录
     * @param {string} query - 搜索查询
     * @param {Object} options - 搜索选项
     * @returns {Array} 搜索结果
     */
    search(query, options = {}) {
        const {
            type = 'all', // 'chat', 'code', 'operation', 'all'
            limit = 20,
            sortBy = 'timestamp',
            sortOrder = 'desc'
        } = options;

        let results = [];
        const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);

        // 获取要搜索的历史记录
        let histories = [];
        if (type === 'all') {
            histories = [...this.chatHistory, ...this.codeHistory, ...this.operationHistory];
        } else if (type === 'chat') {
            histories = this.chatHistory;
        } else if (type === 'code') {
            histories = this.codeHistory;
        } else if (type === 'operation') {
            histories = this.operationHistory;
        }

        // 执行搜索
        results = histories.filter(item => {
            const searchableText = this.getSearchableText(item).toLowerCase();
            return searchTerms.every(term => searchableText.includes(term));
        });

        // 计算相关性分数
        results = results.map(item => ({
            ...item,
            relevanceScore: this.calculateRelevanceScore(item, searchTerms)
        }));

        // 排序
        results.sort((a, b) => {
            if (sortBy === 'relevance') {
                return sortOrder === 'desc' ? b.relevanceScore - a.relevanceScore : a.relevanceScore - b.relevanceScore;
            } else if (sortBy === 'timestamp') {
                return sortOrder === 'desc' ? b.timestamp - a.timestamp : a.timestamp - b.timestamp;
            }
            return 0;
        });

        return results.slice(0, limit);
    }

    /**
     * 获取最近的历史记录
     * @param {string} type - 历史类型
     * @param {number} limit - 限制数量
     * @returns {Array} 历史记录
     */
    getRecent(type = 'all', limit = 10) {
        let histories = [];
        
        if (type === 'all') {
            histories = [...this.chatHistory, ...this.codeHistory, ...this.operationHistory];
        } else if (type === 'chat') {
            histories = this.chatHistory;
        } else if (type === 'code') {
            histories = this.codeHistory;
        } else if (type === 'operation') {
            histories = this.operationHistory;
        }

        return histories
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, limit);
    }

    /**
     * 获取收藏的历史记录
     * @returns {Array} 收藏的记录
     */
    getFavorites() {
        const allHistory = [...this.chatHistory, ...this.codeHistory, ...this.operationHistory];
        return allHistory.filter(item => item.favorite);
    }

    /**
     * 切换收藏状态
     * @param {string} id - 记录ID
     * @returns {boolean} 新的收藏状态
     */
    toggleFavorite(id) {
        const item = this.findItemById(id);
        if (item) {
            item.favorite = !item.favorite;
            this.saveAllHistory();
            return item.favorite;
        }
        return false;
    }

    /**
     * 删除历史记录
     * @param {string} id - 记录ID
     * @returns {boolean} 是否删除成功
     */
    deleteItem(id) {
        const chatIndex = this.chatHistory.findIndex(item => item.id === id);
        if (chatIndex !== -1) {
            this.chatHistory.splice(chatIndex, 1);
            this.saveChatHistory();
            return true;
        }

        const codeIndex = this.codeHistory.findIndex(item => item.id === id);
        if (codeIndex !== -1) {
            this.codeHistory.splice(codeIndex, 1);
            this.saveCodeHistory();
            return true;
        }

        const opIndex = this.operationHistory.findIndex(item => item.id === id);
        if (opIndex !== -1) {
            this.operationHistory.splice(opIndex, 1);
            this.saveOperationHistory();
            return true;
        }

        return false;
    }

    /**
     * 清除所有历史记录
     * @param {string} type - 要清除的类型
     */
    clearHistory(type = 'all') {
        if (type === 'all' || type === 'chat') {
            this.chatHistory = [];
            this.saveChatHistory();
        }
        if (type === 'all' || type === 'code') {
            this.codeHistory = [];
            this.saveCodeHistory();
        }
        if (type === 'all' || type === 'operation') {
            this.operationHistory = [];
            this.saveOperationHistory();
        }
        
        this.rebuildSearchIndex();
    }

    /**
     * 导出历史记录
     * @param {string} type - 导出类型
     * @param {string} format - 导出格式 ('json', 'csv', 'txt')
     * @returns {string} 导出的数据
     */
    exportHistory(type = 'all', format = 'json') {
        let data = [];
        
        if (type === 'all') {
            data = [...this.chatHistory, ...this.codeHistory, ...this.operationHistory];
        } else if (type === 'chat') {
            data = this.chatHistory;
        } else if (type === 'code') {
            data = this.codeHistory;
        } else if (type === 'operation') {
            data = this.operationHistory;
        }

        switch (format) {
            case 'json':
                return JSON.stringify(data, null, 2);
            case 'csv':
                return this.convertToCSV(data);
            case 'txt':
                return this.convertToText(data);
            default:
                return JSON.stringify(data, null, 2);
        }
    }

    /**
     * 获取历史统计信息
     * @returns {Object} 统计信息
     */
    getStatistics() {
        const now = Date.now();
        const oneDay = 24 * 60 * 60 * 1000;
        const oneWeek = 7 * oneDay;
        const oneMonth = 30 * oneDay;

        const allHistory = [...this.chatHistory, ...this.codeHistory, ...this.operationHistory];

        return {
            total: {
                chat: this.chatHistory.length,
                code: this.codeHistory.length,
                operation: this.operationHistory.length,
                all: allHistory.length
            },
            recent: {
                today: allHistory.filter(item => now - item.timestamp < oneDay).length,
                thisWeek: allHistory.filter(item => now - item.timestamp < oneWeek).length,
                thisMonth: allHistory.filter(item => now - item.timestamp < oneMonth).length
            },
            favorites: this.getFavorites().length,
            oldestRecord: allHistory.length > 0 ? Math.min(...allHistory.map(item => item.timestamp)) : null,
            newestRecord: allHistory.length > 0 ? Math.max(...allHistory.map(item => item.timestamp)) : null
        };
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     * @private
     */
    generateId() {
        return 'hist_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 提取标签
     * @param {string} content - 内容
     * @returns {Array} 标签数组
     * @private
     */
    extractTags(content) {
        const tags = [];
        
        // 提取常见的操作关键词
        const keywords = ['格式', '插入', '删除', '替换', '查找', '表格', '图片', '标题', '段落', '字体'];
        keywords.forEach(keyword => {
            if (content.includes(keyword)) {
                tags.push(keyword);
            }
        });

        return tags;
    }

    /**
     * 提取代码标签
     * @param {string} code - 代码
     * @returns {Array} 标签数组
     * @private
     */
    extractCodeTags(code) {
        const tags = [];
        
        // 检测 Word API 方法
        const apiMethods = ['insertText', 'getSelection', 'insertTable', 'search', 'replace'];
        apiMethods.forEach(method => {
            if (code.includes(method)) {
                tags.push(method);
            }
        });

        return tags;
    }

    /**
     * 限制历史记录大小
     * @param {Array} history - 历史记录数组
     * @private
     */
    limitHistorySize(history) {
        if (history.length > this.maxHistorySize) {
            history.splice(this.maxHistorySize);
        }
    }

    /**
     * 更新搜索索引
     * @param {Object} item - 历史项目
     * @private
     */
    updateSearchIndex(item) {
        const searchableText = this.getSearchableText(item);
        const words = searchableText.toLowerCase().split(/\s+/);
        
        words.forEach(word => {
            if (word.length > 2) { // 忽略太短的词
                if (!this.searchIndex.has(word)) {
                    this.searchIndex.set(word, []);
                }
                this.searchIndex.get(word).push(item.id);
            }
        });
    }

    /**
     * 重建搜索索引
     * @private
     */
    rebuildSearchIndex() {
        this.searchIndex.clear();
        const allHistory = [...this.chatHistory, ...this.codeHistory, ...this.operationHistory];
        allHistory.forEach(item => this.updateSearchIndex(item));
    }

    /**
     * 构建搜索索引
     * @private
     */
    buildSearchIndex() {
        this.rebuildSearchIndex();
    }

    /**
     * 获取可搜索的文本
     * @param {Object} item - 历史项目
     * @returns {string} 可搜索的文本
     * @private
     */
    getSearchableText(item) {
        switch (item.type) {
            case 'chat':
                return `${item.content} ${item.tags.join(' ')}`;
            case 'code':
                return `${item.code} ${item.description} ${item.tags.join(' ')}`;
            case 'operation':
                return `${item.action} ${item.target} ${item.result}`;
            default:
                return '';
        }
    }

    /**
     * 计算相关性分数
     * @param {Object} item - 历史项目
     * @param {Array} searchTerms - 搜索词
     * @returns {number} 相关性分数
     * @private
     */
    calculateRelevanceScore(item, searchTerms) {
        const text = this.getSearchableText(item).toLowerCase();
        let score = 0;

        searchTerms.forEach(term => {
            const occurrences = (text.match(new RegExp(term, 'g')) || []).length;
            score += occurrences;
        });

        // 收藏的项目获得额外分数
        if (item.favorite) {
            score += 10;
        }

        // 最近的项目获得额外分数
        const ageHours = (Date.now() - item.timestamp) / (1000 * 60 * 60);
        if (ageHours < 24) {
            score += 5;
        }

        return score;
    }

    /**
     * 根据ID查找项目
     * @param {string} id - 项目ID
     * @returns {Object|null} 找到的项目
     * @private
     */
    findItemById(id) {
        const allHistory = [...this.chatHistory, ...this.codeHistory, ...this.operationHistory];
        return allHistory.find(item => item.id === id) || null;
    }

    /**
     * 加载所有历史记录
     * @private
     */
    loadAllHistory() {
        this.chatHistory = StorageService.getChatHistory() || [];
        this.codeHistory = this.loadCodeHistory();
        this.operationHistory = this.loadOperationHistory();
    }

    /**
     * 保存所有历史记录
     * @private
     */
    saveAllHistory() {
        this.saveChatHistory();
        this.saveCodeHistory();
        this.saveOperationHistory();
    }

    /**
     * 保存聊天历史
     * @private
     */
    saveChatHistory() {
        StorageService.saveChatHistory(this.chatHistory);
    }

    /**
     * 加载代码历史
     * @returns {Array} 代码历史
     * @private
     */
    loadCodeHistory() {
        try {
            const history = localStorage.getItem('code-history');
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.error('加载代码历史失败:', error);
            return [];
        }
    }

    /**
     * 保存代码历史
     * @private
     */
    saveCodeHistory() {
        try {
            localStorage.setItem('code-history', JSON.stringify(this.codeHistory));
        } catch (error) {
            console.error('保存代码历史失败:', error);
        }
    }

    /**
     * 加载操作历史
     * @returns {Array} 操作历史
     * @private
     */
    loadOperationHistory() {
        try {
            const history = localStorage.getItem('operation-history');
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.error('加载操作历史失败:', error);
            return [];
        }
    }

    /**
     * 保存操作历史
     * @private
     */
    saveOperationHistory() {
        try {
            localStorage.setItem('operation-history', JSON.stringify(this.operationHistory));
        } catch (error) {
            console.error('保存操作历史失败:', error);
        }
    }

    /**
     * 转换为CSV格式
     * @param {Array} data - 数据
     * @returns {string} CSV字符串
     * @private
     */
    convertToCSV(data) {
        if (data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => 
                    JSON.stringify(row[header] || '')
                ).join(',')
            )
        ].join('\n');
        
        return csvContent;
    }

    /**
     * 转换为文本格式
     * @param {Array} data - 数据
     * @returns {string} 文本字符串
     * @private
     */
    convertToText(data) {
        return data.map(item => {
            const date = new Date(item.timestamp).toLocaleString();
            switch (item.type) {
                case 'chat':
                    return `[${date}] 聊天: ${item.content}`;
                case 'code':
                    return `[${date}] 代码: ${item.description}\n${item.code}`;
                case 'operation':
                    return `[${date}] 操作: ${item.action} - ${item.result}`;
                default:
                    return `[${date}] ${JSON.stringify(item)}`;
            }
        }).join('\n\n');
    }
}
