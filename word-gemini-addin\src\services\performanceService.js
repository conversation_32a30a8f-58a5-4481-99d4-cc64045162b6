/**
 * 性能监控和优化服务
 * 监控应用性能并提供优化建议
 */

export class PerformanceService {
    constructor() {
        this.metrics = new Map();
        this.observers = [];
        this.isMonitoring = false;
        this.performanceData = {
            apiCalls: [],
            renderTimes: [],
            memoryUsage: [],
            userInteractions: []
        };
        
        this.init();
    }

    /**
     * 初始化性能服务
     */
    init() {
        this.setupPerformanceObserver();
        this.startMemoryMonitoring();
        this.setupUserInteractionTracking();
    }

    /**
     * 开始性能监控
     */
    startMonitoring() {
        this.isMonitoring = true;
        console.log('性能监控已启动');
    }

    /**
     * 停止性能监控
     */
    stopMonitoring() {
        this.isMonitoring = false;
        this.observers.forEach(observer => observer.disconnect());
        console.log('性能监控已停止');
    }

    /**
     * 记录 API 调用性能
     * @param {string} apiName - API 名称
     * @param {number} startTime - 开始时间
     * @param {number} endTime - 结束时间
     * @param {boolean} success - 是否成功
     * @param {number} dataSize - 数据大小（字节）
     */
    recordApiCall(apiName, startTime, endTime, success = true, dataSize = 0) {
        if (!this.isMonitoring) return;

        const duration = endTime - startTime;
        const record = {
            name: apiName,
            duration,
            success,
            dataSize,
            timestamp: Date.now(),
            throughput: dataSize > 0 ? dataSize / duration : 0
        };

        this.performanceData.apiCalls.push(record);
        this._limitArraySize(this.performanceData.apiCalls, 100);

        // 如果 API 调用过慢，记录警告
        if (duration > 5000) { // 5秒
            console.warn(`API 调用 ${apiName} 耗时过长: ${duration}ms`);
        }
    }

    /**
     * 记录渲染性能
     * @param {string} component - 组件名称
     * @param {number} renderTime - 渲染时间
     */
    recordRenderTime(component, renderTime) {
        if (!this.isMonitoring) return;

        const record = {
            component,
            renderTime,
            timestamp: Date.now()
        };

        this.performanceData.renderTimes.push(record);
        this._limitArraySize(this.performanceData.renderTimes, 50);
    }

    /**
     * 记录用户交互
     * @param {string} action - 交互动作
     * @param {number} responseTime - 响应时间
     */
    recordUserInteraction(action, responseTime) {
        if (!this.isMonitoring) return;

        const record = {
            action,
            responseTime,
            timestamp: Date.now()
        };

        this.performanceData.userInteractions.push(record);
        this._limitArraySize(this.performanceData.userInteractions, 50);
    }

    /**
     * 获取性能报告
     * @returns {Object} 性能报告
     */
    getPerformanceReport() {
        const report = {
            summary: this._generateSummary(),
            apiPerformance: this._analyzeApiPerformance(),
            renderPerformance: this._analyzeRenderPerformance(),
            memoryUsage: this._analyzeMemoryUsage(),
            recommendations: this._generateRecommendations()
        };

        return report;
    }

    /**
     * 优化建议
     * @returns {Array} 优化建议列表
     */
    getOptimizationSuggestions() {
        const suggestions = [];
        
        // 分析 API 性能
        const slowApiCalls = this.performanceData.apiCalls.filter(call => call.duration > 3000);
        if (slowApiCalls.length > 0) {
            suggestions.push({
                type: 'api',
                priority: 'high',
                message: `发现 ${slowApiCalls.length} 个慢速 API 调用，建议优化网络请求或增加缓存`
            });
        }

        // 分析内存使用
        const memoryData = this.performanceData.memoryUsage;
        if (memoryData.length > 0) {
            const latestMemory = memoryData[memoryData.length - 1];
            if (latestMemory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB
                suggestions.push({
                    type: 'memory',
                    priority: 'medium',
                    message: '内存使用量较高，建议清理不必要的对象引用'
                });
            }
        }

        // 分析渲染性能
        const slowRenders = this.performanceData.renderTimes.filter(render => render.renderTime > 100);
        if (slowRenders.length > 0) {
            suggestions.push({
                type: 'render',
                priority: 'medium',
                message: `发现 ${slowRenders.length} 个慢速渲染，建议优化 DOM 操作`
            });
        }

        return suggestions;
    }

    /**
     * 清理性能数据
     */
    clearPerformanceData() {
        this.performanceData = {
            apiCalls: [],
            renderTimes: [],
            memoryUsage: [],
            userInteractions: []
        };
    }

    /**
     * 设置性能观察器
     * @private
     */
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.entryType === 'measure') {
                            this.recordRenderTime(entry.name, entry.duration);
                        }
                    });
                });

                observer.observe({ entryTypes: ['measure', 'navigation'] });
                this.observers.push(observer);
            } catch (error) {
                console.warn('无法设置性能观察器:', error);
            }
        }
    }

    /**
     * 开始内存监控
     * @private
     */
    startMemoryMonitoring() {
        if ('memory' in performance) {
            const recordMemory = () => {
                if (!this.isMonitoring) return;

                const memory = performance.memory;
                const record = {
                    usedJSHeapSize: memory.usedJSHeapSize,
                    totalJSHeapSize: memory.totalJSHeapSize,
                    jsHeapSizeLimit: memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                };

                this.performanceData.memoryUsage.push(record);
                this._limitArraySize(this.performanceData.memoryUsage, 100);
            };

            // 每30秒记录一次内存使用情况
            setInterval(recordMemory, 30000);
            recordMemory(); // 立即记录一次
        }
    }

    /**
     * 设置用户交互跟踪
     * @private
     */
    setupUserInteractionTracking() {
        const trackInteraction = (eventType) => {
            return (event) => {
                if (!this.isMonitoring) return;

                const startTime = performance.now();
                
                // 使用 requestAnimationFrame 来测量响应时间
                requestAnimationFrame(() => {
                    const responseTime = performance.now() - startTime;
                    this.recordUserInteraction(eventType, responseTime);
                });
            };
        };

        document.addEventListener('click', trackInteraction('click'));
        document.addEventListener('keypress', trackInteraction('keypress'));
        document.addEventListener('scroll', trackInteraction('scroll'));
    }

    /**
     * 生成性能摘要
     * @returns {Object} 性能摘要
     * @private
     */
    _generateSummary() {
        const apiCalls = this.performanceData.apiCalls;
        const renderTimes = this.performanceData.renderTimes;
        
        return {
            totalApiCalls: apiCalls.length,
            averageApiTime: apiCalls.length > 0 ? 
                apiCalls.reduce((sum, call) => sum + call.duration, 0) / apiCalls.length : 0,
            totalRenders: renderTimes.length,
            averageRenderTime: renderTimes.length > 0 ?
                renderTimes.reduce((sum, render) => sum + render.renderTime, 0) / renderTimes.length : 0,
            monitoringDuration: Date.now() - (this.performanceData.apiCalls[0]?.timestamp || Date.now())
        };
    }

    /**
     * 分析 API 性能
     * @returns {Object} API 性能分析
     * @private
     */
    _analyzeApiPerformance() {
        const apiCalls = this.performanceData.apiCalls;
        if (apiCalls.length === 0) return null;

        const successfulCalls = apiCalls.filter(call => call.success);
        const failedCalls = apiCalls.filter(call => !call.success);

        return {
            totalCalls: apiCalls.length,
            successRate: (successfulCalls.length / apiCalls.length) * 100,
            averageDuration: apiCalls.reduce((sum, call) => sum + call.duration, 0) / apiCalls.length,
            slowestCall: Math.max(...apiCalls.map(call => call.duration)),
            fastestCall: Math.min(...apiCalls.map(call => call.duration)),
            failedCalls: failedCalls.length
        };
    }

    /**
     * 分析渲染性能
     * @returns {Object} 渲染性能分析
     * @private
     */
    _analyzeRenderPerformance() {
        const renderTimes = this.performanceData.renderTimes;
        if (renderTimes.length === 0) return null;

        return {
            totalRenders: renderTimes.length,
            averageTime: renderTimes.reduce((sum, render) => sum + render.renderTime, 0) / renderTimes.length,
            slowestRender: Math.max(...renderTimes.map(render => render.renderTime)),
            fastestRender: Math.min(...renderTimes.map(render => render.renderTime))
        };
    }

    /**
     * 分析内存使用
     * @returns {Object} 内存使用分析
     * @private
     */
    _analyzeMemoryUsage() {
        const memoryData = this.performanceData.memoryUsage;
        if (memoryData.length === 0) return null;

        const latest = memoryData[memoryData.length - 1];
        const peak = Math.max(...memoryData.map(data => data.usedJSHeapSize));

        return {
            current: latest.usedJSHeapSize,
            peak: peak,
            limit: latest.jsHeapSizeLimit,
            utilizationRate: (latest.usedJSHeapSize / latest.jsHeapSizeLimit) * 100
        };
    }

    /**
     * 生成优化建议
     * @returns {Array} 建议列表
     * @private
     */
    _generateRecommendations() {
        return this.getOptimizationSuggestions();
    }

    /**
     * 限制数组大小
     * @param {Array} array - 数组
     * @param {number} maxSize - 最大大小
     * @private
     */
    _limitArraySize(array, maxSize) {
        if (array.length > maxSize) {
            array.splice(0, array.length - maxSize);
        }
    }
}
