/**
 * 快捷操作服务
 * 提供常用的 Word 操作快捷方式
 */

import { WordService } from './wordService.js';

export class QuickActionsService {
    constructor() {
        this.actions = new Map();
        this.customActions = new Map();
        this.recentActions = [];
        this.maxRecentActions = 10;
        
        this.initializeDefaultActions();
    }

    /**
     * 初始化默认操作
     */
    initializeDefaultActions() {
        // 文本格式化操作
        this.registerAction('bold', {
            name: '加粗',
            description: '将选中文本设为粗体',
            icon: '𝐁',
            category: 'format',
            execute: () => this.formatText({ bold: true })
        });

        this.registerAction('italic', {
            name: '斜体',
            description: '将选中文本设为斜体',
            icon: '𝐼',
            category: 'format',
            execute: () => this.formatText({ italic: true })
        });

        this.registerAction('underline', {
            name: '下划线',
            description: '为选中文本添加下划线',
            icon: '𝐔',
            category: 'format',
            execute: () => this.formatText({ underline: true })
        });

        // 颜色操作
        this.registerAction('red-text', {
            name: '红色文本',
            description: '将选中文本设为红色',
            icon: '🔴',
            category: 'color',
            execute: () => this.formatText({ color: '#FF0000' })
        });

        this.registerAction('blue-text', {
            name: '蓝色文本',
            description: '将选中文本设为蓝色',
            icon: '🔵',
            category: 'color',
            execute: () => this.formatText({ color: '#0000FF' })
        });

        // 插入操作
        this.registerAction('insert-table', {
            name: '插入表格',
            description: '插入3x3表格',
            icon: '📊',
            category: 'insert',
            execute: () => this.insertTable(3, 3)
        });

        this.registerAction('insert-date', {
            name: '插入日期',
            description: '插入当前日期',
            icon: '📅',
            category: 'insert',
            execute: () => this.insertCurrentDate()
        });

        this.registerAction('insert-time', {
            name: '插入时间',
            description: '插入当前时间',
            icon: '🕐',
            category: 'insert',
            execute: () => this.insertCurrentTime()
        });

        // 段落操作
        this.registerAction('center-align', {
            name: '居中对齐',
            description: '将选中段落居中对齐',
            icon: '📄',
            category: 'paragraph',
            execute: () => this.alignParagraph('center')
        });

        this.registerAction('left-align', {
            name: '左对齐',
            description: '将选中段落左对齐',
            icon: '📄',
            category: 'paragraph',
            execute: () => this.alignParagraph('left')
        });

        this.registerAction('right-align', {
            name: '右对齐',
            description: '将选中段落右对齐',
            icon: '📄',
            category: 'paragraph',
            execute: () => this.alignParagraph('right')
        });

        // 查找替换操作
        this.registerAction('find-replace', {
            name: '查找替换',
            description: '查找并替换文本',
            icon: '🔍',
            category: 'edit',
            execute: (params) => this.findAndReplace(params.find, params.replace)
        });

        // 清理操作
        this.registerAction('clear-format', {
            name: '清除格式',
            description: '清除选中文本的所有格式',
            icon: '🧹',
            category: 'format',
            execute: () => this.clearFormatting()
        });

        // 文档信息
        this.registerAction('word-count', {
            name: '字数统计',
            description: '显示文档字数统计',
            icon: '📊',
            category: 'info',
            execute: () => this.getWordCount()
        });
    }

    /**
     * 注册操作
     * @param {string} id - 操作ID
     * @param {Object} action - 操作对象
     */
    registerAction(id, action) {
        this.actions.set(id, {
            id,
            ...action,
            createdAt: Date.now()
        });
    }

    /**
     * 注册自定义操作
     * @param {string} id - 操作ID
     * @param {Object} action - 操作对象
     */
    registerCustomAction(id, action) {
        this.customActions.set(id, {
            id,
            ...action,
            custom: true,
            createdAt: Date.now()
        });
        this.saveCustomActions();
    }

    /**
     * 执行操作
     * @param {string} actionId - 操作ID
     * @param {Object} params - 参数
     * @returns {Promise<any>} 执行结果
     */
    async executeAction(actionId, params = {}) {
        const action = this.actions.get(actionId) || this.customActions.get(actionId);
        
        if (!action) {
            throw new Error(`操作 "${actionId}" 不存在`);
        }

        try {
            const startTime = performance.now();
            const result = await action.execute(params);
            const endTime = performance.now();

            // 记录到最近操作
            this.addToRecentActions({
                ...action,
                executedAt: Date.now(),
                executionTime: endTime - startTime,
                params
            });

            return {
                success: true,
                result,
                executionTime: endTime - startTime
            };
        } catch (error) {
            console.error(`执行操作 "${actionId}" 失败:`, error);
            throw error;
        }
    }

    /**
     * 获取所有操作
     * @param {string} category - 分类筛选
     * @returns {Array} 操作列表
     */
    getAllActions(category = null) {
        const allActions = [
            ...Array.from(this.actions.values()),
            ...Array.from(this.customActions.values())
        ];

        if (category) {
            return allActions.filter(action => action.category === category);
        }

        return allActions;
    }

    /**
     * 获取操作分类
     * @returns {Array} 分类列表
     */
    getCategories() {
        const categories = new Set();
        this.getAllActions().forEach(action => {
            if (action.category) {
                categories.add(action.category);
            }
        });
        return Array.from(categories);
    }

    /**
     * 获取最近操作
     * @returns {Array} 最近操作列表
     */
    getRecentActions() {
        return this.recentActions;
    }

    /**
     * 搜索操作
     * @param {string} query - 搜索查询
     * @returns {Array} 搜索结果
     */
    searchActions(query) {
        const searchTerm = query.toLowerCase();
        return this.getAllActions().filter(action => 
            action.name.toLowerCase().includes(searchTerm) ||
            action.description.toLowerCase().includes(searchTerm)
        );
    }

    /**
     * 删除自定义操作
     * @param {string} actionId - 操作ID
     * @returns {boolean} 是否删除成功
     */
    deleteCustomAction(actionId) {
        const deleted = this.customActions.delete(actionId);
        if (deleted) {
            this.saveCustomActions();
        }
        return deleted;
    }

    /**
     * 格式化文本
     * @param {Object} formatting - 格式化选项
     * @returns {Promise<boolean>} 是否成功
     * @private
     */
    async formatText(formatting) {
        return await WordService.formatSelectedText(formatting);
    }

    /**
     * 插入表格
     * @param {number} rows - 行数
     * @param {number} cols - 列数
     * @returns {Promise<boolean>} 是否成功
     * @private
     */
    async insertTable(rows, cols) {
        const code = `
            Word.run(async (context) => {
                const selection = context.document.getSelection();
                const table = selection.insertTable(${rows}, ${cols}, Word.InsertLocation.after);
                table.headerRowCount = 1;
                await context.sync();
            });
        `;
        
        const result = await WordService.executeJavaScript(code);
        return result.success;
    }

    /**
     * 插入当前日期
     * @returns {Promise<boolean>} 是否成功
     * @private
     */
    async insertCurrentDate() {
        const date = new Date().toLocaleDateString('zh-CN');
        return await WordService.insertText(date);
    }

    /**
     * 插入当前时间
     * @returns {Promise<boolean>} 是否成功
     * @private
     */
    async insertCurrentTime() {
        const time = new Date().toLocaleTimeString('zh-CN');
        return await WordService.insertText(time);
    }

    /**
     * 段落对齐
     * @param {string} alignment - 对齐方式
     * @returns {Promise<boolean>} 是否成功
     * @private
     */
    async alignParagraph(alignment) {
        const code = `
            Word.run(async (context) => {
                const selection = context.document.getSelection();
                const paragraphs = selection.paragraphs;
                paragraphs.load('alignment');
                await context.sync();
                
                paragraphs.items.forEach(paragraph => {
                    paragraph.alignment = Word.Alignment.${alignment};
                });
                
                await context.sync();
            });
        `;
        
        const result = await WordService.executeJavaScript(code);
        return result.success;
    }

    /**
     * 查找并替换
     * @param {string} findText - 查找文本
     * @param {string} replaceText - 替换文本
     * @returns {Promise<number>} 替换数量
     * @private
     */
    async findAndReplace(findText, replaceText) {
        return await WordService.searchAndReplace(findText, replaceText);
    }

    /**
     * 清除格式
     * @returns {Promise<boolean>} 是否成功
     * @private
     */
    async clearFormatting() {
        const code = `
            Word.run(async (context) => {
                const selection = context.document.getSelection();
                selection.font.reset();
                await context.sync();
            });
        `;
        
        const result = await WordService.executeJavaScript(code);
        return result.success;
    }

    /**
     * 获取字数统计
     * @returns {Promise<Object>} 统计信息
     * @private
     */
    async getWordCount() {
        return await WordService.getDocumentInfo();
    }

    /**
     * 添加到最近操作
     * @param {Object} action - 操作对象
     * @private
     */
    addToRecentActions(action) {
        // 移除重复的操作
        this.recentActions = this.recentActions.filter(recent => recent.id !== action.id);
        
        // 添加到开头
        this.recentActions.unshift(action);
        
        // 限制数量
        if (this.recentActions.length > this.maxRecentActions) {
            this.recentActions = this.recentActions.slice(0, this.maxRecentActions);
        }
        
        this.saveRecentActions();
    }

    /**
     * 保存自定义操作
     * @private
     */
    saveCustomActions() {
        try {
            const customActionsArray = Array.from(this.customActions.values());
            localStorage.setItem('custom-actions', JSON.stringify(customActionsArray));
        } catch (error) {
            console.error('保存自定义操作失败:', error);
        }
    }

    /**
     * 加载自定义操作
     * @private
     */
    loadCustomActions() {
        try {
            const saved = localStorage.getItem('custom-actions');
            if (saved) {
                const customActionsArray = JSON.parse(saved);
                customActionsArray.forEach(action => {
                    this.customActions.set(action.id, action);
                });
            }
        } catch (error) {
            console.error('加载自定义操作失败:', error);
        }
    }

    /**
     * 保存最近操作
     * @private
     */
    saveRecentActions() {
        try {
            localStorage.setItem('recent-actions', JSON.stringify(this.recentActions));
        } catch (error) {
            console.error('保存最近操作失败:', error);
        }
    }

    /**
     * 加载最近操作
     * @private
     */
    loadRecentActions() {
        try {
            const saved = localStorage.getItem('recent-actions');
            if (saved) {
                this.recentActions = JSON.parse(saved);
            }
        } catch (error) {
            console.error('加载最近操作失败:', error);
        }
    }

    /**
     * 初始化
     */
    init() {
        this.loadCustomActions();
        this.loadRecentActions();
    }
}
