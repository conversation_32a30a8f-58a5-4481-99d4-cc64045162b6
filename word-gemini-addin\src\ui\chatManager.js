/**
 * 聊天界面管理器
 * 处理聊天界面的显示和交互
 */

import { UI_CONFIG, PROMPTS } from '../config/constants.js';
import { NotificationManager } from './notificationManager.js';

export class ChatManager {
    constructor() {
        this.chatHistory = [];
        this.isProcessing = false;
        this.currentMode = UI_CONFIG.MODES.NORMAL;
        this.lastResponse = '';
        this.messageContainer = null;
        this.inputElement = null;
        this.notificationManager = new NotificationManager();
        this.typingIndicator = null;
    }

    /**
     * 初始化聊天管理器
     */
    initialize() {
        this.messageContainer = document.getElementById('chat-messages');
        this.inputElement = document.getElementById('user-input');
        
        if (!this.messageContainer || !this.inputElement) {
            console.error('聊天界面元素未找到');
            return false;
        }

        this._setupEventListeners();
        this._loadChatHistory();
        return true;
    }

    /**
     * 添加消息到聊天界面
     * @param {string} content - 消息内容
     * @param {string} type - 消息类型
     * @param {Object} metadata - 元数据
     */
    addMessage(content, type, metadata = {}) {
        if (!this.messageContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;

        // 处理 Markdown 渲染
        const renderedContent = this._renderMarkdown(content);
        let html = `<div>${renderedContent}</div>`;

        // 添加代码块显示
        if (metadata.jsCode) {
            html += this._createCodeBlock(metadata.jsCode);
        }

        // 添加时间戳
        if (type !== UI_CONFIG.MESSAGE_TYPES.SYSTEM) {
            const timestamp = new Date().toLocaleTimeString();
            html += `<div class="message-timestamp">${timestamp}</div>`;
        }

        messageDiv.innerHTML = html;
        this.messageContainer.appendChild(messageDiv);

        // 滚动到底部
        this._scrollToBottom();

        // 保存到历史记录
        if (type !== UI_CONFIG.MESSAGE_TYPES.SYSTEM) {
            const messageData = {
                content,
                type,
                timestamp: Date.now(),
                metadata
            };

            this.chatHistory.push(messageData);

            // 如果有历史服务，也保存到历史服务中
            if (window.app && window.app.historyService) {
                window.app.historyService.addChatMessage(messageData);
            }
        }
    }

    /**
     * 清除聊天记录
     */
    clearChat() {
        if (this.messageContainer) {
            this.messageContainer.innerHTML = '';
        }
        this.chatHistory = [];
        this._saveChatHistory();
        
        // 显示欢迎消息
        this.addMessage(PROMPTS.WELCOME_MESSAGE, UI_CONFIG.MESSAGE_TYPES.ASSISTANT);
    }

    /**
     * 设置处理状态
     * @param {boolean} processing - 是否正在处理
     */
    setProcessing(processing) {
        this.isProcessing = processing;
        this._updateUIState();
    }

    /**
     * 设置操作模式
     * @param {string} mode - 操作模式
     */
    setMode(mode) {
        this.currentMode = mode;
        this._updateModeDisplay();
    }

    /**
     * 获取用户输入
     * @returns {string} 用户输入的文本
     */
    getUserInput() {
        return this.inputElement ? this.inputElement.value.trim() : '';
    }

    /**
     * 清空用户输入
     */
    clearUserInput() {
        if (this.inputElement) {
            this.inputElement.value = '';
        }
    }

    /**
     * 显示错误消息
     * @param {string} error - 错误信息
     */
    showError(error) {
        this.addMessage(`❌ ${error}`, UI_CONFIG.MESSAGE_TYPES.ERROR);
        this.notificationManager.showError(error);
    }

    /**
     * 显示成功消息
     * @param {string} message - 成功信息
     */
    showSuccess(message) {
        this.addMessage(`✅ ${message}`, UI_CONFIG.MESSAGE_TYPES.SYSTEM);
        this.notificationManager.showSuccess(message);
    }

    /**
     * 显示警告消息
     * @param {string} message - 警告信息
     */
    showWarning(message) {
        this.addMessage(`⚠️ ${message}`, UI_CONFIG.MESSAGE_TYPES.SYSTEM);
        this.notificationManager.showWarning(message);
    }

    /**
     * 显示信息消息
     * @param {string} message - 信息内容
     */
    showInfo(message) {
        this.addMessage(`ℹ️ ${message}`, UI_CONFIG.MESSAGE_TYPES.SYSTEM);
        this.notificationManager.showInfo(message);
    }

    /**
     * 显示加载状态
     * @param {string} message - 加载信息
     */
    showLoading(message = '正在处理...') {
        this.hideLoading(); // 先移除之前的加载状态

        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'message assistant loading-message';
        loadingDiv.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <span class="loading-text">${message}</span>
            </div>
        `;
        loadingDiv.id = 'loading-message';

        this.messageContainer.appendChild(loadingDiv);
        this._scrollToBottom();

        // 添加进入动画
        setTimeout(() => {
            loadingDiv.classList.add('show');
        }, 10);
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingMessage = document.getElementById('loading-message');
        if (loadingMessage) {
            loadingMessage.classList.add('fade-out');
            setTimeout(() => {
                if (loadingMessage.parentNode) {
                    loadingMessage.remove();
                }
            }, 300);
        }
    }

    /**
     * 显示打字指示器
     */
    showTypingIndicator() {
        this.hideTypingIndicator(); // 先移除之前的指示器

        this.typingIndicator = document.createElement('div');
        this.typingIndicator.className = 'message assistant typing-indicator';
        this.typingIndicator.innerHTML = `
            <div class="typing-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span class="typing-text">AI 正在思考...</span>
            </div>
        `;

        this.messageContainer.appendChild(this.typingIndicator);
        this._scrollToBottom();

        // 添加进入动画
        setTimeout(() => {
            this.typingIndicator.classList.add('show');
        }, 10);
    }

    /**
     * 隐藏打字指示器
     */
    hideTypingIndicator() {
        if (this.typingIndicator && this.typingIndicator.parentNode) {
            this.typingIndicator.classList.add('fade-out');
            setTimeout(() => {
                if (this.typingIndicator && this.typingIndicator.parentNode) {
                    this.typingIndicator.remove();
                }
                this.typingIndicator = null;
            }, 300);
        }
    }

    /**
     * 更新加载消息
     * @param {string} message - 新的加载消息
     */
    updateLoadingMessage(message) {
        const loadingText = document.querySelector('#loading-message .loading-text');
        if (loadingText) {
            loadingText.textContent = message;
        }
    }

    /**
     * 渲染 Markdown 内容
     * @param {string} content - 原始内容
     * @returns {string} 渲染后的 HTML
     * @private
     */
    _renderMarkdown(content) {
        if (typeof marked === 'function') {
            return marked.parse(content);
        }
        return this._escapeHtml(content);
    }

    /**
     * 转义 HTML 字符
     * @param {string} text - 原始文本
     * @returns {string} 转义后的文本
     * @private
     */
    _escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 创建代码块
     * @param {string} code - 代码内容
     * @returns {string} 代码块 HTML
     * @private
     */
    _createCodeBlock(code) {
        return `
            <div class="conversion-step">
                <div class="step-title">🤖 生成的 Word JavaScript 代码</div>
                <pre class="code-block">${this._escapeHtml(code)}</pre>
                <div class="code-actions">
                    <button class="ms-Button ms-Button--secondary copy-code-btn" onclick="navigator.clipboard.writeText(\`${code.replace(/`/g, '\\`')}\`)">
                        📋 复制代码
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 滚动到底部
     * @private
     */
    _scrollToBottom() {
        if (this.messageContainer) {
            setTimeout(() => {
                this.messageContainer.scrollTop = this.messageContainer.scrollHeight;
            }, 100);
        }
    }

    /**
     * 设置事件监听器
     * @private
     */
    _setupEventListeners() {
        // 回车发送消息
        if (this.inputElement) {
            this.inputElement.addEventListener('keypress', (event) => {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    if (!this.isProcessing) {
                        this._triggerSendMessage();
                    }
                }
            });
        }

        // 模式切换监听
        const modeRadios = document.querySelectorAll('input[name="mode"]');
        modeRadios.forEach(radio => {
            radio.addEventListener('change', (event) => {
                this.setMode(event.target.value);
            });
        });
    }

    /**
     * 触发发送消息事件
     * @private
     */
    _triggerSendMessage() {
        const event = new CustomEvent('sendMessage', {
            detail: {
                message: this.getUserInput(),
                mode: this.currentMode
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 更新 UI 状态
     * @private
     */
    _updateUIState() {
        const sendButton = document.getElementById('send-message');
        const executeButton = document.getElementById('execute-js-code');
        
        if (sendButton) {
            sendButton.disabled = this.isProcessing;
            sendButton.innerHTML = this.isProcessing 
                ? '<div class="loading"></div> 处理中...' 
                : '发送';
        }

        if (this.inputElement) {
            this.inputElement.disabled = this.isProcessing;
        }
    }

    /**
     * 更新模式显示
     * @private
     */
    _updateModeDisplay() {
        const modeInfo = document.querySelector('.mode-info');
        if (modeInfo) {
            const modeText = this.currentMode === UI_CONFIG.MODES.JS_DIRECT 
                ? '直接生成JavaScript' 
                : '普通对话';
            modeInfo.textContent = `当前模式: ${modeText}`;
        }
    }

    /**
     * 加载聊天历史
     * @private
     */
    _loadChatHistory() {
        // 这里可以从 StorageService 加载历史记录
        // 暂时跳过，避免循环依赖
    }

    /**
     * 保存聊天历史
     * @private
     */
    _saveChatHistory() {
        // 这里可以保存到 StorageService
        // 暂时跳过，避免循环依赖
    }
}
