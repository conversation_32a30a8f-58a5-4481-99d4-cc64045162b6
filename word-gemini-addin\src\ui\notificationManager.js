/**
 * 通知管理器
 * 处理各种类型的用户通知和提示
 */

export class NotificationManager {
    constructor() {
        this.notifications = new Map();
        this.defaultDuration = 5000; // 5秒
        this.maxNotifications = 5;
        this.container = null;
        this.init();
    }

    /**
     * 初始化通知管理器
     */
    init() {
        this.createContainer();
    }

    /**
     * 显示成功通知
     * @param {string} message - 消息内容
     * @param {Object} options - 选项
     */
    showSuccess(message, options = {}) {
        return this.show(message, 'success', options);
    }

    /**
     * 显示错误通知
     * @param {string} message - 消息内容
     * @param {Object} options - 选项
     */
    showError(message, options = {}) {
        return this.show(message, 'error', { 
            duration: 8000, // 错误消息显示更长时间
            ...options 
        });
    }

    /**
     * 显示警告通知
     * @param {string} message - 消息内容
     * @param {Object} options - 选项
     */
    showWarning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    /**
     * 显示信息通知
     * @param {string} message - 消息内容
     * @param {Object} options - 选项
     */
    showInfo(message, options = {}) {
        return this.show(message, 'info', options);
    }

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型
     * @param {Object} options - 选项
     * @returns {string} 通知ID
     */
    show(message, type = 'info', options = {}) {
        const id = this.generateId();
        const notification = this.createNotification(id, message, type, options);
        
        // 限制通知数量
        this.limitNotifications();
        
        // 添加到容器
        this.container.appendChild(notification.element);
        
        // 保存到映射
        this.notifications.set(id, notification);
        
        // 触发动画
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 10);
        
        // 自动移除
        if (options.duration !== 0) {
            const duration = options.duration || this.defaultDuration;
            setTimeout(() => {
                this.remove(id);
            }, duration);
        }
        
        return id;
    }

    /**
     * 移除通知
     * @param {string} id - 通知ID
     */
    remove(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        // 添加移除动画
        notification.element.classList.add('removing');
        
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            this.notifications.delete(id);
        }, 300);
    }

    /**
     * 清除所有通知
     */
    clear() {
        this.notifications.forEach((_, id) => {
            this.remove(id);
        });
    }

    /**
     * 显示加载通知
     * @param {string} message - 加载消息
     * @returns {string} 通知ID
     */
    showLoading(message = '正在处理...') {
        return this.show(message, 'loading', {
            duration: 0, // 不自动移除
            showSpinner: true
        });
    }

    /**
     * 显示进度通知
     * @param {string} message - 消息
     * @param {number} progress - 进度百分比 (0-100)
     * @returns {string} 通知ID
     */
    showProgress(message, progress = 0) {
        return this.show(message, 'progress', {
            duration: 0,
            progress: progress
        });
    }

    /**
     * 更新进度通知
     * @param {string} id - 通知ID
     * @param {number} progress - 新的进度百分比
     * @param {string} message - 新的消息（可选）
     */
    updateProgress(id, progress, message = null) {
        const notification = this.notifications.get(id);
        if (!notification || notification.type !== 'progress') return;
        
        const progressBar = notification.element.querySelector('.progress-bar');
        const progressText = notification.element.querySelector('.progress-text');
        
        if (progressBar) {
            progressBar.style.width = `${Math.min(100, Math.max(0, progress))}%`;
        }
        
        if (message && progressText) {
            progressText.textContent = message;
        }
        
        // 如果进度达到100%，自动移除
        if (progress >= 100) {
            setTimeout(() => {
                this.remove(id);
            }, 1000);
        }
    }

    /**
     * 显示确认对话框
     * @param {string} message - 确认消息
     * @param {Object} options - 选项
     * @returns {Promise<boolean>} 用户选择结果
     */
    showConfirm(message, options = {}) {
        return new Promise((resolve) => {
            const id = this.generateId();
            const notification = this.createConfirmNotification(id, message, options, resolve);
            
            this.container.appendChild(notification.element);
            this.notifications.set(id, notification);
            
            setTimeout(() => {
                notification.element.classList.add('show');
            }, 10);
        });
    }

    /**
     * 创建通知容器
     * @private
     */
    createContainer() {
        this.container = document.createElement('div');
        this.container.className = 'notification-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        `;
        document.body.appendChild(this.container);
    }

    /**
     * 创建通知元素
     * @param {string} id - 通知ID
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型
     * @param {Object} options - 选项
     * @returns {Object} 通知对象
     * @private
     */
    createNotification(id, message, type, options) {
        const element = document.createElement('div');
        element.className = `notification notification-${type}`;
        element.style.cssText = `
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 10px;
            padding: 16px;
            max-width: 350px;
            pointer-events: auto;
            transform: translateX(100%);
            transition: all 0.3s ease;
            opacity: 0;
        `;

        // 根据类型设置样式
        this.applyTypeStyles(element, type);

        // 创建内容
        let content = `
            <div class="notification-content">
                ${options.showSpinner ? '<div class="notification-spinner"></div>' : ''}
                <div class="notification-message">${message}</div>
            </div>
        `;

        // 添加进度条
        if (type === 'progress') {
            content += `
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${options.progress || 0}%"></div>
                </div>
                <div class="progress-text">${options.progress || 0}%</div>
            `;
        }

        // 添加关闭按钮
        if (options.closable !== false) {
            content += `
                <button class="notification-close" onclick="this.parentElement.remove()">×</button>
            `;
        }

        element.innerHTML = content;

        // 添加显示类
        const originalAdd = element.classList.add.bind(element.classList);
        element.classList.add = function(className) {
            if (className === 'show') {
                element.style.transform = 'translateX(0)';
                element.style.opacity = '1';
            } else if (className === 'removing') {
                element.style.transform = 'translateX(100%)';
                element.style.opacity = '0';
            }
            originalAdd(className);
        };

        return {
            id,
            element,
            type,
            message,
            options
        };
    }

    /**
     * 创建确认通知
     * @param {string} id - 通知ID
     * @param {string} message - 消息
     * @param {Object} options - 选项
     * @param {Function} resolve - Promise resolve 函数
     * @returns {Object} 通知对象
     * @private
     */
    createConfirmNotification(id, message, options, resolve) {
        const element = document.createElement('div');
        element.className = 'notification notification-confirm';
        element.style.cssText = `
            background: white;
            border: 2px solid #0078d4;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 10px;
            padding: 20px;
            max-width: 400px;
            pointer-events: auto;
            transform: translateX(100%);
            transition: all 0.3s ease;
            opacity: 0;
        `;

        element.innerHTML = `
            <div class="confirm-content">
                <div class="confirm-message">${message}</div>
                <div class="confirm-actions">
                    <button class="confirm-btn ms-Button ms-Button--primary">
                        ${options.confirmText || '确认'}
                    </button>
                    <button class="cancel-btn ms-Button ms-Button--secondary">
                        ${options.cancelText || '取消'}
                    </button>
                </div>
            </div>
        `;

        // 添加事件监听器
        const confirmBtn = element.querySelector('.confirm-btn');
        const cancelBtn = element.querySelector('.cancel-btn');

        confirmBtn.addEventListener('click', () => {
            this.remove(id);
            resolve(true);
        });

        cancelBtn.addEventListener('click', () => {
            this.remove(id);
            resolve(false);
        });

        return {
            id,
            element,
            type: 'confirm',
            message,
            options
        };
    }

    /**
     * 应用类型样式
     * @param {HTMLElement} element - 通知元素
     * @param {string} type - 通知类型
     * @private
     */
    applyTypeStyles(element, type) {
        const styles = {
            success: 'border-left: 4px solid #107c10; background-color: #dff6dd;',
            error: 'border-left: 4px solid #d13438; background-color: #fde7e9;',
            warning: 'border-left: 4px solid #fde047; background-color: #fff4ce;',
            info: 'border-left: 4px solid #0078d4; background-color: #deecf9;',
            loading: 'border-left: 4px solid #0078d4; background-color: #deecf9;',
            progress: 'border-left: 4px solid #0078d4; background-color: #deecf9;'
        };

        if (styles[type]) {
            element.style.cssText += styles[type];
        }
    }

    /**
     * 限制通知数量
     * @private
     */
    limitNotifications() {
        if (this.notifications.size >= this.maxNotifications) {
            const firstId = this.notifications.keys().next().value;
            this.remove(firstId);
        }
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     * @private
     */
    generateId() {
        return 'notification_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
}
