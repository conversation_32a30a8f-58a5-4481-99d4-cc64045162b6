/**
 * 快捷操作面板
 * 显示和管理快捷操作的用户界面
 */

import { QuickActionsService } from '../services/quickActionsService.js';

export class QuickActionsPanel {
    constructor() {
        this.quickActionsService = new QuickActionsService();
        this.panel = null;
        this.isVisible = false;
        this.currentCategory = 'all';
        this.searchQuery = '';
        
        this.init();
    }

    /**
     * 初始化面板
     */
    init() {
        this.quickActionsService.init();
        this.createPanel();
        this.setupEventListeners();
    }

    /**
     * 显示面板
     */
    show() {
        if (this.panel) {
            this.panel.style.display = 'block';
            this.isVisible = true;
            this.refreshActions();
        }
    }

    /**
     * 隐藏面板
     */
    hide() {
        if (this.panel) {
            this.panel.style.display = 'none';
            this.isVisible = false;
        }
    }

    /**
     * 切换面板显示状态
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * 创建面板
     * @private
     */
    createPanel() {
        this.panel = document.createElement('div');
        this.panel.className = 'quick-actions-panel';
        this.panel.innerHTML = `
            <div class="panel-header">
                <h3>🚀 快捷操作</h3>
                <button class="close-btn" id="close-quick-actions">×</button>
            </div>
            
            <div class="panel-toolbar">
                <div class="search-box">
                    <input type="text" id="action-search" placeholder="搜索操作..." />
                    <button id="search-btn">🔍</button>
                </div>
                
                <div class="category-filter">
                    <select id="category-filter">
                        <option value="all">所有分类</option>
                    </select>
                </div>
            </div>
            
            <div class="panel-tabs">
                <button class="tab-btn active" data-tab="actions">操作</button>
                <button class="tab-btn" data-tab="recent">最近</button>
                <button class="tab-btn" data-tab="custom">自定义</button>
            </div>
            
            <div class="panel-content">
                <div class="tab-content active" id="actions-tab">
                    <div class="actions-grid" id="actions-grid">
                        <!-- 操作按钮将在这里动态生成 -->
                    </div>
                </div>
                
                <div class="tab-content" id="recent-tab">
                    <div class="recent-actions" id="recent-actions">
                        <!-- 最近操作将在这里显示 -->
                    </div>
                </div>
                
                <div class="tab-content" id="custom-tab">
                    <div class="custom-actions-header">
                        <button id="add-custom-action" class="ms-Button ms-Button--primary">
                            ➕ 添加自定义操作
                        </button>
                    </div>
                    <div class="custom-actions" id="custom-actions">
                        <!-- 自定义操作将在这里显示 -->
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addPanelStyles();
        
        // 添加到页面
        document.body.appendChild(this.panel);
        
        // 初始化分类选择器
        this.initializeCategoryFilter();
        
        // 初始化操作网格
        this.refreshActions();
    }

    /**
     * 设置事件监听器
     * @private
     */
    setupEventListeners() {
        // 关闭按钮
        const closeBtn = this.panel.querySelector('#close-quick-actions');
        closeBtn.addEventListener('click', () => this.hide());

        // 搜索
        const searchInput = this.panel.querySelector('#action-search');
        const searchBtn = this.panel.querySelector('#search-btn');
        
        searchInput.addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.refreshActions();
        });
        
        searchBtn.addEventListener('click', () => {
            this.refreshActions();
        });

        // 分类筛选
        const categoryFilter = this.panel.querySelector('#category-filter');
        categoryFilter.addEventListener('change', (e) => {
            this.currentCategory = e.target.value;
            this.refreshActions();
        });

        // 标签切换
        const tabBtns = this.panel.querySelectorAll('.tab-btn');
        tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 添加自定义操作
        const addCustomBtn = this.panel.querySelector('#add-custom-action');
        addCustomBtn.addEventListener('click', () => {
            this.showAddCustomActionDialog();
        });
    }

    /**
     * 切换标签
     * @param {string} tabName - 标签名称
     * @private
     */
    switchTab(tabName) {
        // 更新标签按钮状态
        const tabBtns = this.panel.querySelectorAll('.tab-btn');
        tabBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // 更新内容显示
        const tabContents = this.panel.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-tab`);
        });

        // 刷新对应内容
        switch (tabName) {
            case 'actions':
                this.refreshActions();
                break;
            case 'recent':
                this.refreshRecentActions();
                break;
            case 'custom':
                this.refreshCustomActions();
                break;
        }
    }

    /**
     * 刷新操作列表
     * @private
     */
    refreshActions() {
        const actionsGrid = this.panel.querySelector('#actions-grid');
        actionsGrid.innerHTML = '';

        let actions;
        if (this.searchQuery) {
            actions = this.quickActionsService.searchActions(this.searchQuery);
        } else {
            actions = this.quickActionsService.getAllActions(
                this.currentCategory === 'all' ? null : this.currentCategory
            );
        }

        actions.forEach(action => {
            const actionBtn = this.createActionButton(action);
            actionsGrid.appendChild(actionBtn);
        });
    }

    /**
     * 刷新最近操作
     * @private
     */
    refreshRecentActions() {
        const recentContainer = this.panel.querySelector('#recent-actions');
        recentContainer.innerHTML = '';

        const recentActions = this.quickActionsService.getRecentActions();
        
        if (recentActions.length === 0) {
            recentContainer.innerHTML = '<p class="empty-message">暂无最近操作</p>';
            return;
        }

        recentActions.forEach(action => {
            const actionItem = this.createRecentActionItem(action);
            recentContainer.appendChild(actionItem);
        });
    }

    /**
     * 刷新自定义操作
     * @private
     */
    refreshCustomActions() {
        const customContainer = this.panel.querySelector('#custom-actions');
        customContainer.innerHTML = '';

        const customActions = this.quickActionsService.getAllActions().filter(action => action.custom);
        
        if (customActions.length === 0) {
            customContainer.innerHTML = '<p class="empty-message">暂无自定义操作</p>';
            return;
        }

        customActions.forEach(action => {
            const actionItem = this.createCustomActionItem(action);
            customContainer.appendChild(actionItem);
        });
    }

    /**
     * 创建操作按钮
     * @param {Object} action - 操作对象
     * @returns {HTMLElement} 按钮元素
     * @private
     */
    createActionButton(action) {
        const button = document.createElement('button');
        button.className = 'action-btn';
        button.innerHTML = `
            <div class="action-icon">${action.icon}</div>
            <div class="action-name">${action.name}</div>
            <div class="action-description">${action.description}</div>
        `;
        
        button.addEventListener('click', async () => {
            try {
                button.classList.add('executing');
                await this.executeAction(action.id);
                button.classList.remove('executing');
                button.classList.add('success');
                setTimeout(() => button.classList.remove('success'), 1000);
            } catch (error) {
                button.classList.remove('executing');
                button.classList.add('error');
                setTimeout(() => button.classList.remove('error'), 2000);
                console.error('执行操作失败:', error);
            }
        });

        return button;
    }

    /**
     * 创建最近操作项
     * @param {Object} action - 操作对象
     * @returns {HTMLElement} 操作项元素
     * @private
     */
    createRecentActionItem(action) {
        const item = document.createElement('div');
        item.className = 'recent-action-item';
        
        const executedTime = new Date(action.executedAt).toLocaleString();
        
        item.innerHTML = `
            <div class="action-info">
                <span class="action-icon">${action.icon}</span>
                <div class="action-details">
                    <div class="action-name">${action.name}</div>
                    <div class="action-time">执行时间: ${executedTime}</div>
                    <div class="execution-time">耗时: ${Math.round(action.executionTime)}ms</div>
                </div>
            </div>
            <button class="repeat-btn" title="重复执行">🔄</button>
        `;

        const repeatBtn = item.querySelector('.repeat-btn');
        repeatBtn.addEventListener('click', async () => {
            try {
                await this.executeAction(action.id, action.params);
            } catch (error) {
                console.error('重复执行操作失败:', error);
            }
        });

        return item;
    }

    /**
     * 创建自定义操作项
     * @param {Object} action - 操作对象
     * @returns {HTMLElement} 操作项元素
     * @private
     */
    createCustomActionItem(action) {
        const item = document.createElement('div');
        item.className = 'custom-action-item';
        
        item.innerHTML = `
            <div class="action-info">
                <span class="action-icon">${action.icon}</span>
                <div class="action-details">
                    <div class="action-name">${action.name}</div>
                    <div class="action-description">${action.description}</div>
                </div>
            </div>
            <div class="action-controls">
                <button class="edit-btn" title="编辑">✏️</button>
                <button class="delete-btn" title="删除">🗑️</button>
                <button class="execute-btn" title="执行">▶️</button>
            </div>
        `;

        // 事件监听器
        const editBtn = item.querySelector('.edit-btn');
        const deleteBtn = item.querySelector('.delete-btn');
        const executeBtn = item.querySelector('.execute-btn');

        editBtn.addEventListener('click', () => {
            this.showEditCustomActionDialog(action);
        });

        deleteBtn.addEventListener('click', () => {
            if (confirm(`确定要删除操作"${action.name}"吗？`)) {
                this.quickActionsService.deleteCustomAction(action.id);
                this.refreshCustomActions();
            }
        });

        executeBtn.addEventListener('click', async () => {
            try {
                await this.executeAction(action.id);
            } catch (error) {
                console.error('执行自定义操作失败:', error);
            }
        });

        return item;
    }

    /**
     * 执行操作
     * @param {string} actionId - 操作ID
     * @param {Object} params - 参数
     * @private
     */
    async executeAction(actionId, params = {}) {
        const result = await this.quickActionsService.executeAction(actionId, params);
        
        // 触发自定义事件
        const event = new CustomEvent('actionExecuted', {
            detail: { actionId, result, params }
        });
        document.dispatchEvent(event);
        
        return result;
    }

    /**
     * 初始化分类筛选器
     * @private
     */
    initializeCategoryFilter() {
        const categoryFilter = this.panel.querySelector('#category-filter');
        const categories = this.quickActionsService.getCategories();
        
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = this.getCategoryDisplayName(category);
            categoryFilter.appendChild(option);
        });
    }

    /**
     * 获取分类显示名称
     * @param {string} category - 分类名称
     * @returns {string} 显示名称
     * @private
     */
    getCategoryDisplayName(category) {
        const displayNames = {
            format: '格式化',
            color: '颜色',
            insert: '插入',
            paragraph: '段落',
            edit: '编辑',
            info: '信息'
        };
        return displayNames[category] || category;
    }

    /**
     * 显示添加自定义操作对话框
     * @private
     */
    showAddCustomActionDialog() {
        // 这里可以实现一个模态对话框来添加自定义操作
        const name = prompt('操作名称:');
        if (!name) return;

        const description = prompt('操作描述:');
        if (!description) return;

        const code = prompt('JavaScript 代码:');
        if (!code) return;

        const icon = prompt('图标 (emoji):') || '⚡';

        const customAction = {
            name,
            description,
            icon,
            category: 'custom',
            execute: () => {
                return WordService.executeJavaScript(code);
            }
        };

        const actionId = 'custom_' + Date.now();
        this.quickActionsService.registerCustomAction(actionId, customAction);
        this.refreshCustomActions();
    }

    /**
     * 显示编辑自定义操作对话框
     * @param {Object} action - 操作对象
     * @private
     */
    showEditCustomActionDialog(action) {
        // 简化的编辑对话框
        const newName = prompt('操作名称:', action.name);
        if (newName && newName !== action.name) {
            action.name = newName;
            this.quickActionsService.saveCustomActions();
            this.refreshCustomActions();
        }
    }

    /**
     * 添加面板样式
     * @private
     */
    addPanelStyles() {
        if (document.getElementById('quick-actions-styles')) return;

        const style = document.createElement('style');
        style.id = 'quick-actions-styles';
        style.textContent = `
            .quick-actions-panel {
                position: fixed;
                top: 50px;
                right: 20px;
                width: 350px;
                max-height: 600px;
                background: white;
                border: 1px solid #ccc;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                display: none;
                overflow: hidden;
            }

            .panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px;
                background: #f5f5f5;
                border-bottom: 1px solid #ddd;
            }

            .panel-header h3 {
                margin: 0;
                font-size: 16px;
            }

            .close-btn {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                width: 24px;
                height: 24px;
            }

            .panel-toolbar {
                padding: 10px;
                border-bottom: 1px solid #eee;
            }

            .search-box {
                display: flex;
                margin-bottom: 10px;
            }

            .search-box input {
                flex: 1;
                padding: 6px;
                border: 1px solid #ccc;
                border-radius: 4px 0 0 4px;
            }

            .search-box button {
                padding: 6px 10px;
                border: 1px solid #ccc;
                border-left: none;
                border-radius: 0 4px 4px 0;
                background: #f5f5f5;
                cursor: pointer;
            }

            .category-filter select {
                width: 100%;
                padding: 6px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }

            .panel-tabs {
                display: flex;
                background: #f5f5f5;
            }

            .tab-btn {
                flex: 1;
                padding: 10px;
                border: none;
                background: none;
                cursor: pointer;
                border-bottom: 2px solid transparent;
            }

            .tab-btn.active {
                background: white;
                border-bottom-color: #0078d4;
            }

            .panel-content {
                max-height: 400px;
                overflow-y: auto;
            }

            .tab-content {
                display: none;
                padding: 15px;
            }

            .tab-content.active {
                display: block;
            }

            .actions-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 10px;
            }

            .action-btn {
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 6px;
                background: white;
                cursor: pointer;
                text-align: center;
                transition: all 0.2s;
            }

            .action-btn:hover {
                background: #f0f0f0;
                transform: translateY(-1px);
            }

            .action-btn.executing {
                background: #fff3cd;
                border-color: #ffeaa7;
            }

            .action-btn.success {
                background: #d4edda;
                border-color: #c3e6cb;
            }

            .action-btn.error {
                background: #f8d7da;
                border-color: #f5c6cb;
            }

            .action-icon {
                font-size: 20px;
                margin-bottom: 5px;
            }

            .action-name {
                font-size: 12px;
                font-weight: bold;
                margin-bottom: 2px;
            }

            .action-description {
                font-size: 10px;
                color: #666;
                line-height: 1.2;
            }

            .recent-action-item,
            .custom-action-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px;
                border-bottom: 1px solid #eee;
            }

            .action-info {
                display: flex;
                align-items: center;
                flex: 1;
            }

            .action-info .action-icon {
                margin-right: 10px;
                font-size: 16px;
            }

            .action-details .action-name {
                font-weight: bold;
                margin-bottom: 2px;
            }

            .action-time,
            .execution-time {
                font-size: 11px;
                color: #666;
            }

            .action-controls {
                display: flex;
                gap: 5px;
            }

            .action-controls button {
                background: none;
                border: none;
                cursor: pointer;
                padding: 4px;
                border-radius: 3px;
            }

            .action-controls button:hover {
                background: #f0f0f0;
            }

            .empty-message {
                text-align: center;
                color: #666;
                font-style: italic;
                margin: 20px 0;
            }

            .custom-actions-header {
                margin-bottom: 15px;
            }
        `;
        
        document.head.appendChild(style);
    }
}
