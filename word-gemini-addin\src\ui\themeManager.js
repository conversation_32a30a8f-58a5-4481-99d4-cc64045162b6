/**
 * 主题管理器
 * 处理应用的主题切换和样式管理
 */

export class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.themes = {
            light: {
                name: '浅色主题',
                colors: {
                    primary: '#0078d4',
                    background: '#ffffff',
                    surface: '#f3f2f1',
                    text: '#323130',
                    textSecondary: '#8a8886',
                    border: '#edebe9',
                    success: '#107c10',
                    error: '#d13438',
                    warning: '#fde047',
                    info: '#0078d4'
                }
            },
            dark: {
                name: '深色主题',
                colors: {
                    primary: '#4fc3f7',
                    background: '#1e1e1e',
                    surface: '#2d2d2d',
                    text: '#ffffff',
                    textSecondary: '#cccccc',
                    border: '#404040',
                    success: '#4caf50',
                    error: '#f44336',
                    warning: '#ff9800',
                    info: '#2196f3'
                }
            },
            auto: {
                name: '跟随系统',
                colors: null // 将根据系统设置动态选择
            }
        };
        
        this.init();
    }

    /**
     * 初始化主题管理器
     */
    init() {
        this.loadSavedTheme();
        this.setupSystemThemeListener();
        this.createThemeToggle();
    }

    /**
     * 设置主题
     * @param {string} themeName - 主题名称
     */
    setTheme(themeName) {
        if (!this.themes[themeName]) {
            console.warn(`主题 "${themeName}" 不存在`);
            return;
        }

        this.currentTheme = themeName;
        
        if (themeName === 'auto') {
            this.applySystemTheme();
        } else {
            this.applyTheme(this.themes[themeName]);
        }
        
        this.saveTheme(themeName);
        this.updateThemeToggle();
        this.dispatchThemeChangeEvent();
    }

    /**
     * 获取当前主题
     * @returns {string} 当前主题名称
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * 获取可用主题列表
     * @returns {Array} 主题列表
     */
    getAvailableThemes() {
        return Object.keys(this.themes).map(key => ({
            key,
            name: this.themes[key].name
        }));
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const themes = ['light', 'dark', 'auto'];
        const currentIndex = themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;
        this.setTheme(themes[nextIndex]);
    }

    /**
     * 应用主题
     * @param {Object} theme - 主题对象
     * @private
     */
    applyTheme(theme) {
        const root = document.documentElement;
        
        // 设置 CSS 自定义属性
        Object.entries(theme.colors).forEach(([key, value]) => {
            root.style.setProperty(`--color-${key}`, value);
        });

        // 更新 body 类名
        document.body.className = document.body.className
            .replace(/theme-\w+/g, '')
            .trim();
        document.body.classList.add(`theme-${this.currentTheme}`);
    }

    /**
     * 应用系统主题
     * @private
     */
    applySystemTheme() {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const systemTheme = prefersDark ? this.themes.dark : this.themes.light;
        this.applyTheme(systemTheme);
        
        // 更新 body 类名以反映实际应用的主题
        document.body.className = document.body.className
            .replace(/theme-\w+/g, '')
            .trim();
        document.body.classList.add(`theme-${prefersDark ? 'dark' : 'light'}`);
    }

    /**
     * 设置系统主题监听器
     * @private
     */
    setupSystemThemeListener() {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', () => {
            if (this.currentTheme === 'auto') {
                this.applySystemTheme();
                this.dispatchThemeChangeEvent();
            }
        });
    }

    /**
     * 创建主题切换按钮
     * @private
     */
    createThemeToggle() {
        const toggle = document.createElement('button');
        toggle.id = 'theme-toggle';
        toggle.className = 'theme-toggle ms-Button ms-Button--secondary';
        toggle.innerHTML = this.getThemeIcon();
        toggle.title = '切换主题';
        
        toggle.addEventListener('click', () => {
            this.toggleTheme();
        });

        // 添加到页面
        const header = document.querySelector('.ms-welcome__header');
        if (header) {
            header.appendChild(toggle);
        } else {
            // 如果没有 header，添加到主容器
            const main = document.querySelector('.ms-welcome__main');
            if (main) {
                main.insertBefore(toggle, main.firstChild);
            }
        }
    }

    /**
     * 更新主题切换按钮
     * @private
     */
    updateThemeToggle() {
        const toggle = document.getElementById('theme-toggle');
        if (toggle) {
            toggle.innerHTML = this.getThemeIcon();
            toggle.title = `当前主题: ${this.themes[this.currentTheme].name}`;
        }
    }

    /**
     * 获取主题图标
     * @returns {string} 图标 HTML
     * @private
     */
    getThemeIcon() {
        const icons = {
            light: '☀️',
            dark: '🌙',
            auto: '🔄'
        };
        return icons[this.currentTheme] || '🎨';
    }

    /**
     * 加载保存的主题
     * @private
     */
    loadSavedTheme() {
        try {
            const savedTheme = localStorage.getItem('app-theme');
            if (savedTheme && this.themes[savedTheme]) {
                this.setTheme(savedTheme);
            } else {
                // 默认使用自动主题
                this.setTheme('auto');
            }
        } catch (error) {
            console.warn('无法加载保存的主题设置:', error);
            this.setTheme('light');
        }
    }

    /**
     * 保存主题设置
     * @param {string} themeName - 主题名称
     * @private
     */
    saveTheme(themeName) {
        try {
            localStorage.setItem('app-theme', themeName);
        } catch (error) {
            console.warn('无法保存主题设置:', error);
        }
    }

    /**
     * 派发主题变更事件
     * @private
     */
    dispatchThemeChangeEvent() {
        const event = new CustomEvent('themechange', {
            detail: {
                theme: this.currentTheme,
                colors: this.getCurrentColors()
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 获取当前主题颜色
     * @returns {Object} 颜色对象
     */
    getCurrentColors() {
        if (this.currentTheme === 'auto') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            return prefersDark ? this.themes.dark.colors : this.themes.light.colors;
        }
        return this.themes[this.currentTheme].colors;
    }

    /**
     * 检查是否为深色主题
     * @returns {boolean} 是否为深色主题
     */
    isDarkTheme() {
        if (this.currentTheme === 'dark') {
            return true;
        }
        if (this.currentTheme === 'auto') {
            return window.matchMedia('(prefers-color-scheme: dark)').matches;
        }
        return false;
    }

    /**
     * 添加主题变更监听器
     * @param {Function} callback - 回调函数
     */
    onThemeChange(callback) {
        document.addEventListener('themechange', callback);
    }

    /**
     * 移除主题变更监听器
     * @param {Function} callback - 回调函数
     */
    offThemeChange(callback) {
        document.removeEventListener('themechange', callback);
    }
}
