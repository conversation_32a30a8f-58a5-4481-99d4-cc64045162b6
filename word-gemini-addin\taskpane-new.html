<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>AI格式转换</title>
    
    <!-- Office JavaScript API -->
    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js"></script>
    
    <!-- 样式 -->
    <link rel="stylesheet" href="taskpane.css">
    
    <!-- 外部依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <!-- 应用样式增强 -->
    <style>
        /* 内联关键样式以避免 FOUC */
        .app-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
    </style>
</head>

<body>
    <div class="ms-welcome">
        <!-- 删除了 header 部分，包括 logo 和标题 -->
        
        <main class="ms-welcome__main">
            <!-- API Key 配置区域 -->
            <div id="api-config" class="config-section">
                <h3>API配置</h3>
                <div class="input-group">
                    <label for="api-key">通义千问 API Key:</label>
                    <input type="password" id="api-key" placeholder="输入您的通义千问 API Key">
                    <button id="save-api-key" class="ms-Button ms-Button--primary">
                        <span class="ms-Button-label">保存</span>
                    </button>
                </div>
            </div>
            
            <!-- 聊天界面 -->
            <div id="chat-container" class="chat-container" style="display: none;">
                <!-- 模式选择区域 -->
                <div class="mode-selection">
                    <h4>操作模式</h4>
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="mode" value="normal" checked>
                            <span>普通对话</span>
                        </label>
                        <label>
                            <input type="radio" name="mode" value="js-direct">
                            <span>直接生成JavaScript</span>
                        </label>
                    </div>
                    <div class="mode-info"></div>
                </div>
                
                <!-- 聊天消息区域 -->
                <div id="chat-messages" class="chat-messages">
                    <!-- 消息将动态添加到这里 -->
                </div>
                
                <!-- 输入区域 -->
                <div class="chat-input-container">
                    <div class="input-area">
                        <textarea id="user-input" placeholder="请输入您的需求..."></textarea>
                        <button id="send-message" class="ms-Button ms-Button--primary">
                            <span class="ms-Button-label">发送</span>
                        </button>
                    </div>
                    
                    <div class="action-buttons">
                        <button id="insert-to-doc" class="ms-Button ms-Button--secondary" disabled>
                            <span class="ms-Button-label">📄 插入到文档</span>
                        </button>
                        <button id="execute-js-code" class="ms-Button ms-Button--secondary" disabled>
                            <span class="ms-Button-label">⚡ 执行代码</span>
                        </button>
                        <button id="show-quick-actions" class="ms-Button ms-Button--secondary">
                            <span class="ms-Button-label">🚀 快捷操作</span>
                        </button>
                        <button id="show-history" class="ms-Button ms-Button--secondary">
                            <span class="ms-Button-label">📚 历史记录</span>
                        </button>
                        <button id="show-performance" class="ms-Button ms-Button--secondary">
                            <span class="ms-Button-label">📊 性能监控</span>
                        </button>
                        <button id="clear-chat" class="ms-Button ms-Button--secondary">
                            <span class="ms-Button-label">🗑️ 清空对话</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 状态显示 -->
            <div id="status" class="status-message"></div>
        </main>
    </div>
    
    <!-- 应用脚本将由 webpack 自动注入 -->
</body>
</html>
