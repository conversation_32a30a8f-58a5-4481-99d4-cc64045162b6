/* CSS 自定义属性（主题变量） */
:root {
    /* 浅色主题（默认） */
    --color-primary: #0078d4;
    --color-background: #ffffff;
    --color-surface: #f3f2f1;
    --color-text: #323130;
    --color-text-secondary: #8a8886;
    --color-border: #edebe9;
    --color-success: #107c10;
    --color-error: #d13438;
    --color-warning: #fde047;
    --color-info: #0078d4;
}

/* 深色主题 */
.theme-dark {
    --color-primary: #4fc3f7;
    --color-background: #1e1e1e;
    --color-surface: #2d2d2d;
    --color-text: #ffffff;
    --color-text-secondary: #cccccc;
    --color-border: #404040;
    --color-success: #4caf50;
    --color-error: #f44336;
    --color-warning: #ff9800;
    --color-info: #2196f3;
}

/* Office UI Fabric 样式 */
html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-sizing: border-box;
    background-color: var(--color-background);
    color: var(--color-text);
    transition: background-color 0.3s ease, color 0.3s ease;
}

*, *::before, *::after {
    box-sizing: inherit;
}

.ms-welcome {
    position: relative;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    height: 100vh; /* 使用 vh 确保占满整个视口高度 */
}

.ms-welcome__header {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background-color: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    flex-shrink: 0;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.ms-welcome__header img {
    margin-bottom: 10px;
}

.ms-welcome__header h1 {
    margin: 0;
    color: var(--color-text);
    font-size: 24px;
    font-weight: 300;
    transition: color 0.3s ease;
}

.ms-welcome__main {
    flex-grow: 1; /* 占据剩余所有空间 */
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow-y: hidden; /* 防止主区域自身出现滚动条 */
}

/* 配置区域样式 */
.config-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #edebe9;
    border-radius: 4px;
    background-color: #faf9f8;
}

.config-section h3 {
    margin: 0 0 15px 0;
    color: #323130;
    font-size: 16px;
    font-weight: 600;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-group label {
    font-size: 14px;
    color: #323130;
    font-weight: 500;
}

.input-group input, .input-group textarea {
    padding: 8px 12px;
    border: 1px solid #8a8886;
    border-radius: 2px;
    font-size: 14px;
    font-family: inherit;
}

.input-group input:focus, .input-group textarea:focus {
    outline: none;
    border-color: #0078d4;
    box-shadow: 0 0 0 1px #0078d4;
}

/* 聊天界面样式 */
.chat-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0; /* 允许 flex item 缩小 */
}

.chat-messages {
    flex-grow: 1; /* 占据聊天容器中的所有可用空间 */
    overflow-y: auto;
    padding: 15px;
    border: 1px solid #edebe9;
    border-radius: 4px;
    background-color: #ffffff;
    margin-bottom: 15px;
    min-height: 100px; /* 设定一个最小高度 */
}

.message {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
    max-width: 90%; /* 稍微加宽一点 */
    word-wrap: break-word;
    white-space: pre-wrap; /* 允许换行符和空格被保留，同时自动换行 */
    line-height: 1.4;
}

.message.user {
    background-color: #0078d4;
    color: white;
    margin-left: auto;
    text-align: left; /* 用户消息也左对齐，但通过margin-left推到右边 */
}

.message.assistant {
    background-color: #f3f2f1;
    color: #323130;
    margin-right: auto;
}

.message.system {
    background-color: #fff4ce;
    color: #8a6d3b;
    border: 1px solid #ffeeba;
    text-align: left;
    font-style: italic;
    margin: 10px auto;
    width: 100%;
    max-width: 100%;
}

.chat-input-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    flex-shrink: 0; /* 防止输入区域被压缩 */
}

/* 在你的 taskpane.css 文件中添加 */
#loader {
    display: none; /* 默认隐藏 */
    border: 4px solid #f3f3f3;
    border-radius: 50%;
    border-top: 4px solid #3498db;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.chat-input-container .input-group {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    gap: 10px;
    width: 100%;
}

.input-area {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    gap: 10px;
    width: 100%;
}

.chat-input-container textarea {
    flex: 1;
    resize: vertical;
    min-height: 60px;
    max-height: 150px; /* 增加最大高度 */
    width: 100%;
    box-sizing: border-box;
    margin: 0;
}

.chat-input-container button {
    flex-shrink: 0;
    min-width: 60px;
    height: fit-content;
    align-self: flex-end; /* 让按钮和文本框底部对齐 */
    margin-bottom: 2px; /* 细微调整对齐 */
}

.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-start; /* 从左边开始排列 */
    flex-wrap: wrap; /* 允许换行 */
    width: 100%;
}

/* 按钮样式 */
.ms-Button {
    padding: 8px 16px;
    border: 1px solid #8a8886;
    border-radius: 2px;
    background-color: #ffffff;
    color: #323130;
    font-size: 14px;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.ms-Button:hover {
    background-color: #f3f2f1;
    border-color: #323130;
}

.ms-Button--primary {
    background-color: #0078d4;
    border-color: #0078d4;
    color: #ffffff;
}

.ms-Button--primary:hover {
    background-color: #106ebe;
    border-color: #106ebe;
}

.ms-Button--secondary {
    background-color: #f3f2f1;
    border-color: #8a8886;
    color: #323130;
}

.ms-Button--secondary:hover {
    background-color: #edebe9;
}

.ms-Button:disabled {
    background-color: #f3f2f1;
    border-color: #c8c6c4;
    color: #a19f9d;
    cursor: not-allowed;
}

/* 状态消息 */
.status-message {
    padding: 10px;
    margin-top: 10px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    flex-shrink: 0;
    min-height: 42px; /* 给一个固定高度防止布局跳动 */
    line-height: 22px;
}

.status-message:not(:empty) {
    border-width: 1px;
    border-style: solid;
}

.status-message.success {
    background-color: #dff6dd;
    color: #107c10;
    border-color: #107c10;
}

.status-message.error {
    background-color: #fde7e9;
    color: #d13438;
    border-color: #d13438;
}

.status-message.info {
    background-color: #deecf9;
    color: #0078d4;
    border-color: #0078d4;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3); /* 用于深色背景的加载动画 */
    border-top: 3px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.ms-Button--secondary .loading {
     border: 3px solid rgba(0, 0, 0, 0.2);
     border-top: 3px solid #323130;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模式选择样式 */
.mode-selection {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #edebe9;
    border-radius: 4px;
    background-color: #faf9f8;
    flex-shrink: 0;
}

.mode-selection h4 {
    margin: 0 0 10px 0;
    color: #323130;
    font-size: 14px;
    font-weight: 600;
}

.radio-group {
    display: flex;
    gap: 20px;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-size: 14px;
    color: #323130;
}

.radio-group input[type="radio"] {
    margin: 0;
}

/* JavaScript 代码显示样式 */
pre.code-block { /* 使用pre标签更好地保留格式 */
    background-color: #2d2d2d; /* 深色背景 */
    color: #cccccc; /* 浅色文字 */
    border: 1px solid #444;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    white-space: pre-wrap;   /* 允许自动换行 */
    word-wrap: break-word;   /* 强制长单词换行 */
    overflow-x: auto;
}

.conversion-step {
    margin: 15px 0;
    padding: 10px;
    border-left: 3px solid #0078d4;
    background-color: #f3f2f1; /* 使用浅灰色背景 */
}

.step-title {
    font-weight: bold;
    color: #323130; /* 深色标题更易读 */
    margin-bottom: 5px;
}

/* 应用加载状态 */
.app-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    padding: 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* 错误状态样式 */
.host-error,
.initialization-error {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.error-content {
    text-align: center;
    padding: 40px;
    max-width: 500px;
    border: 1px solid #d13438;
    border-radius: 8px;
    background-color: #fde7e9;
}

.error-content h2 {
    color: #d13438;
    margin-bottom: 20px;
    font-size: 24px;
}

.error-content p {
    color: #323130;
    margin-bottom: 15px;
    line-height: 1.5;
}

.error-details {
    background-color: #f3f2f1;
    border: 1px solid #edebe9;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    text-align: left;
    white-space: pre-wrap;
    overflow-x: auto;
}

.error-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

/* 消息时间戳 */
.message-timestamp {
    font-size: 11px;
    color: #8a8886;
    margin-top: 5px;
    text-align: right;
}

.message.user .message-timestamp {
    color: rgba(255, 255, 255, 0.7);
}

/* 代码操作按钮 */
.code-actions {
    margin-top: 10px;
    text-align: right;
}

.copy-code-btn {
    font-size: 12px;
    padding: 4px 8px;
}

/* 加载消息样式 */
.loading-message {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.loading-message.show {
    opacity: 0.8;
    transform: translateY(0);
}

.loading-message.fade-out {
    opacity: 0;
    transform: translateY(-10px);
}

.loading-message .loading-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.loading-message .loading-spinner {
    width: 16px;
    height: 16px;
    border-width: 2px;
    margin: 0;
}

/* 打字指示器样式 */
.typing-indicator {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.typing-indicator.show {
    opacity: 0.8;
    transform: translateY(0);
}

.typing-indicator.fade-out {
    opacity: 0;
    transform: translateY(-10px);
}

.typing-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background-color: #8a8886;
    border-radius: 50%;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.typing-text {
    font-style: italic;
    color: #8a8886;
    font-size: 13px;
}

/* 消息动画增强 */
.message {
    opacity: 0;
    transform: translateY(10px);
    animation: messageSlideIn 0.3s ease forwards;
}

@keyframes messageSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 消息悬停效果 */
.message:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
}

/* 代码块复制按钮悬停效果 */
.copy-code-btn:hover {
    background-color: var(--color-surface);
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* 主题切换按钮样式 */
.theme-toggle {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid var(--color-border);
    background-color: var(--color-background);
    color: var(--color-text);
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 100;
}

.theme-toggle:hover {
    background-color: var(--color-surface);
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.theme-toggle:active {
    transform: scale(0.95);
}

/* 主题过渡动画 */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 深色主题特定样式 */
.theme-dark .chat-messages {
    background-color: var(--color-surface);
    border-color: var(--color-border);
}

.theme-dark .message.user {
    background-color: var(--color-primary);
}

.theme-dark .message.assistant {
    background-color: var(--color-surface);
    color: var(--color-text);
}

.theme-dark .code-block {
    background-color: #1a1a1a;
    color: #e0e0e0;
    border-color: var(--color-border);
}

.theme-dark .loading-spinner {
    border-color: var(--color-surface);
    border-top-color: var(--color-primary);
}

/* 模式信息显示 */
.mode-info {
    font-size: 12px;
    color: #8a8886;
    margin-top: 5px;
    font-style: italic;
}

/* 改进的滚动条样式 */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 重试选项样式 */
.retry-option {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #ffffff;
    border: 1px solid #d13438;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    animation: slideInUp 0.3s ease-out;
}

.retry-content {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.retry-content span {
    color: #323130;
    margin-right: 10px;
}

.retry-btn,
.cancel-btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid;
    cursor: pointer;
    transition: all 0.2s ease;
}

.retry-btn {
    background-color: #0078d4;
    border-color: #0078d4;
    color: white;
}

.retry-btn:hover {
    background-color: #106ebe;
}

.cancel-btn {
    background-color: #f3f2f1;
    border-color: #8a8886;
    color: #323130;
}

.cancel-btn:hover {
    background-color: #edebe9;
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 错误详情展开/折叠 */
.error-details-toggle {
    background: none;
    border: none;
    color: #0078d4;
    cursor: pointer;
    font-size: 12px;
    margin-top: 10px;
    text-decoration: underline;
}

.error-details-toggle:hover {
    color: #106ebe;
}

.error-details-expanded {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f8f8;
    border-radius: 4px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 11px;
    max-height: 200px;
    overflow-y: auto;
}

/* 错误统计显示 */
.error-stats {
    display: flex;
    gap: 15px;
    margin: 10px 0;
    padding: 10px;
    background-color: #fff4ce;
    border-radius: 4px;
    font-size: 12px;
}

.error-stat-item {
    text-align: center;
}

.error-stat-number {
    font-size: 16px;
    font-weight: bold;
    color: #d13438;
}

.error-stat-label {
    color: #8a6d3b;
    margin-top: 2px;
}

/* 改进的通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 300px;
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    animation: slideInRight 0.3s ease-out;
}

.notification.success {
    background-color: #dff6dd;
    border-left: 4px solid #107c10;
    color: #107c10;
}

.notification.error {
    background-color: #fde7e9;
    border-left: 4px solid #d13438;
    color: #d13438;
}

.notification.warning {
    background-color: #fff4ce;
    border-left: 4px solid #fde047;
    color: #8a6d3b;
}

.notification.info {
    background-color: #deecf9;
    border-left: 4px solid #0078d4;
    color: #0078d4;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .ms-welcome__main {
        padding: 10px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .error-content {
        padding: 20px;
        margin: 20px;
    }

    .error-actions {
        flex-direction: column;
    }

    .radio-group {
        flex-direction: column;
        gap: 10px;
    }

    .retry-option {
        bottom: 10px;
        right: 10px;
        left: 10px;
    }

    .retry-content {
        flex-direction: column;
        text-align: center;
    }

    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .error-stats {
        flex-direction: column;
        gap: 10px;
    }
}