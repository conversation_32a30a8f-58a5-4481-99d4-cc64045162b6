/* Office UI Fabric 样式 */
html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-sizing: border-box; /* 确保 padding 和 border 不会影响元素总宽度和高度 */
}

*, *::before, *::after {
    box-sizing: inherit;
}

.ms-welcome {
    position: relative;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    height: 100vh; /* 使用 vh 确保占满整个视口高度 */
}

.ms-welcome__header {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background-color: #f3f2f1;
    border-bottom: 1px solid #edebe9;
    flex-shrink: 0; /* 防止头部被压缩 */
}

.ms-welcome__header img {
    margin-bottom: 10px;
}

.ms-welcome__header h1 {
    margin: 0;
    color: #323130;
    font-size: 24px;
    font-weight: 300;
}

.ms-welcome__main {
    flex-grow: 1; /* 占据剩余所有空间 */
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow-y: hidden; /* 防止主区域自身出现滚动条 */
}

/* 配置区域样式 */
.config-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #edebe9;
    border-radius: 4px;
    background-color: #faf9f8;
}

.config-section h3 {
    margin: 0 0 15px 0;
    color: #323130;
    font-size: 16px;
    font-weight: 600;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-group label {
    font-size: 14px;
    color: #323130;
    font-weight: 500;
}

.input-group input, .input-group textarea {
    padding: 8px 12px;
    border: 1px solid #8a8886;
    border-radius: 2px;
    font-size: 14px;
    font-family: inherit;
}

.input-group input:focus, .input-group textarea:focus {
    outline: none;
    border-color: #0078d4;
    box-shadow: 0 0 0 1px #0078d4;
}

/* 聊天界面样式 */
.chat-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0; /* 允许 flex item 缩小 */
}

.chat-messages {
    flex-grow: 1; /* 占据聊天容器中的所有可用空间 */
    overflow-y: auto;
    padding: 15px;
    border: 1px solid #edebe9;
    border-radius: 4px;
    background-color: #ffffff;
    margin-bottom: 15px;
    min-height: 100px; /* 设定一个最小高度 */
}

.message {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
    max-width: 90%; /* 稍微加宽一点 */
    word-wrap: break-word;
    white-space: pre-wrap; /* 允许换行符和空格被保留，同时自动换行 */
    line-height: 1.4;
}

.message.user {
    background-color: #0078d4;
    color: white;
    margin-left: auto;
    text-align: left; /* 用户消息也左对齐，但通过margin-left推到右边 */
}

.message.assistant {
    background-color: #f3f2f1;
    color: #323130;
    margin-right: auto;
}

.message.system {
    background-color: #fff4ce;
    color: #8a6d3b;
    border: 1px solid #ffeeba;
    text-align: left;
    font-style: italic;
    margin: 10px auto;
    width: 100%;
    max-width: 100%;
}

.chat-input-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    flex-shrink: 0; /* 防止输入区域被压缩 */
}

/* 在你的 taskpane.css 文件中添加 */
#loader {
    display: none; /* 默认隐藏 */
    border: 4px solid #f3f3f3;
    border-radius: 50%;
    border-top: 4px solid #3498db;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.chat-input-container .input-group {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    gap: 10px;
    width: 100%;
}

.input-area {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    gap: 10px;
    width: 100%;
}

.chat-input-container textarea {
    flex: 1;
    resize: vertical;
    min-height: 60px;
    max-height: 150px; /* 增加最大高度 */
    width: 100%;
    box-sizing: border-box;
    margin: 0;
}

.chat-input-container button {
    flex-shrink: 0;
    min-width: 60px;
    height: fit-content;
    align-self: flex-end; /* 让按钮和文本框底部对齐 */
    margin-bottom: 2px; /* 细微调整对齐 */
}

.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-start; /* 从左边开始排列 */
    flex-wrap: wrap; /* 允许换行 */
    width: 100%;
}

/* 按钮样式 */
.ms-Button {
    padding: 8px 16px;
    border: 1px solid #8a8886;
    border-radius: 2px;
    background-color: #ffffff;
    color: #323130;
    font-size: 14px;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.ms-Button:hover {
    background-color: #f3f2f1;
    border-color: #323130;
}

.ms-Button--primary {
    background-color: #0078d4;
    border-color: #0078d4;
    color: #ffffff;
}

.ms-Button--primary:hover {
    background-color: #106ebe;
    border-color: #106ebe;
}

.ms-Button--secondary {
    background-color: #f3f2f1;
    border-color: #8a8886;
    color: #323130;
}

.ms-Button--secondary:hover {
    background-color: #edebe9;
}

.ms-Button:disabled {
    background-color: #f3f2f1;
    border-color: #c8c6c4;
    color: #a19f9d;
    cursor: not-allowed;
}

/* 状态消息 */
.status-message {
    padding: 10px;
    margin-top: 10px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    flex-shrink: 0;
    min-height: 42px; /* 给一个固定高度防止布局跳动 */
    line-height: 22px;
}

.status-message:not(:empty) {
    border-width: 1px;
    border-style: solid;
}

.status-message.success {
    background-color: #dff6dd;
    color: #107c10;
    border-color: #107c10;
}

.status-message.error {
    background-color: #fde7e9;
    color: #d13438;
    border-color: #d13438;
}

.status-message.info {
    background-color: #deecf9;
    color: #0078d4;
    border-color: #0078d4;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3); /* 用于深色背景的加载动画 */
    border-top: 3px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.ms-Button--secondary .loading {
     border: 3px solid rgba(0, 0, 0, 0.2);
     border-top: 3px solid #323130;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模式选择样式 */
.mode-selection {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #edebe9;
    border-radius: 4px;
    background-color: #faf9f8;
    flex-shrink: 0;
}

.mode-selection h4 {
    margin: 0 0 10px 0;
    color: #323130;
    font-size: 14px;
    font-weight: 600;
}

.radio-group {
    display: flex;
    gap: 20px;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-size: 14px;
    color: #323130;
}

.radio-group input[type="radio"] {
    margin: 0;
}

/* JavaScript 代码显示样式 */
pre.code-block { /* 使用pre标签更好地保留格式 */
    background-color: #2d2d2d; /* 深色背景 */
    color: #cccccc; /* 浅色文字 */
    border: 1px solid #444;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    white-space: pre-wrap;   /* 允许自动换行 */
    word-wrap: break-word;   /* 强制长单词换行 */
    overflow-x: auto;
}

.conversion-step {
    margin: 15px 0;
    padding: 10px;
    border-left: 3px solid #0078d4;
    background-color: #f3f2f1; /* 使用浅灰色背景 */
}

.step-title {
    font-weight: bold;
    color: #323130; /* 深色标题更易读 */
    margin-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .ms-welcome__main {
        padding: 10px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }
}