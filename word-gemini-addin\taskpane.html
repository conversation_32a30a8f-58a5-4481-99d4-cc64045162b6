<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>AI格式转换</title>
    
    <!-- Office JavaScript API -->
    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js"></script>
    
    <!-- 样式 -->
    <link rel="stylesheet" href="taskpane.css">
    <!-- 引入 marked.js -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>

<body>
    <div class="ms-welcome">
        <!-- 删除了 header 部分，包括 logo 和标题 -->
        
        <main class="ms-welcome__main">
            <!-- API Key 配置区域 -->
            <div id="api-config" class="config-section">
                <h3>API配置</h3>
                <div class="input-group">
                    <label for="api-key">通义千问 API Key:</label>
                    <input type="password" id="api-key" placeholder="输入您的通义千问 API Key">
                    <button id="save-api-key" class="ms-Button ms-Button--primary">
                        <span class="ms-Button-label">保存</span>
                    </button>
                </div>
            </div>
            
            <!-- 聊天界面 -->
            <div id="chat-container" class="chat-container" style="display: none;">
                <!-- 模式选择区域 -->
                <div class="mode-selection">
                    <h4>操作模式</h4>
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="mode" value="normal" checked>
                            <span>普通对话</span>
                        </label>
                        <label>
                            <input type="radio" name="mode" value="js-direct">
                            <span>直接生成JavaScript</span>
                        </label>
                    </div>
                </div>
                
                <div id="chat-messages" class="chat-messages"></div>
                
                <div class="chat-input-container">
                    <div class="input-area">
                        <textarea id="user-input" placeholder="输入您的需求..."></textarea>
                        <button id="send-message">发送</button>
                        <div id="loader"></div> <!-- 添加加载器 -->
                    </div>
                    
                    <div class="action-buttons">
                        <!-- 删除了“插入到文档”和“执行代码”按钮 -->
                        <button id="clear-chat" class="ms-Button ms-Button--secondary">
                            <span class="ms-Button-label">清空对话</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 状态显示 -->
            <div id="status" class="status-message"></div>
        </main>
    </div>
    
    <script src="taskpane.js"></script>
</body>
</html>