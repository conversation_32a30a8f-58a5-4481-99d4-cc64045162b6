/* global Office */

Office.onReady((info) => {
    if (info.host === Office.HostType.Word) {
        // 确保DOM完全加载后再绑定事件
        document.addEventListener('DOMContentLoaded', function() {
            initializeAddin();
        });
        
        // 如果DOM已经加载完成，直接初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeAddin);
        } else {
            initializeAddin();
        }
    }
});

function initializeAddin() {
    try {
        document.getElementById("save-api-key").onclick = saveApiKey;
        document.getElementById("send-message").onclick = sendMessage;
        document.getElementById("insert-to-doc").onclick = insertToDocument;
        document.getElementById("execute-js-code").onclick = executeJavaScriptCode;
        document.getElementById("clear-chat").onclick = clearChat;

        // 回车发送消息
        document.getElementById("user-input").addEventListener("keypress", function(event) {
            if (event.key === "Enter" && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        });

        // 监听模式切换，以决定是否显示"执行代码"按钮
        document.querySelectorAll('input[name="mode"]').forEach(radio => {
            radio.addEventListener('change', handleModeChange);
        });

        // 检查是否已保存API Key
        checkApiKey();
    } catch (error) {
        console.error('初始化失败:', error);
    }
}

class WordGeminiAddin {
    constructor() {
        this.apiKey = null;
        this.chatHistory = []; // 用于普通对话模式
        this.lastResponse = '';
        this.lastJavaScriptCode = '';
    }

    /**
     * 清理AI生成的代码，移除Markdown标记和可能的解释文字。
     * @param {string} code - AI返回的原始代码字符串。
     * @param {string} mode - 'js'，用于JavaScript代码清理策略。
     * @returns {string} 清理后的纯代码。
     */
    cleanAiOutput(code, mode = 'js') {
        let cleanCode = code.trim();
        
        // 移除Markdown代码块标记
        cleanCode = cleanCode.replace(/^```(?:javascript|js)?\s*\n?/im, '');
        cleanCode = cleanCode.replace(/\n?```\s*$/im, '');
        cleanCode = cleanCode.trim();
    
        // 针对JS代码的特殊处理
        if (mode === 'js') {
            // 检查是否已经包含executeUserCode函数
            const functionIndex = cleanCode.indexOf('async function executeUserCode');
            if (functionIndex >= 0) {
                // 如果前面有解释文字，截取掉
                if (functionIndex > 0) {
                    cleanCode = cleanCode.substring(functionIndex);
                }
            } else {
                // 如果AI没有生成executeUserCode函数，我们需要包装代码
                // 首先移除可能的Word.run包装
                cleanCode = cleanCode.replace(/^Word\.run\(async \(context\) => \{\s*/im, '');
                cleanCode = cleanCode.replace(/\s*\}\);?\s*$/im, '');
                cleanCode = cleanCode.trim();
                
                // 移除可能的多余的async (context) => { 包装
                cleanCode = cleanCode.replace(/^async \(context\) => \{\s*/im, '');
                cleanCode = cleanCode.replace(/\s*\}\s*$/im, '');
                cleanCode = cleanCode.trim();
                
                // 确保代码安全性 - 添加基本的错误检查
                const safeCode = `
                    // 安全检查和基本操作
                    const selection = context.document.getSelection();
                    ${cleanCode}
                    await context.sync();
                `;
                
                // 包装成executeUserCode函数
                cleanCode = `async function executeUserCode(context) {
    try {
        ${safeCode.replace(/\n/g, '\n        ')}
    } catch (error) {
        console.error('代码执行错误:', error);
        throw error;
    }
}`;
            }
        }
        
        return cleanCode;
    }

    async callGeminiAPI(message) {
        try {
            const response = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                    model: "qwen-plus",
                    messages: [
                        ...this.chatHistory,
                        {
                            role: "user",
                            content: message
                        }
                    ],
                    temperature: 0.7,
                    max_tokens: 1024
                })
            });

            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }

            const data = await response.json();
            if (data.choices && data.choices[0] && data.choices[0].message) {
                return data.choices[0].message.content;
            } else {
                throw new Error('API返回格式错误');
            }
        } catch (error) {
            console.error('通义千问API调用错误:', error);
            throw error;
        }
    }

    addMessageWithCode(content, type, jsCode = null) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        
        if (jsCode) {
            messageDiv.innerHTML = `
                <div>${this.escapeHtml(content)}</div>
                <div class="conversion-step">
                    <div class="step-title">生成的JavaScript代码</div>
                    <pre class="code-block">${this.escapeHtml(jsCode)}</pre>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `<div>${this.escapeHtml(content)}</div>`;
        }
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    escapeHtml(text) {
        return text.replace(/&/g, "&amp;")
                   .replace(/</g, "&lt;")
                   .replace(/>/g, "&gt;")
                   .replace(/"/g, "&quot;")
                   .replace(/'/g, "&#39;");
    }

    addMessage(content, type) {
        this.addMessageWithCode(content, type);
    }

    showStatus(message, type = 'info') {
        const statusDiv = document.getElementById('status');
        statusDiv.textContent = message;
        statusDiv.className = `status-message ${type}`;
        
        // 3秒后自动清除状态
        setTimeout(() => {
            statusDiv.textContent = '';
            statusDiv.className = 'status-message';
        }, 3000);
    }

    async generateWordJavaScript(userRequest) {
        // 彻底修复的提示词，严格禁止不存在的API
        const prompt = `你是一个专业的Word JavaScript API开发助手。请根据用户需求生成安全可靠的Word JavaScript代码。

用户需求：${userRequest}

## 严格禁止使用的方法（这些方法在Word JavaScript API中不存在）：
❌ getByText() - 此方法完全不存在于任何对象上（如 body, range, paragraph 等）
❌ getSubrange() - 此方法完全不存在
❌ range.getSubrange() - 此方法完全不存在
❌ paragraph.getRange().getSubrange() - 此方法完全不存在
❌ selection.getSubrange() - 此方法完全不存在
❌ 任何包含 "getSubrange" 的方法调用
❌ 任何包含 "getByText" 的方法调用

## 黄金法则：如何查找文本 (必须遵循)
当需要查找文本时，**必须**使用 \`search\` 方法。这是唯一受支持的文本查找方式。

### ✅ 正确的查找示例:
\`\`\`javascript
const searchResults = context.document.body.search('要查找的文本', { matchCase: false, matchWholeWord: true });
context.load(searchResults, 'items'); // 必须加载items属性
await context.sync();

if (searchResults.items.length > 0) {
    // 对找到的第一个结果进行操作
    const firstResult = searchResults.items[0];
    firstResult.font.color = 'red'; // 操作前确保font属性已加载
}
\`\`\`

## 必须遵循的规则：
1. 必须生成一个名为 executeUserCode 的异步函数
2. 函数接受一个 context 参数
3. 所有对象访问前必须检查是否存在
4. 在访问对象属性前必须先调用 context.load() 加载需要的属性
5. 在操作完成后必须调用 await context.sync()
6. 包含完整的错误处理
7. 只返回纯JavaScript代码，不要包含解释文字
8. 只使用经过验证的Word JavaScript API方法

## 字符类型识别与处理规则（重要！）：
    ### 📝 字符识别模式：
    1. **中文字符识别**：
       - 中文汉字范围：\\u4e00-\\u9fff
       - 中文标点：\\u3000-\\u303f
       - 识别模式：/[\\u4e00-\\u9fff\\u3000-\\u303f]/
       - 示例："你好"、"，"、"。"
    
    2. **英文字符识别**：
       - 英文字母：a-zA-Z
       - 英文标点：常见ASCII标点
       - 识别模式：/[a-zA-Z\\s.,!?;:"'()\[\]{}]/
       - 示例："Hello"、"world!"
    
    3. **数字字符识别**：
       - 阿拉伯数字：0-9
       - 数学符号：+、-、*、/、=、%等
       - 识别模式：/[0-9+\\-*/=%.()]/ 
       - 示例："123"、"3.14"、"100%"
    
    4. **混合文本处理**：
       - 当文本包含多种字符类型时，需要分别处理
       - 使用正则表达式精确匹配每种类型
       - 保持原有的字符类型和格式
    
    ### 🎯 处理策略：
    - **精确替换**：只替换指定类型的字符，保持其他字符不变
    - **类型保持**：维持原始字符的类型属性（中文保持中文，英文保持英文）
    - **格式保留**：保持原有的空格、换行、标点等格式

    ### 💡 针对“把所有中文变成微软雅黑”的示例：
    当用户要求“把所有中文变成微软雅黑”时，AI生成的代码必须遍历文档中的所有文本，并使用正则表达式 \`/[\\u4e00-\\u9fff\\u3000-\\u303f]/g\` 来精确匹配中文内容。对于每个匹配到的中文范围，才应用字体格式。**绝不能**对整个文档或段落直接应用格式，而必须是针对匹配到的中文文本范围。

    **错误示例（AI不应生成此类代码）：**
    \`\`\`javascript
    // 错误：直接对整个文档应用字体，不区分中英文
    context.document.body.font.name = "微软雅黑";
    \`\`\`

    **正确示例（AI应生成此类代码）：**
    \`\`\`javascript
    await Word.run(async (context) => {
        const body = context.document.body;
        const ranges = body.search(/[\\u4e00-\\u9fff\\u3000-\\u303f]/g, { matchCase: false });
        context.load(ranges, 'items');
        await context.sync();

        if (ranges.items.length > 0) { // 确保有匹配项才进行操作
            for (let i = 0; i < ranges.items.length; i++) {
                ranges.items[i].font.name = "微软雅黑";
            }
            await context.sync();
        } else {
            console.log("未找到中文内容。");
        }
    });
    \`\`\`

## Word API 正确使用模式（只使用这些方法）：

### ✅ 获取和操作选区：
\`\`\`javascript
const selection = context.document.getSelection();
context.load(selection, 'text');
await context.sync();
if (selection) {
    selection.insertText("新文本", Word.InsertLocation.replace);
    await context.sync();
}
\`\`\`

### ✅ 获取段落集合：
\`\`\`javascript
const paragraphs = context.document.body.paragraphs;
context.load(paragraphs, 'text');
await context.sync();
if (paragraphs && paragraphs.items.length > 0) {
    const firstParagraph = paragraphs.items[0];
    context.load(firstParagraph, 'text');
    await context.sync();
    firstParagraph.insertText("新段落文本", Word.InsertLocation.end);
    await context.sync();
}
\`\`\`

### ✅ 获取段落范围（正确方法）：
\`\`\`javascript
const paragraphs = context.document.body.paragraphs;
context.load(paragraphs);
await context.sync();
if (paragraphs && paragraphs.items.length > 0) {
    const paragraph = paragraphs.items[0];
    const range = paragraph.getRange(); // 正确：直接获取段落的完整范围
    context.load(range, 'text');
    await context.sync();
    // 对range进行操作，但不要调用getSubrange
    range.insertText("插入文本", Word.InsertLocation.end);
    await context.sync();
}
\`\`\`

### ✅ 查找和替换：
\`\`\`javascript
const searchResults = context.document.body.search("查找文本");
context.load(searchResults);
await context.sync();
if (searchResults && searchResults.items.length > 0) {
    searchResults.replaceAll("替换文本");
    await context.sync();
}
\`\`\`

### ✅ 文本格式化：
\`\`\`javascript
const selection = context.document.getSelection();
const font = selection.font;
font.bold = true;
font.size = 14;
font.color = "blue";
await context.sync();
\`\`\`

### ✅ 插入表格：
\`\`\`javascript
const selection = context.document.getSelection();
const table = selection.insertTable(3, 2, Word.InsertLocation.after, [['A1', 'B1'], ['A2', 'B2'], ['A3', 'B3']]);
context.load(table);
await context.sync();
\`\`\`

### ✅ 插入图片：
\`\`\`javascript
const selection = context.document.getSelection();
selection.insertInlinePictureFromBase64(base64String, Word.InsertLocation.replace);
await context.sync();
\`\`\`
### 💡 字符处理示例代码：
    \`\`\`javascript
    // 字符类型检测函数
    function detectCharacterType(char) {
        if (/[\\u4e00-\\u9fff\\u3000-\\u303f]/.test(char)) {
            return 'chinese';
        } else if (/[a-zA-Z]/.test(char)) {
            return 'english';
        } else if (/[0-9]/.test(char)) {
            return 'number';
        } else {
            return 'other';
        }
    }
    
    // 类型特定处理函数
    function processTextByType(text, targetType, operation) {
        return text.split('').map(char => {
            const charType = detectCharacterType(char);
            if (charType === targetType) {
                return operation(char);
            }
            return char;
        }).join('');
    }
    \`\`\`
## 严格按照以下格式返回（不要偏离）：
async function executeUserCode(context) {
    try {
        // 1. 获取对象引用（使用上述正确方法）
        // 2. 使用 context.load() 加载需要的属性
        // 3. 调用 await context.sync() 同步
        // 4. 检查对象是否存在后再操作
        // 5. 执行实际操作（严禁使用getSubrange等不存在的方法）
        // 6. 最终调用 await context.sync()
        
        await context.sync();
    } catch (error) {
        console.error('执行错误:', error);
        throw error;
    }
}

重要提醒：绝对不要使用任何包含 "getSubrange" 或 "subrange" 的方法！`;

        try {
            const response = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify({
                    model: "qwen-plus",
                    messages: [{
                        role: "user",
                        content: prompt
                    }],
                    temperature: 0.01, // 极低温度确保稳定输出
                    max_tokens: 2000,
                    top_p: 0.1 // 添加top_p参数进一步限制输出的随机性
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API请求失败: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            if (data.choices && data.choices[0] && data.choices[0].message) {
                const rawCode = data.choices[0].message.content;
                
                // 额外的安全检查：如果生成的代码包含禁用方法，直接拒绝
                if (rawCode.includes('getSubrange') || rawCode.includes('subrange')) {
                    throw new Error('AI生成的代码包含不支持的API方法，请重新生成');
                }
                
                const cleanedCode = this.cleanAiOutput(rawCode, 'js');
                console.log('生成的原始代码:', rawCode);
                console.log('清理后的代码:', cleanedCode);
                return cleanedCode;
            } else {
                throw new Error('API返回格式错误');
            }
        } catch (error) {
            console.error('生成Word JavaScript代码错误:', error);
            throw error;
        }
    }
}

// 创建全局实例
const addin = new WordGeminiAddin();

function checkApiKey() {
    try {
        const savedApiKey = localStorage.getItem('qwen-api-key');
        if (savedApiKey) {
            addin.apiKey = savedApiKey;
            const apiKeyInput = document.getElementById('api-key');
            if (apiKeyInput) {
                apiKeyInput.value = savedApiKey;
            }
            showChatInterface();
        }
    } catch (error) {
        console.error('读取API Key失败:', error);
        addin.showStatus('读取保存的API Key失败，请重新输入', 'error');
    }
}

function saveApiKey() {
    try {
        const apiKeyInput = document.getElementById('api-key');
        if (!apiKeyInput) {
            addin.showStatus('找不到API Key输入框', 'error');
            return;
        }
        
        const apiKey = apiKeyInput.value.trim();
        if (!apiKey) {
            addin.showStatus('请输入API Key', 'error');
            return;
        }
        
        // 验证API Key格式（基本检查）
        if (apiKey.length < 10) {
            addin.showStatus('API Key格式不正确，请检查', 'error');
            return;
        }
        
        addin.apiKey = apiKey;
        localStorage.setItem('qwen-api-key', apiKey);
        addin.showStatus('API Key保存成功', 'success');
        showChatInterface();
    } catch (error) {
        console.error('保存API Key失败:', error);
        addin.showStatus('保存API Key失败: ' + error.message, 'error');
    }
}

function showChatInterface() {
    document.getElementById('api-config').style.display = 'none';
    document.getElementById('chat-container').style.display = 'flex';
    
    if (addin.chatHistory.length === 0) {
        addin.addMessage('您好！我是AI助手。请选择操作模式。', 'assistant');
    }
    handleModeChange(); // 初始化按钮状态
}

async function sendMessage() {
    const userInput = document.getElementById('user-input');
    const message = userInput.value.trim();
    
    if (!message) { return; }
    
    const selectedMode = document.querySelector('input[name="mode"]:checked').value;
    
    // 添加用户消息到界面
    addin.addMessage(message, 'user');
    userInput.value = '';
    
    try {
        addin.showStatus('正在处理...', 'info');
        
        if (selectedMode === 'js-direct') {
            // 直接生成JavaScript模式
            addin.showStatus('正在生成Word JavaScript代码...', 'info');
            const jsCode = await addin.generateWordJavaScript(message);
            
            // 保存生成的代码
            addin.lastJavaScriptCode = jsCode;
            
            // 显示生成的代码
            addin.addMessageWithCode('JavaScript代码生成完成！正在自动执行...', 'assistant', jsCode);
            
            // 启用执行按钮
            document.getElementById('execute-js-code').disabled = false;
            document.getElementById('insert-to-doc').disabled = false;
            
            // 自动执行生成的JavaScript代码
            setTimeout(async () => {
                await executeJavaScriptCode();
            }, 500); // 稍微延迟执行，确保UI更新完成
        } else {
            // 普通对话模式
            addin.chatHistory.push({ role: 'user', content: message });
            
            const response = await addin.callGeminiAPI(message);
            addin.lastResponse = response;
            
            addin.chatHistory.push({ role: 'assistant', content: response });
            addin.addMessage(response, 'assistant');
            
            // 启用插入按钮
            document.getElementById('insert-to-doc').disabled = false;
            
            addin.showStatus('回复完成', 'success');
        }
    } catch (error) {
        console.error('发送消息错误:', error);
        let errorMessage = '抱歉，处理您的请求时出现错误。';
        
        if (error.message.includes('401') || error.message.includes('API Key')) {
            errorMessage += '请检查API Key是否正确。';
        } else if (error.message.includes('网络')) {
            errorMessage += '请检查网络连接。';
        } else {
            errorMessage += '请稍后重试。';
        }
        
        addin.addMessage(errorMessage, 'assistant');
        addin.showStatus('处理失败', 'error');
    }
}

async function insertToDocument() {
    if (!addin.lastResponse) {
        addin.showStatus('没有可插入的内容', 'error');
        return;
    }
    
    try {
        await Word.run(async (context) => {
            const selection = context.document.getSelection();
            selection.insertText(addin.lastResponse, Word.InsertLocation.replace);
            
            await context.sync();
        });
        
        addin.showStatus('内容已插入到文档', 'success');
    } catch (error) {
        console.error('插入文档错误:', error);
        addin.showStatus('插入文档失败', 'error');
    }
}

function clearChat() {
    const messagesContainer = document.getElementById('chat-messages');
    messagesContainer.innerHTML = '';
    addin.chatHistory = [];
    addin.lastResponse = '';
    addin.lastJavaScriptCode = '';
    
    document.getElementById('insert-to-doc').disabled = true;
    document.getElementById('execute-js-code').disabled = true;
    
    addin.addMessage('对话已清空。有什么可以帮助您的吗？', 'assistant');
    addin.showStatus('对话已清空', 'info');
}

async function executeJavaScriptCode() {
    if (!addin.lastJavaScriptCode) {
        console.error("executeJavaScriptCode: No code to execute.");
        addin.showStatus('没有可执行的JavaScript代码', 'error');
        return;
    }

    try {
        const codeToExecute = addin.lastJavaScriptCode;
        console.log("--- 准备执行JS代码 ---");
        console.log(codeToExecute);
        console.log("-------------------------");

        addin.showStatus('正在执行JavaScript代码...', 'info');

        // 清理之前可能存在的函数
        if (typeof window.executeUserCode !== 'undefined') {
            delete window.executeUserCode;
        }

        // 使用eval在全局作用域中定义AI生成的函数
        eval(codeToExecute);

        // 检查函数是否已定义
        if (typeof executeUserCode !== 'function') {
            throw new Error('AI生成的代码没有定义 executeUserCode 函数。请重新生成代码。');
        }

        // 在Word.run上下文中执行定义的函数
        await Word.run(executeUserCode);

        console.log("Word.run 执行完成。");
        addin.showStatus('JavaScript代码执行成功！', 'success');
        addin.addMessage('代码执行成功！操作已完成。', 'assistant');
    } catch (error) {
        console.error('执行JavaScript代码错误:', error);
        addin.showStatus(`代码执行失败: ${error.message}`, 'error');
        addin.addMessage(`代码执行出错：${error.message}\n\n请检查生成的代码或重新生成。`, 'assistant');
    } finally {
        // 清理，避免函数在全局作用域中残留
        try {
            if (typeof window.executeUserCode !== 'undefined') {
                delete window.executeUserCode;
            }
            if (typeof executeUserCode !== 'undefined') {
                executeUserCode = undefined;
            }
        } catch (e) {
            console.warn('清理executeUserCode函数时出现警告:', e);
        }
    }
}

function handleModeChange() {
    const selectedMode = document.querySelector('input[name="mode"]:checked').value;
    const executeBtn = document.getElementById('execute-js-code');
    const insertBtn = document.getElementById('insert-to-doc');

    if (selectedMode === 'js-direct') {
        // 在JavaScript模式下显示执行按钮
        executeBtn.style.display = 'inline-block';
        executeBtn.disabled = !addin.lastJavaScriptCode; 
        insertBtn.disabled = true; // 在代码模式下，插入按钮通常是禁用的
    } else {
        // 普通对话模式下隐藏执行按钮
        executeBtn.style.display = 'none';
        insertBtn.disabled = !addin.lastResponse;
    }
}