/**
 * API 服务测试
 */

import { ApiService } from '../../src/services/apiService.js';

describe('ApiService', () => {
    let apiService;

    beforeEach(() => {
        apiService = new ApiService();
        fetch.mockClear();
    });

    describe('构造函数', () => {
        test('应该正确初始化', () => {
            expect(apiService.apiKey).toBeNull();
            expect(apiService.retryCount).toBe(0);
            expect(apiService.cache).toBeDefined();
        });
    });

    describe('setApiKey', () => {
        test('应该设置 API Key', () => {
            const testKey = 'test-api-key';
            apiService.setApiKey(testKey);
            expect(apiService.apiKey).toBe(testKey);
        });
    });

    describe('isApiKeyValid', () => {
        test('空 API Key 应该返回 false', () => {
            expect(apiService.isApiKeyValid()).toBe(false);
        });

        test('有效 API Key 应该返回 true', () => {
            apiService.setApiKey('valid-key');
            expect(apiService.isApiKeyValid()).toBe(true);
        });

        test('空字符串 API Key 应该返回 false', () => {
            apiService.setApiKey('   ');
            expect(apiService.isApiKeyValid()).toBe(false);
        });
    });

    describe('callApi', () => {
        beforeEach(() => {
            apiService.setApiKey('test-key');
        });

        test('应该成功调用 API', async () => {
            const mockResponse = {
                output: {
                    text: 'Test response'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                json: () => Promise.resolve(mockResponse)
            });

            const messages = [
                { role: 'user', content: 'Test message' }
            ];

            const result = await apiService.callApi(messages);
            expect(result).toBe('Test response');
            expect(fetch).toHaveBeenCalledTimes(1);
        });

        test('没有 API Key 时应该抛出错误', async () => {
            apiService.setApiKey(null);
            
            const messages = [
                { role: 'user', content: 'Test message' }
            ];

            await expect(apiService.callApi(messages)).rejects.toThrow('请先配置您的通义千问 API Key');
        });

        test('API 调用失败时应该抛出错误', async () => {
            fetch.mockRejectedValueOnce(new Error('Network error'));

            const messages = [
                { role: 'user', content: 'Test message' }
            ];

            await expect(apiService.callApi(messages)).rejects.toThrow('API 调用失败');
        });

        test('应该使用缓存的响应', async () => {
            const mockResponse = {
                output: {
                    text: 'Cached response'
                }
            };

            // 第一次调用
            fetch.mockResolvedValueOnce({
                ok: true,
                json: () => Promise.resolve(mockResponse)
            });

            const messages = [
                { role: 'user', content: 'Test message' }
            ];

            const result1 = await apiService.callApi(messages);
            expect(result1).toBe('Cached response');
            expect(fetch).toHaveBeenCalledTimes(1);

            // 第二次调用应该使用缓存
            const result2 = await apiService.callApi(messages);
            expect(result2).toBe('Cached response');
            expect(fetch).toHaveBeenCalledTimes(1); // 没有新的网络请求
        });
    });

    describe('generateWordJavaScript', () => {
        beforeEach(() => {
            apiService.setApiKey('test-key');
        });

        test('应该生成 Word JavaScript 代码', async () => {
            const mockResponse = {
                output: {
                    text: '```javascript\nWord.run(async (context) => {\n  // test code\n});\n```'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                json: () => Promise.resolve(mockResponse)
            });

            const result = await apiService.generateWordJavaScript('设置文本为粗体');
            expect(result).toContain('Word.run');
            expect(result).toContain('test code');
        });

        test('应该提取代码块中的内容', async () => {
            const mockResponse = {
                output: {
                    text: '这是一些说明文字\n```javascript\nconst test = "code";\n```\n更多说明'
                }
            };

            fetch.mockResolvedValueOnce({
                ok: true,
                json: () => Promise.resolve(mockResponse)
            });

            const result = await apiService.generateWordJavaScript('测试');
            expect(result).toBe('const test = "code";');
        });
    });

    describe('getCacheStats', () => {
        test('应该返回缓存统计信息', () => {
            const stats = apiService.getCacheStats();
            expect(stats).toHaveProperty('hits');
            expect(stats).toHaveProperty('misses');
            expect(stats).toHaveProperty('hitRate');
            expect(stats).toHaveProperty('cacheSize');
        });
    });

    describe('clearCache', () => {
        test('应该清除 API 缓存', () => {
            const clearSpy = jest.spyOn(apiService.cache, 'deleteByTag');
            apiService.clearCache();
            expect(clearSpy).toHaveBeenCalledWith('api_response');
        });
    });

    describe('queueRequest', () => {
        test('应该将请求添加到队列', async () => {
            const mockRequest = jest.fn().mockResolvedValue('test result');
            
            const promise = apiService.queueRequest(mockRequest, 5);
            expect(apiService.requestQueue).toHaveLength(1);
            
            const result = await promise;
            expect(result).toBe('test result');
            expect(mockRequest).toHaveBeenCalled();
        });

        test('应该按优先级排序请求', () => {
            const lowPriorityRequest = jest.fn();
            const highPriorityRequest = jest.fn();
            
            apiService.queueRequest(lowPriorityRequest, 1);
            apiService.queueRequest(highPriorityRequest, 10);
            
            expect(apiService.requestQueue[0].priority).toBe(10);
            expect(apiService.requestQueue[1].priority).toBe(1);
        });
    });

    describe('getQueueStatus', () => {
        test('应该返回队列状态', () => {
            const status = apiService.getQueueStatus();
            expect(status).toHaveProperty('queueLength');
            expect(status).toHaveProperty('isProcessing');
            expect(status).toHaveProperty('oldestRequest');
        });
    });

    describe('batchProcess', () => {
        beforeEach(() => {
            apiService.setApiKey('test-key');
        });

        test('应该批量处理请求', async () => {
            const mockResponse = {
                output: {
                    text: 'Batch response'
                }
            };

            fetch.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve(mockResponse)
            });

            const requests = [
                { messages: [{ role: 'user', content: 'Request 1' }] },
                { messages: [{ role: 'user', content: 'Request 2' }] }
            ];

            const results = await apiService.batchProcess(requests, { batchSize: 1, delay: 10 });
            expect(results).toHaveLength(2);
            expect(results[0].status).toBe('fulfilled');
            expect(results[1].status).toBe('fulfilled');
        });
    });

    describe('preloadCommonRequests', () => {
        beforeEach(() => {
            apiService.setApiKey('test-key');
        });

        test('应该预加载常用请求', async () => {
            const mockResponse = {
                output: {
                    text: 'Preloaded response'
                }
            };

            fetch.mockResolvedValue({
                ok: true,
                json: () => Promise.resolve(mockResponse)
            });

            const commonRequests = [
                {
                    name: 'test_request',
                    messages: [{ role: 'user', content: 'Test' }],
                    options: {}
                }
            ];

            await apiService.preloadCommonRequests(commonRequests);
            expect(fetch).toHaveBeenCalled();
        });
    });
});
