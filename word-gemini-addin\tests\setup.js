/**
 * Jest 测试设置文件
 * 配置测试环境和全局模拟
 */

// 模拟 Office JavaScript API
global.Office = {
    context: {
        host: 'Word',
        platform: 'PC',
        diagnostics: {
            version: '16.0.0'
        }
    },
    onReady: jest.fn((callback) => {
        if (typeof callback === 'function') {
            callback({ host: 'Word' });
        }
        return Promise.resolve({ host: 'Word' });
    }),
    HostType: {
        Word: 'Word',
        Excel: 'Excel',
        PowerPoint: 'PowerPoint'
    }
};

// 模拟 Word JavaScript API
global.Word = {
    run: jest.fn((callback) => {
        const mockContext = {
            document: {
                getSelection: jest.fn(() => ({
                    insertText: jest.fn(),
                    font: {
                        bold: false,
                        italic: false,
                        color: '#000000',
                        size: 12,
                        name: '<PERSON><PERSON><PERSON>'
                    },
                    text: 'Mock selected text'
                })),
                body: {
                    search: jest.fn(() => ({
                        items: []
                    })),
                    text: 'Mock document content'
                },
                properties: {
                    title: 'Mock Document',
                    author: 'Test User'
                }
            },
            sync: jest.fn(() => Promise.resolve()),
            load: jest.fn()
        };
        
        if (typeof callback === 'function') {
            return Promise.resolve(callback(mockContext));
        }
        return Promise.resolve();
    }),
    InsertLocation: {
        before: 'Before',
        after: 'After',
        start: 'Start',
        end: 'End',
        replace: 'Replace'
    },
    Alignment: {
        left: 'Left',
        center: 'Center',
        right: 'Right',
        justify: 'Justify'
    }
};

// 模拟 marked.js
global.marked = {
    parse: jest.fn((text) => text)
};

// 模拟 localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
    length: 0,
    key: jest.fn()
};
global.localStorage = localStorageMock;

// 模拟 sessionStorage
global.sessionStorage = localStorageMock;

// 模拟 fetch API
global.fetch = jest.fn(() =>
    Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve({
            output: {
                text: 'Mock API response'
            }
        }),
        text: () => Promise.resolve('Mock response text')
    })
);

// 模拟 performance API
global.performance = {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    memory: {
        usedJSHeapSize: 1024 * 1024,
        totalJSHeapSize: 2 * 1024 * 1024,
        jsHeapSizeLimit: 4 * 1024 * 1024
    }
};

// 模拟 PerformanceObserver
global.PerformanceObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    disconnect: jest.fn()
}));

// 模拟 Blob
global.Blob = jest.fn().mockImplementation((content, options) => ({
    size: content ? content.join('').length : 0,
    type: options?.type || 'text/plain'
}));

// 模拟 URL.createObjectURL
global.URL = {
    createObjectURL: jest.fn(() => 'mock-url'),
    revokeObjectURL: jest.fn()
};

// 模拟 navigator
Object.defineProperty(global.navigator, 'clipboard', {
    value: {
        writeText: jest.fn(() => Promise.resolve())
    },
    writable: true
});

// 模拟 window.matchMedia
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn()
    }))
});

// 模拟 ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
}));

// 模拟 MutationObserver
global.MutationObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    disconnect: jest.fn(),
    takeRecords: jest.fn()
}));

// 模拟 requestAnimationFrame
global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = jest.fn(id => clearTimeout(id));

// 模拟 requestIdleCallback
global.requestIdleCallback = jest.fn(cb => setTimeout(cb, 1));
global.cancelIdleCallback = jest.fn(id => clearTimeout(id));

// 设置测试超时
jest.setTimeout(30000);

// 全局测试工具函数
global.createMockApiResponse = (data) => ({
    ok: true,
    status: 200,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data))
});

global.createMockError = (message, status = 500) => {
    const error = new Error(message);
    error.status = status;
    return error;
};

// 清理函数
afterEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// 控制台警告过滤
const originalWarn = console.warn;
console.warn = (...args) => {
    // 过滤掉一些已知的无害警告
    const message = args[0];
    if (typeof message === 'string') {
        if (message.includes('Warning: ReactDOM.render is deprecated')) {
            return;
        }
        if (message.includes('Warning: componentWillReceiveProps')) {
            return;
        }
    }
    originalWarn.apply(console, args);
};

// 测试环境标识
process.env.NODE_ENV = 'test';

console.log('🧪 测试环境已设置完成');
