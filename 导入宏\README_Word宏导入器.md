# Word宏导入器使用说明

这是一个用于自动化将外部宏文件导入到Microsoft Word文档中的Python工具集。

## 📁 文件说明

### 1. `word_macro_importer.py` - 完整功能版
- **功能**: 提供完整的宏导入、导出、管理功能
- **特点**: 支持批量处理、组件管理、详细配置
- **适用**: 需要高级功能和批量处理的用户

### 2. `simple_macro_importer.py` - 简化版
- **功能**: 快速导入单个宏文件到Word文档
- **特点**: 简单易用、配置简单、快速上手
- **适用**: 只需要基本导入功能的用户

### 3. `install_requirements.bat` - 依赖安装脚本
- **功能**: 自动安装所需的Python库
- **包含**: pywin32、pathlib等依赖

## 🚀 快速开始

### 步骤1: 安装依赖
双击运行 `install_requirements.bat` 或在命令行中执行：
```bash
pip install pywin32
```

### 步骤2: 启用Word VBA访问
1. 打开Microsoft Word
2. 文件 → 选项 → 信任中心 → 信任中心设置
3. 选择"宏设置"
4. 勾选"信任对VBA项目对象模型的访问"
5. 点击确定

### 步骤3: 运行脚本

#### 使用简化版（推荐新手）:
```bash
python simple_macro_importer.py
```

#### 使用完整版（高级用户）:
```bash
python word_macro_importer.py
```

## 📖 详细使用方法

### 简化版使用方法

1. **编辑配置**:
   打开 `simple_macro_importer.py`，修改配置区域：
   ```python
   # 要导入的宏文件路径
   MACRO_FILE = r"C:\path\to\your\macro.bas"
   
   # 目标Word文档路径（None表示创建新文档）
   TARGET_DOC = None
   
   # 输出文档路径
   OUTPUT_DOC = r"C:\path\to\output.docm"
   
   # 是否创建示例宏文件
   CREATE_SAMPLE = True
   ```

2. **支持的宏文件格式**:
   - `.bas` - VBA标准模块文件
   - `.cls` - VBA类模块文件
   - `.frm` - VBA窗体文件
   - `.txt` - 包含VBA代码的文本文件
   - `.vba` - VBA代码文件

3. **运行脚本**:
   ```bash
   python simple_macro_importer.py
   ```

### 完整版使用方法

1. **批量导入示例**:
   ```python
   # 在main()函数中配置
   MACRO_FILES = [
       r"C:\macros\macro1.bas",
       r"C:\macros\macro2.cls",
       r"C:\macros\macro3.txt"
   ]
   ```

2. **高级功能**:
   - 组件列表查看
   - 宏文件导出
   - 批量处理
   - 详细错误处理

## 🔧 宏文件创建

### 创建VBA文本文件
创建一个 `.txt` 或 `.vba` 文件，包含VBA代码：

```vba
Sub HelloWorld()
    MsgBox "Hello, World!", vbInformation, "我的宏"
End Sub

Sub SetChineseFontToSongTi()
    ' 将中文字符设置为宋体
    Dim rng As Range
    Set rng = ActiveDocument.Range
    
    With rng.Find
        .Text = "[一-龯]"
        .MatchWildcards = True
        .Replacement.Text = ""
        .Replacement.Font.NameFarEast = "宋体"
        .Execute Replace:=wdReplaceAll
    End With
    
    MsgBox "中文字符已设置为宋体！", vbInformation
End Sub
```

### 从Word导出宏文件
1. 在Word中按 `Alt+F11` 打开VBA编辑器
2. 右键点击模块 → 导出文件
3. 保存为 `.bas`、`.cls` 或 `.frm` 文件

## ⚠️ 常见问题

### 问题1: "VBA项目访问被拒绝"
**解决方法**:
1. 在Word中启用VBA项目访问（见快速开始步骤2）
2. 重启Word和Python脚本

### 问题2: "模块导入失败"
**可能原因**:
- 宏文件格式不正确
- 文件路径包含特殊字符
- VBA代码语法错误

**解决方法**:
- 检查文件扩展名是否正确
- 使用绝对路径
- 验证VBA代码语法

### 问题3: "Word无法启动"
**解决方法**:
- 确保Word已正确安装
- 检查Word是否被其他程序占用
- 以管理员身份运行脚本

### 问题4: "pywin32导入失败"
**解决方法**:
```bash
pip uninstall pywin32
pip install pywin32
# 或者
pip install pywin32 --user
```

## 🎯 使用场景

### 场景1: 批量部署宏到多个文档
```python
# 使用完整版
documents = ["doc1.docx", "doc2.docx", "doc3.docx"]
macro_files = ["common_macro.bas"]

for doc in documents:
    batch_import_macros(doc, macro_files, doc.replace('.docx', '_with_macro.docm'))
```

### 场景2: 快速添加单个宏
```python
# 使用简化版
# 修改配置后直接运行
python simple_macro_importer.py
```

### 场景3: 宏文件管理
```python
# 使用完整版的导出功能
importer = WordMacroImporter()
importer.connect_to_word()
importer.open_or_create_document("document.docm")
importer.export_macro_to_file("MyMacro", "backup_macro.bas")
```

## 📝 注意事项

1. **文件格式**: 包含宏的文档必须保存为 `.docm` 格式
2. **安全设置**: 确保Word的宏安全设置允许运行宏
3. **权限**: 某些操作可能需要管理员权限
4. **版本兼容**: 支持Word 2010及以上版本
5. **编码**: VBA文件建议使用UTF-8编码

## 🔍 高级技巧

### 自动运行宏
在VBA代码中使用特殊的宏名称：
```vba
Sub AutoOpen()
    ' 文档打开时自动运行
    MsgBox "文档已打开！"
End Sub

Sub AutoNew()
    ' 创建新文档时自动运行
    MsgBox "新文档已创建！"
End Sub
```

### 条件导入
```python
# 检查宏是否已存在
for component in importer.list_components():
    if component['name'] == 'MyMacro':
        print("宏已存在，跳过导入")
        break
else:
    importer.import_macro_file("MyMacro.bas")
```

### 错误处理
```python
try:
    success = import_macro_to_word(macro_file, doc_path)
    if not success:
        print("导入失败，尝试备用方法")
        # 备用处理逻辑
except Exception as e:
    print(f"发生错误: {e}")
    # 错误恢复逻辑
```

## 🔧 故障排除

### 常见问题及解决方案

#### 问题0：导入的宏代码出现中文乱码 🔥
**问题**：导入的VBA宏中的中文注释和字符串显示为乱码。

**症状示例**：
```vba
' 原始代码
' 将整个文档的远东字体设置为宋体
MsgBox "文档字体已设置为宋体！", vbInformation, "设置完成"

' 导入后乱码
' 灏嗘暣涓枃妗ｇ殑杩滀笢瀛椾綋璁剧疆涓哄畫浣?
MsgBox "鏂囨。瀛椾綋宸茶缃负瀹嬩綋锛?", vbInformation, "璁剧疆瀹屾垚"
```

**原因**：`VBComponents.Import()` 方法对UTF-8编码的中文字符处理存在问题。

**解决方案**：已修复！现在 `.bas` 文件使用文本读取方式导入，完美支持中文编码。

**验证方法**：
1. 运行修复后的脚本
2. 按 Alt+F11 打开VBA编辑器
3. 检查中文注释和字符串是否正常显示

详细技术说明请参考：`编码修复说明.md`

#### 问题1：生成的文档内容为空
**原因**：`TARGET_DOC` 设置为 `None`，创建了空白新文档
**解决**：将 `TARGET_DOC` 设置为原文档路径
```python
TARGET_DOC = r"C:\Users\<USER>\Desktop\11.docx"  # 指定原文档
```

#### 问题2：宏没有真正导入
**原因**：
- 文件格式错误（使用.docx而不是.docm）
- VBA访问权限未启用
- 宏文件路径或格式错误

**解决**：
1. 确保输出文件使用 `.docm` 格式
2. 启用VBA项目访问权限
3. 检查宏文件是否为正确的 `.bas` 格式

#### 问题3："VBA项目访问被拒绝"
**解决步骤**：
1. 打开Word → 文件 → 选项 → 信任中心
2. 点击"信任中心设置"
3. 选择"宏设置"
4. 勾选"信任对VBA项目对象模型的访问"
5. 点击确定并重启Word

#### 问题4：导入的宏不是期望的内容
**原因**：`CREATE_SAMPLE = True` 覆盖了用户的宏文件
**解决**：设置 `CREATE_SAMPLE = False` 使用现有宏文件

### 配置检查清单

使用前请确认以下配置：

**simple_macro_importer.py**：
```python
MACRO_FILE = r"C:\Users\<USER>\Desktop\SetChineseFontSimple.bas"  # ✅ 正确的.bas文件
TARGET_DOC = r"C:\Users\<USER>\Desktop\11.docx"                    # ✅ 指定原文档
OUTPUT_DOC = r"C:\Users\<USER>\Desktop\12.docm"                    # ✅ 启用宏的格式
CREATE_SAMPLE = False                                            # ✅ 使用现有文件
```

**word_macro_importer.py**：
```python
TARGET_DOC = r"C:\Users\<USER>\Desktop\11.docx"                    # ✅ 指定原文档
MACRO_FILES = [r"C:\Users\<USER>\Desktop\SetChineseFontSimple.bas"] # ✅ 列表格式，.bas文件
OUTPUT_DOC = r"C:\Users\<USER>\Desktop\12.docm"                    # ✅ 启用宏的格式
CREATE_SAMPLE = False                                            # ✅ 使用现有文件
```

### 验证工具

运行测试脚本验证配置：
```bash
python test_macro_import.py
```

预期输出：
```
✓ 文件存在: C:\Users\<USER>\Desktop\11.docx
✓ 文件存在: C:\Users\<USER>\Desktop\SetChineseFontSimple.bas
✓ 输出文件已生成: C:\Users\<USER>\Desktop\12.docm
✓ 文件不为空，可能包含内容
✓ 宏导入测试完成，文件生成成功
```

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本（建议3.6+）
2. Word版本（建议2010+）
3. pywin32库版本
4. VBA访问权限设置
5. 文件路径和权限
6. 参考 `修复说明.md` 了解详细的问题解决方案

---

**祝您使用愉快！** 🎉