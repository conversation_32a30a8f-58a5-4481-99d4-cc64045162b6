@echo off
echo ===================================
echo Word宏导入器 - 依赖安装程序
echo ===================================
echo.

REM 检查Python是否已安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python安装。
    echo 请先安装Python 3.6或更高版本。
    echo 可以从 https://www.python.org/downloads/ 下载。
    echo.
    pause
    exit /b 1
)

echo [信息] 检测到Python已安装。
echo.

echo [信息] 正在安装必要的依赖库...
echo.

REM 安装pywin32库
echo [信息] 正在安装pywin32库...
pip install pywin32
if %errorlevel% neq 0 (
    echo [警告] pywin32安装可能出现问题。
    echo 您可以尝试手动运行: pip install pywin32 --user
) else (
    echo [成功] pywin32安装完成。
)
echo.

REM 安装pathlib库（Python 3.6+已内置，但为了兼容性仍然安装）
echo [信息] 正在安装pathlib库...
pip install pathlib
if %errorlevel% neq 0 (
    echo [警告] pathlib安装可能出现问题。
    echo 您可以尝试手动运行: pip install pathlib --user
) else (
    echo [成功] pathlib安装完成。
)
echo.

echo ===================================
echo 安装完成！
echo ===================================
echo.
echo 现在您可以运行以下Python脚本：
echo 1. word_macro_importer.py - 完整功能版
echo 2. simple_macro_importer.py - 简化版
echo.
echo 如果遇到任何问题，请确保：
echo - Word已正确安装
echo - 已启用VBA项目访问权限
echo - 使用管理员权限运行脚本
echo.

pause