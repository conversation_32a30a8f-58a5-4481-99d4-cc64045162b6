#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版Word宏导入器
功能：快速将外部宏文件导入到Word文档
依赖：pip install pywin32
"""

import win32com.client
import os
import sys

def import_macro_to_word(macro_file_path, word_doc_path=None, output_path=None):
    """
    将宏文件导入到Word文档
    
    Args:
        macro_file_path (str): 宏文件路径(.bas, .cls, .frm或.txt)
        word_doc_path (str, optional): 目标Word文档路径，None表示创建新文档
        output_path (str, optional): 输出文档路径
    
    Returns:
        bool: 是否成功
    """
    word_app = None
    document = None
    
    try:
        print(f"正在导入宏文件: {macro_file_path}")
        
        # 检查宏文件是否存在
        if not os.path.exists(macro_file_path):
            print(f"错误: 宏文件不存在 - {macro_file_path}")
            return False
        
        # 启动Word应用程序
        print("正在启动Word...")
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = True
        
        # 打开或创建文档
        if word_doc_path and os.path.exists(word_doc_path):
            print(f"正在打开文档: {word_doc_path}")
            document = word_app.Documents.Open(os.path.abspath(word_doc_path))
        else:
            print("创建新文档")
            document = word_app.Documents.Add()
        
        # 获取VBA项目
        vb_project = document.VBProject
        
        # 检查VBA访问权限
        try:
            component_count = vb_project.VBComponents.Count
            print(f"VBA项目访问正常，当前有 {component_count} 个组件")
        except Exception as e:
            print("错误: VBA项目访问被拒绝")
            print("解决方法:")
            print("1. 在Word中打开：文件 → 选项 → 信任中心 → 信任中心设置")
            print("2. 选择'宏设置'，勾选'信任对VBA项目对象模型的访问'")
            print("3. 重新运行此脚本")
            return False
        
        # 根据文件扩展名处理不同类型的宏文件
        file_ext = os.path.splitext(macro_file_path)[1].lower()
        
        if file_ext in ['.cls', '.frm']:
            # 直接导入VBA组件文件（类模块和窗体）
            print(f"导入VBA组件文件: {file_ext}")
            imported_component = vb_project.VBComponents.Import(os.path.abspath(macro_file_path))
            print(f"成功导入组件: {imported_component.Name}")
            
        elif file_ext in ['.bas', '.txt', '.vba']:
            # 从文本文件导入VBA代码
            print("从文本文件导入VBA代码")
            with open(macro_file_path, 'r', encoding='utf-8') as f:
                macro_code = f.read()
            
            # 创建新的标准模块
            module_name = os.path.splitext(os.path.basename(macro_file_path))[0]
            new_module = vb_project.VBComponents.Add(1)  # vbext_ct_StdModule = 1
            new_module.Name = module_name
            
            # 添加代码
            code_module = new_module.CodeModule
            code_module.AddFromString(macro_code)
            print(f"成功创建模块: {module_name}")
            
        else:
            print(f"错误: 不支持的文件类型 {file_ext}")
            print("支持的文件类型: .bas, .cls, .frm, .txt, .vba")
            return False
        
        # 显示当前所有组件
        print("\n当前文档中的VBA组件:")
        print("-" * 40)
        component_types = {1: "标准模块", 2: "类模块", 3: "窗体", 100: "文档模块"}
        
        for i in range(1, vb_project.VBComponents.Count + 1):
            component = vb_project.VBComponents(i)
            comp_type = component_types.get(component.Type, "未知类型")
            print(f"{i}. {component.Name} ({comp_type})")
        
        # 保存文档
        if output_path:
            # 确保保存为启用宏的格式
            if not output_path.lower().endswith('.docm'):
                output_path = os.path.splitext(output_path)[0] + '.docm'
            
            print(f"\n正在保存文档: {output_path}")
            document.SaveAs2(os.path.abspath(output_path), FileFormat=13)  # wdFormatXMLDocumentMacroEnabled
            print("文档保存成功")
        elif word_doc_path:
            print("\n保存到原文档")
            document.Save()
        else:
            # 新文档，提示用户保存
            default_name = os.path.splitext(os.path.basename(macro_file_path))[0] + "_with_macro.docm"
            save_path = os.path.join(os.path.dirname(os.path.abspath(macro_file_path)), default_name)
            print(f"\n正在保存新文档: {save_path}")
            document.SaveAs2(save_path, FileFormat=13)
            print("文档保存成功")
        
        print("\n[成功] 宏导入完成！")
        print("\n使用提示:")
        print("1. 按 Alt+F11 打开VBA编辑器查看导入的宏")
        print("2. 按 Alt+F8 运行宏")
        print("3. 或在'开发工具'选项卡中点击'宏'按钮")
        
        return True
        
    except Exception as e:
        print(f"导入过程中出现错误: {e}")
        return False
    
    finally:
        # 清理资源（保持Word和文档打开供用户使用）
        print("\n注意: Word应用程序保持打开状态，您可以继续编辑文档")

def create_sample_vba_file(file_path):
    """
    创建示例VBA文件
    
    Args:
        file_path (str): 输出文件路径
    """
    sample_vba = '''Sub HelloMacro()
    ' 简单的问候宏
    MsgBox "Hello! 这是导入的宏！", vbInformation, "导入的宏"
End Sub

Sub SetChineseFontToSongTi()
    ' 将文档中的中文字符设置为宋体
    Dim doc As Document
    Dim rng As Range
    
    Set doc = ActiveDocument
    Set rng = doc.Range
    
    ' 使用查找替换功能
    With rng.Find
        .ClearFormatting
        .Replacement.ClearFormatting
        .Text = "[一-龯]"
        .MatchWildcards = True
        .Replacement.Text = ""
        .Replacement.Font.NameFarEast = "宋体"
        .Execute Replace:=wdReplaceAll
    End With
    
    MsgBox "中文字符已设置为宋体字体！", vbInformation, "字体设置完成"
End Sub

Sub FormatDocument()
    ' 格式化整个文档
    Dim doc As Document
    Set doc = ActiveDocument
    
    With doc.Range.Font
        .Name = "Times New Roman"
        .Size = 12
        .Bold = False
        .Italic = False
    End With
    
    With doc.Range.ParagraphFormat
        .SpaceAfter = 6
        .LineSpacing = LinesToPoints(1.15)
    End With
    
    MsgBox "文档格式化完成！", vbInformation, "格式化完成"
End Sub

Sub InsertCurrentDateTime()
    ' 插入当前日期和时间
    Selection.InsertDateTime DateTimeFormat:="yyyy年MM月dd日 HH:mm:ss", _
        InsertAsField:=False
End Sub
'''
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(sample_vba)
        print(f"示例VBA文件已创建: {file_path}")
        return True
    except Exception as e:
        print(f"创建示例文件失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("简化版Word宏导入器")
    print("=" * 40)
    
    # ==================== 配置区域 ====================
    
    # 要导入的宏文件路径
    MACRO_FILE = r"C:\Users\<USER>\Desktop\SetChineseFontSimple.bas"
    
    # 目标Word文档路径（None表示创建新文档）
    TARGET_DOC = r"C:\Users\<USER>\Desktop\11.docx"  # 指定要添加宏的原文档
    
    # 输出文档路径（None表示保存到默认位置）
    OUTPUT_DOC = r"C:\Users\<USER>\Desktop\12.docm"  # 保存为启用宏的格式
    
    # 是否创建示例宏文件
    CREATE_SAMPLE = False  # 使用现有的SetChineseFontSimple.bas文件
    
    # ==================== 执行区域 ====================
    
    # 检查依赖
    try:
        import win32com.client
    except ImportError:
        print("错误: 未安装pywin32库")
        print("请运行: pip install pywin32")
        input("按回车键退出...")
        return
    
    # 创建示例宏文件
    if CREATE_SAMPLE:
        if create_sample_vba_file(MACRO_FILE):
            print(f"将使用示例宏文件: {MACRO_FILE}")
        else:
            print("创建示例文件失败，请检查路径权限")
            return
    
    # 检查宏文件是否存在
    if not os.path.exists(MACRO_FILE):
        print(f"错误: 宏文件不存在 - {MACRO_FILE}")
        print("请检查文件路径或设置CREATE_SAMPLE=True创建示例文件")
        input("按回车键退出...")
        return
    
    # 执行导入
    print(f"\n开始导入宏文件: {MACRO_FILE}")
    success = import_macro_to_word(MACRO_FILE, TARGET_DOC, OUTPUT_DOC)
    
    if success:
        print("\n[成功] 宏导入成功！")
    else:
        print("\n[失败] 宏导入失败")
        print("\n故障排除建议:")
        print("1. 确保Word已正确安装")
        print("2. 启用VBA项目访问权限")
        print("3. 检查宏文件格式和内容")
        print("4. 以管理员身份运行此脚本")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()