#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文编码修复验证测试
测试修复后的Word宏导入器是否正确处理中文编码
"""

import os
import subprocess
import sys
from pathlib import Path

def test_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"[OK] {description}: {file_path}")
        return True
    else:
        print(f"[NO] {description} 不存在: {file_path}")
        return False

def test_file_not_empty(file_path, description):
    """检查文件是否非空"""
    if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
        size = os.path.getsize(file_path)
        print(f"[OK] {description} 非空: {file_path} ({size} 字节)")
        return True
    else:
        print(f"[NO] {description} 为空或不存在: {file_path}")
        return False

def run_script_test(script_path, script_name):
    """运行脚本并检查结果"""
    print(f"\n{'='*60}")
    print(f"测试 {script_name}")
    print(f"{'='*60}")
    
    try:
        # 运行脚本
        result = subprocess.run(
            [sys.executable, script_path],
            capture_output=True,
            text=True,
            encoding='gbk',  # Windows中文环境使用gbk编码
            errors='ignore',  # 忽略编码错误
            timeout=60
        )
        
        print(f"脚本执行状态: {'成功' if result.returncode == 0 else '失败'}")
        
        if result.stdout:
            print("\n标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("\n错误输出:")
            print(result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("[错误] 脚本执行超时")
        return False
    except Exception as e:
        print(f"[错误] 脚本执行异常: {e}")
        return False

def check_vba_encoding(docm_path):
    """检查生成的docm文件中的VBA编码是否正确"""
    print(f"\n检查 {docm_path} 中的VBA编码...")
    
    try:
        import win32com.client
        
        # 启动Word应用程序
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = False
        
        # 打开文档
        doc = word_app.Documents.Open(os.path.abspath(docm_path))
        
        # 检查VBA项目
        vb_project = doc.VBProject
        
        print(f"VBA组件数量: {vb_project.VBComponents.Count}")
        
        # 查找SetChineseFontSimple模块
        target_module = None
        for i in range(1, vb_project.VBComponents.Count + 1):
            component = vb_project.VBComponents.Item(i)
            print(f"组件 {i}: {component.Name} ({component.Type})")
            
            if component.Name == "SetChineseFontSimple":
                target_module = component
                break
        
        if target_module:
            print(f"\n找到目标模块: {target_module.Name}")
            
            # 获取模块代码
            code_module = target_module.CodeModule
            line_count = code_module.CountOfLines
            
            print(f"代码行数: {line_count}")
            
            # 检查前几行代码中的中文字符
            encoding_ok = True
            for line_num in range(1, min(line_count + 1, 10)):
                line_text = code_module.Lines(line_num, 1)
                if line_text.strip():
                    print(f"第{line_num}行: {line_text.strip()}")
                    
                    # 检查是否包含乱码字符
                    if any(char in line_text for char in ['灏', '嗘', '涓', '枃', '妗', '鐨']):
                        print(f"[错误] 第{line_num}行包含乱码字符")
                        encoding_ok = False
            
            if encoding_ok:
                print("[OK] VBA代码编码正常，未发现乱码")
            else:
                print("[NO] VBA代码存在编码问题")
                
        else:
            print("[NO] 未找到SetChineseFontSimple模块")
            encoding_ok = False
        
        # 关闭文档和应用程序
        doc.Close()
        word_app.Quit()
        
        return encoding_ok
        
    except Exception as e:
        print(f"[错误] 检查VBA编码时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("Word宏导入器中文编码修复验证测试")
    print("=" * 60)
    
    # 测试文件路径
    base_dir = r"C:\Users\<USER>\Desktop"
    macro_dir = os.path.join(base_dir, "导入宏")
    
    # 必要文件检查
    required_files = {
        os.path.join(base_dir, "11.docx"): "源文档",
        os.path.join(base_dir, "SetChineseFontSimple.bas"): "VBA宏文件",
        os.path.join(macro_dir, "simple_macro_importer.py"): "简化版导入器",
        os.path.join(macro_dir, "word_macro_importer.py"): "完整版导入器"
    }
    
    print("\n检查必要文件...")
    all_files_exist = True
    for file_path, description in required_files.items():
        if not test_file_exists(file_path, description):
            all_files_exist = False
    
    if not all_files_exist:
        print("\n[错误] 缺少必要文件，无法进行测试")
        return False
    
    # 清理旧的输出文件
    output_files = [
        os.path.join(base_dir, "12.docm"),
        os.path.join(base_dir, "13.docm")
    ]
    
    for output_file in output_files:
        if os.path.exists(output_file):
            try:
                os.remove(output_file)
                print(f"清理旧文件: {output_file}")
            except:
                print(f"无法删除旧文件: {output_file}")
    
    # 测试结果
    test_results = []
    
    # 测试1: simple_macro_importer.py
    print("\n" + "="*60)
    print("测试1: simple_macro_importer.py")
    print("="*60)
    
    script1_path = os.path.join(macro_dir, "simple_macro_importer.py")
    output1_path = os.path.join(base_dir, "12.docm")
    
    success1 = run_script_test(script1_path, "简化版宏导入器")
    
    if success1:
        file_ok1 = test_file_not_empty(output1_path, "输出文档")
        if file_ok1:
            encoding_ok1 = check_vba_encoding(output1_path)
            test_results.append(("simple_macro_importer.py", success1 and file_ok1 and encoding_ok1))
        else:
            test_results.append(("simple_macro_importer.py", False))
    else:
        test_results.append(("simple_macro_importer.py", False))
    
    # 测试2: word_macro_importer.py
    print("\n" + "="*60)
    print("测试2: word_macro_importer.py")
    print("="*60)
    
    script2_path = os.path.join(macro_dir, "word_macro_importer.py")
    output2_path = os.path.join(base_dir, "13.docm")
    
    success2 = run_script_test(script2_path, "完整版宏导入器")
    
    if success2:
        file_ok2 = test_file_not_empty(output2_path, "输出文档")
        if file_ok2:
            encoding_ok2 = check_vba_encoding(output2_path)
            test_results.append(("word_macro_importer.py", success2 and file_ok2 and encoding_ok2))
        else:
            test_results.append(("word_macro_importer.py", False))
    else:
        test_results.append(("word_macro_importer.py", False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    all_passed = True
    for script_name, passed in test_results:
        status = "[OK] 通过" if passed else "[NO] 失败"
        print(f"{script_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n[成功] 所有测试通过！中文编码修复成功！")
        print("\n验证要点:")
        print("- 宏导入成功")
        print("- 文档内容保留")
        print("- 中文字符正常显示")
        print("- 模块名称正确")
    else:
        print("\n[失败] 部分测试失败，请检查相关问题")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中发生异常: {e}")
        sys.exit(1)