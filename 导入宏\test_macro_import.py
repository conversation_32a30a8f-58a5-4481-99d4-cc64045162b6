#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试宏导入功能
"""

import os
import sys

def test_simple_importer():
    """
    测试简化版宏导入器
    """
    print("测试简化版宏导入器...")
    print("=" * 40)
    
    # 检查必要文件是否存在
    files_to_check = [
        r"C:\Users\<USER>\Desktop\11.docx",
        r"C:\Users\<USER>\Desktop\SetChineseFontSimple.bas",
        r"C:\Users\<USER>\Desktop\导入宏\simple_macro_importer.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"[OK] 文件存在: {file_path}")
        else:
            print(f"[NO] 文件不存在: {file_path}")
            return False
    
    print("\n所有必要文件都存在，可以进行宏导入测试。")
    return True

def check_output_file():
    """
    检查输出文件
    """
    output_file = r"C:\Users\<USER>\Desktop\12.docm"
    
    if os.path.exists(output_file):
        file_size = os.path.getsize(output_file)
        print(f"\n[OK] 输出文件已生成: {output_file}")
        print(f"文件大小: {file_size} 字节")
        
        if file_size > 0:
            print("[OK] 文件不为空，可能包含内容")
            return True
        else:
            print("[NO] 文件为空")
            return False
    else:
        print(f"\n[NO] 输出文件不存在: {output_file}")
        return False

if __name__ == "__main__":
    print("宏导入测试工具")
    print("=" * 50)
    
    # 测试文件存在性
    if test_simple_importer():
        print("\n[OK] 预检查通过")
        
        # 检查输出文件
        if check_output_file():
            print("\n[OK] 宏导入测试完成，文件生成成功")
        else:
            print("\n[NO] 输出文件检查失败")
    else:
        print("\n[NO] 预检查失败")
    
    input("\n按回车键退出...")