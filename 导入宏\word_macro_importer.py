#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Word宏文件导入器
功能：自动化将外部宏文件(.bas, .cls, .frm)导入到Word文档中
依赖：pip install pywin32
"""

import win32com.client
import pythoncom  # 添加这个导入
import os
import sys
from pathlib import Path
import shutil
import tempfile

class WordMacroImporter:
    def __init__(self):
        self.word_app = None
        self.document = None
        self.vb_project = None
    
    def connect_to_word(self, visible=False):
        """
        连接到Word应用程序
        
        Args:
            visible (bool): 是否显示Word界面
        
        Returns:
            bool: 是否成功连接
        """
        try:
            # 在多线程环境中初始化COM
            pythoncom.CoInitialize()
            
            self.word_app = win32com.client.Dispatch("Word.Application")
            self.word_app.Visible = visible
            print("成功连接到Word应用程序")
            return True
        except Exception as e:
            print(f"连接Word失败: {e}")
            return False
    
    def open_or_create_document(self, doc_path=None):
        """
        打开现有文档或创建新文档
        
        Args:
            doc_path (str, optional): 文档路径，None表示创建新文档
        
        Returns:
            bool: 是否成功
        """
        try:
            if doc_path and os.path.exists(doc_path):
                print(f"正在打开文档: {doc_path}")
                self.document = self.word_app.Documents.Open(os.path.abspath(doc_path))
            else:
                print("创建新文档")
                self.document = self.word_app.Documents.Add()
            
            # 获取VBA项目
            self.vb_project = self.document.VBProject
            print("文档准备完成")
            return True
        except Exception as e:
            print(f"打开/创建文档失败: {e}")
            return False
    
    def enable_vba_access(self):
        """
        启用VBA项目访问（需要在Word中手动设置信任中心）
        
        Returns:
            bool: 是否可以访问VBA项目
        """
        try:
            # 尝试访问VBA项目
            component_count = self.vb_project.VBComponents.Count
            print(f"VBA项目访问正常，当前有 {component_count} 个组件")
            return True
        except Exception as e:
            print(f"VBA项目访问被拒绝: {e}")
            print("请在Word中启用VBA项目访问：")
            print("文件 → 选项 → 信任中心 → 信任中心设置 → 宏设置")
            print("勾选'信任对VBA项目对象模型的访问'")
            return False
    
    def import_macro_file(self, macro_file_path, component_name=None):
        """
        导入宏文件到Word文档
        
        Args:
            macro_file_path (str): 宏文件路径(.bas, .cls, .frm)
            component_name (str, optional): 组件名称，None表示使用文件名
        
        Returns:
            bool: 是否成功导入
        """
        try:
            if not os.path.exists(macro_file_path):
                print(f"宏文件不存在: {macro_file_path}")
                return False
            
            file_ext = os.path.splitext(macro_file_path)[1].lower()
            if file_ext not in ['.bas', '.cls', '.frm']:
                print(f"不支持的文件类型: {file_ext}")
                print("支持的文件类型: .bas (模块), .cls (类模块), .frm (窗体)")
                return False
            
            print(f"正在导入宏文件: {macro_file_path}")
            
            if file_ext == '.bas':
                # 对于.bas文件，使用文本读取方式避免编码问题
                with open(macro_file_path, 'r', encoding='utf-8') as f:
                    macro_code = f.read()
                
                module_name = component_name or os.path.splitext(os.path.basename(macro_file_path))[0]
                return self.import_macro_from_text(macro_code, module_name)
            else:
                # 对于.cls和.frm文件，使用直接导入
                imported_component = self.vb_project.VBComponents.Import(os.path.abspath(macro_file_path))
                
                # 如果指定了组件名称，则重命名
                if component_name:
                    imported_component.Name = component_name
                    print(f"组件已重命名为: {component_name}")
                
                print(f"宏文件导入成功: {imported_component.Name}")
            return True
            
        except Exception as e:
            print(f"导入宏文件失败: {e}")
            return False
    
    def import_macro_from_text(self, macro_code, module_name="ImportedMacro"):
        """
        从文本代码导入宏
        
        Args:
            macro_code (str): VBA代码文本
            module_name (str): 模块名称
        
        Returns:
            bool: 是否成功导入
        """
        try:
            print(f"正在创建模块: {module_name}")
            
            # 创建新的标准模块
            new_module = self.vb_project.VBComponents.Add(1)  # vbext_ct_StdModule = 1
            new_module.Name = module_name
            
            # 添加代码
            code_module = new_module.CodeModule
            code_module.AddFromString(macro_code)
            
            print(f"宏代码导入成功: {module_name}")
            return True
            
        except Exception as e:
            print(f"导入宏代码失败: {e}")
            return False
    
    def create_macro_from_file(self, text_file_path, module_name=None):
        """
        从文本文件创建宏
        
        Args:
            text_file_path (str): 包含VBA代码的文本文件路径
            module_name (str, optional): 模块名称
        
        Returns:
            bool: 是否成功
        """
        try:
            if not os.path.exists(text_file_path):
                print(f"文件不存在: {text_file_path}")
                return False
            
            # 读取文件内容
            with open(text_file_path, 'r', encoding='utf-8') as f:
                macro_code = f.read()
            
            if not module_name:
                module_name = os.path.splitext(os.path.basename(text_file_path))[0]
            
            return self.import_macro_from_text(macro_code, module_name)
            
        except Exception as e:
            print(f"从文件创建宏失败: {e}")
            return False
    
    def export_macro_to_file(self, component_name, output_path):
        """
        导出宏到文件
        
        Args:
            component_name (str): 组件名称
            output_path (str): 输出文件路径
        
        Returns:
            bool: 是否成功导出
        """
        try:
            # 查找组件
            component = None
            for i in range(1, self.vb_project.VBComponents.Count + 1):
                comp = self.vb_project.VBComponents(i)
                if comp.Name == component_name:
                    component = comp
                    break
            
            if not component:
                print(f"未找到组件: {component_name}")
                return False
            
            print(f"正在导出组件: {component_name}")
            component.Export(os.path.abspath(output_path))
            print(f"导出成功: {output_path}")
            return True
            
        except Exception as e:
            print(f"导出组件失败: {e}")
            return False
    
    def list_components(self):
        """
        列出文档中的所有VBA组件
        
        Returns:
            list: 组件信息列表
        """
        try:
            components = []
            component_types = {
                1: "标准模块",
                2: "类模块", 
                3: "窗体",
                100: "文档模块"
            }
            
            print("\n当前文档中的VBA组件:")
            print("-" * 50)
            
            for i in range(1, self.vb_project.VBComponents.Count + 1):
                component = self.vb_project.VBComponents(i)
                comp_type = component_types.get(component.Type, "未知类型")
                
                comp_info = {
                    'name': component.Name,
                    'type': comp_type,
                    'type_code': component.Type
                }
                components.append(comp_info)
                
                print(f"{i}. {component.Name} ({comp_type})")
            
            return components
            
        except Exception as e:
            print(f"获取组件列表失败: {e}")
            return []
    
    def save_document(self, save_path=None, enable_macros=True):
        """
        保存文档
        
        Args:
            save_path (str, optional): 保存路径
            enable_macros (bool): 是否保存为启用宏的格式
        
        Returns:
            bool: 是否成功保存
        """
        try:
            if save_path:
                if enable_macros:
                    # 保存为启用宏的Word文档 (.docm)
                    if not save_path.lower().endswith('.docm'):
                        save_path = os.path.splitext(save_path)[0] + '.docm'
                    print(f"正在保存为启用宏的文档: {save_path}")
                    self.document.SaveAs2(os.path.abspath(save_path), FileFormat=13)  # wdFormatXMLDocumentMacroEnabled
                else:
                    print(f"正在保存文档: {save_path}")
                    self.document.SaveAs2(os.path.abspath(save_path))
            else:
                print("保存文档到当前位置")
                self.document.Save()
            
            print("文档保存成功")
            return True
            
        except Exception as e:
            print(f"保存文档失败: {e}")
            return False
    
    def close_document(self):
        """
        关闭文档
        """
        try:
            if self.document:
                self.document.Close()
                print("文档已关闭")
        except Exception as e:
            print(f"关闭文档时出错: {e}")
    
    def quit_word(self):
        """
        退出Word应用程序并清理COM
        """
        try:
            if self.word_app:
                self.word_app.Quit()
                print("Word应用程序已退出")
        except Exception as e:
            print(f"退出Word时出错: {e}")
        finally:
            # 清理COM
            try:
                pythoncom.CoUninitialize()
            except:
                pass

def batch_import_macros(doc_path, macro_files, output_path=None):
    """
    批量导入宏文件
    
    Args:
        doc_path (str): Word文档路径（None表示创建新文档）
        macro_files (list): 宏文件路径列表
        output_path (str, optional): 输出文档路径
    
    Returns:
        bool: 是否成功
    """
    importer = WordMacroImporter()
    
    try:
        # 连接Word
        if not importer.connect_to_word(visible=True):
            return False
        
        # 打开或创建文档
        if not importer.open_or_create_document(doc_path):
            return False
        
        # 检查VBA访问权限
        if not importer.enable_vba_access():
            return False
        
        # 批量导入宏文件
        success_count = 0
        for macro_file in macro_files:
            if importer.import_macro_file(macro_file):
                success_count += 1
        
        print(f"\n导入完成: {success_count}/{len(macro_files)} 个文件成功")
        
        # 显示组件列表
        importer.list_components()
        
        # 保存文档
        if output_path or doc_path:
            save_path = output_path or doc_path
            importer.save_document(save_path, enable_macros=True)
        
        return success_count > 0
        
    except Exception as e:
        print(f"批量导入过程中出现错误: {e}")
        return False
    
    finally:
        importer.close_document()
        importer.quit_word()

def create_sample_macro_file(file_path):
    """
    创建示例宏文件
    
    Args:
        file_path (str): 输出文件路径
    """
    sample_macro = '''Attribute VB_Name = "SampleMacro"
' 示例宏模块
' 这是一个简单的示例宏

Sub HelloWorld()
    MsgBox "Hello, World! 这是导入的宏！", vbInformation, "示例宏"
End Sub

Sub SetChineseFontToSongTi()
    ' 将中文字符设置为宋体
    Dim rng As Range
    Set rng = ActiveDocument.Range
    
    With rng.Find
        .Text = "[一-龯]"
        .MatchWildcards = True
        .Replacement.Text = ""
        .Replacement.Font.NameFarEast = "宋体"
        .Execute Replace:=wdReplaceAll
    End With
    
    MsgBox "中文字符已设置为宋体！", vbInformation
End Sub

Sub FormatDocument()
    ' 格式化文档
    With ActiveDocument.Range.Font
        .Name = "Times New Roman"
        .Size = 12
    End With
    
    MsgBox "文档格式化完成！", vbInformation
End Sub
'''
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(sample_macro)
        print(f"示例宏文件已创建: {file_path}")
    except Exception as e:
        print(f"创建示例文件失败: {e}")

def main():
    """
    主函数 - 配置和运行
    """
    print("Word宏文件导入器")
    print("=" * 50)
    
    # ==================== 配置区域 ====================
    
    # 目标Word文档（None表示创建新文档）
    TARGET_DOC = r"C:\Users\<USER>\Desktop\11.docx"  # 指定要添加宏的原文档
    
    # 要导入的宏文件列表
    MACRO_FILES = [
        r"C:\Users\<USER>\Desktop\SetChineseFontSimple.bas",
        # r"C:\path\to\macro1.bas",
        # r"C:\path\to\macro2.cls",
    ]
    
    # 输出文档路径
    OUTPUT_DOC = r"C:\Users\<USER>\Desktop\13.docm"  # 保存为启用宏的格式
    
    # 是否创建示例宏文件
    CREATE_SAMPLE = False  # 使用现有的SetChineseFontSimple.bas文件
    SAMPLE_FILE_PATH = r"C:\Users\<USER>\Desktop\sample_macro.bas"
    
    # ==================== 执行区域 ====================
    
    # 检查依赖
    try:
        import win32com.client
    except ImportError:
        print("错误: 未安装pywin32库")
        print("请运行: pip install pywin32")
        return
    
    # 创建示例宏文件
    if CREATE_SAMPLE:
        create_sample_macro_file(SAMPLE_FILE_PATH)
        MACRO_FILES.append(SAMPLE_FILE_PATH)
    
    if not MACRO_FILES:
        print("警告: 没有指定要导入的宏文件")
        print("请在配置区域添加宏文件路径")
        return
    
    # 执行批量导入
    print(f"\n准备导入 {len(MACRO_FILES)} 个宏文件...")
    success = batch_import_macros(TARGET_DOC, MACRO_FILES, OUTPUT_DOC)
    
    if success:
        print("\n[成功] 宏导入任务完成！")
        print(f"文档已保存到: {OUTPUT_DOC}")
        print("\n使用提示:")
        print("1. 打开生成的.docm文件")
        print("2. 按Alt+F11打开VBA编辑器查看导入的宏")
        print("3. 按Alt+F8运行宏")
    else:
        print("\n[失败] 宏导入任务失败")
        print("\n故障排除:")
        print("1. 确保Word已安装且可以正常运行")
        print("2. 在Word中启用VBA项目访问")
        print("3. 检查宏文件路径是否正确")
        print("4. 确保宏文件格式正确(.bas, .cls, .frm)")

if __name__ == "__main__":
    main()