# DOCM到DOCX转换器使用说明

## 📋 概述

本工具包提供了将Word宏文档（.docm）转换为普通Word文档（.docx）的功能。转换后的文档将失去宏功能，但可以在不支持宏的环境中正常使用。

## 📁 文件说明

| 文件名 | 功能 | 适用场景 |
|--------|------|----------|
| `docm_to_docx_converter.py` | 完整版转换器 | 批量转换、命令行使用 |
| `simple_docm_to_docx.py` | 简化版转换器 | 单文件快速转换 |

## 🚀 快速开始

### 方法一：使用简化版转换器

1. **修改配置**：编辑 `simple_docm_to_docx.py` 文件中的路径
   ```python
   INPUT_DOCM = r"C:\Users\<USER>\Desktop\12.docm"  # 你的DOCM文件路径
   OUTPUT_DOCX = r"C:\Users\<USER>\Desktop\12_converted.docx"  # 输出路径
   ```

2. **运行脚本**：
   ```bash
   python simple_docm_to_docx.py
   ```

### 方法二：使用完整版转换器

#### 单文件转换
```bash
# 自动生成输出文件名
python docm_to_docx_converter.py document.docm

# 指定输出文件名
python docm_to_docx_converter.py document.docm output.docx
```

#### 批量转换
```bash
# 转换目录中所有DOCM文件
python docm_to_docx_converter.py --batch ./input_folder

# 指定输出目录
python docm_to_docx_converter.py --batch ./input_folder ./output_folder
```

## 💻 代码示例

### 在Python脚本中使用

```python
from docm_to_docx_converter import convert_docm_to_docx, batch_convert_docm_to_docx

# 单文件转换
success = convert_docm_to_docx(
    docm_path="C:/path/to/document.docm",
    output_path="C:/path/to/document.docx"
)

# 批量转换
success_count, fail_count = batch_convert_docm_to_docx(
    input_directory="C:/path/to/docm_files",
    output_directory="C:/path/to/docx_files"
)
```

## ⚠️ 重要注意事项

### 转换限制
- **宏代码会丢失**：转换后的DOCX文件不包含任何VBA宏代码
- **文档内容保留**：文本、格式、图片等内容完全保留
- **兼容性提升**：DOCX文件可在更多环境中打开

### 使用场景
✅ **适合转换的情况**：
- 需要在不支持宏的环境中查看文档
- 分享文档给不需要宏功能的用户
- 存档保存文档内容
- 减小文件大小

❌ **不适合转换的情况**：
- 文档依赖宏功能进行操作
- 需要保留自动化功能
- 文档包含重要的VBA代码

## 🔧 技术细节

### 文件格式对比

| 特性 | .docm | .docx |
|------|-------|-------|
| 宏支持 | ✅ 支持 | ❌ 不支持 |
| 文件大小 | 较大 | 较小 |
| 安全性 | 可能包含恶意宏 | 更安全 |
| 兼容性 | 需要支持宏的环境 | 广泛兼容 |

### 转换原理

1. **打开DOCM文件**：使用Word COM接口打开源文件
2. **格式转换**：调用SaveAs2方法，指定FileFormat=16（DOCX格式）
3. **自动清理**：Word会自动移除宏代码，保留文档内容
4. **保存文件**：生成新的DOCX文件

## 🛠️ 故障排除

### 常见错误及解决方案

| 错误信息 | 可能原因 | 解决方案 |
|----------|----------|----------|
| "文件不存在" | 路径错误 | 检查文件路径是否正确 |
| "连接Word失败" | Word未安装 | 安装Microsoft Word |
| "权限被拒绝" | 文件被占用 | 关闭Word，确保文件未被使用 |
| "转换失败" | 文件损坏 | 尝试在Word中手动打开文件 |

### 环境要求

- **操作系统**：Windows
- **Python版本**：3.6+
- **依赖库**：pywin32
- **软件要求**：Microsoft Word

### 安装依赖

```bash
pip install pywin32
```

## 📝 使用示例

### 示例1：转换单个文件

```python
# 转换桌面上的12.docm文件
from simple_docm_to_docx import convert_docm_to_docx

input_file = r"C:\Users\<USER>\Desktop\12.docm"
output_file = r"C:\Users\<USER>\Desktop\12_no_macro.docx"

if convert_docm_to_docx(input_file, output_file):
    print("转换成功！")
else:
    print("转换失败！")
```

### 示例2：批量转换

```python
# 转换整个文件夹中的所有DOCM文件
from docm_to_docx_converter import batch_convert_docm_to_docx

input_folder = r"C:\Users\<USER>\Desktop\导入宏"
output_folder = r"C:\Users\<USER>\Desktop\转换后的文档"

success, failed = batch_convert_docm_to_docx(input_folder, output_folder)
print(f"成功转换 {success} 个文件，失败 {failed} 个文件")
```

## 🔄 与宏导入器的关系

本转换器是Word宏导入器工具包的补充工具：

- **宏导入器**：将宏添加到文档中，生成DOCM文件
- **格式转换器**：将DOCM文件转换为DOCX文件，移除宏

这样形成了完整的工作流程：
```
原始文档(.docx) → 添加宏 → 宏文档(.docm) → 移除宏 → 普通文档(.docx)
```

## 📞 技术支持

如果遇到问题，请检查：
1. Python和pywin32是否正确安装
2. Microsoft Word是否可以正常运行
3. 文件路径是否正确
4. 文件是否被其他程序占用

---

**版本信息**：v1.0  
**更新日期**：2024年1月  
**兼容性**：Windows + Microsoft Word + Python 3.6+