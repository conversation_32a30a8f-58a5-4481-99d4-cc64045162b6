# -*- coding: utf-8 -*-
"""
DOCM到DOCX转换器
功能：将包含宏的Word文档(.docm)转换为普通Word文档(.docx)
注意：转换过程中宏代码会丢失
依赖：pip install pywin32
"""

import win32com.client
import os
import sys
from pathlib import Path

def convert_docm_to_docx(docm_path, output_path=None):
    """
    将DOCM文件转换为DOCX文件
    
    Args:
        docm_path (str): 输入的DOCM文件路径
        output_path (str, optional): 输出的DOCX文件路径，如果为None则自动生成
    
    Returns:
        bool: 转换是否成功
    """
    word_app = None
    document = None
    
    try:
        # 检查输入文件
        if not os.path.exists(docm_path):
            print(f"[错误] 输入文件不存在: {docm_path}")
            return False
        
        if not docm_path.lower().endswith('.docm'):
            print(f"[错误] 输入文件不是DOCM格式: {docm_path}")
            return False
        
        # 生成输出路径
        if output_path is None:
            output_path = os.path.splitext(docm_path)[0] + '.docx'
        
        # 确保输出路径是DOCX格式
        if not output_path.lower().endswith('.docx'):
            output_path = os.path.splitext(output_path)[0] + '.docx'
        
        print(f"正在转换: {docm_path} -> {output_path}")
        
        # 启动Word应用程序
        print("正在启动Word...")
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = False  # 后台运行
        
        # 打开DOCM文件
        print("正在打开DOCM文件...")
        document = word_app.Documents.Open(os.path.abspath(docm_path))
        
        # 检查文档是否包含宏
        vb_project = document.VBProject
        macro_count = 0
        for component in vb_project.VBComponents:
            if component.Type in [1, 2, 3]:  # Module, ClassModule, UserForm
                macro_count += 1
        
        if macro_count > 0:
            print(f"[警告] 检测到 {macro_count} 个宏组件，转换后将丢失")
        
        # 保存为DOCX格式
        print("正在保存为DOCX格式...")
        # FileFormat=16 对应 wdFormatXMLDocument (DOCX格式)
        document.SaveAs2(os.path.abspath(output_path), FileFormat=16)
        
        print(f"[成功] 转换完成: {output_path}")
        return True
        
    except Exception as e:
        print(f"[错误] 转换失败: {e}")
        return False
        
    finally:
        # 清理资源
        try:
            if document:
                document.Close()
            if word_app:
                word_app.Quit()
        except:
            pass

def batch_convert_docm_to_docx(input_directory, output_directory=None):
    """
    批量转换目录中的所有DOCM文件
    
    Args:
        input_directory (str): 输入目录路径
        output_directory (str, optional): 输出目录路径，如果为None则使用输入目录
    
    Returns:
        tuple: (成功数量, 失败数量)
    """
    if not os.path.exists(input_directory):
        print(f"[错误] 输入目录不存在: {input_directory}")
        return 0, 0
    
    if output_directory is None:
        output_directory = input_directory
    
    # 创建输出目录
    os.makedirs(output_directory, exist_ok=True)
    
    # 查找所有DOCM文件
    docm_files = list(Path(input_directory).glob('*.docm'))
    
    if not docm_files:
        print(f"[信息] 在目录中未找到DOCM文件: {input_directory}")
        return 0, 0
    
    print(f"找到 {len(docm_files)} 个DOCM文件")
    
    success_count = 0
    fail_count = 0
    
    for docm_file in docm_files:
        output_file = os.path.join(output_directory, docm_file.stem + '.docx')
        
        if convert_docm_to_docx(str(docm_file), output_file):
            success_count += 1
        else:
            fail_count += 1
    
    print(f"\n批量转换完成: 成功 {success_count} 个，失败 {fail_count} 个")
    return success_count, fail_count

def main():
    """
    主函数 - 命令行接口
    """
    print("DOCM到DOCX转换器")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  单文件转换: python docm_to_docx_converter.py <docm文件路径> [输出路径]")
        print("  批量转换:   python docm_to_docx_converter.py --batch <输入目录> [输出目录]")
        print("\n示例:")
        print("  python docm_to_docx_converter.py document.docm")
        print("  python docm_to_docx_converter.py document.docm output.docx")
        print("  python docm_to_docx_converter.py --batch ./input_folder ./output_folder")
        return
    
    if sys.argv[1] == '--batch':
        # 批量转换模式
        if len(sys.argv) < 3:
            print("[错误] 请指定输入目录")
            return
        
        input_dir = sys.argv[2]
        output_dir = sys.argv[3] if len(sys.argv) > 3 else None
        
        batch_convert_docm_to_docx(input_dir, output_dir)
    else:
        # 单文件转换模式
        docm_path = sys.argv[1]
        output_path = sys.argv[2] if len(sys.argv) > 2 else None
        
        convert_docm_to_docx(docm_path, output_path)

if __name__ == "__main__":
    # 配置示例
    EXAMPLE_CONFIG = {
        # 单文件转换示例
        "single_file": {
            "input": r"C:\Users\<USER>\Desktop\12.docm",
            "output": r"C:\Users\<USER>\Desktop\12_converted.docx"
        },
        
        # 批量转换示例
        "batch_convert": {
            "input_directory": r"C:\Users\<USER>\Desktop",
            "output_directory": r"C:\Users\<USER>\Desktop\converted_docx"
        }
    }
    
    # 如果直接运行脚本，显示使用说明
    if len(sys.argv) == 1:
        print("DOCM到DOCX转换器 - 使用示例")
        print("=" * 50)
        print("\n1. 单文件转换:")
        print(f"   convert_docm_to_docx('{EXAMPLE_CONFIG['single_file']['input']}')")
        print("\n2. 批量转换:")
        print(f"   batch_convert_docm_to_docx('{EXAMPLE_CONFIG['batch_convert']['input_directory']}')")
        print("\n3. 命令行使用:")
        print("   python docm_to_docx_converter.py document.docm")
        print("   python docm_to_docx_converter.py --batch ./input_folder")
        print("\n[警告] 转换过程中宏代码会丢失！")
        
        # 交互式转换
        choice = input("\n是否要进行交互式转换？(y/n): ").lower()
        if choice == 'y':
            file_path = input("请输入DOCM文件路径: ").strip('"')
            if file_path:
                convert_docm_to_docx(file_path)
    else:
        main()