# -*- coding: utf-8 -*-
"""
简化版DOCM到DOCX转换器
功能：快速将DOCM文档转换为DOCX文档
注意：转换后宏代码会丢失
依赖：pip install pywin32
"""

import win32com.client
import os

def convert_docm_to_docx(docm_file, docx_file=None):
    """
    将DOCM文件转换为DOCX文件
    
    Args:
        docm_file (str): 输入的DOCM文件路径
        docx_file (str, optional): 输出的DOCX文件路径
    
    Returns:
        bool: 转换是否成功
    """
    word_app = None
    document = None
    
    try:
        # 检查输入文件
        if not os.path.exists(docm_file):
            print(f"[错误] 文件不存在: {docm_file}")
            return False
        
        # 生成输出文件名
        if docx_file is None:
            docx_file = os.path.splitext(docm_file)[0] + '.docx'
        
        print(f"正在转换: {os.path.basename(docm_file)} -> {os.path.basename(docx_file)}")
        
        # 启动Word
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = False
        
        # 打开DOCM文件
        document = word_app.Documents.Open(os.path.abspath(docm_file))
        
        # 保存为DOCX格式 (FileFormat=16 = wdFormatXMLDocument)
        document.SaveAs2(os.path.abspath(docx_file), FileFormat=16)
        
        print(f"[成功] 转换完成: {docx_file}")
        return True
        
    except Exception as e:
        print(f"[错误] 转换失败: {e}")
        return False
        
    finally:
        # 清理资源
        try:
            if document:
                document.Close()
            if word_app:
                word_app.Quit()
        except:
            pass

if __name__ == "__main__":
    # 配置文件路径
    INPUT_DOCM = r"C:\Users\<USER>\Desktop\12.docm"  # 输入的DOCM文件
    OUTPUT_DOCX = r"C:\Users\<USER>\Desktop\12_converted.docx"  # 输出的DOCX文件
    
    print("简化版DOCM到DOCX转换器")
    print("=" * 40)
    print("[警告] 转换后宏代码会丢失！")
    print()
    
    # 执行转换
    success = convert_docm_to_docx(INPUT_DOCM, OUTPUT_DOCX)
    
    if success:
        print("\n转换成功！")
        print(f"原文件: {INPUT_DOCM}")
        print(f"新文件: {OUTPUT_DOCX}")
        print("\n注意事项:")
        print("1. 新的DOCX文件不包含宏代码")
        print("2. 文档内容和格式保持不变")
        print("3. 可以在不支持宏的环境中正常打开")
    else:
        print("\n转换失败！")
        print("请检查:")
        print("1. 输入文件是否存在")
        print("2. Word是否已安装")
        print("3. 文件是否被其他程序占用")
    
    input("\n按回车键退出...")