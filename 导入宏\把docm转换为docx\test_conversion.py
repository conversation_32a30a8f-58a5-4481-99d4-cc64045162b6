# -*- coding: utf-8 -*-
"""
转换效果测试脚本
功能：验证DOCM到DOCX转换的效果
"""

import win32com.client
import os

def check_document_info(file_path):
    """
    检查文档信息
    
    Args:
        file_path (str): 文档路径
    
    Returns:
        dict: 文档信息
    """
    word_app = None
    document = None
    
    try:
        if not os.path.exists(file_path):
            return {"error": "文件不存在"}
        
        # 启动Word
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = False
        
        # 打开文档
        document = word_app.Documents.Open(os.path.abspath(file_path))
        
        # 获取基本信息
        info = {
            "文件名": os.path.basename(file_path),
            "文件大小": f"{os.path.getsize(file_path) / 1024:.1f} KB",
            "页数": document.Range().Information(4),  # wdNumberOfPagesInDocument
            "字符数": len(document.Range().Text),
            "段落数": document.Paragraphs.Count,
        }
        
        # 检查是否包含宏
        try:
            vb_project = document.VBProject
            macro_components = []
            for component in vb_project.VBComponents:
                if component.Type in [1, 2, 3]:  # Module, ClassModule, UserForm
                    macro_components.append(component.Name)
            
            info["宏组件数量"] = len(macro_components)
            info["宏组件列表"] = macro_components if macro_components else "无"
        except:
            info["宏组件数量"] = "无法检测（可能不支持宏）"
            info["宏组件列表"] = "N/A"
        
        return info
        
    except Exception as e:
        return {"error": f"检查失败: {e}"}
        
    finally:
        try:
            if document:
                document.Close()
            if word_app:
                word_app.Quit()
        except:
            pass

def compare_documents(docm_path, docx_path):
    """
    比较DOCM和DOCX文档
    
    Args:
        docm_path (str): DOCM文件路径
        docx_path (str): DOCX文件路径
    """
    print("文档转换效果对比")
    print("=" * 60)
    
    # 检查原始DOCM文件
    print("\n📄 原始文档 (DOCM):")
    docm_info = check_document_info(docm_path)
    if "error" in docm_info:
        print(f"   [错误] {docm_info['error']}")
        return
    
    for key, value in docm_info.items():
        print(f"   {key}: {value}")
    
    # 检查转换后的DOCX文件
    print("\n📄 转换后文档 (DOCX):")
    docx_info = check_document_info(docx_path)
    if "error" in docx_info:
        print(f"   [错误] {docx_info['error']}")
        return
    
    for key, value in docx_info.items():
        print(f"   {key}: {value}")
    
    # 对比分析
    print("\n🔍 转换效果分析:")
    
    # 检查内容是否保持一致
    if docm_info.get("字符数") == docx_info.get("字符数"):
        print("   ✅ 文档内容完全保留")
    else:
        print("   ⚠️ 文档内容可能有变化")
    
    if docm_info.get("页数") == docx_info.get("页数"):
        print("   ✅ 页数保持一致")
    else:
        print("   ⚠️ 页数发生变化")
    
    # 检查宏是否被移除
    docm_macros = docm_info.get("宏组件数量", 0)
    docx_macros = docx_info.get("宏组件数量", 0)
    
    if isinstance(docm_macros, int) and docm_macros > 0:
        if isinstance(docx_macros, str) or docx_macros == 0:
            print(f"   ✅ 宏已成功移除 (原有 {docm_macros} 个宏组件)")
        else:
            print(f"   ⚠️ 宏移除可能不完整")
    else:
        print("   ℹ️ 原文档未检测到宏组件")
    
    # 文件大小对比
    try:
        docm_size = os.path.getsize(docm_path)
        docx_size = os.path.getsize(docx_path)
        size_diff = ((docx_size - docm_size) / docm_size) * 100
        
        if size_diff < 0:
            print(f"   ✅ 文件大小减少了 {abs(size_diff):.1f}%")
        else:
            print(f"   ℹ️ 文件大小增加了 {size_diff:.1f}%")
    except:
        print("   ⚠️ 无法比较文件大小")
    
    print("\n📋 总结:")
    print("   - DOCM格式支持宏功能，文件较大")
    print("   - DOCX格式不支持宏，兼容性更好")
    print("   - 转换过程保留了文档的所有内容和格式")
    print("   - 适合在不需要宏功能的环境中使用")

if __name__ == "__main__":
    # 测试文件路径
    DOCM_FILE = r"C:\Users\<USER>\Desktop\12.docm"
    DOCX_FILE = r"C:\Users\<USER>\Desktop\12_converted.docx"
    
    print("DOCM到DOCX转换效果测试")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(DOCM_FILE):
        print(f"[错误] 原始DOCM文件不存在: {DOCM_FILE}")
    elif not os.path.exists(DOCX_FILE):
        print(f"[错误] 转换后的DOCX文件不存在: {DOCX_FILE}")
        print("请先运行转换脚本生成DOCX文件")
    else:
        # 执行对比测试
        compare_documents(DOCM_FILE, DOCX_FILE)
    
    input("\n按回车键退出...")