# Word宏自动运行器

这是一个Python工具集，用于自动化运行Word文档中的宏并保存结果。

## 文件说明

### 主要脚本

1. **`word_macro_runner.py`** - 完整功能版本
   - 提供完整的Word自动化功能
   - 包含错误处理和宏列表功能
   - 适合复杂的自动化需求

2. **`simple_word_macro.py`** - 简化版本
   - 简洁易用的接口
   - 支持单文件和批量处理
   - 适合快速使用

3. **`install_requirements.bat`** - 依赖安装脚本
   - 自动安装所需的Python库
   - 双击运行即可

## 安装步骤

### 1. 安装Python依赖

**方法一：使用批处理文件（推荐）**
```bash
双击运行 install_requirements.bat
```

**方法二：手动安装**
```bash
pip install pywin32
```

### 2. 配置脚本

编辑脚本中的配置参数：

```python
# 在 simple_word_macro.py 中修改这些参数
WORD_FILE = r"C:\Users\<USER>\Desktop\11.docx"  # Word文档路径
MACRO_NAME = "SetChineseFontToSongTi"  # 宏名称
SAVE_AS = None  # 另存为路径（可选）
SHOW_WORD = True  # 是否显示Word界面
```

## 使用方法

### 快速开始（推荐）

1. 双击运行 `install_requirements.bat` 安装依赖
2. 编辑 `simple_word_macro.py` 中的配置参数
3. 运行脚本：
   ```bash
   python simple_word_macro.py
   ```

### 高级用法

#### 单文件处理

```python
from simple_word_macro import run_word_macro

# 运行宏并保存到原位置
success = run_word_macro(
    doc_path="C:\\path\\to\\document.docx",
    macro_name="YourMacroName"
)

# 运行宏并另存为新文件
success = run_word_macro(
    doc_path="C:\\path\\to\\document.docx",
    macro_name="YourMacroName",
    save_path="C:\\path\\to\\output.docx",
    visible=True  # 显示Word界面
)
```

#### 批量处理

```python
from simple_word_macro import batch_run_macro

file_list = [
    "C:\\path\\to\\doc1.docx",
    "C:\\path\\to\\doc2.docx",
    "C:\\path\\to\\doc3.docx"
]

batch_run_macro(
    file_list=file_list,
    macro_name="YourMacroName",
    output_dir="C:\\path\\to\\output_folder"
)
```

#### 使用完整版本

```python
from word_macro_runner import WordMacroRunner

runner = WordMacroRunner()
runner.connect_to_word(visible=True)
runner.open_document("C:\\path\\to\\document.docx")

# 列出可用的宏
macros = runner.list_available_macros()
print("可用的宏:", macros)

# 运行宏
runner.run_macro("YourMacroName")
runner.save_document()
runner.close_document()
runner.quit_word()
```

## 常见问题

### 1. 宏运行失败

**可能原因：**
- 宏名称不正确
- Word宏安全设置阻止运行
- 文档中不包含指定的宏
- 宏代码存在错误

**解决方法：**
1. 检查宏名称是否正确（区分大小写）
2. 在Word中设置宏安全级别：
   - 文件 → 选项 → 信任中心 → 信任中心设置 → 宏设置
   - 选择"启用所有宏"或"禁用所有宏，并发出通知"
3. 确认文档包含指定的宏
4. 手动测试宏是否能正常运行

### 2. 文件路径问题

**注意事项：**
- 使用绝对路径
- 路径中的反斜杠需要转义：`r"C:\path\to\file.docx"` 或 `"C:\\path\\to\\file.docx"`
- 确保文件存在且没有被其他程序占用

### 3. 权限问题

**解决方法：**
- 以管理员身份运行Python脚本
- 确保对目标文件和文件夹有读写权限

### 4. Word版本兼容性

**支持的版本：**
- Microsoft Word 2010及以上版本
- Office 365

## 宏名称参考

根据之前的对话，以下是一些常用的宏名称：

- `SetChineseFontToSongTi` - 将中文字符设置为宋体
- `AutoOpen` - 文档打开时自动运行
- `AutoExec` - Word启动时自动运行
- `AutoNew` - 新建文档时自动运行
- `AutoClose` - 文档关闭时自动运行

## 示例配置

### 处理桌面上的Word文档

```python
# simple_word_macro.py 配置示例
WORD_FILE = r"C:\Users\<USER>\Desktop\11.docx"
MACRO_NAME = "SetChineseFontToSongTi"
SAVE_AS = r"C:\Users\<USER>\Desktop\11_processed.docx"  # 另存为新文件
SHOW_WORD = True  # 显示处理过程
```

### 批量处理多个文档

```python
# 启用批量模式
BATCH_MODE = True
BATCH_FILES = [
    r"C:\Users\<USER>\Desktop\11.docx",
    r"C:\Users\<USER>\Desktop\coding.docx",
    r"C:\Users\<USER>\Desktop\java后端\day02-Java基础语法.docx"
]
OUTPUT_DIR = r"C:\Users\<USER>\Desktop\processed_docs"
```

## 技术支持

如果遇到问题，请检查：

1. Python版本（建议3.6+）
2. pywin32库是否正确安装
3. Word是否正确安装
4. 宏安全设置
5. 文件权限

## 许可证

本工具仅供学习和个人使用。使用时请遵守相关法律法规。