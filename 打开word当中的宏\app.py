from google import genai
from google.genai import types
import os
from datetime import datetime
import time

class GeminiChatbot:
    def __init__(self, api_key):
        self.api_key = api_key
        # 使用新版SDK初始化客户端
        self.client = genai.Client(api_key=api_key)
        self.chat_history = []
        
        # 测试模型可用性
        try:
            test_response = self.client.models.generate_content(
                model="gemini-2.5-pro",
                contents="Hello"
            )
            print(f"gemini-2.5-pro 连接成功: {test_response.text[:50]}...")
            self.model_name = "gemini-2.5-pro"
        except Exception as e:
            print(f"gemini-2.5-pro 不可用，尝试 gemini-2.5-flash: {e}")
            try:
                test_response = self.client.models.generate_content(
                    model="gemini-2.5-flash",
                    contents="Hello"
                )
                print(f"gemini-2.5-flash 连接成功: {test_response.text[:50]}...")
                self.model_name = "gemini-2.5-flash"
            except Exception as e2:
                print(f"所有模型都不可用: {e2}")
                self.model_name = "gemini-1.5-pro"  # 最后的回退选项

    def get_response(self, prompt):
        """获取AI响应"""
        try:
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=prompt
            )
            
            # 保存到本地历史记录
            self.chat_history.append({
                "timestamp": datetime.now().isoformat(),
                "user_input": prompt,
                "ai_response": response.text if response else None
            })
            
            return response
        except Exception as e:
            print(f"API调用失败: {e}")
            return None

    def generate_vba_macro(self, user_simple_command: str, max_retries=3):
        """
        接收用户的简单指令，构建专业的动态提示词，并调用Gemini API生成VBA宏。
        """
        # 完整的系统提示词
        system_prompt = """
# 角色与核心使命
你是一位世界顶级的VBA解决方案架构师。你的核心使命是设计并编写出在**性能、健壮性和可维护性**上都达到极致的VBA代码。

# 核心设计原则 (你必须在所有代码中体现这些原则)

## 1. 性能至上原则 (Performance First)
*   **核心思想:** 永远选择批量操作，而不是对集合进行逐项循环。
*   **严禁使用:** `For Each char In selection.Characters` 或任何逐一修改格式的循环
*   **必须使用:** 直接对整个Range对象进行一次性格式设置

## 2. 精准性原则 (Precision)
*   **必须使用:** `.Font.NameFarEast` 设置东亚字体，`.Font.NameAscii` 设置西文字体
*   **严禁使用:** 笼统的 `.Font.Name`

## 3. 完整性原则 (Completeness)
*   **必须处理:** `ActiveDocument.StoryRanges` 集合
*   **必须遍历:** `Shapes` 和 `InlineShapes` 中的文本

## 4. 用户体验原则
*   **必须包含:** `Application.ScreenUpdating = False`
*   **必须包含:** 完整的错误处理
*   **必须包含:** 成功提示

**重要：严格禁止任何形式的逐项循环！必须使用批量操作！**
"""
        
        user_task_prompt = f"""
# 用户任务
应用: Microsoft Word
宏名称: AutoTaskFromUser
功能描述: {user_simple_command}
操作范围: 整个活动文档 (ActiveDocument)

请严格遵循上述所有原则生成VBA代码。
"""
        
        final_prompt = f"{system_prompt}\n\n{user_task_prompt}"
        
        for attempt in range(max_retries):
            try:
                # 使用新版SDK的配置方式
                config = types.GenerateContentConfig(
                    temperature=0.1,  # 非常低的温度确保一致性
                    max_output_tokens=4096,
                    thinking_config=types.ThinkingConfig(thinking_budget=0)  # 禁用思考以提高速度
                )
                
                response = self.client.models.generate_content(
                    model=self.model_name,
                    contents=final_prompt,
                    config=config
                )
                
                if response and hasattr(response, 'text') and response.text:
                    clean_code = self.clean_vba_code(response.text)
                    return clean_code
                else:
                    return "错误：API未能生成有效的内容。"
                    
            except Exception as e:
                print(f"尝试 {attempt + 1}/{max_retries} 失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)  # 等待5秒后重试
                else:
                    return f"生成VBA宏时发生错误: {e}"

    def clean_vba_code(self, raw_code):
        """清理API返回的代码，移除markdown标记"""
        clean_code = raw_code.strip()
        # 清理代码块标记
        if clean_code.startswith('```vba'):
            clean_code = clean_code.lstrip('```vba').strip()
        if clean_code.startswith('```'):
            clean_code = clean_code.lstrip('```').strip()
        if clean_code.endswith('```'):
            clean_code = clean_code.rstrip('```').strip()
        return clean_code

    def test_model(self):
        """测试当前模型是否正常工作"""
        try:
            response = self.client.models.generate_content(
                model=self.model_name,
                contents="请回答：你是什么模型？"
            )
            print(f"模型响应: {response.text}")
            return response.text
        except Exception as e:
            print(f"模型测试失败: {e}")
            return None

    def get_chat_history(self):
        """获取聊天历史"""
        return self.chat_history

    def clear_chat_history(self):
        """清除聊天历史"""
        self.chat_history = []