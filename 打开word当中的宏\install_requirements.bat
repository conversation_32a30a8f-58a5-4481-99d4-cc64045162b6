@echo off
echo ========================================
echo Word宏运行器 - 依赖安装脚本
echo ========================================
echo.

echo 正在安装Python依赖库...
echo.

echo 安装pywin32库（用于Word自动化）...
pip install pywin32

echo.
echo 安装pathlib库（用于路径处理）...
pip install pathlib

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 现在您可以运行以下脚本：
echo 1. word_macro_runner.py - 完整功能版本
echo 2. simple_word_macro.py - 简化版本
echo.
pause