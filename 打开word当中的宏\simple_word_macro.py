#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版Word宏运行器
快速运行Word文档中的宏
"""

import win32com.client
import os
import sys

def run_word_macro(doc_path, macro_name, save_path=None, visible=False):
    """
    运行Word文档中的宏
    
    Args:
        doc_path (str): Word文档路径
        macro_name (str): 宏名称
        save_path (str, optional): 保存路径，None表示保存到原位置
        visible (bool): 是否显示Word界面
    
    Returns:
        bool: 是否成功
    """
    word_app = None
    document = None
    
    try:
        # 检查文件是否存在
        if not os.path.exists(doc_path):
            print(f"错误: 文件不存在 - {doc_path}")
            return False
        
        print(f"正在处理文档: {doc_path}")
        print(f"要运行的宏: {macro_name}")
        
        # 启动Word应用程序
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = visible
        
        # 打开文档
        document = word_app.Documents.Open(os.path.abspath(doc_path))
        print("文档打开成功")
        
        # 运行宏
        print("正在运行宏...")
        word_app.Run(macro_name)
        print("宏运行完成")
        
        # 保存文档
        if save_path:
            print(f"正在另存为: {save_path}")
            document.SaveAs2(os.path.abspath(save_path))
        else:
            print("正在保存文档...")
            document.Save()
        
        print("文档保存成功")
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        return False
        
    finally:
        # 清理资源
        try:
            if document:
                document.Close()
            if word_app:
                word_app.Quit()
        except:
            pass

def batch_run_macro(file_list, macro_name, output_dir=None):
    """
    批量运行多个Word文档中的宏
    
    Args:
        file_list (list): Word文档路径列表
        macro_name (str): 宏名称
        output_dir (str, optional): 输出目录
    """
    success_count = 0
    total_count = len(file_list)
    
    print(f"开始批量处理 {total_count} 个文档")
    print("=" * 50)
    
    for i, file_path in enumerate(file_list, 1):
        print(f"\n[{i}/{total_count}] 处理: {os.path.basename(file_path)}")
        
        if output_dir:
            filename = os.path.basename(file_path)
            name, ext = os.path.splitext(filename)
            save_path = os.path.join(output_dir, f"{name}_processed{ext}")
        else:
            save_path = None
        
        if run_word_macro(file_path, macro_name, save_path):
            success_count += 1
            print("✓ 处理成功")
        else:
            print("✗ 处理失败")
    
    print(f"\n批量处理完成: {success_count}/{total_count} 成功")

def main():
    """
    主函数 - 配置和运行
    """
    # ==================== 配置区域 ====================
    
    # 单个文件处理
    WORD_FILE = r"C:\Users\<USER>\Desktop\12.docm"  # 要处理的Word文档
    MACRO_NAME = "SetChineseFontSimple"  # 要运行的宏名称
    SAVE_AS = None  # 另存为路径，None表示保存到原位置
    SHOW_WORD = True  # 是否显示Word界面
    
    # 批量处理（可选）
    BATCH_MODE = False  # 是否启用批量模式
    BATCH_FILES = [  # 批量处理的文件列表
        r"C:\Users\<USER>\Desktop\11.docx",
        r"C:\Users\<USER>\Desktop\coding.docx",
        # 添加更多文件...
    ]
    OUTPUT_DIR = r"C:\Users\<USER>\Desktop\processed"  # 批量处理输出目录
    
    # ==================== 执行区域 ====================
    
    print("Word宏自动运行器 - 简化版")
    print("=" * 40)
    
    # 检查依赖
    try:
        import win32com.client
    except ImportError:
        print("错误: 未安装pywin32库")
        print("请运行: pip install pywin32")
        return
    
    if BATCH_MODE:
        # 批量处理模式
        if OUTPUT_DIR and not os.path.exists(OUTPUT_DIR):
            os.makedirs(OUTPUT_DIR)
            print(f"创建输出目录: {OUTPUT_DIR}")
        
        batch_run_macro(BATCH_FILES, MACRO_NAME, OUTPUT_DIR)
    else:
        # 单文件处理模式
        success = run_word_macro(WORD_FILE, MACRO_NAME, SAVE_AS, SHOW_WORD)
        
        if success:
            print("\n✓ 任务完成成功！")
        else:
            print("\n✗ 任务执行失败")
            print("\n故障排除建议:")
            print("1. 检查Word文档路径是否正确")
            print("2. 检查宏名称是否正确")
            print("3. 确保Word文档包含指定的宏")
            print("4. 检查Word的宏安全设置")
            print("5. 确保Word文档没有被其他程序占用")

if __name__ == "__main__":
    main()