#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Word宏自动运行器
功能：自动打开Word文档，运行指定宏，并保存文档
依赖：pip install pywin32
"""

import win32com.client
import pythoncom  # 添加这个导入
import os
import sys
from pathlib import Path

class WordMacroRunner:
    def __init__(self):
        self.word_app = None
        self.document = None
    
    def connect_to_word(self, visible=False):
        """
        连接到Word应用程序
        
        Args:
            visible (bool): 是否显示Word界面，默认False
        """
        try:
            # 在多线程环境中初始化COM
            pythoncom.CoInitialize()
            
            self.word_app = win32com.client.Dispatch("Word.Application")
            self.word_app.Visible = visible
            print("成功连接到Word应用程序")
            return True
        except Exception as e:
            print(f"连接Word失败: {e}")
            return False
    
    def open_document(self, file_path):
        """
        打开Word文档
        
        Args:
            file_path (str): Word文档的完整路径
        
        Returns:
            bool: 是否成功打开文档
        """
        try:
            if not os.path.exists(file_path):
                print(f"文件不存在: {file_path}")
                return False
            
            # 转换为绝对路径
            abs_path = os.path.abspath(file_path)
            print(f"正在打开文档: {abs_path}")
            
            self.document = self.word_app.Documents.Open(abs_path)
            print("文档打开成功")
            return True
        except Exception as e:
            print(f"打开文档失败: {e}")
            return False
    
    def run_macro(self, macro_name):
        """
        运行指定的宏
        
        Args:
            macro_name (str): 宏的名称
        
        Returns:
            bool: 是否成功运行宏
        """
        try:
            print(f"正在运行宏: {macro_name}")
            
            # 运行宏的几种方式
            # 方式1: 直接运行宏
            self.word_app.Run(macro_name)
            
            # 方式2: 如果宏在特定模块中，可以使用完整路径
            # self.word_app.Run(f"{self.document.Name}!{macro_name}")
            
            # 方式3: 如果宏在Normal模板中
            # self.word_app.Run(f"Normal.{macro_name}")
            
            print("宏运行成功")
            return True
        except Exception as e:
            print(f"运行宏失败: {e}")
            print("可能的原因:")
            print("1. 宏名称不正确")
            print("2. 宏安全设置阻止了宏的运行")
            print("3. 宏中存在错误")
            return False
    
    def save_document(self, save_path=None):
        if self.document:
            try:
                if save_path:
                    # 检查文件扩展名并设置适当的文件格式
                    file_format = None
                    if save_path.lower().endswith('.docx'):
                        file_format = 16  # wdFormatXMLDocument
                    elif save_path.lower().endswith('.docm'):
                        file_format = 17  # wdFormatXMLDocumentMacroEnabled
                    elif save_path.lower().endswith('.doc'):
                        file_format = 0   # wdFormatDocument

                    print(f"正在另存为: {save_path}")
                    if file_format is not None:
                        self.document.SaveAs2(save_path, FileFormat=file_format)
                    else:
                        # 如果没有匹配的格式，则不带格式参数保存
                        self.document.SaveAs2(save_path)
                else:
                    self.document.Save()
                    print(f"文档保存成功")
            except Exception as e:
                print(f"保存文档失败: {e}")
                # raise e

    def close_document(self):
        """
        关闭文档
        """
        try:
            if self.document:
                self.document.Close()
                print("文档已关闭")
        except Exception as e:
            print(f"关闭文档时出错: {e}")
    
    def quit_word(self):
        """
        退出Word应用程序并清理COM
        """
        try:
            if self.word_app:
                self.word_app.Quit()
                print("Word应用程序已退出")
        except Exception as e:
            print(f"退出Word时出错: {e}")
        finally:
            # 清理COM
            try:
                pythoncom.CoUninitialize()
            except:
                pass
    
    def list_available_macros(self):
        """
        列出文档中可用的宏
        
        Returns:
            list: 宏名称列表
        """
        try:
            if not self.document:
                print("请先打开文档")
                return []
            
            macros = []
            
            # 获取文档中的宏
            for i in range(1, self.document.VBProject.VBComponents.Count + 1):
                component = self.document.VBProject.VBComponents(i)
                if component.Type == 1:  # vbext_ct_StdModule
                    code_module = component.CodeModule
                    for line_num in range(1, code_module.CountOfLines + 1):
                        line = code_module.Lines(line_num, 1)
                        if line.strip().startswith("Sub ") and not line.strip().startswith("Sub Auto"):
                            macro_name = line.split("Sub ")[1].split("(")[0].strip()
                            macros.append(macro_name)
            
            return macros
        except Exception as e:
            print(f"获取宏列表失败: {e}")
            return []

def main():
    """
    主函数 - 示例用法
    """
    # 配置参数
    word_file_path = r"C:\Users\<USER>\Desktop\11.docx"  # Word文档路径
    macro_name = "SetChineseFontToSongTi"  # 要运行的宏名称
    save_path = None  # 保存路径，None表示保存到原位置
    
    # 创建运行器实例
    runner = WordMacroRunner()
    
    try:
        # 1. 连接到Word
        if not runner.connect_to_word(visible=True):  # 设置为True可以看到Word界面
            return
        
        # 2. 打开文档
        if not runner.open_document(word_file_path):
            return
        
        # 3. 列出可用的宏（可选）
        print("\n正在检查可用的宏...")
        available_macros = runner.list_available_macros()
        if available_macros:
            print("可用的宏:")
            for macro in available_macros:
                print(f"  - {macro}")
        else:
            print("未找到可用的宏")
        
        # 4. 运行宏
        if not runner.run_macro(macro_name):
            print("\n提示: 如果宏运行失败，请检查:")
            print("1. 宏名称是否正确")
            print("2. Word的宏安全设置")
            print("3. 文档是否包含该宏")
            return
        
        # 5. 保存文档
        if not runner.save_document(save_path):
            return
        
        print("\n所有操作完成成功！")
        
    except Exception as e:
        print(f"程序执行过程中出现错误: {e}")
    
    finally:
        # 6. 清理资源
        runner.close_document()
        runner.quit_word()

def run_macro_simple(word_file, macro_name, save_as=None):
    """
    简化的宏运行函数
    
    Args:
        word_file (str): Word文档路径
        macro_name (str): 宏名称
        save_as (str, optional): 另存为路径
    
    Returns:
        bool: 是否成功
    """
    runner = WordMacroRunner()
    
    try:
        if (runner.connect_to_word() and 
            runner.open_document(word_file) and 
            runner.run_macro(macro_name) and 
            runner.save_document(save_as)):
            print("宏运行完成")
            return True
        else:
            print("宏运行失败")
            return False
    finally:
        runner.close_document()
        runner.quit_word()

if __name__ == "__main__":
    print("Word宏自动运行器")
    print("=" * 50)
    
    # 检查是否安装了pywin32
    try:
        import win32com.client
    except ImportError:
        print("错误: 未安装pywin32库")
        print("请运行: pip install pywin32")
        sys.exit(1)
    
    # 运行主程序
    main()
    
    print("\n程序结束")