@echo off
chcp 65001 >nul
echo ========================================
echo    Markdown Math Formula Converter
echo ========================================
echo.

if "%~1"=="" (
    echo Usage: Drag and drop Markdown file onto this batch file
    echo Or manually input file path
    echo.
    set /p input_file="Please enter Markdown file path: "
) else (
    set "input_file=%~1"
)

if not exist "%input_file%" (
    echo Error: File does not exist - %input_file%
    pause
    exit /b 1
)

echo.
echo Converting file: %input_file%
echo.

python "%~dp0md_math_converter.py" "%input_file%"

if %errorlevel% equ 0 (
    echo.
    echo Success: Conversion completed!
) else (
    echo.
    echo Error: Conversion failed, please check error messages
)

echo.
echo Press any key to exit...
pause >nul