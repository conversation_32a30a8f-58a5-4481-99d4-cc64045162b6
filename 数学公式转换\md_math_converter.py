#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown数学公式转换器
将Markdown文件中的反引号数学公式转换为LaTeX格式，然后使用pandoc转换为Word文档

作者: Assistant
版本: 1.0
"""

import re
import os
import sys
import subprocess
import argparse
from pathlib import Path


class MarkdownMathConverter:
    """Markdown数学公式转换器类"""
    
    def __init__(self):
        self.conversion_patterns = [
            # 基本的反引号数学公式转换
            (r'`([^`]+)`', self._convert_inline_math),
        ]
    
    def _convert_inline_math(self, match):
        """转换内联数学公式"""
        content = match.group(1)
        
        # 检查是否包含数学符号，避免误转换普通代码
        math_indicators = [
            # 积分和微分符号
            '∫', '∮', '∂', '∇', 'd/dx', 'dy/dx', 'df/dx',
            # 求和和乘积
            '∑', '∏', 'Σ', 'Π',
            # 希腊字母
            'α', 'β', 'γ', 'δ', 'ε', 'ζ', 'η', 'θ', 'ι', 'κ', 'λ', 'μ', 'ν', 'ξ', 'π', 'ρ', 'σ', 'τ', 'υ', 'φ', 'χ', 'ψ', 'ω',
            'Α', 'Β', 'Γ', 'Δ', 'Ε', 'Ζ', 'Η', 'Θ', 'Ι', 'Κ', 'Λ', 'Μ', 'Ν', 'Ξ', 'Ο', 'Π', 'Ρ', 'Σ', 'Τ', 'Υ', 'Φ', 'Χ', 'Ψ', 'Ω',
            # 比较和关系符号
            '∞', '≈', '≠', '≤', '≥', '≡', '∝', '∈', '∉', '⊂', '⊆', '∪', '∩', '∅',
            # 逻辑符号
            '∀', '∃', '¬', '∧', '∨', '→', '↔',
            # 运算符号
            '±', '∓', '×', '÷', '√', '∛',
            # 基本数学符号
            '/', '^', '_', '=', '+', '-', '*', '(', ')', '[', ']', '{', '}',
            # 函数
            'exp', 'log', 'ln', 'sin', 'cos', 'tan', 'sinh', 'cosh', 'tanh', 'sqrt', 'lim',
            # 特殊模式
            'to', 'from', 'over', 'under'
        ]
        
        if any(indicator in content for indicator in math_indicators):
            # 进行LaTeX格式转换
            latex_content = self._convert_to_latex(content)
            return f'${latex_content}$'
        else:
            # 不是数学公式，保持原样
            return match.group(0)
    
    def _convert_to_latex(self, content):
        """将数学内容转换为LaTeX格式"""
        # 替换常见的数学符号和格式
        conversions = [
            # 分数转换 - 更全面的模式
            (r'\(([^)]+)\)\s*/\s*\(([^)]+)\)', r'\\frac{\1}{\2}'),
            (r'([a-zA-Z0-9αβγδεζηθλμνπρστφχψωΓΔΘΛΞΠΣΦΨΩ∂∇]+)\s*/\s*([a-zA-Z0-9αβγδεζηθλμνπρστφχψωΓΔΘΛΞΠΣΦΨΩ∂∇]+)', r'\\frac{\1}{\2}'),
            
            # 积分符号 - 增强模式
            (r'∫\[([^\]]+)\s+to\s+([^\]]+)\]', r'∫_{\1}^{\2}'),
            (r'∫\[-∞\s+to\s+∞\]', r'∫_{-∞}^{∞}'),
            (r'∫\[([^\]]+)\]', r'∫_{\1}'),
            (r'∫_([^\s]+)\^([^\s]+)', r'∫_{\1}^{\2}'),
            # 修复积分上下限的多重大括号问题
            (r'∫_{{{([^}]+)}}}\^{{{([^}]+)}}}', r'∫_{\1}^{\2}'),
            (r'∫_{{{([^}]+)}}}', r'∫_{\1}'),
            
            # 偏导数 - 更复杂的模式
            (r'∂([^/]+)/∂([^\s]+)', r'\\frac{\\partial\1}{\\partial\2}'),
            (r'∂²([^/]+)/∂([^\s]+)²', r'\\frac{\\partial²\1}{\\partial\2²}'),
            (r'∂³([^/]+)/∂([^\s]+)³', r'\\frac{\\partial³\1}{\\partial\2³}'),
            
            # 上下标 - 增强模式
            (r'\*\*([^*]+)\*\*', r'\\mathbf{\1}'),  # 粗体向量
            (r'([a-zA-Z])_([a-zA-Z0-9αβγδεζηθλμνπρστφχψωΓΔΘΛΞΠΣΦΨΩ]+)', r'\1_{\2}'),  # 下标
            (r'([a-zA-Z])\^([a-zA-Z0-9αβγδεζηθλμνπρστφχψωΓΔΘΛΞΠΣΦΨΩ\-\+]+)', r'\1^{\2}'),  # 上标
            (r'([a-zA-Z])μ', r'\1_μ'),  # 下标
            (r'([a-zA-Z])ν', r'\1_ν'),  # 下标
            (r'([a-zA-Z])λ', r'\1^λ'),  # 上标
            
            # 围道积分
            (r'∮\[([^\]]+)\]', r'∮_{\1}'),
            (r'∮_([^\s]+)', r'∮_{\1}'),
            
            # 求和、乘积符号
            (r'Σ\[([^\]]+)\]', r'\\sum_{\1}'),
            (r'∑\[([^\]]+)\]', r'\\sum_{\1}'),
            (r'∏\[([^\]]+)\]', r'\\prod_{\1}'),
            (r'∑_([^\s]+)\^([^\s]+)', r'\\sum_{\1}^{\2}'),
            (r'∏_([^\s]+)\^([^\s]+)', r'\\prod_{\1}^{\2}'),
            # 修复求和乘积的多重大括号问题
            (r'\\sum_{{{([^}]+)}}}\^{{{([^}]+)}}}', r'\\sum_{\1}^{\2}'),
            (r'\\prod_{{{([^}]+)}}}\^{{{([^}]+)}}}', r'\\prod_{\1}^{\2}'),
            
            # 指数函数 - 增强模式
            (r'e\^\(([^)]+)\)', r'e^{\1}'),
            (r'e\^\(-([^)]+)\)', r'e^{-\1}'),
            (r'e\^([a-zA-Z0-9αβγδεζηθλμνπρστφχψωΓΔΘΛΞΠΣΦΨΩ\-\+]+)', r'e^{\1}'),
            (r'exp\(([^)]+)\)', r'\\exp(\1)'),
            
            # 三角函数
            (r'\bsin\(([^)]+)\)', r'\\sin(\1)'),
            (r'\bcos\(([^)]+)\)', r'\\cos(\1)'),
            (r'\btan\(([^)]+)\)', r'\\tan(\1)'),
            (r'\bsinh\(([^)]+)\)', r'\\sinh(\1)'),
            (r'\bcosh\(([^)]+)\)', r'\\cosh(\1)'),
            (r'\btanh\(([^)]+)\)', r'\\tanh(\1)'),
            
            # 对数函数
            (r'\blog\(([^)]+)\)', r'\\log(\1)'),
            (r'\bln\(([^)]+)\)', r'\\ln(\1)'),
            (r'\blog_([^\s]+)\(([^)]+)\)', r'\\log_{\1}(\2)'),
            
            # 根号
            (r'√\(([^)]+)\)', r'\\sqrt{\1}'),
            (r'√([a-zA-Z0-9]+)', r'\\sqrt{\1}'),
            (r'∛\(([^)]+)\)', r'\\sqrt[3]{\1}'),
            
            # 极限
            (r'lim\[([^\]]+)\]', r'\\lim_{\1}'),
            (r'lim_([^\s]+)', r'\\lim_{\1}'),
            
            # 矩阵和向量
            (r'\|([^\|]+)\|', r'\\|\1\\|'),  # 绝对值/模长
            (r'\[([^\]]+)\]', r'\\begin{bmatrix}\1\\end{bmatrix}'),  # 矩阵（简化）
            
            # 微分算子
            (r'∇²', r'∇²'),  # 拉普拉斯算子
            (r'∇·', r'∇\\cdot'),  # 散度
            (r'∇×', r'∇\\times'),  # 旋度
            
            # 集合符号
            (r'∈', r'\\in'),
            (r'∉', r'\\notin'),
            (r'⊂', r'\\subset'),
            (r'⊆', r'\\subseteq'),
            (r'∪', r'\\cup'),
            (r'∩', r'\\cap'),
            (r'∅', r'\\emptyset'),
            
            # 逻辑符号
            (r'∀', r'\\forall'),
            (r'∃', r'\\exists'),
            (r'¬', r'\\neg'),
            (r'∧', r'\\land'),
            (r'∨', r'\\lor'),
            (r'→', r'\\rightarrow'),
            (r'↔', r'\\leftrightarrow'),
            
            # 比较符号
            (r'≤', r'\\leq'),
            (r'≥', r'\\geq'),
            (r'≠', r'\\neq'),
            (r'≈', r'\\approx'),
            (r'≡', r'\\equiv'),
            (r'∝', r'\\propto'),
            
            # 其他常见符号
            (r'±', r'\\pm'),
            (r'∓', r'\\mp'),
            (r'×', r'\\times'),
            (r'÷', r'\\div'),
            (r'∞', r'\\infty'),
            (r'∂', r'\\partial'),
            
            # 清理注释
            (r'\.\.\. ([^\s]+) more lines not shown \.\.\.', r''),
        ]
        
        result = content
        for pattern, replacement in conversions:
            result = re.sub(pattern, replacement, result)
        
        # 后处理：清理多余的转义和格式问题
        post_processing = [
            # 修复多重大括号
            (r'{{{([^}]+)}}}', r'{\1}'),
            # 修复分数中的错误
            (r'\\frac{([^}]+)}{([^}]+)}!', r'\\frac{\1}{\2!}'),
            (r'x^\\frac{([^}]+)}{([^}]+)}', r'\\frac{x^{\1}}{\2}'),
            # 修复偏导数符号
            (r'\\frac{\\partial([^}]+)}{\\partial([^}]+)}', r'\\frac{\\partial \1}{\\partial \2}'),
            (r'∂²\\frac{([^}]+)}{\\partial([^}]+)}²', r'\\frac{\\partial² \1}{\\partial \2²}'),
            # 修复希腊字母前的空格
            (r'\\forall([a-zA-Z])', r'\\forall \1'),
            (r'\\exists([a-zA-Z])', r'\\exists \1'),
            # 修复箭头符号
            (r'([a-zA-Z])\\rightarrow([a-zA-Z0-9∞])', r'\1 \\rightarrow \2'),
            # 修复tan函数
            (r'tan\\frac', r'\\tan \\frac'),
            (r'tan\(\\frac', r'\\tan(\\frac'),
            # 修复极限符号
            (r'\\\\lim_{{{([^}]+)}}}', r'\\lim_{\1}'),
            (r'lim_{([^}]+)}', r'\\lim_{\1}'),
            # 修复导数符号
            (r'`([^`]*d[a-zA-Z]/d[a-zA-Z][^`]*)`', lambda m: f'${self._convert_to_latex(m.group(1))}$'),
            # 修复双曲函数
            (r'sinh\(([^)]+)\)', r'\\sinh(\1)'),
            (r'cosh\(([^)]+)\)', r'\\cosh(\1)'),
            (r'tanh\(([^)]+)\)', r'\\tanh(\1)'),
        ]
        
        for pattern, replacement in post_processing:
            result = re.sub(pattern, replacement, result)
        
        return result
    
    def convert_file(self, input_file, output_file=None):
        """转换Markdown文件中的数学公式"""
        input_path = Path(input_file)
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 读取原文件
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 转换数学公式
        converted_content = self._convert_math_formulas(content)
        
        # 确定输出文件路径
        if output_file is None:
            output_file = input_path.with_suffix('.converted.md')
        else:
            output_file = Path(output_file)
        
        # 写入转换后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(converted_content)
        
        return output_file
    
    def _convert_math_formulas(self, content):
        """转换文本中的数学公式"""
        result = content
        
        # 手动定义的转换规则（基于之前的成功转换）
        manual_conversions = [
            # 傅里叶变换
            (r'`F\(ω\) = ∫\[-∞ to ∞\] f\(t\) e\^\(-iωt\) dt`', 
             r'$F(ω) = ∫_{-∞}^{∞} f(t) e^{-iωt} dt$'),
            (r'`f\(t\) = \(1 / 2π\) ∫\[-∞ to ∞\] F\(ω\) e\^\(iωt\) dω`', 
             r'$f(t) = \\frac{1}{2π} ∫_{-∞}^{∞} F(ω) e^{iωt} dω$'),
            
            # 柯西积分公式
            (r'`f\(a\) = \(1 / 2πi\) ∮\[C\] f\(z\) / \(z - a\) dz`', 
             r'$f(a) = \\frac{1}{2πi} ∮_C \\frac{f(z)}{z - a} dz$'),
            
            # 热传导方程
            (r'`∂u/∂t = α ∂²u/∂x²`', 
             r'$\\frac{∂u}{∂t} = α \\frac{∂²u}{∂x²}$'),
            (r'`∂u/∂t = α ∇²u`', 
             r'$\\frac{∂u}{∂t} = α ∇²u$'),
            
            # 麦克斯韦方程组
            (r'`∇ · E = ρ/ε₀`', r'$∇ \\cdot E = \\frac{ρ}{ε₀}$'),
            (r'`∇ · B = 0`', r'$∇ \\cdot B = 0$'),
            (r'`∇ × E = -∂B/∂t`', r'$∇ \\times E = -\\frac{∂B}{∂t}$'),
            (r'`∇ × B = μ₀J + μ₀ε₀ ∂E/∂t`', r'$∇ \\times B = μ₀J + μ₀ε₀ \\frac{∂E}{∂t}$'),
            
            # 薛定谔方程
            (r'`iℏ ∂ψ/∂t = Ĥψ`', r'$iℏ \\frac{∂ψ}{∂t} = Ĥψ$'),
            (r'`-ℏ²/2m ∇²ψ + Vψ = Eψ`', r'$-\\frac{ℏ²}{2m} ∇²ψ + Vψ = Eψ$'),
            
            # 爱因斯坦场方程
            (r'`Rμν - 1/2 gμν R = 8πG/c⁴ Tμν`', r'$R_{μν} - \\frac{1}{2} g_{μν} R = \\frac{8πG}{c⁴} T_{μν}$'),
            
            # 配分函数
            (r'`Z = ∑ᵢ e^(-Eᵢ/kT)`', r'$Z = \\sum_i e^{-E_i/kT}$'),
            (r'`Z = ∫ e^(-H/kT) dq dp`', r'$Z = ∫ e^{-H/kT} dq dp$'),
            
            # 纳维-斯托克斯方程
            (r'`∂v/∂t + (v·∇)v = -∇p/ρ + ν∇²v + f`', r'$\\frac{∂v}{∂t} + (v \\cdot ∇)v = -\\frac{∇p}{ρ} + ν∇²v + f$'),
            
            # 张量协变导数
            (r'`∇μTᵛ = ∂μTᵛ + ΓᵛμλTᵏ`', r'$∇_μT^ν = ∂_μT^ν + Γ^ν_{μλ}T^λ$'),
            
            # 常见数学函数和表达式
            (r'`([^`]*(?:sin|cos|tan|log|ln|exp|sqrt|lim)\([^)]+\)[^`]*)`', 
             lambda m: f'${self._convert_to_latex(m.group(1))}$'),
            
            # 包含分数的表达式
            (r'`([^`]*\([^)]+\)\s*/\s*\([^)]+\)[^`]*)`', 
             lambda m: f'${self._convert_to_latex(m.group(1))}$'),
            
            # 包含上下标的表达式
            (r'`([^`]*[a-zA-Z][_^][a-zA-Z0-9αβγδεζηθλμνπρστφχψωΓΔΘΛΞΠΣΦΨΩ]+[^`]*)`', 
             lambda m: f'${self._convert_to_latex(m.group(1))}$'),
            
            # 包含积分、求和等的表达式
            (r'`([^`]*[∫∮∑∏][^`]*)`', 
             lambda m: f'${self._convert_to_latex(m.group(1))}$'),
            
            # 包含偏导数的表达式
            (r'`([^`]*∂[^`]*/∂[^`]*)`', 
             lambda m: f'${self._convert_to_latex(m.group(1))}$'),
            
            # 通用数学符号模式（最后匹配）
            (r'`([^`]*[∫∂∇∑∏∞≈≠≤≥±×÷√∛αβγδεζηθλμνπρστφχψωΓΔΘΛΞΠΣΦΨΩ∈∉⊂⊆∪∩∅∀∃¬∧∨→↔≡∝∓][^`]*)`', 
             lambda m: f'${self._convert_to_latex(m.group(1))}$'),
        ]
        
        # 应用手动转换规则
        for pattern, replacement in manual_conversions:
            if callable(replacement):
                result = re.sub(pattern, replacement, result)
            else:
                result = re.sub(pattern, replacement, result)
        
        return result
    
    def convert_to_word(self, markdown_file, word_file=None, use_pandoc=True):
        """将Markdown文件转换为Word文档"""
        markdown_path = Path(markdown_file)
        
        if word_file is None:
            word_file = markdown_path.with_suffix('.docx')
        else:
            word_file = Path(word_file)
        
        if use_pandoc:
            try:
                # 使用pandoc转换
                cmd = [
                    'pandoc',
                    str(markdown_path),
                    '--to=docx',
                    f'--output={word_file}'
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                print(f"成功转换为Word文档: {word_file}")
                return word_file
                
            except subprocess.CalledProcessError as e:
                print(f"Pandoc转换失败: {e}")
                print(f"错误输出: {e.stderr}")
                raise
            except FileNotFoundError:
                print("错误: 未找到pandoc命令。请确保已安装pandoc。")
                print("安装方法: https://pandoc.org/installing.html")
                raise
        else:
            print("警告: 当前仅支持pandoc转换")
            return None
    
    def full_conversion(self, input_file, output_word_file=None):
        """完整转换流程：Markdown数学公式转换 + Word文档生成"""
        print(f"开始转换文件: {input_file}")
        
        # 步骤1: 转换数学公式
        print("步骤1: 转换数学公式格式...")
        converted_md = self.convert_file(input_file)
        print(f"数学公式转换完成: {converted_md}")
        
        # 步骤2: 转换为Word文档
        print("步骤2: 转换为Word文档...")
        word_file = self.convert_to_word(converted_md, output_word_file)
        
        # 清理临时文件（可选）
        # converted_md.unlink()  # 删除中间文件
        
        print(f"转换完成! Word文档保存为: {word_file}")
        return word_file


def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(
        description='Markdown数学公式转换器 - 将反引号数学公式转换为LaTeX格式并生成Word文档',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python md_math_converter.py input.md
  python md_math_converter.py input.md -o output.docx
  python md_math_converter.py input.md --math-only
        """
    )
    
    parser.add_argument('input_file', help='输入的Markdown文件路径')
    parser.add_argument('-o', '--output', help='输出的Word文档路径（可选）')
    parser.add_argument('--math-only', action='store_true', 
                       help='仅转换数学公式，不生成Word文档')
    parser.add_argument('--keep-temp', action='store_true', 
                       help='保留中间转换文件')
    
    args = parser.parse_args()
    
    try:
        converter = MarkdownMathConverter()
        
        if args.math_only:
            # 仅转换数学公式
            output_file = converter.convert_file(args.input_file, args.output)
            print(f"数学公式转换完成: {output_file}")
        else:
            # 完整转换流程
            word_file = converter.full_conversion(args.input_file, args.output)
            print(f"转换成功: {word_file}")
            
    except Exception as e:
        print(f"转换失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()