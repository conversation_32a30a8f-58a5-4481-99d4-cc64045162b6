# 实战说明

## 目的

js基础最重要的是语法和编程思维的培养，所以安排了本次实战

- 笔试题→锻炼基本语法的掌握

- 排错题→锻炼编码过程中常见错误的解决

- 编程题→基于课堂练习和每日作业的常见编程思维的训练

## 安排

阶段：JS基础（5天课）

时间：JS基础day05结束后的次日自习

内容和形式：

1. 笔试题（小组内互相提问，组长检查）  
2. 排错题（个人独立完成，组长检查）  
3. 编程题（个人独立完成，组长检查）

素材：见html、MD文件和gif图

## 说明

1. 按照笔试题→排错题→编程题的顺序逐一完成
2. 各小组长负责检查
3. 讲师或者就业指导老师负责进行实战的说明或者题目理解的答疑
4. 当晚23:00 前各小组长统一收集本小组的编程题的文件交由班长
   1. 一个小组一个压缩包
   2. 压缩包命名：第x组-组长名
   3. 解压后是本小组所有组员的“js基础实战3-编程题-题目-名字.html文件”
5. 班长统一发给当班老师

## 注意

如果小组提前完成，可以预习下次课的内容或者组内复盘总结

## 阶段感言

经过基础5天学习，是不是很有收获呀，同时pink老师准备了很多资料给同学，看在辛苦的份上，没有一键三连的同学赶紧三连，同时记得关注我的抖音，里面经常发布各种资料哦~~~

 ![67332795948](assets/1673327959489.png)

