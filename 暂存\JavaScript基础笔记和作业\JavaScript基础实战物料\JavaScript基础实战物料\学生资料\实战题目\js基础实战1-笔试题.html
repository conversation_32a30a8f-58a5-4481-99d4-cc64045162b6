<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>js基础-笔试题</title>
</head>

<body>
  <script>
      //   1. 声明age变量并赋值为18

      //   2. 声明uname, age, gender 多个变量

      //   3. 定义数组，保存 小黑、小红、小花 三个人的名字

      //   4. 写出for循环遍历数组的基本语法

      //   5. 写出数字自增的做法有哪些

      //   6. 写出if单分支、双分支、多分支语句

      //   7. 请写出三元运算符的语法

      //   8. 请写出for循环的语法

      //   9. 写出出数组的常用方法和对应的作用

      //   10. 写出函数的语法
      //         带有形参和返回值

      //   11. 写出作用域有哪些

      //   12. 写出对象语法

      //   13. 写出遍历对象的语法

      //   14. 写出基本数据类型有哪5种

      //   15. 写出 转换为数值型常见方法

      //   16. 写出模板字符串使用方式

      //   17. 写出布尔类型的值为false 的值哪些

      //   18. 写出  +加号的功能有哪些
  </script>
</body>

</html>