<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>js基础-编程题-答案</title>
  </head>
  <body>
    <script>
      /*
        1. 增加年龄
        描述
         1. 提示用户输入年龄，用户输入年龄后，把用户输入的年龄增加 5 岁
         2. 增加 5 岁后，通过弹出框提示用户 “ 据我估计，五年后，你可能 XX 岁了”
        */
      // let age = +prompt('请输入年龄')
      // alert(`据我估计，五年后，你可能${age + 5}岁了`)

      /*
        2. 用户登录验证
        描述
        接收用户输入的用户名和密码，若用户名为 “admin” ,且密码为 “123456” ,则提示用户登录成功!  否则，让用户一直输入。
        ps：使用while循环
        */
      // let msg = prompt('请输入您的名字')
      // let pwd = prompt('请输入您的密码')
      // while (msg !== 'admin' || pwd !== '123456') {
      //   alert('请你先登录')
      //   msg = prompt('名字')
      //   pwd = prompt('密码')
      // }

      // alert('登录成功')

      /*
        3. 用户输入一个数，  计算 1 到这个数的和。
         比如 用户输入的是 5， 则计算 1到5 之间的累加的和 并且弹出计算结果
        */
      // let num = +prompt('请输入一个数字')

      // let sum = 0
      // for (let i = 1; i <= num; i++) {
      //   sum += i
      // }
      // alert(`1到${num}之间所有数的和是 ${sum}`)

      /*
        4. 用户输入分数，根据分数奖励不同的车( 利用多分支语句 )
         - 90~100分     奖励法拉利
         - 80~90分      奖励奥迪
         - 60~80分      奖励奥拓
         - 60分以下      打一顿
        */

      // let score = +prompt('请输入分数')
      // if (score >= 90) {
      //   document.write(`奖励法拉利`)
      // } else if (score >= 80) {
      //   document.write(`奖励奥迪`)
      // } else if (score >= 60) {
      //   document.write(`奖励奥拓`)
      // } else {
      //   document.write(`打一顿`)
      // }

      /*
        5. 打印输出数组里所有的奇数
         let arrNums = [5, 2, 8, 10, 3, 7]
        */
      // let arrNums = [5, 2, 8, 10, 3, 7]
      // for (let i = 0; i < arrNums.length; i++) {
      //   if (arrNums[i] % 2 !== 0) {
      //     console.log(`奇数有 ${arrNums[i]}`)
      //   }
      // }

      /*
        6. 使用for循环 - 打印输出班级里同学们平均年龄
         let arrAges = [15, 19, 21, 33, 18, 24, 10]
        */
      // let arrAges = [15, 19, 21, 33, 18, 24, 10]
      // let sum = 0
      // for (let i = 0; i < arrAges.length; i++) {
      //   sum += arrAges[i]
      // }
      // console.log(`班级里同学们平均年龄是 ${sum / arrAges.length}`)

      /*
        7. 使用for循环 - 求出数组里大于5的和,  以及能被3整除的偶数的个数
         let arrCount = [4, 9, 5, 3, 20, 6, 15, 11, 35]
        */
      // let arrCount = [4, 9, 5, 3, 20, 6, 15, 11, 35]
      // let sum = 0 // 存累加和
      // let temp = [] // 存能被3整除的偶数结果
      // for (let i = 0; i < arrCount.length; i++) {
      //   if (arrCount[i] > 5) {
      //     sum += arrCount[i]
      //   }

      //   if (arr[i] % 3 === 0) {
      //     temp.push(arrCount[i])
      //   }
      // }
      // console.log(`数组里面大于5的和是 ${sum}`)
      // console.log(`数组里面能够被3整除的偶数的个数是 ${temp.length}`)

      /*
        8. 给一个数字数组，该数组中有很多数字0，将不为0的数据存入到一个新的数组中
         let arrAll = [4, 0, 9, 5, 0, 20, 3, 0, 11, 0 , 0, 10]
        */
      // let arrAll = [4, 0, 9, 5, 0, 20, 3, 0, 11, 0, 0, 10]

      // let arrNorZero = []

      // for (let i = 0; i < arrAll.length; i++) {
      //   if (arrAll[i] !== 0) {
      //     arrNorZero.push(arrAll[i])
      //   }
      // }
      // console.log(arrNorZero) // [4, 9, 5, 20, 3, 11, 10]

      /*
        9. 封装余额函数
         目的: 复习函数的声明与调用
         要求:
         1. 运行程序后, 浏览器显示输入确认框(prompt)
         2. 第一个输入确认框提示输入银行卡余额
         3. 第二个输入确认框提示输入当月食宿消费金额
         4. 第三个输入确认框提示输入当月生活消费金额
         5. 输入完毕后,在页面中显示银行卡剩余金额
         6. 提示: 所有功能代码封装在函数内部（函数需要把余额返回）

           function live() {}
          let money = live()
        */
      // function live() {
      //   let money = prompt('银行卡金额')
      //   let num1 = prompt('当月食宿消费金额?')
      //   let num2 = prompt('当月生活消费金额?')
      //   let res = money - num1 - num2
      //   return res
      // }
      // let money = live()
      // document.write(`我的银行卡余额还有${money}元`)

      /*
        10. 对象的使用
        要求:
        1. 对象名为 computer  (电脑对象)
        2. 有size属性, 值为 "15.6 寸"
        3. 有brand属性，值为 "华为"
        4. 有playGame方法，值为function函数，函数内打印 "吃鸡"
        5. 有code方法，值为function函数，函数内打印 "写代码使我快乐"
        6. 页面中输出电脑对象的size属性的值 和 brand属性的值
        7. 调用对象中的playGame 和 code的方法
        let computer = {}
        */
      // let computer = {
      //   size: '15.6 寸',
      //   brand: '华为',
      //   playGame: function () {
      //     console.log('吃鸡')
      //   },
      //   code: function () {
      //     console.log('写代码使我快乐')
      //   },
      // }

      // document.write(computer.size, computer.brand)
      // computer.playGame()
      // computer.code()

      /*
        11. 计算m-n之间所有数的和
          封装函数，实现功能：
          调用函数，传入用户输入的两个数字，分别表示最小值和最大值
          函数返回最小值到最大值之间的所有数字的和
        */

      // let m = +prompt(`输入最小数字`)
      // let n = +prompt(`输入最大数字`)

      // function fn(min, max) {
      //   let sum = 0
      //   for (let i = min; i <= max; i++) {
      //     sum += i
      //   }
      //   return sum
      // }

      // let res = fn(m, n)
      // alert(`${m}到${n} 之间所有数的和是 ${res}`)
      /*
        12. 猜数字游戏，设定次数，最多猜5次
        要求
        1. 生成随机的数字0到20
        2. 只能猜5次，5次机会用完，提示 “这都猜不到”
        3. 猜对了，提示 “恭喜您猜对啦”
        4. 猜小了，提示 “您猜的数字小了”
        5. 猜大了，提示 “您猜的数字大了”
        */

      // function random(min, max) {
      //   return Math.floor(Math.random() * (max - min + 1)) + min
      // }

      // // 生成一个数字先,猜0-20之间的数
      // let num = random(0, 20)

      // let flag

      // // 最多猜5次
      // for (let i = 1; i <= 5; i++) {
      //   let userNum = +prompt('请输入您要猜的数字')

      //   // 比较数字
      //   if (userNum > num) {
      //     alert('您猜的数字大了')
      //   } else if (userNum < num) {
      //     alert('您猜的数字小了')
      //   } else if (userNum === num) {
      //     flag = true
      //     alert('恭喜您猜对了！')
      //     break
      //   }
      // }

      // if (flag === undefined) {
      //   alert('这都猜不到！O(∩_∩)O')
      // }
      /*          
        13. 小娜
        需求: 只要用户输入的不是相关选项, 则一直弹出输入提示框, 等待用户的输入
        思路:
          - while() 循环
          - 弹出输入框
        */
      //   let num

      //   while (num !== 'q') {
      //     num = prompt(`
      //  			你好我是小娜，请选择功能：
      //  			输入q: 退出
      //  			输入1: 求和
      //  			输入2: 求乘积
      //  `)

      //     if (num == 'q') {
      //       alert('我退出咯, 你不爱我了!')

      //     }

      //     if (num == 1) {
      //       let num1 = +prompt('请输入第一个数字')
      //       let num2 = +prompt('请输入第二个数字')
      //       alert(num1 + num2)
      //     }

      //     if (num == 2) {
      //       let num1 = +prompt('请输入第一个数字')
      //       let num2 = +prompt('请输入第二个数字')
      //       alert(num1 * num2)
      //     }
      //   }

      /*
        14. 实现数组里求最大值的函数
        let arrayNums = [4, 9, 5, 3, 20, 6, 15, 11]
        const res = getMax(arrayNums)
        console.log(`数组最大值是 ${res}`) // 20
        */

      // let arrayNums = [4, 9, 5, 3, 20, 6, 15, 11]
      // function getMax(arrayNums) {
      //   let max = 0
      //   for (let i = 0; i < arrayNums.length; i++) {
      //     if (arrayNums[i] > max) {
      //       max = arrayNums[i]
      //     }
      //   }
      //   return max
      // }

      // let res = getMax(arrayNums)
      // console.log(`数组最大值是 ${res}`)

      /*
        15. 操作数组对象
        需求1: 遍历数组里每个对象, 判断出哪个人最大 - 打印它的名字
        需求2: 遍历数组里每个对象, 为每个人增加5岁的年龄, 最后打印整个数组在控制台查看
        let arrPers = [
        {
          name: '小明',
          age: 18,
          sex: '男',
        },
        {
          name: '老刘',
          age: 38,
          sex: '男',
        },
        {
          name: '小王',
          age: 19,
          sex: '女',
        },
      ]
        */
      let arrPers = [
        {
          name: '小明',
          age: 18,
          sex: '男',
        },
        {
          name: '老刘',
          age: 38,
          sex: '男',
        },
        {
          name: '小王',
          age: 19,
          sex: '女',
        },
      ]

      // 需求1: 遍历数组里每个对象, 判断出哪个人最大 - 打印它的名字
      let maxObj = arrPers[0]
      for (let i = 0; i < arrPers.length; i++) {
        if (maxObj.age < arrPers[i].age) {
          maxObj = arrPers[i]
        }
      }
      console.log(maxObj.name)

      // 需求2: 遍历数组里每个对象, 为每个人增加5岁的年龄, 最后打印整个数组在控制台查看
      for (let i = 0; i < arrPers.length; i++) {
        arrPers[i].age = arrPers[i].age + 5
      }
      console.log(arrPers)
    </script>
  </body>
</html>
