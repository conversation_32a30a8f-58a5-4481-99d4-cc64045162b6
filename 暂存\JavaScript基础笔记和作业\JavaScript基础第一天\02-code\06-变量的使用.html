<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // 1. 声明一个年龄变量
    // let age
    // // 2. 赋值   =  赋值
    // age = 18
    // console.log(age)
    // 3. 声明的同时直接赋值  变量的初始化
    // let age = 18
    // 小案例
    let num = 20
    let uname = 'pink老师'
    console.log(num)
    console.log(uname)
  </script>
</body>

</html>