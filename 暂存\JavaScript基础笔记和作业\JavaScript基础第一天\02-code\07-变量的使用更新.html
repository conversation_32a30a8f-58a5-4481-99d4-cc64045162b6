<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>

    // 1 声明的同时直接赋值  变量的初始化
    // let age = 18
    // age = 19
    // // let age = 19
    // console.log(age)
    // 2. 声明多个变量    
    // let age = 18, uname = '迪丽热巴'
    // console.log(age, uname)
    // 提倡声明的方式
    let age = 19
    let uname = '迪丽热巴'
    console.log(age, uname)
  </script>
</body>

</html>