<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // js 弱数据类型的语言 只有当我们赋值了，才知道是什么数据类型
    // let num = 'pink'
    // let num = 10.11
    // console.log(num)
    console.log(1 + 1)
    console.log(1 * 1)
    console.log(1 / 1)
    console.log(4 % 2) // 求余数
    console.log(5 % 3) // 求余数  2 
    console.log(3 % 5) // 求余数  3 
    // java 强数据类型的语言    int num = 10

    console.log('pink老师' - 2)
    console.log(NaN - 2)
    console.log(NaN + 2)
    console.log(NaN / 2)
    console.log(NaN === NaN)
  </script>
</body>

</html>