<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // let str = 'pink'
    // let str1 = "pink"
    // let str2 = `中文`
    // console.log(str2)
    // console.log(11)
    // console.log(`11`)
    // console.log(str)
    // console.log('str')
    // console.log('pink老师讲课非常有"基情"')
    // console.log("pink老师讲课非常有'基情'")
    // console.log('pink老师讲课非常有\'基情\'')
    // 字符串拼接
    // console.log(1 + 1)
    // console.log('pink' + '老师')

    let age = 25

    // document.write('我今年' + 19)
    // document.write('我今年' + age)
    // document.write('我今年' + age + '岁了')
    document.write('我今年' + age + '岁了')


  </script>
</body>

</html>