<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // 1. true  false 是布尔型字面量  
    // console.log(3 > 4)
    // let isCool = false
    // console.log(isCool)
    // 2. 未定义类型   弱数据类型   声明一个变量未赋值就是 undefined
    // let num
    // console.log(num)
    // 3. null 空的
    // let obj = null
    // console.log(obj)
    // 计算有区别
    console.log(undefined + 1)  // NaN
    console.log(null + 1) // 1
  </script>
</body>

</html>