<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <style>
    .box {
      width: 50px;
      height: 50px;
    }
  </style>
</head>

<body>
  <script>
    let str = '123'
    console.log(Number(str))
    console.log(Number('pink'))
    // let num = Number(prompt('输入年薪'))
    // let num = +prompt('输入年薪')
    // console.log(Number(num))
    // console.log(num)
    console.log(parseInt('12px'))
    console.log(parseInt('12.34px'))
    console.log(parseInt('12.94px'))
    console.log(parseInt('abc12.94px'))

    // -------------------
    console.log(parseFloat('12px')) // 12
    console.log(parseFloat('12.34px')) // 12.34
    console.log(parseFloat('12.94px')) // 12.94
    console.log(parseFloat('abc12.94px')) // 12.94
  </script>
</body>

</html>