#  JavaScript基础第三天作业

## 主观题

### 练习题1：

写一个程序，要求如下（★★） 

* 需求1：让用户输入五个有效年龄（0-100之间），**放入数组中**

  * 必须输入五个有效年龄年龄，如果是无效年龄，则不能放入数组中
* 需求2：打印出所有成年人的年龄 (数组筛选)
* 需求3：打印出所有人总年龄 （累加）
* 需求4：打印出所有人的平均年龄 （累加）
* 需求5：打印出最大年龄和最小年龄 （最大值）


### 练习题2：

找出数组中 元素为10的下标，有则打印该下标，没有则打印-1

* 例如: [88,20,10,100,50]  打印 2
* 例如: [88,20,30,100,50]  打印-1

 ### 练习题3: 

使用for循环 - 求出数组元素的和 [5, 8, 9, 2, 1, 5]
     

### 练习题4: 

使用for循环 - 求出数组里大于5的i和 [4, 9, 5, 20, 3, 11]



### 练习题5: 

使用for循环 - 求出班级里同学们平均年龄[15, 19, 21, 33, 18, 24]

### 练习题6: 

计算[2, 6, 18, 15, 40] 中能被3整除的偶数的和

### 练习题7：

计算[2, 6, 18, 15, 40] 中能被3整除的偶数的个数

### 练习题8：

给一个数字数组，该数组中有很多数字0，将不为0的数据存入到一个新的数组中

###  核心练习题

需求：

根据用户输入的个数，页面可以渲染对应王者荣耀永雄的个数

效果如下：

 <img src="assets/222.gif">

思路分析：

1. 渲染图片比较多，我们可以把图片地址放入数组中，
2. 图片名称是有序号排列的，比如1.webp  2.webp 此处可以使用循环方式重复渲染图片
3. 渲染位置？ 可以考虑放到 box盒子里写script 即可

## 排错题

### 拍错题1

~~~html
<body>
  <!-- 请问以下代码会出现什么问题，如何解决？ -->
  <script>
    // 需求： 求 1~100之间的累加和
    // 注意： 此处有3个错误，找出并且修正
    let sum
    for (let i = 1; i < 100; i++;) {
      sum += i
    }
    console.log(sum)
  </script>
</body>
~~~

### 排错题2

~~~html
<!-- bug:请你找到下面代码的bug,把数字1打印出来 -->
<body>
  <script>
    let sum = 0
    let arr = [1, 2, 3, 4, 5]
    for (let i = 1; i < arr.length; i++) {
      console.log(arr[i])
    }
  </script>
</body>
~~~

### 排错题3

~~~html
<!-- bug:找到下面代码死循环的原因,并修改为正确的代码 -->

<body>
  <script>
    for (let i = 1; i <= 5; i++) {
      for (let j = 1; j <= 5; i++) {
        console.log(`这是双重for循环`);
      }
    }
  </script>
</body>
~~~



## 客观题

请扫码做客观题

PC端地址：https://ks.wjx.top/vm/eeLxrmN.aspx# 

二维码：

 ![67332530121](assets/1673325301211.png)

关于pink老师抖音（黑马pink讲前端），领取学习路线图、面试宝典以及八大学科的基础视频哦~~

 ![67332537110](assets/1673325371109.png)



