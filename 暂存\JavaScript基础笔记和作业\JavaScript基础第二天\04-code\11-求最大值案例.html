<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // 1. 用户输入
    let num1 = +prompt('请您输入第一个数：')
    let num2 = +prompt('请您输入第二个数：')
    // 2. 判断输出-三元运算符
    // if (num1 > num2) {
    //   alert(num1)
    // } else {
    //   alert(num2)
    // }
    num1 > num2 ? alert(`最大值是: ${num1}`) : alert(`最大值是: ${num2}`)
  </script>
</body>

</html>