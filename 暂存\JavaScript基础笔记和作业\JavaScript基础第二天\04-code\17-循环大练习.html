<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // let age = 18
    // age = age + 1
    // age += 1
    // 1. 页面输出1~100
    // let i = 1
    // while (i <= 100) {
    //   document.write(`这是第${i}个数<br>`)
    //   i++
    // }
    // 2. 页面输出1~100 累加和
    // let i = 1  // 变量的起始值
    // let sum = 0  // 累加和变量
    // while (i <= 100) {
    //   // 把i 累加到 sum 里面
    //   // sum = sum + i
    //   sum += i
    //   i++
    // }
    // console.log(sum) // 5050
    // 3. 页面输出1~100 偶数和
    let i = 1
    let sum = 0
    while (i <= 100) {
      // 筛选偶数 只有偶数才累加
      if (i % 2 === 0) {
        sum = sum + i
      }
      // 每次循环都要自加
      i++
    }
    console.log(sum)
  </script>
</body>

</html>