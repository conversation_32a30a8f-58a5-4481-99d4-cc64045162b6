<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // 1.声明对象
    // let pink = {
    //   uname: 'pink老师',
    //   age: 18,
    //   gender: '女'
    // }
    // // console.log(pink)
    // // console.log(typeof pink)
    // // 改 把性别的女改为男
    // pink.gender = '男'
    // console.log(pink)
    // // 增
    // pink.hobby = '足球'
    // console.log(pink)
    // // 删 (了解)
    // delete pink.age
    // console.log(pink)
    // // let num = 10
    // num = 20
    // console.log(num)
    // 1. 声明
    // console.log(window.name)
    let obj = {
      'goods-name': '小米10青春版',
      num: '100012816024',
      weight: '0.55kg',
      address: '中国大陆'
    }
    obj.name = '小米10 PLUS'
    obj.color = '粉色'
    // console.log(obj.name)
    console.log(obj.num)
    console.log(obj.weight)
    console.log(obj.address)
    console.log(obj.color)
    // console.log(obj.goods - name)
    // 查的另外一种属性：
    // 对象名['属性名']
    console.log(obj['goods-name'])


    // 查总结：
    // (1) 对象名.属性名  obj.age
    console.log(obj.num)
    // (2) 对象名['属性名']  obj['age']
    console.log(obj['num'])





    // // console.log(address)
    // // 2. 使用属性 查  对象名.属性名
    // console.log(obj.address)
    // console.log(obj.name)


  </script>
</body>

</html>