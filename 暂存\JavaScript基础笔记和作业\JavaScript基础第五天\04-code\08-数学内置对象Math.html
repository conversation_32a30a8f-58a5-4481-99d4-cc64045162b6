<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // 属性
    console.log(Math.PI)
    // 方法
    // ceil 天花板  向上取整
    console.log(Math.ceil(1.1)) // 2 
    console.log(Math.ceil(1.5)) // 2 
    console.log(Math.ceil(1.9)) // 2 
    // floor 地板  向下取整
    console.log(Math.floor(1.1))  // 1
    console.log(Math.floor(1.5))  // 1
    console.log(Math.floor(1.9))  // 1
    console.log(Math.floor('12px'))  // 1
    console.log('----------------')
    // 四舍五入 round
    console.log(Math.round(1.1))  // 1
    console.log(Math.round(1.49))  // 1
    console.log(Math.round(1.5))  // 2
    console.log(Math.round(1.9))  // 2
    console.log(Math.round(-1.1))  // -1 
    console.log(Math.round(-1.5))  // -1
    console.log(Math.round(-1.51))  // -2

    // 取整函数 parseInt(1.2)   // 1
    // 取整函数 parseInt('12px')   // 12

    console.log(Math.max(1, 2, 3, 4, 5))
    console.log(Math.min(1, 2, 3, 4, 5))
    console.log(Math.abs(-1));

    // null  类似 let obj = {}
    let obj = null 
  </script>
</body>

</html>