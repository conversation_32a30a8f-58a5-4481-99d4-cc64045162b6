<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>渲染英雄列表案例</title>
	<link rel="stylesheet" href="css/hero.css">
</head>

<body>
	<!-- 利用对象数组渲染英雄列表案例 -->
	<!-- <ul class="list">
		
	</ul> -->
	<script>
		const datas = [
			{ name: '司马懿', imgSrc: '01.jpg' },
			{ name: '女娲', imgSrc: '02.jpg' },
			{ name: '百里守约', imgSrc: '03.jpg' },
			{ name: '亚瑟', imgSrc: '04.jpg' },
			{ name: '虞姬', imgSrc: '05.jpg' },
			{ name: '张良', imgSrc: '06.jpg' },
			{ name: '安其拉', imgSrc: '07.jpg' },
			{ name: '李白', imgSrc: '08.jpg' },
			{ name: '阿珂', imgSrc: '09.jpg' },
			{ name: '墨子', imgSrc: '10.jpg' },
			{ name: '鲁班', imgSrc: '11.jpg' },
			{ name: '嬴政', imgSrc: '12.jpg' },
			{ name: '孙膑', imgSrc: '13.jpg' },
			{ name: '周瑜', imgSrc: '14.jpg' },
			{ name: 'XXX', imgSrc: '15.jpg' },
			{ name: 'XXX', imgSrc: '16.jpg' },
			{ name: 'XXX', imgSrc: '17.jpg' },
			{ name: 'XXX', imgSrc: '18.jpg' },
			{ name: 'XXX', imgSrc: '19.jpg' },
			{ name: 'XXX', imgSrc: '20.jpg' }
		]

		let str = ''
		for (let i = 0; i < datas.length; i++) {
			str += `
				<li><img src="./uploads/heros/${datas[i].imgSrc}" title="${datas[i].name}"></li>
			`
		}
		document.write(`
		<ul class="list">
			${str}
		</ul>
		`)
	</script>
</body>

</html>