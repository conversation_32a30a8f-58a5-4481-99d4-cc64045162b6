## 客观题

PC端地址：https://ks.wjx.top/vm/hgfFlVd.aspx# 

二维码扫码：

 ![67332678863](assets/1673326788639.png)

## 主观题

### 练习题1：

点名: 每次刷新网页运行, 在控制台 随机输出一位同学的名字 ["老赵", "老李", "小传", "小黑"]，如果输出了，则数组中删除这个名字

### 练习题2：

声明对象

目的: 复习对象的声明

要求:

1. 声明一个变量per, 类型为对象类型
2. 该对象的属性为性别,年龄,爱好(3个)
3. 该对象的方法有 说话, 吃饭(2个)
4. 在控制台分别调用该对象的属性和方法

### 练习题3：

调用对象的方法

目的: 复习对象的使用

要求:

1. 对象声明完毕后, 调用对象中的吃饭的方法
2. 提示: 对象中的方法本质是函数, 调用需要加()
3. 方法也可以传递参数的



### 练习题4：

猜数字游戏，设定次数，最多猜8次



### 练习题5：

完成课堂随机生成颜色的案例。

### 拓展作业1

**需求：** 利用对象数组渲染英雄列表案例

**展示效果：**如下：

![67332737198](assets/1673327371980.png)

功能1：

1. 利用对象数组里面的数据来渲染页面，渲染多个数据
2. 鼠标经过停留会显示`英雄名字`

数据：

~~~javascript
let datas = [
  { name: '司马懿', imgSrc: '01.jpg' },
  { name: '女娲', imgSrc: '02.jpg' },
  { name: '百里守约', imgSrc: '03.jpg' },
  { name: '亚瑟', imgSrc: '04.jpg' },
  { name: '虞姬', imgSrc: '05.jpg' },
  { name: '张良', imgSrc: '06.jpg' },
  { name: '安其拉', imgSrc: '07.jpg' },
  { name: '李白', imgSrc: '08.jpg' },
  { name: '阿珂', imgSrc: '09.jpg' },
  { name: '墨子', imgSrc: '10.jpg' },
  { name: '鲁班', imgSrc: '11.jpg' },
  { name: '嬴政', imgSrc: '12.jpg' },
  { name: '孙膑', imgSrc: '13.jpg' },
  { name: '周瑜', imgSrc: '14.jpg' },
  { name: 'XXX', imgSrc: '15.jpg' },
  { name: 'XXX', imgSrc: '16.jpg' },
  { name: 'XXX', imgSrc: '17.jpg' },
  { name: 'XXX', imgSrc: '18.jpg' },
  { name: 'XXX', imgSrc: '19.jpg' },
  { name: 'XXX', imgSrc: '20.jpg' }
]
~~~



### 拓展作业2

需求： 根据数据完成表格渲染

效果如下：

![67332719365](assets/1673327193659.png)

功能需求：

1. 表格行要求 编号、科目、成绩、和 删除链接
2. 最后计算出总分 和 平均分

数据如下：

~~~javascript
let data = [
  { subject: '语文', score: 46 },
  { subject: '数学', score: 80 },
  { subject: '英语', score: 100 },
]
~~~



## 排错题

### 排错题1

~~~html
<!-- bug:请你找到下面代码的2处BUG进行修改 -->

<body>
  <script>
    let obj = {
      name: '张三',
      age: 20,
      sex: '男',
      address: '中国人'
    }
    // 获取姓名
    console.log(obj.['name']);

    // 获取地址
    console.log(obj.addres);
  </script>
</body>
~~~

### 排错题2

~~~html
<!-- 请你找到下面代码的3处bug进行修改 -->

<body>
  <script>
    let obj = {
      name: '张三',
      age: 20,
      sex: '男',
      address: '中国人',
      sing: function () {
        console.log('我会唱歌')
      }
      sum: function (x, y) {
       return x + y
      }
    }

    console.log(obj.sing)
    console.log(obj.sum)
  </script>
</body>
~~~

## 关注pink老师

经过基础5天学习，是不是很有收获呀，同时pink老师准备了很多资料给同学，看在辛苦的份上，没有一键三连的同学赶紧三连，同时记得关注我的抖音，里面经常发布各种资料哦~~~

 ![67332802349](assets/1673328023493.png)

































