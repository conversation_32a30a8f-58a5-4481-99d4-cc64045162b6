<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        table {
            width: 600px;
            text-align: center;
        }

        table,
        th,
        td {
            border: 1px solid #ccc;
            border-collapse: collapse;
        }

        caption {
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: 700;
        }

        tr {
            height: 40px;
            cursor: pointer;
        }

        table tr:nth-child(1) {
            background-color: #ddd;
        }

        table tr:not(:first-child):hover {
            background-color: #eee;
        }
    </style>
</head>

<body>
    <h2>学生信息</h2>
    <p>将数据渲染到页面中...</p>

    <table>
        <caption>学生列表</caption>
        <tr>
            <th>序号</th>
            <th>姓名</th>
            <th>年龄</th>
            <th>性别</th>
            <th>家乡</th>
        </tr>
        <tr>
            <td>1</td>
            <td>小明</td>
            <td>18</td>
            <td>男</td>
            <td>河北省</td>
        </tr>
        <tr>
            <td>1</td>
            <td>小明</td>
            <td>18</td>
            <td>男</td>
            <td>河北省</td>
        </tr>
        <tr>
            <td>1</td>
            <td>小明</td>
            <td>18</td>
            <td>男</td>
            <td>河北省</td>
        </tr>
        <tr>
            <td>1</td>
            <td>小明</td>
            <td>18</td>
            <td>男</td>
            <td>河北省</td>
        </tr>
        <tr>
            <td>1</td>
            <td>小明</td>
            <td>18</td>
            <td>男</td>
            <td>河北省</td>
        </tr>
        <tr>
            <td>1</td>
            <td>小明</td>
            <td>18</td>
            <td>男</td>
            <td>河北省</td>
        </tr>
    </table>

</body>

</html>