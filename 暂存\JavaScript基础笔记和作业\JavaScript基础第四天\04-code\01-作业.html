<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // 找出数组中 元素为10的下标，有则打印该下标，没有则打印 -1
    // let arr = [88, 20, 10, 100, 50]

    // let result = -1
    // for (let i = 0; i < arr.length; i++) {
    //   if (arr[i] === 10) {
    //     result = i
    //   }
    // }
    // 需求1：让用户输入五个有效年龄（0 - 100之间），放入数组中

    //   - 必须输入五个有效年龄年龄，如果是无效年龄，则不能放入数组中
    // arr.length  动态的
    let arr = []
    while (arr.length < 5) {
      let num = +prompt(`请输入第${arr.length + 1}个数据`)
      arr.push(num)
    }  
  </script>
</body>

</html>