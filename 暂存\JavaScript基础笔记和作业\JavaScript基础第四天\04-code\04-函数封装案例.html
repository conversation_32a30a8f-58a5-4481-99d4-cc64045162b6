<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>

  <script>
    // 1. 求2个数的和
    // function getSum() {
    //   let num1 = +prompt('请输入第一个数')
    //   let num2 = +prompt('请输入第二个数')
    //   console.log(num1 + num2)
    // }
    // getSum()
    // 2. 求 1~100 累加和
    function getSum() {
      let sum = 0
      for (let i = 1; i <= 100; i++) {
        sum += i
      }
      console.log(sum)
    }
    getSum()
  </script>
</body>

</html>