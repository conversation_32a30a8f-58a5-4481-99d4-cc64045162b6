<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>

  <script>

    // 2. 求 1~100 累加和
    // function getSum(end) {   // end = 50
    //   // console.log(end)
    //   let sum = 0
    //   for (let i = 1; i <= end; i++) {
    //     sum += i
    //   }
    //   console.log(sum)
    // }
    // getSum(50)  // 1~50
    // getSum(100)  // 1~100

    function getSum(start, end) {   // end = 50
      // 形参  形式上的参数  
      // console.log(end)
      let sum = 0
      for (let i = start; i <= end; i++) {
        sum += i
      }
      console.log(sum)
    }
    getSum(1, 50)  // 调用的小括号里面 实参 - 实际的参数
    getSum(100, 200)  // 实参 - 实际的参数
  </script>
</body>

</html>