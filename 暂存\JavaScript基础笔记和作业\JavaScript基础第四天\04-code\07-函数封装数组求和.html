<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // 1. 封装函数
    // 给一个参数的默认值
    function getArrSum(arr = []) {
      // console.log(arr)
      let sum = 0
      for (let i = 0; i < arr.length; i++) {
        sum += arr[i]
      }
      console.log(sum)
    }
    getArrSum([1, 2, 3, 4, 5])
    getArrSum([11, 22, 33])
    getArrSum()  // 0
  </script>
</body>

</html>