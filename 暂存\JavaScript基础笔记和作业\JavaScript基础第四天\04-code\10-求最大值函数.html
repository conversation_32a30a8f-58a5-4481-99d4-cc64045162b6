<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>

</head>

<body>
  <div></div>
  <script>
    // 1. 求最大值函数
    // function getMax(x, y) {
    //   return x > y ? x : y
    // }
    // let max = getMax(11, 234)
    // console.log(max)

    // //  2. 求任意数组的最大值，并且返回
    // function getArrValue(arr = []) {
    //   // (1)先准备一个max变量存放数组的第一个值
    //   let max = arr[0]
    //   // (2) 遍历比较
    //   for (let i = 1; i < arr.length; i++) {
    //     if (max < arr[i]) {
    //       max = arr[i]
    //     }
    //   }
    //   // (3) 返回值
    //   return max
    // }

    // // let max = getArrValue([1, 3, 5, 7, 9])
    // // let num = prompt('请输入')
    // let max = getArrValue([11, 3, 55, 7, 29])
    // console.log(max)
    //  3. 求任意数组的最大值和最小值，并且返回
    function getArrValue(arr = []) {
      // (1)先准备一个max变量存放数组的第一个值
      let max = arr[0]
      let min = arr[0]  // 最小值
      // (2) 遍历比较
      for (let i = 1; i < arr.length; i++) {
        // 最大值
        if (max < arr[i]) {
          max = arr[i]
        }
        // 最小值
        if (min > arr[i]) {
          min = arr[i]
        }
      }
      // (3) 返回值  返回的是数组
      return [max, min]
      // return min
    }
    let newArr = getArrValue([11, 3, 55, 7, 29])
    console.log(`数组的最大值是: ${newArr[0]}`)
    console.log(`数组的最小值是: ${newArr[1]}`)
  </script>
</body>

</html>