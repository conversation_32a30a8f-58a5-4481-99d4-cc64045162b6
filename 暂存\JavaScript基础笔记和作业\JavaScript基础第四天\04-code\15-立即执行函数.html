<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>

<body>
  <script>
    // let num = 10
    // let num = 20
    // (function () {
    //   console.log(11)
    // })()

    // (function () {
    //   let num = 10
    // })();
    // (function () {
    //   let num = 20
    // })();
    // 1. 第一种写法
    (function (x, y) {
      console.log(x + y)
      let num = 10
      let arr = []
    })(1, 2);
    // (function(){})();
    // 2.第二种写法
    // (function () { }());
    (function (x, y) {
      let arr = []
      console.log(x + y)
    }(1, 3));


    // (function(){})()
    // (function(){}())
  </script>
</body>

</html>