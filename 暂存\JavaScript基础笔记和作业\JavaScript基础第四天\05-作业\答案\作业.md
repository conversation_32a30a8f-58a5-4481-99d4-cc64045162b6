# JavaScript基础第四天作业

## 客观题

PC端地址：https://ks.wjx.top/vm/mC2UvoL.aspx# 

二维码扫码：

 ![67332554085](assets/1673325540858.png)

## 主观题

### 练习题1：

请看以下代码，并说出执行的流程~~~

```javascript
function printfInput(content) { 
    // 将用户输入的内容, 在页面中显示
    document.write(content)
}
let constr = prompt('请输入内容')
printfInput(constr)
```

### 练习题2：

**目标：**求和函数封装练习

**要求:**

1. 封装函数, 名字为sum
2. 功能: 根据传入的两个数,求和并且返回求和的结果（函数必须有return返回值）

```javascript
function sum(x, y) {
  let res = x + y
  return res
  // return x + y
}
let te = sum(1, 2)
document.write(`两者的和是${te}`)

```

### 练习题3：

**目的:**  封装函数, 复习函数的基本写法。

**需求：**实现两个数的值交换(函数版本)  

**分析:**

1. 函数名为 changeNum()
2. 调用函数时,  `changeNum(1，2)`
3. 经过函数内部处理后,输出  `第一个值的结果是2  第二个值的结果是1`
4. 可以多调用两次

```javascript
function changeNum(x,y) { 
   
     let temp = x
     x = y 
     y = temp 
    document.write(`第一个值的结果是${x} <br> 第二个值的结果是${y}`)
}
changeNum(3,4)
changeNum('pink', 'red')
```

### 练习题4：

**目的:** 复习函数的声明与调用

**题目：**封装余额函数

**要求:**

1. 运行程序后, 浏览器显示输入确认框(prompt)
2. 第一个输入确认框提示输入银行卡余额
3. 第二个输入确认框提示输入当月食宿消费金额
4. 第三个输入确认框提示输入当月生活消费金额
5. 输入完毕后,在页面中显示银行卡剩余金额
6. 提示: 所有功能代码封装在函数内部（函数需要把余额返回）

~~~javascript
function result() {
  let a = prompt('银行卡金额')
  let b = prompt('当月食宿消费金额?')
  let c = prompt('当月生活消费金额?')
  let d = a - b - c
  return d
}
let money = result() 
document.write(`我的银行卡余额还有${money}元`)

~~~

### 练习题5:

**目标：** 封装一个函数,可以求任意数组的和 或 平均值

**要求：**

- 函数可以传递2个参数，比如  handleData(arr, true)      `handleData 处理数据的意思`
  * 参数一： 接受实参传递过来的数组
  * 参数二:    布尔类型  如果是true或者不传递参数 是求和操作，   如果传递过来的参数是 false 则是求平均值

~~~javascript
function handleData(arr, flag = true) {
      let sum = 0
      for (let i = 0; i < arr.length; i++) {
        sum += arr[i]
      }
      if (flag) {
        return sum
      } else {
        return sum / arr.length
      }
    }
// 打印输出查看结果
console.log(handleData([1, 2, 3]))  // 6
console.log(handleData([1, 2, 3], true))  // 6
console.log(handleData([1, 2, 3], false))  // 2
~~~

### 拓展题1

需求：  封装 some 函数查看数组是否存在某个元素  。

- 要传递2个参数 元素、数组
- 如果数组存在元素则返回true，如果没有存在元素就返回 false

例如检测 香蕉  是否存在于  数组['苹果', '香蕉', '橘子', '荔枝', '梨子']中， 返回结果是 true

格式如下：

~~~javascript
 function some(ele, arr = []) {
   // console.log(ele, arr)
   // 1. 声明一个flag变量，里面先存储 false
   let flag = false
   // 2. 遍历循环如果找到了则 修改 flag 里面的值为true， 同时中断循环 break
   for (let i = 0; i < arr.length; i++) {
     if (ele === arr[i]) {
       flag = true
       break
     }
   }
   // 3. 返回这个 falg变量
   return flag
 }
let re = some('荔枝', ['苹果', '香蕉', '橘子', '荔枝', '梨子'])
console.log(re) // true
let re1 = some('榴莲', ['苹果', '香蕉', '橘子', '荔枝', '梨子'])
console.log(re1) // false
~~~

### 拓展题2 

需求：  封装 findeIndex 函数返回查找元素在数组中的索引号。

- 要传递2个参数 元素、数组
- 如果找到，则返回查找元素在数组中的索引号，如果查找不到，则返回 -1

例如检测 香蕉    数组['苹果', '香蕉', '橘子', '荔枝', '梨子']中， 返回结果是  1

格式如下：

~~~html
 <script>
    // 封装函数返回元素的下标  [1, 5, 10, 22, 8, 7]
    // 1. 封装函数 findIndex，传递2个参数 元素、数组
    function findIndex(ele, arr = []) {
     	// 2. 声明一个index变量初始值为 -1
      let index = -1
      // 3. 遍历循环如果找到则修改index为当前的索引号，中断循环
      for (let i = 0; i < arr.length; i++) {
        if (ele === arr[i]) {
          index = i
          break
        }
      }
      // 4. 返回index变量
      return index
    }
    let index1 = findIndex(10, [1, 5, 10, 22, 8, 7])
    console.log(index1) // 2
    let index2 = findIndex(8, [1, 5, 10, 22, 8, 7])
    console.log(index2) // 4
    let index3 = findIndex(88, [1, 5, 10, 22, 8, 7])
    console.log(index3) // -1
  </script>
~~~

## 排错题

### 排错题1

~~~html
<!-- bug:请你找到代码返回NaN的原因,并进行修改 -->
<body>
  <script>
    // 请返回一个数字型的结果 可以使用默认参数或者逻辑中断都可以
    function fn(x, y) {  // 错误 因为y没有参数传递过来所以是undefined 相加结果是NaN 可以给y设置默认参数
      console.log(x + y)
    }
    fn(1)  
  </script>
</body>
~~~

### 排错题2

~~~html
<!-- bug:请你找到下面代码的2处错误,并进行修改过来-->
<body>
  <script>
    // 任意数组求和案例
    function getsumArr(arr) {
      let sum = 0
      for (let i = 0; i < arr.legnth; i++) {  // 错误1： length 写错了这是很多同学容易犯错误的地方
        sum + arr[i]  // 错误2： 是 sum += arr[i]  很多同学不小心把=给丢了
      }
      return sum
    }
    console.log(getsumArr([10, 20, 30, 40]))
  </script>
</body>
~~~

## 关注pink老师

 ![67332799972](assets/1673327999728.png)













23