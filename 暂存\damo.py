import requests
import numpy as np


class DeepSeekEmbeddingClient:
    def __init__(self, api_key, model="deepseek-embed-v1"):
        """
        初始化嵌入客户端

        参数:
            api_key: DeepSeek API 密钥
            model: 使用的嵌入模型 (默认: deepseek-embed-v1)
        """
        self.api_key = api_key
        self.model = model
        self.api_endpoint = "https://api.deepseek.com"  # 假设的API端点

    def get_embeddings(self, texts):
        """
        获取文本的嵌入向量

        参数:
            texts: 字符串列表，需要转换的文本

        返回:
            包含嵌入向量的 NumPy 数组，形状为 (文本数量, 嵌入维度)
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.model,
            "input": texts,
            "encoding_format": "float"  # 获取浮点数格式的向量
        }

        try:
            response = requests.post(self.api_endpoint, headers=headers, json=payload)
            response.raise_for_status()  # 检查HTTP错误

            data = response.json()

            # 提取嵌入向量并转换为NumPy数组
            embeddings = [np.array(item["embedding"]) for item in data["data"]]
            return np.array(embeddings)

        except requests.exceptions.RequestException as e:
            print(f"API请求失败: {e}")
            return None
        except (KeyError, ValueError) as e:
            print(f"响应解析错误: {e}")
            return None


# 使用示例
if __name__ == "__main__":
    # 替换为你的实际 API 密钥
    API_KEY = "sk-89c630d2327c453fb9d9770fdcd73d62"

    # 创建客户端实例
    client = DeepSeekEmbeddingClient(api_key=API_KEY)

    # 需要转换的文本
    texts = [
        "自然语言处理是人工智能的重要领域",
        "DeepSeek提供了先进的文本嵌入模型",
        "使用API可以方便地将文本转换为向量"
    ]

    # 获取嵌入向量
    embeddings = client.get_embeddings(texts)

    if embeddings is not None:
        print(f"成功获取 {len(embeddings)} 个嵌入向量")
        print(f"每个向量的维度: {embeddings.shape[1]}")
        print("\n第一个文本的嵌入向量示例:")
        print(embeddings[0][:10])  # 显示前10个维度

        # 计算文本相似度
        similarity = np.dot(embeddings[0], embeddings[1]) / (
                np.linalg.norm(embeddings[0]) * np.linalg.norm(embeddings[1]))
        print(f"\n前两个文本的余弦相似度: {similarity:.4f}")