import win32com.client
import os
import sys

# 定义要注入的 VBA 宏代码
VBA_MACRO_CODE = """
Sub AutoRunMacro()
    Dim msg As String
    msg = "Hello from automated injected VBA macro! " & vbCrLf & _
          "This macro was injected by Python into a specific file."
    MsgBox msg, vbInformation, "Automated Macro"
End Sub
"""


def inject_macro_into_word_file(target_filepath, macro_name):
    """
    向指定的 Word 文件注入 VBA 宏，然后运行它。
    如果文件不存在，则创建一个新的启用宏的文档并注入。
    如果文件已存在，则打开并注入宏，并根据情况保存为 .docm 或 .dotm。

    Args:
        target_filepath (str): 要注入宏的 Word 文件路径 (可以是 .docx, .dotx, .dotm, .docm)。
        macro_name (str): 要注入并运行的宏的名称（必须与 VBA_MACRO_CODE 中定义的一致）。
    """
    word = None
    doc = None
    try:
        # --- 1. 启动 Word 应用程序 ---
        print("正在启动 Word 应用程序...")
        word = win32com.client.Dispatch("Word.Application")
        word.Visible = True  # 设置为 True 以便观察过程

        # --- 2. 打开指定的 Word 文件 或 创建新文件 ---
        abs_target_filepath = os.path.abspath(target_filepath)

        if os.path.exists(abs_target_filepath):
            print(f"正在打开指定文件: '{target_filepath}'...")
            # 打开文件。Word 会自动加载其 VBProject
            doc = word.Documents.Open(abs_target_filepath)
        else:
            print(f"文件 '{target_filepath}' 不存在，将创建一个新的 Word 文档并注入宏...")
            doc = word.Documents.Add()
            # 新创建的文件，我们总是需要确保它被保存为启用宏的格式
            # 稍后在保存时会处理

        # --- 3. 注入 VBA 宏到文档的 VBProject ---
        print(f"正在将 VBA 宏注入到 '{target_filepath}' 的 VBProject...")

        vb_project = doc.VBProject
        module_name = "MyInjectedMacroModule"

        try:
            # 尝试删除已存在的同名模块，以确保注入的是最新代码
            vb_project.VBComponents.Remove(vb_project.VBComponents(module_name))
            print(f"已删除旧的模块: '{module_name}'.")
        except Exception:
            # 如果模块不存在，此处的错误是正常的，可以忽略
            pass

        # 添加一个新的 VBA 模块
        new_module = vb_project.VBComponents.Add(1)  # 1 for vbext_ct_StdModule
        new_module.Name = module_name

        # 将宏代码写入新模块
        new_module.CodeModule.AddFromString(VBA_MACRO_CODE)
        print(f"宏代码已成功注入到模块 '{module_name}'.")

        # --- 4. 保存修改后的 Word 文件 ---
        # 确定最终保存的格式和路径

        final_filepath = ""  # 初始化最终文件路径
        file_format = None
        # 获取原始文件的扩展名，用于判断最终保存格式
        original_ext = os.path.splitext(target_filepath)[1].lower()

        if original_ext == '.docx':
            # 如果原始是 .docx，我们希望生成 .docm
            final_filepath = os.path.splitext(abs_target_filepath)[0] + '.docm'
            file_format = wdFormatXMLMacroEnabledDocument  # 16
            print(f"原文件是 .docx，将保存为启用宏的文档: '{final_filepath}'")
        elif original_ext == '.dotx':
            # 如果原始是 .dotx，我们希望生成 .dotm
            final_filepath = os.path.splitext(abs_target_filepath)[0] + '.dotm'
            file_format = wdFormatXMLTemplateMacroEnabled  # 17
            print(f"原文件是 .dotx，将保存为启用宏的模板: '{final_filepath}'")
        elif original_ext == '.dotm':
            # 如果原始已经是 .dotm，则保持为 .dotm
            final_filepath = abs_target_filepath  # 保持原路径
            file_format = wdFormatXMLTemplateMacroEnabled  # 17
            print(f"原文件是 .dotm，将保持为模板: '{final_filepath}'")
        elif original_ext == '.docm':
            # 如果原始已经是 .docm，则保持为 .docm
            final_filepath = abs_target_filepath  # 保持原路径
            file_format = wdFormatXMLMacroEnabledDocument  # 16
            print(f"原文件是 .docm，将保持为文档: '{final_filepath}'")
        else:
            # 对于其他格式（或没有扩展名），默认转换为 .docm
            final_filepath = os.path.splitext(abs_target_filepath)[0] + '.docm'
            file_format = wdFormatXMLMacroEnabledDocument  # 16
            print(f"未知或无扩展名文件，将保存为启用宏的文档: '{final_filepath}'")

        # 确保输出目录存在
        output_dir = os.path.dirname(final_filepath)
        if output_dir and not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                print(f"已创建目录: '{output_dir}'")
            except Exception as dir_err:
                print(f"!!! 无法创建目录 '{output_dir}': {dir_err} !!!", file=sys.stderr)
                # 如果无法创建目录，保存将失败，在此处抛出错误退出
                raise dir_err

        # 保存文件
        try:
            doc.SaveAs2(final_filepath, FileFormat=file_format)
            print(f"文件已成功保存为: '{final_filepath}'")
        except Exception as save_error:
            print(f"!!! 保存文件 '{final_filepath}' 时出错: {save_error} !!!", file=sys.stderr)
            print("请检查文件路径是否合法、Word 是否有足够的权限写入、以及是否存在其他程序占用该文件。")
            # 如果保存失败，后续运行宏也没有意义，在此处重新抛出异常，以便在 finally 中捕获
            raise save_error

            # --- 5. 运行注入的 VBA 宏 ---
        print(f"正在运行注入的宏: '{macro_name}'...")
        word.Run(macro_name)
        print("宏执行完毕。请查看 Word 文档中的消息框。")

    except Exception as e:
        print(f"处理 Word 和 VBA 宏时发生错误: {e}", file=sys.stderr)

        # 检查是否是 VBA 项目访问权限问题
        if "Access to the VBA project object model is not trusted" in str(e) or \
                ("System.Reflection.TargetInvocationException" in str(e) and hasattr(e,
                                                                                     '__cause__') and e.__cause__ is not None and "Access to the VBA project object model is not trusted" in str(
                    e.__cause__)):
            print("\n--- 安全设置提示 ---")
            print("错误原因可能是 Word 的宏安全设置。")
            print("请在 Word 中前往 '文件' > '选项' > '信任中心' > '信任中心设置' > '宏设置'。")
            print("勾选 '信任对 Visual Basic 工程的访问 (Trust access to the VBA project object model)' 选项。")
            print("----------------------")

    finally:
        # --- 6. 关闭 Word 应用程序 ---
        if word:
            print("正在关闭 Word 应用程序...")
            try:
                # 尝试关闭文档，以防它仍然打开并且未被正确处理（例如保存失败）
                # doc.Close() 方法可能需要一个参数来决定是否保存
                # 在这种情况下，我们通常不希望因为关闭时出现保存提示而中断
                # 尝试用 False 参数，或者不传递参数（如果 Close() 是可选的）
                if doc:  # 确保 doc 对象存在
                    doc.Close(SaveChanges=False)  # 尝试不保存关闭文档
            except Exception as close_err:
                # 如果关闭文档时出错，也可能是因为文件被锁定或损坏
                print(f"尝试关闭文档时发生错误: {close_err}")
                pass  # 继续执行 Quit()

            word.Quit()
            # del word # 尝试释放 COM 对象，通常 Python 的垃圾回收会处理，但在 COM 场景中显式一下也无妨
            # import pythoncom
            # try:
            #     pythoncom.CoUninitialize() # 有时对 COM 对象的清理有帮助
            # except Exception as com_uninit_err:
            #     print(f"Error during CoUninitialize: {com_uninit_err}")
            #     pass

        print("Word 应用程序已处理完毕。")


# --- 常量定义 ---
# Word 文件格式常量
wdFormatXMLMacroEnabledDocument = 16  # DOCX with macros (.docm)
wdFormatXMLTemplateMacroEnabled = 17  # DOTX with macros (.dotm)

# --- 主执行部分 ---
if __name__ == "__main__":
    # 定义你的目标 Word 文件路径
    # 如果文件不存在，脚本会创建一个新的 .docm 文件。
    # 如果文件存在，它会被打开并修改。
    # 请根据你的需要修改此路径。

    # 修正这里的路径处理，使用原始字符串以避免转义问题
    # 确保您提供的路径是有效的，并且 C:\Users\<USER>\Desktop 目录存在且可写。
    target_word_file = r'C:\Users\<USER>\Desktop\11.docx'  # 可以是 .docx, .docm, .dotx, .dotm

    # 定义要注入并运行的宏的名称
    macro_name_to_run = 'AutoRunMacro'

    # 调用函数执行自动化操作
    inject_macro_into_word_file(target_word_file, macro_name_to_run)

    print("\nPython 脚本执行完毕。")
    print(f"请检查处理后的文件（可能已转换为 .docm/.dotm 格式）")
    print("如果宏未运行，请检查 Word 的宏安全设置。")