
(54)发明名称
一种基于时空融合算法的知识追踪模型及其方法
(57)摘要
本发明提供一种基于时空融合算法的知识追踪
模型及其方法，所述方法包括：构建嵌入模块，使
用嵌入来表示问题、技能和答案，定义技能嵌入矩
阵、问题嵌入矩阵和答案嵌入矩阵；构建聚合模块，
利用图卷积网络聚合图结构中的节点高阶邻居信
息，并创建自适应图优化问题-技能关系；构建状态
演变模块，使用门控循环单元捕捉学生在练习过程
中的序列行为，建模学生知识状态变化；构建历史
回顾模块，通过选择与当前目标问题最相关的练习，
更好地表示学生在特定问题上的能力；构建状态交
互模块，通过交叉注意力机制建模学生当前状态、
历史练习记录与目标问题及相关技能之间的复杂
交互关系。本发明能够有效捕捉知识追踪过程中的
时序特征和空间特征，提高预测学生答题表现的准
确性。

权利要求书3页  说明书12页  附图2页

1. 一种基于时空融合算法的知识追踪模型及其方法，其特征在于，所述方法包括：
构建嵌入模块，使用嵌入来表示问题、技能和答案，定义技能嵌入矩阵、问题嵌入矩阵和答案嵌入矩阵；
构建聚合模块，利用图卷积网络聚合图结构中的节点高阶邻居信息，并创建自适应图优化问题-技能关系；
构建状态演变模块，使用门控循环单元捕捉学生在练习过程中的序列行为，建模学生知识状态变化；
构建历史回顾模块，通过选择与当前目标问题最相关的练习，更好地表示学生在特定问题上的能力；
构建状态交互模块，通过交叉注意力机制建模学生当前状态、历史练习记录与目标问题及相关技能之间的复杂交互关系。

2. 如权利要求1所述的一种基于时空融合算法的知识追踪模型及其方法，其特征在于，所述嵌入模块包括：
技能嵌入矩阵Es∈R|S|×d，用于表示所有技能的嵌入；
问题嵌入矩阵Eq∈R|Q|×d，用于表示所有问题的嵌入；
答案嵌入矩阵Ea∈R2×d，用于表示正确和错误答案的嵌入；
其中，d表示嵌入的维度，|S|和|Q|分别表示技能和问题的数量。

3. 如权利要求1所述的一种基于时空融合算法的知识追踪模型及其方法，其特征在于，所述聚合模块包括：
构建问题-技能关系图，其中问题和技能作为节点，边表示问题与技能的关联关系；
构建问题-技能自适应图，其中问题和技能作为节点，边表示问题与技能的关联强度；
利用图卷积网络聚合节点的高阶邻居信息，将问题的1阶邻居和2阶邻居的信息聚合到问题的嵌入表示中。

4. 如权利要求3所述的一种基于时空融合算法的知识追踪模型及其方法，其特征在于，所述自适应图的创建方式为：
Ãadpqs = SoftMax(ReLU(Wq(Eq||Fq)Ws(Es||Fs)T))
其中，Eq是问题嵌入矩阵，Es是技能嵌入矩阵，Fq是问题静态特征向量，Fs是技能静态特征向量，Wq和Ws是可学习的权重矩阵，||表示向量拼接，ReLU函数用于消除弱连接，SoftMax函数用于对邻接矩阵进行归一化。

5. 如权利要求4所述的一种基于时空融合算法的知识追踪模型及其方法，其特征在于，所述问题静态特征向量Fq包括：
平均反应时间、平均回答次数、问题类型的one-hot编码、平均正确率等；
所述技能静态特征向量Fs包括：
技能类型的one-hot编码、技能难度等。

6. 如权利要求1所述的一种基于时空融合算法的知识追踪模型及其方法，其特征在于，所述状态演变模块包括：
对于每个历史时间步t，将问题嵌入和答案嵌入进行拼接，并通过非线性变换将其投影到d维空间，得到练习的表示：
et = ReLU(W1([q̃t, at]) + b1)
其中，q̃t是经过图卷积网络传播后的问题嵌入，at是学生对问题qt的回答，W1和b1是可学习的权重和偏置；
使用门控循环单元（GRU）来捕捉学生状态的变化，包括更新门、重置门和候选隐藏状态的计算；
通过隐藏状态更新公式ht = (1-zt)⊙ht-1 + zt⊙h̃t得到时间步t的学生知识状态。

7. 如权利要求1所述的一种基于时空融合算法的知识追踪模型及其方法，其特征在于，所述历史回顾模块包括：
定义注意力函数Attention(qt, ei)，基于技能相关性、时间衰减和练习难度计算历史练习与当前目标问题的相关性；
技能相关性通过skill_sim(qt, ei) = sqt·sei / (||sqt||·||sei||)计算，其中sqt和sei分别为目标问题和历史练习的嵌入向量；
时间衰减通过time_decay(ei) = e-λ·Δti计算，其中Δti是当前时间与历史练习ei的时间差，λ为时间衰减系数；
练习难度通过difficulty_factor(ei) = βdifficulty(ei)计算，其中β是难度调整系数，difficulty(ei)是历史练习ei的难度值；
将技能相关性、时间衰减和练习难度结合为综合注意力分数：
Attention(qt, ei) = skill_sim(qt, ei)·time_decay(ei)·difficulty_factor(ei)
通过softmax函数归一化得到注意力权重，选择权重最高的K个练习作为回顾内容。

8. 如权利要求1所述的一种基于时空融合算法的知识追踪模型及其方法，其特征在于，所述状态交互模块包括：
学生当前状态与目标问题的交互⟨ht, q̃t⟩；
学生当前状态与相关技能的交互⟨ht, s̃j⟩，其中s̃j∈Nqt；
历史练习与目标问题的交互⟨ei, q̃t⟩，其中ei∈Ie；
历史练习与相关技能的交互⟨ei, s̃j⟩，其中ei∈Ie，s̃j∈Nqt；
通过交叉注意力机制实现上述交互，并将所有交互结果进行融合：
Combined Output = ReLU(Wfc·[Outputhq, Outpuths, Outputeq, Outputes] + bfc)
最终通过Sigmoid函数输出学生正确回答目标问题的概率：
pt = σ(Wout·Combined Output + bout)。

9. 如权利要求1所述的一种基于时空融合算法的知识追踪模型及其方法，其特征在于，使用交叉熵损失函数训练模型：
L = -∑(at log pt + (1-at)log(1-pt))
其中，at是学生对第t个问题的真实答题结果，pt是模型预测学生正确回答第t个问题的概率。

10. 如权利要求3所述的一种基于时空融合算法的知识追踪模型及其方法，其特征在于，在聚合模块中使用图卷积网络对节点表示进行更新：
xi = σ( (1/∑j∈Ni∪{i}Aij) · ∑j∈Ni∪{i}WlAijxj(l-1) + bl )
其中，Ni是节点i的邻居节点集合，Wl和bl是第l层的可学习权重矩阵和偏置向量，Aij为自适应邻接矩阵节点i和节点j之间的权重，σ是非线性激活函数，如ReLU，xj(l-1)是节点j在上一层的表示。

一种基于时空融合算法的知识追踪模型及其方法

技术领域
[0001]  本发明涉及教育技术领域，尤其涉及一种基于时空融合算法的知识追踪模型及其方法。

背景技术
[0002]  知识追踪(Knowledge Tracing, KT)是指在线教育系统中追踪学习者的知识状态随时间变化的过程，其核心目标是预测学习者在未来任务中的表现。传统的知识追踪方法主要通过分析学习者的历史作答记录来推断其知识状态，但存在以下问题：
[0003]  首先，传统方法通常难以有效捕捉学习者知识状态的动态变化。在实际学习过程中，学习者的知识状态会随着学习活动而不断变化，传统方法缺乏对这种时序特性的建模能力。
[0004]  其次，现有方法往往忽视问题与技能之间的复杂关系。在线教育环境中，问题和技能构成了一个复杂的图结构，问题之间可能共享相同的技能，而技能之间也存在着各种联系，传统方法难以充分利用这种结构化信息。
[0005]  第三，现有方法通常将所有历史交互信息等同看待，缺乏针对性的关注机制，无法识别出对当前预测最有价值的历史信息，导致模型性能受到噪声信息的影响。
[0006]  因此，开发一种能够同时捕捉时序特征和空间特征的知识追踪模型，对提高预测准确性具有重要意义。

发明内容
[0007]  本发明的目的是提供一种基于时空融合算法的知识追踪模型及其方法，用以解决现有技术中难以同时有效捕捉时序特征和空间特征的问题。
[0008]  为实现上述目的，本发明提供了一种基于时空融合算法的知识追踪模型及其方法，所述方法包括：
[0009]  构建嵌入模块，使用嵌入来表示问题、技能和答案，定义技能嵌入矩阵、问题嵌入矩阵和答案嵌入矩阵；
[0010]  构建聚合模块，利用图卷积网络聚合图结构中的节点高阶邻居信息，并创建自适应图优化问题-技能关系；
[0011]  构建状态演变模块，使用门控循环单元捕捉学生在练习过程中的序列行为，建模学生知识状态变化；
[0012]  构建历史回顾模块，通过选择与当前目标问题最相关的练习，更好地表示学生在特定问题上的能力；
[0013]  构建状态交互模块，通过交叉注意力机制建模学生当前状态、历史练习记录与目标问题及相关技能之间的复杂交互关系。
[0014]  优选地，所述嵌入模块包括：
[0015]  技能嵌入矩阵Es∈R|S|×d，用于表示所有技能的嵌入；
[0016]  问题嵌入矩阵Eq∈R|Q|×d，用于表示所有问题的嵌入；
[0017]  答案嵌入矩阵Ea∈R2×d，用于表示正确和错误答案的嵌入；
[0018]  其中，d表示嵌入的维度，|S|和|Q|分别表示技能和问题的数量。
[0019]  优选地，所述聚合模块包括：
[0020]  构建问题-技能关系图，其中问题和技能作为节点，边表示问题与技能的关联关系；
[0021]  构建问题-技能自适应图，其中问题和技能作为节点，边表示问题与技能的关联强度；
[0022]  利用图卷积网络聚合节点的高阶邻居信息，将问题的1阶邻居和2阶邻居的信息聚合到问题的嵌入表示中。
[0023]  优选地，所述自适应图的创建方式为：
[0024]  Ãadpqs = SoftMax(ReLU(Wq(Eq||Fq)Ws(Es||Fs)T))
[0025]  其中，Eq是问题嵌入矩阵，Es是技能嵌入矩阵，Fq是问题静态特征向量，Fs是技能静态特征向量，Wq和Ws是可学习的权重矩阵，||表示向量拼接，ReLU函数用于消除弱连接，SoftMax函数用于对邻接矩阵进行归一化。
[0026]  优选地，所述问题静态特征向量Fq包括：
[0027]  平均反应时间、平均回答次数、问题类型的one-hot编码、平均正确率等；
[0028]  所述技能静态特征向量Fs包括：
[0029]  技能类型的one-hot编码、技能难度等。
[0030]  优选地，所述状态演变模块包括：
[0031]  对于每个历史时间步t，将问题嵌入和答案嵌入进行拼接，并通过非线性变换将其投影到d维空间，得到练习的表示：
[0032]  et = ReLU(W1([q̃t, at]) + b1)
[0033]  其中，q̃t是经过图卷积网络传播后的问题嵌入，at是学生对问题qt的回答，W1和b1是可学习的权重和偏置；
[0034]  使用门控循环单元（GRU）来捕捉学生状态的变化，包括：
[0035]  更新门：zt = σ(Wz[et, ht-1] + bz)
[0036]  重置门：rt = σ(Wr[et, ht-1] + br)
[0037]  候选隐藏状态：h̃t = tanh(Wh[et, rt ⊙ ht-1] + bh)
[0038]  隐藏状态更新：ht = (1-zt) ⊙ ht-1 + zt ⊙ h̃t
[0039]  其中，⊙表示逐元素乘法，σ是Sigmoid激活函数。
[0040]  优选地，所述历史回顾模块包括：
[0041]  定义注意力函数Attention(qt, ei)，基于技能相关性、时间衰减和练习难度计算历史练习与当前目标问题的相关性；
[0042]  技能相关性通过skill_sim(qt, ei) = sqt·sei / (||sqt||·||sei||)计算，其中sqt和sei分别为目标问题和历史练习的嵌入向量；
[0043]  时间衰减通过time_decay(ei) = e-λ·Δti计算，其中Δti是当前时间与历史练习ei的时间差，λ为时间衰减系数；
[0044]  练习难度通过difficulty_factor(ei) = βdifficulty(ei)计算，其中β是难度调整系数，difficulty(ei)是历史练习ei的难度值；
[0045]  将技能相关性、时间衰减和练习难度结合为综合注意力分数：
[0046]  Attention(qt, ei) = skill_sim(qt, ei)·time_decay(ei)·difficulty_factor(ei)
[0047]  通过softmax函数归一化得到注意力权重，选择权重最高的K个练习作为回顾内容。
[0048]  优选地，所述状态交互模块包括：
[0049]  学生当前状态与目标问题的交互⟨ht, q̃t⟩；
[0050]  学生当前状态与相关技能的交互⟨ht, s̃j⟩，其中s̃j∈Nqt；
[0051]  历史练习与目标问题的交互⟨ei, q̃t⟩，其中ei∈Ie；
[0052]  历史练习与相关技能的交互⟨ei, s̃j⟩，其中ei∈Ie，s̃j∈Nqt；
[0053]  通过交叉注意力机制实现上述交互，交叉注意力机制的关键公式包括：
[0054]  Query、Key和Value的计算：Q = WQ·Query + bQ, K = WK·Key + bK, V = WV·Value + bV；
[0055]  注意力分数的计算：Scores = (Q·KT) / √dk；
[0056]  Softmax归一化：Attention Weights = Softmax(Scores)；
[0057]  加权求和：Output = Attention Weights·V；
[0058]  将所有交互结果进行融合：
[0059]  Combined Output = ReLU(Wfc·[Outputhq, Outpuths, Outputeq, Outputes] + bfc)
[0060]  最终通过Sigmoid函数输出学生正确回答目标问题的概率：
[0061]  pt = σ(Wout·Combined Output + bout)。
[0062]  优选地，使用交叉熵损失函数训练模型：
[0063]  L = -∑(at log pt + (1-at)log(1-pt))
[0064]  其中，at是学生对第t个问题的真实答题结果，pt是模型预测学生正确回答第t个问题的概率。
[0065]  本发明具有以下有益效果：
[0066]  1. 本发明通过融合时序特征和空间特征，能够更全面地捕捉学习者的知识状态变化过程，提高预测准确性；
[0067]  2. 通过自适应图结构和图卷积网络，本发明能够更好地利用问题-技能之间的复杂关系，有效挖掘高阶依赖信息；
[0068]  3. 本发明引入历史回顾模块和注意力机制，能够选择性地关注最相关的历史信息，减少噪声对预测的干扰；
[0069]  4. 交叉注意力机制使模型能够捕捉学生状态、目标问题和技能之间的多维交互关系，进一步提高模型的表达能力和泛化能力。

附图说明
[0070]  图1是本发明一种基于时空融合算法的知识追踪模型的整体架构示意图；
[0071]  图2是本发明的嵌入模块示意图；
[0072]  图3是本发明的自适应图构建和聚合模块示意图；
[0073]  图4是本发明的状态演变模块示意图；
[0074]  图5是本发明的历史回顾模块示意图；
[0075]  图6是本发明的状态交互模块示意图。

具体实施方式
[0076]  下面结合附图对本发明作进一步的详细说明。
[0077]  本发明提出了一种基于时空融合算法的知识追踪模型及其方法，该模型包括嵌入模块、聚合模块、状态演变模块、历史回顾模块和状态交互模块。整体架构如图1所示。
[0078]  实施例1：
[0079]  在本实施例中，图1展示了本发明的整体架构，包括五个主要模块：嵌入模块、聚合模块、状态演变模块、历史回顾模块和状态交互模块。

[0080]  首先，嵌入模块负责为问题、技能和答案生成初始表示。如图2所示，定义了三种嵌入矩阵：
[0081]  技能嵌入矩阵Es∈R|S|×d，用于表示所有技能的嵌入；
[0082]  问题嵌入矩阵Eq∈R|Q|×d，用于表示所有问题的嵌入；
[0083]  答案嵌入矩阵Ea∈R2×d，用于表示正确和错误答案的嵌入；
[0084]  其中，d表示嵌入的维度，通常设置为64或128，|S|和|Q|分别表示技能和问题的数量。这些嵌入矩阵作为模型参数，在训练过程中通过端到端方式优化。

[0085]  其次，聚合模块负责处理问题-技能关系图中的节点信息传播。如图3所示，本模块包括：
[0086]  (1) 构建问题-技能关系图：问题和技能作为节点，边表示它们之间的关联关系；
[0087]  (2) 创建自适应图：为解决传统邻接矩阵无法反映节点间真实依赖关系的问题，引入自适应邻接矩阵：
[0088]  Ãadpqs = SoftMax(ReLU(Wq(Eq||Fq)Ws(Es||Fs)T))
[0089]  其中，||表示向量拼接，Fq和Fs分别是问题和技能的静态特征向量，包含诸如问题类型、平均反应时间、技能难度等信息。ReLU函数用于消除弱连接，SoftMax用于归一化。
[0090]  (3) 图卷积更新：通过多层图卷积网络（GCN）更新节点表示：
[0091]  xi = σ( (1/∑j∈Ni∪{i}Aij) · ∑j∈Ni∪{i}WlAijxj(l-1) + bl )
[0092]  其中，Ni是节点i的邻居集合，Wl和bl是第l层的可学习参数，Aij是自适应邻接矩阵中的权重。通过堆叠多层GCN，模型能够聚合高阶邻居信息。

[0093]  第三，状态演变模块负责捕捉学生知识状态的时序变化。如图4所示，本模块包括：
[0094]  (1) 练习表示：对于每个历史时间步t，生成练习表示：
[0095]  et = ReLU(W1([q̃t, at]) + b1)
[0096]  其中，q̃t是经过GCN传播后的问题嵌入，at是学生对问题qt的回答（0或1）。
[0097]  (2) GRU建模：使用门控循环单元（GRU）更新学生状态：
[0098]  更新门：zt = σ(Wz[et, ht-1] + bz)
[0099]  重置门：rt = σ(Wr[et, ht-1] + br)
[0100]  候选隐藏状态：h̃t = tanh(Wh[et, rt ⊙ ht-1] + bh)
[0101]  隐藏状态更新：ht = (1-zt) ⊙ ht-1 + zt ⊙ h̃t
[0102]  其中，ht表示学生在时间步t的知识状态，⊙表示逐元素乘法，σ是Sigmoid激活函数。

[0103]  第四，历史回顾模块负责筛选与当前目标问题最相关的历史练习。如图5所示，本模块包括：
[0104]  (1) 定义注意力函数Attention(qt, ei)，计算历史练习与当前目标问题的相关性：
[0105]  Attention(qt, ei) = skill_sim(qt, ei)·time_decay(ei)·difficulty_factor(ei)
[0106]  其中：
[0107]  - 技能相关性skill_sim(qt, ei)通过余弦相似度计算问题嵌入向量间的相似性；
[0108]  - 时间衰减time_decay(ei) = e-λ·Δti模拟学生记忆遗忘过程，Δti是时间差，λ是衰减系数；
[0109]  - 难度因子difficulty_factor(ei) = βdifficulty(ei)考虑练习难度对学习效果的影响，β是调整系数。
[0110]  (2) 归一化注意力权重并选择前K个最相关练习：
[0111]  αi,t = softmax(Attention(qt, ei))
[0112]  选择αi,t值最高的K个历史练习作为回顾内容，K可以根据目标问题的复杂度动态调整。

[0113]  第五，状态交互模块负责建模学生状态与其他元素之间的复杂交互关系。如图6所示，本模块包括：
[0114]  (1) 定义以下四类交互：
[0115]  - 学生当前状态与目标问题的交互⟨ht, q̃t⟩；
[0116]  - 学生当前状态与相关技能的交互⟨ht, s̃j⟩，其中s̃j∈Nqt；
[0117]  - 历史练习与目标问题的交互⟨ei, q̃t⟩，其中ei∈Ie；
[0118]  - 历史练习与相关技能的交互⟨ei, s̃j⟩，其中ei∈Ie，s̃j∈Nqt。
[0119]  (2) 利用交叉注意力机制实现这些交互，关键步骤包括：
[0120]  - Query、Key和Value的线性变换；
[0121]  - 计算注意力分数Scores = (Q·KT) / √dk；
[0122]  - Softmax归一化得到注意力权重；
[0123]  - 加权求和得到交互结果Output = Attention Weights·V。
[0124]  (3) 融合交互结果并输出预测概率：
[0125]  Combined Output = ReLU(Wfc·[Outputhq, Outpuths, Outputeq, Outputes] + bfc)
[0126]  pt = σ(Wout·Combined Output + bout)
[0127]  其中，pt表示学生正确回答目标问题qt的概率。

[0128]  最后，使用交叉熵损失函数训练模型：
[0129]  L = -∑(at log pt + (1-at)log(1-pt))
[0130]  其中，at是真实标签，pt是预测概率。

[0131]  实施例2：
[0132]  在本实施例中，详细说明了自适应图构建过程中静态特征的提取方法。如前所述，问题静态特征向量Fq和技能静态特征向量Fs可增强自适应图的表达能力。
[0133]  问题静态特征Fq可包括：
[0134]  - 平均反应时间：记录学生回答该问题所花费时间的平均值；
[0135]  - 平均回答次数：记录该问题被回答的平均次数；
[0136]  - 问题类型：使用one-hot编码表示不同类型的问题（如选择题、填空题等）；
[0137]  - 平均正确率：记录所有学生在该问题上的平均正确率；
[0138]  - 问题难度：基于IRT模型计算的问题难度参数。

[0139]  技能静态特征Fs可包括：
[0140]  - 技能类型：使用one-hot编码表示不同领域的技能；
[0141]  - 技能难度：基于所有涉及该技能的问题难度计算的平均难度；
[0142]  - 技能关联度：与其他技能的关联程度；
[0143]  - 技能掌握时间：学生平均需要多长时间掌握该技能。

[0144]  这些静态特征通过与嵌入向量拼接后，经过权重矩阵变换，增强了自适应图中节点之间关系的表达能力。通过引入这些特征，模型能够更好地理解问题和技能之间的内在联系，提高图结构的表达能力。

[0145]  实施例3：
[0146]  在本实施例中，详细说明了邻居采样策略，用于提高大规模图数据处理的效率。
[0147]  当问题和技能数量较大时，直接处理完整的邻接矩阵可能导致计算负担过重。为此，引入邻居采样策略：
[0148]  (1) 重要性采样：根据自适应邻接矩阵中的权重Aij进行采样，优先选择权重较大的邻居节点；
[0149]  (2) 固定大小采样：对每个节点，采样固定数量k的邻居节点，k可设置为8、16或32；
[0150]  (3) 动态采样：根据节点的度（degree）动态调整采样数量，对于高连接度的节点采样更多邻居。

[0151]  在训练过程中，每个批次可以使用不同的邻居采样结果，增加训练的多样性。在推理阶段，可以采用多次采样并平均结果的策略，提高预测的稳定性。

[0152]  实施例4：
[0153]  在本实施例中，详细说明了历史回顾模块中注意力函数的设计变体。
[0154]  除了基本的注意力函数Attention(qt, ei) = skill_sim(qt, ei)·time_decay(ei)·difficulty_factor(ei)，本发明还提供以下变体：
[0155]  (1) 加入学生因素：
[0156]  Attention(qt, ei, st) = skill_sim(qt, ei)·time_decay(ei)·difficulty_factor(ei)·student_factor(st, ei)
[0157]  其中，student_factor(st, ei)表示学生特定的因素，如学生对相关技能的掌握程度。
[0158]  (2) 非线性组合：
[0159]  Attention(qt, ei) = tanh(W·[skill_sim(qt, ei), time_decay(ei), difficulty_factor(ei)] + b)
[0160]  通过可学习的权重W和偏置b，以非线性方式组合各个因素。
[0161]  (3) 多头注意力：
[0162]  将注意力机制扩展为多头形式，每个头学习不同的注意力模式，然后将多个头的结果合并。
[0163]  这些变体可以根据具体应用场景和数据特点进行选择，以获得最佳性能。

[0164]  以上所述仅为本发明的优选实施例，并非因此限制本发明的专利范围，凡是利用本发明说明书及附图内容所作的等效结构或等效流程变换，或直接或间接运用在其他相关的技术领域，均同理包括在本发明的专利保护范围内。
