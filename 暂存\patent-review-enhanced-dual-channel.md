(54)发明名称
一种基于回顾增强的双通道状态建模知识追踪模型及其方法
(57)摘要
本发明提供一种基于回顾增强的双通道状态建模知识追踪模型及其方法，所述方法包括：构建嵌入模块，使用嵌入来表示问题、技能和答案，定义技能嵌入矩阵、问题嵌入矩阵和答案嵌入矩阵；构建回顾增强模块，通过计算当前题目与历史题目的相似度并考虑时间距离因素，模拟学习者的记忆、回顾、检索和遗忘过程；构建双通道状态建模模块，包括LSTM通道、Transformer通道和TCN通道，分别捕捉序列依赖关系、长距离依赖和回顾增强信息；构建动态门控融合模块，通过两阶段融合机制动态调整不同通道的权重；构建预测模块，输出学生正确回答目标问题的概率。本发明能够有效模拟人类学习过程中的回顾行为，同时利用多通道架构捕捉不同类型的学习模式，提高预测学生答题表现的准确性。

权利要求书3页  说明书12页  附图2页

1. 一种基于回顾增强的双通道状态建模知识追踪模型及其方法，其特征在于，所述方法包括：
构建嵌入模块，使用嵌入来表示问题、技能和答案，定义技能嵌入矩阵、问题嵌入矩阵和答案嵌入矩阵；
构建回顾增强模块，通过计算当前题目与历史题目的相似度并考虑时间距离因素，模拟学习者的记忆、回顾、检索和遗忘过程；
构建双通道状态建模模块，包括LSTM通道、Transformer通道和TCN通道，分别捕捉序列依赖关系、长距离依赖和回顾增强信息；
构建动态门控融合模块，通过两阶段融合机制动态调整不同通道的权重；
构建预测模块，输出学生正确回答目标问题的概率。

2. 如权利要求1所述的一种基于回顾增强的双通道状态建模知识追踪模型及其方法，其特征在于，所述嵌入模块包括：
技能嵌入矩阵$E_s \in \mathbb{R}^{|S| \times d}$，用于表示所有技能的嵌入；
问题嵌入矩阵$E_q \in \mathbb{R}^{|Q| \times d}$，用于表示所有问题的嵌入；
答案嵌入矩阵$E_a \in \mathbb{R}^{2 \times d}$，用于表示正确和错误答案的嵌入；
其中，$d$表示嵌入的维度，$|S|$和$|Q|$分别表示技能和问题的数量。

3. 如权利要求1所述的一种基于回顾增强的双通道状态建模知识追踪模型及其方法，其特征在于，所述回顾增强模块包括：
计算当前题目与历史题目的相似度，使用余弦相似度公式：
$$\text{sim}(q_t, q_i) = \frac{e_{q_t} \cdot e_{q_i}}{\|e_{q_t}\| \cdot \|e_{q_i}\|}$$
其中，$e_{q_t}$和$e_{q_i}$分别为当前题目和历史题目的嵌入向量；
构建上三角矩阵确保只考虑历史题目，避免信息泄露；
引入时间距离编码器，将时间距离转换为向量表示：
$$\text{time\_encoding}(\Delta t) = PE(\Delta t)$$
其中，$PE$为位置编码函数，$\Delta t$为时间距离。

4. 如权利要求3所述的一种基于回顾增强的双通道状态建模知识追踪模型及其方法，其特征在于，所述回顾增强模块的相似度计算方式为：
$$\text{enhanced\_sim}(q_t, q_i) = \text{sim}(q_t, q_i) \cdot \exp(-\lambda \cdot \Delta t)$$
其中，$\lambda$为遗忘系数，用于模拟遗忘曲线的影响，$\Delta t$为当前题目与历史题目的时间距离；
通过softmax函数归一化得到注意力权重：
$$\alpha_i = \text{softmax}(\text{enhanced\_sim}(q_t, q_i))$$
选择权重最高的$K$个历史题目作为回顾内容。

5. 如权利要求1所述的一种基于回顾增强的双通道状态建模知识追踪模型及其方法，其特征在于，所述双通道状态建模模块包括：
LSTM通道：使用长短期记忆网络捕捉序列依赖关系，包括遗忘门、输入门和输出门的计算；
Transformer通道：使用多头自注意力机制捕捉长距离依赖关系；
TCN通道：使用时间卷积网络处理回顾增强信息，专门建模学生通过回顾历史题目来辅助解题的过程。

6. 如权利要求5所述的一种基于回顾增强的双通道状态建模知识追踪模型及其方法，其特征在于，所述LSTM通道的计算包括：
遗忘门：$f_t = \sigma(W_f \cdot [h_{t-1}, x_t] + b_f)$
输入门：$i_t = \sigma(W_i \cdot [h_{t-1}, x_t] + b_i)$
候选值：$\tilde{C}_t = \tanh(W_C \cdot [h_{t-1}, x_t] + b_C)$
细胞状态更新：$C_t = f_t \odot C_{t-1} + i_t \odot \tilde{C}_t$
输出门：$o_t = \sigma(W_o \cdot [h_{t-1}, x_t] + b_o)$
隐藏状态：$h_t = o_t \odot \tanh(C_t)$
其中，$\sigma$为sigmoid函数，$\odot$表示逐元素乘法，$W$和$b$为可学习参数。

7. 如权利要求5所述的一种基于回顾增强的双通道状态建模知识追踪模型及其方法，其特征在于，所述Transformer通道包括：
多头自注意力机制：
$$\text{MultiHead}(Q, K, V) = \text{Concat}(\text{head}_1, \ldots, \text{head}_h)W^O$$
其中，$\text{head}_i = \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)$
$$\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V$$
位置编码：将序列位置信息编码为向量并与输入嵌入相加；
前馈神经网络：$\text{FFN}(x) = \max(0, xW_1 + b_1)W_2 + b_2$
残差连接和层归一化：$\text{LayerNorm}(x + \text{Sublayer}(x))$。

8. 如权利要求5所述的一种基于回顾增强的双通道状态建模知识追踪模型及其方法，其特征在于，所述TCN通道包括：
因果卷积：确保在时间步t只能访问t及之前的信息；
膨胀卷积：通过膨胀因子扩大感受野，捕捉长距离依赖；
残差连接：$y = \text{Activation}(\text{Conv}(x)) + x$
其中，$\text{Conv}$表示膨胀因果卷积操作，$\text{Activation}$为激活函数；
专门处理回顾增强信息，将回顾权重与题目序列结合。

9. 如权利要求1所述的一种基于回顾增强的双通道状态建模知识追踪模型及其方法，其特征在于，所述动态门控融合模块包括：
第一阶段融合：融合LSTM通道和Transformer通道的输出
$$g_1 = \sigma(W_1 \cdot [h_{\text{LSTM}}, h_{\text{Transformer}}] + b_1)$$
$$\text{fused}_1 = g_1 \odot h_{\text{LSTM}} + (1 - g_1) \odot h_{\text{Transformer}}$$
第二阶段融合：将第一阶段结果与TCN通道输出融合
$$g_2 = \sigma(W_2 \cdot [\text{fused}_1, h_{\text{TCN}}] + b_2)$$
$$\text{final\_output} = g_2 \odot \text{fused}_1 + (1 - g_2) \odot h_{\text{TCN}}$$
其中，$\odot$表示逐元素乘法，$\sigma$为sigmoid函数，$W$和$b$为可学习参数。

10. 如权利要求1所述的一种基于回顾增强的双通道状态建模知识追踪模型及其方法，其特征在于，所述预测模块包括：
将融合后的特征通过全连接层进行变换：
$$\text{hidden} = \text{ReLU}(W_{\text{pred}} \cdot \text{final\_output} + b_{\text{pred}})$$
通过sigmoid函数输出预测概率：
$$p_t = \sigma(W_{\text{out}} \cdot \text{hidden} + b_{\text{out}})$$
其中，$p_t$表示学生正确回答目标问题的概率；
使用二元交叉熵损失函数训练模型：
$$L = -\sum(y_t \log p_t + (1-y_t)\log(1-p_t))$$
其中，$y_t$是真实标签，$p_t$是预测概率。

一种基于回顾增强的双通道状态建模知识追踪模型及其方法

技术领域
[0001]  本发明涉及教育技术领域，尤其涉及一种基于回顾增强的双通道状态建模知识追踪模型及其方法。

背景技术
[0002]  知识追踪(Knowledge Tracing, KT)是指在线教育系统中追踪学习者的知识状态随时间变化的过程，其核心目标是预测学习者在未来任务中的表现。传统的知识追踪方法主要通过分析学习者的历史作答记录来推断其知识状态，但存在以下问题：
[0003]  首先，现有模型如SAKT、AKT、SAINT、simpleKT虽然引入了注意力机制，但主要关注题目序列的时序关系，没有充分利用历史相似题目的信息。在实际学习中，学生遇到新题目时会回想之前做过的类似题目，这种重要的学习行为在传统模型中被忽略了。
[0004]  其次，大多数模型通常采用单一的模型架构（如LSTM或Transformer），这种设计可能无法同时捕捉不同类型的学习模式。例如，LSTM擅长捕捉序列依赖但在处理长距离依赖时表现不佳，Transformer虽然能处理长距离依赖但可能无法很好地捕捉局部序列特征。
[0005]  第三，现有方法缺乏对学习者记忆、回顾、检索和遗忘等认知过程的有效建模，无法模拟人类学习过程中的重要行为模式。
[0006]  因此，开发一种能够模拟人类回顾行为并同时捕捉多种学习模式的知识追踪模型，对提高预测准确性具有重要意义。

发明内容
[0007]  本发明的目的是提供一种基于回顾增强的双通道状态建模知识追踪模型及其方法，用以解决现有技术中无法有效利用历史相似题目信息和单一模型架构局限性的问题。
[0008]  为实现上述目的，本发明提供了一种基于回顾增强的双通道状态建模知识追踪模型及其方法，所述方法包括：
[0009]  构建嵌入模块，使用嵌入来表示问题、技能和答案，定义技能嵌入矩阵、问题嵌入矩阵和答案嵌入矩阵；
[0010]  构建回顾增强模块，通过计算当前题目与历史题目的相似度并考虑时间距离因素，模拟学习者的记忆、回顾、检索和遗忘过程；
[0011]  构建双通道状态建模模块，包括LSTM通道、Transformer通道和TCN通道，分别捕捉序列依赖关系、长距离依赖和回顾增强信息；
[0012]  构建动态门控融合模块，通过两阶段融合机制动态调整不同通道的权重；
[0013]  构建预测模块，输出学生正确回答目标问题的概率。
[0014]  优选地，所述嵌入模块包括：
[0015]  技能嵌入矩阵$E_s \in \mathbb{R}^{|S| \times d}$，用于表示所有技能的嵌入；
[0016]  问题嵌入矩阵$E_q \in \mathbb{R}^{|Q| \times d}$，用于表示所有问题的嵌入；
[0017]  答案嵌入矩阵$E_a \in \mathbb{R}^{2 \times d}$，用于表示正确和错误答案的嵌入；
[0018]  其中，$d$表示嵌入的维度，$|S|$和$|Q|$分别表示技能和问题的数量。
[0019]  优选地，所述回顾增强模块包括：
[0020]  计算当前题目与历史题目的相似度，使用余弦相似度公式：
[0021]  $$\text{sim}(q_t, q_i) = \frac{e_{q_t} \cdot e_{q_i}}{\|e_{q_t}\| \cdot \|e_{q_i}\|}$$
[0022]  其中，$e_{q_t}$和$e_{q_i}$分别为当前题目和历史题目的嵌入向量；
[0023]  构建上三角矩阵确保只考虑历史题目，避免信息泄露；
[0024]  引入时间距离编码器，将时间距离转换为向量表示。
[0025]  优选地，所述回顾增强模块的相似度计算方式为：
[0026]  $$\text{enhanced\_sim}(q_t, q_i) = \text{sim}(q_t, q_i) \cdot \exp(-\lambda \cdot \Delta t)$$
[0027]  其中，$\lambda$为遗忘系数，用于模拟遗忘曲线的影响，$\Delta t$为当前题目与历史题目的时间距离；
[0028]  通过softmax函数归一化得到注意力权重，选择权重最高的K个历史题目作为回顾内容。
[0029]  优选地，所述双通道状态建模模块包括：
[0030]  LSTM通道：使用长短期记忆网络捕捉序列依赖关系，包括遗忘门、输入门和输出门的计算；
[0031]  Transformer通道：使用多头自注意力机制捕捉长距离依赖关系；
[0032]  TCN通道：使用时间卷积网络处理回顾增强信息，专门建模学生通过回顾历史题目来辅助解题的过程。
[0033]  优选地，所述LSTM通道的计算包括：
[0034]  遗忘门：$f_t = \sigma(W_f \cdot [h_{t-1}, x_t] + b_f)$
[0035]  输入门：$i_t = \sigma(W_i \cdot [h_{t-1}, x_t] + b_i)$
[0036]  候选值：$\tilde{C}_t = \tanh(W_C \cdot [h_{t-1}, x_t] + b_C)$
[0037]  细胞状态更新：$C_t = f_t \odot C_{t-1} + i_t \odot \tilde{C}_t$
[0038]  输出门：$o_t = \sigma(W_o \cdot [h_{t-1}, x_t] + b_o)$
[0039]  隐藏状态：$h_t = o_t \odot \tanh(C_t)$
[0040]  其中，$\sigma$为sigmoid函数，$\odot$表示逐元素乘法，$W$和$b$为可学习参数。
[0041]  优选地，所述Transformer通道包括：
[0042]  多头自注意力机制：
[0043]  $$\text{MultiHead}(Q, K, V) = \text{Concat}(\text{head}_1, \ldots, \text{head}_h)W^O$$
[0044]  其中，$\text{head}_i = \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)$
[0045]  $$\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V$$
[0046]  位置编码：将序列位置信息编码为向量并与输入嵌入相加；
[0047]  前馈神经网络：$\text{FFN}(x) = \max(0, xW_1 + b_1)W_2 + b_2$
[0048]  残差连接和层归一化：$\text{LayerNorm}(x + \text{Sublayer}(x))$。
[0049]  优选地，所述TCN通道包括：
[0050]  因果卷积：确保在时间步t只能访问t及之前的信息；
[0051]  膨胀卷积：通过膨胀因子扩大感受野，捕捉长距离依赖；
[0052]  残差连接：$y = \text{Activation}(\text{Conv}(x)) + x$
[0053]  其中，$\text{Conv}$表示膨胀因果卷积操作，$\text{Activation}$为激活函数；
[0054]  专门处理回顾增强信息，将回顾权重与题目序列结合。
[0055]  优选地，所述动态门控融合模块包括：
[0056]  第一阶段融合：融合LSTM通道和Transformer通道的输出
[0057]  $$g_1 = \sigma(W_1 \cdot [h_{\text{LSTM}}, h_{\text{Transformer}}] + b_1)$$
[0058]  $$\text{fused}_1 = g_1 \odot h_{\text{LSTM}} + (1 - g_1) \odot h_{\text{Transformer}}$$
[0059]  第二阶段融合：将第一阶段结果与TCN通道输出融合
[0060]  $$g_2 = \sigma(W_2 \cdot [\text{fused}_1, h_{\text{TCN}}] + b_2)$$
[0061]  $$\text{final\_output} = g_2 \odot \text{fused}_1 + (1 - g_2) \odot h_{\text{TCN}}$$
[0062]  其中，$\odot$表示逐元素乘法，$\sigma$为sigmoid函数，$W$和$b$为可学习参数。
[0063]  优选地，所述预测模块包括：
[0064]  将融合后的特征通过全连接层进行变换：
[0065]  $$\text{hidden} = \text{ReLU}(W_{\text{pred}} \cdot \text{final\_output} + b_{\text{pred}})$$
[0066]  通过sigmoid函数输出预测概率：
[0067]  $$p_t = \sigma(W_{\text{out}} \cdot \text{hidden} + b_{\text{out}})$$
[0068]  其中，$p_t$表示学生正确回答目标问题的概率；
[0069]  使用二元交叉熵损失函数训练模型。
[0070]  本发明具有以下有益效果：
[0071]  1. 本发明通过回顾增强模块，能够有效模拟人类学习过程中的回顾行为，利用历史相似题目的信息，提高预测准确性；
[0072]  2. 通过双通道状态建模架构，本发明能够同时捕捉序列依赖关系、长距离依赖和回顾增强信息，克服单一模型架构的局限性；
[0073]  3. 本发明引入动态门控融合机制，能够根据不同情况自适应地调整各通道的权重，提高模型的灵活性和表达能力；
[0074]  4. 时间距离编码和遗忘曲线建模使模型能够更好地模拟人类记忆和遗忘过程，增强模型的生物学合理性。

附图说明
[0075]  图1是本发明一种基于回顾增强的双通道状态建模知识追踪模型的整体架构示意图；
[0076]  图2是本发明的嵌入模块示意图；
[0077]  图3是本发明的回顾增强模块示意图；
[0078]  图4是本发明的双通道状态建模模块示意图；
[0079]  图5是本发明的动态门控融合模块示意图；
[0080]  图6是本发明的预测模块示意图。

具体实施方式
[0081]  下面结合附图对本发明作进一步的详细说明。
[0082]  本发明提出了一种基于回顾增强的双通道状态建模知识追踪模型及其方法，该模型包括嵌入模块、回顾增强模块、双通道状态建模模块、动态门控融合模块和预测模块。整体架构如图1所示。
[0083]  实施例1：
[0084]  在本实施例中，图1展示了本发明的整体架构，包括五个主要模块：嵌入模块、回顾增强模块、双通道状态建模模块、动态门控融合模块和预测模块。

[0085]  首先，嵌入模块负责为问题、技能和答案生成初始表示。如图2所示，定义了三种嵌入矩阵：
[0086]  技能嵌入矩阵$E_s \in \mathbb{R}^{|S| \times d}$，用于表示所有技能的嵌入；
[0087]  问题嵌入矩阵$E_q \in \mathbb{R}^{|Q| \times d}$，用于表示所有问题的嵌入；
[0088]  答案嵌入矩阵$E_a \in \mathbb{R}^{2 \times d}$，用于表示正确和错误答案的嵌入；
[0089]  其中，$d$表示嵌入的维度，通常设置为64、128或256，$|S|$和$|Q|$分别表示技能和问题的数量。这些嵌入矩阵作为模型参数，在训练过程中通过端到端方式优化。

[0090]  其次，回顾增强模块负责模拟学习者的回顾行为。如图3所示，本模块包括：
[0091]  (1) 相似度计算：使用余弦相似度计算当前题目与历史题目的相似度：
[0092]  $$\text{sim}(q_t, q_i) = \frac{e_{q_t} \cdot e_{q_i}}{\|e_{q_t}\| \cdot \|e_{q_i}\|}$$
[0093]  其中，$e_{q_t}$和$e_{q_i}$分别为当前题目和历史题目的嵌入向量。
[0094]  (2) 时间距离建模：引入遗忘曲线，考虑时间距离对相似度的影响：
[0095]  $$\text{enhanced\_sim}(q_t, q_i) = \text{sim}(q_t, q_i) \cdot \exp(-\lambda \cdot \Delta t)$$
[0096]  其中，$\lambda$为遗忘系数，$\Delta t$为时间距离。这种设计模拟了人类记忆中的遗忘过程，距离当前时间越远的题目，其影响权重越小。
[0097]  (3) 上三角矩阵约束：为避免信息泄露，使用上三角矩阵确保只考虑历史题目，即只有当$i < t$时，才计算$\text{sim}(q_t, q_i)$。
[0098]  (4) 注意力权重计算：通过softmax函数归一化得到注意力权重，选择权重最高的$K$个历史题目作为回顾内容。

[0099]  第三，双通道状态建模模块负责捕捉不同类型的学习模式。如图4所示，本模块包括三个并行通道：
[0100]  (1) LSTM通道：擅长捕捉序列依赖关系，特别是短期记忆模式。LSTM的计算包括：
[0101]  遗忘门：$f_t = \sigma(W_f \cdot [h_{t-1}, x_t] + b_f)$
[0102]  输入门：$i_t = \sigma(W_i \cdot [h_{t-1}, x_t] + b_i)$
[0103]  候选值：$\tilde{C}_t = \tanh(W_C \cdot [h_{t-1}, x_t] + b_C)$
[0104]  细胞状态更新：$C_t = f_t \odot C_{t-1} + i_t \odot \tilde{C}_t$
[0105]  输出门：$o_t = \sigma(W_o \cdot [h_{t-1}, x_t] + b_o)$
[0106]  隐藏状态：$h_t = o_t \odot \tanh(C_t)$
[0107]  这种设计使LSTM能够选择性地记住和遗忘信息，适合建模学生的短期学习状态变化。

[0108]  (2) Transformer通道：擅长捕捉长距离依赖关系，能够关注序列中任意位置的信息。其核心是多头自注意力机制：
[0109]  $$\text{MultiHead}(Q, K, V) = \text{Concat}(\text{head}_1, \ldots, \text{head}_h)W^O$$
[0110]  其中，每个注意力头计算为：
[0111]  $$\text{head}_i = \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)$$
[0112]  $$\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V$$
[0113]  通过多头机制，Transformer能够从不同角度关注序列信息，捕捉复杂的依赖关系。

[0114]  (3) TCN通道：专门处理回顾增强信息，使用时间卷积网络的因果卷积和膨胀卷积：
[0115]  因果卷积确保在时间步t只能访问t及之前的信息，避免信息泄露；
[0116]  膨胀卷积通过膨胀因子扩大感受野，能够捕捉长距离依赖；
[0117]  残差连接：$y = \text{Activation}(\text{Conv}(x)) + x$，提高训练稳定性。
[0118]  TCN通道将回顾权重与题目序列结合，专门建模学生通过回顾历史题目来辅助解题的过程。

[0119]  第四，动态门控融合模块负责融合三个通道的输出。如图5所示，采用两阶段融合策略：
[0120]  (1) 第一阶段：融合LSTM通道和Transformer通道的输出
[0121]  $$g_1 = \sigma(W_1 \cdot [h_{\text{LSTM}}, h_{\text{Transformer}}] + b_1)$$
[0122]  $$\text{fused}_1 = g_1 \odot h_{\text{LSTM}} + (1 - g_1) \odot h_{\text{Transformer}}$$
[0123]  其中，$g_1$是动态门控权重，根据输入自适应调整两个通道的重要性。
[0124]  (2) 第二阶段：将第一阶段结果与TCN通道输出融合
[0125]  $$g_2 = \sigma(W_2 \cdot [\text{fused}_1, h_{\text{TCN}}] + b_2)$$
[0126]  $$\text{final\_output} = g_2 \odot \text{fused}_1 + (1 - g_2) \odot h_{\text{TCN}}$$
[0127]  这种两阶段设计使模型能够精细地控制不同信息的权重，例如对于需要回顾历史题目的情况，模型可能会增加TCN通道的权重。

[0128]  第五，预测模块负责输出最终的预测结果。如图6所示，本模块包括：
[0129]  (1) 特征变换：将融合后的特征通过全连接层进行变换
[0130]  $$\text{hidden} = \text{ReLU}(W_{\text{pred}} \cdot \text{final\_output} + b_{\text{pred}})$$
[0131]  (2) 概率输出：通过sigmoid函数输出预测概率
[0132]  $$p_t = \sigma(W_{\text{out}} \cdot \text{hidden} + b_{\text{out}})$$
[0133]  其中，$p_t$表示学生正确回答目标问题的概率。
[0134]  (3) 损失函数：使用二元交叉熵损失函数训练模型
[0135]  $$L = -\sum(y_t \log p_t + (1-y_t)\log(1-p_t))$$
[0136]  其中，$y_t$是真实标签，$p_t$是预测概率。

[0137]  实施例2：
[0138]  在本实施例中，详细说明了回顾增强模块中时间距离编码的实现方法。
[0139]  时间距离编码器采用正弦和余弦函数的组合来编码时间距离：
[0140]  $$PE(\Delta t, 2i) = \sin\left(\frac{\Delta t}{10000^{2i/d}}\right)$$
[0141]  $$PE(\Delta t, 2i+1) = \cos\left(\frac{\Delta t}{10000^{2i/d}}\right)$$
[0142]  其中，$\Delta t$是时间距离，$i$是维度索引，$d$是编码维度。
[0143]  这种编码方式能够为不同的时间距离生成唯一的向量表示，使模型能够区分不同时间点的题目。编码后的时间向量与题目嵌入向量结合，增强了模型对时间信息的感知能力。

[0144]  实施例3：
[0145]  在本实施例中，详细说明了动态门控融合模块中权重调整的策略。
[0146]  门控权重的计算不仅考虑当前输入，还考虑学习者的历史表现和题目特征：
[0147]  (1) 学习者特征：包括历史正确率、学习时长、题目类型偏好等；
[0148]  (2) 题目特征：包括题目难度、所需技能数量、题目类型等；
[0149]  (3) 上下文特征：包括当前学习会话的长度、最近几道题的表现等。
[0150]  综合这些特征，门控机制能够更精确地调整各通道的权重。例如：
[0151]  - 对于需要回顾历史知识的复杂题目，增加TCN通道的权重；
[0152]  - 对于需要理解长期学习趋势的情况，增加Transformer通道的权重；
[0153]  - 对于需要关注最近学习状态的情况，增加LSTM通道的权重。

[0154]  实施例4：
[0155]  在本实施例中，详细说明了模型训练和推理过程中的优化策略。
[0156]  (1) 训练策略：
[0157]  - 采用梯度裁剪防止梯度爆炸；
[0158]  - 使用学习率调度器动态调整学习率；
[0159]  - 引入Dropout防止过拟合；
[0160]  - 使用批量归一化加速训练收敛。
[0161]  (2) 推理优化：
[0162]  - 对于长序列，采用滑动窗口策略减少计算复杂度；
[0163]  - 缓存历史题目的嵌入向量，避免重复计算；
[0164]  - 使用近似算法加速相似度计算。
[0165]  (3) 超参数设置：
[0166]  - 嵌入维度$d$通常设置为128或256；
[0167]  - 遗忘系数$\lambda$设置为0.01-0.1之间；
[0168]  - 回顾题目数量$K$设置为5-20之间；
[0169]  - 学习率初始值设置为0.001。

[0170]  以上所述仅为本发明的优选实施例，并非因此限制本发明的专利范围，凡是利用本发明说明书及附图内容所作的等效结构或等效流程变换，或直接或间接运用在其他相关的技术领域，均同理包括在本发明的专利保护范围内。