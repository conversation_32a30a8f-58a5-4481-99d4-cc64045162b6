import json
import pandas as pd

# --- 配置部分 ---
# JSON 文件路径，使用原始字符串格式，避免反斜杠转义问题，并移除外部多余的双引号
json_file_path = r'C:\Users\<USER>\Desktop\game_20230612.json'
# Excel 文件输出路径，可以指定一个完整路径，或者在当前脚本目录下生成
excel_file_path = r'C:\Users\<USER>\Desktop\game_logs_output.xlsx'
# --- 配置结束 ---

all_data_for_excel = []

try:
    print(f"正在尝试读取 JSON 文件: {json_file_path}")
    with open(json_file_path, 'r', encoding='utf-8') as f:
        # 使用 json.load 加载整个 JSON 文件
        data = json.load(f)

    print(f"文件读取成功，开始处理 {len(data)} 条记录...")

    # 遍历主列表中的每个日志记录
    for i, record in enumerate(data):
        # 打印进度，每处理1000条记录显示一次
        if (i + 1) % 1000 == 0:
            print(f"  已处理记录数: {i+1}/{len(data)}")

        actor_id = record.get('actor_id')
        actor_data = record.get('actor_data', {}) # 获取 actor_data，如果不存在则返回空字典

        # 从 actor_data 中提取一些常用信息，可以根据需要添加更多
        actor_info = {
            "actor_id": actor_id,
            "province": actor_data.get('province', ''),
            "city": actor_data.get('city', ''),
            "school": actor_data.get('school', ''),
            "grade": actor_data.get('grade', ''),
            "class": actor_data.get('class', ''),
            "name": actor_data.get('name', ''),
            "gender": actor_data.get('gender', '') # 0: Female, 1: Male
        }

        # 遍历 context 中的每个任务/问题记录
        contexts = record.get('context', [])
        # 确保 contexts 是一个列表，即使 record.get('context') 返回 None
        if contexts is None:
            contexts = []

        for context_item in contexts:
            question_id = context_item.get('question_id')
            tasks = context_item.get('task', [])
            # 确保 tasks 是一个列表
            if tasks is None:
                tasks = []

            # 遍历每个 task（交互事件）
            for task_event in tasks:
                verb = task_event.get('verb')
                tool = task_event.get('tool')
                timestamp = task_event.get('timestamp')
                object_val = task_event.get('object') # object 可能为字符串、数字、列表、字典等

                # --- 处理 object_val ---
                object_str = "" # 初始化一个空字符串作为默认值
                if object_val is None:
                    # 如果 object_val 是 None (对应JSON的null)，则保持为空字符串
                    object_str = ""
                elif isinstance(object_val, list):
                    # 如果是列表，将列表中的所有元素转换为字符串并用 "; " 连接
                    # 这里使用 map(str, ...) 来确保列表中的每个元素都能被转换为字符串
                    object_str = "; ".join(map(str, object_val))
                elif isinstance(object_val, dict):
                    # 如果 object_val 是字典，可以将其序列化为JSON字符串以保留结构
                    # 或者根据需要提取特定字段，例如: object_str = object_val.get('some_key', '')
                    try:
                        # 尝试将其转换为 JSON 字符串，以保留字典结构
                        import json
                        object_str = json.dumps(object_val)
                    except TypeError:
                        # 如果字典包含无法序列化的类型，则转换为普通字符串
                        object_str = str(object_val)
                else:
                    # 对于其他类型（字符串、数字等），直接转换为字符串
                    object_str = str(object_val)
                # --- object_val 处理结束 ---

                # 提取每个事件的详细信息，并与 actor_id 等信息合并
                row_data = {
                    **actor_info, # 合并学生基本信息
                    "question_id": question_id,
                    "verb": verb,
                    "tool": tool,
                    "timestamp": timestamp,
                    "object": object_str # 使用处理后的字符串
                }
                all_data_for_excel.append(row_data)

    print(f"共提取了 {len(all_data_for_excel)} 条交互事件记录。")

    if not all_data_for_excel:
        print("没有找到任何可供处理的交互事件数据。请检查您的JSON文件格式和内容。")
    else:
        # 将列表转换为 pandas DataFrame
        df = pd.DataFrame(all_data_for_excel)

        print(f"正在将数据写入 Excel 文件: {excel_file_path}")
        # 将 DataFrame 导出为 Excel 文件
        # index=False 表示不将 DataFrame 的索引写入 Excel 文件
        df.to_excel(excel_file_path, index=False, engine='openpyxl') # 指定引擎以确保兼容性
        print(f"成功将 JSON 数据转换为 Excel 文件: {excel_file_path}")

except FileNotFoundError:
    print(f"错误：文件 '{json_file_path}' 未找到。请确保文件路径和名称正确。")
except json.JSONDecodeError:
    print(f"错误：无法解析 JSON 文件 '{json_file_path}'。请检查文件格式是否为有效的 JSON。")
except TypeError as e:
    print(f"类型错误发生：{e}。这通常发生在尝试对None值执行操作时。请检查 'object' 字段的处理逻辑，确保没有对None值直接进行操作。")
except Exception as e:
    print(f"发生了一个意外错误：{e}")