(async function() {
    // 获取CSRF令牌
    const getCsrfToken = () => {
    return document.cookie.match(/_xsrf=([^;]+)/)?.[1] || '';
    };

    // 查找所有文章元素和ID
    const findArticles = () => {
            // 尝试多种选择器找到文章元素
            let articles = [];

    // 尝试多种可能的文章元素选择器
        const selectors = [
    '.ArticleItem',
            '.ContentItem',
            '[data-zop-itemid]',
            '.Post-content',
            '.zg-item'
        ];

    for (const selector of selectors) {
            const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
            console.log(`使用选择器 ${selector} 找到 ${elements.length} 个元素`);
            articles = [...elements];
            break;
        }
    }

    // 如果还是没找到，尝试从URL中提取
    if (articles.length === 0) {
            const postLinks = [...document.querySelectorAll('a[href*="/posts/"]')];
        console.log(`找到 ${postLinks.length} 个文章链接`);

        // 创建虚拟元素
        articles = postLinks.map(link => {
                const article = document.createElement('div');
        article.setAttribute('data-post-url', link.href);
        return article;
            });
    }

    return articles;
    };

    // 从元素中提取文章ID
    const extractPostId = (element) => {
            // 方法1: 从元素属性中直接获取
            let postId = element.getAttribute('data-zop-itemid') ||
            element.getAttribute('data-za-element-id');

    // 方法2: 从data-zop JSON中提取
    if (!postId) {
        try {
                const dataZop = element.getAttribute('data-zop');
            if (dataZop) {
                    const zopObj = JSON.parse(dataZop);
                postId = zopObj.itemId;
            }
        } catch (e) {}
    }

    // 方法3: 从链接URL中提取
    if (!postId) {
            const link = element.querySelector('a[href*="/posts/"]') ||
                element.querySelector('a[href*="/p/"]');
        if (link) {
                const match = link.href.match(/\/(posts|p)\/(\d+)/);
            if (match) postId = match[2];
        }
    }

    // 方法4: 从自定义属性中提取
    if (!postId && element.getAttribute('data-post-url')) {
            const match = element.getAttribute('data-post-url').match(/\/(posts|p)\/(\d+)/);
        if (match) postId = match[2];
    }

    return postId;
    };

    // 删除单篇文章
    const deletePost = async (postId) => {
        const csrfToken = getCsrfToken();
    if (!csrfToken) {
        console.error("无法获取CSRF令牌，请确保你已登录");
        return false;
    }

    // 尝试新的API路径
        const apiEndpoints = [
            `https://www.zhihu.com/api/v4/posts/${postId}`,
            `https://www.zhihu.com/api/v4/articles/${postId}`,
            `https://zhuanlan.zhihu.com/api/posts/${postId}`
        ];

    for (const endpoint of apiEndpoints) {
        try {
            console.log(`尝试删除文章: ${endpoint}`);
                const response = await fetch(endpoint, {
                    method: 'DELETE',
                    headers: {
                'x-xsrftoken': csrfToken,
                        'x-requested-with': 'XMLHttpRequest',
                        'content-type': 'application/json'
            },
            credentials: 'include'
                });

            if (response.status >= 200 && response.status < 300) {
                console.log(`成功删除文章 ${postId}`);
                return true;
            } else {
                console.log(`尝试 ${endpoint} 失败: ${response.status}`);
            }
        } catch (error) {
            console.log(`尝试 ${endpoint} 出错:`, error);
        }
    }

    console.error(`所有尝试均失败，无法删除文章 ${postId}`);
    return false;
    };

    // 使用UI界面删除文章
    const deleteByUI = async (article) => {
    try {
        // 查找此文章的更多菜单按钮并点击
            const menuSelectors = [
        '.ContentItem-more',
                '[aria-label="更多"]',
                '[class*="more"]',
                '.Post-ActionMenuButton',
                '.Button--plain',
                '.Zi--More'
            ];

        let menuButton = null;
        for (const selector of menuSelectors) {
            menuButton = article.querySelector(selector);
            if (menuButton) break;
        }

        if (!menuButton) {
            console.log("未找到菜单按钮");
            return false;
        }

        console.log("找到菜单按钮，点击...");
        menuButton.click();

        // 等待菜单出现
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 查找删除按钮
            const deleteButtons = [...document.querySelectorAll('button, div[role="button"]')].filter(
                btn => (btn.textContent || '').includes('删除') ||
                        (btn.innerText || '').includes('删除')
        );

        if (deleteButtons.length === 0) {
            console.log("未找到删除按钮");
            return false;
        }

        console.log("找到删除按钮，点击...");
        deleteButtons[0].click();

        // 等待确认对话框
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 查找确认按钮
            const confirmButtons = [...document.querySelectorAll('button')].filter(
                btn => (btn.textContent || '').includes('确认') ||
                        (btn.textContent || '').includes('确定')
        );

        if (confirmButtons.length === 0) {
            console.log("未找到确认按钮");
            return false;
        }

        console.log("找到确认按钮，点击...");
        confirmButtons[0].click();

        // 等待删除操作完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        return true;
    } catch (error) {
        console.error("UI删除过程中出错:", error);
        return false;
    }
    };

    // 创建UI控制面板
    const createControlPanel = () => {
    // 如果已存在控制面板，则移除
        const existingPanel = document.getElementById('zh-delete-helper');
    if (existingPanel) existingPanel.remove();

        const panel = document.createElement('div');
    panel.id = 'zh-delete-helper';
    panel.style.position = 'fixed';
    panel.style.top = '10px';
    panel.style.right = '10px';
    panel.style.zIndex = '10000';
    panel.style.backgroundColor = '#ff5a5f';
    panel.style.padding = '10px';
    panel.style.borderRadius = '5px';
    panel.style.color = 'white';
    panel.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
    panel.style.fontFamily = 'Arial, sans-serif';
    panel.style.fontSize = '14px';

    panel.innerHTML = `
            <h3 style="margin: 0 0 10px 0; font-size: 16px;">知乎文章批量删除助手</h3>
            <div style="margin-bottom: 10px;">
            <button id="start-api-delete" style="padding: 5px 10px; margin-right: 5px; background: white; color: #ff5a5f; border: none; border-radius: 3px; cursor: pointer;">API删除</button>
            <button id="start-ui-delete" style="padding: 5px 10px; background: white; color: #ff5a5f; border: none; border-radius: 3px; cursor: pointer;">UI删除</button>
            <button id="stop-delete" style="padding: 5px 10px; margin-left: 5px; background: #ffdddd; color: #ff5a5f; border: none; border-radius: 3px; cursor: pointer; display: none;">停止</button>
            </div>
            <div id="delete-status" style="margin: 5px 0; font-size: 12px;">准备就绪</div>
            <div id="delete-progress" style="margin: 5px 0; height: 5px; background: #ffdddd; border-radius: 3px; overflow: hidden;">
            <div id="progress-bar" style="width: 0%; height: 100%; background: white;"></div>
            </div>
            <div id="delete-log" style="margin-top: 10px; max-height: 150px; overflow-y: auto; font-size: 12px; color: #ffdddd;"></div>
        `;

    document.body.appendChild(panel);
    return panel;
    };

    // 更新日志
    const updateLog = (message) => {
        const logElement = document.getElementById('delete-log');
    if (logElement) {
            const logEntry = document.createElement('div');
        logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        logElement.appendChild(logEntry);
        logElement.scrollTop = logElement.scrollHeight;
    }
    console.log(message);
    };

    // 更新状态
    const updateStatus = (message) => {
        const statusElement = document.getElementById('delete-status');
    if (statusElement) {
        statusElement.textContent = message;
    }
    };

    // 更新进度条
    const updateProgress = (current, total) => {
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
            const percentage = Math.round((current / total) * 100);
            progressBar.style.width = `${percentage}%`;
        }
    };

    // 主函数
    const main = async () => {
            let isRunning = false;
    let shouldStop = false;

    // 创建控制面板
        const panel = createControlPanel();

    // API删除按钮事件
    document.getElementById('start-api-delete').addEventListener('click', async function() {
        if (isRunning) return;
        isRunning = true;
        shouldStop = false;

        this.style.display = 'none';
        document.getElementById('start-ui-delete').style.display = 'none';
        document.getElementById('stop-delete').style.display = 'inline-block';

        updateStatus('正在查找文章...');
        updateLog('开始API删除模式');

            const articles = findArticles();
        if (articles.length === 0) {
            updateLog('未找到任何文章，请确保你在正确的页面上');
            updateStatus('未找到文章');

            this.style.display = 'inline-block';
            document.getElementById('start-ui-delete').style.display = 'inline-block';
            document.getElementById('stop-delete').style.display = 'none';
            isRunning = false;
            return;
        }

            const postIds = [];
        for (const article of articles) {
                const postId = extractPostId(article);
            if (postId && !postIds.includes(postId)) {
                postIds.push(postId);
            }
        }

        updateLog(`找到 ${postIds.length} 篇文章`);

        if (!confirm(`确定要删除这 ${postIds.length} 篇文章吗？此操作不可撤销!`)) {
            updateStatus('已取消');

            this.style.display = 'inline-block';
            document.getElementById('start-ui-delete').style.display = 'inline-block';
            document.getElementById('stop-delete').style.display = 'none';
            isRunning = false;
            return;
        }

        let successCount = 0;
        let failCount = 0;

        for (let i = 0; i < postIds.length; i++) {
            if (shouldStop) {
                updateLog('操作已中止');
                break;
            }

                const postId = postIds[i];
            updateStatus(`正在删除第 ${i+1}/${postIds.length} 篇文章`);
            updateProgress(i, postIds.length);
            updateLog(`删除文章 ID: ${postId}`);

                const success = await deletePost(postId);
            if (success) {
                successCount++;
                updateLog(`✓ 成功删除文章 ${postId}`);
            } else {
                failCount++;
                updateLog(`✗ 删除文章 ${postId} 失败`);
            }

            // 添加延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        updateProgress(postIds.length, postIds.length);
        updateStatus(`完成: 成功${successCount}篇，失败${failCount}篇`);
        updateLog(`批量删除完成! 成功: ${successCount}, 失败: ${failCount}`);

        this.style.display = 'inline-block';
        document.getElementById('start-ui-delete').style.display = 'inline-block';
        document.getElementById('stop-delete').style.display = 'none';
        isRunning = false;

        // 如果有成功，询问是否刷新页面
        if (successCount > 0 && confirm('删除完成，是否刷新页面？')) {
            window.location.reload();
        }
    });

    // UI删除按钮事件
    document.getElementById('start-ui-delete').addEventListener('click', async function() {
        if (isRunning) return;
        isRunning = true;
        shouldStop = false;

        this.style.display = 'none';
        document.getElementById('start-api-delete').style.display = 'none';
        document.getElementById('stop-delete').style.display = 'inline-block';

        updateStatus('正在查找文章...');
        updateLog('开始UI删除模式');

            const articles = findArticles();
        if (articles.length === 0) {
            updateLog('未找到任何文章，请确保你在正确的页面上');
            updateStatus('未找到文章');

            this.style.display = 'inline-block';
            document.getElementById('start-api-delete').style.display = 'inline-block';
            document.getElementById('stop-delete').style.display = 'none';
            isRunning = false;
            return;
        }

        updateLog(`找到 ${articles.length} 篇文章`);

        if (!confirm(`确定要删除这 ${articles.length} 篇文章吗？此操作不可撤销!`)) {
            updateStatus('已取消');

            this.style.display = 'inline-block';
            document.getElementById('start-api-delete').style.display = 'inline-block';
            document.getElementById('stop-delete').style.display = 'none';
            isRunning = false;
            return;
        }

        let successCount = 0;
        let failCount = 0;

        for (let i = 0; i < articles.length; i++) {
            if (shouldStop) {
                updateLog('操作已中止');
                break;
            }

                const article = articles[i];
                const postId = extractPostId(article) || `未知ID-${i}`;

            updateStatus(`正在删除第 ${i+1}/${articles.length} 篇文章`);
            updateProgress(i, articles.length);
            updateLog(`尝试UI删除文章: ${postId}`);

                const success = await deleteByUI(article);
            if (success) {
                successCount++;
                updateLog(`✓ 成功UI删除文章 ${postId}`);
            } else {
                failCount++;
                updateLog(`✗ UI删除文章 ${postId} 失败`);
            }
        }

        updateProgress(articles.length, articles.length);
        updateStatus(`完成: 成功${successCount}篇，失败${failCount}篇`);
        updateLog(`批量删除完成! 成功: ${successCount}, 失败: ${failCount}`);

        this.style.display = 'inline-block';
        document.getElementById('start-api-delete').style.display = 'inline-block';
        document.getElementById('stop-delete').style.display = 'none';
        isRunning = false;

        // 如果有成功，询问是否刷新页面
        if (successCount > 0 && confirm('删除完成，是否刷新页面？')) {
            window.location.reload();
        }
    });

    // 停止按钮事件
    document.getElementById('stop-delete').addEventListener('click', function() {
        shouldStop = true;
        updateLog('正在停止...');
        updateStatus('正在停止...');
    });

    updateLog('知乎文章批量删除助手已加载');
    };

    // 执行主函数
    main();
})();