import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import sys
from datetime import datetime

class DesktopTodoWidget:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.todos = self.load_todos()
        self.is_minimized = False
        self.setup_ui()
        self.update_display()
        
    def setup_window(self):
        # 窗口基本设置
        self.root.title("每日待办")
        self.root.geometry("280x400+100+100")
        self.root.resizable(False, False)
        
        # 设置窗口始终在最前面
        self.root.attributes('-topmost', True)
        
        # 去掉标题栏装饰
        self.root.overrideredirect(True)
        
        # 设置窗口背景
        self.root.configure(bg='#f0f0f0')
        
        # 绑定拖拽事件
        self.root.bind('<Button-1>', self.start_drag)
        self.root.bind('<B1-Motion>', self.drag_window)
        
        # 防止窗口被意外关闭
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_ui(self):
        # 主容器
        main_frame = tk.Frame(self.root, bg='white', relief='raised', bd=2)
        main_frame.pack(fill='both', expand=True, padx=2, pady=2)
        
        # 标题栏
        self.header_frame = tk.Frame(main_frame, bg='#4a90e2', height=40)
        self.header_frame.pack(fill='x')
        self.header_frame.pack_propagate(False)
        
        # 标题栏内容
        title_frame = tk.Frame(self.header_frame, bg='#4a90e2')
        title_frame.pack(fill='both', expand=True)
        
        # 标题和日期
        self.title_label = tk.Label(title_frame, text="每日待办", 
                                   bg='#4a90e2', fg='white', 
                                   font=('Microsoft YaHei', 10, 'bold'))
        self.title_label.pack(side='left', padx=10, pady=5)
        
        self.date_label = tk.Label(title_frame, text=self.get_current_date(), 
                                  bg='#4a90e2', fg='white', 
                                  font=('Microsoft YaHei', 8))
        self.date_label.pack(side='left', padx=(0, 10))
        
        # 最小化按钮
        self.minimize_btn = tk.Button(title_frame, text="−", 
                                     bg='#4a90e2', fg='white', 
                                     font=('Arial', 12, 'bold'),
                                     relief='flat', bd=0,
                                     command=self.toggle_minimize)
        self.minimize_btn.pack(side='right', padx=10, pady=5)
        
        # 隐藏按钮（替代关闭）
        hide_btn = tk.Button(title_frame, text="_", 
                            bg='#4a90e2', fg='white', 
                            font=('Arial', 12, 'bold'),
                            relief='flat', bd=0,
                            command=self.hide_to_tray)
        hide_btn.pack(side='right', padx=(0, 5), pady=5)
        
        # 绑定标题栏拖拽
        self.header_frame.bind('<Button-1>', self.start_drag)
        self.header_frame.bind('<B1-Motion>', self.drag_window)
        title_frame.bind('<Button-1>', self.start_drag)
        title_frame.bind('<B1-Motion>', self.drag_window)
        self.title_label.bind('<Button-1>', self.start_drag)
        self.title_label.bind('<B1-Motion>', self.drag_window)
        self.date_label.bind('<Button-1>', self.start_drag)
        self.date_label.bind('<B1-Motion>', self.drag_window)
        
        # 内容区域
        self.content_frame = tk.Frame(main_frame, bg='white')
        self.content_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 输入框
        input_frame = tk.Frame(self.content_frame, bg='white')
        input_frame.pack(fill='x', pady=(0, 10))
        
        self.entry = tk.Entry(input_frame, font=('Microsoft YaHei', 9),
                             relief='solid', bd=1)
        self.entry.pack(fill='x', ipady=5)
        self.entry.bind('<Return>', self.add_todo)
        self.entry.insert(0, "输入待办事项，按回车添加...")
        self.entry.bind('<FocusIn>', self.clear_placeholder)
        self.entry.bind('<FocusOut>', self.restore_placeholder)
        
        # 待办列表容器
        list_frame = tk.Frame(self.content_frame, bg='white')
        list_frame.pack(fill='both', expand=True)
        
        # 滚动条
        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side='right', fill='y')
        
        # 列表框
        self.listbox = tk.Listbox(list_frame, 
                                 yscrollcommand=scrollbar.set,
                                 font=('Microsoft YaHei', 9),
                                 relief='flat', bd=0,
                                 selectmode='single',
                                 activestyle='none')
        self.listbox.pack(side='left', fill='both', expand=True)
        scrollbar.config(command=self.listbox.yview)
        
        # 绑定双击事件
        self.listbox.bind('<Double-Button-1>', self.toggle_todo)
        self.listbox.bind('<Button-3>', self.show_context_menu)
        
        # 统计信息
        self.stats_label = tk.Label(self.content_frame, 
                                   text="今日待办：0 | 已完成：0",
                                   bg='white', fg='#666',
                                   font=('Microsoft YaHei', 8))
        self.stats_label.pack(pady=(10, 0))
        
        # 右键菜单
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="删除", command=self.delete_selected)
        self.context_menu.add_command(label="标记完成/未完成", command=self.toggle_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="退出程序", command=self.quit_app)
        
    def get_current_date(self):
        return datetime.now().strftime("%m月%d日")
        
    def start_drag(self, event):
        self.x = event.x
        self.y = event.y
        
    def drag_window(self, event):
        deltax = event.x - self.x
        deltay = event.y - self.y
        x = self.root.winfo_x() + deltax
        y = self.root.winfo_y() + deltay
        self.root.geometry(f"+{x}+{y}")
        
    def toggle_minimize(self):
        if self.is_minimized:
            # 展开
            self.root.geometry("280x400")
            self.content_frame.pack(fill='both', expand=True, padx=5, pady=5)
            self.minimize_btn.config(text="−")
            self.is_minimized = False
        else:
            # 最小化
            self.root.geometry("280x40")
            self.content_frame.pack_forget()
            self.minimize_btn.config(text="+")
            self.is_minimized = True
            
    def hide_to_tray(self):
        """隐藏到系统托盘（简化版：最小化窗口）"""
        self.root.withdraw()  # 隐藏窗口
        # 创建恢复窗口的方法
        self.root.after(1000, self.show_restore_hint)
        
    def show_restore_hint(self):
        """显示恢复提示"""
        # 创建一个小的恢复按钮
        self.restore_window = tk.Toplevel()
        self.restore_window.title("待办事项")
        self.restore_window.geometry("120x30+50+50")
        self.restore_window.attributes('-topmost', True)
        self.restore_window.resizable(False, False)
        
        restore_btn = tk.Button(self.restore_window, text="显示待办事项",
                               command=self.restore_from_tray,
                               bg='#4a90e2', fg='white',
                               relief='flat')
        restore_btn.pack(fill='both', expand=True)
        
    def restore_from_tray(self):
        """从托盘恢复"""
        self.root.deiconify()  # 显示主窗口
        if hasattr(self, 'restore_window'):
            self.restore_window.destroy()
            
    def on_closing(self):
        """处理窗口关闭事件"""
        self.hide_to_tray()
        
    def quit_app(self):
        """完全退出应用"""
        self.save_todos()
        if hasattr(self, 'restore_window'):
            self.restore_window.destroy()
        self.root.quit()
        self.root.destroy()
        sys.exit()
        
    def clear_placeholder(self, event):
        if self.entry.get() == "输入待办事项，按回车添加...":
            self.entry.delete(0, tk.END)
            self.entry.config(fg='black')
            
    def restore_placeholder(self, event):
        if not self.entry.get():
            self.entry.insert(0, "输入待办事项，按回车添加...")
            self.entry.config(fg='gray')
            
    def add_todo(self, event=None):
        text = self.entry.get().strip()
        if text and text != "输入待办事项，按回车添加...":
            todo = {
                'id': len(self.todos) + 1,
                'text': text,
                'completed': False,
                'date': datetime.now().strftime('%Y-%m-%d')
            }
            self.todos.append(todo)
            self.entry.delete(0, tk.END)
            self.restore_placeholder(None)
            self.update_display()
            self.save_todos()
            
    def toggle_todo(self, event=None):
        self.toggle_selected()
        
    def toggle_selected(self):
        selection = self.listbox.curselection()
        if selection:
            index = selection[0]
            today_todos = self.get_today_todos()
            if index < len(today_todos):
                todo_id = today_todos[index]['id']
                for todo in self.todos:
                    if todo['id'] == todo_id:
                        todo['completed'] = not todo['completed']
                        break
                self.update_display()
                self.save_todos()
                
    def delete_selected(self):
        selection = self.listbox.curselection()
        if selection:
            index = selection[0]
            today_todos = self.get_today_todos()
            if index < len(today_todos):
                todo_id = today_todos[index]['id']
                self.todos = [t for t in self.todos if t['id'] != todo_id]
                self.update_display()
                self.save_todos()
                
    def show_context_menu(self, event):
        try:
            self.listbox.selection_clear(0, tk.END)
            self.listbox.selection_set(self.listbox.nearest(event.y))
            self.context_menu.post(event.x_root, event.y_root)
        except:
            pass
            
    def get_today_todos(self):
        today = datetime.now().strftime('%Y-%m-%d')
        return [todo for todo in self.todos if todo['date'] == today]
        
    def update_display(self):
        self.listbox.delete(0, tk.END)
        today_todos = self.get_today_todos()
        
        for todo in today_todos:
            status = "✓" if todo['completed'] else "○"
            text = f"{status} {todo['text']}"
            self.listbox.insert(tk.END, text)
            
            # 设置已完成项目的颜色
            if todo['completed']:
                self.listbox.itemconfig(tk.END, {'fg': '#888'})
                
        # 更新统计
        total = len(today_todos)
        completed = len([t for t in today_todos if t['completed']])
        self.stats_label.config(text=f"今日待办：{total} | 已完成：{completed}")
        
        # 更新日期
        self.date_label.config(text=self.get_current_date())
        
    def load_todos(self):
        try:
            if os.path.exists('todos.json'):
                with open('todos.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        return []
        
    def save_todos(self):
        try:
            with open('todos.json', 'w', encoding='utf-8') as f:
                json.dump(self.todos, f, ensure_ascii=False, indent=2)
        except:
            pass
            
    def run(self):
        # 定期更新日期和自动保存
        def periodic_update():
            self.date_label.config(text=self.get_current_date())
            self.save_todos()  # 定期保存
            self.root.after(60000, periodic_update)  # 每分钟更新一次
            
        periodic_update()
        
        # 设置异常处理
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.quit_app()
        except Exception as e:
            print(f"应用出现错误: {e}")
            self.quit_app()

if __name__ == "__main__":
    try:
        app = DesktopTodoWidget()
        app.run()
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")